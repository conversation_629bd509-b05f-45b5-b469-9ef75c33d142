import { MigrationInterface, QueryRunner } from 'typeorm';

// This migration is to add a createdAt column to the datapoint_generation
// table as there are requirements to fetch the generations based on createdAt time

export class SchemaUpdate1738217374078 implements MigrationInterface {
  name = 'SchemaUpdate1738217374078';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_generation" ADD "createdAt" TIMESTAMP DEFAULT now()`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_generation" DROP COLUMN "createdAt"`,
    );
  }
}
