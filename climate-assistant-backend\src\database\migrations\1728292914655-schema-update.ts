import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1728292914655 implements MigrationInterface {
  name = 'SchemaUpdate1728292914655';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "file_upload" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."chat_message_role_enum" RENAME TO "chat_message_role_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."chat_message_role_enum" AS ENUM('user', 'system', 'assistant', 'function')`,
    );
    await queryRunner.query(
      `ALTER TABLE "chat_message" ALTER COLUMN "role" TYPE "public"."chat_message_role_enum" USING "role"::"text"::"public"."chat_message_role_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."chat_message_role_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."chat_message_role_enum_old" AS ENUM('user', 'system', 'assistant')`,
    );
    await queryRunner.query(
      `ALTER TABLE "chat_message" ALTER COLUMN "role" TYPE "public"."chat_message_role_enum_old" USING "role"::"text"::"public"."chat_message_role_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."chat_message_role_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."chat_message_role_enum_old" RENAME TO "chat_message_role_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "file_upload" DROP COLUMN "createdAt"`,
    );
  }
}
