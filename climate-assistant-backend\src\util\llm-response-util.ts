export function trimHtmlPreAndPostfix(llmResponse: string): string {
  if (llmResponse) {
    llmResponse = llmResponse.replace(`\`\`\`html`, '');
    llmResponse = llmResponse.replace(`\`\`\``, '');
  }
  return llmResponse;
}

/**
 * Fixes a malformed JSON string where array elements contain key-value pairs without proper object notation.
 *
 * **Example Input (Malformed JSON):**
 * {
 *    "active": ["chunk-3": "60%"],
 *    "inactive": ["chunk-6": "50%", "chunk-2": "55%"]
 * }
 *
 * **Expected Output (Valid JSON):**
 * {
 *   "active": [
 *     { "chunk-3": "60%" }
 *   ],
 *   "inactive": [
 *     { "chunk-6": "50%" },
 *     { "chunk-2": "55%" }
 *   ]
 * }
 *
 * @param {string} input - The malformed JSON string.
 * @returns {any} - A properly formatted JSON object.
 * @throws {Error} If the input cannot be parsed into valid JSON.
 */
function fixMalformedJson(input: string): any {
  try {
    // Convert incorrectly structured arrays into proper JSON object format.
    // Example: ["chunk-3": "60%"] → [{ "chunk-3": "60%" }]
    let fixedInput = input.replace(
      /(\[)(\s*)(".*?")(\s*:\s*".*?")(\s*)(\])/g,
      (_, open, space1, key, value, space2, close) => {
        return `${open}${space1}{${key}${value}}${space2}${close}`;
      }
    );

    // Parse the corrected string to ensure it's a valid JSON object.
    return JSON.parse(fixedInput);
  } catch (error) {
    throw new Error('Invalid input format. Could not convert to JSON.');
  }
}

/**
 * Processes an array of chunk objects and extracts their data into a structured format.
 *
 * @param {{[key: string]: string;}[]} citations - Array of citation objects (e.g., [{ "chunk-3": "60%" }])
 * @param {string[]} documentChunksIndex - Array of document chunk IDs.
 * @returns {Array<{ id: string, text: string }>} - Processed array of chunk data.
 */
function processNumericCitationParts(
  citations: { [key: string]: string }[],
  documentChunksIndex: string[]
): { id: string; text: string }[] {
  const entries: { id: string; text: string }[] = [];

  if (Array.isArray(citations)) {
    for (const chunkObj of citations) {
      // Each chunkObj is an object with a single key, e.g., { "chunk-3": "60%" }
      for (const key in chunkObj) {
        if (Object.prototype.hasOwnProperty.call(chunkObj, key)) {
          const text = chunkObj[key];
          const idMatch = key.match(/chunk-(\d+)/);
          // Convert GPT's 1-based index to a 0-based index.
          const index = idMatch ? parseInt(idMatch[1], 10) - 1 : 0;
          entries.push({ id: documentChunksIndex[index], text });
        }
      }
    }
  }
  return entries;
}

export function extractCitationsFromReportTextGeneration(
  input: string,
  documentChunksIndex: string[]
): {
  reportText: string;
  citation: Record<
    string,
    {
      id: string;
      active: boolean;
      text?: string;
    }[]
  >;
} {
  // The citation dictionary to be built.
  const citation: Record<
    string,
    { id: string; active: boolean; text?: string }[]
  > = {};

  // This counter will determine our citation keys.
  let occurrence = 0;

  /**
   * Regex explanation:
   * - The pattern matches either a <source> or a <sources-options> tag.
   * - ([\s\S]*?) is used to capture everything inside (including newlines).
   * - The closing tag is matched with <\/\1> so that the closing tag
   *   matches the opening tag exactly.
   */
  const regex = /<(source|sources-options)>\s*([\s\S]*?)\s*<\/\1>/g;

  const newstring = input.replace(regex, (match, tag, innerContent) => {
    occurrence++;
    const key = `dpcite-${occurrence}`;
    let citationsArray: { id: string; active: boolean; text?: string }[] = [];
    // Start building the replacement snippet.
    let replacementSnippet = `[${key}|color-#eab308`;

    if (tag === 'source') {
      // For <source>, we expect innerContent to be a JSON array of strings.
      try {
        const arr = JSON.parse(innerContent);
        if (Array.isArray(arr)) {
          arr.forEach((item: string) => {
            const idMatch = item.match(/chunk-(\d+)/);
            // GPT returns 1-based indices, so we need to subtract 1.
            const index = idMatch ? parseInt(idMatch[1], 10) - 1 : 0;
            // Mark these as active. (No extra text for these sources.)
            citationsArray.push({
              id: documentChunksIndex[index],
              active: true,
            });
          });
        }
      } catch (error) {
        console.error(`Error parsing JSON for ${key} in <source>:`, error);
      }
      // The replacement snippet for a <source> tag remains as [dpcite-X|color-#eab308|text-"[source]"].
      replacementSnippet += `|text-"[source]"]`;
    } else if (tag === 'sources-options') {
      // For <sources-options>, we expect a JSON object with "active" and "inactive" keys.
      // Example expected JSON:
      // {
      //   "active": [
      //     { "chunk-3": "60%" }
      //   ],
      //   "inactive": [
      //     { "chunk-6": "50%" },
      //     { "chunk-2": "55%" }
      //   ]
      // }
      try {
        type Chunk = {
          [key: string]: string;
        };
        type DataStructure = {
          active: Chunk[];
          inactive: Chunk[];
        };
        const aiCitationobj: DataStructure = fixMalformedJson(innerContent);

        const activeEntries = processNumericCitationParts(
          aiCitationobj.active,
          documentChunksIndex
        );
        const inactiveEntries = processNumericCitationParts(
          aiCitationobj.inactive,
          documentChunksIndex
        );

        // Combine active entries (first) and inactive entries.
        citationsArray = [
          ...activeEntries.map((entry) => ({
            id: entry.id,
            text: entry.text,
            active: true,
          })),
          ...inactiveEntries.map((entry) => ({
            id: entry.id,
            text: entry.text,
            active: false,
          })),
        ];

        // For the replacement snippet, if there is at least one active item,
        // include its text as an extra parameter.

        if (activeEntries.length > 0) {
          replacementSnippet += `|text-"${activeEntries[0].text}"`;
        } else {
          replacementSnippet += `|text-"[source]"`;
        }
      } catch (error) {
        console.error(
          `Error parsing JSON for ${key} in <sources-options>:`,
          error
        );
      }
      replacementSnippet += `]`;
    }

    // Save the citation data under our unique key.
    citation[key] = citationsArray;
    return replacementSnippet;
  });

  return { reportText: newstring, citation };
}

/**
 * Matches citation formatting with pattern [dpcite-123|color-#abc123|text-"citation text"]
 *
 * Captures the citation number, hex color code, and the citation text
 */
export const CITATION_CLIENT_REGEX =
  /\[dpcite-(\d+)\|color-([#a-fA-F0-9]+)\|text-"([^"]+)"\]/g;

/**
 * Matches table cells with two types of markup:
 * 1. Row/column merged cells: <r1#c2>content
 * 2. Regular merged cells: <rm>content</rm>.
 *
 * Captures escape characters, cell content, and row/column numbers when present
 */
export const MERGED_CELL_REGEX =
  /(\\?\|)(?:<r(\d+)#c(\d+)>(.*?)|<rm>(.*?)<\/rm>)(\\?\|)/g;
