import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { FileText, FileCode, FileCog, FileSpreadsheet, FilePieChart, Clock, FileQuestion, ChevronRight, AlertTriangle } from 'lucide-react';
import { DocumentStatusChip } from './DocumentStatusChip';
import { DocumentStatus, DocumentType } from '@/types/ecovadis';

// Mock document data
export const mockDocuments = [
  {
    id: 'doc-1',
    name: 'ESG Strategy 2024-2025.pdf',
    type: 'sustainability_report' as DocumentType,
    date: new Date('2025-04-08'),
    status: 'processing' as DocumentStatus,
    linkedQuestionsCount: 0,
    gapsCount: 0,
    fileType: 'pdf'
  },
  {
    id: 'doc-2',
    name: 'Sustainability Report 2023.pdf',
    type: 'sustainability_report' as DocumentType,
    date: new Date('2023-05-15'),
    status: 'complete' as DocumentStatus,
    linkedQuestionsCount: 15,
    gapsCount: 0,
    fileType: 'pdf'
  },
  {
    id: 'doc-3',
    name: 'Carbon Emissions Data.xlsx',
    type: 'reporting_document' as DocumentType,
    date: new Date('2023-06-20'),
    status: 'complete' as DocumentStatus,
    linkedQuestionsCount: 4,
    gapsCount: 1,
    fileType: 'spreadsheet'
  },
  {
    id: 'doc-4',
    name: 'Supply Chain Ethics Policy.docx',
    type: 'supplier_code' as DocumentType,
    date: new Date('2023-04-10'),
    status: 'invalid' as DocumentStatus,
    linkedQuestionsCount: 0,
    gapsCount: 3,
    fileType: 'doc'
  },
  {
    id: 'doc-5',
    name: 'Environmental Certificate.pdf',
    type: 'certificate' as DocumentType,
    date: new Date('2023-07-05'),
    status: 'complete' as DocumentStatus,
    linkedQuestionsCount: 6,
    gapsCount: 0,
    fileType: 'pdf'
  },
  {
    id: 'doc-6',
    name: 'Board Diversity Report.pdf',
    type: 'reporting_document' as DocumentType,
    date: new Date('2023-02-28'),
    status: 'complete' as DocumentStatus,
    linkedQuestionsCount: 3,
    gapsCount: 0,
    fileType: 'pdf'
  },
  {
    id: 'doc-7',
    name: 'Water Usage Metrics 2023.xlsx',
    type: 'reporting_document' as DocumentType,
    date: new Date('2023-08-12'),
    status: 'complete' as DocumentStatus,
    linkedQuestionsCount: 2,
    gapsCount: 0,
    fileType: 'spreadsheet'
  },
  {
    id: 'doc-8',
    name: 'Community Engagement Photos.jpg',
    type: 'other' as DocumentType,
    date: new Date('2023-09-30'),
    status: 'complete' as DocumentStatus,
    linkedQuestionsCount: 1,
    gapsCount: 0,
    fileType: 'image'
  },
  {
    id: 'doc-9',
    name: 'Corrupted Sustainability Data.pdf',
    type: 'reporting_document' as DocumentType,
    date: new Date('2023-10-15'),
    status: 'failed' as DocumentStatus,
    linkedQuestionsCount: 0,
    gapsCount: 0,
    fileType: 'pdf'
  }
];

export const DocumentList = () => {
  const [documents, setDocuments] = useState(mockDocuments);
  const [filteredDocuments, setFilteredDocuments] = useState(mockDocuments);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');

  useEffect(() => {
    // Filter documents based on search query and type
    const filtered = documents.filter(doc => {
      const matchesSearch = doc.name.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesType = filterType === 'all' || doc.type === filterType;
      return matchesSearch && matchesType;
    });
    
    setFilteredDocuments(filtered);
  }, [documents, searchQuery, filterType]);

  const getDocumentIcon = (type) => {
    switch (type) {
      case 'policy':
        return <FileText className="h-5 w-5 text-glacier-darkBlue" />;
      case 'certificate':
        return <FileCog className="h-5 w-5 text-green-600" />;
      case 'sustainability_report':
        return <FilePieChart className="h-5 w-5 text-glacier-mint" />;
      case 'reporting_document':
        return <FileSpreadsheet className="h-5 w-5 text-blue-500" />;
      case 'supplier_code':
        return <FileCode className="h-5 w-5 text-blue-500" />;
      case 'other':
        return <FileQuestion className="h-5 w-5 text-gray-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  const formatDate = (date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  return (
    <div className="space-y-4">
      <div className="flex gap-4 mb-6">
        <div className="relative flex-1">
          <input
            type="text"
            placeholder="Search documents..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <select
          className="px-3 py-2 border border-gray-300 rounded-md"
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
        >
          <option value="all">All Types</option>
          <option value="policy">Policies</option>
          <option value="certificate">Certificates</option>
          <option value="sustainability_report">Sustainability Reports</option>
          <option value="reporting_document">Reporting Documents</option>
          <option value="supplier_code">Supplier Codes</option>
          <option value="other">Other</option>
        </select>
      </div>

      {filteredDocuments.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto rounded-full bg-gray-100 flex items-center justify-center mb-4">
            <FileQuestion className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-1">No documents found</h3>
          <p className="text-gray-500">Try adjusting your search or upload a new document.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredDocuments.map((doc) => (
            <Link key={doc.id} to={`/documents/${doc.id}`}>
              <Card className="hover:border-glacier-darkBlue/30 transition-all cursor-pointer">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      {getDocumentIcon(doc.type)}
                      <div className="ml-2.5">
                        <h3 className="font-medium text-sm text-glacier-darkBlue truncate max-w-[210px]">{doc.name}</h3>
                        <div className="flex items-center mt-1 text-xs text-gray-500">
                          <Clock className="h-3.5 w-3.5 mr-1" />
                          <span>{formatDate(doc.date)}</span>
                        </div>
                      </div>
                    </div>
                    <DocumentStatusChip status={doc.status} />
                  </div>
                  
                  <div className="flex items-center justify-between mt-3">
                    <div className="flex items-center space-x-2">
                      {doc.linkedQuestionsCount > 0 && (
                        <Badge variant="outline" className="flex items-center text-xs px-2 py-0.5">
                          <FileText className="h-3 w-3 mr-1" />
                          {doc.linkedQuestionsCount} questions
                        </Badge>
                      )}
                      
                      {doc.gapsCount > 0 && (
                        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 flex items-center text-xs px-2 py-0.5">
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          {doc.gapsCount} gaps
                        </Badge>
                      )}
                    </div>
                    <ChevronRight className="h-4 w-4 text-gray-400" />
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
};
