
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export type StatusChipVariant = 'default' | 'outline' | 'secondary' | 'destructive' | 'warning' | 'info' | 'forest';

export interface StatusChipProps {
  variant?: StatusChipVariant;
  text?: string;
  status?: string;
  className?: string;
}

export const StatusChip: React.FC<StatusChipProps> = ({ variant = 'default', text, status, className }) => {
  // Convert status to display text if provided
  const displayText = text || (status ? status.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, c => c.toUpperCase()) : '');
  
  // Determine variant based on status if no variant provided
  let computedVariant = variant;
  if (status && variant === 'default') {
    if (status === 'complete') computedVariant = 'forest';
    else if (status === 'pending' || status === 'in_progress') computedVariant = 'warning';
    else if (status === 'not_applicable') computedVariant = 'secondary';
  }
  const getVariantStyles = () => {
    switch (variant) {
      case 'warning':
        return 'bg-amber-100 text-amber-800 hover:bg-amber-100';
      case 'info':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
      case 'forest':
        return 'bg-green-100 text-green-800 hover:bg-green-100';
      default:
        return '';
    }
  };

  return (
    <Badge 
      variant={['warning', 'info', 'forest'].includes(computedVariant) ? 'outline' : (computedVariant as 'default' | 'outline' | 'secondary' | 'destructive')} 
      className={cn(getVariantStyles(), className)}
    >
      {displayText}
    </Badge>
  );
};

export default StatusChip;
