import { Injectable } from '@nestjs/common';
import OpenAI from 'openai';
import { Stream } from 'openai/streaming';
import { LLM_MODELS } from 'src/constants';

const openai = new OpenAI({
  apiKey: '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', //@S consider removing
  //baseURL: 'https://api.perplexity.ai',
  baseURL: 'https://gateway.helicone.ai',
  defaultHeaders: {
    'Helicone-Auth': `Bearer pk-helicone-eu-m2ub3sy-zjxeqvq-rv6stey-v5bexja`, //@S remove too
    'Helicone-Target-Url': 'https://api.perplexity.ai',
  },
});
const seed = 123;
const temperature = 0.7;

@Injectable()
export class PerplexityService {
  async createCompletion(
    model: 'llama-3-sonar-small-32k-online',
    messages: Array<{
      role: 'user' | 'assistant' | 'system' | 'function';
      content: string;
    }>,
  ): Promise<string> {
    const titleParams: OpenAI.Chat.ChatCompletionCreateParams = {
      model,
      messages: messages as OpenAI.Chat.ChatCompletionMessageParam[],
      seed,
      temperature,
      top_p: 0,
    };

    const result: OpenAI.Chat.ChatCompletion =
      await openai.chat.completions.create(titleParams);

    return result.choices[0].message.content ?? '';
  }

  async *createCompletionStream(
    model: LLM_MODELS,
    messages: Array<{
      role: 'user' | 'assistant' | 'system' | 'function';
      content: string;
    }>,
  ): AsyncIterableIterator<string> {
    const params: OpenAI.Chat.ChatCompletionCreateParams = {
      model,
      messages: messages as OpenAI.Chat.ChatCompletionMessageParam[],
      stream: true,
      seed,
      temperature,
      top_p: 0,
    };

    const stream: Stream<OpenAI.Chat.ChatCompletionChunk> =
      await openai.chat.completions.create(params);

    for await (const part of stream) {
      yield part.choices[0].delta.content ?? '';
    }

    return;
  }
}
