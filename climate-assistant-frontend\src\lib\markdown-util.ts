import { marked } from 'marked';
import mermaid from 'mermaid';

export const MERGED_CELL_REGEX = /(\\?\|)<r(\d+)#c(\d+)>(.*?)(\\?\|)/g;
export const MERMAID_REGEX = /```mermaid\n([\s\S]*?)```/g;

/**
 * Initializes mermaid configuration
 */
export function initMermaid(): void {
  mermaid.initialize({
    startOnLoad: false, // Prevent auto-loading to control errors
    theme: 'default',
    securityLevel: 'loose',
    flowchart: { useMaxWidth: true, htmlLabels: true },
    // Add error handling
    logLevel: 'error',
    suppressErrorRendering: true,
  });
}

/**
 * Parses markdown text, handles special merge cell indicators, and renders mermaid diagrams
 * @param text Markdown text to parse
 * @returns HTML with proper merged cells and mermaid diagrams
 */
export function parseMarkdown(text: string): string {
  // Initialize mermaid if not already initialized
  if (typeof window !== 'undefined' && !window.mermaidInitialized) {
    initMermaid();
    window.mermaidInitialized = true;
  }

  text = extractCurrentPageContent(text);

  // Step 1: Extract and preserve mermaid blocks
  const mermaidBlocks: string[] = [];
  const textWithPlaceholders = text.replace(
    MERMAID_REGEX,
    (_match, mermaidCode) => {
      mermaidBlocks.push(mermaidCode.trim());
      return `<mermaid-placeholder-${mermaidBlocks.length - 1}></mermaid-placeholder-${mermaidBlocks.length - 1}>`;
    }
  );

  // Step 2: Replace merge indicators with custom HTML elements that survive markdown parsing
  const preprocessed = textWithPlaceholders.replace(
    MERGED_CELL_REGEX,
    (_match, _startPipe, rowspan, colspan, content) =>
      `<merge-cell data-rowspan="${rowspan}" data-colspan="${colspan}">${content}</merge-cell>`
  );

  // Step 3: Parse the preprocessed markdown to HTML
  const html = marked.parse(preprocessed).toString();

  // Step 4: Process the generated HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  // Step 4.1: Restore and render mermaid diagrams
  mermaidBlocks.forEach((mermaidCode, index) => {
    const placeholder = tempDiv.querySelector(`mermaid-placeholder-${index}`);
    if (placeholder) {
      const diagramId = `mermaid-diagram-${Date.now()}-${index}`;
      const diagramContainer = document.createElement('div');
      diagramContainer.className = 'mermaid';
      diagramContainer.id = diagramId;
      diagramContainer.setAttribute('data-content', mermaidCode);

      // Replace placeholder with mermaid container
      placeholder.parentNode?.replaceChild(diagramContainer, placeholder);

      // Schedule rendering of this diagram (after the DOM is updated)
      setTimeout(() => {
        safeRenderMermaid(diagramId, mermaidCode).then(({ svg, success }) => {
          const container = document.getElementById(diagramId);
          if (container) {
            container.innerHTML = svg;

            // Optionally add a class to mark error diagrams
            if (!success) {
              container.classList.add('mermaid-error');
            }
          }
        });
      }, 0);
    }
  });

  // Step 4.2: Remove any cells containing <rm> tags
  const cellsWithRmTags = tempDiv.querySelectorAll('td, th');
  cellsWithRmTags.forEach((cell) => {
    if (cell.innerHTML.includes('<rm>') && cell.innerHTML.includes('</rm>')) {
      cell.remove();
    }
  });

  // Step 4.3: Process merge cells
  const tables = tempDiv.querySelectorAll('table');
  tables.forEach((table) => {
    processMergedCells(table);
  });

  // Step 5: Apply styling classes
  tempDiv
    .querySelectorAll('ul')
    .forEach((el) => el.classList.add('ml-6', 'list-disc'));
  tempDiv
    .querySelectorAll('ol')
    .forEach((el) => el.classList.add('ml-6', 'list-decimal'));
  tempDiv
    .querySelectorAll('table')
    .forEach((el) =>
      el.classList.add('table-auto', 'border-collapse', 'w-full', 'text-left')
    );
  tempDiv
    .querySelectorAll('thead')
    .forEach((el) => el.classList.add('bg-slate-200'));
  tempDiv
    .querySelectorAll('th')
    .forEach((el) =>
      el.classList.add('px-4', 'py-2', 'font-medium', 'text-slate-700')
    );
  tempDiv
    .querySelectorAll('tbody')
    .forEach((el) => el.classList.add('bg-white'));
  tempDiv
    .querySelectorAll('td')
    .forEach((el) => el.classList.add('border', 'px-4', 'py-2'));

  // Add styling for mermaid diagrams
  tempDiv
    .querySelectorAll('.mermaid')
    .forEach((el) => el.classList.add('my-4', 'mx-auto', 'overflow-auto'));

  return tempDiv.innerHTML;
}

/**
 * Processes a table to handle merged cells
 * @param table The table element to process
 */
function processMergedCells(table: HTMLTableElement): void {
  // Find all merge-cell elements
  const mergeElements = table.querySelectorAll('merge-cell');

  // Process each merge-cell element
  mergeElements.forEach((mergeElement) => {
    // Find the parent TD or TH cell
    let cellElement = mergeElement.parentElement;
    while (
      cellElement &&
      cellElement.tagName !== 'TD' &&
      cellElement.tagName !== 'TH'
    ) {
      cellElement = cellElement.parentElement;
    }

    if (!cellElement) return; // Skip if no cell found

    // Get rowspan and colspan values
    const rowspan = parseInt(
      mergeElement.getAttribute('data-rowspan') || '1',
      10
    );
    const colspan = parseInt(
      mergeElement.getAttribute('data-colspan') || '1',
      10
    );

    // Set rowspan and colspan attributes on the cell
    cellElement.setAttribute('rowspan', rowspan.toString());
    cellElement.setAttribute('colspan', colspan.toString());

    // Replace the merge-cell element with its content
    cellElement.innerHTML = mergeElement.innerHTML;
  });
}

/**
 * Safely renders a mermaid diagram with error handling
 * @param diagramId Unique ID for the diagram container
 * @param mermaidCode The mermaid syntax to render
 * @returns Promise that resolves to an object with the rendered SVG or error display
 */
export function safeRenderMermaid(
  diagramId: string,
  mermaidCode: string
): Promise<{ svg: string; success: boolean }> {
  return new Promise((resolve) => {
    try {
      // First verify syntax (optional but helpful)
      try {
        // This is only available in newer versions of mermaid
        if (typeof mermaid.parse === 'function') {
          mermaid.parse(mermaidCode);
        }
      } catch (parseError) {
        // If parsing fails, create a fallback immediately
        throw parseError;
      }

      // If parse succeeds or isn't available, try to render
      mermaid
        .render(diagramId, mermaidCode)
        .then(({ svg }) => {
          resolve({ svg, success: true });
        })
        .catch((renderError) => {
          console.warn(`Mermaid render error for ${diagramId}:`, renderError);
          const fallbackSvg = createFallbackDiagram(
            renderError.message || 'Rendering error'
          );
          resolve({ svg: fallbackSvg, success: false });
        });
    } catch (error: any) {
      console.warn(`Mermaid diagram error for ${diagramId}:`, error);
      const fallbackSvg = createFallbackDiagram(
        error.message || 'Diagram syntax error'
      );
      resolve({ svg: fallbackSvg, success: false });
    }
  });
}

/**
 * Creates a user-friendly fallback SVG when a diagram cannot be rendered
 * @param errorMessage Error message to display
 * @returns SVG string with error information
 */
function createFallbackDiagram(errorMessage: string): string {
  const escapedError = errorMessage.replace(/"/g, '&quot;');
  return `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 120" width="100%">
      <rect width="500" height="120" fill="#f8f9fa" stroke="#e1e4e8" stroke-width="1" rx="5" ry="5"/>
      <text x="50%" y="40%" font-family="sans-serif" font-size="14" text-anchor="middle" fill="#333">Diagram could not be rendered</text>
      <text x="50%" y="60%" font-family="sans-serif" font-size="12" fill="#666" text-anchor="middle">Please check diagram syntax</text>
      <text x="50%" y="80%" font-family="sans-serif" font-size="10" fill="#999" text-anchor="middle" font-style="italic">${escapedError}</text>
    </svg>
  `;
}

/**
 * Extracts only the content under "<!-- Current page content -->" comment
 * If the marker isn't found, returns the original text
 *
 * @param text The input text that may contain overlap markers
 * @returns Text with only the current page content
 */
export function extractCurrentPageContent(text: string): string {
  // Define the markers
  // const previousMarker = '<!-- Overlap from previous page -->';
  const currentMarker = '<!-- Current page content -->';
  const nextMarker = '<!-- Overlap from next page -->';
  let currentContent = text.replace(/<PAGE>=+<\/PAGE>/g, '');
  // Check if current marker exists
  if (text.includes(currentMarker)) {
    // Split the content at the current marker
    const parts = text.split(currentMarker);

    // We want the content after the marker (parts[1])
    currentContent = parts[1] || '';
  }

  // If there's a next page marker, remove everything after it
  if (currentContent.includes(nextMarker)) {
    currentContent = currentContent.split(nextMarker)[0];
  }

  // Trim to remove any extra whitespace
  return currentContent.trim();
}

// Declaration merging to add the mermaidInitialized property to the Window interface
declare global {
  interface Window {
    mermaidInitialized?: boolean;
  }
}
