"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1721476964297 = void 0;
class SchemaUpdate1721476964297 {
    constructor() {
        this.name = 'SchemaUpdate1721476964297';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "file_upload_chunk"
       (
           "id"           uuid              NOT NULL DEFAULT uuid_generate_v4(),
           "fileUploadId" uuid              NOT NULL,
           "content"      character varying NOT NULL,
           "embedding"    text NOT NULL,
           CONSTRAINT "PK_b9152b5b35a051cbd111bed25cf" PRIMARY KEY ("id")
       )`);
        await queryRunner.query(`CREATE TABLE "file_upload"
       (
           "id"     uuid              NOT NULL DEFAULT uuid_generate_v4(),
           "userId" uuid              NOT NULL,
           "path"   character varying NOT NULL,
           CONSTRAINT "PK_bb8460e39fcad3aaa44d1d7e5d3" PRIMARY KEY ("id")
       )`);
        await queryRunner.query(`ALTER TABLE "file_upload_chunk"
          ADD CONSTRAINT "FK_080f57c1a27d482d6726935fd77" FOREIGN KEY ("fileUploadId") REFERENCES "file_upload" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "file_upload"
          ADD CONSTRAINT "FK_ef625dca9c38989d7eca19474fe" FOREIGN KEY ("userId") REFERENCES "user" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "file_upload" DROP CONSTRAINT "FK_ef625dca9c38989d7eca19474fe"`);
        await queryRunner.query(`ALTER TABLE "file_upload_chunk" DROP CONSTRAINT "FK_080f57c1a27d482d6726935fd77"`);
        await queryRunner.query(`DROP TABLE "file_upload"`);
        await queryRunner.query(`DROP TABLE "file_upload_chunk"`);
    }
}
exports.SchemaUpdate1721476964297 = SchemaUpdate1721476964297;
//# sourceMappingURL=1721476964297-schema-update.js.map