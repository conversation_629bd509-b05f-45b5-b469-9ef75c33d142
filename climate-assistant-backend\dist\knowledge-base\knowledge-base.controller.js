"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeBaseController = void 0;
const common_1 = require("@nestjs/common");
const upload_utils_1 = require("../util/upload-utils");
const knowledge_base_service_1 = require("./knowledge-base.service");
const swagger_1 = require("@nestjs/swagger");
let KnowledgeBaseController = class KnowledgeBaseController {
    constructor(knowledgeBaseService) {
        this.knowledgeBaseService = knowledgeBaseService;
    }
    async saveKnowledgeBaseFile(req, file) {
        const { path, originalname } = file;
        await this.knowledgeBaseService.saveFileWithEmbeddings(originalname, path);
        return { message: 'File wurde hochgeladen' };
    }
    async getKnowledgebaseFileUploads() {
        const fileUploads = await this.knowledgeBaseService.getUploadedFiles();
        return fileUploads;
    }
    async deleteKnowledgebaseFile(req, id) {
        await this.knowledgeBaseService.deleteFile(id);
        return { success: true };
    }
    async getEsrsDatapoints(esrs) {
        return this.knowledgeBaseService.getEsrsDatapointsByStandard(esrs);
    }
};
exports.KnowledgeBaseController = KnowledgeBaseController;
__decorate([
    (0, common_1.Post)('/file-upload'),
    (0, common_1.UseInterceptors)(upload_utils_1.fileInterceptor),
    (0, swagger_1.ApiOperation)({ summary: 'Upload a knowledge base file' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'File uploaded successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "saveKnowledgeBaseFile", null);
__decorate([
    (0, common_1.Get)('/file-uploads'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all knowledge base file uploads' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'File uploads retrieved successfully',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getKnowledgebaseFileUploads", null);
__decorate([
    (0, common_1.Delete)('/file-uploads/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a knowledge base file upload' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'File upload deleted successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "deleteKnowledgebaseFile", null);
__decorate([
    (0, common_1.Get)('/datapoints/:esrs'),
    (0, swagger_1.ApiOperation)({ summary: 'Get ESRS datapoints by standard' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'ESRS datapoints retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('esrs')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getEsrsDatapoints", null);
exports.KnowledgeBaseController = KnowledgeBaseController = __decorate([
    (0, swagger_1.ApiTags)('knowledge-base'),
    (0, common_1.Controller)('knowledge-base'),
    __metadata("design:paramtypes", [knowledge_base_service_1.KnowledgeBaseService])
], KnowledgeBaseController);
//# sourceMappingURL=knowledge-base.controller.js.map