import { Link2Icon, Link2OffIcon, ClipboardIcon } from 'lucide-react';
import { Link } from 'react-router-dom';
import { DialogDescription } from '@radix-ui/react-dialog';
import { useQuery } from '@tanstack/react-query';

import { Badge } from '../ui/badge';
import { MarkdownRenderer } from '../ui/markdown-renderer';
import { toast } from '../ui/use-toast';

import { Button, buttonVariants } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { cn, getFirstPageNumber, userHasRequiredRole } from '@/lib/utils';
import { updateDatapointLinkToDocumentChunk } from '@/api/documents/documents.api';
import { DatapointDocumentChunkMapData } from '@/types/project';
import { fetchDocumentLinksForDp } from '@/api/datapoint/datapoint-request.api';
import { useAuthentication } from '@/api/authentication/authentication.query';
import { USER_ROLE } from '@/constants/workspaceConstants';

export function DocumentLinks({
  documentChunkCount,
  datapointRequestId,
}: {
  documentChunkCount: number;
  datapointRequestId: string;
}) {
  const {
    data: documentLinksData,
    isLoading,
    refetch: fetchDocumentLinks,
  } = useQuery({
    queryKey: ['documentLinks', datapointRequestId],
    queryFn: () => fetchDocumentLinksForDp(datapointRequestId),
    enabled: false,
  });

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Badge
          className="rounded-md font-normal bg-slate-300"
          size="sm"
          variant={'secondary'}
          onClick={(e) => {
            e.stopPropagation();
            fetchDocumentLinks();
          }}
        >
          <Link2Icon className="h-3 w-3 mr-1" />
          {documentLinksData?.length || documentChunkCount} Document Links
        </Badge>
      </DialogTrigger>
      <DialogContent
        className="w-2/3 max-w-full text-slate-900"
        onClick={(e) => e.stopPropagation()}
      >
        {documentChunkCount == 0 ? (
          <>
            <DialogHeader>
              <DialogTitle>No Document Linked</DialogTitle>
              <DialogDescription>
                Add a Document-Link from the Document-Page. Simply click on a
                document and change the linked datapoints.
              </DialogDescription>
            </DialogHeader>
            <Link
              to="/documents"
              className={cn(buttonVariants(), 'w-fit px-8 mt-4')}
            >
              Show Documents
            </Link>
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>
                {documentLinksData?.length || documentChunkCount} Document Links
              </DialogTitle>
            </DialogHeader>
            {isLoading && <p>Loading linked Documents</p>}
            <DocumentLinkAccordion
              documentLinksData={documentLinksData}
              fetchDocumentLinks={fetchDocumentLinks}
            />
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}

export function DocumentLinkAccordion({
  documentLinksData,
  fetchDocumentLinks,
}: {
  documentLinksData: DatapointDocumentChunkMapData[];
  fetchDocumentLinks: () => void;
}) {
  const { user } = useAuthentication();
  if (!documentLinksData) return null;

  const sortedDocumentLinksData = documentLinksData.sort((a, b) => {
    const nameComparison = a.documentChunk.document.name.localeCompare(
      b.documentChunk.document.name
    );
    if (nameComparison !== 0) return nameComparison; // Sort by name first

    const aPageNum = getFirstPageNumber(a.documentChunk.page);
    const bPageNum = getFirstPageNumber(b.documentChunk.page);

    return aPageNum - bPageNum; // Then sort by page number if names are the same
  });

  // Group the sorted data by document name
  const groupedDocumentLinksData = sortedDocumentLinksData.reduce(
    (acc, documentLink) => {
      const documentName = documentLink.documentChunk.document.name;
      if (!acc[documentName]) {
        acc[documentName] = [];
      }
      acc[documentName].push(documentLink);
      return acc;
    },
    {} as Record<string, DatapointDocumentChunkMapData[]>
  );

  async function handleRemoveLink({
    documentChunkId,
    datapointRequestId,
  }: {
    documentChunkId: string;
    datapointRequestId: string;
  }) {
    try {
      await updateDatapointLinkToDocumentChunk(documentChunkId, [
        { datapointRequestId, linked: false },
      ]);
      toast({
        title: 'Document Link Removed',
      });
      fetchDocumentLinks();
    } catch (error) {
      toast({
        title: 'Error Removing Document Link',
        variant: 'destructive',
      });
    }
  }

  function getAllContent(
    documentLinksData: DatapointDocumentChunkMapData[]
  ): string {
    return documentLinksData
      .map((link) => {
        return `Document: ${link.documentChunk.document.name}\nPage: ${link.documentChunk.page}\n\n${link.documentChunk.content}\n\n---\n`;
      })
      .join('\n');
  }

  async function handleCopyContent() {
    try {
      const content = getAllContent(sortedDocumentLinksData);
      await navigator.clipboard.writeText(content);
      toast({
        title: 'Content copied to clipboard',
        variant: 'success',
      });
    } catch (error) {
      toast({
        title: 'Failed to copy content',
        variant: 'destructive',
      });
    }
  }

  return (
    <>
      {userHasRequiredRole([USER_ROLE.SuperAdmin], user) && (
        <div className="flex justify-end mb-4">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            onClick={handleCopyContent}
          >
            <ClipboardIcon className="h-4 w-4" />
            Copy All Content
          </Button>
        </div>
      )}
      <Accordion
        type="single"
        collapsible
        className="w-full flex flex-col space-y-6 max-h-[80vh] overflow-y-auto"
      >
        {Object.entries(groupedDocumentLinksData) &&
          Object.entries(groupedDocumentLinksData).map(
            ([documentName, links]) => (
              <div key={documentName} className="w-full space-y-2">
                <Link
                  to={`/documents/${links[0].documentChunk.document.id}`}
                  className="underline font-semibold mb-2"
                >
                  {documentName}
                </Link>
                {links &&
                  links.map((documentLink, index) => (
                    <AccordionItem
                      key={index}
                      value={documentLink.documentChunk.id}
                      className="w-full border-none bg-slate-100 rounded-lg"
                    >
                      <AccordionTrigger className="flex items-center justify-between w-full p-4">
                        <div className="flex items-center space-x-2 justify-between w-full mr-5">
                          <span className="text-sm text-left">
                            Extracted from Page{' '}
                            {documentLink.documentChunk.page}
                          </span>
                          <div className="flex items-center gap-2 justify-between">
                            <Button
                              variant="outline"
                              size="xs"
                              className="flex items-center bg-transparent"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveLink({
                                  documentChunkId:
                                    documentLink.documentChunk.id,
                                  datapointRequestId:
                                    documentLink.datapointRequestId,
                                });
                              }}
                            >
                              <Link2OffIcon className="h-4 w-4 mr-1" />
                              Remove Link
                            </Button>
                          </div>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="px-4 w-full max-w-[70vw] overflow-x-auto">
                        <MarkdownRenderer
                          text={documentLink.documentChunk.content}
                        />
                      </AccordionContent>
                    </AccordionItem>
                  ))}
              </div>
            )
          )}
      </Accordion>
    </>
  );
}
