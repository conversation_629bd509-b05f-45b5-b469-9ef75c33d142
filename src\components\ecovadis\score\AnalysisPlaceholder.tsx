
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Target } from 'lucide-react';

interface AnalysisPlaceholderProps {
  onRunAnalysis?: () => void;
  disabled?: boolean;
}

export const AnalysisPlaceholder = ({ onRunAnalysis, disabled }: AnalysisPlaceholderProps) => {
  return (
    <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
      <p className="text-gray-600 font-medium mb-2">
        Run AI Gap + Score Analysis to get an estimated score
      </p>
      <p className="text-gray-500 text-sm mb-4">
        This will analyze your documents and provide improvement suggestions
      </p>
      {onRunAnalysis && (
        <Button 
          onClick={onRunAnalysis}
          disabled={!!disabled}
          className="bg-glacier-darkBlue hover:bg-glacier-darkBlue/90 text-white flex items-center gap-1 mx-auto"
        >
          <Target className="h-3.5 w-3.5" />
          <span>AI Gap + Score Analysis</span>
        </Button>
      )}
    </div>
  );
};
