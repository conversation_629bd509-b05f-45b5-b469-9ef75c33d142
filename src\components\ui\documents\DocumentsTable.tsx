import { useState, useEffect } from 'react';
import { DocumentStatus } from '@/types/document';
import { DocumentUpload } from '@/types/project';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow
} from '@/components/ui/table';
import { 
  FileText, 
  FileSpreadsheet, 
  FileCog, 
  FilePieChart, 
  FileImage, 
  Download, 
  Trash2, 
  Link2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { DeleteDocumentDialog } from '@/components/DocumentsDeleteConfirm';
import { useNavigate } from 'react-router-dom';
import { reindexDocument } from '@/api/workspace-settings/workspace-settings.api';

interface DocumentsTableProps {
  documents: DocumentUpload[];
  searchQuery: string;
  loading: boolean;
}

export function DocumentsTable({ documents, searchQuery, loading }: DocumentsTableProps) {
  const [filteredDocuments, setFilteredDocuments] = useState<DocumentUpload[]>(documents);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);
  const navigate = useNavigate();
  
  useEffect(() => {
    if (documents && documents.length > 0) {
      const filtered = documents.filter(doc => 
        doc.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredDocuments(filtered);
    } else {
      setFilteredDocuments([]);
    }
  }, [documents, searchQuery]);
  
  const handleDocumentClick = (documentId: string) => {
    // Navigate to document detail page
    navigate(`/documents/${documentId}`);
  };
  
  const handleDownload = (e: React.MouseEvent, document: DocumentUpload) => {
    e.stopPropagation();
    toast.success(`Downloading ${document.name}`, {
      description: "Your file will be downloaded shortly."
    });
    // Implement actual download logic here
    window.open(document.path, '_blank');
  };
  
  const handleDelete = (e: React.MouseEvent, documentId: string) => {
    e.stopPropagation();
    setDocumentToDelete(documentId);
    setDeleteDialogOpen(true);
  };

  const handleIndexing = async (e: React.MouseEvent, documentId: string) => {
    e.stopPropagation();
    try {
      toast.loading(`Indexing document...`, {
        description: "This may take a few seconds."
      });
      await reindexDocument(documentId);
      toast.success('Indexing complete', {
        description: "Document re-indexing has been successfully completed."
      });
    } catch (error) {
      toast.error('Error Indexing document', {
        description: "An error occurred while re-indexing the document."
      });
    }
  };
  
  const getDocumentIcon = (type: string | null, name: string) => {
    if (name.endsWith('.xlsx') || name.endsWith('.xls')) {
      return <FileSpreadsheet className="h-5 w-5 text-emerald-600" />;
    } else if (name.endsWith('.jpg') || name.endsWith('.png') || name.endsWith('.jpeg')) {
      return <FileImage className="h-5 w-5 text-blue-500" />;
    }
    
    switch (type) {
      case 'policy':
        return <FileText className="h-5 w-5 text-red-500" />;
      case 'certificate':
        return <FileCog className="h-5 w-5 text-green-600" />;
      case 'sustainability_report':
        return <FilePieChart className="h-5 w-5 text-red-500" />;
      case 'reporting_document':
        return <FileSpreadsheet className="h-5 w-5 text-emerald-600" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };
  
  const getStatusLabel = (status: DocumentStatus | null) => {
    if (!status) return 'Not processed';
    
    switch (status) {
      case DocumentStatus.NotProcessed:
        return 'Not processed';
      case DocumentStatus.QueuedForExtraction:
      case DocumentStatus.ExtractingData:
        return 'Processing';
      case DocumentStatus.DataExtractionFinished:
      case DocumentStatus.LinkingDataFinished:
        return 'Complete';
      case DocumentStatus.FailedExtraction:
      case DocumentStatus.FailedLinking:
      case DocumentStatus.ErrorProcessing:
        return 'Failed';
      default:
        return String(status);
    }
  };
  
  const getStatusColor = (status: DocumentStatus | null) => {
    if (!status) return "bg-gray-100 text-gray-600";
    
    switch (status) {
      case DocumentStatus.NotProcessed:
        return "bg-gray-100 text-gray-600";
      case DocumentStatus.QueuedForExtraction:
      case DocumentStatus.ExtractingData:
      case DocumentStatus.QueuedForLinking:
      case DocumentStatus.LinkingData:
        return "bg-blue-100 text-blue-600";
      case DocumentStatus.DataExtractionFinished:
      case DocumentStatus.LinkingDataFinished:
        return "bg-green-100 text-green-600";
      case DocumentStatus.FailedExtraction:
      case DocumentStatus.FailedLinking:
      case DocumentStatus.ErrorProcessing:
        return "bg-red-100 text-red-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };
  
  if (loading) {
    return (
      <div className="p-8 text-center">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="w-full overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[40%]">Name</TableHead>
            <TableHead className="w-[20%]">Type</TableHead>
            <TableHead className="w-[15%]">Status</TableHead>
            <TableHead className="w-[15%]">Date</TableHead>
            <TableHead className="w-[10%] text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredDocuments.length > 0 ? (
            filteredDocuments.map(doc => (
              <TableRow 
                key={doc.id} 
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => handleDocumentClick(doc.id)}
              >
                <TableCell className="py-3">
                  <div className="flex items-center">
                    {getDocumentIcon(doc.documentType, doc.name)}
                    <span className="ml-2 text-sm font-medium text-glacier-darkBlue">{doc.name}</span>
                  </div>
                </TableCell>
                <TableCell>{doc.documentType || 'Unknown'}</TableCell>
                <TableCell>
                  <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(doc.status as DocumentStatus)}`}>
                    {getStatusLabel(doc.status as DocumentStatus)}
                  </div>
                </TableCell>
                <TableCell>{formatDate(doc.createdAt)}</TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end space-x-1" onClick={e => e.stopPropagation()}>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 text-gray-500 hover:text-glacier-darkBlue"
                      onClick={(e) => handleDownload(e, doc)}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 text-gray-500 hover:text-red-500"
                      onClick={(e) => handleDelete(e, doc.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 text-gray-500 hover:text-sky-500"
                      onClick={(e) => handleIndexing(e, doc.id)}
                    >
                      <Link2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={5} className="h-24 text-center">
                No documents found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      
      {documentToDelete && (
        <DeleteDocumentDialog
          id={documentToDelete}
          open={deleteDialogOpen}
          setOpen={setDeleteDialogOpen}
        />
      )}
    </div>
  );
}
