export interface ESRSReviewResponse {
  inputTokensUsed: number;
  outputTokensUsed: number;
  costForDocument: number;
  timeToGenerate: string;
  numberOfPages: number;
  pages: PagesEntity[];
}
export interface PagesEntity {
  pageNumber: number;
  textOnPage: string;
  relevantTopics?: string[] | null;
  relevantDisclosureRequirements?: string[] | null;
  paragraphs?: ParagraphsEntity[] | null;
}
export interface ParagraphsEntity {
  paragraph: number;
  textInParagraph: string;
  relevantForDisclosureIDs?: string[] | null;
}

export interface ESRSDatPointLinkResponse {
  inputTokensUsed: number;
  outputTokensUsed: number;
  costForDocument: number;
  timeToGenerate: string;
  numberOfChunks: number;
  totalLinksCreated: number;
  tokensForTopicClassification?: {};
  chunks: ChunkMatches[];
}

export interface ChunkMatches {
  chunkContent: string;
  topicMatches?: Match[] | null;
  disclosureRequirementMatches?: string[] | null;
  datapointMatches?: Match[] | null;
}

export interface ChunkLink {
  text: string;
  certainty: number;
  reason: string;
}

export interface EsrsDatapointDocumentLinks {
  datapointId: string;
  chunkLinks: [ChunkLink];
}

export interface Match {
  matchedId: string;
  reason: string;
  mdrTitle?: string;
  hierarchy?: string;
  extractedText?: string;
  executedClassificationPrompt: boolean | false;
  tokensForClassification: {};
}
export interface DocumentChunkGenerated {
  text: string;
  metadata: {
    headings?: string[];
    sourceDocumentName?: string;
    mainSections?: string[];
    pageNumber: string;
    chunkNumber: number;
  };
}
