// @ts-ignore
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { corsHeaders } from '../_shared/cors.ts';
import { authValidator } from '../_shared/authValidator.ts';

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
  
  try {
    // Parse request data - use destructuring with default values for optional fields
    const { name, type } = await req.json();
    
    // Fast validation - return early if missing required fields
    if (!name || !name.trim()) {
      return new Response(JSON.stringify({
        error: 'Project name is required'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }
    
    // Authenticate user
    const { user, error, supabaseClient, response } = await authValidator(req);
    if (!user || error || !supabaseClient) {
      return response;
    }
    
    const workspaceId = user.user_workspace[0].workspaceId;
    const projectType = type?.toLowerCase();
    
    // Map project types - use object lookup for performance
    const PROJECT_TYPES = {
      'ecovadis': 'EcoVadis'
    };
    
    // Single database operation with all needed fields
    const { data: project, error: projectError } = await supabaseClient
      .from('project')
      .insert({
        name: name.trim(),
        type: PROJECT_TYPES['ecovadis'], // Fallback to original if not in mapping
        workspaceId: workspaceId,
        primaryContentLanguage: 'EN',
        createdBy: user.id,
        reportTextGenerationRules: ''
      })
      .select('id, name, type, workspaceId') // Only select fields we need
      .single();
    
    if (projectError) {
      console.error('Error creating project:', projectError);
      return new Response(JSON.stringify({
        error: 'Failed to create project',
        details: projectError.message
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }
    
    // Return optimized response
    return new Response(JSON.stringify({
      project
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 201 // Created status for resource creation
    });
    
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});