import { FunctionComponent, useState } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import {
  EllipsisIcon,
  PenIcon,
  SidebarIcon,
  SquarePen,
  TrashIcon,
} from 'lucide-react';

import { useHistories } from '@/api/chats/histories.state.ts';
import { ChatHistory } from '@/models/chat.models.ts';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu.tsx';
import { Button } from '@/components/ui/button.tsx';
import {
  MixpanelEvents,
  MixpanelService,
} from '@/services/mixpanel.service.ts';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog.tsx';
import { Input } from '@/components/ui/input.tsx';
import { updateHistory } from '@/api/chats/chats.api.ts';
import { toast } from '@/components/ui/use-toast.ts';

const Sidebar: FunctionComponent<{
  isSidebarOpen: boolean;
  onSidebarToggle: () => void;
}> = ({ isSidebarOpen, onSidebarToggle }) => {
  const { id: activeHistoryId } = useParams<{ id: string }>();
  const { histories, removeHistory } = useHistories();
  const {
    isDialogOpen,
    historyTitle,
    openDialog,
    renamingHistoryId,
    closeDialog,
    onSave,
    historyTitleChanged,
  } = useRenameDialog();

  return (
    <div
      className={`${isSidebarOpen ? 'w-[300px]' : 'w-0'} bg-gray-100 flex flex-col overflow-hidden transition-all h-screen`}
    >
      <RenameDialog
        historyId={renamingHistoryId}
        isDialogOpen={isDialogOpen}
        historyTitle={historyTitle}
        closeDialog={closeDialog}
        onSave={onSave}
        historyTitleChanged={historyTitleChanged}
      ></RenameDialog>
      <div className={`flex flex-col px-5 pt-4 pb-2`}>
        <div className={`flex justify-between items-center -mx-2`}>
          <SideBarToggleButton
            onClick={() => onSidebarToggle()}
          ></SideBarToggleButton>
          <StartNewChatButton></StartNewChatButton>
        </div>
        <div
          className={`mt-8 text-sm  font-semibold`}
          style={{ color: '#0A2344' }}
        >
          Chats
        </div>
      </div>
      <div className="flex flex-col overflow-y-auto">
        {histories?.map((history) => {
          return (
            <SidebarElement
              key={history.id}
              history={history}
              activeId={activeHistoryId}
              onDelete={() => void removeHistory(history.id)}
              onRename={() => openDialog(history.id, history.title)}
            ></SidebarElement>
          );
        })}
      </div>
    </div>
  );
};

const SidebarElement: FunctionComponent<{
  history: ChatHistory;
  activeId: string | undefined;
  onDelete: () => void;
  onRename: () => void;
}> = ({ history, activeId, onDelete, onRename }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  return (
    <Link
      to={`/chats/${history.id}`}
      onClick={() => MixpanelService.track(MixpanelEvents.historySelected())}
    >
      <div
        className={`${history.id === activeId ? 'bg-gray-200' : ''} chat-history-menu-item ${isMenuOpen ? 'chat-history-menu-item--open' : ''} relative px-4 py-2 mx-2 my-1  text-left cursor-pointer hover:bg-gray-200 transition-all border-0 hover:border-0 active:border-0 ring-0 focus:ring-0 font-normal rounded`}
      >
        <div
          className={`overflow-hidden whitespace-nowrap overflow-ellipsis text-sm text-slate-800`}
        >
          {history?.title ?? 'Neuer Chat'}
        </div>

        <div className={`absolute right-1 top-1`}>
          <DropdownMenu onOpenChange={setIsMenuOpen}>
            <DropdownMenuTrigger
              className={`edit-chat-history h-7 w-7 flex justify-center items-center border-0 hover:bg-gray-300 text-gray-800`}
            >
              <EllipsisIcon className="h-4 w-4" />
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                className={`cursor-pointer`}
                onClick={() => onRename()}
              >
                <PenIcon size={14} className={`mr-3`}></PenIcon>
                <div>Umbenennen</div>
              </DropdownMenuItem>
              <DropdownMenuItem
                className={`cursor-pointer text-red-600`}
                onClick={() => onDelete()}
              >
                <TrashIcon size={14} className={`mr-3`}></TrashIcon>
                <div>Löschen</div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </Link>
  );
};

const useRenameDialog = () => {
  const { refetchHistories } = useHistories();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [historyTitle, setHistoryTitle] = useState('');
  const [renamingHistoryId, setRenamingHistoryId] = useState<
    string | undefined
  >(undefined);

  const openDialog = (historyId: string, title: string) => {
    setRenamingHistoryId(historyId);
    setHistoryTitle(title);
    setIsDialogOpen(true);
  };

  const onSave = async () => {
    if (renamingHistoryId) {
      await void updateHistory(renamingHistoryId, { title: historyTitle }).then(
        () => {
          void refetchHistories();
          toast({
            variant: 'success',
            description: 'Chat wurde umbenannt',
          });
        }
      );
      setIsDialogOpen(false);
    }
  };

  return {
    isDialogOpen,
    historyTitle,
    openDialog,
    renamingHistoryId,
    closeDialog: () => setIsDialogOpen(false),
    onSave,
    historyTitleChanged: setHistoryTitle,
  };
};

const RenameDialog: FunctionComponent<{
  isDialogOpen: boolean;
  historyId: string | undefined;
  historyTitle: string;
  closeDialog: () => void;
  onSave: () => void;
  historyTitleChanged: (title: string) => void;
}> = ({
  isDialogOpen,
  historyTitle,
  closeDialog,
  onSave,
  historyTitleChanged,
}) => {
  return (
    <Dialog
      open={isDialogOpen}
      modal
      onOpenChange={(open) => (!open ? closeDialog() : undefined)}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Chat umbennenen</DialogTitle>
          <div className={`pt-6 pb-5`}>
            <div>Geben Sie einen neuen Namen für den Chat ein.</div>

            <div className={`mt-3`}>
              <Input
                value={historyTitle}
                onChange={(event) => historyTitleChanged(event.target.value)}
              ></Input>
            </div>

            <div className={`mt-5 flex flex-row gap-3`}>
              <Button
                variant="darkBlue"
                className={`rounded-full`}
                style={{ backgroundColor: '#143560' }}
                onClick={() => onSave()}
              >
                Speichern
              </Button>
              <Button
                variant="outline"
                className={`rounded-full`}
                onClick={() => closeDialog()}
              >
                Abbrechen
              </Button>
            </div>
          </div>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
};

export const SideBarToggleButton: FunctionComponent<{
  onClick: () => void;
}> = ({ onClick }) => {
  return (
    <Button variant="button" size="icon" onClick={onClick}>
      <SidebarIcon className={`h-5 w-5`}></SidebarIcon>
    </Button>
  );
};

export const StartNewChatButton: FunctionComponent = () => {
  const navigate = useNavigate();
  return (
    <Button variant="button" size="icon" onClick={() => navigate('/')}>
      <SquarePen className={`h-5 w-5`}></SquarePen>
    </Button>
  );
};

export { Sidebar };
