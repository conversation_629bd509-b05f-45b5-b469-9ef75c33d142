
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Check, X } from "lucide-react";

export const LaborRightsScoringBreakdown = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-semibold">Score Assessment</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Score</TableHead>
                <TableHead>Level</TableHead>
                <TableHead>Definition</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell className="font-semibold">50</TableCell>
                <TableCell>Good</TableCell>
                <TableCell>Qualitative objectives or quantitative targets are included, and more than 33% of key sustainability issues are covered.</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-semibold">Scoring Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Framework Criteria</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Explanation</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell>Qualitative objectives or quantitative targets present</TableCell>
                <TableCell>
                  <Check className="text-green-500 h-5 w-5" />
                </TableCell>
                <TableCell>
                  Several documents (e.g., Anti-Harassment Policy, Code of Ethics, Safety Overview, Hiring Process, Training Manual) contain general qualitative commitments to key labor topics such as safety, ethics, inclusion, and learning opportunities.
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>% of key sustainability issues covered</TableCell>
                <TableCell>
                  <Check className="text-green-500 h-5 w-5" />
                  <span className="ml-1">(&gt;33%)</span>
                </TableCell>
                <TableCell>
                  <p>At least 4 out of 6 labor-related topics are now covered:</p>
                  <ol className="list-decimal ml-5 mt-2 space-y-1">
                    <li>Health &amp; Safety – Glacier Safety Training Overview, Responsibility Report</li>
                    <li>Ethics &amp; Compliance – Code of Ethics, Whistleblower Policy</li>
                    <li>Working Conditions – Hiring Process, Responsibility Report</li>
                    <li>Training &amp; Career Development – Training Manual, Internal E-Learnings</li>
                  </ol>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Quantitative targets present</TableCell>
                <TableCell>
                  <X className="text-red-500 h-5 w-5" />
                </TableCell>
                <TableCell>
                  None of the current policies include measurable goals (e.g., % of trained employees, incident rates, gender targets).
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Review mechanism defined in policies</TableCell>
                <TableCell>
                  <X className="text-red-500 h-5 w-5" />
                </TableCell>
                <TableCell>
                  Policies do not yet mention update cycles, responsibilities for review, or version history.
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-semibold">Conclusion: Why the Score is 50 (Good)</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700">
            Thanks to broader policy coverage with qualitative objectives, your policy framework now meets the "Good" level criteria under EcoVadis.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};
