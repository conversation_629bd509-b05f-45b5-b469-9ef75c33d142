"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeBaseFileUpload = void 0;
const typeorm_1 = require("typeorm");
const knowledge_base_file_upload_chunk_entity_1 = require("./knowledge-base-file-upload-chunk.entity");
let KnowledgeBaseFileUpload = class KnowledgeBaseFileUpload {
};
exports.KnowledgeBaseFileUpload = KnowledgeBaseFileUpload;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], KnowledgeBaseFileUpload.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], KnowledgeBaseFileUpload.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], KnowledgeBaseFileUpload.prototype, "path", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => knowledge_base_file_upload_chunk_entity_1.KnowledgeBaseFileUploadChunk, (chunk) => chunk.fileUpload),
    __metadata("design:type", Array)
], KnowledgeBaseFileUpload.prototype, "chunks", void 0);
exports.KnowledgeBaseFileUpload = KnowledgeBaseFileUpload = __decorate([
    (0, typeorm_1.Entity)()
], KnowledgeBaseFileUpload);
//# sourceMappingURL=knowledge-base-file-upload.entity.js.map