import { FunctionComponent, useEffect } from 'react';
import { useParams } from 'react-router-dom';

import { Chat } from '@/components/Chat';
import { useChatGPT } from '@/services/chat-gpt.ts';

const ChatGptExample: FunctionComponent = () => {
  const { id } = useParams<{ id: string }>();
  const {
    sendGPTMessage,
    messages,
    pendingMessage,
    isMessagePending,
    refetchHistory,
  } = useChatGPT(id!);

  useEffect(() => {
    void refetchHistory();
  }, [id]);

  return (
    <Chat
      messages={messages}
      sendMessage={sendGPTMessage}
      pendingMessage={pendingMessage}
      isMessagePending={isMessagePending}
    ></Chat>
  );
};

export { ChatGptExample };
