
import { useState } from "react";
import { GapItem as GapItemType } from "@/types/ecovadis";
import { DocumentUploadModal } from "@/components/ui/documents/DocumentUploadModal";
import { GapCard } from "./gaps/GapCard";
import { GapFixModal } from "./gaps/GapFixModal";
import ConfirmDialog from "@/components/ConfirmDialog";

interface GapItemProps {
  gap: GapItemType;
  onMarkAsComplete: (id: string, complete: boolean) => Promise<void>;
  onDeleteGap?: (id: string) => Promise<boolean>;
  onAssignUser?: (id: string, userId: string | null) => Promise<void>;
  users?: Array<{ id: string, name: string }>;
}

export const GapItem = ({ gap, onMarkAsComplete, onDeleteGap, onAssignUser, users = [] }: GapItemProps) => {
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showFixGapDialog, setShowFixGapDialog] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [currentActionGapId, setCurrentActionGapId] = useState<string | null>(null);  

  // Make sure we're using the correct isComplete status (resolved from backend)
  const isComplete = gap.resolved || gap.isComplete || false;

  const handleMarkComplete = (id: string, complete: boolean) => {
    setCurrentActionGapId(id);
    onMarkAsComplete(id, complete).finally(() => {
      setCurrentActionGapId(null);
    })
  };

  const handleDeleteGap = (id: string) => {
    setShowDeleteConfirm(true);
  };

  const handleConfirmDelete = async () => {
    setShowDeleteConfirm(false);
    if (onDeleteGap) {
      setIsDeleting(true);
      try {
        await onDeleteGap(gap.id);
      } finally {
        // setIsDeleting(false);
      }
    }
  };

  const handleAssignUser = async (id: string, userId: string | null) => {
    if (onAssignUser) {
      await onAssignUser(id, userId);
    }
  };

  // Get the appropriate related document for the modal
  const relatedDocument = gap.documents || [];

  return (
    <>
      <GapCard 
        gap={{...gap, isComplete}}
        onMarkAsComplete={handleMarkComplete}
        onDeleteGap={handleDeleteGap}
        onAssignUser={handleAssignUser}
        users={users}
        onFixGapClick={() => setShowFixGapDialog(true)}
        isDeleting={isDeleting}
        currentActionGapId={currentActionGapId || undefined}
      />
      
      <GapFixModal 
        open={showFixGapDialog}
        onOpenChange={setShowFixGapDialog}
        relatedDocument={relatedDocument}
        gap={gap}
      />
      
      <DocumentUploadModal 
        open={showUploadModal}
        onOpenChange={setShowUploadModal}
      />

      <ConfirmDialog
        open={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        title="Delete Gap"
        description="Are you sure you want to delete this gap? This action cannot be undone."
        onConfirm={handleConfirmDelete}
      />
    </>
  );
};
