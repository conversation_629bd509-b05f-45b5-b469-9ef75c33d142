import { FunctionComponent, ReactNode, useState } from 'react';

import { Sidebar } from '@/components/Sidebar';
import { TopNavigation } from '@/components/TopNavigation';
import {
  MixpanelEvents,
  MixpanelService,
} from '@/services/mixpanel.service.ts';

export const ChatLayout: FunctionComponent<{ children: ReactNode }> = ({
  children,
}) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  const sidebarToggleClicked = () => {
    MixpanelService.track(MixpanelEvents.sidebarToggled());
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="flex flex-row">
      <Sidebar
        isSidebarOpen={isSidebarOpen}
        onSidebarToggle={() => sidebarToggleClicked()}
      ></Sidebar>
      <div className="flex flex-1 flex-col">
        <TopNavigation
          isSidebarOpen={isSidebarOpen}
          onSidebarToggle={() => sidebarToggleClicked()}
        ></TopNavigation>

        {children}
      </div>
    </div>
  );
};
