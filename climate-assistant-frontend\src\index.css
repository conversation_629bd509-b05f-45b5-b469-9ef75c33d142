:root {
    font-family: Pantea, sans-serif;
    line-height: 1.5;
    font-weight: 400;

    color-scheme: light;
    color: rgba(255, 255, 255, 0.87);
    background-color: #242424;

    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
    font-weight: 500;
    color: #646cff;
    text-decoration: inherit;
}

a:hover {
    color: #535bf2;
}

body {
    margin: 0;
    min-width: 320px;
    min-height: 100vh;
    min-height: 100dvh;
}

h1 {
    font-size: 3.2em;
    line-height: 1.1;
}

button {
    border-radius: 8px;
    border: 1px solid transparent;
    padding: 0.6em 1.2em;
    font-size: 1em;
    font-weight: 500;
    font-family: inherit;
    background-color: #1a1a1a;
    cursor: pointer;
    transition: border-color 0.25s;
}

button:hover {
    border-color: #646cff;
}

button:focus,
button:focus-visible {
    outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
    :root {
        color: #213547;
        background-color: #ffffff;
    }

    a:hover {
        color: #747bff;
    }

    button {
        background-color: #f9f9f9;
    }
}


.markdown-response {
    font-family: Pantea-Text, sans-serif;

    :first-child {
        margin-top: 0;
    }

    p:not(:first-child) {
        margin-top: 20px;
    }

    p:not(:last-child) {
        margin-bottom: 20px;
    }

    li p {
        margin-top: 0;
        margin-bottom: 0 !important;
    }

    li {
        margin-bottom: 0.5em;
        margin-top: 0.5em;
    }

    ul,
    ol {
        list-style: initial;
        padding-left: 40px;
        margin-top: 16px;

        ul,
        ol {
            margin-top: 0;
        }
    }

    h1 {
        @apply text-2xl font-bold leading-tight text-black;
        margin-bottom: 0.5rem;
        margin-top: 1rem;
    }

    h2 {
        @apply text-xl font-semibold leading-snug text-black;
        margin-bottom: 0.5rem;
        margin-top: 1rem;
    }

    h3 {
        @apply text-lg font-medium leading-normal text-black;
        margin-bottom: 0.5rem;
        margin-top: 1rem;
    }

    b,
    strong {
        font-weight: 600;
    }

    .markdown-button-style,
    .markdown-detail-button,
    .markdown-esrs-selector {
        @apply text-sm font-medium border hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 mr-4 rounded-full border-black
    }

}

.shine {
    position: relative;
    overflow: hidden;
    width: 600px;
}

.shine-text {
    color: gray;
}

.shine-helper {
    position: absolute;
    top: 0;
    height: 100%;
    left: -100px;
    width: 30%;
    background: linear-gradient(to right, white, transparent 50%);
    -webkit-text-fill-color: transparent;
    animation: shine 2s infinite ease-out;
    overflow: hidden;
}

@keyframes shine {
    0% {
        left: -10%;
    }

    100% {
        left: 110%
    }
}

button:focus,
button:focus-visible {
    outline: none;
}

button:hover {
    border-color: transparent;
}

/* Text Editor */
.ql-toolbar.ql-snow+.ql-container.ql-snow {
    border-top: 1px solid #ccc;
    padding-bottom: 40px;
    background-color: white;
}

.ql-toolbar.ql-snow {
    background-color: rgba(243, 245, 247, 1);
    width: fit-content;
    border: none;
    padding-left: 14px;
    padding-top: 10px;
    padding-bottom: 10px;
    border-radius: 10px;
}

.ql-toolbar.ql-snow .ql-picker-options {
    display: block;
    opacity: 0;
    transform: scale(0.95);
    border: 1px solid transparent;
    box-shadow: rgb(0 0 0 / 10%) 0 2px 8px;
    border-radius: 12px;
    border-color: #cccccc8f !important;
    transition: all 100ms ease-in-out;
}

.ql-toolbar.ql-snow .ql-picker {
    border-radius: 5px;
    width: 130px;
    background-color: white;
    border: 1px solid rgba(192, 202, 215, 1);
    height: 33px;
}

.ql-toolbar.ql-snow .ql-picker .ql-picker-label::before {
    margin-top: 3px;
    color: rgba(0, 0, 0, 1);
}

.ql-toolbar.ql-snow .ql-picker .ql-picker-label svg polygon {
    stroke: rgba(133, 155, 177, 1) !important;
}

.ql-toolbar.ql-snow .ql-formats {
    background-color: rgba(230, 235, 239, 1);
    padding: 4px;
    border-radius: 5px;
}

.ql-toolbar.ql-snow .ql-formats:has(.ql-picker) {
    padding: 0px;
}

.ql-toolbar.ql-snow .ql-formats button {
    border-radius: 4px;
    padding: 8px;
    height: 33px;
    aspect-ratio: 1 / 1;
    width: auto;
    margin: 0px 2px;
}

.ql-toolbar.ql-snow .ql-formats button:hover {
    background-color: #f1f5f9;
    color: rgba(0, 0, 0, 1);
}

.ql-toolbar.ql-snow .ql-formats button.ql-active {
    background-color: white;
    color: rgba(0, 0, 0, 1);
}

.ql-toolbar.ql-snow .ql-formats button.ql-active svg path,
.ql-toolbar.ql-snow .ql-formats button.ql-active svg line {
    stroke: rgba(0, 0, 0, 1);
}

.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
    opacity: 1;
    transform: scale(1);
    padding: 5px;
    padding-top: 0px;
    padding-bottom: 0px;
}

.ql-toolbar.ql-snow .ql-picker-options .ql-picker-item {
    padding-left: 10px;
    padding-right: 10px;
    border-radius: 10px;
    margin-top: 5px;
    margin-bottom: 5px;
}

.ql-toolbar.ql-snow .ql-picker-options .ql-picker-item:hover {
    background-color: #f1f5f9;
    color: unset;
}

.ql-snow {
    box-sizing: border-box;
    border-radius: 6px;
    margin: 6px 0px;
}


/* Markdown Editor */

.w-md-editor {
    border: none;
    box-shadow: none;
    display: flex;
    flex-direction: column;
    gap: 10px;
    background: transparent;
}

.w-md-editor-toolbar {
    border: none;
    background-color: transparent;
}

.w-md-editor-toolbar ul {
    margin-top: 4px;
    height: 50px;
    background-color: rgba(230, 235, 239, 1);
    width: fit-content;
    border: none;
    padding-left: 14px;
    padding-right: 14px;
    border-radius: 10px;
    display: flex;
    align-items: center;
}

.w-md-editor-toolbar li>button {
    padding: 10px;
    border-radius: 5px;
    height: 35px;
    aspect-ratio: 1 / 1;
}

.w-md-editor-content {
    border: 1px solid #ccc;
    border-radius: 10px;
    background-color: white;
}

.w-md-editor-toolbar-child .w-md-editor-toolbar ul>li button {
    padding: 1px 10px 1px 10px;
    height: 35px;
}

.w-md-editor-toolbar-child,
.w-md-editor-toolbar-child>.w-md-editor-toolbar {
    box-shadow: none;
    background-color: transparent;
}

.w-md-editor-toolbar-child>.w-md-editor-toolbar {
    margin-top: 8px;
}

/* 
 Tiptap Editor
 */

.ProseMirror.ProseMirror:focus,
.ProseMirror.ProseMirror:focus-visible {
    outline-color: transparent;
    outline: none;
}

.react-multi-email {
    width: 65% !important;
}