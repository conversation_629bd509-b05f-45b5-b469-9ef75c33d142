import { MainLayout } from '@/components/MainLayout';
import { Skeleton } from '@/components/ui/skeleton';

function PageLoading() {
  return (
    <MainLayout>
      <div className="space-y-5 my-16 mx-10">
        <div className="space-y-8">
          <div className="flex items-center justify-between">
            <Skeleton className="w-1/3 h-8" />
            <div className="flex items-center space-x-2">
              <Skeleton className="w-36 h-8" />
              <Skeleton className="w-12 h-6" />
            </div>
          </div>
          <div className="flex justify-start items-center gap-10">
            <div className="flex items-center space-x-3">
              <Skeleton className="w-16 h-6" />
              <Skeleton className="w-24 h-8" />
            </div>
            <div className="flex items-center space-x-3">
              <Skeleton className="w-16 h-6" />
              <Skeleton className="w-40 h-8" />
            </div>
            <div className="flex items-center space-x-3">
              <Skeleton className="w-20 h-6" />
              <Skeleton className="w-32 h-8" />
            </div>
          </div>
        </div>

        <div className="space-y-5">
          <Skeleton className="w-64 h-8" />
          <div className="grid grid-cols-2 gap-1 mb-5 w-fit">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-8 w-48" />
          </div>

          <div className="space-y-5">
            <Skeleton className="w-1/2 h-6" />
            <Skeleton className="w-full h-12" />
            <Skeleton className="w-full h-16" />
            <Skeleton className="mt-6 w-40 h-10" />
          </div>
        </div>
      </div>
    </MainLayout>
  );
}

export default PageLoading;
