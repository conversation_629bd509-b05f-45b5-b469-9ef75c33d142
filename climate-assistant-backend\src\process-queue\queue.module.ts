import { Modu<PERSON> } from '@nestjs/common';
import { BullBoardModule } from '@bull-board/nestjs';
import { BullAdapter } from '@bull-board/api/bullAdapter';
import {
  ChunkExtractionProcessor,
  ChunkLinkingProcessor,
  DatapointGenerationProcessor,
  DatapointReviewProcessor,
} from './queue.service';
import { JobProcessor } from 'src/types/jobs';
import { DocumentModule } from 'src/document/document.module';
import { DatapointDocumentChunkModule } from 'src/datapoint-document-chunk/datapoint-document-chunk.module';
import { UsersModule } from 'src/users/users.module';
import { DatapointRequestModule } from 'src/datapoint/datapoint-request.module';
import { DataRequestModule } from 'src/data-request/data-request.module';
import { QueuesModule } from 'src/queues/queues.module';

@Module({
  imports: [
    // BullBoard for queue monitoring
    BullBoardModule.forFeature({
      name: JobProcessor.ChunkExtraction,
      adapter: BullAdapter,
    }),
    BullBoardModule.forFeature({
      name: JobProcessor.ChunkDpLinking,
      adapter: BullAdapter,
    }),
    BullBoardModule.forFeature({
      name: JobProcessor.DatapointGeneration,
      adapter: BullAdapter,
    }),
    BullBoardModule.forFeature({
      name: JobProcessor.DatapointReview,
      adapter: BullAdapter,
    }),
    QueuesModule,
    DocumentModule,
    UsersModule,
    DatapointDocumentChunkModule,
    DatapointRequestModule,
    DataRequestModule,
  ],
  providers: [
    ChunkExtractionProcessor,
    ChunkLinkingProcessor,
    DatapointGenerationProcessor,
    DatapointReviewProcessor,
  ],
  exports: [],
})
export class ProcessQueueModule {}
