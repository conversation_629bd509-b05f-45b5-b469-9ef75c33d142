import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1720942648773 implements MigrationInterface {
  name = 'SchemaUpdate1720942648773';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "chat_history" ADD "title" character varying NOT NULL DEFAULT ''`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_history" DROP COLUMN "title"`);
  }
}
