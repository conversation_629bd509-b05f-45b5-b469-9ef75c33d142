// supabase/functions/ecovadis-ga-collector

// @ts-ignore
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { simpleSupabaseClient } from "../_shared/authValidator.ts";
import { corsHeaders } from "../_shared/cors.ts";

// Define types for the Dify response structure
type DifyScoreAssessment = {
  score: number;
  level: 'Outstanding' | 'Advanced' | 'Good' | 'Partial' | 'Insufficient';
  scoring_breakdown?: Record<string, string>;
};

type DifyGapAnalysis = {
  gap_title?: string;
  description?: string;
  sample_text?: string;
  affected_documents?: string[];
  recommended_actions?: string[];
  impact_effort?: string;
};

type DifyOutputs = {
  project_id: string;
  question_id: string;
  response: {
    formal_criteria_assessment?: Record<string, Record<string, boolean>>;
    current_score_assessment?: DifyScoreAssessment;
    gap_analysis?: DifyGapAnalysis[];
  }
};

type DifyResponseData = {
  id: string;
  workflow_id: string;
  status: string;
  outputs: DifyOutputs;
  error: string | null;
  elapsed_time: number;
  total_tokens: number;
  total_steps: number;
  created_at: number;
  finished_at: number;
};

type DifyResponse = {
  task_id: string;
  workflow_run_id: string;
  data: DifyResponseData;
};

// Define types for our database models
type ScoreLevel = 'Outstanding' | 'Advanced' | 'Good' | 'Partial' | 'Insufficient';


type ResponseBody = {
  success: boolean;
  scoreId: string | null;
  gapsInserted: string[];
  newStatus: string;
};

type ErrorResponseBody = {
  error: string;
  details?: string;
};

serve(async (req: Request): Promise<Response | undefined> => {
  
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
  
  try {
    // Get the request body
    const difyResponse: DifyOutputs = await req.json();
    
    if (!difyResponse) {
      return new Response(JSON.stringify({
        error: 'Project ID, Question ID and Dify response are required'
      } as ErrorResponseBody), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }

    const projectId = difyResponse.project_id;
    const questionId = difyResponse.question_id;

    const supabaseClient = simpleSupabaseClient;
    
    // 1. Verify that the project and question exist
    const { data: projectQuestion, error: projectQuestionError } = await supabaseClient
      .from('project_ecovadis_question')
      .select('id, questionId')
      .eq('id', questionId)
      .eq('projectId', projectId)
      .single();
    
    if (projectQuestionError) {
      console.error('Error verifying project question:', projectQuestionError);
      return new Response(JSON.stringify({
        error: `Failed to verify project question for project ${projectId} and question ${questionId}`,
        details: projectQuestionError.message
      } as ErrorResponseBody), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }
    
    if (!projectQuestion) {
      return new Response(JSON.stringify({
        error: 'Project question not found'
      } as ErrorResponseBody), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 404
      });
    }
    
    // 2. Extract data from dify response
    let currentScore: {
        questionId: string;
        score: number;
        level: ScoreLevel;
        breakdown: string | null;
        description: string;
        conclusion: string;
    } | null = null;
    let gapAnalysis: Record<string, string>[] = [];
    
    if (difyResponse && 
        difyResponse.response) {

     let outputData = difyResponse.response;
     if(typeof outputData === 'string') {
        outputData = JSON.parse(outputData);
      }

      
      // Extract score information
      if (outputData.current_score_assessment) {
        const scoreData = outputData.current_score_assessment;
        currentScore = {
          questionId: questionId,
          score: scoreData.score || 0,
          level: scoreData.level || 'Insufficient',
          breakdown: scoreData.scoring_breakdown ? JSON.stringify(scoreData.scoring_breakdown) : null,
          description: "",
          conclusion: ""
        };
        
        // Create a description from the scoring breakdown
        if (scoreData.scoring_breakdown) {
          const breakdownPoints = Object.values(scoreData.scoring_breakdown);
          currentScore.description = breakdownPoints.join("\n\n");
        }
      }
      
      // Extract gap analysis
      if (outputData.gap_analysis && 
          Array.isArray(outputData.gap_analysis)) {
            
        gapAnalysis = outputData.gap_analysis.map(gap => {
          return {
            Title: gap.gap_title || "",
            Description: gap.description || "",
            "Sample Text": gap.sample_text || "",
            "Related Document": Array.isArray(gap.affected_documents) ? gap.affected_documents.join(", ") : "",
            "Recommended Actions": Array.isArray(gap.recommended_actions) ? gap.recommended_actions.join("\n") : "",
            "Impact/Effort Evaluation": gap.impact_effort || "Not specified"
          };
        });
      }
    }
    
    // 3. Store the score data
    let scoreId: string | null = null;
    if (currentScore) {
      
      // Check if a score already exists for this question
      const { data: existingScore, error: existingScoreError } = await supabaseClient
        .from('project_ecovadis_question_score')
        .select('id, score, level, breakdown, description, conclusion')
        .eq('questionId', questionId)
        .maybeSingle();
      
      if (existingScoreError) {
        console.error('Error checking existing score:', existingScoreError);
      }
      
      if (existingScore) {
        // If score exists, update it and create history entry
        scoreId = existingScore.id;
        
        // Create history record first
        const historyEntry = {
          scoreId: existingScore.id,
          score: existingScore.score,
          level: existingScore.level,
          description: existingScore.description,
          breakdown: existingScore.breakdown,
          conclusion: existingScore.conclusion,
          version: new Date().getTime() // Using timestamp as version
        };
        
        const { error: historyError } = await supabaseClient
          .from('project_ecovadis_question_score_history')
          .insert(historyEntry);
        
        if (historyError) {
          console.error('Error creating score history:', historyError);
        }
        
        // Update existing score
        const { error: updateScoreError } = await supabaseClient
          .from('project_ecovadis_question_score')
          .update({
            score: currentScore.score,
            level: currentScore.level as ScoreLevel,
            description: currentScore.description,
            breakdown: currentScore.breakdown,
            conclusion: currentScore.conclusion
          })
          .eq('id', existingScore.id);
        
        if (updateScoreError) {
          console.error('Error updating score:', updateScoreError);
        }
      } else {
        // If no score exists, create a new one
        const { data: newScore, error: newScoreError } = await supabaseClient
          .from('project_ecovadis_question_score')
          .insert({
            questionId: questionId,
            score: currentScore.score!,
            level: currentScore.level as ScoreLevel,
            description: currentScore.description,
            breakdown: currentScore.breakdown,
            conclusion: currentScore.conclusion
          })
          .select('id')
          .single();
        
        if (newScoreError) {
          console.error('Error creating new score:', newScoreError);
        } else if (newScore) {
          scoreId = newScore.id;
        }
      }
    }
    
    // 4. Process and store gap analysis data
    const gapsInserted: string[] = [];
    
    // First, check for existing gaps to avoid duplication
    type ExistingGap = { id: string; gaps: Record<string, string> };
    
    const { data: existingGaps, error: existingGapsError } = await supabaseClient
      .from('project_ecovadis_gaps')
      .select('id, gaps')
      .eq('questionId', projectQuestion.questionId)
      .eq('projectId', projectId);

      
    
    if (existingGapsError) {
      console.error('Error checking existing gaps:', existingGapsError);
    }
    
    // Function to check if a gap already exists (based on title)
    const gapExists = (newGap: Record<string, string>, existingGaps: ExistingGap[] | null): boolean => {
      if (!existingGaps || existingGaps.length === 0) return false;
      
      return existingGaps.some(existing => {
        try {
          const existingGapData = existing.gaps;
          return existingGapData && 
                 existingGapData.Title && 
                 newGap.Title && 
                 existingGapData.Title.toLowerCase() === newGap.Title.toLowerCase();
        } catch (e) {
          return false;
        }
      });
    };
    
    // Process each gap
    for (const gap of gapAnalysis) {
      // Skip if gap already exists
      if (gapExists(gap, existingGaps)) continue;
      
      // Extract document references from the "Related Document" field
      const documentRefs = gap["Related Document"] ? 
        gap["Related Document"].split(',').map(doc => {
            // validate if proper uuid
            if (!doc.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)) {
              console.error('Invalid document ID:', doc);
              return null;
            }
            return doc.trim()

        })
        .filter(doc => doc !== null) as string[]
         : [];
      
      // Insert the new gap
      const gapEntry= {
        questionId: projectQuestion.questionId,
        projectId: projectId,
        gaps: gap,
        documents: documentRefs,
        resolved: false,
        assigneeId: null,
        deadline: null
      };
      
      const { data: newGap, error: newGapError } = await supabaseClient
        .from('project_ecovadis_gaps')
        .insert(gapEntry)
        .select('id')
        .single();
      
      if (newGapError) {
        console.error('Error creating gap:', newGapError);
      } else if (newGap) {
        gapsInserted.push(newGap.id!);
      }
    }
    
    // 5. Update the question status if needed
    // If the score is > 0, we can set the question to "in_progress"
    // If it's 0, we can set it to "not_started"
    // If score is >= 75, we can set it to "complete"
    let newStatus = "pending";
    if (currentScore) {
      if (currentScore.score! >= 75) { // Using non-null assertion as we've already checked score exists
        newStatus = "complete";
      }
    }
    
    const { error: updateStatusError } = await supabaseClient
      .from('project_ecovadis_question')
      .update({ status: newStatus })
      .eq('id', questionId);
    
    if (updateStatusError) {
      console.error('Error updating question status:', updateStatusError);
    }
    
    // 6. Return success response
    return new Response(JSON.stringify({
      success: true,
      scoreId: scoreId,
      gapsInserted: gapsInserted,
      newStatus: newStatus
    } as ResponseBody), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error)
    } as ErrorResponseBody), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});