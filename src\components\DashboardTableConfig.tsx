
import { ColumnDef } from '@tanstack/react-table';
import { Link } from 'react-router-dom';
import {
  AlertTriangleIcon,
  CheckCircleIcon,
  CircleIcon,
  FileCheckIcon,
  FileTextIcon,
  XCircleIcon,
} from 'lucide-react';

import { DataTableColumnHeader } from './data-table/DataTableColumnHeader';
import { buttonVariants } from './ui/button';
import { StatusLabel } from './ui/status-label';

import { COLUMNS_ID_MAPPING } from '@/constants/table-constants';
import { Checkbox } from '@/components/ui/checkbox';
import { DataRequestStatus } from '@/types';

// ESRS standards (you can add more as needed)
export const esrsStandards = [
  { value: 'ESRS 2', label: 'ESRS 2' },
  { value: 'E1', label: 'E1' },
  { value: 'E2', label: 'E2' },
  { value: 'E3', label: 'E3' },
  { value: 'E4', label: 'E4' },
  { value: 'E5', label: 'E5' },
  { value: 'S1', label: 'S1' },
  { value: 'S2', label: 'S2' },
  { value: 'S3', label: 'S3' },
  { value: 'S4', label: 'S4' },
  { value: 'G1', label: 'G1' },
];

export const dataRequestStatuses: {
  value: DataRequestStatus;
  label: string;
  icon: React.FC<any>;
  color: string;
}[] = [
  {
    value: DataRequestStatus.NoData,
    label: 'No Data',
    icon: XCircleIcon,
    color: '#E53E3E', // Darker red for "No Data"
  },
  {
    value: DataRequestStatus.NotAnswered,
    label: 'Not Reported',
    icon: CircleIcon,
    color: '#718096', // Darker gray for "Not Reported"
  },
  {
    value: DataRequestStatus.IncompleteData,
    label: 'Incomplete Data',
    icon: AlertTriangleIcon,
    color: '#D69E2E', // Darker yellow for "Incomplete Data"
  },
  {
    value: DataRequestStatus.CompleteData,
    label: 'Ready for Draft',
    icon: CheckCircleIcon,
    color: '#3182CE', // Darker blue for "Complete Data"
  },
  {
    value: DataRequestStatus.Draft,
    label: 'Draft',
    icon: FileTextIcon,
    color: '#DD6B20', // Darker orange for "Draft"
  },
  {
    value: DataRequestStatus.ApprovedAnswer,
    label: 'Approved Answer',
    icon: FileCheckIcon,
    color: '#38A169', // Darker green for "Approved Answer"
  },
];

// Subtopics from the provided list (you can expand this list)
export const subtopics = [
  { value: 'climateChangeMitigation', label: 'Climate Change Mitigation' },
  { value: 'climateChangeAdaptation', label: 'Climate Change Adaptation' },
  // Add other subtopics as necessary
];

// Define the columns for the dashboard table
export const dashboardColumns: ColumnDef<any>[] = [
  // Optional selection column
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() ? "indeterminate" : false)
        }
        onCheckedChange={(value: any) =>
          table.toggleAllPageRowsSelected(!!value)
        }
        aria-label="Select all"
        className="translate-y-[2px] scale-110"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value: any) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px] scale-110"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'drId',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title={COLUMNS_ID_MAPPING['drId']}
      />
    ),
    cell: ({ row }) => (
      <div>
        <Link
          className={buttonVariants({
            variant: 'link',
            className: '!p-0',
          })}
          to={`${row.original.id}`}
        >
          {row.getValue('drId')}
        </Link>
      </div>
    ),
    sortingFn: 'alphanumeric',
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title={COLUMNS_ID_MAPPING['name']}
      />
    ),
    cell: ({ row }) => (
      <div>
        <Link
          className={buttonVariants({
            variant: 'link',
            className: '!p-0 text-wrap font-normal',
          })}
          to={`${row.original.id}`}
        >
          {row.getValue('name')}
        </Link>
      </div>
    ),
    sortingFn: 'alphanumeric',
  },
  {
    accessorKey: 'esrs',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title={COLUMNS_ID_MAPPING['esrs']}
      />
    ),
    cell: ({ row }) => (
      <div>
        <span>{row.getValue('esrs')}</span>
      </div>
    ),
    filterFn: (row, id, value) => value.includes(row.getValue(id)),
  },
  {
    accessorKey: 'dueDate',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title={COLUMNS_ID_MAPPING['dueDate']}
      />
    ),
    cell: ({ row }) => {
      const dueDate = row.getValue('dueDate')
        ? new Date(row.getValue('dueDate'))
        : null;
      const isOverdue = dueDate && dueDate < new Date();
      return (
        <div>
          <span style={{ color: isOverdue ? 'red' : 'inherit' }}>
            {dueDate
              ? dueDate.toLocaleDateString('de-DE', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                })
              : '-'}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'responsiblePerson',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title={COLUMNS_ID_MAPPING['responsiblePerson']}
      />
    ),
    cell: ({ row }) => (
      <div>
        {row.getValue('responsiblePerson') ? (
          <span>{row.getValue('responsiblePerson')}</span>
        ) : (
          <span className="text-gray-400">Unassigned</span>
        )}
      </div>
    ),
  },

  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title={COLUMNS_ID_MAPPING['status']}
      />
    ),
    cell: ({ row }) => {
      return (
        <StatusLabel
          status={row.getValue('status')}
          statuses={dataRequestStatuses}
        />
      );
    },
    filterFn: (row, id, value) => value.includes(row.getValue(id)),
  },
  {
    id: 'edit',
    cell: ({ row }) => (
      <Link
        className={buttonVariants({
          variant: 'outline',
          size: 'xs',
          className: '!rounded-full px-4',
        })}
        to={`${row.original.id}`}
      >
        {/* <HammerIcon className={cn('h-3 w-3', 'mr-1')} /> */}
        Edit
      </Link>
    ),
  },
];
