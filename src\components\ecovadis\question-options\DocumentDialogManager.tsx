
import { Fragment, useMemo } from "react";
import { QuestionOption, AttachedDocument } from "@/types/ecovadis";
import { DocumentDialog } from "@/components/ecovadis/documents/DocumentDialog";
import { EvidenceExamplesDialog } from "@/components/ecovadis/documents/EvidenceExamplesDialog";
import ConfirmDialog from "@/components/ConfirmDialog";
import { UploadDocumentDialog } from "@/components/ecovadis/documents/UploadDocumentDialog";
import { convertToRanges } from "@/lib/utils";

interface DialogState {
  openDialogId: string | null;
  uploadDialogId: string | null;
  editDocId: string | null;
  searchTerm: string;
  filteredDocuments: { id: string; name: string; }[];
  evidenceDialogOpen: boolean;
  selectedEvidence: string | null;
  isDetachDialogOpen: boolean;
  selectedDetachDoc: { optionId: string; docId: string } | null;
}

interface DocumentDialogManagerProps {
  dialogState: DialogState;
  options: QuestionOption[];
  onClose: () => void;
  onAddDocument: () => void;
  onConfirmDetach: () => void;
  onSearchChange: (term: string) => void;
}

export const DocumentDialogManager = ({
  dialogState,
  options,
  onClose,
  onAddDocument,
  onConfirmDetach,
  onSearchChange
}: DocumentDialogManagerProps) => {
  const {
    openDialogId,
    uploadDialogId,
    editDocId,
    searchTerm,
    filteredDocuments,
    evidenceDialogOpen,
    selectedEvidence,
    isDetachDialogOpen
  } = dialogState;

  // Enhanced function to find and consolidate document chunks for editing
  const findEditDocument = () => {
    if (!editDocId) return undefined;
    
    // Look through all options to find all chunks of the document
    let consolidatedDoc: (AttachedDocument & { optionId?: string }) | undefined;
    const allPages: number[] = [];
    let firstComment = '';
    
    for (const option of options) {
      if (option.attachedDocuments && option.id === openDialogId) {
        const chunks = option.attachedDocuments.filter(d => d.id === editDocId);
        
        if (chunks.length > 0) {
          // Use the first chunk as the base document
          if (!consolidatedDoc) {
            consolidatedDoc = { ...chunks[0], optionId: option.id };
            firstComment = chunks[0].comment || '';
          }
          
          // Collect all page numbers from all chunks
          chunks.forEach(chunk => {
            if (chunk.pages) {
              const pageNum = parseInt(chunk.pages, 10);
              if (!isNaN(pageNum)) {
                allPages.push(pageNum);
              }
            }
          });
        }
      }
    }
    
    if (consolidatedDoc && allPages.length > 0) {
      // Sort pages and convert to ranges using utility function
      const sortedPages = [...new Set(allPages)].sort((a, b) => a - b);
      const pageRanges = convertToRanges(sortedPages);
      
      // Set consolidated pages and use first chunk's comment
      consolidatedDoc.pages = pageRanges.join(', ');
      consolidatedDoc.comment = firstComment;
    }
    
    return consolidatedDoc;
  };

  const answerId = useMemo(() => {
    if (!uploadDialogId) return null;
    const option = options.find(o => o.id === uploadDialogId);
    return option ? option.answerId : null;
  }, [options, uploadDialogId]);

  const dialogAnswerId = useMemo(() => {
    if (!openDialogId) return null;
    const option = options.find(o => o.id === openDialogId);
    return option ? option.answerId : null;
  }, [options, openDialogId]);

  const editDocument = findEditDocument();
  const editOptionId = editDocument ? editDocument.optionId : openDialogId;

  return (
    <Fragment>
      {/* Document selection dialog */}
      {(typeof openDialogId === 'string' && openDialogId !== '') && (
        <DocumentDialog
          open={true}
          onClose={onClose}
          optionId={editOptionId || ''}
          answerId={dialogAnswerId || ''}
          editDocument={editDocument}
          existingDocuments={filteredDocuments}
          searchTerm={searchTerm}
          onSearchChange={onSearchChange}
        />
      )}

      {uploadDialogId && typeof uploadDialogId === 'string' && uploadDialogId !== '' && (
        <UploadDocumentDialog
          open={!!uploadDialogId}
          onClose={onClose}
          optionId={uploadDialogId}
          answerId={answerId || ''}
          onAddDocument={onAddDocument}
        />
      )}

      {/* Evidence examples dialog */}
      <EvidenceExamplesDialog
        open={evidenceDialogOpen}
        onOpenChange={(isOpen) => {
          if (!isOpen) onClose();
        }}
        evidence={selectedEvidence}
      />

      {/* Confirm detach document dialog */}
      <ConfirmDialog
        open={isDetachDialogOpen}
        onClose={onClose}
        title="Are you sure you want to detach this document?"
        description="Detaching will remove this document from the answer but will not delete it from your uploaded documents."
        onConfirm={onConfirmDetach}
      />
    </Fragment>
  );
};
