import { useState } from "react";
import { AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Upload, File, Eye } from "lucide-react";
import { QuestionOption, AttachedDocument } from "@/types/ecovadis";
import { DocumentsList } from "@/components/ecovadis/documents/DocumentsList";
import { toast } from "sonner";

interface HybridOptionItemProps {
  option: QuestionOption;
  expanded: boolean;
  onToggleExpand: (optionId: string) => void;
  onToggleOption: (optionId: string) => Promise<void>;
  onUpdateResponse: (optionId: string, response: string) => Promise<void>;
  onOpenDocumentDialog: (optionId: string, docId: string | null) => void;
  onOpenUploadDialog: (optionId: string) => void;
  onOpenEvidenceDialog: (evidence: string) => void;
  onDetachDocument: (optionId: string, docId: string) => void;
  onRemoveDocument: (optionId: string, docId: string, chunkIds?: string[]) => void;
  isLoading?: boolean;
  detachingDocIds?: string[];
  updatingDocIds?: string[];
}

export const HybridOptionItem = ({
  option,
  expanded,
  onToggleExpand,
  onToggleOption,
  onUpdateResponse,
  onOpenDocumentDialog,
  onOpenUploadDialog,
  onOpenEvidenceDialog,
  onDetachDocument,
  onRemoveDocument,
  isLoading = false,
  detachingDocIds = [],
  updatingDocIds = []
}: HybridOptionItemProps) => {
  const [response, setResponse] = useState(option.response || "");
  const [isSaving, setIsSaving] = useState(false);

  const handleSaveResponse = async () => {
    setIsSaving(true);
    try {
      await onUpdateResponse(option.id, response);
      toast.success("Response saved successfully");
    } catch (error) {
      console.error("Error saving response:", error);
      toast.error("Failed to save response");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <AccordionItem 
      value={option.id} 
      className="rounded-md p-4 space-y-2 bg-gray-50/50 mb-4 border-none"
    >
      <div className="flex items-start gap-3">
        <div className="relative mt-1">
          <Checkbox 
            id={`option-${option.id}`} 
            checked={option.selected} 
            onCheckedChange={() => onToggleOption(option.id)} 
            disabled={isLoading}
          />
          {isLoading && (
            <Loader2 className="h-4 w-4 animate-spin absolute top-0 left-0 text-primary" />
          )}
        </div>
        
        <div className="space-y-2 flex-1">
          <div className="flex items-center justify-between">
            <AccordionTrigger 
              onClick={(e) => {
                e.preventDefault();
                onToggleExpand(option.id);
              }}
              className="p-0 hover:no-underline"
            >
              <label 
                htmlFor={`option-${option.id}`} 
                className="font-medium text-glacier-darkBlue cursor-pointer text-left"
              >
                {option.text}
              </label>
            </AccordionTrigger>
          </div>
          
          <AccordionContent>
            {option.evidenceExamples && option.evidenceExamples !== "N/A" && (
              <div className="flex items-center justify-between mb-3">
                <div className="text-sm text-glacier-darkBlue">
                  Examples of documents to attach
                  <button 
                    className="text-sm text-primary underline ml-2" 
                    onClick={() => onOpenEvidenceDialog(option.evidenceExamples)}
                  >
                    Show more
                  </button>
                </div>
              </div>
            )}

            {option.selected && (
              <>
                <div className="space-y-3 mb-3">
                  <label className="text-sm font-medium text-glacier-darkBlue">
                    Additional Response (Optional):
                  </label>
                  <Textarea
                    value={response}
                    onChange={(e) => setResponse(e.target.value)}
                    placeholder="Provide additional context or details..."
                    className="min-h-[80px]"
                    disabled={isLoading || isSaving}
                  />
                  
                  <div className="flex justify-end">
                    <Button
                      onClick={handleSaveResponse}
                      disabled={isLoading || isSaving}
                      variant="outline"
                      size="sm"
                    >
                      {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Save Response
                    </Button>
                  </div>
                </div>

                <div className="mb-3">
                  <h4 className="text-sm font-semibold text-glacier-darkBlue mb-2">
                    Add new document:
                  </h4>
                  <div className="flex items-start gap-3">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex items-center gap-2 text-xs rounded-full" 
                      onClick={() => onOpenDocumentDialog(option.id, null)}
                    >
                      <File className="h-3.5 w-3.5" />
                      Attach from existing Documents
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex items-center gap-2 text-xs rounded-full" 
                      onClick={() => onOpenUploadDialog(option.id)}
                    >
                      <Upload className="h-3.5 w-3.5" />
                      Upload New Document
                    </Button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-semibold text-glacier-darkBlue">
                    Attached evidence documents:
                  </h4>
                </div>
                
                <DocumentsList 
                  documents={option.attachedDocuments || []} 
                  onEdit={docId => onOpenDocumentDialog(option.id, docId)} 
                  onRemove={(docId, chunkIds) => onRemoveDocument(option.id, docId, chunkIds)}
                  detachingDocIds={detachingDocIds}
                  updatingDocIds={updatingDocIds}
                />
              </>
            )}
          </AccordionContent>
        </div>
      </div>
    </AccordionItem>
  );
};