
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the Auth context
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Get the authorization header from the request
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Missing Authorization header');
    }

    // Get the session token
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) {
      throw new Error('Error getting session');
    }

    // Get the project update data from the request body
    const { projectId, updates } = await req.json();

    if (!projectId || !updates) {
      throw new Error('Missing required fields: projectId or updates');
    }

    console.log(`Received update request for project ${projectId}`, updates);

    // Update the project in the database
    const { data: updatedProject, error: updateError } = await supabase
      .from('project')
      .update(updates)
      .eq('id', projectId)
      .select()
      .single();

    if (updateError) {
      throw new Error(`Error updating project: ${updateError.message}`);
    }

    console.log(`Successfully updated project ${projectId}`);

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Project updated successfully', 
        project: updatedProject 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  } catch (error) {
    console.error('Error in update-project function:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
