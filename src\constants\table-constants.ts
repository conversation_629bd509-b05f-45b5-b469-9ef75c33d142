import { ColumnSort } from '@tanstack/react-table';

export enum TABLE_TYPES {
  DASHBOARD = 'DASHBOARD',
  DOCUMENT = 'DOCUMENT',
}

export const DEFAULT_TABLE_STATE = {
  pagination: { pageIndex: 0, pageSize: 10 },
  sorting: [] as ColumnSort[],
};

export const COLUMNS_ID_MAPPING = {
  drId: 'DR',
  esrs: 'ESRS',
  name: 'Name',
  dueDate: 'Approved Until',
  status: 'Status',
  responsiblePerson: 'Person Responsible',
  publishedOn: 'Publishing Date',
  creator: 'Uploaded By',
  createdAt: 'Uploaded At',
  esrsCategory: 'Related Areas',
};
