{"version": 3, "file": "ecovadis-issue-parser.service.js", "sourceRoot": "", "sources": ["../../src/document/ecovadis-issue-parser.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,yBAAuB;AACvB,kEAA4E;AAC5E,8DAA0D;AAC1D,6CAAmD;AACnD,qCAAqD;AACrD,0EAQ+C;AAC/C,4CAA2C;AAQpC,IAAM,0BAA0B,kCAAhC,MAAM,0BAA0B;IAErC,YACmB,cAA8B,EAE/C,qCAA+F,EAE/F,uBAAmE,EAEnE,8BAAiF,EAEjF,0BAAyE,EAEzE,8BAAiF,EAEjF,qCAAyF,EACxE,UAAsB;QAbtB,mBAAc,GAAd,cAAc,CAAgB;QAE9B,0CAAqC,GAArC,qCAAqC,CAAyC;QAE9E,4BAAuB,GAAvB,uBAAuB,CAA2B;QAElD,mCAA8B,GAA9B,8BAA8B,CAAkC;QAEhE,+BAA0B,GAA1B,0BAA0B,CAA8B;QAExD,mCAA8B,GAA9B,8BAA8B,CAAkC;QAEhE,0CAAqC,GAArC,qCAAqC,CAAmC;QACxE,eAAU,GAAV,UAAU,CAAY;QAfxB,WAAM,GAAG,IAAI,eAAM,CAAC,4BAA0B,CAAC,IAAI,CAAC,CAAC;IAgBnE,CAAC;IAEJ,KAAK,CAAC,mCAAmC,CACvC,QAAgB,EAChB,SAAiB;QAEjB,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,2DAA2D,SAAS,EAAE,CACvE,CAAC;QAEF,MAAM,aAAa,GAAG,gCAAgC,CAAC;QACvD,MAAM,mBAAmB,GAAG,MAAM,IAAA,mDAA8B,EAAC;YAC/D,QAAQ;YACR,WAAW,EAAE,IAAI;YACjB,aAAa;SACd,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,8DAA8D,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,CAChG,CAAC;QAGF,MAAM,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAE5D,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;CAyBlB,CAAC;QAEE,MAAM,MAAM,GAAU,EAAE,CAAC;QAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAEtC,IACE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC;gBACpC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,kCAAkC,CAAC,CAAC;gBACxD,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,4BAA4B,CAAC;oBAC9C,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC,EAC5C,CAAC;gBACD,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,yCAAyC,CAAC,GAAG,CAAC,qBAAqB,CACpE,CAAC;gBACF,MAAM;YACR,CAAC;YAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,yBAAyB,CACrE,sBAAU,CAAC,QAAQ,CAAC,EACpB;gBACE;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;iBAC3B;aACF,EACD,IAAI,CACL,CAAC;YAQF,IAAI,CAAC;gBACH,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;gBAC/C,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;oBAC5C,MAAM,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;gBACxC,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,uCAAuC,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,OAAO,EAAE,CACjE,CAAC;YACJ,CAAC;QACH,CAAC;QAGD,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CACrC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YACb,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,IAAI,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC;YACrD,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;YAE1B,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC3B,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;YAC7B,CAAC;YAED,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,OAAO,GAAG,CAAC;QACb,CAAC,EACD,EAAE,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAChC,CAAC,MAAM,CAAC;QAET,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,iDAAiD,CAElD,CAAC;QAGF,KAAK,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACzE,MAAM,WAAW,GAAG,WAMjB,CAAC;YAEJ,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,qBAAqB,SAAS,uBAAuB,WAAW,CAAC,MAAM,EAAE,CAC1E,CAAC;YAEF,IAAI,KAAK,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC;gBACrD,KAAK,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE;aAC5B,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;oBAC1C,KAAK,EAAE,SAAS;oBAChB,WAAW,EAAE,EAAE;iBAChB,CAAC,CAAC;gBACH,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjD,CAAC;YAGD,MAAM,YAAY,GAGZ,EAAE,CAAC;YAET,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE,CAAC;gBAEhC,IAAI,aAAa,GACf,MAAM,IAAI,CAAC,qCAAqC,CAAC,OAAO,CAAC;oBACvD,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE;iBAC9B,CAAC,CAAC;gBAEL,IAAI,CAAC,aAAa,EAAE,CAAC;oBACnB,aAAa,GAAG,IAAI,CAAC,qCAAqC,CAAC,MAAM,CAAC;wBAChE,KAAK,EAAE,KAAK,CAAC,KAAK;wBAClB,UAAU,EAAE,KAAK,CAAC,UAAU;wBAC5B,cAAc,EAAE,KAAK,CAAC,eAAe,IAAI,EAAE;qBAC5C,CAAC,CAAC;oBACH,MAAM,IAAI,CAAC,qCAAqC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACvE,CAAC;gBAED,YAAY,CAAC,IAAI,CAAC;oBAChB,OAAO,EAAE,aAAa,CAAC,EAAE;oBACzB,MAAM,EAAE,KAAK,CAAC,MAAqB;iBACpC,CAAC,CAAC;YACL,CAAC;YAGD,IAAI,YAAY,GAAG,MAAM,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC;gBACnE,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,EAAE;aACxC,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAElB,YAAY,GAAG,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC;oBACxD,SAAS;oBACT,OAAO,EAAE,KAAK,CAAC,EAAE;oBACjB,MAAM,EAAE,6BAAW,CAAC,MAAM;oBAC1B,MAAM,EAAE,YAAY;iBACrB,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,YAAY,CAAC,MAAM,GAAG,YAAY,CAAC;YACrC,CAAC;YAED,MAAM,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,0BAA0B,CAAC,SAAkB,IAAI;QAMrD,MAAM,OAAO,GAAG;YACd,cAAc,EAAE,CAAC;YACjB,kBAAkB,EAAE,CAAC;YACrB,YAAY,EAAE,CAAC;YACf,MAAM,EAAE,EAAE;SACX,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,mDAAmD,MAAM,GAAG,CAC7D,CAAC;QAEF,IAAI,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC;gBAC3D,SAAS,EAAE,CAAC,eAAe,CAAC;gBAC5B,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;aAC5B,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,SAAS,CAAC,MAAM,uBAAuB,CAAC,CAAC;YAGlE,MAAM,SAAS,GAAG,CAAC,CAAC;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;gBACrD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;gBAEhD,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE,CAAC;oBAC7B,IAAI,CAAC;wBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;wBACnE,OAAO,CAAC,cAAc,EAAE,CAAC;wBACzB,OAAO,CAAC,kBAAkB,IAAI,MAAM,CAAC,kBAAkB,CAAC;wBACxD,OAAO,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,CAAC;oBAC9C,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,MAAM,QAAQ,GAAG,6BAA6B,QAAQ,CAAC,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;wBAC9E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;wBAC5B,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAChC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,mCAAmC,KAAK,CAAC,OAAO,EAAE,CAAC;YACpE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC5B,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC;QACxD,OAAO,OAAO,CAAC;IACjB,CAAC;IAKO,KAAK,CAAC,sBAAsB,CAClC,QAA0B,EAC1B,MAAe;QAEf,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjE,OAAO,EAAE,kBAAkB,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,uBAAuB,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC,aAAa,CAAC,MAAM,UAAU,CAC7F,CAAC;QAGF,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CACrD,QAAQ,CAAC,aAAa,CACvB,CAAC;QAEF,IAAI,cAAc,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChD,OAAO,EAAE,kBAAkB,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,SAAS,cAAc,CAAC,eAAe,CAAC,MAAM,gCAAgC,QAAQ,CAAC,YAAY,EAAE,CACtG,CAAC;QAGF,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;QAChE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,0BAA0B,cAAc,CAAC,eAAe,CAAC,MAAM,iBAAiB,CACjF,CAAC;QACJ,CAAC;QAED,OAAO;YACL,kBAAkB,EAAE,cAAc,CAAC,eAAe,CAAC,MAAM;YACzD,YAAY;SACb,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,qBAAqB,CACjC,OAA+B;QAE/B,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YAC3C,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,UAAU,EAAE,MAAM,CAAC,UAAU;SAC9B,CAAC,CAAC,CAAC;QAEJ,MAAM,MAAM,GAAG;;;;EAIjB,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;;;;;;;;;;;CAWrC,CAAC;QAEE,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,yBAAyB,CACrE,sBAAU,CAAC,SAAS,CAAC,EACrB;gBACE;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM;iBAChB;aACF,EACD,IAAI,CACL,CAAC;YAEF,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,SAAS,GAAG,cAAc,CAAC,eAAe,IAAI,EAAE,CAAC;YAEvD,OAAO;gBACL,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,IAAI,EAAE;gBACxC,eAAe,EAAE,SAAS;aAC3B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACnE,OAAO;gBACL,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,IAAI,EAAE;gBACxC,eAAe,EAAE,EAAE;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,mBAAmB,CAC/B,cAA0C;QAE1C,IAAI,CAAC;YAEH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CACxD,uCAAqB,EACrB;gBACE,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAA,YAAE,EAAC,cAAc,CAAC,eAAe,CAAC,EAAE;aACxD,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,SAAS,eAAe,CAAC,MAAM,6CAA6C,CAC7E,CAAC;YAEF,MAAM,0BAA0B,GAC9B,MAAM,IAAI,CAAC,qCAAqC,CAAC,MAAM,CAAC;gBACtD,QAAQ,EAAE,IAAA,YAAE,EAAC,cAAc,CAAC,eAAe,CAAC;aAC7C,CAAC,CAAC;YAEL,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,WAAW,0BAA0B,CAAC,QAAQ,2CAA2C,CAC1F,CAAC;YAGF,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC;gBACpE,EAAE,EAAE,IAAA,YAAE,EAAC,cAAc,CAAC,eAAe,CAAC;aACvC,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,WAAW,YAAY,CAAC,QAAQ,6BAA6B,cAAc,CAAC,eAAe,CAAC,IAAI,CAC9F,IAAI,CACL,EAAE,CACJ,CAAC;YAEF,OAAO,YAAY,CAAC,QAAQ,IAAI,CAAC,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACrE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AA1ZY,gEAA0B;qCAA1B,0BAA0B;IADtC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,6CAA2B,CAAC,CAAA;IAE7C,WAAA,IAAA,0BAAgB,EAAC,+BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,sCAAoB,CAAC,CAAA;IAEtC,WAAA,IAAA,0BAAgB,EAAC,kCAAgB,CAAC,CAAA;IAElC,WAAA,IAAA,0BAAgB,EAAC,sCAAoB,CAAC,CAAA;IAEtC,WAAA,IAAA,0BAAgB,EAAC,uCAAqB,CAAC,CAAA;qCAXP,iCAAc;QAES,oBAAU;QAExB,oBAAU;QAEH,oBAAU;QAEd,oBAAU;QAEN,oBAAU;QAEH,oBAAU;QACrC,oBAAU;GAhB9B,0BAA0B,CA0ZtC"}