import { CustomGptTool } from '../util/chat-gpt.models';
import { ChatMessageDto } from './entities/chat.message.dto';
import { UsersService } from '../users/users.service';
import { ChatGptService } from '../llm/chat-gpt.service';
import { MultiQuestionSearchEngine } from '../util/multi-question-search-engine.service';
import { SearchEngineTool } from '../util/search-engine.tool';
import { LLM_MODELS } from 'src/constants';
export declare class InitiativeDetailService {
    private readonly usersService;
    private readonly chatGptService;
    private readonly multiQuestionSearchEngine;
    private readonly searchEngine;
    readonly gptModel = LLM_MODELS['gpt-4o'];
    constructor(usersService: UsersService, chatGptService: ChatGptService, multiQuestionSearchEngine: MultiQuestionSearchEngine, searchEngine: SearchEngineTool);
    createInitiativeDetailTool(userId: string): CustomGptTool<void, string>;
    createDetailledInitiative(previousMessages: ChatMessageDto[], userId: string): AsyncIterableIterator<string>;
    writeFullSummary(initiativeDescription: string, context: string, previousMessages: ChatMessageDto[], initiativeShortSummary: string, relevantNumbersSummary: string, executionPlan: string, grantList: string, partnersList: string): Promise<AsyncIterableIterator<string>>;
    generateShortSummary(initiativeDescription: string, context: string, previousMessages: ChatMessageDto[]): Promise<string>;
    generateSummaryRelevantNumbers(initiativeDescription: string, context: string, previousMessages: ChatMessageDto[]): Promise<string>;
    generateExecutionPlan(initiativeDescription: string, context: string, previousMessages: ChatMessageDto[]): Promise<string>;
    generatePartnerList(initiativeDescription: string, context: string, previousMessages: ChatMessageDto[]): Promise<string>;
    generateGrantList(initiativeDescription: string, context: string, previousMessages: ChatMessageDto[]): Promise<string>;
    extractInitiativeInformation(previousMessages: ChatMessageDto[]): Promise<string>;
}
