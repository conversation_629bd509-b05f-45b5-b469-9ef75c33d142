import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1731330238412 implements MigrationInterface {
  name = 'SchemaUpdate1731330238412';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_document_chunk" ADD "key_information" text`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_document_chunk" DROP COLUMN "key_information"`,
    );
  }
}
