
import { requestEcovadisQuestionGapAnalysis } from '@/api/ecovadis/ecovadis.api';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';
import { useParams } from 'react-router-dom';

export const useQuestionGapAnalysis = (projectQuestionId?: string, cb?: () => void) => {
  const { id: projectId } = useParams<{ id: string }>();
  const queryClient = useQueryClient();

  // Use React Query's useMutation for gap analysis
  const gapAnalysisMutation = useMutation({
    mutationFn: () => {
      if (!projectQuestionId) throw new Error('Question ID is required');
      // Call the API
      return requestEcovadisQuestionGapAnalysis(projectQuestionId, projectId);
    },
    onSuccess: (data) => {
      cb();
      queryClient.invalidateQueries({ queryKey: ['ecovadisProject', projectId] });
      // Show success toast notification
      toast({
        title: "Gap Analysis Complete",
        description: "Successfully analyzed and identified gaps in documentation with updated score.",
        variant: "success",
      });
    },
    onError: (error) => {
      console.error("Gap analysis failed:", error);
      
      // Show error toast notification
      toast({
        title: "Gap Analysis Failed",
        description: "There was an error analyzing the documentation gaps. Please try again.",
        variant: "destructive",
      });
    }
  });

  const handleGapAnalysis = () => {
    gapAnalysisMutation.mutate();
  };

  return {
    handleGapAnalysis,
    isLoading: gapAnalysisMutation.isPending
  };
};
