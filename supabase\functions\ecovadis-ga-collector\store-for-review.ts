// @ts-expect-error Import for Supabase client types
import type { SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2'

export interface StoreGeneratedGapResult {
  success: boolean;
  gapIds?: string[];
}

interface GapAnalysisItem {
  gap: string;
  gap_type: string;
  description: string;
  sample_text: string;
  pillar_category: string;
  affected_documents: string[];
  recommended_actions: string[];
}

/**
 * Stores generated gaps for Super Admin review
 * Each gap in the analysis array will be stored as a separate row
 */
export async function storeGeneratedGapForReview({
  supabaseClient,
  projectId,
  questionId,
  gapAnalysis,
}: {
  supabaseClient: SupabaseClient,
  projectId: string,
  questionId: string,
  gapAnalysis: GapAnalysisItem[],
}): Promise<StoreGeneratedGapResult> {
  try {
    if (!gapAnalysis || gapAnalysis.length === 0) {
      return { success: true, gapIds: [] };
    }

    // Prepare data for bulk insert - each gap as a separate row
    const gapInserts = gapAnalysis.map(gap => ({
      projectId: projectId,
      questionId: questionId,
      generatedContent: gap, // Store individual gap item
      documents: gap.affected_documents || [],
      status: 'pending_review'
    }));

    const { data: generatedGaps, error: insertError } = await supabaseClient
      .from('project_ecovadis_gap_generation')
      .insert(gapInserts)
      .select('id');

    if (insertError) {
      console.error('Error storing generated gaps for review:', insertError);
      return { success: false };
    }

    return { 
      success: true, 
      gapIds: generatedGaps?.map(gap => gap.id) || []
    };
  } catch (error) {
    console.error('Error in storeGeneratedGapForReview:', error);
    return { success: false };
  }
}
