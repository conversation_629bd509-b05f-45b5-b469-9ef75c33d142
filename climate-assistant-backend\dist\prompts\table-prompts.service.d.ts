import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { Language } from 'src/project/entities/project.entity';
export declare class TablePromptService {
    private readonly GENERATION_LANGUAGE;
    private readonly turndownService;
    private languageCodeToLanguage;
    constructor();
    generateTableDatapointContentGenerationSystemPrompt1(): string;
    generateDatapointContentGenerationExample(exampleOutput: string): string;
    generateDatapointContentGenerationSpecificDPSystemPrompt(esrsDatapoints: ESRSDatapoint[], otherDatapoints: ESRSDatapoint[]): string;
    createFullEsrsDatapointLawTextContext(esrsDatapoint: ESRSDatapoint): string;
    generateDatapointContentGenerationSystemPrompt2({ esrsDatapoint, generationLanguage, reportTextGenerationRules, customUserRemark, }: {
        esrsDatapoint: ESRSDatapoint;
        generationLanguage: Language;
        reportTextGenerationRules: string;
        customUserRemark: string;
    }): string;
    generateTableSystemPrompt({ esrsDatapoints, generationLanguage, reportTextGenerationRules, customUserRemark, currentContent, linkedChunks, otherDatapoints, reportingYear, generalCompanyProfile, }: {
        esrsDatapoints: ESRSDatapoint[];
        generationLanguage: Language;
        reportTextGenerationRules: string;
        customUserRemark: string;
        currentContent: string;
        linkedChunks: string;
        otherDatapoints: ESRSDatapoint[];
        reportingYear: string;
        generalCompanyProfile: string;
    }): string;
}
