import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Workspace } from './entities/workspace.entity';
import { Role, UserWorkspace } from '../users/entities/user-workspace.entity';
import { User } from '../users/entities/user.entity';
import { Token, TokenType } from '../users/entities/token.entity';
import { EmailService } from '../external/email.service';
import {
  IVersionHistory,
  VersionHistory,
} from './entities/version-history.entity';
import { Company } from './entities/company.entity';

@Injectable()
export class WorkspaceService {
  constructor(
    @InjectRepository(Workspace)
    private readonly workspaceRepository: Repository<Workspace>,
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
    @InjectRepository(UserWorkspace)
    private readonly userWorkspaceRepository: Repository<UserWorkspace>,
    @InjectRepository(Token)
    private tokenRepository: Repository<Token>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(VersionHistory)
    private readonly versionHistoryRepository: Repository<VersionHistory>,
    private emailService: EmailService
  ) {}

  async findById(id: Workspace['id']): Promise<Workspace | undefined> {
    return this.workspaceRepository.findOne({
      where: { id },
      relations: ['companies'],
    });
  }

  async updateById(id: string, updates: Partial<Workspace>) {
    const workspace = await this.workspaceRepository.findOneBy({ id });

    if (!workspace) {
      throw new Error('Workspace not found');
    }

    // Update the workspace fields
    Object.assign(workspace, updates);

    // Save the updated workspace
    return this.workspaceRepository.save(workspace);
  }

  async getUsersByWorkspace(workspaceId: string): Promise<User[]> {
    return this.userWorkspaceRepository
      .find({
        where: { workspaceId },
        relations: ['user', 'user.tokens'],
      })
      .then((userWorkspaces) =>
        userWorkspaces.map((uw) => {
          let status = 'INACTIVE';
          if (!!this.isUserActive(uw.user)) {
            status = 'ACTIVE';
          } else {
            const token = uw.user.tokens?.at(-1); //get the last token
            if (token && token.type === TokenType.WorkspaceInvite) {
              status = 'INVITED';
            }
          }
          delete uw.user.password;
          delete uw.user.tokens;
          return {
            ...uw.user,
            role: uw.role,
            status,
          };
        })
      );
  }

  async addUserToWorkspace({
    workspaceId,
    userId,
    role,
  }: {
    workspaceId: string;
    userId: string;
    role: Role;
  }) {
    const userWorkspace = this.userWorkspaceRepository.create({
      workspaceId,
      userId,
      role,
      joinedAt: new Date(),
    });
    return this.userWorkspaceRepository.save(userWorkspace);
  }

  async getUserWorkspace({
    workspaceId,
    userId,
  }: {
    workspaceId: string;
    userId: string;
  }): Promise<UserWorkspace | undefined> {
    return this.userWorkspaceRepository.findOne({
      where: { workspaceId, userId },
    });
  }

  async removeUserFromWorkspace({
    workspaceId,
    userId,
  }: {
    workspaceId: string;
    userId: string;
  }): Promise<void> {
    await this.userWorkspaceRepository.delete({ workspaceId, userId });
  }

  async inviteUserToWorkspace({
    inviteeEmail,
    origin,
    workspaceId,
    email,
    role,
    shouldSendEmail = true,
  }: {
    inviteeEmail: string;
    origin: string;
    workspaceId: string;
    email: string;
    role: Role;
    shouldSendEmail: boolean;
  }) {
    let user = await this.userRepository.findOne({
      where: { email },
      relations: ['userWorkspaces'],
    });
    const invitingUser = await this.userRepository.findOne({
      where: { email: inviteeEmail },
    });
    if (!user) {
      user = this.userRepository.create({ email });
      await this.userRepository.save(user);
    }
    if (this.isUserActive(user)) {
      throw new Error(`${email} is already an active user in the workspace`);
    }

    if (!user.userWorkspaces) {
      const userWorkspace = this.userWorkspaceRepository.create({
        workspaceId,
        userId: user.id,
        role,
      });
      await this.userWorkspaceRepository.save(userWorkspace);
    }

    const inviteToken = crypto.randomUUID();

    const store = await this.tokenRepository.create({
      token: inviteToken,
      user,
      type: TokenType.WorkspaceInvite,
      expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 3),
    });

    await this.tokenRepository.save(store);

    if (shouldSendEmail) {
      await this.emailService.inviteUser({
        token: inviteToken,
        invitingUser,
        email,
        origin,
      });
    }

    return user;
  }

  async acceptInvitation({
    workspaceId,
    userId,
  }: {
    workspaceId: string;
    userId: string;
  }) {
    const userWorkspace = await this.getUserWorkspace({ workspaceId, userId });
    if (userWorkspace) {
      const joinedAt = new Date();
      await this.userWorkspaceRepository.update(userWorkspace, { joinedAt });
      return userWorkspace;
    }
    throw new Error('Invitation not found');
  }

  async storeActionHistory({
    workspaceId,
    event,
    ref,
    versionData,
  }: {
    workspaceId: string;
    event: string;
    ref: string;
    versionData: IVersionHistory;
  }) {
    const history = this.versionHistoryRepository.create({
      workspaceId,
      event,
      ref,
      versionData,
    });
    await this.versionHistoryRepository.save(history);
  }

  async canInviteUser(invitedRole: Role, user: User) {
    const userWorkspace = await this.userWorkspaceRepository.findOne({
      where: { userId: user.id },
    });
    const currentUserRole = userWorkspace?.role;
    if (currentUserRole === Role.SuperAdmin) return true;
    if (
      currentUserRole === Role.AiContributor &&
      [Role.WorkspaceAdmin, Role.Contributor].includes(invitedRole)
    ) {
      return true;
    }
    if (
      currentUserRole === Role.WorkspaceAdmin &&
      [Role.WorkspaceAdmin, Role.Contributor].includes(invitedRole)
    ) {
      return true;
    }
    return false;
  }

  isUserActive(user: User) {
    return !!user.password;
  }

  async getAllWorkspaces(): Promise<Workspace[]> {
    return this.workspaceRepository.find({
      // where: {
      //   createdAt: MoreThanOrEqual(new Date('2025-01-01')),
      // },
      order: {
        name: 'ASC',
      },
    });
  }

  async getCompanyDetailFromWorkspaceId(workspaceId: string) {
    return this.companyRepository.findOne({
      where: { workspaceId },
    });
  }

  async updateCompanyDetail(
    workspaceId: string,
    companyDetail: Company
  ): Promise<Company> {
    const company = await this.companyRepository.findOne({
      where: { workspaceId },
    });
    if (!company) {
      throw new Error(`Company not found for workspaceId: ${workspaceId}`);
    }
    return this.companyRepository.save({ id: company.id, ...companyDetail });
  }
}
