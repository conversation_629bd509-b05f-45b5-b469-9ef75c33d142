
import React, { useState } from 'react';
import { EcovadisQuestion } from '@/types/ecovadis';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { MarkCompleteToggle } from '@/components/ecovadis/gaps/MarkCompleteToggle';
import { Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ScoreInfoModal } from '@/components/ecovadis/ScoreInfoModal';
import { QUESTION_STATUS } from '@/constants/projectConstants';

interface QuestionHeaderProps {
  selectedQuestion: EcovadisQuestion;
  handleMarkQuestionComplete: () => void;
}

export const QuestionHeader: React.FC<QuestionHeaderProps> = ({
  selectedQuestion,
  handleMarkQuestionComplete,
}) => {
  const [showScoreInfo, setShowScoreInfo] = useState(false);
  
  const getBadgeVariant = (impact: string) => {
    switch(impact) {
      case 'High':
      case 'high':
        return 'destructive';
      case 'Medium':
      case 'medium':
        return 'default';
      case 'Low':
      case 'low':
        return 'secondary';
      case 'N/A':
      case 'none':
        return 'outline';
      default:
        return 'outline';
    }
  };

  // Check if question status is "complete" - normalize status value case
  const isComplete = selectedQuestion.status?.toLowerCase() === QUESTION_STATUS.COMPLETE;
  
  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row sm:items-end xl:items-center justify-between gap-4">
        <div>
          <div className="items-center gap-2 sm:block xl:flex">
            <h1 className="text-2xl font-semibold text-glacier-darkBlue">
              {selectedQuestion.name}
            </h1>
            <span className='text-sm text-gray-400'>
            {selectedQuestion.questionCode} {selectedQuestion.indicator}
            </span>
            <div className="flex items-center gap-2 text-nowrap">
              <Badge variant={getBadgeVariant(selectedQuestion.impact || '')}>
                {selectedQuestion.impact ? (selectedQuestion.impact !== 'N/A' && selectedQuestion.impact !== 'none' ? 
                  `${selectedQuestion.impact} Impact` : 'No Impact Rating') : 'Loading...'}
              </Badge>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-6 w-6 rounded-full p-0 text-gray-500 hover:bg-gray-100"
                      onClick={() => setShowScoreInfo(true)}
                    >
                      <Info className="h-4 w-4" />
                      <span className="sr-only">Score information</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Click for score information</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            
          </div>
          <div className="mt-1 text-sm text-gray-600">
            Topic: {selectedQuestion.topic}
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <MarkCompleteToggle 
            isComplete={isComplete} 
            onToggleComplete={handleMarkQuestionComplete} 
            className="min-w-[140px]"
          />
        </div>
      </div>
      
      <Separator />
      
      {/* Score Information Modal */}
      <ScoreInfoModal 
        open={showScoreInfo} 
        onOpenChange={setShowScoreInfo}
        question={selectedQuestion}
      />
    </div>
  );
};
