import confetti from 'canvas-confetti';

export const fireConfetti = () => {
  function randomInRange(min: number, max: number): number {
    return Math.random() * (max - min) + min;
  }

  confetti({
    angle: randomInRange(55, 125),
    spread: randomInRange(50, 70),
    particleCount: Math.floor(randomInRange(50, 100)),
    origin: { y: 0.8 },
    colors: ['#143560', '#6DD4AD', '#539ADB', '#2D9D90'],
  });
};
