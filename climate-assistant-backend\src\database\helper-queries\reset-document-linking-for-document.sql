-- Active: 1724827555825@@127.0.0.1@5432@glacier@public

-- Select all linked document-datapoints
select * from datapoint_document_chunk join document_chunk on datapoint_document_chunk."documentChunkId"=document_chunk.id where document_chunk."documentId"='a073eb6f-2845-430e-af7d-eacbabd0ac9b'

-- Update Document ID
update document set "status" = 'data_extraction_finished' where document."id" = 'a073eb6f-2845-430e-af7d-eacbabd0ac9b'

update document_chunk set "matchingsJson" = Null where document_chunk."documentId" = 'a073eb6f-2845-430e-af7d-eacbabd0ac9b'

-- Remove all document-chunk-links, where document ID
delete from datapoint_document_chunk USING document_chunk Where datapoint_document_chunk."documentChunkId"=document_chunk.id AND document_chunk."documentId"='a073eb6f-2845-430e-af7d-eacbabd0ac9b'
