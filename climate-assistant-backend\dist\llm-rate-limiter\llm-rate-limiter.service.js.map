{"version": 3, "file": "llm-rate-limiter.service.js", "sourceRoot": "", "sources": ["../../src/llm-rate-limiter/llm-rate-limiter.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,uCAA+D;AAG/D,8DAAyD;AACzD,4CAA2C;AAC3C,qEAA+D;AAC/D,wCAAwD;AAExD,MAAM,IAAI,GAAG,GAAG,CAAC;AAsBV,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IAmGhC,YACoC,QAAgC,EACjD,cAA8B;QADI,aAAQ,GAAR,QAAQ,CAAO;QACjD,mBAAc,GAAd,cAAc,CAAgB;QApGhC,WAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;QACzD,kBAAa,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3B,eAAU,GAAmC;YACnD,CAAC,sBAAU,CAAC,EAAE,CAAC,EAAE;gBACf,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;aAChB;YACD,CAAC,sBAAU,CAAC,EAAE,CAAC,EAAE;gBACf,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;aAChB;YACD,CAAC,sBAAU,CAAC,QAAQ,CAAC,CAAC,EAAE;gBACtB,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;aAChB;YACD,CAAC,sBAAU,CAAC,SAAS,CAAC,CAAC,EAAE;gBACvB,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;aAChB;YACD,CAAC,sBAAU,CAAC,SAAS,CAAC,CAAC,EAAE;gBACvB,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;aAChB;YACD,CAAC,sBAAU,CAAC,YAAY,CAAC,CAAC,EAAE;gBAC1B,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;aAChB;YACD,CAAC,sBAAU,CAAC,aAAa,CAAC,CAAC,EAAE;gBAC3B,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;aAChB;YACD,CAAC,sBAAU,CAAC,aAAa,CAAC,CAAC,EAAE;gBAC3B,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;aAChB;YAED,CAAC,sBAAU,CAAC,gBAAgB,CAAC,CAAC,EAAE;gBAC9B,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;aAChB;YACD,CAAC,sBAAU,CAAC,kBAAkB,CAAC,CAAC,EAAE;gBAChC,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;aAChB;YACD,CAAC,sBAAU,CAAC,kBAAkB,CAAC,CAAC,EAAE;gBAChC,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;aAChB;SACF,CAAC;QAEe,gBAAW,GAAoC;YAC9D,CAAC,sBAAU,CAAC,EAAE,CAAC,EAAE;gBACf,kBAAkB,EAAE,MAAM;gBAC1B,oBAAoB,EAAE,GAAG;aAC1B;YACD,CAAC,sBAAU,CAAC,QAAQ,CAAC,CAAC,EAAE;gBACtB,kBAAkB,EAAE,MAAM;gBAC1B,oBAAoB,EAAE,GAAG;aAC1B;YACD,CAAC,sBAAU,CAAC,SAAS,CAAC,CAAC,EAAE;gBACvB,kBAAkB,EAAE,OAAO;gBAC3B,oBAAoB,EAAE,GAAG;aAC1B;YACD,CAAC,sBAAU,CAAC,SAAS,CAAC,CAAC,EAAE;gBACvB,kBAAkB,EAAE,OAAO;gBAC3B,oBAAoB,EAAE,GAAG;aAC1B;YACD,CAAC,sBAAU,CAAC,YAAY,CAAC,CAAC,EAAE;gBAC1B,kBAAkB,EAAE,KAAK;gBACzB,oBAAoB,EAAE,GAAG;aAC1B;YACD,CAAC,sBAAU,CAAC,aAAa,CAAC,CAAC,EAAE;gBAC3B,kBAAkB,EAAE,KAAK;gBACzB,oBAAoB,EAAE,GAAG;aAC1B;YACD,CAAC,sBAAU,CAAC,aAAa,CAAC,CAAC,EAAE;gBAC3B,kBAAkB,EAAE,MAAM;gBAC1B,oBAAoB,EAAE,GAAG;aAC1B;YACD,CAAC,sBAAU,CAAC,IAAI,CAAC,CAAC,EAAE;gBAClB,kBAAkB,EAAE,MAAM;gBAC1B,oBAAoB,EAAE,GAAG;aAC1B;YAED,CAAC,sBAAU,CAAC,gBAAgB,CAAC,CAAC,EAAE;gBAC9B,kBAAkB,EAAE,KAAK;gBACzB,oBAAoB,EAAE,EAAE;aACzB;YACD,CAAC,sBAAU,CAAC,kBAAkB,CAAC,CAAC,EAAE;gBAChC,kBAAkB,EAAE,KAAK;gBACzB,oBAAoB,EAAE,EAAE;aACzB;YACD,CAAC,sBAAU,CAAC,kBAAkB,CAAC,CAAC,EAAE;gBAChC,kBAAkB,EAAE,KAAK;gBACzB,oBAAoB,EAAE,EAAE;aACzB;SACF,CAAC;IAKC,CAAC;IAGE,AAAN,KAAK,CAAC,iBAAiB,CAAC,GAAQ;QAC9B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7C,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,MAAc,EAAE,KAAiB;QACzD,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACvC,OAAO,MAAM,GAAG,MAAM,CAAC,kBAAkB,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,OAAO;QACtB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACxD,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAC1D,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAEO,mBAAmB;QACzB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC/B,IAAI,WAAW,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC;YAElD,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,WAAW;QACjB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC7C,IAAI,CAAC,UAAU,CAAC,KAAmB,CAAC,GAAG;gBACrC,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,CAAC;aAChB,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,OAA2B,EAC3B,WAAmB,CAAC;QAEpB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAErE,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAClD,MAAM,IAAI,KAAK,CACb,0CAA0C,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,kBAAkB,cAAc,OAAO,CAAC,KAAK,EAAE,CAC1H,CAAC;QACJ,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;QACvD,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACjC,eAAQ,CAAC,UAAU,EACnB;YACE,OAAO;SACR,EACD;YACE,QAAQ;SACT,CACF,CAAC;QACF,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,QAAQ,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,eAAe,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,MAAc,EAAE,KAAiB;QACtD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACvC,OAAO,CACL,KAAK,CAAC,UAAU,GAAG,MAAM,IAAI,MAAM,CAAC,kBAAkB;YACtD,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC,oBAAoB,CACjD,CAAC;IACJ,CAAC;IAEO,gBAAgB,CAAC,MAAc,EAAE,KAAiB;QACxD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,UAAU,IAAI,MAAM,CAAC;QAC5C,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,SAAS,KAAK,2BAA2B,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,UAAU,IAAI;YAC5E,kBAAkB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAC1D,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,EACxB,MAAM,EACN,KAAK,EACL,QAAQ,EACR,WAAW,EACX,IAAI,GACe;QACnB,MAAM,WAAW,GAA2C;YAC1D,KAAK;YACL,QAAQ,EAAE,QAAoD;YAC9D,IAAI;YACJ,WAAW;YACX,eAAe,EAAE,IAAI;gBACnB,CAAC,CAAC;oBACE,IAAI,EAAE,aAAa;iBACpB;gBACH,CAAC,CAAC,SAAS;SACd,CAAC;QAIF,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,MAAM,WAAW,GAAG,CAAC,CAAC;QAEtB,OAAO,QAAQ,GAAG,WAAW,EAAE,CAAC;YAI9B,IAAI,YAAY,GAAG,KAAK,CAAC;YACzB,IAAI,KAAK,KAAK,sBAAU,CAAC,EAAE,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;gBAC5C,YAAY,GAAG,sBAAU,CAAC,QAAQ,CAAC,CAAC;YACtC,CAAC;YAGD,IAAI,MAAM,CAAC;YACX,IAAI,YAAY,KAAK,sBAAU,CAAC,EAAE,EAAE,CAAC;gBACnC,OAAO,WAAW,CAAC,WAAW,CAAC;gBAC/B,WAAW,CAAC,gBAAgB,GAAG,QAAQ,CAAC;gBAExC,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;oBACjB,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;gBAC5C,CAAC;qBAAM,CAAC;oBACN,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;gBAC7C,CAAC;YAEH,CAAC;iBAAM,IAAI,YAAY,KAAK,sBAAU,CAAC,oBAAoB,CAAC,EAAE,CAAC;gBAC7D,OAAO,WAAW,CAAC,WAAW,CAAC;gBAC/B,WAAW,CAAC,gBAAgB,GAAG,QAAQ,CAAC;YAC1C,CAAC;iBAAM,IAAI,YAAY,KAAK,sBAAU,CAAC,oBAAoB,CAAC,EAAE,CAAC;gBAC7D,OAAO,WAAW,CAAC,WAAW,CAAC;gBAC/B,WAAW,CAAC,gBAAgB,GAAG,QAAQ,CAAC;gBACxC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;YAC5C,CAAC;iBAAM,IAAI,YAAY,KAAK,sBAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7C,OAAO,WAAW,CAAC,WAAW,CAAC;gBAC/B,WAAW,CAAC,gBAAgB,GAAG,QAAQ,CAAC;gBACxC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;YAC5C,CAAC;iBAAM,IAAI,YAAY,KAAK,sBAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAClD,OAAO,WAAW,CAAC,WAAW,CAAC;gBAC/B,WAAW,CAAC,gBAAgB,GAAG,QAAQ,CAAC;gBACxC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;YACjD,CAAC;iBAAM,CAAC;gBAEN,WAAW,CAAC,WAAW,GAAG,WAAW,CAAC;gBAEtC,OAAQ,WAAmB,CAAC,gBAAgB,CAAC;gBAC7C,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;YACjD,CAAC;YAED,WAAW,CAAC,KAAK,GAAG,YAAY,CAAC;YAEjC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,YAAY,CAAC,EAAE,CAAC;gBAC/C,SAAS;YACX,CAAC;YACD,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAErC,IAAI,CAAC;gBACH,IAAI,QAAQ,GAAG,EAAE,CAAC;gBAClB,IAAI,YAAY,GAAG,CAAC,CAAC;gBACrB,IAAI,gBAAgB,GAAG,CAAC,CAAC;gBAEzB,IAAI,WAAW,CAAC,KAAK,KAAK,aAAa,EAAE,CAAC;oBACxC,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAC9D,CAAC;qBAAM,CAAC;oBACN,MAAM,MAAM,GAA+B,MAAM,OAAO,CAAC,IAAI,CAAC;wBAC5D,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC;wBAC3C,IAAI,OAAO,CAA6B,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CACpD,UAAU,CACR,GAAG,EAAE,CACH,MAAM,CAAC,IAAI,+BAAY,CAAC,mCAAmC,CAAC,CAAC,EAC/D,CAAC,GAAG,EAAE,GAAG,IAAI,CACd,CACF;qBACF,CAAC,CAAC;oBAEH,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;oBACnD,YAAY,GAAG,MAAM,CAAC,KAAK,EAAE,aAAa,IAAI,CAAC,CAAC;oBAChD,gBAAgB,GAAG,MAAM,CAAC,KAAK,EAAE,iBAAiB,IAAI,CAAC,CAAC;gBAC1D,CAAC;gBAED,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;gBAE/C,OAAO;oBACL,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ;oBAChD,MAAM,EAAE,GAAG;oBACX,KAAK,EAAE;wBACL,aAAa,EAAE,YAAY;wBAC3B,iBAAiB,EAAE,gBAAgB;wBACnC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;4BAC5C,KAAK;4BACL,WAAW,EAAE,YAAY;4BACzB,YAAY,EAAE,gBAAgB;yBAC/B,CAAC;qBACH;iBACF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,QAAQ,EAAE,CAAC;gBAEX,IAAI,KAAK,YAAY,+BAAY,EAAE,CAAC;oBAClC,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,kBAAkB,KAAK,CAAC,OAAO,sBAAsB,QAAQ,IAAI,WAAW,GAAG,CAChF,CAAC;oBAEF,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;wBACjB,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,iCAAiC,QAAQ,oBAAoB,CAC9D,CAAC;oBACJ,CAAC;oBACD,SAAS;gBACX,CAAC;gBAED,MAAM,UAAU,GAAG,KAAK,EAAE,MAAM,IAAI,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;gBAE5D,MAAM,oBAAoB,GAAG,UAAU,KAAK,GAAG,CAAC;gBAChD,IAAI,oBAAoB,EAAE,CAAC;oBACzB,OAAO;wBACL,QAAQ,EACN,gKAAgK;wBAClK,MAAM,EAAE,GAAG;wBACX,KAAK,EAAE;4BACL,aAAa,EAAE,CAAC;4BAChB,iBAAiB,EAAE,CAAC;4BACpB,UAAU,EAAE,CAAC;yBACd;qBACF,CAAC;gBAEJ,CAAC;gBAED,MAAM,WAAW,GAAG,UAAU,KAAK,GAAG,CAAC;gBAEvC,MAAM,gBAAgB,GACpB,KAAK,EAAE,OAAO,EAAE,CAAC,aAAa,CAAC;oBAC/B,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,aAAa,CAAC,CAAC;gBAC5C,MAAM,QAAQ,GAAG,QAAQ,CAAC,gBAAgB,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;gBAExD,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,UAAU,KAAK,CAAC,OAAO,cAAc,UAAU,mBAAmB,QAAQ,GAAG,CAC9E,CAAC;gBAKF,IAAI,WAAW,IAAI,QAAQ,GAAG,WAAW,EAAE,CAAC;oBAC1C,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;wBACnB,MAAM,IAAI,KAAK,CACb,kCAAkC,QAAQ,mCAAmC,CAC9E,CAAC;oBACJ,CAAC;oBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;oBAC5C,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC;gBACvE,CAAC;qBAAM,CAAC;oBAEN,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAGD,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC3D,CAAC;CACF,CAAA;AAxXY,sDAAqB;AAyG1B;IADL,IAAA,cAAO,EAAC,EAAE,IAAI,EAAE,mBAAY,CAAC,UAAU,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC;;;;8DAQ3D;gCAhHU,qBAAqB;IAFjC,IAAA,mBAAU,GAAE;IACZ,IAAA,gBAAS,EAAC,mBAAY,CAAC,UAAU,CAAC;IAqG9B,WAAA,IAAA,kBAAW,EAAC,eAAQ,CAAC,UAAU,CAAC,CAAA;6CACA,iCAAc;GArGtC,qBAAqB,CAwXjC"}