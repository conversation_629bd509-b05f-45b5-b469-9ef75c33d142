import { FunctionComponent } from 'react';

const ChatFlowiseExample: FunctionComponent = () => {
  // const [input, setInput] = useState('');
  // const [chatId, setChatId] = useState('');
  // const { sendFlowiseMessage, messages } = useFlowise();
  //
  // const sendMessage = () => {
  //   if (input.trim() === '') return;
  //   void sendFlowiseMessage(input, chatId);
  //   setInput('');
  // };

  return (
    <div>old</div>
    // <div className="flex flex-1 flex-col">
    //   <TopNavigation></TopNavigation>
    //   <div className="flex flex-1 flex-col">
    //     <div>
    //       <b>ChatGPT 3.5-Turbo Flowise</b>
    //       <div className="grid w-full max-w-sm items-center gap-1.5">
    //         <Label htmlFor="chatId">ChatId</Label>
    //         <Input
    //           id="chatId"
    //           value={chatId}
    //           onChange={(e) => setChatId(e.target.value)}
    //         ></Input>
    //       </div>
    //     </div>
    //     <div className="min-h-80">
    //       {messages.map((message, index) => (
    //         <ChatMessageElement
    //           message={message}
    //           key={index}
    //         ></ChatMessageElement>
    //       ))}
    //     </div>
    //     <PromptBar
    //       input={input}
    //       onInputChange={(value) => setInput(value)}
    //       onSendButton={() => sendMessage()}
    //       isSendDisabled={false}
    //     ></PromptBar>
    //   </div>
    // </div>
  );
};

export { ChatFlowiseExample };
