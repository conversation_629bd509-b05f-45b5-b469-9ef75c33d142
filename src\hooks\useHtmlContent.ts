
import { useState, useEffect } from 'react';

// Use import.meta.glob to load all HTML files in the scoringFramework directory
const htmlFiles = import.meta.glob('/src/scoringFramework/*.html', { as: 'raw' });

export const useHtmlContent = (indicator: string | undefined, topic: string | undefined) => {
  const [content, setContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchContent = async () => {
      setIsLoading(true);
      setError(null);
      setContent('');
      if (!indicator || !topic) {
        setIsLoading(false);
        setError('Missing indicator or topic');
        return;
      }

      const transformedIndicator = indicator.toUpperCase() === "WATCH_FINDINGS" 
        ? "360_watch_findings" 
        : indicator.toLowerCase();
      
      try {
        // Clean up the topic name by removing emojis and trimming
        const cleanTopic = topic.replace(/[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{1FA70}-\u{1FAFF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu, '')
          .trim()
          .replace(/\s+/g, '')
          .toLowerCase();
        
        // Create the file path based on indicator and topic - everything lowercase
        const filePath = `/src/scoringFramework/${transformedIndicator}_${cleanTopic}.html`;
        
        // Find the matching file using the import.meta.glob result
        const importFile = htmlFiles[filePath];
        
        if (!importFile) {
          throw new Error(`File not found: ${filePath}`);
        }
        
        // Load the file content
        const htmlContent = await importFile();
        setContent(htmlContent);
        setIsLoading(false);
      } catch (err) {
        console.error('Error loading HTML content:', err);
        setError('Failed to load scoring information');
        setIsLoading(false);
      }
    };

    fetchContent();
  }, [indicator, topic]);

  return { content, isLoading, error };
};
