import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1730220731947 implements MigrationInterface {
  name = 'SchemaUpdate1730220731947';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_workspace" DROP COLUMN "role"`);
    await queryRunner.query(
      `CREATE TYPE "public"."user_workspace_role_enum" AS ENUM('SUPER_ADMIN', 'WORKSPACE_ADMIN', 'AI_CONTRIBUTOR', 'CONTRIBUTOR')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_workspace" ADD "role" "public"."user_workspace_role_enum"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_workspace" DROP COLUMN "role"`);
    await queryRunner.query(`DROP TYPE "public"."user_workspace_role_enum"`);
    await queryRunner.query(
      `ALTER TABLE "user_workspace" ADD "role" character varying`,
    );
  }
}
