import { <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { ESRSTopic } from './esrs-topic.entity';
import { ESRSDisclosureRequirement } from './esrs-disclosure-requirement.entity';

@Entity('esrs_topic_disclosure_requirement')
export class ESRSTopicDisclosureRequirement {
  @PrimaryColumn()
  esrsTopicId: number;

  @PrimaryColumn()
  esrsDisclosureRequirementId: number;

  @ManyToOne(() => ESRSTopic, (topic) => topic.disclosureRequirementRelations)
  @JoinColumn({ name: 'esrsTopicId' })
  topic: ESRSTopic;

  @ManyToOne(() => ESRSDisclosureRequirement, (dr) => dr.topicRelations)
  @JoinColumn({ name: 'esrsDisclosureRequirementId' })
  disclosureRequirement: ESRSDisclosureRequirement;
}
