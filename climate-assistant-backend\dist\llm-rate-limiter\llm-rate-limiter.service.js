"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var LlmRateLimiterService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LlmRateLimiterService = void 0;
const common_1 = require("@nestjs/common");
const bull_1 = require("@nestjs/bull");
const chat_gpt_service_1 = require("../llm/chat-gpt.service");
const constants_1 = require("../constants");
const error_middleware_1 = require("../middleware/error.middleware");
const jobs_1 = require("../types/jobs");
const seed = 123;
let LlmRateLimiterService = LlmRateLimiterService_1 = class LlmRateLimiterService {
    constructor(llmQueue, chatGptService) {
        this.llmQueue = llmQueue;
        this.chatGptService = chatGptService;
        this.logger = new common_1.Logger(LlmRateLimiterService_1.name);
        this.lastResetTime = Date.now();
        this.modelUsage = {
            [constants_1.LLM_MODELS.o1]: {
                tokensUsed: 0,
                requestsUsed: 0,
            },
            [constants_1.LLM_MODELS.o3]: {
                tokensUsed: 0,
                requestsUsed: 0,
            },
            [constants_1.LLM_MODELS['gpt-4o']]: {
                tokensUsed: 0,
                requestsUsed: 0,
            },
            [constants_1.LLM_MODELS['o3-mini']]: {
                tokensUsed: 0,
                requestsUsed: 0,
            },
            [constants_1.LLM_MODELS['o4-mini']]: {
                tokensUsed: 0,
                requestsUsed: 0,
            },
            [constants_1.LLM_MODELS['o1-preview']]: {
                tokensUsed: 0,
                requestsUsed: 0,
            },
            [constants_1.LLM_MODELS['gpt-4o-mini']]: {
                tokensUsed: 0,
                requestsUsed: 0,
            },
            [constants_1.LLM_MODELS['deepseek-r1']]: {
                tokensUsed: 0,
                requestsUsed: 0,
            },
            [constants_1.LLM_MODELS['gemini-2.5-pro']]: {
                tokensUsed: 0,
                requestsUsed: 0,
            },
            [constants_1.LLM_MODELS['gemini-2.0-flash']]: {
                tokensUsed: 0,
                requestsUsed: 0,
            },
            [constants_1.LLM_MODELS['gemini-2.5-flash']]: {
                tokensUsed: 0,
                requestsUsed: 0,
            },
        };
        this.modelLimits = {
            [constants_1.LLM_MODELS.o1]: {
                maxTokensPerMinute: 164000,
                maxRequestsPerMinute: 274,
            },
            [constants_1.LLM_MODELS['gpt-4o']]: {
                maxTokensPerMinute: 445000,
                maxRequestsPerMinute: 267,
            },
            [constants_1.LLM_MODELS['o3-mini']]: {
                maxTokensPerMinute: 5000000,
                maxRequestsPerMinute: 500,
            },
            [constants_1.LLM_MODELS['o4-mini']]: {
                maxTokensPerMinute: 5000000,
                maxRequestsPerMinute: 500,
            },
            [constants_1.LLM_MODELS['o1-preview']]: {
                maxTokensPerMinute: 90000,
                maxRequestsPerMinute: 700,
            },
            [constants_1.LLM_MODELS['gpt-4o-mini']]: {
                maxTokensPerMinute: 90000,
                maxRequestsPerMinute: 700,
            },
            [constants_1.LLM_MODELS['deepseek-r1']]: {
                maxTokensPerMinute: 400000,
                maxRequestsPerMinute: 700,
            },
            [constants_1.LLM_MODELS['o3']]: {
                maxTokensPerMinute: 400000,
                maxRequestsPerMinute: 700,
            },
            [constants_1.LLM_MODELS['gemini-2.5-pro']]: {
                maxTokensPerMinute: 20000,
                maxRequestsPerMinute: 60,
            },
            [constants_1.LLM_MODELS['gemini-2.0-flash']]: {
                maxTokensPerMinute: 20000,
                maxRequestsPerMinute: 60,
            },
            [constants_1.LLM_MODELS['gemini-2.5-flash']]: {
                maxTokensPerMinute: 20000,
                maxRequestsPerMinute: 60,
            },
        };
    }
    async processLlmRequest(job) {
        try {
            const request = JSON.parse(job.data.payload);
            return await this.processJob(request);
        }
        catch (error) {
            throw error;
        }
    }
    exceedsTokenLimit(tokens, model) {
        const limits = this.modelLimits[model];
        return tokens > limits.maxTokensPerMinute;
    }
    async processJob(request) {
        this.resetTokensIfNeeded();
        if (!this.canMakeRequest(request.tokens, request.model)) {
            await new Promise((resolve) => setTimeout(resolve, 1000));
            return await this.processJob(request);
        }
        return await this.makeRequest(request);
    }
    resetTokensIfNeeded() {
        const currentTime = Date.now();
        if (currentTime - this.lastResetTime >= 80 * 1000) {
            this.resetTokens();
            this.lastResetTime = currentTime;
        }
    }
    resetTokens() {
        Object.keys(this.modelUsage).forEach((model) => {
            this.modelUsage[model] = {
                tokensUsed: 0,
                requestsUsed: 0,
            };
        });
        this.logger.log('Tokens reset complete');
    }
    async handleRequest(request, priority = 1) {
        const tokens = this.chatGptService.calculateTokens(request.messages);
        if (this.exceedsTokenLimit(tokens, request.model)) {
            throw new Error(`Request exceeds maximum token limit of ${this.modelLimits[request.model].maxTokensPerMinute} for model ${request.model}`);
        }
        const payload = JSON.stringify({ tokens, ...request });
        const job = await this.llmQueue.add(jobs_1.JobQueue.LlmRequest, {
            payload,
        }, {
            priority,
        });
        try {
            const resp = await job.finished();
            return resp;
        }
        catch (error) {
            this.logger.error('Error processing job:', error);
            throw new Error(`Job failed: ${error.message}`);
        }
    }
    canMakeRequest(tokens, model) {
        const usage = this.modelUsage[model];
        const limits = this.modelLimits[model];
        return (usage.tokensUsed + tokens <= limits.maxTokensPerMinute &&
            usage.requestsUsed < limits.maxRequestsPerMinute);
    }
    updateTokenUsage(tokens, model) {
        this.modelUsage[model].tokensUsed += tokens;
        this.modelUsage[model].requestsUsed++;
        this.logger.log(`Model ${model} - Updated tokens used: ${this.modelUsage[model].tokensUsed}, ` +
            `Requests used: ${this.modelUsage[model].requestsUsed}`);
    }
    async makeRequest({ tokens, model, messages, temperature, json, }) {
        const titleParams = {
            model,
            messages: messages,
            seed,
            temperature,
            response_format: json
                ? {
                    type: 'json_object',
                }
                : undefined,
        };
        let attempts = 1;
        const maxAttempts = 5;
        while (attempts < maxAttempts) {
            let currentModel = model;
            if (model === constants_1.LLM_MODELS.o1 && attempts > 2) {
                currentModel = constants_1.LLM_MODELS['gpt-4o'];
            }
            let openai;
            if (currentModel === constants_1.LLM_MODELS.o1) {
                delete titleParams.temperature;
                titleParams.reasoning_effort = 'medium';
                if (attempts > 1) {
                    openai = this.chatGptService.openAiClient;
                }
                else {
                    openai = this.chatGptService.azureo1Client;
                }
            }
            else if (currentModel === constants_1.LLM_MODELS['o3-mini-2025-01-31']) {
                delete titleParams.temperature;
                titleParams.reasoning_effort = 'medium';
            }
            else if (currentModel === constants_1.LLM_MODELS['o3-mini-2025-01-31']) {
                delete titleParams.temperature;
                titleParams.reasoning_effort = 'medium';
                openai = this.chatGptService.openAiClient;
            }
            else if (currentModel === constants_1.LLM_MODELS['o3']) {
                delete titleParams.temperature;
                titleParams.reasoning_effort = 'medium';
                openai = this.chatGptService.openAiClient;
            }
            else if (currentModel === constants_1.LLM_MODELS['o4-mini']) {
                delete titleParams.temperature;
                titleParams.reasoning_effort = 'medium';
                openai = this.chatGptService.azureo4miniClient;
            }
            else {
                titleParams.temperature = temperature;
                delete titleParams.reasoning_effort;
                openai = this.chatGptService.azureOpenAiClient;
            }
            titleParams.model = currentModel;
            if (!this.canMakeRequest(tokens, currentModel)) {
                continue;
            }
            this.updateTokenUsage(tokens, model);
            try {
                let response = '';
                let promptTokens = 0;
                let completionTokens = 0;
                if (titleParams.model === 'deepseek-r1') {
                    response = await this.chatGptService.callDeepSeek(messages);
                }
                else {
                    const result = await Promise.race([
                        openai.chat.completions.create(titleParams),
                        new Promise((_, reject) => setTimeout(() => reject(new error_middleware_1.TimeoutError('Request timed out after 3 minutes')), 3 * 60 * 1000)),
                    ]);
                    response = result.choices[0].message.content ?? '';
                    promptTokens = result.usage?.prompt_tokens ?? 0;
                    completionTokens = result.usage?.completion_tokens ?? 0;
                }
                this.updateTokenUsage(completionTokens, model);
                return {
                    response: json ? JSON.parse(response) : response,
                    status: 200,
                    token: {
                        prompt_tokens: promptTokens,
                        completion_tokens: completionTokens,
                        total_cost: this.chatGptService.calculateCost({
                            model,
                            inputTokens: promptTokens,
                            outputTokens: completionTokens,
                        }),
                    },
                };
            }
            catch (error) {
                attempts++;
                if (error instanceof error_middleware_1.TimeoutError) {
                    this.logger.log(`Timeout Error: ${error.message}. Retrying attempt ${attempts}/${maxAttempts}.`);
                    if (attempts > 2) {
                        this.logger.log(`Switching to model '4o' after ${attempts} timeout failures.`);
                    }
                    continue;
                }
                const httpStatus = error?.status || error?.response?.status;
                const isInvalidChatRequest = httpStatus === 400;
                if (isInvalidChatRequest) {
                    return {
                        response: 'The request was either filtered due to the context triggering our content management policy or because the context was longer than the allowed context length.',
                        status: 400,
                        token: {
                            prompt_tokens: 0,
                            completion_tokens: 0,
                            total_cost: 0,
                        },
                    };
                }
                const isRateLimit = httpStatus === 429;
                const retryAfterHeader = error?.headers?.['retry-after'] ||
                    error?.response?.headers?.['retry-after'];
                const waitTime = parseInt(retryAfterHeader ?? '60', 10);
                this.logger.log(`Error: ${error.message} - Status: ${httpStatus} - Retry-After: ${waitTime}s`);
                if (isRateLimit && attempts < maxAttempts) {
                    if (waitTime > 240) {
                        throw new Error(`Rate limit hit, but wait time (${waitTime}s) exceeds 240 seconds. Aborting.`);
                    }
                    const safeWait = Math.max(waitTime, 30) + 5;
                    await new Promise((resolve) => setTimeout(resolve, safeWait * 1000));
                }
                else {
                    throw error;
                }
            }
        }
        throw new Error('Max retry attempts reached. Aborting.');
    }
};
exports.LlmRateLimiterService = LlmRateLimiterService;
__decorate([
    (0, bull_1.Process)({ name: jobs_1.JobProcessor.LlmRequest, concurrency: 10 }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], LlmRateLimiterService.prototype, "processLlmRequest", null);
exports.LlmRateLimiterService = LlmRateLimiterService = LlmRateLimiterService_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, bull_1.Processor)(jobs_1.JobProcessor.LlmRequest),
    __param(0, (0, bull_1.InjectQueue)(jobs_1.JobQueue.LlmRequest)),
    __metadata("design:paramtypes", [Object, chat_gpt_service_1.ChatGptService])
], LlmRateLimiterService);
//# sourceMappingURL=llm-rate-limiter.service.js.map