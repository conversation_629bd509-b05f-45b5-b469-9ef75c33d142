"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectGuard = void 0;
const common_1 = require("@nestjs/common");
const project_service_1 = require("./project.service");
let ProjectGuard = class ProjectGuard {
    constructor(projectService) {
        this.projectService = projectService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const projectId = request.params.projectId;
        const workspaceId = request.user.workspaceId;
        const project = await this.projectService.findById(projectId);
        if (project.workspaceId !== workspaceId) {
            throw new common_1.UnauthorizedException(`Project is not from this workspace`);
        }
        return true;
    }
};
exports.ProjectGuard = ProjectGuard;
exports.ProjectGuard = ProjectGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [project_service_1.ProjectService])
], ProjectGuard);
//# sourceMappingURL=project.guard.js.map