import { CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { DataRequestService } from './data-request.service';
export declare class DataRequestGuard implements CanActivate {
    private readonly dataRequestService;
    private readonly reflector;
    constructor(dataRequestService: DataRequestService, reflector: Reflector);
    canActivate(context: ExecutionContext): Promise<boolean>;
}
