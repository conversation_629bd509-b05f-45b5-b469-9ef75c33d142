"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileInterceptor = exports.USER_FILE_UPLOAD_DIRECTORY = void 0;
const multer_1 = require("multer");
const path_1 = require("path");
const fs_1 = require("fs");
const platform_express_1 = require("@nestjs/platform-express");
exports.USER_FILE_UPLOAD_DIRECTORY = 'user-uploads/';
exports.fileInterceptor = (0, platform_express_1.FileInterceptor)('file', {
    storage: (0, multer_1.diskStorage)({
        destination: (req, file, callback) => {
            const workspaceId = req.user?.workspaceId || '';
            const workspacePath = `${exports.USER_FILE_UPLOAD_DIRECTORY}${workspaceId}`;
            if (!(0, fs_1.existsSync)(workspacePath)) {
                (0, fs_1.mkdirSync)(workspacePath, { recursive: true });
            }
            callback(null, workspacePath);
        },
        filename: (req, file, callback) => {
            const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
            const { name, ext } = (0, path_1.parse)(originalName);
            file.originalname = originalName;
            const randomName = Array(32)
                .fill(null)
                .map(() => Math.round(Math.random() * 16).toString(16))
                .join('');
            return callback(null, `${name}-${randomName}${ext}`);
        },
    }),
});
//# sourceMappingURL=upload-utils.js.map