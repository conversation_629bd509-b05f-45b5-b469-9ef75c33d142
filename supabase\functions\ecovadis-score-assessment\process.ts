// @ts-ignore
import { type SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Types
type ScoreLevel = 'Outstanding' | 'Advanced' | 'Good' | 'Partial' | 'Insufficient';

export interface LLMGapAnalysis {
  metadata: Metadata;
  detailed_analysis: DetailedAnalysis;
  current_score_assessment: CurrentScoreAssessment;
  action_prioritization: string;
  conclusion: string;
}
export interface Metadata {
  document_ids?: (string)[] | null;
  themes?: (string)[] | null;
  indicators?: (string)[] | null;
}
export interface DetailedAnalysis {
  document_review: string;
  criteria_checklist: string;
  gap_analysis?: (GapAnalysisEntity)[] | null;
}
export interface GapAnalysisEntity {
  gap_type: string;
  gap: string;
  description: string;
  affected_documents?: (string)[] | null;
  recommended_actions?: (string)[] | null;
  sample_text: string;
}
export interface CurrentScoreAssessment {
  score: number;
  level: ScoreLevel;
  scoring_breakdown_markdown: string;
}

type StoreResponseResult = {
  success: boolean;
  scoreId: string | null;
  newStatus: string;
  error?: string;
  details?: string;
};

/**
 * Stores the Dify response in the database
 * @param difyResponse The Dify response to store
 * @param supabaseClient An authenticated Supabase client
 * @returns A result object indicating success and stored data IDs
 */
export async function storeLLMGapAnalysisResponse({
  llmGapAnalysis,
  supabaseClient,
  projectQuestionId
}: {
  llmGapAnalysis: LLMGapAnalysis,
  supabaseClient: SupabaseClient,
  projectId: string,
  questionId: string,
  projectQuestionId: string,
}): Promise<StoreResponseResult> {
  try {

    // 2. Extract data from dify response
    let currentScore: {
      score: number;
      level: ScoreLevel;
      breakdown: string | null;
      description: string;
      conclusion: string;
    } | null = null;

    // Extract score information
    if (llmGapAnalysis.current_score_assessment) {
      const scoreData = llmGapAnalysis.current_score_assessment;
      currentScore = {
        score: scoreData.score || 0,
        level: scoreData.level || 'Insufficient',
        breakdown: scoreData.scoring_breakdown_markdown,
        description: llmGapAnalysis.action_prioritization || "",
        conclusion: llmGapAnalysis.conclusion || ""
      };

    }

    // 3. Store the score data
    let scoreId: string | null = null;
    if (currentScore) {
      // Check if a score already exists for this question
      const { data: existingScore, error: existingScoreError } = await supabaseClient
        .from('project_ecovadis_question_score')
        .select('id, score, level, breakdown, description, conclusion')
        .eq('questionId', projectQuestionId)
        .maybeSingle();

      if (existingScoreError) {
        console.error('Error checking existing score:', existingScoreError);
      }

      if (existingScore) {
        // If score exists, update it and create history entry
        scoreId = existingScore.id;

        // Create history record first
        const historyEntry = {
          scoreId: existingScore.id,
          score: existingScore.score,
          level: existingScore.level,
          description: existingScore.description,
          breakdown: existingScore.breakdown,
          conclusion: existingScore.conclusion,
          // Using timestamp as version without millisecond precision
          version: Math.floor(new Date().getTime() / 1000)
        };

        const { error: historyError } = await supabaseClient
          .from('project_ecovadis_question_score_history')
          .insert(historyEntry);

        if (historyError) {
          console.error('Error creating score history:', historyError);
        }

        // Update existing score
        const { error: updateScoreError } = await supabaseClient
          .from('project_ecovadis_question_score')
          .update({
            score: currentScore.score,
            level: currentScore.level,
            description: currentScore.description,
            breakdown: currentScore.breakdown,
            conclusion: currentScore.conclusion
          })
          .eq('id', existingScore.id);

        if (updateScoreError) {
          console.error('Error updating score:', updateScoreError);
        }
      } else {
        // If no score exists, create a new one
        const { data: newScore, error: newScoreError } = await supabaseClient
          .from('project_ecovadis_question_score')
          .insert({
            questionId: projectQuestionId,
            score: currentScore.score,
            level: currentScore.level,
            description: currentScore.description,
            breakdown: currentScore.breakdown,
            conclusion: currentScore.conclusion
          })
          .select('id')
          .single();

        if (newScoreError) {
          console.error('Error creating new score:', newScoreError,);
        } else if (newScore) {
          scoreId = newScore.id;
        }
      }
    }

    // Update the question status if needed
    let newStatus = "pending";
    if (currentScore) {
      if (currentScore.score >= 75) {
        newStatus = "complete";
      }
    }

    const { error: updateStatusError } = await supabaseClient
      .from('project_ecovadis_question')
      .update({ status: newStatus })
      .eq('id', projectQuestionId);

    if (updateStatusError) {
      console.error('Error updating question status:', updateStatusError);
    }

    // 6. Return success response
    return {
      success: true,
      scoreId: scoreId,
      newStatus: newStatus
    };
  } catch (error) {
    console.error('Unexpected error:', error instanceof Error ? error.message : String(error));
    return {
      success: false,
      scoreId: null,
      newStatus: "pending",
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error)
    };
  }
}

