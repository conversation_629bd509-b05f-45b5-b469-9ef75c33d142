"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchEngineTool = void 0;
const common_1 = require("@nestjs/common");
const perplexity_service_1 = require("./perplexity.service");
let SearchEngineTool = class SearchEngineTool {
    constructor(perplexityService) {
        this.perplexityService = perplexityService;
    }
    createSearchEngine() {
        return {
            type: 'async-gpt-tool',
            toolDefinition: {
                type: 'function',
                function: {
                    name: 'get-up-to-date-infos-via-search-engine',
                    description: 'Sucht im Web nach der Frage und gibt dir aktuelle Infos - perfekt für Fragen zu Förderungen, Anbietern, Competitors oder aktuellen Entwicklungen.',
                    parameters: {
                        type: 'object',
                        properties: {
                            question: {
                                type: 'string',
                                description: 'Die Frage, die beantwortet werden soll',
                            },
                        },
                        require: ['question'],
                    },
                },
            },
            execute: (payload) => this.searchWithSearchEngine(payload.question),
        };
    }
    async searchWithSearchEngine(question) {
        if (question === undefined) {
            return '';
        }
        console.log(`[Search Engine] Question: ${question}`);
        const answer = await this.perplexityService.createCompletion('llama-3-sonar-small-32k-online', [
            {
                role: 'system',
                content: ` 
          Du bist eine Suchmaschine die Kontextinformationen zu der Frage des Users liefert sodass damit eine gute Antwort für den User generiert werden kann.
          
          Konzentriere dich kurz und bündig möglichst viele Fakten bereitzustellen!
          
          Du antwortest immer auf deutsch - auch wenn dir die Frage auf englisch gestellt wird.
       `,
            },
            { role: 'user', content: question },
        ]);
        return answer;
    }
};
exports.SearchEngineTool = SearchEngineTool;
exports.SearchEngineTool = SearchEngineTool = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [perplexity_service_1.PerplexityService])
], SearchEngineTool);
//# sourceMappingURL=search-engine.tool.js.map