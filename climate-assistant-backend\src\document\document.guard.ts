import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { DocumentService } from './document.service';

@Injectable()
export class DocumentGuard implements CanActivate {
  constructor(private readonly documentService: DocumentService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const documentId = request.params.id;
    const workspaceId = request.user.workspaceId;

    const document = await this.documentService.findDocumentById(documentId);

    if (!document) {
      throw new UnauthorizedException(`Document not found`);
    }
    if (document.workspaceId !== workspaceId) {
      throw new UnauthorizedException(`Document is not from this workspace`);
    }

    request.document = document;

    return true;
  }
}

@Injectable()
export class DocumentChunkGuard implements CanActivate {
  constructor(private readonly documentService: DocumentService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const documentChunkId = request.params.id;
    const workspaceId = request.user.workspaceId;

    const documentChunk =
      await this.documentService.findDocumentChunkById(documentChunkId);

    if (!documentChunk) {
      throw new UnauthorizedException(`DocumentChunk not found`);
    }
    if (documentChunk.document.workspaceId !== workspaceId) {
      throw new UnauthorizedException(
        `DocumentCunk is not from this workspace`,
      );
    }

    return true;
  }
}
