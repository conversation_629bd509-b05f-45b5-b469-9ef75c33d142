import {
  BoldIcon,
  ItalicIcon,
  UnderlineIcon,
  StrikethroughIcon,
  AlignLeftIcon,
  AlignCenterIcon,
  AlignRightIcon,
  AlignJustifyIcon,
  TableIcon,
  Grid2x2XIcon,
  ListIcon,
  ListOrderedIcon,
} from 'lucide-react';
import { Editor } from '@tiptap/react';

import { Button } from '../button';
import IconRenderer from '../icons';

import { cn } from '@/lib/utils';

const ColumnAddIcon = (props: any) => (
  <IconRenderer iconName="ColumnAdd" {...props} />
);
const ColumnDeleteIcon = (props: any) => (
  <IconRenderer iconName="ColumnDelete" {...props} />
);
const RowAddIcon = (props: any) => (
  <IconRenderer iconName="RowAdd" {...props} />
);
const RowDeleteIcon = (props: any) => (
  <IconRenderer iconName="RowDelete" {...props} />
);

const formatConfig = {
  bold: { icon: BoldIcon, toggle: 'toggleBold', args: [] },
  italic: { icon: ItalicIcon, toggle: 'toggleItalic', args: [] },
  underline: { icon: UnderlineIcon, toggle: 'toggleUnderline', args: [] },
  strike: { icon: StrikethroughIcon, toggle: 'toggleStrike', args: [] },
  alignLeft: { icon: AlignLeftIcon, toggle: 'setTextAlign', args: ['left'] },
  alignCenter: {
    icon: AlignCenterIcon,
    toggle: 'setTextAlign',
    args: ['center'],
  },
  alignRight: { icon: AlignRightIcon, toggle: 'setTextAlign', args: ['right'] },
  alignJustify: {
    icon: AlignJustifyIcon,
    toggle: 'setTextAlign',
    args: ['justify'],
  },
  orderedList: { icon: ListOrderedIcon, toggle: 'toggleOrderedList', args: [] },
  bulletList: { icon: ListIcon, toggle: 'toggleBulletList', args: [] },
  table: {
    icon: TableIcon,
    toggle: 'insertTable',
    args: [{ rows: 3, cols: 3, withHeaderRow: true }],
  },
  addColumnAfter: {
    icon: ColumnAddIcon,
    toggle: 'addColumnAfter',
    args: [],
  },
  deleteColumn: {
    icon: ColumnDeleteIcon,
    toggle: 'deleteColumn',
    args: [],
  },
  addRowAfter: {
    icon: RowAddIcon,
    toggle: 'addRowAfter',
    args: [],
  },
  deleteRow: {
    icon: RowDeleteIcon,
    toggle: 'deleteRow',
    args: [],
  },
  deleteTable: {
    icon: Grid2x2XIcon,
    toggle: 'deleteTable',
    args: [],
  },
};

type Toggle =
  | 'toggleBold'
  | 'toggleItalic'
  | 'toggleUnderline'
  | 'toggleStrike'
  | 'setTextAlign'
  | 'insertTable'
  | 'addColumnAfter'
  | 'deleteColumn'
  | 'addRowAfter'
  | 'deleteRow'
  | 'deleteTable';

export const TipTapMenuButton = ({
  format,
  editor,
}: {
  format: keyof typeof formatConfig;
  editor: Editor;
}) => {
  const config = formatConfig[format];
  const isActive = format.includes('align')
    ? editor.isActive({ textAlign: config.args[0] })
    : editor.isActive(format);
  const IconComponent = config.icon;
  const toggleCommad = config.toggle as Toggle;

  const handleClick = () => {
    editor
      .chain()
      .focus()
      [toggleCommad](config.args[0] as any)
      .run();
  };

  return (
    <Button
      className={cn(
        'rounded-sm p-0 aspect-square border-0 h-8 text-slate-900 hover:bg-slate-100',
        isActive ? 'bg-white' : 'bg-slate-200'
      )}
      onClick={handleClick}
    >
      <IconComponent
        className={cn(
          'w-4 h-4',
          (config as any).color ? `text-${(config as any).color}-500` : ''
        )}
      />
    </Button>
  );
};
