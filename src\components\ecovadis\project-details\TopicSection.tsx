
import React from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { EcovadisQuestion } from '@/types/ecovadis';
import { IndicatorSection } from './IndicatorSection';

interface TopicSectionProps {
  topic: string;
  questions: EcovadisQuestion[];
  expandedTopics: string[];
  expandedIndicators: string[];
  selectedQuestion: string;
  onToggleTopic: (topic: string) => void;
  onToggleIndicator: (indicatorKey: string) => void;
  onSelectQuestion: (questionId: string) => void;
}

export const TopicSection: React.FC<TopicSectionProps> = ({
  topic,
  questions,
  expandedTopics,
  expandedIndicators,
  selectedQuestion,
  onToggleTopic,
  onToggleIndicator,
  onSelectQuestion
}) => {
  const isExpanded = expandedTopics.includes(topic);
  
  const handleTopicClick = () => {
    onToggleTopic(topic);
  };
  
  // Indicator ordering
  const INDICATOR_ORDER = [
    'Policies',
    'Measures', 
    'Coverage',
    'Endorsements',
    'Certification',
    'Reporting',
    '360 Watch'
  ];
  
  // Group questions by indicator
  const questionsByIndicator: Record<string, EcovadisQuestion[]> = {};
  questions.forEach(question => {
    const indicator = question.indicator || 'OTHER';
    if (!questionsByIndicator[indicator]) {
      questionsByIndicator[indicator] = [];
    }
    questionsByIndicator[indicator].push(question);
  });

  // Sort indicators by defined order
  const sortedIndicators = Object.keys(questionsByIndicator).sort((a, b) => {
    const indexA = INDICATOR_ORDER.indexOf(a);
    const indexB = INDICATOR_ORDER.indexOf(b);
    
    // If both indicators are in the order array, sort by their index
    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }
    // If only one is in the order array, prioritize it
    if (indexA !== -1) return -1;
    if (indexB !== -1) return 1;
    // If neither is in the order array, sort alphabetically
    return a.localeCompare(b);
  });
  
  return (
    <div className="mb-3">
      <button
        className="flex items-center justify-between w-full py-2 px-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded"
        onClick={handleTopicClick}
      >
        <span className="flex items-center">
          {isExpanded ? 
            <ChevronDown className="h-4 w-4 mr-1 text-gray-500" /> : 
            <ChevronRight className="h-4 w-4 mr-1 text-gray-500" />
          }
          <span>{topic}</span>
        </span>
        <span className="text-xs text-gray-500">
          {questions.filter(q => q.status === 'Complete').length}/{questions.length}
        </span>
      </button>
      
      {isExpanded && (
        <div className="ml-6 mt-1 space-y-1">
          {sortedIndicators.map((indicator) => {
            const indicatorQuestions = questionsByIndicator[indicator];
            const indicatorKey = `${topic}-${indicator}`;
            const isIndicatorExpanded = expandedIndicators.includes(indicatorKey);
            
            return (
              <IndicatorSection
                key={indicator}
                indicator={indicator}
                questions={indicatorQuestions}
                isExpanded={isIndicatorExpanded}
                selectedQuestion={selectedQuestion}
                onToggle={() => onToggleIndicator(indicatorKey)}
                onSelectQuestion={onSelectQuestion}
              />
            );
          })}
        </div>
      )}
    </div>
  );
};
