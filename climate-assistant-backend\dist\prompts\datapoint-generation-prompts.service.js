"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NormalDpPromptService = void 0;
const common_1 = require("@nestjs/common");
const TurndownService = require("turndown");
const turndownPluginGfm = require("joplin-turndown-plugin-gfm");
const constants_1 = require("../constants");
const llm_response_util_1 = require("../util/llm-response-util");
let NormalDpPromptService = class NormalDpPromptService {
    constructor() {
        this.GENERATION_LANGUAGE = 'German';
        this.turndownService = new TurndownService({
            headingStyle: 'atx',
            bulletListMarker: '*',
        });
        const tables = turndownPluginGfm.tables;
        const strikethrough = turndownPluginGfm.strikethrough;
        this.turndownService.use([tables, strikethrough]);
    }
    generateDatapointContentGenerationSystemPrompt({ esrsDatapoint, generationLanguage, reportTextGenerationRules, customUserRemark, currentContent, otherDatapoints, reportingYear, generalCompanyProfile, linkedChunks, }) {
        const otherDatapointsNames = otherDatapoints
            .map((datapoint) => datapoint.name)
            .join(', ');
        const cleanCustomUserRemark = customUserRemark
            .replace(/<p>/g, '')
            .replace(/<\/p>/g, '')
            .trim();
        const currentYear = new Date().getFullYear();
        return `You are an AI assistant tasked by a European company to craft text blocks that are inserted in their sustainability report. Follow the provided legal requirements of the EU's corporate sustainability reporting directive (CSRD) and correctly reference and cite the company's internal document chunks. You write the text for the datapoint *${esrsDatapoint.datapointId} - ${esrsDatapoint.name}*. Below there is chunks of company internal documents retrieved from our database as context where you might find the required data. Use them as source to inform the paragraphs for the report, cite the Chunk IDs that are above each chunk (not the chunkNumber), write from their perspective and adhere to the requirements provided.
    
    The company has a range of disclosure requirements (DR) it has to report upon. Each DR consists of several datapoints (DPs). The company has many documents with partially relevant data which are chunked up and stored in a vector db. Your task is to find relevant information for the datapoint (DP) *${esrsDatapoint.datapointId} - ${esrsDatapoint.name}*, which the company has to report upon, from a list of RAG-retrieved chunks. Track the sources and cite them correctly, in particular making sure to cite the Chunk IDs above each chunk and not the chunkNumber at the end of the chunks. Consider all relevant legal requirements and write in a way that can be directly integrated into the company’s sustainability report.

    The contents of this prompt are
    1. *Instructions* with details on the requirements for your datapoint text.
    2. *Legal Requirements* Law texts incl application requirements detailling what exactly to report.
    3. *Example output* what the generated json should exactly look like.
    4. *Context*: RAG-retrieved chunks from corporate documentation
    5. Final Remark instructions.

    **Instructions**:
    1. Analyze the legal requirements and the context to identify relevant facts as defined by the legal requirements of *this* datapoint *${esrsDatapoint.datapointId} - ${esrsDatapoint.name}* as well as relevant considerations for it. In particular consider source and time of claims, as sometimes there might be different claims for different values, stemming from subsidies of the company, data from previous years etc.
    2. Adress everything requested by the *legal requirements*. Ensure the writing aligns with the DP's structural and content requirements. Mention that information is missing, contradicting, unclear or ambiguous only in a separate <p> at the end, if that's the case. Avoid both over- and underreporting.
    3. Use only information provided in the context. Do not infer or create additional information. Correctly cite all claims, using the Chunk ID of the source. If specific information is not available, explicitly state: "The required information is not provided in the context." State this in ${constants_1.LANGUAGE_MAP[generationLanguage]} language.
    4. Use precise and professional language suitable for a corporate sustainability report.
    5. Format the output as JSON with HTML formatting within the "datapoint" section. Never introduce any empty lines for space.
    6. Cite sources using the format: <source>["chunk-6"]</source>. The number is the document chunk id at the top of each document chunk (e.g. Document Chunk ID: chunk-3) and typically is the position of the chunk in the array of chunks provided to you. For some chunks there is chunkNumber mentioned at the end of the text. This chunkNumber is just the position of the chunk within its source document, this is NOT the document chunk ID that you have to cite here.
    7. If the output contains Numbers, Year-Numbers, Monetary Numbers, Percentages or similar numeric Data, pack them inside a <span> tag with the following styling: style="color: #eab308"
    8.  All the outcomes incl reasoning tokens that you generate are written in ${constants_1.LANGUAGE_MAP[generationLanguage]} language.
    9. ${reportTextGenerationRules !== '' ? `**Content Generation Rules**:\n${reportTextGenerationRules} \n Prioritize strictly adhering to these rules.` : ''}
    10. ${cleanCustomUserRemark !== '' && cleanCustomUserRemark.length > 2 ? `**USER INSTRUCTION (HIGHEST PRIORITY)**:\n${cleanCustomUserRemark}\n\nYou MUST incorporate this instruction when generating the datapoint text.` : ''}
    ${!!currentContent && `10..1: **EXISTING CONTENT**:\n${this.turndownService.turndown(currentContent.replace(llm_response_util_1.CITATION_CLIENT_REGEX, '$3'))}\n\nThe datapoint already contains the text above. The output you generate will overwrite it. The user is likely requesting either:\n- Quality improvements to this existing text\n- Specific modifications based on their instruction above\n\nYour response should build upon this content while addressing the user's needs.`}
    ${!!generalCompanyProfile && `10..2: **GENERAL COMPANY PROFILE**:\n${this.turndownService.turndown(generalCompanyProfile)}\n\nThis is the general company profile. Use this information to inform your generation of the data point. In particular focus on company structure and which year is being reported for.`}
    11. Prioritize more recent sources over older ones. Prioritize document types in this order: Business Report → Sustainability Report → Materiality Analysis → Policy → Strategy → Other. Take "Remarks" of the user into account, if provided.
    ${reportingYear ? `12. The current reporting year is ${reportingYear}.` : ''}

    We are currently in the year ${currentYear} and ${reportingYear ? ' regardless of what the reporing rules below specify or what the reference document mention, we are strictly reporting for the year ' + reportingYear : ' figure out the reporting year from the reporting rules specified below or from the other references'}. Consider this when doing your generation.

    Before generating the final output: Find relevant facts & keep track of their sources. Do not waste reasnoing tokens on details of those, just collect them as keywords and note their sources. Correct citing is crucial. Most of them come from the context but also sometimes are referenced in the user remark.

    Remember to adhere strictly to the provided information, requirements and json format. Use structuring elements like h3 headings to make it easier to read, but avoid empty lines/breaks. Use only information that is explicitly mentioned in the context. Report on everything needed for ${esrsDatapoint.name} and do not report anything that is not explicitly required for this. You are crafting a report for the company, not evaluating the data provided. If the core information are not provided, just literally state: "The required information is not provided in the context.", NEVER explain the requirements concluding with "information has not been provided". If only a tiny part of information is missing, you can attach a separate <p> at the very end stating what important, required information hasn't been provided. Outside of such a <p> never ever talk about the context, as you would not mention provided context it a final report. You are crafting a report for the company, not evaluating the data provided. Cite every single piece of information in the place where you use it (so in the middle of sentences, not at the end of sentences or even paragraphs) and strictly adhere to this format <source>["chunk-6"]</source>, you have to ensure the chunk number matches exactly to the value provided in context as "Document Chunk ID" (NOT chunkNumber at the end of the chunks).

    **Output Format**:
    - Return a json with the key 'datapoint' and the text of it including in-text citations as values and optionally a key "key_gaps" if there is information missing that is not provided in the context or other considerations to express.
    - Format the text using HTML tags (h1, h2, h3, p).
    - Add a <br> before each h1, h2, h3 tag, except at the beginning.
    - Wrap numbers, year numbers, monetary numbers, percentages, or similar numeric data in <span style="color: #eab308"> tags.
    - cite sources as <source>["chunk-6", "chunk-2"]</source>
    ----------

    Response json example: Learn from this one-shot example json with masked facts what the output should look like.
    <example>
      {"datapoint": "<h2>X1-1_XX – Offenlegung spezifischer Richtlinien zur Inklusion oder Fördermaßnahmen für besonders gefährdete Personengruppen in der eigenen Belegschaft</h2>
      <p>Konzern XXX ist im Rahmen seiner konzernweiten „Group Social Policy“ konkrete Verpflichtungen zu Inklusion und aktiven Fördermaßnahmen für besonders gefährdete oder unterrepräsentierte Personengruppen eingegangen ist. Insbesondere sind folgenden Gruppen explizit benannt:</p>

      * **Frauen (Gender Equality):**  
        Ein unternehmensweiter „Gender Balance Plan“ soll bis 2027 einen Frauenanteil von 40% in den Executive Management Committees erreichen. <source>["chunk-2"]</source>
      * **Personen mit Behinderungen:**  
        Der Konzern hat sowohl global als auch in bestimmten Ländern wie Frankreich (dort über eine eigene „Disability Policy“) konkrete Ziele, um den Zugang zu Arbeit, die Eingliederung und das nachhaltige Beschäftigungsverhältnis für Mitarbeitende mit Behinderungen zu verbessern. <source>["chunk-3"]</source>

      * **LGBTQIA+ Community, ethnische Minderheiten und verschiedene Altersgruppen:**  
        Im Rahmen der Group Social Policy werden bis 2030 schrittweise Maßnahmen aktiviert, die gezielt die Inklusion von Mitarbeiterinnen und Mitarbeitern aus LGBTQIA+-Kreisen, unterschiedlichen Nationalitäten und Ethnien sowie verschiedenen Generationen fördern sollen. <source>["chunk-17"]</source>

      Diese Verpflichtungen stellen laut JCDecaux eigenständige Handlungsfelder dar, die im gesamten Konzern durch lokale Aktionspläne und entsprechende Controlling-Mechanismen umgesetzt werden. <source>["chunk-4"]</source> Eine genauere Ausgestaltung einzelner Fördermaßnahmen – etwa spezifische Trainingsprogramme oder interne Mentoring-Initiativen – wird in den jeweiligen Gesellschaften an lokale Gegebenheiten angepasst, wobei das erklärte Ziel ist, etwaige Barrieren für die betreffenden Gruppen abzubauen und die Chancengleichheit aktiv zu verbessern.
    ",
    "key_gaps":"no gaps with respect to the legal requirements"}
    </example>
    <bad_example>
    {"datapoint": "E1-1_12 – Angabe zum Ausschluss des Unternehmens aus den EU Paris-aligned Benchmarks
    Gemäß ESRS E1-1 (16)(g) hat das Unternehmen offenzulegen, ob es aufgrund der in der Verordnung (EU) 2020/1818 (Climate Benchmark Standards Regulation) genannten Ausschlusskriterien (Art. 12.1 (d) bis (g) und Art. 12.2) von den EU Paris-aligned Benchmarks ausgenommen ist  . Zu diesen Kriterien zählen insbesondere der wirtschaftliche Anteil aus Aktivitäten mit Kohle, Öl oder Gas sowie die Emissionsintensität bei der Stromerzeugung . Ein positiver oder negativer Ausschluss des Unternehmens kann nur anhand umfassender Umsatzauswertungen und Emissionsdaten festgestellt werden, die im vorliegenden Kontext nicht angegeben sind. <sources-options>{"active":["chunk-1":"500 TEUR"]}</sources-options>
    Die hierfür erforderlichen Informationen zum Status des Unternehmens in Bezug auf die EU Paris-aligned Benchmarks wurden im bereitgestellten Kontext nicht offengelegt."}
    This is an extremely bad example, because it analyses the legal requirements and concludes that data are missing. We want a draft for the report text, or if data are missing, nothing but the literal sentence "The required information is not provided in the context." (in the generation language). Moreover the sources are at the end of the sentence instead of in-text. Finally it talks about "das Unternehmen" even though no company refers to themselves in their own reports as "the company". Typically companies use phrasings like "Company_Name ist vom Paris Agreement ausgeschlossen...". In this case however, the correct response would have been:
    {"datapoint": "<h2>E1-1_12 – Angabe zum Ausschluss des Unternehmens aus den EU Paris-aligned Benchmarks</h2>
    <p>Die notwendigen Informationen sind nicht im Kontext enthalten.</p>.",
    "key_gaps":"Important information is missing in the context. Specifically XYZ"}
    </bad_example>

    **Legal Requirements**:
    1. Requirements for the text you have to generate according to ESRS specific rules for this datapoint its topical standard [ESRS ${esrsDatapoint.esrsDisclosureRequirement.dr}](https://xbrl.efrag.org/e-esrs/esrs-set1-2023.html):
        ${esrsDatapoint.datapointId} ${esrsDatapoint.name}
        <requirements>
        ${esrsDatapoint.lawText} 
        </requirements>
        ${esrsDatapoint.footnotes
            ? `<footnotes>
          ${esrsDatapoint.footnotes}
          </footnotes>`
            : ''}
        ${esrsDatapoint.lawTextAR
            ? `<application_requirements>
          ${esrsDatapoint.lawTextAR}
          </application_requirements>`
            : ''}
        ${esrsDatapoint.footnotesAR
            ? `<footnotes_application_requirements>
          ${esrsDatapoint.footnotesAR}
          </footnotes_application_requirements>`
            : ''}
    
    Note: You are only generating the text for one datapoiont. It belongs to a disclosure requirement and there are other datapoints that belong to the same disclosure requirement. Do not include information that belongs to other datapoints in this text. As a reference, here is some related datapoints that have their own texts and thus its contents should NOT occur in the output text here:
    ${otherDatapointsNames}

    **Context**:
    The following context contains information potentially relevant to the datapoint from the company you are reporting for. Use this to inform your generation of the data point:
    <retrieved_context>
    ${linkedChunks.replace(llm_response_util_1.MERGED_CELL_REGEX, '')}
    </retrieved_context>
    ----------
    
    **Final Remark**:
    Before generating the final output: Find relevant facts & keep track of their sources. Language (for both reasoning and output): ${constants_1.LANGUAGE_MAP[generationLanguage]}`;
    }
    improvingFormattingPrompt({ esrsDatapoint, generationLanguage, reportTextGenerationRules, customUserRemark, currentContent, generalCompanyProfile, reportingYear, predatapointGenerationChatCompletionResponse, }) {
        const cleanCustomUserRemark = customUserRemark
            .replace(/<p>/g, '')
            .replace(/<\/p>/g, '')
            .trim();
        return `Here is a json generated by AI. However the AI makes erros in formatting the response. Reproduce this exact json, phrased and styled in the exact same way, except for:
            1. the sources are sometimes formatted in wrong ways. The formatting style has to adhere to this format: <source>["chunk-X", "chunk-Y"]</source>. If there are sources formatted differently, correct them to this formatting. But ensure you dont change the source values, it should be the same as in the AI output.
            2. Sometimes the output reads like an evaluation of available data or assessment of the company. However what is required is a (draft of) a text snippet for the sustainability report of the company. If the output is an evaluation of data or a judgement of the disclosed information, rewrite it to a draft of a text snippet that one can insert into a draft report. E.g. when the text states something like "According to the information available, [company name] has a Group-wide reporting system in place", this is wrong. Correct the phrasing of the entire paragraph to something like "[company name] has a Group-wide reporting system in place...". The same goes for phrasing like "is not described in the context provided" or "according to the context...". Remove those, just write the text snippe. Ensure that the output never contains text about the availability of documents and information, as this obviously would never be printed in a sustainability report. If the company name is present, use it in third person. An exception to this applies if the text has at the very and a separate paragraph discussing missing information. Leave that.
            3. Remove any mention of missing data. Ensure that the output remains centered on the identification and structuring of required data points based on the information that is available, not what is not avaiable.
            4. The text should be written from the perspective of the company. Phrasings like "The company does XXX" or "Die Gesellschaft hat XXX" are usually wrong. Companies usually would write "Example_Company_Name does XXX" (with their own company name).
            5. If there is no information given, just state 'there is no relevant information provided'.
            6. Sometimes the text starts with empty lines or there are double empty lines. Remove those. We never want extra empty lines or breaks.
            7. The title should start with the datapoint ID (e.g. 'SX.XXX-XX-0X', not ESRS). ${esrsDatapoint.datapointId.includes('MDR')
            ? 'The subtitles should also have their corresponding IDs (e.g. SX.XXX-X_0X) as they are described by lawtexts & application requirements.'
            : 'The subtitles should not use HTML formatting for subheadings h3 etc, only the main title. Otherwise keep the html formatting.'}
            8. ${cleanCustomUserRemark !== '' && cleanCustomUserRemark.length > 2 ? `**USER INSTRUCTION (HIGHEST PRIORITY)**:\n${cleanCustomUserRemark}\n\nYou MUST incorporate this instruction when generating the datapoint text.` : ''}
            8.1: ${currentContent !== '' ? `**EXISTING CONTENT**:\n${currentContent}\n\nThe datapoint already contains the text above. The output you generate will overwrite it. The user is likely requesting either:\n- Quality improvements to this existing text\n- Specific modifications based on their instruction above\n\nYour response should build upon this content while addressing the user's needs.` : ''}            


            ${reportTextGenerationRules
            ? `Here is the general reporting rules for the company: ${reportTextGenerationRules}`
            : ''}
            9. ${reportingYear ? `The current reporting year is ${reportingYear}.` : ''}
            10. ${reportTextGenerationRules !== '' ? `**Content Generation Rules**:\n${reportTextGenerationRules} \n Prioritize strictly adhering to these rules.` : ''}
            11. ${cleanCustomUserRemark !== '' && cleanCustomUserRemark.length > 2 ? `**Additional User Remark (High Priority - CONSIDER THIS WHEN GENERATING THE DATAPOINT)**:\n${cleanCustomUserRemark}` : ''}
            12. ${!!generalCompanyProfile && `**General Company Profile**:\n${generalCompanyProfile}\n\nThis is the general company profile. Use this information to inform your generation of the data point. In particular focus on company structure and which year is being reported for.`}


                **Legal Requirements** according to which the DP should be generated
              Requirements for the text you have to generate according to ESRS specific rules for this datapoint its topical standard [ESRS ${esrsDatapoint.esrsDisclosureRequirement.dr}](https://xbrl.efrag.org/e-esrs/esrs-set1-2023.html):
                  ${esrsDatapoint.datapointId} ${esrsDatapoint.name}
                  <requirements>
                  ${esrsDatapoint.lawText} 
                  </requirements>
                  ${esrsDatapoint.footnotes
            ? `<footnotes>
                    ${esrsDatapoint.footnotes}
                    </footnotes>`
            : ''}
                  ${esrsDatapoint.lawTextAR
            ? `<application_requirements>
                    ${esrsDatapoint.lawTextAR}
                    </application_requirements>`
            : ''}
                  ${esrsDatapoint.footnotesAR
            ? `<footnotes_application_requirements>
                    ${esrsDatapoint.footnotesAR}
                    </footnotes_application_requirements>`
            : ''}

            unformatted version: ${predatapointGenerationChatCompletionResponse}
            Language (for both reasoning and output): ${constants_1.LANGUAGE_MAP[generationLanguage]}

            Generate below a new json in this format: {"datapoint":"<h3>${esrsDatapoint.datapointId} - ${esrsDatapoint.name}<h3><p>[...]..."}.`;
    }
};
exports.NormalDpPromptService = NormalDpPromptService;
exports.NormalDpPromptService = NormalDpPromptService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], NormalDpPromptService);
//# sourceMappingURL=datapoint-generation-prompts.service.js.map