import { Injectable, Logger } from '@nestjs/common';
import { <PERSON><PERSON> } from '@nestjs/schedule';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';
import { InjectRepository } from '@nestjs/typeorm';
import { isDevelopment } from '../env-helper';
import { In, LessThan, Repository } from 'typeorm';
import { Document, DocumentStatus } from '../document/entities/document.entity';
import { JobProcessor, JobQueue } from 'src/types/jobs';

@Injectable()
export class CronService {
  constructor(
    @InjectQueue(JobProcessor.ChunkExtraction)
    private readonly chunkExtractionQueue: Queue,
    @InjectQueue(JobProcessor.ChunkDpLinking)
    private readonly chunkLinkingQueue: Queue,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>
  ) {}

  private readonly logger = new Logger(CronService.name);

  @Cron('*/10 * * * *') // every 10 minutes
  async checkUnprocessedDocuments() {
    this.logger.log('Checking for unprocessed documents...');

    const includedStatuses = [
      DocumentStatus.NotProcessed,
      DocumentStatus.ExtractingData,
      DocumentStatus.DataExtractionFinished,
      DocumentStatus.LinkingData,
    ];

    const unprocessedDocuments = await this.documentRepository.find({
      where: {
        status: In(includedStatuses),
        createdAt: LessThan(new Date(Date.now() - 10 * 60 * 1000)), // 10 minutes ago
      },
    });

    for (const document of unprocessedDocuments) {
      this.logger.log(`Processing document: ${document.id}`);

      try {
        switch (document.status) {
          case DocumentStatus.NotProcessed:
          case DocumentStatus.ExtractingData:
            this.logger.log(
              `Adding document to extraction queue: ${document.id}`
            );
            await this.chunkExtractionQueue.add(
              JobQueue.ChunkExtract,
              {
                documentId: document.id,
              },
              {
                jobId: `chunkExtraction-${document.id}`,
                attempts: 5,
                backoff: {
                  type: 'exponential',
                  delay: 5000,
                },
                removeOnComplete: isDevelopment ? false : true,
              }
            );
            this.documentRepository.update(document.id, {
              status: DocumentStatus.QueuedForExtraction,
            });
            break;

          case DocumentStatus.DataExtractionFinished:
          case DocumentStatus.LinkingData:
            this.logger.log(`Adding document to linking queue: ${document.id}`);
            // await this.chunkLinkingQueue.add(
            //   JobQueue.ChunkDpLink,
            //   {
            //     documentId: document.id,
            //   },
            //   {
            //     jobId: `chunkLinking-${document.id}`,
            //     attempts: 5,
            //     backoff: {
            //       type: 'exponential',
            //       delay: 5000,
            //     },
            //     removeOnComplete: isDevelopment ? false : true,
            //   }
            // );
            this.documentRepository.update(document.id, {
              status: DocumentStatus.LinkingDataFinished,
            });
            break;

          default:
            this.logger.log(
              `Invalid status for document: ${document.id}: ${document.status}`
            );
            break;
        }
      } catch (error) {
        this.logger.error(error);
      }
    }
  }
}
