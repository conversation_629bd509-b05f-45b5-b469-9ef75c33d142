import { Repository } from 'typeorm';
import { DatapointRequestData, DatapointRequestWithDocumentCount } from './entities/datapoint-request.dto';
import { DatapointQueueStatus, DatapointRequest, DatapointRequestStatus } from 'src/datapoint/entities/datapoint-request.entity';
import { PromptService } from 'src/prompts/prompts.service';
import { ProjectService } from 'src/project/project.service';
import { Comment } from 'src/project/entities/comment.entity';
import { DocumentChunk } from 'src/document/entities/document-chunk.entity';
import { UsersService } from 'src/users/users.service';
import { User } from 'src/users/entities/user.entity';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { DatapointDocumentChunk } from 'src/datapoint-document-chunk/entities/datapoint-document-chunk.entity';
import { MDRPromptService } from 'src/prompts/mdr-prompts.service';
import { NumericsPromptService } from 'src/prompts/numerics-prompts.service';
import { NormalDpPromptService } from 'src/prompts/datapoint-generation-prompts.service';
import { TablePromptService } from 'src/prompts/table-prompts.service';
import { ESRSDatapoint } from './entities/esrs-datapoint.entity';
import { Document } from 'src/document/entities/document.entity';
import { ESRSTopic } from 'src/knowledge-base/entities/esrs-topic.entity';
import { DatapointGeneration, datapointGenerationStatus } from './entities/datapoint-generation.entity';
import { ESRSTopicDatapoint } from 'src/knowledge-base/entities/esrs-topic-datapoint.entity';
import { LlmRateLimiterService } from 'src/llm-rate-limiter/llm-rate-limiter.service';
export declare class DatapointRequestService {
    private readonly datapointRequestRepository;
    private readonly datapointDocumentChunkMapRepository;
    private readonly esrsDatapointRepository;
    private readonly esrsTopicDatapointRepository;
    private readonly datapointGenerationRepository;
    private readonly documentRepository;
    private readonly promptService;
    private readonly mdrPromptService;
    private readonly NormalDpPromptService;
    private readonly tablePromptService;
    private readonly datapointPromptService;
    private readonly projectService;
    private readonly userService;
    private readonly workspaceService;
    private readonly llmRateLimiterService;
    constructor(datapointRequestRepository: Repository<DatapointRequest>, datapointDocumentChunkMapRepository: Repository<DatapointDocumentChunk>, esrsDatapointRepository: Repository<ESRSDatapoint>, esrsTopicDatapointRepository: Repository<ESRSTopicDatapoint>, datapointGenerationRepository: Repository<DatapointGeneration>, documentRepository: Repository<Document>, promptService: PromptService, mdrPromptService: MDRPromptService, NormalDpPromptService: NormalDpPromptService, tablePromptService: TablePromptService, datapointPromptService: NumericsPromptService, projectService: ProjectService, userService: UsersService, workspaceService: WorkspaceService, llmRateLimiterService: LlmRateLimiterService);
    private readonly logger;
    private readonly MAX_DATAPOINT_GENERATION_DOCUMENT_CHUNKS_LENGHT;
    private readonly THRESHOLD_DOCUMENT_CHUNK_LENGTH_UNTIL_REDUCING;
    private readonly MAX_CHATCONTENT_MESSAGE_LENGTH;
    private isNumericDataPoint;
    findById(datapointRequestId: string): Promise<DatapointRequest>;
    findDatapointWithGenerationsById(datapointRequestId: string): Promise<DatapointRequest>;
    findDatapointGenerationWithId(datapointGenerationId: string): Promise<DatapointGeneration>;
    findAllDataPointRequests(dataRequestId: string, userId: string): Promise<DatapointRequestWithDocumentCount[]>;
    loadMaterialTopics(datapointRequestId: string): Promise<any>;
    findData(datapointRequestId: string): Promise<DatapointRequestData>;
    update({ datapointRequestId, updateDatapointRequestPayload, userId, workspaceId, event, }: {
        datapointRequestId: string;
        updateDatapointRequestPayload: Partial<DatapointRequest>;
        userId?: string;
        workspaceId?: string;
        event?: string;
    }): Promise<DatapointRequest>;
    generateHierarchicalListOfTopics({ topicRelations, projectId, material, }: {
        topicRelations: ESRSDatapoint['topicRelations'];
        projectId: string;
        material: boolean;
    }): Promise<ESRSTopic[]>;
    reviewDatapointContentWithAI({ datapointRequestId, userId, workspaceId, }: {
        datapointRequestId: string;
        userId: string;
        workspaceId: string;
    }): Promise<Comment[]>;
    validateDatapointRequestGenerationRightsOrFail({ datapointRequest, userId, }: {
        datapointRequest: DatapointRequest;
        userId: string;
    }): Promise<boolean>;
    generateDatapointContentWithAI({ datapointRequestId, userId, workspaceId, useExistingReportTextForReference, }: {
        datapointRequestId: string;
        userId: string;
        workspaceId: string;
        useExistingReportTextForReference: boolean;
    }): Promise<void>;
    loadDatapointCitations(datapointRequestId: string, citationId: string): Promise<any>;
    updateContentAndReplaceCitation({ datapointRequestId, citationId, index, workspaceId, userId, }: {
        datapointRequestId: string;
        citationId: string;
        index: number;
        workspaceId: string;
        userId: string;
    }): Promise<DatapointRequest>;
    generateDatapointGenerationContextFromLinkedDocumentChunks(datapointRequest: DatapointRequest): Promise<{
        context: string;
        documentChunksIndex: string[];
    }>;
    reduceLinkedDocumentChunkContextToRelevantInformationOnly(datapointRequest: DatapointRequest, documentChunk: DocumentChunk): Promise<string>;
    datapointRequestStatusProcessor(datapointRequest: DatapointRequestData): DatapointRequestStatus;
    updateContentWithMDRText(datapointRequestId: string, mdrText: string): Promise<void>;
    approveDatapointGeneration({ datapointRequestId }: {
        datapointRequestId: any;
    }): Promise<DatapointRequest>;
    updateGenerationStatus({ datapointGenerationId, status, userId, workspaceId, }: {
        datapointGenerationId: string;
        status: datapointGenerationStatus;
        userId: string;
        workspaceId: string;
    }): Promise<{
        content?: string;
        status: datapointGenerationStatus;
        evaluator?: User;
        evaluatedAt?: Date;
    }>;
    findDatapointRequestsExcludingStatus({ dataRequestId, status, }: {
        dataRequestId: string;
        status: DatapointRequestStatus[];
    }): Promise<DatapointRequest[]>;
    findDatapointRequestsByStatus({ dataRequestId, status, }: {
        dataRequestId: string;
        status: DatapointRequestStatus[];
    }): Promise<DatapointRequest[]>;
    updateStatus({ id, status, }: {
        id: string;
        status: DatapointRequestStatus;
    }): Promise<void>;
    updateQueueStatus({ datapointRequestId, queueStatus, }: {
        datapointRequestId: string;
        queueStatus: DatapointQueueStatus | null;
    }): Promise<void>;
    loadDocumentLinks(datapointRequestId: string): Promise<DatapointDocumentChunk[]>;
}
