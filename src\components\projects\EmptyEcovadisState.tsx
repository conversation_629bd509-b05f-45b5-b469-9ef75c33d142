
import React, { useState } from 'react';
import { FileText, ClipboardList, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { UploadQuestionnaireDialog } from '@/components/ecovadis/questionnaire/UploadQuestionnaireDialog';

interface EmptyEcovadisStateProps {
  onUploadQuestionnaire: () => void;
  projectId: string;
}

export function EmptyEcovadisState({ onUploadQuestionnaire, projectId }: EmptyEcovadisStateProps) {
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  
  return (
    <div className="flex flex-col items-center justify-center min-h-[80vh] text-center p-6">
      <div className="flex justify-center space-x-4 mb-4">
        <div className="rounded-full bg-blue-100 p-3">
          <FileText size={24} className="text-blue-600" />
        </div>
        <div className="rounded-full bg-green-100 p-3">
          <ClipboardList size={24} className="text-green-600" />
        </div>
      </div>
      
      <h2 className="text-2xl font-semibold mb-2">No EcoVadis Questionnaire Yet</h2>
      <p className="text-gray-600 mb-8 max-w-md">
        Upload your EcoVadis questionnaire to begin tracking and improving your sustainability performance.
      </p>
      
      <Button variant='ghost' className="flex items-center" onClick={() => setUploadDialogOpen(true)}>
        <Upload className="mr-2 h-5 w-5" />
        Upload EcoVadis Questionnaire
      </Button>
      
      <UploadQuestionnaireDialog
        open={uploadDialogOpen}
        onOpenChange={setUploadDialogOpen}
        projectId={projectId}
        onUploadSuccess={onUploadQuestionnaire}
      />
    </div>
  );
}
