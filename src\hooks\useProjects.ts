
import { fetchProjects, updateProject } from '@/api/projects/projects.api';
import { PROJECTS_QUERY_KEY } from '@/api/apiConstants';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export interface ProjectProgress {
  percentage: number;
  complete: number;
  incomplete: number;
  gaps: number;
}

export interface Project {
  id: string;
  name: string;
  type: string;
  deadline?: Date;
  progress: ProjectProgress;
}

export const useProjects = () => {
  return useQuery({
    queryKey: [PROJECTS_QUERY_KEY],
    queryFn: fetchProjects,
    refetchOnWindowFocus: false,
    refetchInterval: 60000, // Reduced frequent refetching to once per minute
    staleTime: 30000, // Increased stale time to 30 seconds
    retry: 3, // Retry failed requests 3 times
  });
};

export const useUpdateProject = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ projectId, updates }: { projectId: string; updates: any }) => 
      updateProject(projectId, updates),
    onSuccess: () => {
      // Invalidate projects query to refresh the data
      queryClient.invalidateQueries({ queryKey: [PROJECTS_QUERY_KEY] });
      toast.success("Project updated successfully");
    },
    onError: (error: any) => {
      console.error('Error updating project:', error);
      toast.error(error.message || "Failed to update project");
    }
  });
};
