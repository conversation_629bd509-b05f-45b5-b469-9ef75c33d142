import * as React from 'react';

import { parseMarkdown, initMermaid } from '@/lib/markdown-util';

interface MarkdownRendererProps extends React.HTMLAttributes<HTMLDivElement> {
  text: string;
}

const MarkdownRenderer = React.forwardRef<
  HTMLDivElement,
  MarkdownRendererProps
>(({ className, text, ...props }, ref) => {
  initMermaid();

  return (
    <div
      ref={ref}
      className={className + ' markdown-renderer'}
      dangerouslySetInnerHTML={{
        __html: parseMarkdown(text),
      }}
      {...props}
    />
  );
});

MarkdownRenderer.displayName = 'MarkdownRenderer';

export { MarkdownRenderer };
