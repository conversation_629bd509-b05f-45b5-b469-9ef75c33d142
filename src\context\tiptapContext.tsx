import React, { createContext, useContext } from 'react';

interface EditorContextValue {
  content: string;
  setContent: (content: string) => void;
}

const EditorContext = createContext<EditorContextValue | null>(null);

export const useEditorContext = (): EditorContextValue => {
  const context = useContext(EditorContext);
  if (!context) {
    throw new Error(
      'useEditorContext must be used inside an EditorContextProvider'
    );
  }
  return context;
};

interface EditorContextProviderProps {
  content: string;
  setContent: (content: string) => void;
  children: React.ReactNode;
}

export const EditorContextProvider: React.FC<EditorContextProviderProps> = ({
  content,
  setContent,
  children,
}) => {
  return (
    <EditorContext.Provider value={{ content, setContent }}>
      {children}
    </EditorContext.Provider>
  );
};
