// @ts-expect-error TODO look into this later
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { corsHeaders } from '../_shared/cors.ts';
import { authValidator } from '../_shared/authValidator.ts';

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }

  try {

    const { user, error, supabaseClient, response } = await authValidator(req);
    if (error || !supabaseClient) {
      return response;
    }
    

    // Parse request body as an array of link requests
    const linkRequests = await req.json();

    // Validate input
    if (!Array.isArray(linkRequests) || linkRequests.length === 0) {
      return new Response(
        JSON.stringify({ error: 'Invalid input. Expected an array of link requests.' }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const results: any[] = [];

    // Process each link request
    for (const request of linkRequests) {
      const { projectId, optionId, documentId, pages, comment } = request;

      // Validate individual request
      if (!projectId || !optionId || !documentId || !Array.isArray(pages) || pages.length === 0) {
        results.push({
          request,
          status: 'error',
          message: 'Invalid request format. Required: projectId, optionId, documentId, and non-empty pages array'
        });
        continue;
      }

      try {
        // Convert numeric page numbers to strings
        const pageStrings = pages.map(page => page.toString());

        // Fetch document chunks matching the document ID and pages
        const { data: documentChunks, error: chunksFetchError } = await supabaseClient
          .from('document_chunk')
          .select('id')
          .eq('documentId', documentId)
          .in('page', pageStrings);

        if (chunksFetchError) {
          throw new Error(`Error fetching document chunks: ${chunksFetchError.message}`);
        }

        if (!documentChunks || documentChunks.length === 0) {
          results.push({
            request,
            status: 'error',
            message: 'No document chunks found for the specified document and pages'
          });
          continue;
        }

        // Check if answer already exists
        const { data: existingAnswer, error: answerFetchError } = await supabaseClient
          .from('project_ecovadis_answer')
          .select('id')
          .eq('projectId', projectId)
          .eq('optionId', optionId)
          .maybeSingle();

        if (answerFetchError) {
          throw new Error(`Error fetching existing answer: ${answerFetchError.message}`);
        }

        let answerId;

        if (existingAnswer) {
          // Use existing answer
          answerId = existingAnswer.id;
        } else {
          // Create new answer with response="true"
          const { data: newAnswer, error: createAnswerError } = await supabaseClient
            .from('project_ecovadis_answer')
            .insert({
              projectId,
              optionId,
              response: 'true'
            })
            .select('id')
            .single();

          if (createAnswerError) {
            throw new Error(`Error creating new answer: ${createAnswerError.message}`);
          }

          answerId = newAnswer.id;
        }

        // Check for existing links to avoid duplicates
        const { data: existingLinks, error: existingLinksError } = await supabaseClient
          .from('project_ecovadis_linked_document_chunks')
          .select('documentChunkId')
          .eq('answerId', answerId);

        if (existingLinksError) {
          throw new Error(`Error checking existing links: ${existingLinksError.message}`);
        }

        // Filter out document chunks that are already linked
        const existingChunkIds = existingLinks?.map(link => link.documentChunkId) || [];
        const chunksToLink = documentChunks.filter(chunk => !existingChunkIds.includes(chunk.id));

        if (chunksToLink.length > 0) {
          // Create linked document chunks - only set comment on the first chunk
          const linkedChunksData = chunksToLink.map((chunk, index) => ({
            answerId,
            documentChunkId: chunk.id,
            comment: index === 0 ? comment || null : null
          }));

          const { data: linkedChunks, error: linkError } = await supabaseClient
            .from('project_ecovadis_linked_document_chunks')
            .insert(linkedChunksData)
            .select();

          if (linkError) {
            throw new Error(`Error linking document chunks: ${linkError.message}`);
          }

          results.push({ 
            request,
            status: 'success', 
            answerId, 
            linkedChunksCount: linkedChunks.length,
            newLinksCreated: true,
            commentAdded: !!comment
          });
        } else {
          results.push({ 
            request,
            status: 'success', 
            answerId, 
            linkedChunksCount: 0,
            newLinksCreated: false,
            message: 'All document chunks already linked to this answer'
          });
        }
      } catch (error) {
        results.push({ 
          request, 
          status: 'error', 
          message: error.message 
        });
      }
    }

    // Calculate summary statistics
    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;
    const totalChunksLinked = results.reduce((total, result) => 
      total + (result.linkedChunksCount || 0), 0);
    const commentsAdded = results.filter(r => r.status === 'success' && r.commentAdded).length;

    return new Response(
      JSON.stringify({ 
        success: errorCount === 0,
        summary: {
          totalRequestsProcessed: linkRequests.length,
          successCount,
          errorCount,
          totalChunksLinked,
          commentsAdded
        },
        results
      }),
      { 
        status: errorCount === 0 ? 200 : 207, // 207 Multi-Status if there are partial failures
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: 'Unexpected error', details: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});