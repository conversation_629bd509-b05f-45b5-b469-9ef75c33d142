import { OnModuleInit } from '@nestjs/common';
import { Repository } from 'typeorm';
import { DocumentChunk } from 'src/document/entities/document-chunk.entity';
import { Document, DocumentStatus } from 'src/document/entities/document.entity';
import { Workspace } from '../workspace/entities/workspace.entity';
import { DocumentParserService } from './document-parser.service';
import { DatapointDocumentChunk } from 'src/datapoint-document-chunk/entities/datapoint-document-chunk.entity';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { User } from 'src/users/entities/user.entity';
import { Queue } from 'bull';
import { ChatGptService } from 'src/llm/chat-gpt.service';
import { ProjectEcovadisLinkedDocumentChunks } from '../ecovadis/entities/project-ecovadis-linked-document-chunks.entity';
export declare class DocumentService implements OnModuleInit {
    private readonly documentChunkRepository;
    private readonly datapointDocumentChunkRepository;
    private readonly datapointRepository;
    private readonly documentRepository;
    private readonly documentParserService;
    private readonly workspaceService;
    private readonly chatGptService;
    private readonly chunkExtractionQueue;
    private readonly projectEcovadisLinkedDocumentChunksRepository;
    constructor(documentChunkRepository: Repository<DocumentChunk>, datapointDocumentChunkRepository: Repository<DatapointDocumentChunk>, datapointRepository: Repository<DatapointRequest>, documentRepository: Repository<Document>, documentParserService: DocumentParserService, workspaceService: WorkspaceService, chatGptService: ChatGptService, chunkExtractionQueue: Queue, projectEcovadisLinkedDocumentChunksRepository: Repository<ProjectEcovadisLinkedDocumentChunks>);
    private readonly logger;
    private pineconeClient;
    private tokenizer;
    onModuleInit(): Promise<void>;
    getWorkspaceDocumentUploads(workspaceId: Workspace['id']): Promise<any[]>;
    getUniqueDatapointCount(documentchunks: DocumentChunk[]): number;
    findDocumentById(id: Document['id']): Promise<Document>;
    getDocumentData(id: Document['id']): Promise<{
        creator: {
            id: string;
            name: string;
        };
        datapointsCount: number;
        id: string;
        workspaceId: string;
        name: string;
        path: string;
        createdAt: Date;
        status: DocumentStatus;
        workspace: Workspace;
        chunks: DocumentChunk[];
        documentType: string;
        esrsCategory: string[];
        year: number;
        month: number;
        day: number;
        remarks: string;
        createdBy: string;
    }>;
    deleteDocument(id: Document['id'], workspaceId: Workspace['id']): Promise<void>;
    saveDocument({ originalname, path, workspaceId, userId, documentType, esrsCategory, year, month, day, remarks, pageNumbers, answerId, premiumParse, }: {
        originalname: string;
        path: string;
        workspaceId: Workspace['id'];
        userId: User['id'];
        documentType: string;
        esrsCategory: string[];
        year: number;
        month: number | null;
        day: number | null;
        remarks: string;
        pageNumbers?: string;
        answerId?: string;
        premiumParse?: boolean;
    }): Promise<{
        workspaceId: string;
        createdBy: string;
        path: string;
        name: string;
        documentType: string;
        esrsCategory: string[];
        year: number;
        month: number;
        day: number;
        remarks: string;
    } & Document>;
    updateDocumentSettings({ id, workspaceId, userId, documentType, esrsCategory, year, month, day, remarks, }: {
        id: Document['id'];
        workspaceId: Workspace['id'];
        userId: User['id'];
        documentType: string;
        esrsCategory: string[];
        year: number;
        month: number | null;
        day: number | null;
        remarks: string;
    }): Promise<void>;
    extractDocumentChunks(id: Document['id'], premiumMode?: boolean): Promise<void>;
    private countTokens;
    private splitTextIntoChunks;
    private fallbackCharacterBasedSplitting;
    indexChunkToPinecone({ document, chunk, }: {
        document: Document;
        chunk: DocumentChunk;
    }): Promise<void>;
    deleteDocumentFromPinecone(documentId: Document['id']): Promise<void>;
    reindexDocument(documentId: Document['id']): Promise<void>;
    bulkReindexDocuments(options: {
        documentIds?: Document['id'][];
        workspaceId?: Workspace['id'];
    }): Promise<{
        success: string[];
        failed: {
            id: string;
            error: string;
        }[];
    }>;
    findDocumentChunkById(id: DocumentChunk['id']): Promise<DocumentChunk>;
    deleteDocumentChunk({ id, workspaceId, userId, }: {
        id: DocumentChunk['id'];
        workspaceId: Workspace['id'];
        userId: string;
    }): Promise<void>;
    getDocumentChunk(id: DocumentChunk['id']): Promise<DocumentChunk>;
    createDocumentChunkMap({ documentChunkId, datapointRequestId, userId, }: {
        documentChunkId: DocumentChunk['id'];
        datapointRequestId: DatapointRequest['id'];
        userId: string;
    }): Promise<void>;
    updateDocumentChunkMap({ documentChunkId, datapointRequestId, userId, status, }: {
        documentChunkId: DocumentChunk['id'];
        datapointRequestId: DatapointRequest['id'];
        userId: string;
        status: boolean;
    }): Promise<void>;
    bulkUpdateDocumentChunkMap({ documentChunkId, userId, data, }: {
        documentChunkId: DocumentChunk['id'];
        userId: string;
        data: {
            datapointRequestId: DatapointRequest['id'];
            linked: boolean;
        }[];
    }): Promise<void>;
    updateDocumentStatus(documentId: Document['id'], { status }: {
        status: DocumentStatus;
    }): Promise<void>;
    findDocumentChunkByPage(documentId: string, pageNumber: number): Promise<DocumentChunk>;
    saveLinkedDocumentChunk({ documentChunkId, answerId, comment, }: {
        documentChunkId: string;
        answerId: string;
        comment: string;
    }): Promise<{
        documentChunkId: string;
        answerId: string;
        comment: string;
    } & ProjectEcovadisLinkedDocumentChunks>;
}
