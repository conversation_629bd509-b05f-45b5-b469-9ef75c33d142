{"version": 3, "file": "data-request.guard.js", "sourceRoot": "", "sources": ["../../src/data-request/data-request.guard.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAKwB;AACxB,uCAAyC;AACzC,iEAA4D;AAGrD,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAC3B,YACmB,kBAAsC,EACtC,SAAoB;QADpB,uBAAkB,GAAlB,kBAAkB,CAAoB;QACtC,cAAS,GAAT,SAAS,CAAW;IACpC,CAAC;IAEJ,KAAK,CAAC,WAAW,CAAC,OAAyB;QACzC,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC;QACnD,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;QAE7C,MAAM,WAAW,GACf,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAC3D,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;QAEpC,IAAI,OAAO,CAAC,WAAW,KAAK,WAAW,EAAE,CAAC;YACxC,MAAM,IAAI,8BAAqB,CAAC,oCAAoC,CAAC,CAAC;QACxE,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CACpC,aAAa,EACb,OAAO,CAAC,UAAU,EAAE,CACrB,CAAC;QAEF,QAAQ,WAAW,EAAE,CAAC;YACpB,KAAK,gBAAgB;gBACnB,OAAO,IAAI,CAAC,kBAAkB,CAAC,yCAAyC,CACtE;oBACE,WAAW;oBACX,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;iBACxB,CACF,CAAC;YACJ;gBACE,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;CACF,CAAA;AApCY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCAG4B,yCAAkB;QAC3B,gBAAS;GAH5B,gBAAgB,CAoC5B"}