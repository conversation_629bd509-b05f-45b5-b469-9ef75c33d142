
import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

export const EcovadisProjectLoadingState: React.FC = () => {
  return (
    <div className="flex h-[calc(100vh-4rem)]">
      {/* Sidebar loading state */}
      <div className="w-80 border-r p-4 flex flex-col">
        <div className="space-y-2 mb-8">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-32" />
        </div>
        
        <div className="mb-6">
          <Skeleton className="h-2.5 w-full rounded-full mb-1" />
          <div className="flex justify-between text-xs">
            <Skeleton className="h-3 w-8" />
            <Skeleton className="h-3 w-8" />
          </div>
        </div>
        
        <div className="flex-1 overflow-y-auto space-y-4">
          {Array(5).fill(0).map((_, i) => (
            <div key={i}>
              <Skeleton className="h-5 w-full mb-2" />
              <div className="pl-3 space-y-2">
                {Array(Math.floor(Math.random() * 5) + 2).fill(0).map((_, j) => (
                  <Skeleton key={j} className="h-4 w-full" />
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Main content loading state */}
      <div className="flex-1 overflow-auto">
        <div className="border-b p-4">
          <div className="flex justify-between items-center">
            <Skeleton className="h-7 w-72" />
            <div className="flex space-x-2">
              <Skeleton className="h-9 w-28 rounded-md" />
              <Skeleton className="h-9 w-28 rounded-md" />
            </div>
          </div>
        </div>
        
        <div className="p-6">
          <div className="space-y-8">
            <div>
              <div className="flex justify-between items-center mb-2">
                <div className="space-y-1">
                  <Skeleton className="h-7 w-64" />
                  <Skeleton className="h-4 w-96" />
                </div>
                <div className="flex space-x-3">
                  <Skeleton className="h-8 w-24 rounded-md" />
                  <Skeleton className="h-8 w-24 rounded-md" />
                </div>
              </div>
              
              <div className="mt-6 space-y-4">
                <div className="border rounded-md p-4">
                  <Skeleton className="h-5 w-48 mb-3" />
                  <Skeleton className="h-24 w-full" />
                </div>
                
                <div className="border rounded-md p-4">
                  <div className="flex space-x-2 mb-3">
                    {Array(3).fill(0).map((_, i) => (
                      <Skeleton key={i} className="h-8 w-24 rounded-md" />
                    ))}
                  </div>
                  <div className="space-y-3">
                    {Array(4).fill(0).map((_, i) => (
                      <div key={i} className="border p-3 rounded-md">
                        <Skeleton className="h-5 w-full mb-2" />
                        <Skeleton className="h-16 w-full" />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
