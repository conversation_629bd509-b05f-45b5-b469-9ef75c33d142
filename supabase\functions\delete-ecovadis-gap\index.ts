
// @ts-expect-error TODO look into this later
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { authValidator } from "../_shared/authValidator.ts";
import { USER_ROLE } from "../_shared/constants.ts";

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }

  try {
    // Get the request body
    const { gapId } = await req.json();

    if (!gapId) {
      return new Response(JSON.stringify({
        error: 'Gap ID is required'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }

    const { user, error, supabaseClient, response: errResponse } = await authValidator(req);
    
    if (!user || error || !supabaseClient) {
      return errResponse;
    }

    // Check if user is Super Admin
    const userRole = user.user_workspace?.[0]?.role;
    if (userRole !== USER_ROLE.SuperAdmin) {
      return new Response(JSON.stringify({
        error: 'Unauthorized. Only Super Admins can delete gaps.'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 403
      });
    }

    // Delete the gap
    const { error: deleteError } = await supabaseClient
      .from('project_ecovadis_gaps')
      .delete()
      .eq('id', gapId);

    if (deleteError) {
      console.error('Error deleting gap:', deleteError);
      return new Response(JSON.stringify({
        error: 'Failed to delete gap'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Gap deleted successfully'
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});
