"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1721710479086 = void 0;
class SchemaUpdate1721710479086 {
    constructor() {
        this.name = 'SchemaUpdate1721710479086';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "knowledge_base_file_upload_chunk"
       (
           "id"           uuid              NOT NULL DEFAULT uuid_generate_v4(),
           "fileUploadId" uuid              NOT NULL,
           "content"      character varying NOT NULL,
           "embedding"    text,
           CONSTRAINT "PK_29a524a899874616b338fafcdb3" PRIMARY KEY ("id")
       )`);
        await queryRunner.query(`CREATE TABLE "knowledge_base_file_upload"
       (
           "id"   uuid              NOT NULL DEFAULT uuid_generate_v4(),
           "name" character varying NOT NULL,
           "path" character varying NOT NULL,
           CONSTRAINT "PK_d803e5e022ec40aa0979f039814" PRIMARY KEY ("id")
       )`);
        await queryRunner.query(`ALTER TABLE "knowledge_base_file_upload_chunk"
          ADD CONSTRAINT "FK_d906b1920691f35fa454d766439" FOREIGN KEY ("fileUploadId") REFERENCES "file_upload" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "knowledge_base_file_upload_chunk" DROP CONSTRAINT "FK_d906b1920691f35fa454d766439"`);
        await queryRunner.query(`DROP TABLE "knowledge_base_file_upload"`);
        await queryRunner.query(`DROP TABLE "knowledge_base_file_upload_chunk"`);
    }
}
exports.SchemaUpdate1721710479086 = SchemaUpdate1721710479086;
//# sourceMappingURL=1721710479086-schema-update.js.map