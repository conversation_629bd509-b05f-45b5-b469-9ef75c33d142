import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1729019561528 implements MigrationInterface {
  name = 'SchemaUpdate1729019561528';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "data_request" ALTER COLUMN "content" SET DEFAULT ''`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "data_request" ALTER COLUMN "content" DROP DEFAULT`,
    );
  }
}
