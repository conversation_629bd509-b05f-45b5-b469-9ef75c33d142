// @ts-ignore
import * as XLSX from 'https://esm.sh/xlsx@0.18.5';
import { authValidator } from '../_shared/authValidator.ts';
import { corsHeaders } from '../_shared/cors.ts';
// @ts-ignore
import { encodeBase64 } from "https://deno.land/std/encoding/base64.ts";
// @ts-ignore
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { convertToRanges } from '../_shared/utils.ts';

const DEFAULT_INDICATOR_IMPACT = 'Medium';
const DEFAULT_PARENT_DOCS_ALLOWED = 'Yes';

// Types for database tables
interface EcoVadisTheme {
  id: string;
  title: string;
  description: string;
}

interface EcoVadisQuestion {
  id: string;
  themeId: string;
  questionCode: string;
  indicator: string;
  questionName: string;
  question: string;
}

interface ProjectEcoVadisTheme {
  id: string;
  themeId: string;
  projectId: string;
  impact: string;
}

interface ProjectEcoVadisQuestion {
  id: string;
  questionId: string;
  projectId: string;
  impact: string;
  status: string;
}

interface EcoVadisAnswerOption {
  id: string;
  questionId: string;
  issueTitle: string;
  instructions: string;
}

interface ProjectEcoVadisAnswer {
  id: string;
  projectId: string;
  optionId: string;
  response: string;
}

interface ProjectEcoVadisLinkedDocumentChunk {
  id: number;
  answerId: string;
  documentChunkId: string;
  comment: string;
}

interface DocumentChunk {
  id: string;
  documentId: string;
  page: string;
  content: string;
}

interface Document {
  id: string;
  name: string;
  path: string;
}

interface User {
  id: string;
  name: string;
  email: string;
}

serve(async (req) => {

  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }

  try {
    // Parse the request body to get the projectId
    const { projectId } = await req.json();

    if (!projectId) {
      return new Response(
        JSON.stringify({ error: 'Project ID is required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const { error, supabaseClient, response } = await authValidator(req);
    if (error || !supabaseClient) {
      return response;
    }

    // Fetch project data to verify it exists and is an EcoVadis project
    const { data: projectData, error: projectError } = await supabaseClient
      .from('project')
      .select('*')
      .eq('id', projectId)
      .eq('type', 'EcoVadis')
      .single();

    if (projectError || !projectData) {
      return new Response(
        JSON.stringify({
          error: 'EcoVadis project not found or invalid project type',
          details: projectError
        }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Fetch all required data in parallel for efficiency
    const [
      themesResponse,
      projectThemesResponse,
      questionsResponse,
      projectQuestionsResponse,
      answerOptionsResponse,
      projectAnswersResponse
    ] = await Promise.all([
      supabaseClient.from('ecovadis_theme').select('*'),
      supabaseClient.from('project_ecovadis_theme').select('*').eq('projectId', projectId),
      supabaseClient.from('ecovadis_question').select('*'),
      supabaseClient.from('project_ecovadis_question').select('*').eq('projectId', projectId),
      supabaseClient.from('ecovadis_answer_option').select('*'),
      supabaseClient.from('project_ecovadis_answer').select('*').eq('projectId', projectId)
    ]);



    // Check for errors in any of the responses
    const errors = [
      themesResponse.error,
      projectThemesResponse.error,
      questionsResponse.error,
      projectQuestionsResponse.error,
      answerOptionsResponse.error,
      projectAnswersResponse.error
    ].filter(Boolean);

    if (errors.length > 0) {
      return new Response(
        JSON.stringify({ error: 'Error fetching data', details: errors }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Extract data from responses
    const themes: EcoVadisTheme[] = themesResponse.data || [];
    const projectThemes: ProjectEcoVadisTheme[] = projectThemesResponse.data || [];
    const questions: EcoVadisQuestion[] = questionsResponse.data || [];
    const projectQuestions: ProjectEcoVadisQuestion[] = projectQuestionsResponse.data || [];
    const answerOptions: EcoVadisAnswerOption[] = answerOptionsResponse.data || [];
    const projectAnswers: ProjectEcoVadisAnswer[] = projectAnswersResponse.data || [];

    // Validate that we have the minimum required data to generate a meaningful questionnaire
    if (themes.length === 0) {
      return new Response(
        JSON.stringify({ error: 'No EcoVadis themes found in the database' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    if (questions.length === 0) {
      return new Response(
        JSON.stringify({ error: 'No EcoVadis questions found in the database' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get all answer IDs to fetch linked document chunks
    const answerIds = projectAnswers.map(answer => answer.id).filter(Boolean);

    let documentChunks: DocumentChunk[] = [];
    let linkedDocumentChunks: ProjectEcoVadisLinkedDocumentChunk[] = [];

    if (answerIds.length > 0) {
      // Batch answerIds to handle potential large datasets
      const batchSize = 500; // Conservative batch size to stay under 1000 result limit

      for (let i = 0; i < answerIds.length; i += batchSize) {
        const batchAnswerIds = answerIds.slice(i, i + batchSize);

        const { data: chunksData, error: chunksError } = await supabaseClient
          .from('project_ecovadis_linked_document_chunks')
          .select(`
            id,
            answerId,
            documentChunkId,
            comment,
            document_chunk!inner(
              id,
              documentId,
              page
            )
          `)
          .in('answerId', batchAnswerIds);

        if (chunksError) {
          return new Response(
            JSON.stringify({ error: 'Error fetching document chunks', details: chunksError }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
          );
        }

        // Extract document chunks from the nested structure
        const linkedChunks = chunksData?.map(item => ({
          id: item.id,
          answerId: item.answerId,
          documentChunkId: item.document_chunk?.id,
          comment: item.comment
        })) || [];
        linkedDocumentChunks.push(...linkedChunks);
        const batchChunks = chunksData?.map(item => item.document_chunk).filter(Boolean) || [];
        documentChunks.push(...batchChunks);
      }
    }

    // Fetch document chunks and their corresponding documents
    let documents: Document[] = [];


    if (documentChunks.length > 0) {
      const documentIds = Array.from(new Set(documentChunks.map(chunk => chunk.documentId).filter(Boolean)));

      if (documentIds.length > 0) {
        const { data: docsData, error: docsError } = await supabaseClient
          .from('document')
          .select('id, name, path')
          .in('id', documentIds);

        if (docsError) {
          return new Response(
            JSON.stringify({ error: 'Error fetching documents', details: docsError }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
          );
        }

        documents = docsData || [];
      }
    }

    // Create lookup maps for easier data access
    const themeMap = new Map(themes.map(theme => [theme.id, theme]));
    const projectThemeMap = new Map(projectThemes.map(pt => [pt.themeId, pt]));
    const projectQuestionMap = new Map(projectQuestions.map(pq => [pq.questionId, pq]));

    // Map document chunks to document names
    const documentChunkMap = new Map(documentChunks.map(chunk => [chunk.id, chunk]));
    const documentMap = new Map(documents.map(doc => [doc.id, doc]));

    // Group answer options by question ID
    const optionsByQuestionId = new Map();
    for (const option of answerOptions) {
      if (!optionsByQuestionId.has(option.questionId)) {
        optionsByQuestionId.set(option.questionId, []);
      }
      optionsByQuestionId.get(option.questionId).push(option);
    }

    // Group linked document chunks by answer ID
    const linkedChunksByAnswerId = new Map();
    for (const chunk of linkedDocumentChunks) {
      if (!linkedChunksByAnswerId.has(chunk.answerId)) {
        linkedChunksByAnswerId.set(chunk.answerId, []);
      }
      linkedChunksByAnswerId.get(chunk.answerId).push(chunk);
    }

    // Group answers by option ID
    const answersByOptionId = new Map();
    for (const answer of projectAnswers) {
      if (answer.optionId) {
        answersByOptionId.set(answer.optionId, answer);
      }
    }

    // Define headers for the Excel file
    const headers = [
      'Theme',
      'Theme\'s impact on overall score',
      'Management indicator',
      'Indicator\'s impact on theme score',
      'Question name',
      'Question code',
      'Impact on score',
      'Can documents from your parent company or group be credited for this question?',
      'Question',
      'Select the option(s) applicable to your company by entering "yes" in column M',
      'Examples of evidence to support your answer, including supporting documents',
      'Who in your company is assigned to this question?',
      'Enter "yes" for options applicable to your company; otherwise leave as blank',
      'Provide name(s) of your supporting document(s) and/or describe the evidence to support your answers',
      'Provide supporting values or additional comments',
      'Provide page # to evidence in supporting document(s)'
    ];

    // Prepare data for the Excel file
    const rows = [headers]; // First row is headers

    // Group questions by theme for better organization
    const questionsByTheme = new Map();
    for (const question of questions) {
      if (!questionsByTheme.has(question.themeId)) {
        questionsByTheme.set(question.themeId, []);
      }
      questionsByTheme.get(question.themeId).push(question);
    }

    // Sort themes alphabetically for consistency
    const sortedThemes = [...themes].sort((a, b) => {
      return (a.title || '').localeCompare(b.title || '');
    });

    // Process each theme and its questions
    for (const theme of sortedThemes) {
      // Skip themes with no ID
      if (!theme.id) continue;

      const themeQuestions = questionsByTheme.get(theme.id) || [];

      // Sort questions by code for consistency and readability
      const sortedQuestions = [...themeQuestions].sort((a, b) => {
        return (a.questionCode || '').localeCompare(b.questionCode || '');
      });

      const projectTheme = projectThemeMap.get(theme.id);
      const themeImpact = projectTheme ? projectTheme.impact : 'Medium';

      // Skip themes with no questions
      if (sortedQuestions.length === 0) continue;

      for (const question of sortedQuestions) {
        // Skip questions with no ID
        if (!question.id) continue;

        const projectQuestion = projectQuestionMap.get(question.id);
        const questionImpact = projectQuestion ? projectQuestion.impact : DEFAULT_INDICATOR_IMPACT;

        // Get options for this question
        const options = optionsByQuestionId.get(question.id) || [];

        // Process each option as a separate row
        for (let i = 0; i < options.length; i++) {
          const option = options[i];
          if (!option.id) continue; // Skip options with no ID

          // Check if option is answered
          const isAnswered = answersByOptionId.has(option.id);
          const optionResponse = isAnswered ? 'yes' : '';

          // If option is answered, process linked documents
          if (isAnswered) {
            const answer = answersByOptionId.get(option.id);
            const linkedChunks = linkedChunksByAnswerId.get(answer.id) || [];

            // First add a row for the option itself, without document details
            rows.push(createRow(
              theme, themeImpact, question, questionImpact,
              i === 0, // Only include question details for first option
              option.issueTitle || '',
              option.instructions || '',
              optionResponse,
              '', '', '' // Empty document info for now
            ));

            // Group linked chunks by document
            const documentGroups = new Map(); // Map of documentId -> {name, pages, comments}

            for (const chunk of linkedChunks) {
              const documentChunk = documentChunkMap.get(chunk.documentChunkId);
              if (documentChunk) {
                const document = documentMap.get(documentChunk.documentId);
                if (document) {
                  // Create or update document group
                  if (!documentGroups.has(document.id)) {
                    documentGroups.set(document.id, {
                      name: document.name,
                      pages: [],
                      comments: []
                    });
                  }

                  // Add page number (as number) to pages array
                  const pageNum = parseInt(documentChunk.page, 10);
                  if (!isNaN(pageNum)) {
                    documentGroups.get(document.id).pages.push(pageNum);
                  } else if (documentChunk.page) {
                    // If not a number but not empty, add as is
                    documentGroups.get(document.id).pages.push(documentChunk.page);
                  }

                  // Add comment if not empty
                  if (chunk.comment) {
                    documentGroups.get(document.id).comments.push(chunk.comment);
                  }
                }
              }
            }

            // Add a row for each unique document with consolidated page ranges and comments
            for (const [_, docGroup] of documentGroups) {
              // Convert page numbers to ranges using the existing function
              let pageRanges = '';
              if (docGroup.pages.length > 0) {
                // First, sort the page numbers
                const sortedPages = docGroup.pages
                  .filter(page => typeof page === 'number')
                  .sort((a, b) => a - b);

                // Convert to ranges and join
                if (sortedPages.length > 0) {
                  const ranges = convertToRanges(sortedPages);
                  pageRanges = ranges.join(', ');
                } else {
                  // Handle case where pages might be strings
                  pageRanges = docGroup.pages.join(', ');
                }
              }

              // Concatenate comments
              const combinedComments = docGroup.comments.join('\n');

              // Add a row for this document with consolidated info
              rows.push(createRow(
                theme, themeImpact, question, questionImpact,
                false, // Never include question details for document rows
                '', '', '', // Empty option info
                docGroup.name,
                combinedComments,
                pageRanges
              ));
            }

            // If there are no linked documents, make sure the option row is still added
            if (documentGroups.size === 0) {
              rows.push(createRow(
                theme, themeImpact, question, questionImpact,
                i === 0, // Only include question details for first option
                option.issueTitle || '',
                option.instructions || '',
                optionResponse,
                '', '', '' // Empty document info
              ));
            }

            // Skip to next option since we've already added rows
            continue;
          }

          // If not answered, add a single row for the option
          rows.push(createRow(
            theme, themeImpact, question, questionImpact,
            i === 0, // Only include question details for first option
            option.issueTitle || '',
            option.instructions || '',
            optionResponse,
            '', '', '' // Empty document info
          ));
        }

        // If there are no options, add at least one row for the question
        if (options.length === 0) {
          rows.push(createRow(
            theme, themeImpact, question, questionImpact,
            true, // Include question details
            '', '', '', '', '', ''
          ));
        }
      }
    }

    // Check if we have any data rows besides the header
    if (rows.length <= 1) {
      return new Response(
        JSON.stringify({ error: 'No questionnaire data available to generate Excel file' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    try {
      // Create Excel workbook
      const workbook = XLSX.utils.book_new();

      // Create worksheet from the rows
      const worksheet = XLSX.utils.aoa_to_sheet(rows);

      // Set column widths
      worksheet['!cols'] = [
        { wch: 20 },  // A: Theme
        { wch: 15 },  // B: Theme's impact
        { wch: 20 },  // C: Management indicator
        { wch: 20 },  // D: Indicator's impact
        { wch: 30 },  // E: Question name
        { wch: 15 },  // F: Question code
        { wch: 15 },  // G: Impact on score
        { wch: 25 },  // H: Can parent docs be used
        { wch: 50 },  // I: Question
        { wch: 40 },  // J: Select options
        { wch: 50 },  // K: Examples of evidence
        { wch: 25 },  // L: Assigned to
        { wch: 15 },  // M: Applicable options
        { wch: 50 },  // N: Supporting documents
        { wch: 30 },  // O: Additional comments
        { wch: 15 }   // P: Page numbers
      ];

      // Apply styling to header row
      for (let i = 0; i < headers.length; i++) {
        const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });
        if (!worksheet[cellRef]) worksheet[cellRef] = { v: headers[i] };
        worksheet[cellRef].s = {
          font: { bold: true, color: { rgb: "FFFFFF" } },
          fill: { fgColor: { rgb: "4F81BD" } },
          alignment: { horizontal: "center", vertical: "center", wrapText: true }
        };
      }

      // Set row heights for better readability
      worksheet['!rows'] = [{ hpt: 30 }]; // Header row height

      // Apply styling to data rows for better readability
      for (let r = 1; r < rows.length; r++) {
        // Apply alternate row coloring for better readability
        const rowStyle = {
          alignment: { vertical: "top", wrapText: true }
        };

        // Add subtle background color for even rows
        if (r % 2 === 0) {
          rowStyle['fill'] = { fgColor: { rgb: "F2F2F2" } };
        }

        for (let c = 0; c < headers.length; c++) {
          const cellRef = XLSX.utils.encode_cell({ r, c });
          if (worksheet[cellRef]) {
            worksheet[cellRef].s = rowStyle;
          }
        }
      }

      // Add the worksheet to the workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Questionnaire');

      // Add metadata to the workbook
      workbook.Props = {
        Title: `EcoVadis Questionnaire - ${projectData.name}`,
        Subject: "EcoVadis Assessment",
        Author: "Generated by Supabase Edge Function",
        CreatedDate: new Date()
      };

      // Generate Excel file as buffer with compression for better performance
      const excelBuffer = XLSX.write(workbook, {
        type: 'buffer',
        bookType: 'xlsx',
        compression: true
      });

      // Generate a more user-friendly filename with project name and date
      const safeProjectName = projectData.name.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 30);
      const dateString = new Date().toISOString().split('T')[0];
      const filename = `EcoVadis_Questionnaire_${safeProjectName}_${dateString}.xlsx`;


      // Return the Excel file as response
      return new Response(JSON.stringify({
        data: encodeBase64(excelBuffer),
        filename: filename
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('Error generating Excel file:', error);
      return new Response(
        JSON.stringify({
          error: 'Failed to generate Excel file buffer',
          message: error.message
        }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

  } catch (error) {
    console.error('Error generating EcoVadis questionnaire:', error);

    // Determine appropriate status code based on error
    let statusCode = 500;
    if (error.message && (
      error.message.includes('not found') ||
      error.message.includes('does not exist')
    )) {
      statusCode = 404;
    } else if (error.message && (
      error.message.includes('permission denied') ||
      error.message.includes('not authorized')
    )) {
      statusCode = 403;
    } else if (error.message && (
      error.message.includes('invalid input') ||
      error.message.includes('validation failed')
    )) {
      statusCode = 400;
    }

    // Sanitize error message for sensitive information
    const sanitizedMessage = error.message
      ? error.message.replace(/Bearer [A-Za-z0-9\-_\.]+/g, 'Bearer [REDACTED]')
      : 'Unknown error';

    return new Response(
      JSON.stringify({
        error: 'Failed to generate EcoVadis questionnaire',
        message: sanitizedMessage,
        type: error.name || 'Error',
        // Only include stack in development environments
        // @ts-ignore
        ...(Deno.env.get('ENVIRONMENT') === 'development' ? { stack: error.stack } : {})
      }),
      { status: statusCode, headers: { 'Content-Type': 'application/json' } }
    );
  }
})

// Helper function to create a row with the right structure
function createRow(theme, themeImpact, question, questionImpact, includeQuestion,
  optionText, optionInstruction, optionResponse,
  document, comment, page) {
  return [
    includeQuestion ? (theme.title || '') : '',                  // Theme
    includeQuestion ? themeImpact : '',                          // Theme's impact on overall score
    includeQuestion ? (question.indicator || '') : '',           // Management indicator
    includeQuestion ? DEFAULT_INDICATOR_IMPACT : '',             // Indicator's impact on theme score
    includeQuestion ? (question.questionName || '') : '',        // Question name
    includeQuestion ? (question.questionCode || '') : '',        // Question code
    includeQuestion ? questionImpact : '',                       // Impact on score
    includeQuestion ? DEFAULT_PARENT_DOCS_ALLOWED : '',          // Can parent company docs be credited
    includeQuestion ? (question.question || '') : '',            // Question text
    optionText,                                                  // Select option
    optionInstruction,                                           // Example of evidence
    '',                                                          // Who is assigned
    optionResponse,                                              // Enter "yes" for applicable option
    document,                                                    // Supporting document
    comment,                                                     // Additional comment
    page                                                         // Page number
  ];
}
