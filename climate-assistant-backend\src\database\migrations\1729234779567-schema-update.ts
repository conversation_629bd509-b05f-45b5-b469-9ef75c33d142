import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1729234779567 implements MigrationInterface {
  name = 'SchemaUpdate1729234779567';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `update esrs_datapoint as "ed1"
set "esrsDisclosureRequirementId"= "edr"."id"
from esrs_datapoint as "ed" join esrs_disclosure_requirement as "edr" on ed."datapointId" like edr.dr || '\_%'
where ed1.id = ed.id;
      `,
    );
    await queryRunner.query(
      `
      UPDATE esrs_datapoint
SET "esrsDisclosureRequirementId" = CASE "datapointId"
    WHEN 'E1.MDR-P_01-06' THEN 5
    WHEN 'E1.MDR-P_07-08' THEN 5
    WHEN 'E1.MDR-A_01-12' THEN 6
    WHEN 'E1.MDR-A_13-14' THEN 6
    WHEN 'E1.MDR-T_01-13' THEN 7
    WHEN 'E1.MDR-T_14-19' THEN 7
    WHEN 'E2.MDR-P_01-06' THEN 14
    WHEN 'E2.MDR-P_07-08' THEN 14
    WHEN 'E2.MDR-A_01-12' THEN 15
    WHEN 'E2.MDR-A_13-14' THEN 15
    WHEN 'E2.MDR-T_01-13' THEN 16
    WHEN 'E2.MDR-T_14-19' THEN 16
    WHEN 'E3.MDR-P_01-06' THEN 21
    WHEN 'E3.MDR-P_07-08' THEN 21
    WHEN 'E3.MDR-A_01-12' THEN 22
    WHEN 'E3.MDR-A_13-14' THEN 22
    WHEN 'E3.MDR-T_01-13' THEN 23
    WHEN 'E3.MDR-T_14-19' THEN 23
    WHEN 'E4.MDR-P_01-06' THEN 29
    WHEN 'E4.MDR-P_07-08' THEN 29
    WHEN 'E4.MDR-A_01-12' THEN 30
    WHEN 'E4.MDR-A_13-14' THEN 30
    WHEN 'E4.MDR-T_01-13' THEN 31
    WHEN 'E4.MDR-T_14-19' THEN 31
    WHEN 'E5.MDR-P_01-06' THEN 35
    WHEN 'E5.MDR-P_07-08' THEN 35
    WHEN 'E5.MDR-A_01-12' THEN 36
    WHEN 'E5.MDR-A_13-14' THEN 36
    WHEN 'E5.MDR-T_01-13' THEN 37
    WHEN 'E5.MDR-T_14-19' THEN 37
    WHEN 'S1.MDR-P_01-06' THEN 42
    WHEN 'S1.MDR-P_07-08' THEN 42
    WHEN 'S1.MDR-A_01-12' THEN 45
    WHEN 'S1.MDR-A_13-14' THEN 45
    WHEN 'S1.MDR-T_01-13' THEN 46
    WHEN 'S1.MDR-T_14-19' THEN 46
    WHEN 'S2.MDR-P_01-06' THEN 60
    WHEN 'S2.MDR-P_07-08' THEN 60
    WHEN 'S2.MDR-A_01-12' THEN 63
    WHEN 'S2.MDR-A_13-14' THEN 63
    WHEN 'S2.MDR-T_01-13' THEN 64
    WHEN 'S2.MDR-T_14-19' THEN 64
    WHEN 'S3.MDR-P_01-06' THEN 66
    WHEN 'S3.MDR-P_07-08' THEN 66
    WHEN 'S3.MDR-A_01-12' THEN 69
    WHEN 'S3.MDR-A_13-14' THEN 69
    WHEN 'S3.MDR-T_01-13' THEN 70
    WHEN 'S3.MDR-T_14-19' THEN 70
    WHEN 'S4.MDR-P_01-06' THEN 72
    WHEN 'S4.MDR-P_07-08' THEN 72
    WHEN 'S4.MDR-A_01-12' THEN 75
    WHEN 'S4.MDR-A_13-14' THEN 75
    WHEN 'S4.MDR-T_01-13' THEN 76
    WHEN 'S4.MDR-T_14-19' THEN 76
    WHEN 'G1.MDR-P_01-06' THEN 78
    WHEN 'G1.MDR-P_07-08' THEN 79
    WHEN 'G1.MDR-A_01-12' THEN 81
    WHEN 'G1.MDR-A_13-14' THEN 82
END
WHERE "datapointId" IN ('E1.MDR-P_01-06', 'E1.MDR-P_07-08', 'E1.MDR-A_01-12', 'E1.MDR-A_13-14', 'E1.MDR-T_01-13', 'E1.MDR-T_14-19',
                      'E2.MDR-P_01-06', 'E2.MDR-P_07-08', 'E2.MDR-A_01-12', 'E2.MDR-A_13-14', 'E2.MDR-T_01-13', 'E2.MDR-T_14-19',
                      'E3.MDR-P_01-06', 'E3.MDR-P_07-08', 'E3.MDR-A_01-12', 'E3.MDR-A_13-14', 'E3.MDR-T_01-13', 'E3.MDR-T_14-19',
                      'E4.MDR-P_01-06', 'E4.MDR-P_07-08', 'E4.MDR-A_01-12', 'E4.MDR-A_13-14', 'E4.MDR-T_01-13', 'E4.MDR-T_14-19',
                      'E5.MDR-P_01-06', 'E5.MDR-P_07-08', 'E5.MDR-A_01-12', 'E5.MDR-A_13-14', 'E5.MDR-T_01-13', 'E5.MDR-T_14-19',
                      'S1.MDR-P_01-06', 'S1.MDR-P_07-08', 'S1.MDR-A_01-12', 'S1.MDR-A_13-14', 'S1.MDR-T_01-13', 'S1.MDR-T_14-19',
                      'S2.MDR-P_01-06', 'S2.MDR-P_07-08', 'S2.MDR-A_01-12', 'S2.MDR-A_13-14', 'S2.MDR-T_01-13', 'S2.MDR-T_14-19',
                      'S3.MDR-P_01-06', 'S3.MDR-P_07-08', 'S3.MDR-A_01-12', 'S3.MDR-A_13-14', 'S3.MDR-T_01-13', 'S3.MDR-T_14-19',
                      'S4.MDR-P_01-06', 'S4.MDR-P_07-08', 'S4.MDR-A_01-12', 'S4.MDR-A_13-14', 'S4.MDR-T_01-13', 'S4.MDR-T_14-19',
                      'G1.MDR-P_01-06', 'G1.MDR-P_07-08', 'G1.MDR-A_01-12', 'G1.MDR-A_13-14');
      
      `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `update esrs_datapoint set "esrsDisclosureRequirementId" = null`,
    );
  }
}
