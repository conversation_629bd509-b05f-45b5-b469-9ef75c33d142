import React, { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';

import PageLoading from './loading';

import { Switch } from '@/components/ui/switch';
import { MainLayout } from '@/components/MainLayout';
import { Label } from '@/components/ui/label';
import { MemberSelector } from '@/components/dashboard/MemberSelector';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { InfoboxCompletionSteps } from '@/components/dashboard/InfoboxCompletionSteps';
import { ReportText } from '@/components/dashboard/ReportText';
import { CommentSection } from '@/components/dashboard/Comments';
import { DueDatePicker } from '@/components/dashboard/DueDate';
import {
  CommentType,
  DatapointRequestStatus,
  DataRequestStatus,
} from '@/types';
import { StatusLabel } from '@/components/ui/status-label';
import { useDataRequest } from '@/hooks/useDataRequest';
import { dataRequestStatuses } from '@/components/DashboardTableConfig';
import { CommentGenerationSection } from '@/components/dashboard/super-admin-tools/CommentGenerations';
import { useAuthentication } from '@/api/authentication/authentication.query';
import { userHasRequiredRole } from '@/lib/utils';
import { USER_ROLE } from '@/constants/workspaceConstants';
import { Button } from '@/components/ui/button';
import { GenerateBulkDatapointsDialog } from '@/components/GenerateBulkDatapointsDialog';
import { ReviewDatapointsDialog } from '@/components/ReviewDatapointsDialog';
import { QUEUE_STATUS } from '@/types/project';
import { fetchAllMembers } from '@/api/workspace-settings/workspace-settings.api';
import { DataRequestProvider } from '@/context/dataRequestContext';
import { Accordion } from '@/components/ui/accordion';
import { DatapointRequestItem } from '@/components/dashboard/DatapointRequestItem';

export const DataRequestDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { user } = useAuthentication();
  const [datapointGenerationInProgress, setDatapointGenerationInProgress] =
    useState(false);
  const [datapointReviewInProgress, setDatapointReviewInProgress] =
    useState(false);
  const [showReviewDialog, setShowReviewDialog] = useState(false);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);
  const [members, setMembers] = useState<
    {
      id: string;
      name: string;
    }[]
  >([]);

  const {
    dataRequest,
    refetchDataRequest,
    updateResponsiblePerson,
    updateDueDate,
    handleReportDataRequestChanged,
    progress,
    handleBulkGenerateDatapoints,
    handleBulkReviewDatapoints,
  } = useDataRequest(id!);

  useEffect(() => {
    fetchAllMembers().then((users) => setMembers(users));
  }, []);

  const isSuperAdmin = useMemo(
    () => userHasRequiredRole([USER_ROLE.SuperAdmin], user),
    [user]
  );

  const datapointsToGenerate = useMemo(() => {
    if (!dataRequest) return [];
    return dataRequest.datapointRequests
      .filter((data) => {
        return (
          data.status === DatapointRequestStatus.NoData &&
          data.documentChunkCount &&
          data.documentChunkCount > 0
        );
      })
      .map((data) => data.id);
  }, [dataRequest]);

  const datapointsToReview = useMemo(() => {
    if (!dataRequest) return [];
    return dataRequest.datapointRequests
      .filter(
        (data) =>
          data.status !== DatapointRequestStatus.NoData &&
          data.status !== DatapointRequestStatus.NotAnswered
      )
      .map((data) => data.id);
  }, [dataRequest]);

  useEffect(() => {
    if (!dataRequest) return;
    setDatapointGenerationInProgress(
      dataRequest.datapointRequests.some(
        (req) => req.queueStatus === QUEUE_STATUS.QueuedForGeneration
      )
    );
    setDatapointReviewInProgress(
      dataRequest.datapointRequests.some(
        (req) => req.queueStatus === QUEUE_STATUS.QueuedForReview
      )
    );
  }, [dataRequest]);

  useEffect(() => {
    setDatapointGenerationInProgress(
      dataRequest?.queueStatus === QUEUE_STATUS.QueuedForGeneration
    );
    setDatapointReviewInProgress(
      dataRequest?.queueStatus === QUEUE_STATUS.QueuedForReview
    );
  }, [dataRequest?.queueStatus]);

  if (!dataRequest) return <PageLoading />;

  return (
    <MainLayout>
      <DataRequestProvider refetchDataRequest={refetchDataRequest}>
        <div className="space-y-5 mb-16 m-4">
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl tracking-wide">
                {dataRequest.disclosureRequirement.dr}:{' '}
                <span className="font-bold">
                  {dataRequest.disclosureRequirement.name}
                </span>
              </h2>
              <div className="flex items-center space-x-2">
                {isSuperAdmin && datapointsToGenerate.length > 0 && (
                  <>
                    <Button
                      className="rounded-full px-4 flex items-center"
                      onClick={() => setShowGenerateDialog(true)}
                      disabled={datapointGenerationInProgress}
                    >
                      {datapointGenerationInProgress
                        ? 'Generating'
                        : 'Generate'}{' '}
                      for {datapointsToGenerate.length} Datapoints
                    </Button>
                    <GenerateBulkDatapointsDialog
                      dataRequestId={dataRequest.id}
                      open={showGenerateDialog}
                      onOpenChange={setShowGenerateDialog}
                      onGenerate={handleBulkGenerateDatapoints}
                      datapointCount={datapointsToGenerate.length}
                      isGenerating={datapointGenerationInProgress}
                      setIsGenerating={setDatapointGenerationInProgress}
                    />
                  </>
                )}
                {isSuperAdmin && datapointsToReview.length > 0 && (
                  <>
                    <Button
                      className="rounded-full px-4 flex items-center"
                      onClick={() => setShowReviewDialog(true)}
                      disabled={datapointReviewInProgress}
                    >
                      {datapointReviewInProgress ? 'Reviewing' : 'Review'} for{' '}
                      {datapointsToReview.length} Datapoints
                    </Button>
                    <ReviewDatapointsDialog
                      dataRequestId={dataRequest.id}
                      open={showReviewDialog}
                      onOpenChange={setShowReviewDialog}
                      onReview={handleBulkReviewDatapoints}
                      datapointCount={datapointsToReview.length}
                      isReviewing={datapointReviewInProgress}
                      setIsReviewing={setDatapointReviewInProgress}
                    />
                  </>
                )}
                <Label
                  className="text-nowrap ml-10"
                  htmlFor={`report-disclosure-${dataRequest.id}`}
                >
                  Report Disclosure Requirement
                </Label>
                <Switch
                  checked={dataRequest.status !== DataRequestStatus.NotAnswered}
                  onCheckedChange={(checked) => {
                    handleReportDataRequestChanged(checked);
                  }}
                  onClick={(e) => e.stopPropagation()}
                  id={`report-disclosure-${dataRequest.id}`}
                />
              </div>
            </div>
            <div className="flex justify-start items-center gap-10">
              <div className="flex items-center space-x-3">
                <Label>Status: </Label>
                <StatusLabel
                  status={dataRequest.status}
                  statuses={dataRequestStatuses}
                />
              </div>
              <div className="flex items-center space-x-3">
                <Label>Responsible: </Label>
                <MemberSelector
                  members={members}
                  action={updateResponsiblePerson}
                  currentUser={dataRequest.responsiblePerson?.id}
                  placeholder="Select User"
                />
              </div>
              <div className="flex items-center space-x-3">
                <Label>Complete Until: </Label>
                <DueDatePicker
                  currentDueDate={dataRequest.dueDate}
                  action={updateDueDate}
                />
              </div>
            </div>
          </div>

          <Tabs defaultValue="information" className="w-full">
            <TabsList className="grid grid-cols-2 gap-1 mb-5 w-fit">
              <TabsTrigger value="information" className="h-8 mb-1">
                Information for Datapoints ({progress()})
              </TabsTrigger>
              <TabsTrigger value="reporttext" className="h-8 mb-1">
                Reporttext for {dataRequest.disclosureRequirement.dr}{' '}
              </TabsTrigger>
            </TabsList>
            <TabsContent value="information" className="space-y-5">
              <h3 className="text-xl mb-2 tracking-wide">
                <span className="font-bold">Information for Datapoints </span>(
                {progress()})
              </h3>

              <InfoboxCompletionSteps variant={'success'} />

              <Accordion type="multiple" className="w-full space-y-3">
                {dataRequest.datapointRequests.map(
                  (datapointRequest, index) => (
                    <DatapointRequestItem
                      key={index}
                      datapointRequest={datapointRequest}
                      projectId={dataRequest.projectId}
                    />
                  )
                )}
              </Accordion>
            </TabsContent>

            <TabsContent value="reporttext">
              <h3 className="text-xl mb-2 tracking-wide">
                <span className="font-bold">
                  Reporttext for {dataRequest.disclosureRequirement.dr}{' '}
                </span>
              </h3>
              <ReportText
                dataRequest={dataRequest}
                content={dataRequest.content}
                members={members}
              />
              {userHasRequiredRole(
                [USER_ROLE.SuperAdmin, USER_ROLE.AiContributor],
                user
              ) && (
                <CommentGenerationSection
                  projectId={dataRequest.projectId}
                  savedComments={dataRequest.commentGenerations}
                  updateCallback={refetchDataRequest}
                />
              )}
              <CommentSection
                savedComments={dataRequest.comments}
                projectId={dataRequest.projectId}
                commentableId={dataRequest.id}
                commentableType={CommentType.DataRequest}
                updateCallback={refetchDataRequest}
              />
            </TabsContent>
          </Tabs>

          {/* Save Button */}
        </div>
      </DataRequestProvider>
    </MainLayout>
  );
};
