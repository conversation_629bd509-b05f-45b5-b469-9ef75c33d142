import { hotjar } from 'react-hotjar';

const HOTJAR_ID = import.meta.env.VITE_HOTJAR_ID;
const IS_PRODUCTION = import.meta.env.VITE_IS_PRODUCTION;

export const HotjarService = {
  init: () => {
    if (IS_PRODUCTION) {
      hotjar.initialize({ id: parseInt(HOTJAR_ID), sv: 6, debug: true });
    }
  },

  identifyUser: async (email: string) => {
    if (hotjar.initialized()) {
      hotjar.identify(email, {});
    }
  },

  // track: (event: AnalyticsEvent) => {
  //   hotjar.event('button-click');
  // }, // no tracking for now
};
