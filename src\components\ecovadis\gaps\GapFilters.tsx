
import { useState } from "react";
import { 
  FileX, 
  Wrench, 
  X, 
  <PERSON>, 
  Check,
  LayoutList,
  FileText,
  Building
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

interface GapFiltersProps {
  onFilterChange: (filters: any) => void;
  activeFilters: any;
  availableThemes?: string[];
  availableProjects?: {id: string, name: string}[];
  users?: Array<{ id: string, name: string }>;
}

export const GapFilters = ({ 
  onFilterChange, 
  activeFilters, 
  availableThemes = [],
  availableProjects = [],
  users = []
}: GapFiltersProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [appliedFilters, setAppliedFilters] = useState<string[]>([]);

  const handleViewTypeChange = (value: string) => {
    onFilterChange({ ...activeFilters, viewType: value });
    if (value !== 'list') {
      addFilterChip('viewType', value, value === 'document' ? 'Document View' : 'List View');
    } else {
      removeFilterChip('viewType');
    }
  };

  const handleAssigneeChange = (value: string) => {
    onFilterChange({ ...activeFilters, assignee: value });
    if (value !== 'all') {
      let label = value;
      if (value === 'me') {
        label = 'Only me';
      } else if (value === 'unassigned') {
        label = 'Unassigned';
      } else {
        // Find the user name for the label
        const user = users.find(u => u.id === value);
        label = user ? user.name : 'Unknown User';
      }
      addFilterChip('assignee', value, label);
    } else {
      removeFilterChip('assignee');
    }
  };

  const handleStatusChange = (value: string) => {
    onFilterChange({ ...activeFilters, status: value });
    if (value !== 'all') {
      const label = value === 'completed' ? 'Completed' : 'Incomplete';
      addFilterChip('status', value, label);
    } else {
      removeFilterChip('status');
    }
  };

  const handleTopicChange = (value: string) => {
    onFilterChange({ ...activeFilters, topic: value });
    if (value !== 'all') {
      // Use the theme name directly from the value
      addFilterChip('topic', value, value);
    } else {
      removeFilterChip('topic');
    }
  };

  const handleProjectChange = (value: string) => {
    onFilterChange({ ...activeFilters, project: value });
    if (value !== 'all') {
      const project = availableProjects.find(p => p.id === value);
      const label = project ? project.name : 'Unknown Project';
      addFilterChip('project', value, label);
    } else {
      removeFilterChip('project');
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    if (e.target.value) {
      onFilterChange({ ...activeFilters, search: e.target.value });
      addFilterChip('search', e.target.value, `"${e.target.value}"`);
    } else {
      const { search, ...rest } = activeFilters;
      onFilterChange(rest);
      removeFilterChip('search');
    }
  };

  const addFilterChip = (filterType: string, value: string, label: string) => {
    // First remove any existing filter of this type
    removeFilterChip(filterType);
    
    // Then add the new one
    setAppliedFilters(prev => [...prev, `${filterType}-${value}`]);
  };

  const removeFilterChip = (filterType: string) => {
    setAppliedFilters(prev => prev.filter(filter => !filter.startsWith(`${filterType}-`)));
  };

  const handleRemoveFilter = (filter: string) => {
    const [filterType, value] = filter.split('-');
    setAppliedFilters(appliedFilters.filter(f => f !== filter));
    
    // Remove the filter from activeFilters
    const { [filterType]: removedFilter, ...rest } = activeFilters;
    onFilterChange(rest);
    
    // Reset the corresponding select to "all" value
    if (filterType === 'gapType' || filterType === 'assignee' || 
        filterType === 'status' || filterType === 'topic' || 
        filterType === 'project' || filterType === 'viewType') {
      // For these filter types, we reset to their default values
      if (filterType === 'viewType') {
        onFilterChange({ ...rest, viewType: 'list' });
      } else {
        onFilterChange({ ...rest, [filterType]: 'all' });
      }
    }
    
    // Clear search input if search filter is removed
    if (filterType === 'search') {
      setSearchTerm("");
    }
  };

  const renderFilterBadge = (filter: string) => {
    const [filterType, value] = filter.split(/-(.*)/s);
    
    let label = value;
    let icon = null;
    
    if (filterType === 'gapType') {
      if (value === 'evidence_gap') {
        icon = <Wrench className="h-3.5 w-3.5 mr-1" />;
        label = 'Evidence Gap';
      } else if (value === 'invalid_document') {
        icon = <FileX className="h-3.5 w-3.5 mr-1" />;
        label = 'Invalid Document';
      }
    } else if (filterType === 'assignee') {
      if (value === 'me') {
        icon = <Users className="h-3.5 w-3.5 mr-1" />;
        label = 'Only me';
      } else if (value === 'unassigned') {
        label = 'Unassigned';
      } else {
        const user = users.find(u => u.id === value);
        label = user ? user.name : 'Unknown User';
      }
    } else if (filterType === 'status') {
      icon = <Check className="h-3.5 w-3.5 mr-1" />;
      label = value === 'completed' ? 'Completed' : 'Incomplete';
    } else if (filterType === 'viewType') {
      icon = <LayoutList className="h-3.5 w-3.5 mr-1" />;
      label = value === 'document' ? 'Document View' : 'List View';
    } else if (filterType === 'topic') {
      // For topic, use the value directly as it's the theme title
      label = value;
    } else if (filterType === 'project') {
      const project = availableProjects.find(p => p.id === value);
      label = project ? project.name : 'Unknown Project';
    } else if (filterType === 'search') {
      label = `"${value}"`;
    }

    return (
      <Badge 
        key={filter} 
        variant="outline" 
        className="flex items-center gap-1 py-1.5 px-3 bg-gray-100 text-gray-700"
      >
        {icon}
        {label}
        <X 
          className="h-3.5 w-3.5 ml-1 cursor-pointer" 
          onClick={() => handleRemoveFilter(filter)}
        />
      </Badge>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap items-center gap-3">
        <Select defaultValue="list" value={activeFilters.viewType || "list"} onValueChange={handleViewTypeChange}>
          <SelectTrigger className="w-[140px]">
            <div className="flex items-center gap-2">
              <LayoutList className="h-4 w-4" />
              <span>{activeFilters.viewType === 'document' ? 'Document View' : 'List View'}</span>
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="list">List View</SelectItem>
            <SelectItem value="document">Document View</SelectItem>
          </SelectContent>
        </Select>

        <Select defaultValue="all" value={activeFilters.assignee || "all"} onValueChange={handleAssigneeChange}>
          <SelectTrigger className="w-[150px]">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span>
                {activeFilters.assignee === 'me' ? 'Only me' :
                 activeFilters.assignee === 'unassigned' ? 'Unassigned' :
                 activeFilters.assignee && activeFilters.assignee !== 'all' ? 
                   (() => {
                     const user = users.find(u => u.id === activeFilters.assignee);
                     return user ? (user.name.length > 10 ? user.name.substring(0, 10) + '...' : user.name) : 'Unknown';
                   })() :
                 'Assigned to'}
              </span>
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Users</SelectItem>
            <SelectItem value="me">Only me</SelectItem>
            <SelectItem value="unassigned">Unassigned</SelectItem>
            {users.map(user => (
              <SelectItem key={user.id} value={user.id}>{user.name}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select defaultValue="all" value={activeFilters.status || "all"} onValueChange={handleStatusChange}>
          <SelectTrigger className="w-[120px]">
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4" />
              <span>
                {activeFilters.status === 'completed' ? 'Completed' :
                 activeFilters.status === 'pending' ? 'Incomplete' :
                 'Status'}
              </span>
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="pending">Incomplete</SelectItem>
          </SelectContent>
        </Select>

        <Select defaultValue="all" value={activeFilters?.topic || "all"} onValueChange={handleTopicChange}>
          <SelectTrigger className="w-[120px]">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              <span>
                {activeFilters.topic ? activeFilters.topic !== 'all' ? 
                  (activeFilters.topic?.length > 8 ? 
                    activeFilters.topic.substring(0, 8) + '...' : 
                    activeFilters.topic) : 
                  'Topics' : 'Topics'}
              </span>
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            {availableThemes.map(theme => (
              <SelectItem key={theme} value={theme}>{theme}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select defaultValue="all" value={activeFilters.project || "all"} onValueChange={handleProjectChange}>
          <SelectTrigger className="w-[140px]">
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              <span>
                {activeFilters.project && activeFilters.project !== 'all' ? 
                  (() => {
                    const project = availableProjects.find(p => p.id === activeFilters.project);
                    const projectName = project ? project.name : 'Unknown';
                    return projectName.length > 10 ? projectName.substring(0, 10) + '...' : projectName;
                  })() :
                  'All Projects'}
              </span>
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Projects</SelectItem>
            {availableProjects.map(project => (
              <SelectItem key={project.id} value={project.id}>{project.name}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        <div className="flex-1 min-w-[200px]">
          <Input 
            type="search" 
            placeholder="Search by keyword" 
            value={searchTerm}
            onChange={handleSearchChange}
            className="w-full"
          />
        </div>
      </div>

      {appliedFilters.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {appliedFilters.map(renderFilterBadge)}
        </div>
      )}
    </div>
  );
};
