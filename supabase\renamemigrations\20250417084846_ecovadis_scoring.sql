CREATE TYPE ecovadis_score_level AS ENUM ('Outstanding', 'Advanced', 'Good', 'Partial', 'Insufficient');

CREATE TABLE IF NOT EXISTS project_ecovadis_question_score (
  "id" uuid PRIMARY KEY NOT NULL DEFAULT public.uuid_generate_v4(),
  "questionId" uuid NOT NULL,
  "score" integer NOT NULL CHECK (score >= 0 AND score <= 100),
  "level" ecovadis_score_level NOT NULL,
  "description" text,
  "breakdown" text,
  "conclusion" text,
  "createdAt" timestamp NOT NULL DEFAULT now(),
  
  CONSTRAINT "FK_project_ecovadis_question_score_question" 
    FOREIGN KEY ("questionId") REFERENCES project_ecovadis_question("id") ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS project_ecovadis_question_score_history (
  "id" uuid PRIMARY KEY NOT NULL DEFAULT public.uuid_generate_v4(),
  "scoreId" uuid NOT NULL,
  "score" integer NOT NULL CHECK (score >= 1 AND score <= 100),
  "level" ecovadis_score_level NOT NULL,
  "description" text,
  "breakdown" text,
  "conclusion" text,
  "version" integer NOT NULL,
  "createdAt" timestamp NOT NULL DEFAULT now(),
  
  CONSTRAINT "FK_project_ecovadis_question_score_history_score" 
    FOREIGN KEY ("scoreId") REFERENCES project_ecovadis_question_score("id") ON DELETE CASCADE
);

CREATE INDEX idx_project_ecovadis_question_score_question ON project_ecovadis_question_score("questionId");
CREATE INDEX idx_project_ecovadis_question_score_history_score ON project_ecovadis_question_score_history("scoreId");
CREATE INDEX idx_project_ecovadis_question_score_history_version ON project_ecovadis_question_score_history("version");