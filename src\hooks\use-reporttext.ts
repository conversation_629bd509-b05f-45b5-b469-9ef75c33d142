import { useEffect, useMemo, useState } from 'react';

import {
  approveDataRequest,
  fetchDataRequestGenerations,
  generateDataRequestWithAi,
  reviewDataRequestContentWithAI,
  updateDataRequestPayload,
  updateDRGenerationStatus,
} from '@/api/data-request/data-request.api';
import { DataRequestStatus, DatapointRequestStatus } from '@/types';
import {
  CommentData,
  DataRequestData,
  generationStatus,
  IDataGenerations,
  QUEUE_STATUS,
} from '@/types/project';
import { permitOverride, userHasRequiredRole } from '@/lib/utils';
import { useAuthentication } from '@/api/authentication/authentication.query';
import { toast } from '@/components/ui/use-toast';
import { USER_ROLE } from '@/constants/workspaceConstants';
import { fireConfetti } from '@/lib/confetti';
import { GenerateReportTextWithAIFormData } from '@/components/dashboard/AiGenerateDrConfirmModal';
import { useDataRequestContext } from '@/context/dataRequestContext';

export function useReportText({
  dataRequest,
  content,
}: {
  content: string;
  dataRequest: DataRequestData;
}) {
  const [reportText, setReportText] = useState<string>(content);
  const { refetchDataRequest } = useDataRequestContext();
  const [isLoadingReviewWithAi, setIsLoadingReviewWithAi] = useState(false);
  const [isLoadingGenerateWithAi, setIsLoadingGenerateWithAi] = useState(false);
  const [confirmAiDialogOpen, setConfirmAiDialogOpen] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [dataRequestGeneration, setDataRequestGeneration] = useState<
    IDataGenerations[]
  >([]);

  useEffect(() => {
    if (userHasRequiredRole([USER_ROLE.SuperAdmin], user)) {
      fetchDRGenerations();
    }
  }, [dataRequest]);

  useEffect(() => {
    if (dataRequest.queueStatus === QUEUE_STATUS.QueuedForGeneration) {
      setIsLoadingGenerateWithAi(true);
    }
    if (dataRequest.queueStatus === QUEUE_STATUS.QueuedForReview) {
      setIsLoadingReviewWithAi(true);
    }
  }, [dataRequest]);

  const { user } = useAuthentication();

  async function fetchDRGenerations() {
    const drGenerations = await fetchDataRequestGenerations(dataRequest.id);
    setDataRequestGeneration(drGenerations);
  }

  async function handleApprove() {
    try {
      await approveDataRequest(dataRequest.id);
      fireConfetti();
      toast({
        title: 'Report Text Approved',
        variant: 'success',
      });
      refetchDataRequest();
    } catch (error) {
      toast({
        title: 'Error approving report text',
        variant: 'destructive',
      });
    }
  }

  async function handleSave() {
    try {
      await updateDataRequestPayload(dataRequest.id, { content: reportText });
      setIsDirty(false);
      toast({
        title: 'Report Text Saved',
        variant: 'success',
      });
      refetchDataRequest();
    } catch (error) {
      toast({
        title: 'Error saving report text',
        variant: 'destructive',
      });
    }
  }

  function canReviewWithAi(): {
    allow: boolean;
    tooltip: string;
  } {
    switch (true) {
      case permitOverride():
        return { allow: true, tooltip: 'Review report text using AI' };

      case !dataRequest.disclosureRequirement.publicAccess &&
        !userHasRequiredRole(
          [USER_ROLE.SuperAdmin, USER_ROLE.AiContributor],
          user
        ):
        return {
          allow: false,
          tooltip:
            'This gaps are only shown after quality assurance by Glacier',
        };

      case dataRequest.status !== DataRequestStatus.Draft ||
        reportText.trim() === '':
        return {
          allow: false,
          tooltip: 'No draft available to review',
        };

      case isDirty:
        return {
          allow: false,
          tooltip: 'Cannot review because there are unsaved changes.',
        };

      case isLoadingReviewWithAi:
        return {
          allow: false,
          tooltip: 'Cannot review because AI review is currently loading.',
        };

      case !userHasRequiredRole(
        [
          USER_ROLE.SuperAdmin,
          USER_ROLE.AiContributor,
          USER_ROLE.WorkspaceAdmin,
          USER_ROLE.AiReviewer,
          USER_ROLE.Contributor,
        ],
        user
      ):
        return {
          allow: false,
          tooltip:
            'You do not have the right permissions to Review report text with AI',
        };

      default:
        return { allow: true, tooltip: 'Review report text using AI' };
    }
  }

  function dataRequestDr() {
    return dataRequest.disclosureRequirement.dr;
  }

  async function handleReviewWithAi() {
    setIsLoadingReviewWithAi(true);
    try {
      toast({
        title: `AI Review for ${dataRequestDr()} started. This may take up to 30 seconds.`,
        variant: 'default',
      });
      const gapAnalysisComment = await reviewDataRequestContentWithAI(
        dataRequest.id
      );
      if (gapAnalysisComment) {
        console.log('gapAnalysisComment', gapAnalysisComment);
        toast({
          title: `AI Review for ${dataRequestDr()} available`,
          variant: 'success',
        });
      }
    } catch (e) {
      toast({
        title: `Error reviewing ${dataRequestDr()} with AI`,
        variant: 'destructive',
      });
    }
    refetchDataRequest();
    setIsLoadingReviewWithAi(false);
  }

  function allReportedDPsHaveCompleteData() {
    // Filter out "NotAnswered" datapoint requests
    const filteredDatapoints = dataRequest.datapointRequests.filter(
      (datapoint) => datapoint.status !== DatapointRequestStatus.NotAnswered
    );

    // Check if any datapoint in the filtered list is not "CompleteData"
    return !filteredDatapoints.some(
      (datapoint) => datapoint.status !== DatapointRequestStatus.CompleteData
    );
  }

  function canGenerateWithAi(): {
    allow: boolean;
    tooltip: string;
  } {
    switch (true) {
      case permitOverride():
        return { allow: true, tooltip: 'Generate report text using AI' };

      case dataRequest.status === DataRequestStatus.NotAnswered:
        return {
          allow: false,
          tooltip: 'Cannot generate because the data request is not reported.',
        };

      case !dataRequest.disclosureRequirement.publicAccess &&
        !userHasRequiredRole(
          [USER_ROLE.SuperAdmin, USER_ROLE.AiContributor],
          user
        ):
        return {
          allow: false,
          tooltip:
            'This disclosure requirement is only shown after quality assurance by Glacier',
        };

      case !dataRequest.datapointRequests ||
        dataRequest.datapointRequests.length === 0:
        return {
          allow: false,
          tooltip: 'Cannot generate because there are no datapoint reported.',
        };

      case !allReportedDPsHaveCompleteData():
        return {
          allow: false,
          tooltip:
            'Cannot generate because not all reported datapoints have complete data.',
        };

      case isDirty:
        return {
          allow: false,
          tooltip: 'Cannot generate because there are unsaved changes.',
        };

      case !userHasRequiredRole(
        [
          USER_ROLE.SuperAdmin,
          USER_ROLE.AiContributor,
          USER_ROLE.WorkspaceAdmin,
          USER_ROLE.Contributor,
        ],
        user
      ):
        return {
          allow: false,
          tooltip:
            'You do not have the right permissions to Generate report text with AI',
        };

      default:
        return { allow: true, tooltip: 'Generate report text using AI' };
    }
  }

  async function handleGenerateWithAi(data: GenerateReportTextWithAIFormData) {
    setIsLoadingGenerateWithAi(true);
    try {
      toast({
        title: `AI Generation for ${dataRequestDr()} started. This may take up to 30 seconds.`,
        variant: 'default',
      });
      const { content, id } = await generateDataRequestWithAi(
        dataRequest.id,
        data
      );
      if (content) {
        if (userHasRequiredRole([USER_ROLE.SuperAdmin], user)) {
          setDataRequestGeneration([
            ...dataRequestGeneration,
            {
              id: id || '0',
              status: generationStatus.pending,
              createdAt: new Date().toISOString(),
              data: { content },
            },
          ]);
        } else {
          setReportText(content);
        }
        toast({
          title: `AI Generation for ${dataRequestDr()} available`,
          variant: 'success',
        });
      }
    } catch (e) {
      toast({
        title: `Error generating ${dataRequestDr()} with AI`,
        variant: 'destructive',
      });
    }
    refetchDataRequest();
    setIsLoadingGenerateWithAi(false);
  }

  const canApproveReportText = useMemo(() => {
    const unansweredComments = dataRequest.comments.reduce(
      (acc: number, comment: CommentData) => (comment.resolved ? acc : acc + 1),
      0
    );
    return unansweredComments === 0 && reportText.trim() !== '' && !isDirty;
  }, [dataRequest.comments, reportText, isDirty]);

  function getApproveReportTextTooltipContent() {
    return canApproveReportText
      ? 'Approve the current report text'
      : reportText.trim() === ''
        ? 'Cannot approve empty text'
        : isDirty
          ? 'Save the content first'
          : 'Pending unresolved comments';
  }

  async function updateDrGenerationStatus(
    id: string,
    updatedStatus: generationStatus
  ) {
    if (userHasRequiredRole([USER_ROLE.SuperAdmin], user)) {
      try {
        const data: { content?: string; status: generationStatus } =
          await updateDRGenerationStatus(id, updatedStatus);
        if (
          (updatedStatus === generationStatus.approved ||
            updatedStatus === generationStatus.minorChanges) &&
          data.content
        ) {
          setReportText(data.content);
        }
        const updatedGenerationData = dataRequestGeneration.map(
          (generation) => {
            if (generation.id === id) {
              return { ...generation, status: updatedStatus };
            }
            return generation;
          }
        );
        setDataRequestGeneration(updatedGenerationData);
        if (updatedStatus === generationStatus.approved) {
          fireConfetti();
        }
        toast({
          title: `Generation ${updatedStatus}`,
          variant: 'success',
        });
      } catch (error) {
        toast({
          title: 'Error updating datapoint generation status',
          variant: 'destructive',
        });
        console.error('Error updating datapoint generation status:', error);
      }
    }
  }

  async function unapproveDataRequest() {
    await updateDataRequestPayload(dataRequest.id, {
      status: DataRequestStatus.Draft,
      approvedAt: null,
      approvedBy: null,
    });
    refetchDataRequest();
  }

  return {
    reportText,
    setReportText: (newReportText: string) => {
      setReportText(newReportText);
      setIsDirty(true);
    },
    handleApprove,
    handleSave,
    canReviewWithAi,
    handleReviewWithAi,
    canGenerateWithAi,
    handleGenerateWithAi,
    canApproveReportText,
    isLoadingGenerateWithAi,
    isLoadingReviewWithAi,
    confirmAiDialogOpen,
    setConfirmAiDialogOpen,
    dataRequestGeneration,
    updateDrGenerationStatus,
    unapproveDataRequest,
    getApproveReportTextTooltipContent,
    isDirty,
  };
}
