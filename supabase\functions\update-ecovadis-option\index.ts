
// @ts-expect-error TODO look into this later
import { serve } from "https://deno.land/std@0.131.0/http/server.ts";
import { authValidator } from "../_shared/authValidator.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
  
  try {
    // Get the request body
    const { optionId, projectId, updates } = await req.json();
    
    // Validate inputs
    if (!optionId) {
      return new Response(JSON.stringify({
        error: "Option ID is required"
      }), {
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        },
        status: 400
      });
    }
    
    console.log(`Processing request for option ${optionId}`, updates);
    
    const { user, error, supabaseClient: supabase, response } = await authValidator(req);
    if (error || !supabase) {
      return response;
    }
    
    // Check if we're updating selection status
    if (updates.selected !== undefined) {
      // If option is selected, create or update the answer
      if (updates.selected) {
        console.log(`Option ${optionId} was selected`);
        
        // Check if we already have an answer for this option
        const { data: existingAnswer, error: checkError } = await supabase
          .from('project_ecovadis_answer')
          .select('*')
          .eq('optionId', optionId)
          .eq('projectId', projectId)
          .maybeSingle();
        
        if (checkError) {
          console.error('Error checking existing answer:', checkError);
          return new Response(JSON.stringify({
            error: "Failed to check existing answer"
          }), {
            headers: {
              ...corsHeaders,
              "Content-Type": "application/json"
            },
            status: 500
          });
        }
        
        // Get option data to find question ID
        const { data: optionData, error: optionError } = await supabase
          .from('ecovadis_answer_option')
          .select('id, questionId')
          .eq('id', optionId)
          .single();
        
        if (optionError) {
          console.error('Error fetching option data:', optionError);
          return new Response(JSON.stringify({
            error: "Failed to fetch option data"
          }), {
            headers: {
              ...corsHeaders,
              "Content-Type": "application/json"
            },
            status: 500
          });
        }
        
        // Get question data to find project ID
        const { data: questionData, error: questionError } = await supabase
          .from('project_ecovadis_question')
          .select('projectId')
          .eq('questionId', optionData.questionId)
          .eq('projectId', projectId)
          .single();
        
        if (questionError) {
          console.error('Error fetching question data:', questionError);
          return new Response(JSON.stringify({
            error: "Failed to fetch question data"
          }), {
            headers: {
              ...corsHeaders,
              "Content-Type": "application/json"
            },
            status: 500
          });
        }
        
        // Determine the response value based on supportsAnswerText
        // If evidence_examples is provided, use it, otherwise use "true" for regular checkboxes
        const responseValue = updates.evidence_examples || "true";
        
        // If it already exists, update it, otherwise insert
        if (!existingAnswer) {
          console.log('Inserting new answer with project ID:', questionData.projectId);
          
          const { data, error } = await supabase
            .from('project_ecovadis_answer')
            .insert({
              optionId: optionId,
              projectId: questionData.projectId,
              response: responseValue,
              supportsAnswerText: updates.supportsAnswerText || false
            });
          
          if (error) {
            console.error('Error inserting answer:', error);
            return new Response(JSON.stringify({
              error: "Failed to insert answer"
            }), {
              headers: {
                ...corsHeaders,
                "Content-Type": "application/json"
              },
              status: 500
            });
          }
          
          console.log('Successfully inserted answer');
        } else {
          console.log('Answer already exists, updating response');
          
          const { data, error } = await supabase
            .from('project_ecovadis_answer')
            .update({
              response: responseValue,
              supportsAnswerText: updates.supportsAnswerText || existingAnswer.supportsAnswerText || false
            })
            .eq('id', existingAnswer.id);
          
          if (error) {
            console.error('Error updating answer:', error);
            return new Response(JSON.stringify({
              error: "Failed to update answer"
            }), {
              headers: {
                ...corsHeaders,
                "Content-Type": "application/json"
              },
              status: 500
            });
          }
          
          console.log('Successfully updated answer');
        }
      } else {
        // If option is deselected, set response to null
        console.log(`Option ${optionId} was deselected, setting response to null`);
        
        const { data, error } = await supabase
          .from('project_ecovadis_answer')
          .update({ response: null })
          .eq('optionId', optionId)
          .eq('projectId', projectId);
        
        if (error) {
          console.error('Error updating answer:', error);
          return new Response(JSON.stringify({
            error: "Failed to update answer"
          }), {
            headers: {
              ...corsHeaders,
              "Content-Type": "application/json"
            },
            status: 500
          });
        }
        
        console.log('Successfully deselected option');
      }
    } else if (updates.evidence_examples !== undefined) {
      // Only updating evidence examples/text response
      const { data: existingAnswer, error: checkError } = await supabase
        .from('project_ecovadis_answer')
        .select('*')
        .eq('optionId', optionId)
        .maybeSingle();
      
      if (checkError) {
        console.error('Error checking existing answer:', checkError);
        return new Response(JSON.stringify({
          error: "Failed to check existing answer"
        }), {
          headers: {
            ...corsHeaders,
            "Content-Type": "application/json"
          },
          status: 500
        });
      }
      
      if (existingAnswer) {
        // Update existing response
        const { data, error } = await supabase
          .from('project_ecovadis_answer')
          .update({
            response: updates.evidence_examples
          })
          .eq('id', existingAnswer.id);
        
        if (error) {
          console.error('Error updating evidence examples:', error);
          return new Response(JSON.stringify({
            error: "Failed to update evidence examples"
          }), {
            headers: {
              ...corsHeaders,
              "Content-Type": "application/json"
            },
            status: 500
          });
        }
        
        console.log('Successfully updated evidence examples');
      } else {
        // Create new answer if none exists
        const { data: optionData, error: optionError } = await supabase
          .from('ecovadis_answer_option')
          .select('id, questionId')
          .eq('id', optionId)
          .single();
        
        if (optionError) {
          console.error('Error fetching option data:', optionError);
          return new Response(JSON.stringify({
            error: "Failed to fetch option data"
          }), {
            headers: {
              ...corsHeaders,
              "Content-Type": "application/json"
            },
            status: 500
          });
        }
        
        const { data: questionData, error: questionError } = await supabase
          .from('project_ecovadis_question')
          .select('projectId')
          .eq('questionId', optionData.questionId)
          .single();
        
        if (questionError) {
          console.error('Error fetching question data:', questionError);
          return new Response(JSON.stringify({
            error: "Failed to fetch question data"
          }), {
            headers: {
              ...corsHeaders,
              "Content-Type": "application/json"
            },
            status: 500
          });
        }
        
        const { data, error } = await supabase
          .from('project_ecovadis_answer')
          .insert({
            optionId: optionId,
            projectId: questionData.projectId,
            response: updates.evidence_examples,
            supportsAnswerText: updates.supportsAnswerText || true
          });
        
        if (error) {
          console.error('Error creating answer with evidence examples:', error);
          return new Response(JSON.stringify({
            error: "Failed to create answer with evidence examples"
          }), {
            headers: {
              ...corsHeaders,
              "Content-Type": "application/json"
            },
            status: 500
          });
        }
        
        console.log('Successfully created answer with evidence examples');
      }
    }
    
    // Return success response
    return new Response(JSON.stringify({
      success: true
    }), {
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      },
      status: 200
    });
  } catch (error) {
    console.error('Error processing request:', error);
    return new Response(JSON.stringify({
      error: error.message
    }), {
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      },
      status: 500
    });
  }
});
