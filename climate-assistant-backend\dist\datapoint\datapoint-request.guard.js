"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatapointRequestGuard = void 0;
const common_1 = require("@nestjs/common");
const datapoint_request_service_1 = require("./datapoint-request.service");
const data_request_service_1 = require("../data-request/data-request.service");
const core_1 = require("@nestjs/core");
let DatapointRequestGuard = class DatapointRequestGuard {
    constructor(dataRequestService, datapointRequestService, reflector) {
        this.dataRequestService = dataRequestService;
        this.datapointRequestService = datapointRequestService;
        this.reflector = reflector;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const datapointRequestId = request.params.datapointRequestId;
        const workspaceId = request.user.workspaceId;
        const datapointRequest = await this.datapointRequestService.findById(datapointRequestId);
        const dataRequestId = datapointRequest.dataRequestId;
        const dataRequest = await this.dataRequestService.findProject(dataRequestId);
        const project = dataRequest.project;
        if (project.workspaceId !== workspaceId) {
            throw new common_1.UnauthorizedException(`Project is not from this workspace`);
        }
        request.datapointRequest = datapointRequest;
        const customCheck = this.reflector.get('customCheck', context.getHandler());
        switch (customCheck) {
            case 'generateWithAI':
                return this.datapointRequestService.validateDatapointRequestGenerationRightsOrFail({
                    datapointRequest,
                    userId: request.user.id,
                });
            default:
                return true;
        }
    }
};
exports.DatapointRequestGuard = DatapointRequestGuard;
exports.DatapointRequestGuard = DatapointRequestGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [data_request_service_1.DataRequestService,
        datapoint_request_service_1.DatapointRequestService,
        core_1.Reflector])
], DatapointRequestGuard);
//# sourceMappingURL=datapoint-request.guard.js.map