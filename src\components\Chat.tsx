import { FunctionComponent, useState } from 'react';
import ScrollToBottom from 'react-scroll-to-bottom';

import { PromptBar } from '@/components/PromptBar';
import {
  ChatMessageElement,
  ChatMessageLoading,
} from '@/components/ChatMessageElement';
import { ChatMessage } from '@/models/chat.models.ts';
import { MainLayout } from '@/components/MainLayout';

const Chat: FunctionComponent<{
  messages: ChatMessage[];
  pendingMessage: string;
  sendMessage: (input: string) => void;
  isMessagePending: boolean;
}> = ({ messages, pendingMessage, sendMessage, isMessagePending }) => {
  const [input, setInput] = useState('');

  const onSendButton = () => {
    if (input.trim() === '') return;
    void sendMessage(input);
    setInput('');
  };

  return (
    <MainLayout>
      <div className="flex flex-col flex-1 justify-between overflow-hidden">
        <ScrollToBottom
          className="flex overflow-y-auto"
          initialScrollBehavior="auto"
          mode="bottom"
          followButtonClassName="hidden"
        >
          {messages.map((message, index) => (
            <ChatMessageElement
              sendMessage={sendMessage}
              message={message}
              key={index}
              isPending={isMessagePending}
            ></ChatMessageElement>
          ))}
          {isMessagePending && pendingMessage !== '' && (
            <ChatMessageElement
              sendMessage={sendMessage}
              message={{ role: 'assistant', content: pendingMessage }}
              isRendering={true}
              isPending={isMessagePending}
            ></ChatMessageElement>
          )}
          {isMessagePending && pendingMessage === '' && (
            <ChatMessageLoading></ChatMessageLoading>
          )}
        </ScrollToBottom>

        <div className="px-12 pb-6">
          <PromptBar
            input={input}
            onInputChange={(value) => setInput(value)}
            onSendButton={() => onSendButton()}
            isSendDisabled={input.trim() === '' || isMessagePending}
          ></PromptBar>
        </div>
      </div>
    </MainLayout>
  );
};

export { Chat };
