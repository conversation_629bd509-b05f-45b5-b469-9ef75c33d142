import { useQuery, useQueryClient } from '@tanstack/react-query';
import { fetchAllWorkspaces } from '@/api/workspace-settings/workspace-settings.api';
import { Workspace } from '@/types/workspace';

export function useWorkspaces() {
  const queryClient = useQueryClient();

  const { data: workspaces = [], isLoading } = useQuery<Workspace[]>({
    queryKey: ['workspaces'],
    queryFn: fetchAllWorkspaces,
  });

  function refetchWorkspaces() {
    queryClient.invalidateQueries({ queryKey: ['workspaces'] });
  }

  return {
    workspaces,
    loading: isLoading,
    refetchWorkspaces,
  };
}
