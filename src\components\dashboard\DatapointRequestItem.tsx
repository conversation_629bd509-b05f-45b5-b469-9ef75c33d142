import {
  AlertTriangleIcon,
  CheckCircleIcon,
  CircleIcon,
  LoaderCircle,
  SaveIcon,
  XCircleIcon,
  WandSparklesIcon,
} from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';

import { Label } from '../ui/label';
import { Button } from '../ui/button';
import { Switch } from '../ui/switch';
import { StatusLabel } from '../ui/status-label';
import { TipTapEditor } from '../ui/tiptap/tiptap-editor';
import IconRenderer from '../ui/icons';

import { DocumentLinks } from './DocumentLinksModal';
import { CommentSection } from './Comments';
import { AiGenerateDatapointConfirmModal } from './AiGenerateDpConfirmModal';
import { GeneratedContent } from './GeneratedContent';
import EsrsInfo from './EsrsInfo';
import { CommentGenerationSection } from './super-admin-tools/CommentGenerations';
import {
  MATERIAL_TOPIC_ORDER,
  MaterialTopicList,
  MaterialTopicsType,
} from './MaterialTopicList';

import { CommentType, DatapointRequestStatus } from '@/types';
import { useDatapointRequest } from '@/hooks/useDatapointRequest';
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { DatapointRequestData, generationStatus } from '@/types/project';
import { userHasRequiredRole } from '@/lib/utils';
import { useAuthentication } from '@/api/authentication/authentication.query';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { USER_ROLE } from '@/constants/workspaceConstants';
import { loadMaterialTopics } from '@/api/datapoint/datapoint-request.api';

export function DatapointRequestItem({
  datapointRequest,
  projectId,
}: {
  datapointRequest: DatapointRequestData;
  projectId: string;
}) {
  const {
    content,
    status,
    esrsDatapoint,
    datapointGenerations,
    isDirty,
    isLoadingReviewWithAi,
    isLoadingGenerateWithAi,
    handleInputChange,
    handleReportDatapointStatusChange,
    handleCommentChanges,
    handleSave,
    canReviewWithAi,
    handleReviewWithAi,
    canGenerateWithAi,
    handleGenerateWithAi,
    confirmAiDialogOpen,
    setConfirmAiDialogOpen,
    handleUpdateDatapointGenerationStatus,
  } = useDatapointRequest({ datapointRequest });

  const { user } = useAuthentication();

  const isSuperAdminUser = useMemo(
    () => userHasRequiredRole([USER_ROLE.SuperAdmin], user),
    [user]
  );

  const [materialTopics, setMaterialTopics] = useState<MaterialTopicsType[]>(
    []
  );
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    if (!materialTopics.length && isExpanded && isSuperAdminUser) {
      loadMaterialTopics({ datapointRequestId: datapointRequest.id })
        .then((data) => {
          const sordedMaterialTopics = data.sort(
            (a: MaterialTopicsType, b: MaterialTopicsType) => {
              return (
                MATERIAL_TOPIC_ORDER.indexOf(a.level) -
                MATERIAL_TOPIC_ORDER.indexOf(b.level)
              );
            }
          );
          setMaterialTopics(sordedMaterialTopics);
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, [isExpanded]);

  const { allow: userCanReviewWithAi, tooltip: canReviewWithAiTooltipText } =
    canReviewWithAi();
  const {
    allow: userCanGenerateWithAi,
    tooltip: canGenerateWithAiTooltipText,
  } = canGenerateWithAi();

  return (
    <AccordionItem
      className="bg-slate-50 px-5 py-2 rounded-lg"
      key={datapointRequest.id}
      value={datapointRequest.id}
    >
      <AccordionTrigger onClick={() => setIsExpanded(!isExpanded)}>
        <div className="space-y-4 text-left">
          <span className="tracking-wide">
            <span className="font-normal">{esrsDatapoint.datapointId}: </span>
            <span className="">{esrsDatapoint.name}</span>
          </span>
          <div className="flex justify-start items-center gap-6">
            <div className="flex items-center space-x-2">
              <Switch
                size={'sm'}
                checked={status !== 'not_answered'}
                onCheckedChange={(checked) => {
                  handleReportDatapointStatusChange(checked);
                }}
                onClick={(e) => e.stopPropagation()}
                id={`report-disclosure-${datapointRequest.id}`}
              />
              <Label htmlFor={`report-disclosure-${datapointRequest.id}`}>
                Report Datapoint
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Label>Status: </Label>
              <StatusLabel status={status} statuses={datapointStatuses} />
            </div>
            <div className="flex items-center space-x-2">
              <DocumentLinks
                documentChunkCount={
                  datapointRequest.documentChunkCount as number
                }
                datapointRequestId={datapointRequest.id}
              />
            </div>
            <div className="flex items-center space-x-2">
              {/*Removed for now*/}
              {false && (
                <Button
                  onClick={(e) => e.stopPropagation()}
                  variant="link"
                  size="xs"
                  className="underline"
                >
                  Explain Datapoint
                </Button>
              )}
            </div>
          </div>
        </div>
      </AccordionTrigger>
      <AccordionContent>
        {isSuperAdminUser && (
          <div className="text-md text-red-500">
            <h1>ADMIN ONLY</h1>
            <p>Datapoint-Request ID: {datapointRequest.id}</p>
            <p>
              Datatype:{' '}
              {JSON.stringify(datapointRequest.esrsDatapoint.dataType)}
            </p>
          </div>
        )}
        <div className="flex flex-col gap-1 mt-2">
          <span className="font-semibold">Text for Datapoint</span>
          <TipTapEditor
            content={content}
            refId={datapointRequest.id}
            setContent={(newValue) => handleInputChange(newValue)}
          />
          <div className="mt-3">
            <Tooltip>
              <TooltipTrigger>
                <Button
                  onClick={() => handleSave(datapointRequest.id)}
                  variant={'default'}
                  disabled={!isDirty}
                >
                  <SaveIcon className="h-4 w-4 mr-2" />
                  Save
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {isDirty ? 'Save changes' : 'No changes to save'}
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger>
                <Button
                  variant={'outline'}
                  disabled={!userCanGenerateWithAi}
                  onClick={() => setConfirmAiDialogOpen(true)}
                  className="ml-2"
                >
                  {isLoadingGenerateWithAi ? (
                    <LoaderCircle className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <IconRenderer
                      iconName={'HammerSparcle'}
                      className="h-4 w-4 mr-2"
                    />
                  )}
                  Generate with AI
                </Button>
              </TooltipTrigger>
              <TooltipContent>{canGenerateWithAiTooltipText}</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger>
                <Button
                  variant="outline"
                  className="ml-2"
                  onClick={() => handleReviewWithAi()}
                  disabled={!userCanReviewWithAi}
                >
                  {isLoadingReviewWithAi ? (
                    <LoaderCircle className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <WandSparklesIcon className="h-4 w-4 mr-2" />
                  )}
                  Review with AI
                </Button>
              </TooltipTrigger>
              <TooltipContent>{canReviewWithAiTooltipText}</TooltipContent>
            </Tooltip>
          </div>
          {isSuperAdminUser && <EsrsInfo esrsDatapoint={esrsDatapoint} />}
          {isSuperAdminUser && materialTopics.length > 0 && (
            <div className="mt-4">
              <span className="underline">Material Topics</span>
              <ul>
                {materialTopics?.map((topic) => (
                  <MaterialTopicList key={topic.id} topic={topic} />
                ))}
              </ul>
            </div>
          )}
          {isSuperAdminUser && datapointGenerations.length > 0 && (
            <GeneratedContent
              generationContents={datapointGenerations}
              handleApproveOrReject={(id: string, status: generationStatus) =>
                handleUpdateDatapointGenerationStatus(id, status)
              }
            />
          )}
          {isSuperAdminUser && (
            <CommentGenerationSection
              projectId={projectId}
              savedComments={datapointRequest.commentGenerations}
              updateCallback={handleCommentChanges}
            />
          )}
          <CommentSection
            savedComments={datapointRequest.comments}
            projectId={projectId}
            commentableId={datapointRequest.id}
            commentableType={CommentType.DatapointRequest}
            updateCallback={handleCommentChanges}
          />
        </div>
        <AiGenerateDatapointConfirmModal
          open={confirmAiDialogOpen}
          setOpen={setConfirmAiDialogOpen}
          callback={handleGenerateWithAi}
          datapointRequest={datapointRequest}
        />
      </AccordionContent>
    </AccordionItem>
  );
}

const datapointStatuses: {
  value: DatapointRequestStatus;
  label: string;
  icon: React.FC<any>;
  color: string;
}[] = [
  {
    value: DatapointRequestStatus.NoData,
    label: 'No Data',
    icon: XCircleIcon,
    color: '#E53E3E', // Darker red for "No Data"
  },
  {
    value: DatapointRequestStatus.NotAnswered,
    label: 'Not Reported',
    icon: CircleIcon,
    color: '#718096', // Darker gray for "Not Reported"
  },
  {
    value: DatapointRequestStatus.IncompleteData,
    label: 'Unresolved Comments',
    icon: AlertTriangleIcon,
    color: '#D69E2E', // Darker yellow for "Incomplete Data"
  },
  {
    value: DatapointRequestStatus.CompleteData,
    label: 'Complete',
    icon: CheckCircleIcon,
    color: '#3182CE', // Darker blue for "Complete Data"
  },
  {
    value: DatapointRequestStatus.QueuedForGeneration,
    label: 'Queued for Generation',
    icon: CheckCircleIcon,
    color: '#D69E2E', // Darker blue for "Complete Data"
  },
];
