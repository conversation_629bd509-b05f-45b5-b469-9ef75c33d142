// File: supabase/functions/_shared/authValidator.ts
// @ts-expect-error TODO look into this later
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.4';
import { corsHeaders } from './cors.ts';

export interface AuthResult {
  authUser: any | null;
  user: {
    id: string;
    email: string;
    name: string;
    created_at: string;
    updated_at: string;
    user_workspace: {
      workspaceId: string;
      role: string;
      created_at: string;
      updated_at: string;
    }[];
  } | null;
  error: string | null;
  status?: number;
  supabaseClient?: any;
  response?: Response;
}

/**
 * Validates a user's authentication token from the request
 * @param req The incoming request object
 * @returns Object containing user data and error information
 */
export async function authValidator(req: Request): Promise<AuthResult> {
  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );
    
    // Get the JWT token from the authorization header
    const authHeader = req.headers.get("Authorization");
    if (!authHeader) {
      return {
        authUser: null,
        user: null,
        error: "No authorization header",
        status: 401,
        response: new Response(
          JSON.stringify({ error: "No authorization header" }),
          { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 401 }
        )
      };
    }
    
    // Extract the token
    const token = authHeader.replace("Bearer ", "");
    
    // Verify the token and get the user
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser(token);

    if (userError || !user) {
      return {
        authUser: null,
        user: null,
        error: userError?.message || 'Unauthorized',
        status: 401,
        response: new Response(
          JSON.stringify({
            error: 'Unauthorized',
            details: userError?.message
          }),
          {
            headers: {
              ...corsHeaders,
              'Content-Type': 'application/json'
            },
            status: 401
          }
        )
      };
    }

    const { data, error } = await supabaseClient
    .from('user')
    .select(
      `
      *,
      user_workspace(*)
    `
    )
    .eq('auth_id', user.id)
    .single();

    // Authentication successful
    return {
      authUser: user,
      user:  data,
      supabaseClient,
      error: null
    };
    
  } catch (error) {
    // Handle unexpected errors
    return {
      authUser: null,
      user: null,
      error: `Authentication error: ${error.message}`,
      status: 500,
      response: new Response(
        JSON.stringify({ error: `Authentication error: ${error.message}` }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
      )
    };
  }
}

export const simpleSupabaseClient = createClient(
  Deno.env.get("SUPABASE_URL") ?? "",
  Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
);