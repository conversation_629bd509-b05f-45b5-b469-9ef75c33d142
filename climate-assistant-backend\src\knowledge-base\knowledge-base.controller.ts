import {
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Request,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { fileInterceptor } from '../util/upload-utils';
import { KnowledgeBaseService } from './knowledge-base.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('knowledge-base')
@Controller('knowledge-base')
export class KnowledgeBaseController {
  constructor(private readonly knowledgeBaseService: KnowledgeBaseService) {}

  @Post('/file-upload')
  @UseInterceptors(fileInterceptor)
  @ApiOperation({ summary: 'Upload a knowledge base file' })
  @ApiResponse({ status: 201, description: 'File uploaded successfully' })
  async saveKnowledgeBaseFile(
    @Request() req,
    @UploadedFile() file: Express.Multer.File,
  ) {
    const { path, originalname } = file;

    await this.knowledgeBaseService.saveFileWithEmbeddings(originalname, path);

    return { message: 'File wurde hochgeladen' };
  }

  @Get('/file-uploads')
  @ApiOperation({ summary: 'Get all knowledge base file uploads' })
  @ApiResponse({
    status: 200,
    description: 'File uploads retrieved successfully',
  })
  async getKnowledgebaseFileUploads() {
    const fileUploads = await this.knowledgeBaseService.getUploadedFiles();

    return fileUploads;
  }

  @Delete('/file-uploads/:id')
  @ApiOperation({ summary: 'Delete a knowledge base file upload' })
  @ApiResponse({ status: 200, description: 'File upload deleted successfully' })
  async deleteKnowledgebaseFile(@Request() req, @Param('id') id: string) {
    await this.knowledgeBaseService.deleteFile(id);
    return { success: true };
  }

  @Get('/datapoints/:esrs')
  @ApiOperation({ summary: 'Get ESRS datapoints by standard' })
  @ApiResponse({
    status: 200,
    description: 'ESRS datapoints retrieved successfully',
  })
  async getEsrsDatapoints(@Param('esrs') esrs: string) {
    return this.knowledgeBaseService.getEsrsDatapointsByStandard(esrs);
  }
}
