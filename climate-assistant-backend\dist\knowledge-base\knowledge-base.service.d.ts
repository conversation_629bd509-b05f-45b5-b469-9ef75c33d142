import { KnowledgeBaseFileUploadChunk } from './entities/knowledge-base-file-upload-chunk.entity';
import { DataSource, Repository } from 'typeorm';
import { KnowledgeBaseFileUpload } from './entities/knowledge-base-file-upload.entity';
import { ChatGptService } from '../llm/chat-gpt.service';
import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { ESRSDisclosureRequirement } from './entities/esrs-disclosure-requirement.entity';
import { ESRSTopic } from './entities/esrs-topic.entity';
export declare class KnowledgeBaseService {
    private readonly knowledgeBaseFileUploadRepository;
    private readonly knowledgeBaseFileUploadChunkRepository;
    private readonly esrsDatapointRepository;
    private readonly esrsDisclosureRequirementRepository;
    private readonly esrsTopicRepository;
    private dataSource;
    private readonly chatGptService;
    constructor(knowledgeBaseFileUploadRepository: Repository<KnowledgeBaseFileUpload>, knowledgeBaseFileUploadChunkRepository: Repository<KnowledgeBaseFileUploadChunk>, esrsDatapointRepository: Repository<ESRSDatapoint>, esrsDisclosureRequirementRepository: Repository<ESRSDisclosureRequirement>, esrsTopicRepository: Repository<ESRSTopic>, dataSource: DataSource, chatGptService: ChatGptService);
    saveFileWithEmbeddings(originalname: string, path: string): Promise<void>;
    getUploadedFiles(): Promise<KnowledgeBaseFileUpload[]>;
    deleteFile(id: KnowledgeBaseFileUpload['id']): Promise<void>;
    getSimilarChunksWithVectorSearch(question: string, elements: number, similarityThreshold?: number): Promise<{
        content: string;
        similarity: number;
    }[]>;
    getSimilarChunksForMultipleQuestions(questions: string[], chunksPerQuestion: number): Promise<string[]>;
    getEsrsDatapointsByStandard(esrs: string): Promise<ESRSDatapoint[]>;
    getEsrsTopics(): Promise<ESRSTopic[]>;
}
