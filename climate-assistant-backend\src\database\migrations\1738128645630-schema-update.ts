import { MigrationInterface, QueryRunner } from 'typeorm';

// This migration is to add the `exampleOutput` column to the `esrs_datapoint` table.
// This is required to pass example in the prompt while generating the datapoint.
export class SchemaUpdate1738128645630 implements MigrationInterface {
  name = 'SchemaUpdate1738128645630';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ADD "exampleOutput" text`,
    );

    await queryRunner.query(`UPDATE esrs_datapoint
        SET "exampleOutput" = '| **Product Group** | **Product/Material Name** | **Expected Durability (Years)** | **Industry Average Durability (Years)** | **Deviation (Years)** | **Deviation (%)** | **Packaging Included (Yes/No)** | **Notes/Comments** |
        | ----------------- | ------------------------- | ------------------------------- | --------------------------------------- | --------------------- | ----------------- | ------------------------------- | ------------------ |
        |                   |                           |                                 |                                         |                       |                   |                                 |                    |
        |                   |                           |                                 |                                         |                       |                   |                                 |                    |
        |                   |                           |                                 |                                         |                       |                   |                                 |                    |
        |                   |                           |                                 |                                         |                       |                   |                                 |                    |
        |                   |                           |                                 |                                         |                       |                   |                                 |                    |
        |                   |                           |                                 |                                         |                       |                   |                                 |                    |'
        WHERE "datapointId" = 'E5-5_02';`);

    await queryRunner.query(`UPDATE esrs_datapoint
        SET "exampleOutput" = '| **Recovery Operation Type** | **Total Amount Diverted (kg/tonnes)** | **Hazardous Waste (kg/tonnes)** | **Non-Hazardous Waste (kg/tonnes)** | **Notes/Comments** |
        | --------------------------- | ------------------------------------- | ------------------------------- | ----------------------------------- | ------------------ |
        | Preparation for Reuse       |                                       |                                 |                                     |                    |
        | Recycling                   |                                       |                                 |                                     |                    |
        | Other Recovery Operations   |                                       |                                 |                                     |                    |
        | **Total**                   |                                       |                                 |                                     |                    |'
        WHERE "datapointId" = 'E5-5_08';`);

    await queryRunner.query(`UPDATE esrs_datapoint
        SET "exampleOutput" = '| **Disposal Treatment Type** | **Total Amount (kg/tonnes)** | **Hazardous Waste (kg/tonnes)** | **Non-Hazardous Waste (kg/tonnes)** | **Notes/Comments** |
        | --------------------------- | ---------------------------- | ------------------------------- | ----------------------------------- | ------------------ |
        | Incineration                |                              |                                 |                                     |                    |
        | Landfill                    |                              |                                 |                                     |                    |
        | Other Disposal Operations   |                              |                                 |                                     |                    |
        | **Total**                   |                              |                                 |                                     |                    |'
        WHERE "datapointId" = 'E5-5_09';`);

    await queryRunner.query(`UPDATE esrs_datapoint
        SET "exampleOutput" = '|                       | At-risk functions          | Managers                   | AMSB (^132^)              | Other own workers         |
|-----------------------|----------------------------|----------------------------|----------------------------|----------------------------|
| **Training coverage** |                            |                            |                            |                            |
| Total                 |                            |                            |                            |                            |
| Total receiving training |                        |                            |                            |                            |
| **Delivery method and duration** |               |                            |                            |                            |
| Classroom training    |                            |                            |                            |                            |
| Computer-based training |                         |                            |                            |                            |
| Voluntary computer-based training |              |                            |                            |                            |
| **Frequency**         |                            |                            |                            |                            |
| How often training is required |                  |                            |                            | -                          |
| **Topics covered**    |                            |                            |                            |                            |
| Definition of corruption |                        |                            |                            |                            |
| Policy                |                            |                            |                            |                            |
| Procedures on suspicion/detection |               |                            |                            |                            |
| Etc.                  |                            |                            |                            |                            |'
        WHERE "datapointId" = 'G1-4_03';`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" DROP COLUMN "exampleOutput"`,
    );
  }
}
