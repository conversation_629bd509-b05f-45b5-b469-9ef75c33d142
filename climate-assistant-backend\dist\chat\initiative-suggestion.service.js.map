{"version": 3, "file": "initiative-suggestion.service.js", "sourceRoot": "", "sources": ["../../src/chat/initiative-suggestion.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,8DAAyD;AAGzD,0DAAsD;AAEtD,uGAAyF;AACzF,qFAAgF;AAChF,4CAA2C;AAUpC,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IACtC,YACmB,cAA8B,EAC9B,YAA0B,EAC1B,gBAA2C,EAC3C,oBAA0C;QAH1C,mBAAc,GAAd,cAAc,CAAgB;QAC9B,iBAAY,GAAZ,YAAY,CAAc;QAC1B,qBAAgB,GAAhB,gBAAgB,CAA2B;QAC3C,yBAAoB,GAApB,oBAAoB,CAAsB;QAGpD,aAAQ,GAAG,sBAAU,CAAC,QAAQ,CAAC,CAAC;IAFtC,CAAC;IAIJ,iCAAiC,CAC/B,MAAc;QAEd,OAAO;YACL,IAAI,EAAE,oBAAoB;YAC1B,cAAc,EAAE;gBACd,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE;oBACR,IAAI,EAAE,+BAA+B;oBACrC,WAAW,EACT,6HAA6H;iBAChI;aACF;YACD,OAAO,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,CACvB,IAAI,CAAC,2BAA2B,CAAC,QAA4B,EAAE,MAAM,CAAC;SACzE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,CAAC,2BAA2B,CAChC,gBAAkC,EAClC,MAAc;QAEd,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAEtD,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAC9C,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAC3B,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QACrE,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CACrD,OAAO,EACP,gBAAgB,CACjB,CAAC;QAEF,MAAM,yBAAyB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAClE,OAAO,EACP,gBAAgB,EAChB,cAAc,CACf,CAAC;QACF,MAAM,wBAAwB,GAAG,MAAM,IAAI,CAAC,4BAA4B,CACtE,OAAO,EACP,gBAAgB,EAChB,cAAc,CACf,CAAC;QACF,MAAM,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,GAC9C,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,yBAAyB,CAAC,CAAC,CAAC;QAE3E,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAa,CACpC,OAAO,EACP,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,CACf,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAE/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mCAAmC,CAC3D,OAAO,EACP,KAAK,EACL,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,EACd,gBAAgB,CACjB,CAAC;QAEF,MAAM,IAAI,CAAC;QACX,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;YAChC,MAAM,IAAI,CAAC;QACb,CAAC;QAED,OAAO;IACT,CAAC;IAED,KAAK,CAAC,mCAAmC,CACvC,OAAe,EACf,KAAe,EACf,mBAA2B,EAC3B,mBAA6B,EAC7B,cAA8B,EAC9B,gBAAkC;QAElC,OAAO,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC/D;gBACE,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,wBAAwB,CAC/B,OAAO,EACP,KAAK,EACL,mBAAmB,EACnB,cAAc,EACd,mBAAmB,EACnB,gBAAgB,CACjB;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,OAAe,EACf,mBAA2B,EAC3B,mBAA6B,EAC7B,cAA8B;QAE9B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CACvD,IAAI,CAAC,QAAQ,EACb;YACE;gBACE,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,yBAAyB,CAChC,OAAO,EACP,mBAAmB,EACnB,cAAc,EACd,mBAAmB,CACpB;aACF;SACF,EACD,IAAI,CACL,CAAC;QAEF,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAwB,CAAC;QAE5D,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,4BAA4B,CAChC,OAAe,EACf,gBAAkC,EAClC,cAA8B;QAE9B,MAAM,QAAQ,GAAG;YACf;gBACE,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,mCAAmC,CAC1C,OAAO,EACP,gBAAgB,EAChB,cAAc,CACf;aACF;SACkB,CAAC;QAEtB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAC/D,IAAI,CAAC,QAAQ,EACb,QAAwC,EACxC,IAAI,CACL,CAAC;QAEF,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAA4B,CAAC;QAE5E,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC;YACzD,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,uBAAuB,CAC3B,OAAe,EACf,gBAAkC,EAClC,cAA8B;QAE9B,MAAM,QAAQ,GAAG;YACf;gBACE,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,oCAAoC,CAC3C,OAAO,EACP,gBAAgB,EAChB,cAAc,CACf;aACF;SACkB,CAAC;QAEtB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAC/D,IAAI,CAAC,QAAQ,EACb,QAAwC,EACxC,IAAI,CACL,CAAC;QAEF,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAA4B,CAAC;QAE5E,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,SAAS,CAAC,CAAC;QAE/D,MAAM,MAAM,GACV,MAAM,IAAI,CAAC,oBAAoB,CAAC,oCAAoC,CAClE,SAAS,EACT,CAAC,CACF,CAAC;QAEJ,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,OAAe,EACf,gBAAkC;QAElC,MAAM,QAAQ,GAAG;YACf;gBACE,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,2BAA2B,CAAC,OAAO,EAAE,gBAAgB,CAAC;aAChE;SACkB,CAAC;QAEtB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAC/D,IAAI,CAAC,QAAQ,EACb,QAAwC,EACxC,IAAI,CACL,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAE5B,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAmB,CAAC;QACtD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC;QACvE,CAAC;IACH,CAAC;CACF,CAAA;AA9NY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;qCAGwB,iCAAc;QAChB,4BAAY;QACR,gEAAyB;QACrB,6CAAoB;GALlD,2BAA2B,CA8NvC;AAED,MAAM,2BAA2B,GAAG,CAClC,OAAe,EACf,gBAAkC,EAClC,EAAE,CAAC;;;;;;;;;;;aAWQ,OAAO;;uBAEG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC;CAChF,CAAC;AAEF,MAAM,mCAAmC,GAAG,CAC1C,OAAe,EACf,gBAAkC,EAClC,cAA8B,EAC9B,EAAE,CAAC;;;;;;;6DAOwD,cAAc,CAAC,MAAM,0BAA0B,cAAc,CAAC,SAAS;qEAC/D,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,cAAc,CAAC,QAAQ;;;;;;;;aAQtI,OAAO;;uBAEG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC;CAChF,CAAC;AAEF,MAAM,oCAAoC,GAAG,CAC3C,OAAe,EACf,gBAAkC,EAClC,cAA8B,EAC9B,EAAE,CAAC;;;;;;;6DAOwD,cAAc,CAAC,MAAM,0BAA0B,cAAc,CAAC,SAAS;qEAC/D,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,cAAc,CAAC,QAAQ;;;;;;;;aAQtI,OAAO;;uBAEG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC;CAChF,CAAC;AAEF,MAAM,yBAAyB,GAAG,CAChC,OAAe,EACf,mBAA2B,EAC3B,cAA8B,EAC9B,mBAA6B,EAC7B,EAAE,CAAC;;;;;;;6DAOwD,cAAc,CAAC,MAAM,0BAA0B,cAAc,CAAC,SAAS;qEAC/D,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,cAAc,CAAC,QAAQ;;;;;;aAMtI,OAAO;2BACO,mBAAmB;0CACJ,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC;GACpE,CAAC;AAEJ,MAAM,wBAAwB,GAAG,CAC/B,OAAe,EACf,KAAe,EACf,mBAA2B,EAC3B,cAA8B,EAC9B,mBAA6B,EAC7B,gBAAkC,EAClC,EAAE,CAAC;;;;;;;8EAOyE,cAAc,CAAC,MAAM,0BAA0B,cAAc,CAAC,SAAS;gGACrD,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,cAAc,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;aA4BjK,OAAO;2BACO,mBAAmB;0CACJ,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC;oCACnC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;4BACzB,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC;GACnF,CAAC;AAGJ,MAAM,sBAAsB,GAAG,CAC7B,qBAA6B,EAC7B,cAA8B,EAC9B,OAAe,EACf,EAAE,CAAC;;;;;;;;;uDASkD,cAAc,CAAC,MAAM,0BAA0B,cAAc,CAAC,SAAS;+DAC/D,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,cAAc,CAAC,QAAQ;;;;;;;;;;0BAUnH,qBAAqB;aAClC,OAAO;GACjB,CAAC"}