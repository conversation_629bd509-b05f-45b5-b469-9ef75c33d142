import { Injectable, Logger } from '@nestjs/common';
import 'dotenv/config';
import { MarkdownTextSplitter } from '@langchain/textsplitters';
import { encoding_for_model } from '@dqbd/tiktoken';
import type { DocumentChunkGenerated } from 'src/types';
import * as XLSX from 'xlsx';
import { LLM_MODELS } from 'src/constants';
import { parseDocumentWithLlamaparseApi } from 'src/llm/llamaparse.service';
import { LlmRateLimiterService } from 'src/llm-rate-limiter/llm-rate-limiter.service';
import { parseDocumentWithAzureDocumentIntelligence } from 'src/llm/azure-docintelligence.service';

@Injectable()
export class DocumentParserService {
  constructor(private readonly llmRateLimitService: LlmRateLimiterService) {}
  private tokenizer = encoding_for_model('gpt-3.5-turbo');
  private readonly logger = new Logger(DocumentParserService.name);

  private countTokens(text: string): number {
    return this.tokenizer.encode(text).length;
  }

  private parseMarkdownTable(tableLines: string[]): {
    headers: any[][];
    rows: any[][];
  } {
    this.logger.log(`Parsing markdown table with ${tableLines.length} lines`);

    // Find the separator line index (line with |---|---|)
    const separatorLineIndex = tableLines.findIndex(
      (line) =>
        line.trim().includes('---') &&
        /^\|(\s*[-]+\s*\|)+\s*$/.test(line.trim())
    );

    if (separatorLineIndex <= 0) {
      this.logger.log('Table format appears to use single header row');
      const headersLine = tableLines[0];
      const dataLines = tableLines.slice(2); // Skip separator line

      const headers = [
        headersLine
          .trim()
          .split('|')
          .map((header) => header.trim())
          .filter((header) => header.length > 0),
      ];

      const rows = dataLines.map((line) => {
        return line
          .trim()
          .split('|')
          .map((cell) => cell.trim())
          .filter((cell) => cell.length > 0);
      });

      const rowsWithContent = rows.filter((row) =>
        row.some((cell) => cell !== '')
      );

      this.logger.log(
        `Table parsed: ${headers.length} header rows, ${rowsWithContent.length} data rows`
      );
      return { headers, rows: rowsWithContent };
    }

    // All lines before separator are headers
    const headerLines = tableLines.slice(0, separatorLineIndex);
    // All lines after separator are data
    const dataLines = tableLines.slice(separatorLineIndex + 1);

    // Parse header rows
    const headers = headerLines.map((line) =>
      line
        .trim()
        .split('|')
        .map((header) => header.trim())
        .filter((header) => header.length > 0)
    );

    // Parse data rows
    const rows = dataLines.map((line) => {
      return line
        .trim()
        .split('|')
        .map((cell) => cell.trim())
        .filter((cell) => cell.length > 0);
    });

    // Remove rows where all cells are empty
    const rowsWithContent = rows.filter((row) =>
      row.some((cell) => cell !== '')
    );

    this.logger.log(
      `Table parsed: ${headers.length} header rows, ${rowsWithContent.length} data rows`
    );
    return { headers, rows: rowsWithContent };
  }

  async parseDocumentToMarkdown(
    path: string,
    premiumMode?: boolean
  ): Promise<DocumentChunkGenerated[]> {
    this.logger.log(
      `Starting to parse document: ${path}, premium mode: ${premiumMode}`
    );

    if (path.endsWith('.pdf')) {
      this.logger.log(`Detected PDF document: ${path}`);
      return this.parsePageBasedPDFToMarkdown(path, premiumMode);
    } else if (
      path.endsWith('.xlsx') ||
      path.endsWith('.xls') ||
      path.endsWith('.csv')
    ) {
      this.logger.log(`Detected spreadsheet document: ${path}`);
      return this.parseSpreadsheetToMarkdown(path);
    } else {
      this.logger.log(`Unsupported file type: ${path}`);
      throw new Error('Unsupported file type');
    }
  }

  private async processCurrentChunk({
    currentChunk,
    currentChunkTokens,
    currentChunkPageNumbers,
    chunkDataArray,
    chunkHeadingHierarchy,
    sourceDocumentName,
    mainSections,
    chunkNumber,
  }: {
    currentChunk: string;
    currentChunkTokens: number;
    currentChunkPageNumbers: Set<number>;
    chunkDataArray: DocumentChunkGenerated[];
    chunkHeadingHierarchy: string[];
    sourceDocumentName: string;
    mainSections: string[];
    chunkNumber: number;
  }): Promise<number> {
    this.logger.log(
      `Processing chunk #${chunkNumber}, ${currentChunkTokens} tokens, pages: ${Array.from(currentChunkPageNumbers).join(',')}`
    );

    // Generate page number metadata
    const pageNumbersArray = Array.from(currentChunkPageNumbers).sort(
      (a, b) => a - b
    );
    let pageNumberMetadata = '';
    if (pageNumbersArray.length === 1) {
      pageNumberMetadata = `${pageNumbersArray[0]}`;
    } else {
      const isConsecutive = pageNumbersArray.every(
        (num, idx) => idx === 0 || num === pageNumbersArray[idx - 1] + 1
      );
      if (isConsecutive) {
        pageNumberMetadata = `${pageNumbersArray[0]}-${
          pageNumbersArray[pageNumbersArray.length - 1]
        }`;
      } else {
        pageNumberMetadata = pageNumbersArray.join(',');
      }
    }

    if (currentChunkTokens > 3000) {
      const htmlTableInfo = this.analyzeHtmlTables(currentChunk);
      this.logger.log(
        `Large chunk contains HTML tables: ${htmlTableInfo.hasTables}, table percentage: ${htmlTableInfo.tablePercentage.toFixed(2)}`
      );

      // If the chunk contains HTML tables, we need to be careful about splitting
      if (htmlTableInfo.hasTables && htmlTableInfo.tablePercentage > 0) {
        chunkDataArray.push({
          text: currentChunk,
          metadata: {
            headings: [...chunkHeadingHierarchy],
            sourceDocumentName: sourceDocumentName,
            mainSections: [...mainSections],
            pageNumber: pageNumberMetadata,
            chunkNumber: chunkNumber,
          },
        });
        chunkNumber++;
      } else {
        // Split the chunk if it's too large
        const markdownTextSplitter = new MarkdownTextSplitter({
          chunkSize: 3000,
          chunkOverlap: 0,
          keepSeparator: true,
        });
        const splitChunks = await markdownTextSplitter.splitText(currentChunk);

        for (const splitChunk of splitChunks) {
          chunkDataArray.push({
            text: splitChunk,
            metadata: {
              headings: [...chunkHeadingHierarchy],
              sourceDocumentName: sourceDocumentName,
              mainSections: [...mainSections],
              pageNumber: pageNumberMetadata,
              chunkNumber: chunkNumber,
            },
          });
          chunkNumber++;
        }
      }
    } else {
      this.logger.log(`Adding regular chunk #${chunkNumber}`);
      chunkDataArray.push({
        text: currentChunk,
        metadata: {
          headings: [...chunkHeadingHierarchy],
          sourceDocumentName: sourceDocumentName,
          mainSections: [...mainSections],
          pageNumber: pageNumberMetadata,
          chunkNumber: chunkNumber,
        },
      });
      chunkNumber++;
    }

    this.logger.log(
      `Finished processing chunk, new chunk number: ${chunkNumber}`
    );
    return chunkNumber;
  }

  /**
   * Enhances HTML elements with CSS classes for better rendering
   * @param text The HTML content to enhance
   * @returns The HTML content with added CSS classes
   */
  private enhanceHtmlWithStyles(text: string): string {
    this.logger.log(`Enhancing HTML with styles, input length: ${text.length}`);
    const result = text
      .replace(/<ul(?!\s+class=)/g, '<ul class="ml-6 list-disc"')
      .replace(/<ol(?!\s+class=)/g, '<ol class="ml-6 list-decimal"')
      .replace(
        /<table(?!\s+class=)/g,
        '<table class="table-auto border-collapse w-full text-left"'
      )
      .replace(/<thead(?!\s+class=)/g, '<thead class="bg-slate-200"')
      .replace(
        /<th(?!\s+class=|\s+colspan|\s+rowspan)/g,
        '<th class="px-4 py-2 font-medium text-slate-700"'
      )
      .replace(/<tbody(?!\s+class=)/g, '<tbody class="bg-white"')
      .replace(
        /<td(?!\s+class=|\s+colspan|\s+rowspan)/g,
        '<td class="border px-4 py-2"'
      );

    this.logger.log(
      `HTML enhanced with styles, output length: ${result.length}`
    );
    return result;
  }

  /**
   * Analyze HTML tables in a text chunk
   */
  private analyzeHtmlTables(text: string): {
    hasTables: boolean;
    tableCount: number;
    tablePercentage: number;
    tableRanges: Array<{ start: number; end: number }>;
  } {
    this.logger.log(`Analyzing HTML tables in text of length: ${text.length}`);

    const result = {
      hasTables: false,
      tableCount: 0,
      tablePercentage: 0,
      tableRanges: [] as Array<{ start: number; end: number }>,
    };

    // Find all table opening and closing tags
    const openTags: number[] = [];
    const tableRanges: Array<{ start: number; end: number }> = [];

    let tableOpens = 0;
    let tableCloses = 0;

    // Use regex to find all opening and closing table tags
    const openTagRegex = /<table[^>]*>/gi;
    const closeTagRegex = /<\/table>/gi;

    let match: RegExpExecArray;
    while ((match = openTagRegex.exec(text)) !== null) {
      openTags.push(match.index);
      tableOpens++;
    }

    while ((match = closeTagRegex.exec(text)) !== null) {
      if (openTags.length > 0) {
        const start = openTags.shift()!;
        const end = match.index + 8; // Length of </table>
        tableRanges.push({ start, end });
        tableCloses++;
      }
    }

    // Calculate the total length of text covered by tables
    let totalTableLength = 0;
    for (const range of tableRanges) {
      totalTableLength += range.end - range.start;
    }

    result.hasTables = tableRanges.length > 0;
    result.tableCount = tableRanges.length;
    result.tablePercentage =
      text.length > 0 ? totalTableLength / text.length : 0;
    result.tableRanges = tableRanges;

    this.logger.log(
      `Table analysis complete: found ${result.tableCount} tables, coverage: ${(result.tablePercentage * 100).toFixed(2)}%`
    );
    return result;
  }

  /**
   * Pre-processes text lines to ensure HTML tables stay together
   * @param lines Array of text lines
   * @returns Array of processed lines with tables joined
   */
  private joinLinesWithHtmlTables(lines: string[]): string[] {
    this.logger.log(
      `Joining lines with HTML tables, processing ${lines.length} lines`
    );

    const joinedLines: string[] = [];
    let currentTableLines: string[] = [];
    let tableLevel = 0;

    for (const line of lines) {
      // Count opening table tags in this line
      const openTagMatches = line.match(/<table[^>]*>/gi);
      const openTagCount = openTagMatches ? openTagMatches.length : 0;

      // Count closing table tags in this line
      const closeTagMatches = line.match(/<\/table>/gi);
      const closeTagCount = closeTagMatches ? closeTagMatches.length : 0;

      // Update table nesting level
      tableLevel += openTagCount - closeTagCount;

      // Ensure tableLevel doesn't go below 0 (malformed HTML)
      tableLevel = Math.max(0, tableLevel);

      // Add the line to current table collection or directly to output
      if (tableLevel > 0 || openTagCount > 0) {
        // We're inside a table or just started one
        currentTableLines.push(line);

        // If we've just exited all tables, join and add the combined lines
        if (tableLevel === 0) {
          joinedLines.push(currentTableLines.join('\n'));
          currentTableLines = [];
        }
      } else {
        // Regular line, not in a table
        joinedLines.push(line);
      }
    }

    // Handle any remaining table content (in case of unbalanced tags)
    if (currentTableLines.length > 0) {
      joinedLines.push(currentTableLines.join('\n'));
    }

    this.logger.log(
      `Joined HTML tables, resulting in ${joinedLines.length} lines`
    );
    return joinedLines;
  }

  /**
   * Process PDF document by page with overlapping content for context
   */
  async parsePageBasedPDFToMarkdown(
    filePath: string,
    premiumMode?: boolean
  ): Promise<DocumentChunkGenerated[]> {
    this.logger.log(
      `Parsing PDF document by page with overlap: ${filePath}, premium mode: ${premiumMode}`
    );

    const allDocumentMarkdown =
      await parseDocumentWithAzureDocumentIntelligence({
        filePath,
        features: premiumMode ? ['ocrHighResolution'] : [],
      });
    this.logger.log(
      `Received parsed document from Azure Document Intelligence`
    );

    const sourceDocumentName = filePath.split('/').pop();

    // Split content by page separator
    const pages = allDocumentMarkdown.text.split('\n<!-- PageBreak -->\n');
    this.logger.log(`Document split into ${pages.length} pages`);

    const chunkDataArray: DocumentChunkGenerated[] = [];
    let chunkNumber = 1;

    // Process each page with overlap
    for (let pageIndex = 0; pageIndex < pages.length; pageIndex++) {
      // Current page content
      let pageContent = pages[pageIndex];
      const currentPageNumber = pageIndex + 1;

      // Extract heading hierarchy for this page
      const headingHierarchy: string[] = [];
      const mainSections: string[] = [];
      const headingRegex = /^(#{1,6})\s+(.*)$/gm;
      let match;

      while ((match = headingRegex.exec(pageContent)) !== null) {
        const headingLevel = match[1].length;
        const headingText = match[2].trim();

        // Update heading hierarchy
        while (headingHierarchy.length >= headingLevel) {
          headingHierarchy.pop();
        }
        headingHierarchy.push(headingText);

        if (headingLevel === 1 && !mainSections.includes(headingText)) {
          mainSections.push(headingText);
        }
      }

      // Add content from previous page (approximately 100 tokens)
      // if (pageIndex > 0) {
      //   const prevPage = pages[pageIndex - 1];
      //   // Get approximately 100 tokens from the end of the previous page
      //   // ~4 chars per token, aiming for ~400 characters
      //   const overlapSize = 400;
      //   const prevPageOverlap =
      //     prevPage.length > overlapSize
      //       ? prevPage.substring(prevPage.length - overlapSize)
      //       : prevPage;

      //   // Add a separator to indicate overlapping content
      //   pageContent = `<!-- Overlap from previous page -->\n${prevPageOverlap}\n<!-- Current page content -->\n${pageContent}`;
      // }

      // Add content from next page (approximately 100 tokens)
      // if (pageIndex < pages.length - 1) {
      //   const nextPage = pages[pageIndex + 1];
      //   // Get approximately 100 tokens from the beginning of the next page
      //   // ~4 chars per token, aiming for ~400 characters
      //   const overlapSize = 400;
      //   const nextPageOverlap =
      //     nextPage.length > overlapSize
      //       ? nextPage.substring(0, overlapSize)
      //       : nextPage;

      //   // Add a separator to indicate overlapping content
      //   pageContent = `${pageContent}\n<!-- Overlap from next page -->\n${nextPageOverlap}`;
      // }

      // Create page chunk
      chunkDataArray.push({
        text: pageContent,
        metadata: {
          headings: [...headingHierarchy],
          sourceDocumentName: sourceDocumentName,
          mainSections: [...mainSections],
          pageNumber: currentPageNumber.toString(),
          chunkNumber: chunkNumber,
        },
      });

      chunkNumber++;
    }

    this.logger.log(
      `Page-based PDF parsing complete, generated ${chunkDataArray.length} chunks`
    );
    return chunkDataArray;
  }

  async parsePDFDocumentToMarkdown(
    filePath: string,
    premiumMode?: boolean
  ): Promise<DocumentChunkGenerated[]> {
    this.logger.log(
      `Parsing PDF document to markdown: ${filePath}, premium mode: ${premiumMode}`
    );

    const allDocumentMarkdown = await parseDocumentWithLlamaparseApi({
      filePath,
      premiumMode,
      pageSeparator: '\n<PAGE>=================</PAGE>\n',
    });
    this.logger.log(
      `Received parsed document from Llamaparse API, text length: ${allDocumentMarkdown.text.length}`
    );

    // return [
    //   {
    //     text: allDocumentMarkdown,
    //     metadata: {
    //       headings: [],
    //       sourceDocumentName: path.split('/').pop(),
    //       mainSections: [],
    //       pageNumber: '1',
    //       chunkNumber: 1,
    //     },
    //   },
    // ];

    const sourceDocumentName = filePath.split('/').pop();

    let lines = allDocumentMarkdown.text.split('\n');
    this.logger.log(`Document split into ${lines.length} lines`);

    lines = this.joinLinesWithHtmlTables(lines);
    this.logger.log(`After joining HTML tables: ${lines.length} lines`);

    const headingHierarchy: string[] = [];
    const mainSections: string[] = [];
    const chunkDataArray: DocumentChunkGenerated[] = [];
    let currentChunk = '';
    let currentChunkTokens = 0;
    let pageNumber = 1; // Start from page 1
    let chunkNumber = 1;
    let currentChunkPageNumbers = new Set<number>(); // Track page numbers
    let chunkHeadingHierarchy: string[] = [];
    const headingRegex = /^(#{1,6})\s+(.*)$/;
    const pageSeparatorRegex = /^\s*<PAGE>=================<\/PAGE>\s*$/;
    // Regex to detect table lines
    const tableLineRegex = /^\s*\|.*\|\s*$/;
    // Variables to handle tables
    let inTable = false;
    let tableLines: string[] = [];
    const tablePageNumbers = new Set<number>();

    // Loop over lines
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      if (pageSeparatorRegex.test(line)) {
        pageNumber++;
        // Add page number to current tables or chunks
        if (inTable) {
          tablePageNumbers.add(pageNumber);
        } else {
          currentChunkPageNumbers.add(pageNumber);
        }
        continue; // Skip adding the page separator to the chunk
      }

      // Handle tables
      if (inTable) {
        if (tableLineRegex.test(line)) {
          // Line is part of the table
          tableLines.push(line);
          tablePageNumbers.add(pageNumber);
          continue; // Skip further processing for this line
        } else {
          // Table has ended
          inTable = false;

          // Process the table
          const { headers, rows } = this.parseMarkdownTable(tableLines);

          const tableMarkdown = this.createMarkdownTable(headers, rows);
          const tableTokenCount = this.countTokens(tableMarkdown);

          // Generate page number metadata for table
          const pageNumbersArray = Array.from(tablePageNumbers).sort(
            (a, b) => a - b
          );
          let pageNumberMetadata = '';
          if (pageNumbersArray.length === 1) {
            pageNumberMetadata = `${pageNumbersArray[0]}`;
          } else {
            const isConsecutive = pageNumbersArray.every(
              (num, idx) => idx === 0 || num === pageNumbersArray[idx - 1] + 1
            );
            if (isConsecutive) {
              pageNumberMetadata = `${pageNumbersArray[0]}-${
                pageNumbersArray[pageNumbersArray.length - 1]
              }`;
            } else {
              pageNumberMetadata = pageNumbersArray.join(',');
            }
          }

          if (tableTokenCount > 500) {
            // Table is too big, split it
            const splitTables = this.splitTableByTokenCount(headers, rows, 500);

            for (const splitTable of splitTables) {
              chunkDataArray.push({
                text: splitTable,
                metadata: {
                  headings: [...chunkHeadingHierarchy],
                  sourceDocumentName: sourceDocumentName,
                  mainSections: [...mainSections],
                  pageNumber: pageNumberMetadata,
                  chunkNumber: chunkNumber,
                },
              });
              chunkNumber++; // Increment chunkNumber
            }
          } else {
            // Table is acceptable size
            chunkDataArray.push({
              text: tableMarkdown,
              metadata: {
                headings: [...chunkHeadingHierarchy],
                sourceDocumentName: sourceDocumentName,
                mainSections: [...mainSections],
                pageNumber: pageNumberMetadata,
                chunkNumber: chunkNumber,
              },
            });
            chunkNumber++; // Increment chunkNumber
          }

          // Reset tableLines and tablePageNumbers
          tableLines = [];
          tablePageNumbers.clear();

          // Since we've processed the table, continue processing the current line
        }
      }

      // Check if line is the start of a table
      if (!inTable && tableLineRegex.test(line)) {
        // Line is the start of a table
        inTable = true;
        tableLines.push(line);
        tablePageNumbers.add(pageNumber);
        // If we were building a chunk, process it before starting the table
        if (currentChunk !== '') {
          chunkNumber = await this.processCurrentChunk({
            currentChunk,
            currentChunkTokens,
            currentChunkPageNumbers,
            chunkDataArray,
            chunkHeadingHierarchy,
            sourceDocumentName,
            mainSections,
            chunkNumber,
          });
          currentChunk = '';
          currentChunkTokens = 0;
          currentChunkPageNumbers.clear();
        }
        continue; // Skip further processing for this line
      }

      // Handle headings
      const headingMatch = line.match(headingRegex);

      if (currentChunk === '') {
        // Starting a new chunk
        // Capture the heading hierarchy for the chunk
        chunkHeadingHierarchy = headingHierarchy.slice();
        currentChunkPageNumbers = new Set<number>();
      }

      if (headingMatch) {
        const headingLevel = headingMatch[1].length;
        const headingText = headingMatch[2].trim();

        // Pop heading hierarchy only if new heading is at a higher or same level
        while (headingHierarchy.length >= headingLevel) {
          headingHierarchy.pop();
        }
        headingHierarchy.push(headingText);

        if (headingLevel === 1 && !mainSections.includes(headingText)) {
          mainSections.push(headingText);
        }
      }

      // Add line to currentChunk
      currentChunk += line + '\n';
      currentChunkTokens = this.countTokens(currentChunk);
      currentChunkPageNumbers.add(pageNumber);

      if (currentChunkTokens >= 500) {
        // Process currentChunk
        chunkNumber = await this.processCurrentChunk({
          currentChunk,
          currentChunkTokens,
          currentChunkPageNumbers,
          chunkDataArray,
          chunkHeadingHierarchy,
          sourceDocumentName,
          mainSections,
          chunkNumber,
        });
        // Reset currentChunk and tokens
        currentChunk = '';
        currentChunkTokens = 0;
        currentChunkPageNumbers.clear();
        // Update chunkHeadingHierarchy
        chunkHeadingHierarchy = headingHierarchy.slice();
      }
    }

    // Process any remaining table
    if (inTable) {
      this.logger.log(`Processing final table with ${tableLines.length} lines`);
      const { headers, rows } = this.parseMarkdownTable(tableLines);

      const tableMarkdown = this.createMarkdownTable(headers, rows);
      const tableTokenCount = this.countTokens(tableMarkdown);

      // Generate page number metadata for table
      const pageNumbersArray = Array.from(tablePageNumbers).sort(
        (a, b) => a - b
      );
      let pageNumberMetadata = '';
      if (pageNumbersArray.length === 1) {
        pageNumberMetadata = `${pageNumbersArray[0]}`;
      } else {
        const isConsecutive = pageNumbersArray.every(
          (num, idx) => idx === 0 || num === pageNumbersArray[idx - 1] + 1
        );
        if (isConsecutive) {
          pageNumberMetadata = `${pageNumbersArray[0]}-${
            pageNumbersArray[pageNumbersArray.length - 1]
          }`;
        } else {
          pageNumberMetadata = pageNumbersArray.join(',');
        }
      }

      if (tableTokenCount > 500) {
        // Table is too big, split it
        const splitTables = this.splitTableByTokenCount(headers, rows, 500);

        for (const splitTable of splitTables) {
          chunkDataArray.push({
            text: splitTable,
            metadata: {
              headings: [...chunkHeadingHierarchy],
              sourceDocumentName: sourceDocumentName,
              mainSections: [...mainSections],
              pageNumber: pageNumberMetadata,
              chunkNumber: chunkNumber,
            },
          });
          chunkNumber++; // Increment chunkNumber
        }
      } else {
        // Table is acceptable size
        chunkDataArray.push({
          text: tableMarkdown,
          metadata: {
            headings: [...chunkHeadingHierarchy],
            sourceDocumentName: sourceDocumentName,
            mainSections: [...mainSections],
            pageNumber: pageNumberMetadata,
            chunkNumber: chunkNumber,
          },
        });
        chunkNumber++; // Increment chunkNumber
      }
    }

    // Add any remaining chunk
    if (currentChunk) {
      this.logger.log(
        `Processing final text chunk of ${currentChunkTokens} tokens`
      );
      chunkNumber = await this.processCurrentChunk({
        currentChunk,
        currentChunkTokens,
        currentChunkPageNumbers,
        chunkDataArray,
        chunkHeadingHierarchy,
        sourceDocumentName,
        mainSections,
        chunkNumber,
      });
    }

    this.logger.log(
      `PDF parsing complete, generated ${chunkDataArray.length} chunks`
    );
    return chunkDataArray;
  }

  private createMarkdownTable(headers: any[][], rows: any[][]): string {
    this.logger.log(
      `Creating markdown table with ${headers.length} header rows and ${rows.length} data rows`
    );

    if (headers.length === 0 || rows.length === 0) return '';

    let markdown = '';

    // Find the maximum number of columns across all headers and rows
    const maxColumns = Math.max(
      ...headers.map((row) => row.length),
      ...rows.map((row) => row.length)
    );

    // Extend all header rows to match maximum columns
    const extendedHeaders = headers.map((headerRow) => {
      const extended = [...headerRow];
      while (extended.length < maxColumns) {
        extended.push('');
      }
      return extended;
    });

    // Determine which columns to include (have data in any header or data row)
    const columnsToInclude: number[] = [];

    for (let colIndex = 0; colIndex < maxColumns; colIndex++) {
      let hasData = false;

      // Check header rows
      for (const headerRow of extendedHeaders) {
        const cell = headerRow[colIndex];
        if (cell !== undefined && cell !== null && String(cell).trim() !== '') {
          hasData = true;
          break;
        }
      }

      // Check data rows if needed
      if (!hasData) {
        for (const row of rows) {
          const cell = row[colIndex];
          if (
            cell !== undefined &&
            cell !== null &&
            String(cell).trim() !== ''
          ) {
            hasData = true;
            break;
          }
        }
      }

      if (hasData) {
        columnsToInclude.push(colIndex);
      }
    }

    // If less than 2 columns have data, return an empty string
    if (columnsToInclude.length < 2) {
      return '';
    }

    // Special case: Only one header row - standard markdown table format
    if (extendedHeaders.length === 1) {
      // Create the single header row
      const filteredRow = columnsToInclude.map((colIndex) =>
        this.escapeCell(extendedHeaders[0][colIndex])
      );
      markdown += '| ' + filteredRow.join(' | ') + ' |\n';

      // Create separator row
      markdown += '| ' + columnsToInclude.map(() => '---').join(' | ') + ' |\n';

      // Create data rows
      for (const row of rows) {
        const rowData = columnsToInclude.map((colIndex) => {
          const cellData =
            row[colIndex] !== undefined && row[colIndex] !== null
              ? row[colIndex]
              : '';
          return this.escapeCell(cellData);
        });
        markdown += '| ' + rowData.join(' | ') + ' |\n';
      }
    }
    // Multiple header rows - use a different approach to make them visually distinct
    else {
      // Use the first header row as the actual markdown header
      const firstHeaderRow = extendedHeaders[0];
      const filteredFirstHeaderRow = columnsToInclude.map((colIndex) =>
        this.escapeCell(firstHeaderRow[colIndex])
      );
      markdown += '| ' + filteredFirstHeaderRow.join(' | ') + ' |\n';

      // Create separator row
      markdown += '| ' + columnsToInclude.map(() => '---').join(' | ') + ' |\n';

      // Add remaining header rows as bold data rows
      for (let i = 1; i < extendedHeaders.length; i++) {
        const headerRow = extendedHeaders[i];
        const filteredRow = columnsToInclude.map((colIndex) => {
          const cell = headerRow[colIndex];
          const cellStr = this.escapeCell(cell);
          // Make header cells bold
          return cellStr ? `**${cellStr}**` : '';
        });
        markdown += '| ' + filteredRow.join(' | ') + ' |\n';
      }

      // Create data rows
      for (const row of rows) {
        const rowData = columnsToInclude.map((colIndex) => {
          const cellData =
            row[colIndex] !== undefined && row[colIndex] !== null
              ? row[colIndex]
              : '';
          return this.escapeCell(cellData);
        });
        markdown += '| ' + rowData.join(' | ') + ' |\n';
      }
    }

    this.logger.log(`Markdown table created, length: ${markdown.length}`);
    return markdown;
  }

  private escapeCell(cell: any): string {
    let cellStr = String(cell);

    // Escape pipe characters
    cellStr = cellStr.replace(/\|/g, '\\|');

    // Replace newline characters with a space
    cellStr = cellStr.replace(/\r?\n|\r/g, ' ');

    // Trim whitespace
    cellStr = cellStr.trim();

    return cellStr;
  }

  private splitTableByTokenCount(
    headers: any[][],
    rows: any[][],
    maxTokens: number
  ): string[] {
    this.logger.log(
      `Splitting table by token count, threshold: ${maxTokens}, rows: ${rows.length}`
    );

    const tables: string[] = [];

    // Adjust headers for consistent column count
    const maxColumns = Math.max(
      ...headers.map((row) => row.length),
      ...rows.map((row) => row.length)
    );

    const extendedHeaders = headers.map((headerRow) => {
      const extended = [...headerRow];
      while (extended.length < maxColumns) {
        extended.push('');
      }
      return extended;
    });

    this.splitRowsRecursively({
      headers: extendedHeaders,
      rows: rows,
      start: 0,
      end: rows.length,
      maxTokens: maxTokens,
      result: tables,
      countTokens: this.countTokens.bind(this),
      createMarkdownTable: this.createMarkdownTable.bind(this),
    });

    this.logger.log(
      `Table splitting complete, generated ${tables.length} table chunks`
    );
    return tables;
  }

  private splitRowsRecursively({
    headers,
    rows,
    start,
    end,
    maxTokens,
    result,
    countTokens,
    createMarkdownTable,
  }: {
    headers: any[][];
    rows: any[][];
    start: number;
    end: number;
    maxTokens: number;
    result: string[];
    countTokens: (md: string) => number;
    createMarkdownTable: (headers: any[][], rows: any[][]) => string;
  }) {
    this.logger.log(
      `Splitting rows recursively: rows ${start}-${end}, max tokens: ${maxTokens}`
    );

    const chunkRows = rows.slice(start, end);
    const markdown = createMarkdownTable(headers, chunkRows);
    if (!markdown) {
      return; // Skip empty
    }
    const tokenCount = countTokens(markdown);
    if (tokenCount > maxTokens && end - start > 1) {
      const mid = Math.floor((start + end) / 2);
      this.splitRowsRecursively({
        headers,
        rows,
        start,
        end: mid,
        maxTokens,
        result,
        countTokens,
        createMarkdownTable,
      });
      this.splitRowsRecursively({
        headers,
        rows,
        start: mid,
        end,
        maxTokens,
        result,
        countTokens,
        createMarkdownTable,
      });
    } else {
      result.push(markdown);
    }
  }

  private detectHeaderRows(data: any[][]): number {
    this.logger.log(
      `Detecting header rows with heuristic method, ${data.length} rows total`
    );

    if (data.length <= 1) return 1;

    // Look at the first few rows (up to 5) to identify potential headers
    const maxRowsToCheck = Math.min(5, data.length - 1);
    let headerRowCount = 1; // Default to first row as header

    for (let i = 1; i < maxRowsToCheck; i++) {
      const row = data[i];

      // If row is empty or has only a few values, it's likely not a header
      const nonEmptyCells = row.filter(
        (cell) =>
          cell !== undefined && cell !== null && String(cell).trim() !== ''
      ).length;

      if (nonEmptyCells < 2) continue;

      // Check if the row has characteristics of headers:
      // 1. Mostly text values rather than numbers
      // 2. Short text values (typical for headers)
      const textCells = row.filter(
        (cell) =>
          cell !== undefined &&
          cell !== null &&
          typeof cell === 'string' &&
          String(cell).trim() !== '' &&
          String(cell).length < 50 &&
          isNaN(Number(cell))
      ).length;

      const isLikelyHeader = textCells >= nonEmptyCells * 0.7;

      if (isLikelyHeader) {
        headerRowCount = i + 1;
      } else {
        // Once we find a row that doesn't look like a header, stop checking
        break;
      }
    }

    this.logger.log(
      `Heuristic header detection complete, found ${headerRowCount} header rows`
    );
    return headerRowCount;
  }

  async detectHeaderRowsWithLLM(tableData: any[][]): Promise<number> {
    this.logger.log(
      `Detecting header rows with LLM, ${tableData.length} rows total`
    );

    if (tableData.length <= 1) return 1;

    // Filter out completely empty rows that might appear during page transitions
    const filteredTableData = tableData.filter((row) =>
      row.some(
        (cell) =>
          cell !== undefined && cell !== null && String(cell).trim() !== ''
      )
    );

    if (filteredTableData.length <= 1) return 1;

    // Convert table data to a sample for LLM analysis (first 8 rows max)
    const rowSample = filteredTableData
      .slice(0, 8)
      .map((row) =>
        row
          .map((cell) =>
            cell !== null && cell !== undefined ? String(cell) : ''
          )
          .join('\t')
      )
      .join('\n');

    // Create a prompt that clearly defines the header detection task
    const messages: any[] = [
      {
        role: 'system',
        content:
          'You are an expert in spreadsheet structure analysis. Your task is to identify header rows in tabular data.',
      },
      {
        role: 'user',
        content: `Analyze this tabular data and determine how many rows should be considered as headers.
        
        Header rows typically:
        - Contain column titles or category labels
        - Use shorter text rather than lengthy content
        - Appear at the top of the table
        - May form a hierarchical structure (in multi-row headers)
        
        Return ONLY a single number representing the count of header rows as JSON.
        Example:
        {
          "headerRowCount": 1
        }
        
        Table data (tab-separated):
        ${rowSample}`,
      },
    ];

    try {
      const result = await this.llmRateLimitService.handleRequest({
        model: LLM_MODELS['gpt-4o'],
        messages,
        json: true,
        temperature: 0,
      });

      const headerCount = result.response.headerRowCount;
      if (headerCount) {
        this.logger.log(`LLM detected ${headerCount} header rows`);
        return headerCount;
      }

      this.logger.warn(
        'LLM response could not be parsed as a valid header count, falling back to heuristic method'
      );
      const fallbackCount = this.detectHeaderRows(filteredTableData);
      this.logger.log(
        `Fallback header detection found ${fallbackCount} header rows`
      );
      return fallbackCount;
    } catch (error: any) {
      this.logger.error(
        `Error using LLM for header detection: ${error.message}, falling back to heuristic method`
      );
      const fallbackCount = this.detectHeaderRows(filteredTableData);
      this.logger.log(
        `Fallback header detection found ${fallbackCount} header rows`
      );
      return fallbackCount;
    }
  }

  /**
   * Enhanced spreadsheet parsing with improved table detection for hidden rows/columns
   */
  private async parseSpreadsheetToMarkdown(
    path: string,
    maxTokens: number = 3000
  ): Promise<DocumentChunkGenerated[]> {
    this.logger.log(
      `Enhanced parsing of spreadsheet to markdown: ${path}, max tokens: ${maxTokens}`
    );

    // Read the workbook with full options to preserve all formatting
    const workbook = XLSX.readFile(path, {
      cellStyles: true,
      cellNF: true,
      cellDates: true,
      cellFormula: true,
      sheetStubs: true,
      sheetRows: 0,
    });

    this.logger.log(
      `Loaded workbook: ${path}, sheets: ${workbook.SheetNames.join(', ')}`
    );

    const sourceDocumentName = path.split('/').pop();
    const chunkDataArray: DocumentChunkGenerated[] = [];
    let pageNumber = 0;
    let globalChunkNumber = 1; // Track chunk number continuously across all tables

    for (const sheetName of workbook.SheetNames) {
      pageNumber++; // Increment page number for each sheet
      this.logger.log(
        `Processing sheet ${pageNumber}/${workbook.SheetNames.length}: "${sheetName}"`
      );

      const sheet = workbook.Sheets[sheetName];

      // 1. Detect hidden rows and columns
      const { hiddenRows, hiddenCols } = this.detectHiddenRowsAndColumns(sheet);
      this.logger.log(
        `Sheet "${sheetName}" has ${hiddenRows.size} hidden rows and ${hiddenCols.size} hidden columns`
      );

      // 2. Detect potential tables using available metadata
      const metadataTables = this.detectNativeTables(workbook, sheetName);

      // 3. Store original merged cells before unmerging
      const originalMerges = sheet['!merges'] ? [...sheet['!merges']] : [];

      // 4. Apply enhanced unmerging
      // this.enhancedUnmergeCells(sheet);

      // 5. Get the raw data with all cells
      const rawJsonData = XLSX.utils.sheet_to_json(sheet, {
        header: 1,
        defval: '', // Use empty string as default
        blankrows: true, // Keep blank rows
        raw: false, // Get formatted values
      }) as any[][];

      if (rawJsonData.length === 0) {
        this.logger.log(`Sheet "${sheetName}" is empty, skipping`);
        continue;
      }

      const processedJsonData = this.postProcessMergedCells(
        rawJsonData,
        originalMerges
      );

      // 6. Process data with and without hidden cells
      const dataWithHidden = this.normalizeSheetDataWithHiddenCells(
        processedJsonData,
        hiddenRows,
        hiddenCols,
        true // preserve hidden cells for analysis
      );

      // const dataWithoutHidden = this.normalizeSheetDataWithHiddenCells(
      //   processedJsonData,
      //   hiddenRows,
      //   hiddenCols,
      //   false // remove hidden cells for display
      // );

      // 7. Collect tables from various sources
      let tables: Array<{ headers: any[][]; rows: any[][] }> = [];

      // Process metadata-detected tables
      if (metadataTables.length > 0) {
        for (const tableInfo of metadataTables) {
          this.logger.log(
            `Processing metadata-detected table: ${tableInfo.name}`
          );

          // Extract the table based on its range
          const table = this.extractNativeTable(sheet, tableInfo.range);

          // Adjust for hidden rows/columns
          const adjustedTable = this.adjustTableForHiddenCells(
            table,
            tableInfo.range,
            hiddenRows,
            hiddenCols
          );

          tables.push(adjustedTable);
        }
      }

      // Fallback to our enhanced table detection if needed
      if (tables.length === 0) {
        this.logger.log(
          `No metadata tables found, using enhanced detection algorithms`
        );
        const detectedTables = await this.detectTablesInSheet(
          dataWithHidden,
          originalMerges
        );
        tables.push(...detectedTables);
      }

      // Deduplicate tables
      tables = this.deduplicateTables(tables);

      this.logger.log(
        `Sheet "${sheetName}" has ${tables.length} tables after processing`
      );

      // 8. Convert tables to markdown and add to chunks
      let tableNumber = 1;
      for (const table of tables) {
        // Skip tables that are too small or empty
        if (table.rows.length === 0 || table.headers.length === 0) {
          this.logger.log(`Skipping empty table #${tableNumber}`);
          tableNumber++;
          continue;
        }

        // Validate table has actual content
        const hasValidHeader = table.headers.some((headerRow) =>
          headerRow.some(
            (cell) =>
              cell !== undefined && cell !== null && String(cell).trim() !== ''
          )
        );

        const hasValidData = table.rows.some((row) =>
          row.some(
            (cell) =>
              cell !== undefined && cell !== null && String(cell).trim() !== ''
          )
        );

        if (!hasValidHeader || !hasValidData) {
          this.logger.log(
            `Skipping table #${tableNumber} (invalid headers or data)`
          );
          tableNumber++;
          continue;
        }

        // Create markdown tables and split them if needed
        const markdownTables = this.splitTableByTokenCount(
          table.headers,
          table.rows,
          maxTokens
        );

        this.logger.log(
          `Table #${tableNumber} split into ${markdownTables.length} chunks`
        );

        // Add each markdown table chunk
        for (const markdown of markdownTables) {
          // Skip empty markdown
          if (!markdown || markdown.trim() === '') continue;

          chunkDataArray.push({
            text: markdown,
            metadata: {
              sourceDocumentName: sourceDocumentName,
              headings: [sheetName, `Table ${tableNumber}`],
              pageNumber: String(pageNumber),
              chunkNumber: globalChunkNumber++,
              mainSections: [],
            },
          });
        }

        tableNumber++;
      }
    }

    this.logger.log(
      `Enhanced spreadsheet parsing complete, generated ${chunkDataArray.length} chunks`
    );
    return chunkDataArray;
  }

  /**
   * Normalize sheet data to ensure consistent dimensions and handle empty values
   */
  private normalizeSheetData(data: any[][]): any[][] {
    this.logger.log(`Normalizing sheet data: ${data.length} rows`);

    if (data.length === 0) return [];

    // Find the maximum number of columns
    const maxCols = Math.max(...data.map((row) => row.length));

    // Create a new normalized data array with consistent dimensions
    const normalizedData = data.map((row) => {
      // Create a new row with the maximum column count
      const normalizedRow = Array(maxCols).fill('');

      // Copy existing values
      for (let i = 0; i < row.length; i++) {
        const value = row[i];
        normalizedRow[i] = value !== undefined && value !== null ? value : '';
      }

      return normalizedRow;
    });

    this.logger.log(
      `Sheet normalized to consistent dimensions: ${data.length} rows x ${maxCols} columns`
    );
    return normalizedData;
  }

  /**
   * Extract data for a specific region from the normalized sheet data
   */
  private extractRegionData(
    data: any[][],
    region: {
      startRow: number;
      startCol: number;
      height: number;
      width: number;
    }
  ): any[][] {
    this.logger.log(
      `Extracting region data: starting at row ${region.startRow}, col ${region.startCol}, ${region.height}x${region.width}`
    );

    const { startRow, startCol, height, width } = region;

    // Create a new array for the region data
    const regionData: any[][] = [];

    for (let r = 0; r < height; r++) {
      const row: any[] = [];
      for (let c = 0; c < width; c++) {
        // Get data from the original sheet, handling out-of-bounds gracefully
        const dataRow = startRow + r;
        const dataCol = startCol + c;

        if (dataRow < data.length && dataCol < data[dataRow].length) {
          row.push(data[dataRow][dataCol]);
        } else {
          row.push('');
        }
      }
      regionData.push(row);
    }

    return regionData;
  }

  /**
   * Check if a value is likely a date
   */
  private isLikelyDate(value: any): boolean {
    if (typeof value !== 'string') return false;

    // Check common date formats
    const datePatterns = [
      /^\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4}$/, // MM/DD/YYYY, DD/MM/YYYY
      /^\d{4}[-\/]\d{1,2}[-\/]\d{1,2}$/, // YYYY/MM/DD
      /^\d{1,2}[-\/][A-Za-z]{3,9}[-\/]\d{2,4}$/, // DD-Mon-YYYY
      /^[A-Za-z]{3,9}[-\/]\d{1,2}[-\/]\d{2,4}$/, // Mon-DD-YYYY
    ];

    for (const pattern of datePatterns) {
      if (pattern.test(value)) return true;
    }

    // Try to parse as date
    const date = new Date(value);
    return !isNaN(date.getTime());
  }

  /**
   * Find empty rows and columns with adaptive thresholds based on data density
   */
  private findEmptyRowsAndColsAdaptive(
    data: any[][],
    headerRowCount = 2
  ): {
    emptyRows: boolean[];
    emptyCols: boolean[];
  } {
    this.logger.log(
      `Finding empty rows and columns adaptively, ${data.length} rows, header count: ${headerRowCount}`
    );

    if (data.length === 0) return { emptyRows: [], emptyCols: [] };

    const rowCount = data.length;
    const colCount = data[0].length;

    // Calculate data density to adjust emptiness threshold
    const totalCells = rowCount * colCount;
    let nonEmptyCells = 0;

    // Count non-empty cells
    for (let r = 0; r < rowCount; r++) {
      for (let c = 0; c < colCount; c++) {
        const value = data[r][c];
        if (
          value !== undefined &&
          value !== null &&
          String(value).trim() !== ''
        ) {
          nonEmptyCells++;
        }
      }
    }

    // Calculate data density as percentage
    const dataDensity = nonEmptyCells / totalCells;

    // Adaptive threshold: For sparse sheets (low density), be more lenient
    // For dense sheets, require more empty cells to consider a row/col empty
    const emptyThreshold = Math.min(0.9, Math.max(0.95, 1 - dataDensity));

    // Arrays to track empty/non-empty status
    const rowEmptyCounts = Array(rowCount).fill(0);
    const colEmptyCounts = Array(colCount).fill(0);

    // Count empty cells in each row and column
    for (let r = 0; r < rowCount; r++) {
      for (let c = 0; c < colCount; c++) {
        const value = data[r][c];
        const isEmpty =
          value === undefined || value === null || String(value).trim() === '';

        if (isEmpty) {
          rowEmptyCounts[r]++;
          colEmptyCounts[c]++;
        }
      }
    }

    // Determine empty rows and columns based on adaptive threshold
    const emptyRows = rowEmptyCounts.map(
      (count) => count / colCount >= emptyThreshold
    );
    const emptyCols = colEmptyCounts.map((count, colIndex) => {
      // Check if this column has any header content
      let hasHeaderContent = false;
      for (let r = 0; r < Math.min(headerRowCount, rowCount); r++) {
        const value = data[r][colIndex];
        if (
          value !== undefined &&
          value !== null &&
          String(value).trim() !== ''
        ) {
          console.log(
            `Header content check: ${String(value).trim()}`,
            headerRowCount
          );
          hasHeaderContent = true;
          break;
        }
      }

      // If column has header content, use a stricter threshold or never mark as empty
      if (hasHeaderContent) {
        // Option 1: Never mark a column with header content as empty
        return false;

        // Option 2: Use a much stricter threshold for columns with headers
        // return (count - headerRowCount) / (rowCount - headerRowCount) >= 0.98;
      }

      // Standard threshold for columns without header content
      return count / rowCount >= emptyThreshold;
    });

    this.logger.log(
      `Adaptive analysis complete: detected ${emptyRows.filter(Boolean).length} empty rows and ${emptyCols.filter(Boolean).length} empty columns`
    );
    return { emptyRows, emptyCols };
  }

  /**
   * Enhanced region identification using 8-way connectivity
   * This better handles diagonal relationships between cells
   */
  private identifyTableRegionsEnhanced(
    data: any[][],
    emptyRows: boolean[],
    emptyCols: boolean[]
  ): Array<{
    startRow: number;
    startCol: number;
    height: number;
    width: number;
  }> {
    this.logger.log(`Identifying table regions with 8-way connectivity`);

    const rowCount = data.length;
    const colCount = data[0].length;

    // Create a grid representing non-empty cells
    const grid: boolean[][] = Array(rowCount)
      .fill(false)
      .map((_, r) =>
        Array(colCount)
          .fill(false)
          .map((_, c) => {
            if (emptyRows[r] || emptyCols[c]) return false;
            const value = data[r][c];
            return !(
              value === undefined ||
              value === null ||
              String(value).trim() === ''
            );
          })
      );

    // Use connected component analysis with 8-way connectivity
    const visited: boolean[][] = Array(rowCount)
      .fill(false)
      .map(() => Array(colCount).fill(false));

    const regions: Array<{
      startRow: number;
      startCol: number;
      height: number;
      width: number;
    }> = [];

    // Direction vectors for 8-way connectivity
    const dr = [-1, -1, 0, 1, 1, 1, 0, -1];
    const dc = [0, 1, 1, 1, 0, -1, -1, -1];

    // Find all connected components (table regions)
    for (let r = 0; r < rowCount; r++) {
      for (let c = 0; c < colCount; c++) {
        if (grid[r][c] && !visited[r][c]) {
          // Start a new region
          let minRow = r,
            maxRow = r,
            minCol = c,
            maxCol = c;

          // Use BFS to explore the connected component
          const queue: [number, number][] = [[r, c]];
          visited[r][c] = true;

          while (queue.length > 0) {
            const [curR, curC] = queue.shift()!;

            // Update region boundaries
            minRow = Math.min(minRow, curR);
            maxRow = Math.max(maxRow, curR);
            minCol = Math.min(minCol, curC);
            maxCol = Math.max(maxCol, curC);

            // Explore neighbors with 8-way connectivity
            for (let i = 0; i < 8; i++) {
              const newR = curR + dr[i];
              const newC = curC + dc[i];

              // Check if the neighbor is valid and unvisited
              if (
                newR >= 0 &&
                newR < rowCount &&
                newC >= 0 &&
                newC < colCount &&
                grid[newR][newC] &&
                !visited[newR][newC]
              ) {
                queue.push([newR, newC]);
                visited[newR][newC] = true;
              }
            }
          }

          // Check if region is big enough to be a potential table
          // Minimum 2x2 size and at least 4 cells
          if (maxRow - minRow >= 1 && maxCol - minCol >= 1) {
            regions.push({
              startRow: minRow,
              startCol: minCol,
              height: maxRow - minRow + 1,
              width: maxCol - minCol + 1,
            });
          }
        }
      }
    }

    this.logger.log(
      `Table region identification complete, found ${regions.length} potential regions`
    );
    return regions;
  }

  /**
   * Apply hierarchical clustering to better group table regions
   * This helps handle complex layouts with many tables
   */
  private applyHierarchicalClustering(
    regions: Array<{
      startRow: number;
      startCol: number;
      height: number;
      width: number;
    }>,
    data: any[][]
  ): Array<{
    startRow: number;
    startCol: number;
    height: number;
    width: number;
  }> {
    this.logger.log(
      `Applying hierarchical clustering to ${regions.length} regions`
    );

    if (regions.length <= 1) return regions;

    // Calculate distances between all region pairs
    const distances: { i: number; j: number; distance: number }[] = [];

    for (let i = 0; i < regions.length; i++) {
      for (let j = i + 1; j < regions.length; j++) {
        const r1 = regions[i];
        const r2 = regions[j];

        // Calculate center points
        const r1CenterRow = r1.startRow + r1.height / 2;
        const r1CenterCol = r1.startCol + r1.width / 2;
        const r2CenterRow = r2.startRow + r2.height / 2;
        const r2CenterCol = r2.startCol + r2.width / 2;

        // Calculate Euclidean distance between centers
        const distance = Math.sqrt(
          Math.pow(r1CenterRow - r2CenterRow, 2) +
            Math.pow(r1CenterCol - r2CenterCol, 2)
        );

        distances.push({ i, j, distance });
      }
    }

    // Sort distances
    distances.sort((a, b) => a.distance - b.distance);

    // Create a union-find data structure for clustering
    const parent = Array.from({ length: regions.length }, (_, i) => i);

    const find = (x: number): number => {
      if (parent[x] !== x) {
        parent[x] = find(parent[x]);
      }
      return parent[x];
    };

    const union = (x: number, y: number): void => {
      parent[find(x)] = find(y);
    };

    // Dynamic clustering threshold based on table density and count
    const dataDensity = this.calculateDataDensity(data);
    const baseClusterThreshold = 5; // Base threshold for merging
    const clusterThreshold =
      baseClusterThreshold * (1 + Math.min(1, Math.max(0.2, dataDensity * 2)));

    // Merge regions based on distance threshold
    for (const { i, j, distance } of distances) {
      if (distance > clusterThreshold) continue;

      const r1 = regions[i];
      const r2 = regions[j];

      // Additional checks for alignment
      const horizontalOverlap =
        r1.startCol <= r2.startCol + r2.width &&
        r2.startCol <= r1.startCol + r1.width;

      const verticalOverlap =
        r1.startRow <= r2.startRow + r2.height &&
        r2.startRow <= r1.startRow + r1.height;

      // Only merge if there's alignment
      if (horizontalOverlap || verticalOverlap) {
        union(i, j);
      }
    }

    // Group regions by cluster
    const clusters = new Map<number, number[]>();

    for (let i = 0; i < regions.length; i++) {
      const clusterID = find(i);
      if (!clusters.has(clusterID)) {
        clusters.set(clusterID, []);
      }
      clusters.get(clusterID)!.push(i);
    }

    // Create merged regions
    const mergedRegions: typeof regions = [];

    for (const clusterIndices of clusters.values()) {
      if (clusterIndices.length === 1) {
        // Single region in cluster, keep as is
        mergedRegions.push(regions[clusterIndices[0]]);
        continue;
      }

      // Merge multiple regions in cluster
      let minRow = Infinity;
      let minCol = Infinity;
      let maxRow = -Infinity;
      let maxCol = -Infinity;

      for (const idx of clusterIndices) {
        const region = regions[idx];
        minRow = Math.min(minRow, region.startRow);
        minCol = Math.min(minCol, region.startCol);
        maxRow = Math.max(maxRow, region.startRow + region.height - 1);
        maxCol = Math.max(maxCol, region.startCol + region.width - 1);
      }

      mergedRegions.push({
        startRow: minRow,
        startCol: minCol,
        height: maxRow - minRow + 1,
        width: maxCol - minCol + 1,
      });
    }

    this.logger.log(
      `Clustering complete: merged into ${mergedRegions.length} regions`
    );
    return mergedRegions;
  }

  /**
   * Enhanced table detection algorithm with progressive consistency enforcement
   * Maintains table cohesion throughout large worksheets
   */
  private async detectTablesInSheet(
    data: any[][],
    merges: any[] = []
  ): Promise<{ headers: any[][]; rows: any[][] }[]> {
    this.logger.log(
      `Detecting tables in sheet: ${data.length} rows, ${merges.length} merged regions`
    );

    if (data.length === 0) return [];

    // Normalize the data
    const normalizedData = this.normalizeSheetData(data);

    // For very large sheets, use a progressive approach instead of trying to process all at once
    if (normalizedData.length > 300) {
      this.logger.log(
        `Large sheet detected (${normalizedData.length} rows), using progressive approach`
      );
      return this.detectTablesProgressively(normalizedData, merges);
    }

    // Standard processing for smaller sheets
    this.logger.log(
      `Using standard table detection for sheet with ${normalizedData.length} rows`
    );
    const { emptyRows, emptyCols } =
      this.findEmptyRowsAndColsAdaptive(normalizedData);
    const regions = this.identifyTableRegionsEnhanced(
      normalizedData,
      emptyRows,
      emptyCols
    );
    const mergedRegions = this.applyHierarchicalClustering(
      regions,
      normalizedData
    );

    // Convert regions to tables
    const tables = await this.regionsToTables(mergedRegions, normalizedData);
    this.logger.log(
      `Standard detection complete, found ${tables.length} tables`
    );
    return tables;
  }

  /**
   * Progressive table detection for large worksheets
   * Processes the sheet in overlapping chunks to maintain consistency
   */
  private async detectTablesProgressively(
    data: any[][],
    merges: any[] = []
  ): Promise<{ headers: any[][]; rows: any[][] }[]> {
    this.logger.log(
      `Starting progressive table detection on ${data.length} rows`
    );

    const rowCount = data.length;
    const chunkSize = 150; // Process in reasonable chunks
    const overlap = 50; // Ensure chunks overlap to maintain consistency

    // Analyze the first chunk to detect the primary table pattern
    const firstChunk = data.slice(0, Math.min(chunkSize, rowCount));
    const { emptyRows: firstEmptyRows, emptyCols: firstEmptyCols } =
      this.findEmptyRowsAndColsAdaptive(firstChunk);

    // Detect column structure from the first chunk - this is critical for consistency
    const columnStructure = this.analyzeColumnStructure(
      firstChunk,
      firstEmptyCols
    );

    // Strategy determination - based on first chunk analysis
    const analysisResult = this.analyzeSheetStructure(firstChunk);
    const isContinuousTable = analysisResult.continuousTableProbability > 0.7;
    this.logger.log(
      `Sheet analysis: continuous table probability: ${analysisResult.continuousTableProbability.toFixed(2)}, detected ${analysisResult.estimatedHeaderRows} header rows`
    );

    // If we detect a single continuous table structure, handle it specially
    if (isContinuousTable) {
      this.logger.log(
        `Detected continuous table structure, handling as single table`
      );
      return this.handleContinuousTable(data, columnStructure);
    }

    // Process chunks with awareness of global structure
    const allRegions: Array<{
      startRow: number;
      startCol: number;
      height: number;
      width: number;
      confidence: number;
    }> = [];

    // Process each chunk
    for (
      let startRow = 0;
      startRow < rowCount;
      startRow += chunkSize - overlap
    ) {
      const endRow = Math.min(startRow + chunkSize, rowCount);

      // Extract the current chunk
      const chunk = data.slice(startRow, endRow);

      // Process this chunk
      const { emptyRows, emptyCols } = this.findEmptyRowsAndColsAdaptive(chunk);

      // Force column structure consistency with first chunk when appropriate
      const consistentEmptyCols = this.enforceColumnConsistency(
        emptyCols,
        columnStructure
      );

      // Detect regions in this chunk
      const regions = this.identifyTableRegionsEnhanced(
        chunk,
        emptyRows,
        consistentEmptyCols
      );

      // Adjust region positions to account for chunk offset
      const offsetRegions = regions.map((region) => ({
        ...region,
        startRow: region.startRow + startRow,
        confidence: this.assessTableConfidenceAdaptive(
          this.extractRegionData(chunk, region),
          chunk.length,
          chunk[0].length
        ),
      }));

      // Add to our collection
      allRegions.push(...offsetRegions);
    }

    // Apply global consistency enforcement
    const cohesiveRegions = this.enforceGlobalConsistency(allRegions, data);

    this.logger.log(
      `Progressive detection complete, found ${cohesiveRegions.length} table regions`
    );
    const result = await this.regionsToTables(cohesiveRegions, data);
    this.logger.log(`Converted regions to ${result.length} tables`);
    return result;
  }

  /**
   * Analyzes sheet structure to determine if it contains a single continuous table
   */
  private analyzeSheetStructure(data: any[][]): {
    continuousTableProbability: number;
    tableTypes: string[];
    estimatedHeaderRows: number;
  } {
    // Initialize result
    const result = {
      continuousTableProbability: 0,
      tableTypes: [] as string[],
      estimatedHeaderRows: 1,
    };

    if (data.length < 10) return result;

    // Check for patterns indicating a continuous table
    // 1. Analyze first few rows - are they likely headers?
    let headerRows = 0;
    let headerEvidence = 0;

    for (let r = 0; r < Math.min(10, data.length); r++) {
      // Check if row has header-like properties
      const headerScore = this.calculateHeaderLikelihood(data[r]);

      if (headerScore > 0.6) {
        headerRows++;
        headerEvidence += headerScore;
      } else {
        break; // First non-header row found
      }
    }

    result.estimatedHeaderRows = Math.max(1, headerRows);

    // 2. Analyze row consistency - do subsequent rows follow a consistent pattern?
    const rowPatternScores: number[] = [];

    // Skip the identified headers
    for (let r = headerRows; r < Math.min(100, data.length); r++) {
      // Calculate pattern consistency with preceding rows
      if (r > headerRows) {
        rowPatternScores.push(
          this.calculateRowPatternSimilarity(data[r], data[r - 1])
        );
      }
    }

    // Calculate the average pattern consistency
    const avgPatternConsistency =
      rowPatternScores.length > 0
        ? rowPatternScores.reduce((sum, score) => sum + score, 0) /
          rowPatternScores.length
        : 0;

    // 3. Analyze column data types - are they consistent?
    const columnTypeConsistency = this.analyzeColumnTypeConsistency(
      data,
      headerRows
    );

    // Determine the continuity probability
    result.continuousTableProbability =
      headerEvidence * 0.3 +
      avgPatternConsistency * 0.4 +
      columnTypeConsistency * 0.3;

    // Identify table types based on analysis
    if (result.continuousTableProbability > 0.7) {
      result.tableTypes.push('continuous');
    } else if (avgPatternConsistency > 0.6) {
      result.tableTypes.push('semi-continuous');
    } else {
      result.tableTypes.push('segmented');
    }

    return result;
  }

  /**
   * Handle a worksheet that contains a single continuous table
   * This is a special case optimization for common worksheet layouts
   */
  private async handleContinuousTable(
    data: any[][],
    columnStructure: { isActive: boolean[] }
  ): Promise<{ headers: any[][]; rows: any[][] }[]> {
    // Find the header section - we need to determine where the headers end
    let headerRowCount = 1; // Default to 1 header row

    // Use a stronger header detection approach for continuous tables
    for (let r = 1; r < Math.min(10, data.length); r++) {
      const headerLikelihood = this.calculateHeaderLikelihood(data[r]);
      const dataRowLikelihood = this.calculateDataRowLikelihood(data[r]);

      if (headerLikelihood > dataRowLikelihood && headerLikelihood > 0.6) {
        headerRowCount++;
      } else {
        break;
      }
    }

    // Determine active columns based on column structure and content
    const activeColumns: number[] = [];

    for (let c = 0; c < data[0].length; c++) {
      if (columnStructure.isActive[c]) {
        activeColumns.push(c);
      }
    }

    // Determine where the table data ends (ignore trailing empty rows)
    let lastDataRow = data.length - 1;

    while (lastDataRow > headerRowCount) {
      const isEmptyRow = activeColumns.every((c) => {
        const value = data[lastDataRow][c];
        return (
          value === undefined || value === null || String(value).trim() === ''
        );
      });

      if (isEmptyRow) {
        lastDataRow--;
      } else {
        break;
      }
    }

    // Extract the continuous table
    const tableRegion = {
      startRow: 0,
      startCol: 0,
      height: lastDataRow + 1,
      width: data[0].length,
    };

    // Create a single table from this region
    const headers = data.slice(0, headerRowCount);
    const rows = data.slice(headerRowCount, lastDataRow + 1);

    return [{ headers, rows }];
  }

  /**
   * Analyze column structure to help maintain consistency
   */
  private analyzeColumnStructure(
    data: any[][],
    emptyCols: boolean[]
  ): { isActive: boolean[] } {
    const colCount = data[0].length;
    const isActive = Array(colCount).fill(false);

    // First, mark columns that aren't empty
    for (let c = 0; c < colCount; c++) {
      isActive[c] = !emptyCols[c];
    }

    // Now, analyze data density in each column
    const columnDensity: number[] = Array(colCount).fill(0);

    for (let c = 0; c < colCount; c++) {
      let nonEmptyCells = 0;

      for (let r = 0; r < data.length; r++) {
        const value = data[r][c];
        if (
          value !== undefined &&
          value !== null &&
          String(value).trim() !== ''
        ) {
          nonEmptyCells++;
        }
      }

      columnDensity[c] = nonEmptyCells / data.length;
    }

    // Update activity based on density
    for (let c = 0; c < colCount; c++) {
      // Mark columns with reasonable density as active
      if (columnDensity[c] > 0.1) {
        isActive[c] = true;
      }
    }

    return { isActive };
  }

  /**
   * Enforce column consistency based on global structure
   */
  private enforceColumnConsistency(
    localEmptyCols: boolean[],
    columnStructure: { isActive: boolean[] }
  ): boolean[] {
    // Create a copy to avoid modifying the original
    const adjustedEmptyCols = [...localEmptyCols];

    // Enforce global column structure for consistency
    for (
      let c = 0;
      c < Math.min(localEmptyCols.length, columnStructure.isActive.length);
      c++
    ) {
      // If this is a globally active column, don't mark it as empty
      if (columnStructure.isActive[c]) {
        adjustedEmptyCols[c] = false;
      }
    }

    return adjustedEmptyCols;
  }

  /**
   * Enforce global consistency across regions detected in different chunks
   */
  private enforceGlobalConsistency(
    regions: Array<{
      startRow: number;
      startCol: number;
      height: number;
      width: number;
      confidence: number;
    }>,
    data: any[][]
  ): Array<{
    startRow: number;
    startCol: number;
    height: number;
    width: number;
  }> {
    if (regions.length <= 1) return regions;

    // Sort regions by position (top to bottom)
    regions.sort((a, b) => a.startRow - b.startRow);

    // Group regions by proximity and alignment
    const groups: Array<typeof regions> = [];
    let currentGroup: typeof regions = [regions[0]];

    for (let i = 1; i < regions.length; i++) {
      const current = regions[i];
      const previous = regions[i - 1];

      // Check if these regions are well-aligned
      const horizontalOverlap = this.calculateHorizontalOverlap(
        current,
        previous
      );
      const verticalGap =
        current.startRow - (previous.startRow + previous.height);

      // If aligned and close, add to current group
      if (horizontalOverlap > 0.5 && verticalGap < 20) {
        currentGroup.push(current);
      } else {
        // Start a new group
        groups.push(currentGroup);
        currentGroup = [current];
      }
    }

    // Add the last group
    if (currentGroup.length > 0) {
      groups.push(currentGroup);
    }

    // Merge regions within each group
    const mergedRegions: Array<{
      startRow: number;
      startCol: number;
      height: number;
      width: number;
    }> = [];

    for (const group of groups) {
      if (group.length === 1) {
        // Single region, keep as is (remove confidence as it's no longer needed)
        const { confidence, ...region } = group[0];
        mergedRegions.push(region);
      } else {
        // Multiple regions, merge them
        let startRow = Infinity;
        let startCol = Infinity;
        let endRow = -Infinity;
        let endCol = -Infinity;

        // Find the bounding box containing all regions in this group
        for (const region of group) {
          startRow = Math.min(startRow, region.startRow);
          startCol = Math.min(startCol, region.startCol);
          endRow = Math.max(endRow, region.startRow + region.height);
          endCol = Math.max(endCol, region.startCol + region.width);
        }

        mergedRegions.push({
          startRow,
          startCol,
          height: endRow - startRow,
          width: endCol - startCol,
        });
      }
    }

    return mergedRegions;
  }

  /**
   * Convert regions to tables
   */
  private async regionsToTables(
    regions: Array<{
      startRow: number;
      startCol: number;
      height: number;
      width: number;
    }>,
    data: any[][]
  ): Promise<{ headers: any[][]; rows: any[][] }[]> {
    const tables: { headers: any[][]; rows: any[][] }[] = [];

    for (const region of regions) {
      // Extract region data
      const regionData = this.extractRegionData(data, region);

      // Skip regions that are too small
      if (regionData.length < 2 || regionData[0].length < 2) continue;

      // Analyze region data to detect headers
      const headerRowCount = await this.detectHeaderRowsWithLLM(regionData);

      // Apply more robust header count determination
      const safeHeaderCount = this.calculateOptimalHeaderCount(
        headerRowCount,
        regionData
      );

      // Create the table
      tables.push({
        headers: regionData.slice(0, safeHeaderCount),
        rows: regionData.slice(safeHeaderCount),
      });
    }

    return tables;
  }

  /**
   * Calculate how much two regions overlap horizontally (0-1 score)
   */
  private calculateHorizontalOverlap(regionA: any, regionB: any): number {
    const aLeft = regionA.startCol;
    const aRight = regionA.startCol + regionA.width;
    const bLeft = regionB.startCol;
    const bRight = regionB.startCol + regionB.width;

    // Calculate overlap width
    const overlapStart = Math.max(aLeft, bLeft);
    const overlapEnd = Math.min(aRight, bRight);
    const overlapWidth = Math.max(0, overlapEnd - overlapStart);

    // Calculate total span
    const aWidth = aRight - aLeft;
    const bWidth = bRight - bLeft;
    const totalWidth = Math.max(aWidth, bWidth);

    return totalWidth > 0 ? overlapWidth / totalWidth : 0;
  }

  /**
   * Calculate how similar a row is to being a header row (0-1 score)
   */
  private calculateHeaderLikelihood(row: any[]): number {
    if (!row || row.length === 0) return 0;

    let textCells = 0;
    let nonEmptyCells = 0;
    let shortTextCells = 0;

    for (const cell of row) {
      if (cell !== undefined && cell !== null && String(cell).trim() !== '') {
        nonEmptyCells++;

        if (typeof cell === 'string') {
          textCells++;

          // Headers often have shorter text
          if (String(cell).length < 50) {
            shortTextCells++;
          }
        }
      }
    }

    if (nonEmptyCells === 0) return 0;

    // Headers typically have:
    // 1. High proportion of text cells
    // 2. High proportion of short text cells
    // 3. Good overall cell coverage

    const textRatio = textCells / nonEmptyCells;
    const shortTextRatio = shortTextCells / nonEmptyCells;
    const coverageRatio = nonEmptyCells / row.length;

    return textRatio * 0.4 + shortTextRatio * 0.4 + coverageRatio * 0.2;
  }

  /**
   * Calculate how similar a row is to being a data row (0-1 score)
   */
  private calculateDataRowLikelihood(row: any[]): number {
    if (!row || row.length === 0) return 0;

    let numericCells = 0;
    let dateCells = 0;
    let nonEmptyCells = 0;

    for (const cell of row) {
      if (cell !== undefined && cell !== null && String(cell).trim() !== '') {
        nonEmptyCells++;

        if (typeof cell === 'number' || !isNaN(Number(cell))) {
          numericCells++;
        } else if (cell instanceof Date || this.isLikelyDate(cell)) {
          dateCells++;
        }
      }
    }

    if (nonEmptyCells === 0) return 0;

    // Data rows often have:
    // 1. More numeric/date values
    // 2. More varied cell types

    const numericRatio = (numericCells + dateCells) / nonEmptyCells;
    const coverageRatio = nonEmptyCells / row.length;

    return numericRatio * 0.6 + coverageRatio * 0.4;
  }

  /**
   * Calculate similarity between row patterns (0-1 score)
   */
  private calculateRowPatternSimilarity(rowA: any[], rowB: any[]): number {
    if (!rowA || !rowB || rowA.length !== rowB.length) return 0;

    let matchingCellTypes = 0;
    let cellsCompared = 0;

    for (let c = 0; c < rowA.length; c++) {
      const cellA = rowA[c];
      const cellB = rowB[c];

      const typeA = this.getCellDataType(cellA);
      const typeB = this.getCellDataType(cellB);

      // Only compare non-empty cells
      if (typeA !== 'empty' || typeB !== 'empty') {
        cellsCompared++;

        if (typeA === typeB) {
          matchingCellTypes++;
        }
      }
    }

    return cellsCompared > 0 ? matchingCellTypes / cellsCompared : 0;
  }

  /**
   * Get the data type of a cell
   */
  private getCellDataType(cell: any): string {
    if (cell === undefined || cell === null || String(cell).trim() === '') {
      return 'empty';
    }

    if (typeof cell === 'number' || !isNaN(Number(cell))) {
      return 'number';
    }

    if (cell instanceof Date || this.isLikelyDate(cell)) {
      return 'date';
    }

    if (typeof cell === 'boolean') {
      return 'boolean';
    }

    // String type - could be further refined if needed
    return 'text';
  }

  /**
   * Analyze column type consistency after headers
   */
  private analyzeColumnTypeConsistency(
    data: any[][],
    headerRows: number
  ): number {
    if (data.length <= headerRows) return 0;

    const colCount = data[0].length;
    let totalConsistency = 0;
    let columnsAnalyzed = 0;

    for (let c = 0; c < colCount; c++) {
      // Skip columns that are completely empty
      let hasData = false;

      for (let r = headerRows; r < data.length; r++) {
        if (this.getCellDataType(data[r][c]) !== 'empty') {
          hasData = true;
          break;
        }
      }

      if (!hasData) continue;

      // Analyze data type consistency for this column
      const typeCounts: Record<string, number> = {};
      let totalCells = 0;

      for (let r = headerRows; r < data.length; r++) {
        const cellType = this.getCellDataType(data[r][c]);

        if (cellType !== 'empty') {
          typeCounts[cellType] = (typeCounts[cellType] || 0) + 1;
          totalCells++;
        }
      }

      // Find most common type
      let maxTypeCount = 0;
      for (const count of Object.values(typeCounts)) {
        maxTypeCount = Math.max(maxTypeCount, count);
      }

      // Calculate consistency score for this column
      const columnConsistency = totalCells > 0 ? maxTypeCount / totalCells : 0;

      totalConsistency += columnConsistency;
      columnsAnalyzed++;
    }

    return columnsAnalyzed > 0 ? totalConsistency / columnsAnalyzed : 0;
  }

  /**
   * Calculate data density for the sheet
   */
  private calculateDataDensity(data: any[][]): number {
    if (data.length === 0) return 0;

    const rowCount = data.length;
    const colCount = data[0].length;
    let nonEmptyCells = 0;
    const totalCells = rowCount * colCount;

    for (let r = 0; r < rowCount; r++) {
      for (let c = 0; c < colCount; c++) {
        const value = data[r][c];
        if (
          value !== undefined &&
          value !== null &&
          String(value).trim() !== ''
        ) {
          nonEmptyCells++;
        }
      }
    }

    return nonEmptyCells / totalCells;
  }

  /**
   * Adaptive table confidence assessment with context awareness
   */
  private assessTableConfidenceAdaptive(
    regionData: any[][],
    totalRows: number,
    totalCols: number
  ): number {
    if (regionData.length < 2) return 0;

    const rowCount = regionData.length;
    const colCount = regionData[0].length;

    if (colCount < 2) return 0;

    // Count non-empty cells
    let nonEmptyCells = 0;
    const totalCells = rowCount * colCount;

    // Count consistent data types per column
    const columnDataTypes: Record<number, Map<string, number>> = {};

    for (let c = 0; c < colCount; c++) {
      columnDataTypes[c] = new Map<string, number>();
    }

    // Analyze the data
    for (let r = 0; r < rowCount; r++) {
      for (let c = 0; c < colCount; c++) {
        const value = regionData[r][c];

        if (
          value !== undefined &&
          value !== null &&
          String(value).trim() !== ''
        ) {
          nonEmptyCells++;

          // Determine data type
          let dataType = 'text';
          if (typeof value === 'number' || !isNaN(Number(value))) {
            dataType = 'number';
          } else if (value instanceof Date || this.isLikelyDate(value)) {
            dataType = 'date';
          }

          // Update column data type counts
          const currentCount = columnDataTypes[c].get(dataType) || 0;
          columnDataTypes[c].set(dataType, currentCount + 1);
        }
      }
    }

    // Calculate relative size of region compared to whole sheet
    const relativeSizeRatio = (rowCount * colCount) / (totalRows * totalCols);

    // Small tables in large sheets should be assessed differently
    const isSmallTable = relativeSizeRatio < 0.1;

    // For small tables, we need stricter consistency requirements
    const minRequiredDensity = isSmallTable ? 0.4 : 0.2;

    // Reject if there's not enough data
    const densityScore = nonEmptyCells / totalCells;
    if (densityScore < minRequiredDensity) {
      return 0;
    }

    // Calculate column consistency score with adaptive weighting
    let totalConsistency = 0;
    let columnsWithData = 0;

    for (let c = 0; c < colCount; c++) {
      const types = columnDataTypes[c];
      let maxCount = 0;
      let totalCount = 0;

      for (const count of types.values()) {
        maxCount = Math.max(maxCount, count);
        totalCount += count;
      }

      if (totalCount > 0) {
        const columnConsistency = maxCount / totalCount;
        totalConsistency += columnConsistency;
        columnsWithData++;
      }
    }

    const typeConsistencyScore =
      columnsWithData > 0 ? totalConsistency / columnsWithData : 0;

    // Calculate grid pattern score with emphasis on header-like structures
    let headerLikeScore = 0;

    // Check first row for header-like characteristics
    if (rowCount > 1) {
      let headerCells = 0;
      for (let c = 0; c < colCount; c++) {
        const value = regionData[0][c];
        if (
          value !== undefined &&
          value !== null &&
          String(value).trim() !== '' &&
          typeof value === 'string' &&
          value.length < 50
        ) {
          headerCells++;
        }
      }

      headerLikeScore = headerCells / colCount;
    }

    // Combine scores with weights adjusted for table size
    if (isSmallTable) {
      // Small tables need stronger evidence of tabular structure
      return (
        densityScore * 0.3 + typeConsistencyScore * 0.4 + headerLikeScore * 0.3
      );
    } else {
      // Larger tables can be more forgiving
      return (
        densityScore * 0.25 +
        typeConsistencyScore * 0.35 +
        headerLikeScore * 0.4
      );
    }
  }

  /**
   * Determine optimal header count based on data analysis
   */
  private calculateOptimalHeaderCount(
    llmHeaderCount: number,
    regionData: any[][]
  ): number {
    if (regionData.length <= 1) return 1;

    // Enforce reasonable limits
    const maxPossible = Math.min(5, Math.floor(regionData.length / 2));

    // Start with LLM's suggestion
    const headerCount = Math.max(1, Math.min(llmHeaderCount, maxPossible));

    // Analyze the first few rows to see if they look like headers
    const potentialHeaderRows = Math.min(5, regionData.length - 1);
    const headerLikeScores: number[] = [];

    for (let r = 0; r < potentialHeaderRows; r++) {
      let textValues = 0;
      let numericValues = 0;
      let nonEmptyValues = 0;

      for (let c = 0; c < regionData[r].length; c++) {
        const value = regionData[r][c];
        if (
          value !== undefined &&
          value !== null &&
          String(value).trim() !== ''
        ) {
          nonEmptyValues++;

          if (typeof value === 'number' || !isNaN(Number(value))) {
            numericValues++;
          } else {
            textValues++;
          }
        }
      }

      // Headers typically have:
      // 1. More text than numbers
      // 2. Good cell coverage
      const cellCoverage = nonEmptyValues / regionData[r].length;
      const textRatio = nonEmptyValues > 0 ? textValues / nonEmptyValues : 0;

      // Compute header-like score
      headerLikeScores.push(cellCoverage * 0.5 + textRatio * 0.5);
    }

    // Check for significant drop in header-like score
    for (let r = 1; r < headerLikeScores.length; r++) {
      if (headerLikeScores[r] < headerLikeScores[r - 1] * 0.7) {
        // Found a significant drop - likely transition from header to data
        return r;
      }
    }

    // If no clear drop, stick with LLM suggestion with constraints
    return headerCount;
  }

  /**
   * Detects hidden rows and columns in a worksheet
   */
  private detectHiddenRowsAndColumns(sheet: XLSX.WorkSheet): {
    hiddenRows: Set<number>;
    hiddenCols: Set<number>;
  } {
    const hiddenRows = new Set<number>();
    const hiddenCols = new Set<number>();

    // Process hidden rows
    if (sheet['!rows']) {
      for (let r = 0; r < sheet['!rows'].length; r++) {
        if (sheet['!rows'][r] && sheet['!rows'][r].hidden) {
          hiddenRows.add(r);
        }
      }
    }

    // Process hidden columns
    if (sheet['!cols']) {
      for (let c = 0; c < sheet['!cols'].length; c++) {
        if (sheet['!cols'][c] && sheet['!cols'][c].hidden) {
          hiddenCols.add(c);
        }
      }
    }

    this.logger.log(
      `Detected ${hiddenRows.size} hidden rows and ${hiddenCols.size} hidden columns`
    );
    return { hiddenRows, hiddenCols };
  }

  /**
   * Normalize sheet data with awareness of hidden rows and columns
   */
  private normalizeSheetDataWithHiddenCells(
    data: any[][],
    hiddenRows: Set<number>,
    hiddenCols: Set<number>,
    preserveHidden: boolean = false
  ): any[][] {
    this.logger.log(
      `Normalizing sheet data: ${data.length} rows, ${hiddenRows.size} hidden rows, ${hiddenCols.size} hidden columns`
    );

    if (data.length === 0) return [];

    const maxCols = Math.max(...data.map((row) => row.length));
    const normalizedData: any[][] = [];

    for (let r = 0; r < data.length; r++) {
      // Skip hidden rows if not preserving them
      if (!preserveHidden && hiddenRows.has(r)) continue;

      const row = data[r];
      const normalizedRow: any[] = [];

      for (let c = 0; c < maxCols; c++) {
        // Skip hidden columns if not preserving them
        if (!preserveHidden && hiddenCols.has(c)) continue;

        const value = c < row.length ? row[c] : '';
        normalizedRow.push(value !== undefined && value !== null ? value : '');
      }

      normalizedData.push(normalizedRow);
    }

    this.logger.log(
      `Sheet normalized to: ${normalizedData.length} rows x ${normalizedData[0]?.length || 0} columns`
    );
    return normalizedData;
  }

  /**
   * Detects potential tables in a workbook using available metadata
   */
  private detectNativeTables(
    workbook: XLSX.WorkBook,
    sheetName: string
  ): Array<{
    name: string;
    ref: string;
    range: {
      s: { r: number; c: number };
      e: { r: number; c: number };
    };
  }> {
    this.logger.log(`Detecting native Excel tables in sheet: ${sheetName}`);

    const tables: Array<{
      name: string;
      ref: string;
      range: {
        s: { r: number; c: number };
        e: { r: number; c: number };
      };
    }> = [];

    // Check if the Workbook object exists
    if (workbook.Workbook) {
      // Look for tables in the Names collection as SheetJS doesn't expose Tables directly
      if (workbook.Workbook.Names) {
        for (const name of workbook.Workbook.Names) {
          // Check if this name is associated with the current sheet
          // and follows table naming patterns
          if (
            name.Sheet === workbook.SheetNames.indexOf(sheetName) &&
            (name.Name.startsWith('Table') || name.Name.includes('_Table'))
          ) {
            try {
              // Parse the reference to get the table range
              if (
                name.Ref &&
                name.Ref.includes('!') &&
                name.Ref.includes(':')
              ) {
                const range = this.parseExcelReference(name.Ref);
                if (range) {
                  tables.push({
                    name: name.Name,
                    ref: name.Ref,
                    range,
                  });
                }
              }
            } catch (error) {
              this.logger.warn(
                `Error parsing name reference: ${error.message}`
              );
            }
          }
        }
      }

      // Check for other types of named ranges that might be tables
      if (workbook.Workbook.Names) {
        for (const name of workbook.Workbook.Names) {
          // Look for data ranges (common pattern for tables)
          if (
            name.Sheet === workbook.SheetNames.indexOf(sheetName) &&
            (name.Name.endsWith('Data') || name.Name.includes('Range'))
          ) {
            try {
              if (
                name.Ref &&
                name.Ref.includes('!') &&
                name.Ref.includes(':')
              ) {
                const range = this.parseExcelReference(name.Ref);
                if (range) {
                  tables.push({
                    name: name.Name,
                    ref: name.Ref,
                    range,
                  });
                }
              }
            } catch (error) {
              this.logger.warn(
                `Error parsing data range reference: ${error.message}`
              );
            }
          }
        }
      }
    }

    // If we couldn't find tables through metadata, we can try to infer them
    // from the sheet content itself (looking for formatting patterns)
    if (tables.length === 0) {
      this.logger.log(
        `No native tables found in metadata, will rely on detection algorithms`
      );
    } else {
      this.logger.log(
        `Detected ${tables.length} potential tables from metadata in sheet: ${sheetName}`
      );
    }

    return tables;
  }

  /**
   * Alternative method to check for tables using sheet formatting clues
   */
  private detectTablesFromFormatting(sheet: XLSX.WorkSheet): Array<{
    name: string;
    range: {
      s: { r: number; c: number };
      e: { r: number; c: number };
    };
  }> {
    const tables: Array<{
      name: string;
      range: {
        s: { r: number; c: number };
        e: { r: number; c: number };
      };
    }> = [];

    // Check if the sheet has defined names that look like table ranges
    const range = sheet['!ref'] ? XLSX.utils.decode_range(sheet['!ref']) : null;
    if (!range) return tables;

    // Look for distinct formatted regions that might be tables
    // This approach is simplified - in a full implementation, you'd analyze
    // cell formatting to identify table-like structures

    this.logger.log(`Attempting to detect tables from sheet formatting`);

    // For now, just return the entire sheet as a potential table
    // (real implementation would be more sophisticated)
    tables.push({
      name: 'DetectedTable',
      range: {
        s: { r: range.s.r, c: range.s.c },
        e: { r: range.e.r, c: range.e.c },
      },
    });

    return tables;
  }

  /**
   * Parse Excel cell reference (e.g., 'Sheet1!$A$1:$D$10') into row/column indices
   */
  private parseExcelReference(ref: string): {
    s: { r: number; c: number };
    e: { r: number; c: number };
  } | null {
    // Extract just the range part (remove sheet name if present)
    const rangePart = ref.includes('!') ? ref.split('!')[1] : ref;

    // Remove any $ signs (absolute references)
    const cleanRange = rangePart.replace(/\$/g, '');

    // Split into start and end references
    const [start, end] = cleanRange.split(':');

    if (!start || !end) return null;

    try {
      // Use XLSX utility to decode cell references
      const s = XLSX.utils.decode_cell(start);
      const e = XLSX.utils.decode_cell(end);

      return { s, e };
    } catch (error) {
      this.logger.warn(`Invalid cell reference: ${ref}`);
      return null;
    }
  }

  /**
   * Extracts data from a native Excel table
   */
  private extractNativeTable(
    sheet: XLSX.WorkSheet,
    tableRange: {
      s: { r: number; c: number };
      e: { r: number; c: number };
    }
  ): { headers: any[][]; rows: any[][] } {
    const { s, e } = tableRange;

    // Extract all cells in the table range
    const tableData: any[][] = [];
    for (let r = s.r; r <= e.r; r++) {
      const row: any[] = [];
      for (let c = s.c; c <= e.c; c++) {
        const cellRef = XLSX.utils.encode_cell({ r, c });
        const cell = sheet[cellRef];
        row.push(cell ? cell.v : '');
      }
      tableData.push(row);
    }

    // Most Excel tables have a single header row, but we'll check for formatting clues
    // For now, use a reasonable default (typically 1 header row)
    const headerRowCount = 1;

    const headers = tableData.slice(0, headerRowCount);
    const rows = tableData.slice(headerRowCount);

    this.logger.log(
      `Native table extracted with ${headers.length} header rows and ${rows.length} data rows`
    );
    return { headers, rows };
  }

  /**
   * Enhanced unmerging of cells with better content preservation
   */
  private enhancedUnmergeCells(sheet: XLSX.WorkSheet): void {
    this.logger.log(`Enhanced unmerging of cells in worksheet`);

    const merges = sheet['!merges'];
    if (!merges) {
      this.logger.log('No merged cells found in worksheet');
      return;
    }

    this.logger.log(`Found ${merges.length} merged regions to process`);

    for (const merge of merges) {
      const cellAddress = XLSX.utils.encode_cell(merge.s);
      const cell = sheet[cellAddress];

      if (!cell) continue; // No value to propagate

      // Get the value and type of the starting cell
      const value = cell.v;
      const type = cell.t;
      const formula = cell.f; // Preserve formula if present

      // Calculate merge dimensions
      const rowSpan = merge.e.r - merge.s.r + 1;
      const colSpan = merge.e.c - merge.s.c + 1;

      // Loop over all cells in the merged range
      for (let r = merge.s.r; r <= merge.e.r; ++r) {
        for (let c = merge.s.c; c <= merge.e.c; ++c) {
          const address = { r, c };
          const cellRef = XLSX.utils.encode_cell(address);

          // For the first cell, add merge metadata with exact same pattern as original
          if (
            r === merge.s.r &&
            c === merge.s.c &&
            (rowSpan > 1 || colSpan > 1)
          ) {
            sheet[cellRef] = {
              v: `|<r${rowSpan}#c${colSpan}>${value}|`,
              t: type,
              f: formula,
            };
          } else {
            // For other cells in the merged range, use the same value
            sheet[cellRef] = {
              v: `lalal`,
              t: type,
            };
          }
        }
      }
    }

    // Remove the merges after processing
    delete sheet['!merges'];
    this.logger.log('Enhanced cell unmerging complete');
  }

  /**
   * Post-processes the table data to handle merged cells properly with precise tracking
   */
  private postProcessMergedCells(
    data: any[][],
    originalMerges: any[]
  ): any[][] {
    if (!originalMerges || originalMerges.length === 0 || data.length === 0) {
      return data;
    }

    this.logger.log(
      `Post-processing ${originalMerges.length} merged regions in ${data.length}x${data[0].length} data grid`
    );

    // Create a deep copy of the data
    const processedData = JSON.parse(JSON.stringify(data));

    // Get dimensions
    const rowCount = data.length;
    const colCount = Math.max(...data.map((row) => row.length));

    // Track each cell's merge status in a 2D grid
    type MergeInfo = {
      isMerged: boolean;
      isFirstCell: boolean;
      firstCellRow: number;
      firstCellCol: number;
      rowSpan: number;
      colSpan: number;
      value: any;
    };

    // Initialize merge map with safe dimensions
    const mergeMap: Array<Array<MergeInfo | null>> = [];
    for (let r = 0; r < rowCount; r++) {
      mergeMap[r] = [];
      for (let c = 0; c < colCount; c++) {
        mergeMap[r][c] = null;
      }
    }

    // Log merge boundaries for debugging
    this.logger.log(`Processing merges for data grid ${rowCount}x${colCount}`);

    // First, validate all merges are within bounds
    const validMerges = originalMerges.filter((merge) => {
      const isValid =
        merge.s.r >= 0 &&
        merge.s.r < rowCount &&
        merge.s.c >= 0 &&
        merge.s.c < colCount &&
        merge.e.r >= 0 &&
        merge.e.r < rowCount &&
        merge.e.c >= 0 &&
        merge.e.c < colCount;

      if (!isValid) {
        this.logger.warn(
          `Skipping out-of-bounds merge: (${merge.s.r},${merge.s.c}) to (${merge.e.r},${merge.e.c})`
        );
      }
      return isValid;
    });

    // Process each merge region
    for (let i = 0; i < validMerges.length; i++) {
      const merge = validMerges[i];
      const rowSpan = merge.e.r - merge.s.r + 1;
      const colSpan = merge.e.c - merge.s.c + 1;

      // Get value from the first cell
      const firstCellValue = data[merge.s.r]?.[merge.s.c] || '';

      this.logger.debug(
        `Merge #${i}: (${merge.s.r},${merge.s.c}) spans ${rowSpan}x${colSpan}, value: "${firstCellValue}"`
      );

      // Mark all cells in this merged region
      for (let r = merge.s.r; r <= merge.e.r; r++) {
        for (let c = merge.s.c; c <= merge.e.c; c++) {
          if (r < rowCount && c < colCount) {
            mergeMap[r][c] = {
              isMerged: true,
              isFirstCell: r === merge.s.r && c === merge.s.c,
              firstCellRow: merge.s.r,
              firstCellCol: merge.s.c,
              rowSpan,
              colSpan,
              value: firstCellValue,
            };
          }
        }
      }
    }

    // Second pass: Update the cell values based on merge status
    let firstCellsProcessed = 0;
    let secondaryCellsProcessed = 0;

    for (let r = 0; r < rowCount; r++) {
      for (let c = 0; c < colCount; c++) {
        const mergeInfo = mergeMap[r][c];

        // Skip cells not in any merge
        if (!mergeInfo) continue;

        if (r >= processedData.length) continue; // Safety check
        if (c >= (processedData[r]?.length || 0)) continue; // Safety check

        if (mergeInfo.isFirstCell) {
          // First cell of a merge gets special formatting
          if (mergeInfo.rowSpan > 1 || mergeInfo.colSpan > 1) {
            processedData[r][c] =
              `|<r${mergeInfo.rowSpan}#c${mergeInfo.colSpan}>${mergeInfo.value}|`;
            firstCellsProcessed++;
          }
        } else {
          // Secondary cells in a merge get <rm> tag
          processedData[r][c] = `<rm>${mergeInfo.value}</rm>`;
          secondaryCellsProcessed++;
        }
      }
    }

    this.logger.log(
      `Processed ${firstCellsProcessed} first cells and ${secondaryCellsProcessed} secondary cells`
    );

    return processedData;
  }

  /**
   * Adjust table data to account for hidden rows and columns
   */
  private adjustTableForHiddenCells(
    table: { headers: any[][]; rows: any[][] },
    range: { s: { r: number; c: number }; e: { r: number; c: number } },
    hiddenRows: Set<number>,
    hiddenCols: Set<number>
  ): { headers: any[][]; rows: any[][] } {
    // Create mapping from original indices to adjusted indices (after removing hidden cells)
    const rowMapping: number[] = [];
    const colMapping: number[] = [];

    let adjustedRowIdx = 0;
    for (let r = range.s.r; r <= range.e.r; r++) {
      rowMapping[r] = hiddenRows.has(r) ? -1 : adjustedRowIdx++;
    }

    let adjustedColIdx = 0;
    for (let c = range.s.c; c <= range.e.c; c++) {
      colMapping[c] = hiddenCols.has(c) ? -1 : adjustedColIdx++;
    }

    // Process headers (we need to map row/col indices and skip hidden cells)
    const adjustedHeaders: any[][] = [];
    for (
      let headerRowIdx = 0;
      headerRowIdx < table.headers.length;
      headerRowIdx++
    ) {
      const originalRow = range.s.r + headerRowIdx;
      if (rowMapping[originalRow] === -1) continue; // Skip hidden row

      const adjustedRow: any[] = [];
      for (let c = range.s.c; c <= range.e.c; c++) {
        if (colMapping[c] === -1) continue; // Skip hidden column

        const colOffset = c - range.s.c;
        const headerRow = table.headers[headerRowIdx];
        adjustedRow.push(
          colOffset < headerRow.length ? headerRow[colOffset] : ''
        );
      }

      adjustedHeaders.push(adjustedRow);
    }

    // Process data rows
    const adjustedRows: any[][] = [];
    for (let dataRowIdx = 0; dataRowIdx < table.rows.length; dataRowIdx++) {
      const originalRow = range.s.r + table.headers.length + dataRowIdx;
      if (rowMapping[originalRow] === -1) continue; // Skip hidden row

      const adjustedRow: any[] = [];
      for (let c = range.s.c; c <= range.e.c; c++) {
        if (colMapping[c] === -1) continue; // Skip hidden column

        const colOffset = c - range.s.c;
        const dataRow = table.rows[dataRowIdx];
        adjustedRow.push(colOffset < dataRow.length ? dataRow[colOffset] : '');
      }

      adjustedRows.push(adjustedRow);
    }

    return { headers: adjustedHeaders, rows: adjustedRows };
  }

  /**
   * Deduplicate tables that might be detected multiple ways
   */
  private deduplicateTables(
    tables: Array<{ headers: any[][]; rows: any[][] }>
  ): Array<{ headers: any[][]; rows: any[][] }> {
    if (tables.length <= 1) return tables;

    const unique: Array<{ headers: any[][]; rows: any[][] }> = [];
    const seen = new Set<string>();

    // Create signatures for tables to identify duplicates
    for (const table of tables) {
      if (table.headers.length === 0 || table.rows.length === 0) continue;

      // Create a simple signature based on header content and table dimensions
      const headerSample = table.headers[0]
        .slice(0, 3)
        .map((h) => String(h || ''))
        .join('|');
      const dimensions = `${table.headers.length}:${table.rows.length}:${table.headers[0].length}`;
      const signature = `${headerSample}:${dimensions}`;

      if (!seen.has(signature)) {
        seen.add(signature);
        unique.push(table);
      }
    }

    return unique;
  }
}
