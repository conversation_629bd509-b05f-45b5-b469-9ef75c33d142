import { useQuery, useQueryClient } from '@tanstack/react-query';

import { fetchDocumentUploads } from '@/api/workspace-settings/workspace-settings.api';
import { DocumentUpload } from '@/types/project';

export function useDocuments() {
  const queryClient = useQueryClient();
  const { data: uploadedFiles, isLoading: loading } = useQuery<
    DocumentUpload[]
  >({
    queryKey: ['uploadedFiles'],
    queryFn: fetchDocumentUploads,
  });

  function refetchDocuments() {
    queryClient.invalidateQueries({ queryKey: ['uploadedFiles'] });
  }

  return {
    uploadedFiles,
    loading,
    refetchDocuments,
  };
}
