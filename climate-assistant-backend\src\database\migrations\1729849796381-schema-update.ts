import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1729849796381 implements MigrationInterface {
  name = 'SchemaUpdate1729849796381';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."document_status_enum" AS ENUM('not_processed', 'in_extraction', 'data_extraction_finished', 'linking_data', 'linking_data_finished', 'error_processing')`,
    );
    await queryRunner.query(
      `ALTER TABLE "document" ADD "status" "public"."document_status_enum" NOT NULL DEFAULT 'not_processed'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "status"`);
    await queryRunner.query(`DROP TYPE "public"."document_status_enum"`);
  }
}
