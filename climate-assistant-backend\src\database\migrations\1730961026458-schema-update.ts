import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1730961026458 implements MigrationInterface {
  name = 'SchemaUpdate1730961026458';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE datapoint_document_chunk
      DROP CONSTRAINT "FK_2a5f6913cfb1a14844a6ae4652d",
      ADD CONSTRAINT "FK_2a5f6913cfb1a14844a6ae4652d"
      FOREIGN KEY ("documentChunkId") REFERENCES document_chunk("id") ON DELETE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE datapoint_document_chunk
      DROP CONSTRAINT "FK_2a5f6913cfb1a14844a6ae4652d",
      ADD CONSTRAINT "FK_2a5f6913cfb1a14844a6ae4652d"
      FOREIGN KEY ("documentChunkId") REFERENCES document_chunk("id") ON DELETE NO ACTION;`,
    );
  }
}
