
import React, { useState, useMemo, useCallback } from 'react';
import { MainLayout } from '@/components/MainLayout';
import { PageHeader } from '@/components/layout/PageLayout';
import { GapCard } from '@/components/ecovadis/gaps/GapCard';
import { GapFilters } from '@/components/ecovadis/gaps/GapFilters';
import { EmptyGapsState } from '@/components/ecovadis/gaps/EmptyGapsState';
import { GapItem } from '@/types/ecovadis';
import { fireConfetti } from '@/lib/confetti';
import { useWorkspaceGaps } from '@/hooks/useWorkspaceGaps';
import { useWorkspaceSettings } from '@/api/workspace-settings/useWorkspaceSettings';
import { useAuth } from '@/context/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { GapFixModal } from '@/components/ecovadis/gaps/GapFixModal';
import { DocumentGapCard } from '@/components/ecovadis/gaps/DocumentGapCard';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { exportWorkspaceGaps } from '@/api/ecovadis/ecovadis.api';
import { useToast } from '@/components/ui/use-toast';

const Improvements = () => {
  const { gaps, isLoading, handleMarkGapComplete, handleAssignGap } = useWorkspaceGaps();
  const { workspaceUsers } = useWorkspaceSettings();
  const { user } = useAuth();
  const [activeFilters, setActiveFilters] = useState<any>({});
  const [selectedGap, setSelectedGap] = useState<GapItem | null>(null);
  const [isFixModalOpen, setIsFixModalOpen] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [currentActionGapId, setCurrentActionGapId] = useState<string | null>(null);
  const { toast }  = useToast();

  // Prepare users for MemberSelector with current user at the top
  const users = useMemo(() => {
    if (!workspaceUsers) return [];
    
    const mappedUsers = workspaceUsers.map(user => ({
      id: user.id,
      name: user.name || user.email
    }));
    
    // Put current user at the top if they exist in workspace users
    if (user?.id) {
      const currentUserIndex = mappedUsers.findIndex(u => u.id === user.id);
      if (currentUserIndex > 0) {
        const currentUser = mappedUsers.splice(currentUserIndex, 1)[0];
        mappedUsers.unshift(currentUser);
      }
    }
    
    return mappedUsers;
  }, [workspaceUsers, user?.id]);

  // Extract unique themes and projects using useMemo
  const uniqueThemes = useMemo(() => {
    if (gaps.length === 0) return [];
    const themes = new Set<string>();
    gaps.forEach(gap => {
      if (gap.ecovadis_question?.ecovadis_theme?.title) {
        themes.add(gap.ecovadis_question.ecovadis_theme.title);
      }
    });
    return Array.from(themes);
  }, [gaps]);

  const uniqueProjects = useMemo(() => {
    if (gaps.length === 0) return [];
    const projects = new Map<string, string>();
    gaps.forEach(gap => {
      if (gap.projectId && gap.projectName) {
        projects.set(gap.projectId, gap.projectName);
      }
    });
    return Array.from(projects.entries()).map(([id, name]) => ({ id, name }));
  }, [gaps]);

  const filteredGaps = useMemo(() => {
    if (!gaps || gaps.length === 0) {
      return [];
    }
    let result = [...gaps];
    // Filter by gap type
    if (activeFilters.gapType && activeFilters.gapType !== 'all') {
      result = result.filter(gap => gap.type === activeFilters.gapType);
    }

    // Filter by assignee
    if (activeFilters.assignee) {
      if (activeFilters.assignee === 'me') {
        result = result.filter(gap => gap.assignedTo === user?.id);
      } else if (activeFilters.assignee === 'unassigned') {
        result = result.filter(gap => !gap.assignedTo);
      } else if (activeFilters.assignee !== 'all') {
        result = result.filter(gap => gap.assignedTo === activeFilters.assignee);
      }
    }

    // Filter by status
    if (activeFilters.status) {
      if (activeFilters.status === 'completed') {
        result = result.filter(gap => gap.resolved === true);
      } else if (activeFilters.status === 'pending') {
        result = result.filter(gap => gap.resolved !== true);
      }
    }

    // Filter by topic - Updated to use ecovadis_question.ecovadis_theme.title
    if (activeFilters.topic && activeFilters.topic !== 'all') {
      result = result.filter(gap => {
        // Check if the gap has the ecovadis_theme data
        return gap.ecovadis_question?.ecovadis_theme?.title === activeFilters.topic;
      });
    }

    // Filter by project
    if (activeFilters.project && activeFilters.project !== 'all') {
      result = result.filter(gap => gap.projectId === activeFilters.project);
    }

    // Filter by search term
    if (activeFilters.search) {
      const searchTerm = activeFilters.search.toLowerCase();
      result = result.filter(gap => 
        (gap.title?.toLowerCase().includes(searchTerm)) || 
        (gap.description?.toLowerCase().includes(searchTerm))
      );
    }

    // Remove document filtering - we want all gaps in document view now
    // including those without documents
    
    return result;
  }, [gaps, activeFilters, user?.id]);

  const onMarkComplete = useCallback(async (id: string, complete: boolean) => {
    setCurrentActionGapId(id);
    try {
      await handleMarkGapComplete(id, complete);
      
      if (complete) {
        fireConfetti();
        toast({
          variant: 'default',
          title: "Gap marked as complete",
          description: "Great progress toward improving your score!"
        })
      } else {
        toast({
          variant: 'default',
          title: "Gap marked as incomplete",
          description: "You can complete this gap when you're ready."
        })
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: "Error updating gap",
        description: "Failed to update gap status. Please try again."
      });
    } finally {
      setCurrentActionGapId(null);
    }
  }, [handleMarkGapComplete, toast]);
  
  const handleFixGapClick = useCallback((gap: GapItem) => {
    setSelectedGap(gap);
    setIsFixModalOpen(true);
  },[setSelectedGap, setIsFixModalOpen]);

  const groupGapsByDocument = (gaps: GapItem[]) => {
    const documentGroups: Record<string, { name: string, gaps: GapItem[] }> = {};
    const seenDocuments = new Map<string, string>();
    const gapsWithoutDocuments: GapItem[] = [];
    
    gaps.forEach(gap => {
      if (gap.documents && gap.documents.length > 0) {
        // Process each document associated with this gap
        gap.documents.forEach(doc => {
          // Handle both string IDs and document objects
          const docId = typeof doc === 'string' ? doc : doc.id;
          const docName = typeof doc === 'string' ? 
            (seenDocuments.get(doc) || `Document ${doc}`) : 
            doc.name;
          
          // Store document name for future references
          if (!seenDocuments.has(docId)) {
            seenDocuments.set(docId, docName);
          }
          
          // Initialize document group if needed
          if (!documentGroups[docId]) {
            documentGroups[docId] = {
              name: docName,
              gaps: []
            };
          }
          
          // Add the gap to this document group
          // Check if it's already added (avoid duplicates)
          const isGapAlreadyAdded = documentGroups[docId].gaps.some(g => g.id === gap.id);
          if (!isGapAlreadyAdded) {
            documentGroups[docId].gaps.push(gap);
          }
        });
      } else {
        // Collect gaps without documents
        gapsWithoutDocuments.push(gap);
      }
    });
    
    // Add a special group for gaps without documents if any exist
    if (gapsWithoutDocuments.length > 0) {
      documentGroups["no-document"] = {
        name: "No Existing Document",
        gaps: gapsWithoutDocuments
      };
    }
    
    return documentGroups;
  };

  // Handle exporting gaps to Excel
  const handleExportGaps = useCallback(async () => {
    try {
      setExporting(true);
      toast({
        variant: 'default',
        title: "Exporting Gaps",
      })
      
      // Get projectId from filter if present
      const projectId = activeFilters.project && activeFilters.project !== 'all' 
        ? activeFilters.project 
        : undefined;
      
      // Call API to export gaps
      const blob = await exportWorkspaceGaps(projectId);
      
      // Generate filename with date
      const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
      let projectName = "";
      if (projectId && filteredGaps.length > 0) {
        const project = filteredGaps.find(gap => gap.projectId === projectId);
        if (project && project.projectName) {
          projectName = project.projectName.replace(/[^a-zA-Z0-9]/g, "_") + "_";
        }
      }
      const filename = `${projectName}EcovadisGaps_${date}.xlsx`;
      
      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Cleanup
      URL.revokeObjectURL(url);
      toast({
        variant: 'success',
        title: "Export Successful",
        description: `Gaps exported successfully as ${filename}`,
      })
    } catch (error) {
      console.error("Export failed:", error);
      toast({
        variant: 'destructive',
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export gaps",
      })
    } finally {
      setExporting(false);
    }
  },[activeFilters.project, filteredGaps, toast]);

  // Loading skeleton UI
  if (isLoading) {
    return (
      <MainLayout>
        <div className="px-8 py-6">
          <Skeleton className="h-12 w-1/3 mb-2" />
          <Skeleton className="h-6 w-2/3 mb-6" />
          
          <Skeleton className="h-12 w-full mb-6" />
          
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-48 w-full" />
            ))}
          </div>
        </div>
      </MainLayout>
    );
  }
  
  // Group gaps by document for document view
  const documentGroups = groupGapsByDocument(filteredGaps);
  const hasDocumentGroups = Object.keys(documentGroups).length > 0;
  
  return (
    <MainLayout>
      <div className="px-8 py-6">
        <div className="flex justify-between items-start mb-4">
          <PageHeader 
            title="All Gaps Overview" 
            description="Implement actions to close sustainability gaps and improve your performance."
          />
          <Button 
            onClick={handleExportGaps}
            variant="outline"
            className="flex items-center gap-2"
            disabled={exporting || isLoading || filteredGaps.length === 0}
          >
            <Download className="h-4 w-4" />
            Export All Gaps
          </Button>
        </div>
        
        <div className="mb-6">
          <GapFilters 
            onFilterChange={setActiveFilters}
            activeFilters={activeFilters}
            availableThemes={uniqueThemes}
            availableProjects={uniqueProjects}
            users={users}
          />
        </div>
        
        <div className="space-y-4">
          {activeFilters.viewType === 'document' ? (
            hasDocumentGroups ? (
              Object.entries(documentGroups).map(([documentId, { name, gaps }]) => (
                <DocumentGapCard
                  key={documentId}
                  documentId={documentId}
                  documentName={name}
                  gaps={gaps}
                  onMarkAsComplete={onMarkComplete}
                  onFixGapClick={handleFixGapClick}
                  onAssignUser={handleAssignGap}
                  users={users}
                  currentActionGapId={currentActionGapId}
                />
              ))
            ) : (
              <EmptyGapsState />
            )
          ) : (
            // List view - show gaps individually
            filteredGaps?.length > 0 ? (
              filteredGaps?.map(gap => (
                <GapCard
                  key={gap.id}
                  gap={gap}
                  onMarkAsComplete={onMarkComplete}
                  onAssignUser={handleAssignGap}
                  users={users}
                  onFixGapClick={() => handleFixGapClick(gap)}
                  currentActionGapId={currentActionGapId}
                />
              ))
            ) : (
              <EmptyGapsState />
            )
          )}
        </div>
      </div>

      <GapFixModal
        open={isFixModalOpen}
        onOpenChange={setIsFixModalOpen}
        relatedDocument={selectedGap?.documents || []}
        gap={selectedGap}
      />
    </MainLayout>
  );
};

export default Improvements;
