import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import OpenAI from 'openai';
import {
  ChatCompletionMessageParam,
  ChatCompletionTool,
  ChatCompletionMessageToolCall,
} from 'openai/resources';
import { GoogleGenAI, FunctionCallingConfigMode, Type } from '@google/genai';
import { LLM_MODELS } from '../../constants';
import { Pinecone } from '@pinecone-database/pinecone';
import { ChatGptService } from '../../llm/chat-gpt.service';
import { CohereService } from '../../llm/cohere.service';
import {addPageRangeBuffer, validatePageNumbersAgainstDocument } from '../../util/common-util';

interface VectorSearchArgs {
  queries: string[]; // Changed to support multiple queries
  limit?: number;
  workspaceId?: string;
}

interface GetDocumentPagesArgs {
  documentId: string;
  pages: number[];
  topKPerPage?: number;
}

interface ParsedAttachmentLink {
  chunkId: string;
  page: string;
  comment: string;
}

interface ParsedAttachment {
  optionId: string;
  documentId: string;
  links: ParsedAttachmentLink[];
}

interface ProjectQuestionData {
  id: string;
  status: string;
  impact: string;
  ecovadis_question: {
    id: string;
    questionCode: string;
    question: string;
    questionName: string;
    indicator: string;
    themeId: string;
    type: string;
  };
}

interface ThemeData {
  id: string;
  title: string;
  description: string;
  project_ecovadis_theme: Array<{
    id: string;
    impact: string;
    issues: Array<{
      impact: string;
      issueId: string;
    }>;
  }>;
}

interface OptionData {
  id: string;
  issueTitle: string;
  instructions: string;
  project_ecovadis_answer: Array<{
    id: string;
    response: string;
  }>;
}

export interface EcoVadisFinalResponse {
  answers: {
    answer_option: {
      id: string;
      answer_name: string;
    };
    document_chunks: {
      document_chunk_id: string;
      comment: string;
    }[];
  }[];
}

export interface EcoVadisResponse {
  answer: EcoVadisFinalResponse;
  evidence_sources: Array<{
    content: string;
    relevance_score: number;
  }>;
  confidence_level: 'high' | 'medium' | 'low';
  conversationHistory: ChatCompletionMessageParam[];
  saveResults?: any[];
}

// Interface for tracking seen chunks across iterations
interface SeenChunksTracker {
  chunkIds: Set<string>;
  chunkTexts: Set<string>;
}

@Injectable()
export class EnhancedEcoVadisAnswerAgentService {
  private readonly logger = new Logger(EnhancedEcoVadisAnswerAgentService.name);
  private readonly openAiClient: OpenAI;
  private readonly geminiClient: GoogleGenAI;
  private pineconeClient: Pinecone;
  private supabase: SupabaseClient;




  // OpenAI-compatible JSON schema (full OpenAPI 3.0 with strict mode)
  private readonly openAiResponseSchema = {
    type: 'object',
    description: 'Single JSON response containing evidence-backed answers for EcoVadis questions',
    properties: {
      answers: {
        type: 'array',
        description: 'List of answer options, each with evidence document chunks and per-link comments. Must contain at least one answer if evidence exists.',
        minItems: 0,
        maxItems: 20,
        items: {
          type: 'object',
          properties: {
            answer_option: {
              type: 'object',
              description: 'Identifier and text of the answer option',
              properties: {
                id: {
                  type: 'string',
                  description: 'Identifier UUID of the answer option',
                  pattern: '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$',
                },
                answer_name: {
                  type: 'string',
                  description: 'Text of the answer option',
                  minLength: 1,
                  maxLength: 200,
                },
              },
              required: ['id', 'answer_name'],
              additionalProperties: false,
            },
            document_chunks: {
              type: 'array',
              description: 'List of document chunks containing the evidence for this option with a brief comment (empty if none).',
              minItems: 0,
              maxItems: 10,
              items: {
                type: 'object',
                properties: {
                  document_chunk_id: {
                    type: 'string',
                    description: 'Identifier UUID of the document chunk',
                    pattern: '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$',
                  },
                  comment: {
                    type: 'string',
                    description: 'Clarifying comment or explanation about why this specific chunk is evidence. Must properly escape quotes using backslash (\\") and avoid control characters.',
                    minLength: 1,
                    maxLength: 1000,
                  },
                },
                required: ['document_chunk_id', 'comment'],
                additionalProperties: false,
              },
            },
          },
          required: ['answer_option', 'document_chunks'],
          additionalProperties: false,
        },
      },
    },
    required: ['answers'],
    additionalProperties: false,
  };



  constructor(
    private configService: ConfigService,
    private chatGptService: ChatGptService,
    private cohereService: CohereService
  ) {
    const azureResource = (process.env.AZURE_RESOURCE || '').replace(/\/$/, '');
    const azureApiKey = process.env.AZURE_OPENAI_API_KEY;

    if (!azureResource || !azureApiKey) {
      throw new Error('Azure OpenAI is not configured. Please set AZURE_RESOURCE and AZURE_OPENAI_API_KEY');
    }

    this.openAiClient = new OpenAI({
      apiKey: azureApiKey,
      baseURL: process.env.HELICONE_AUTH_API_KEY 
        ? 'https://oai.helicone.ai/openai/v1/'
        : `${azureResource}/openai/v1/`,
      defaultQuery: { 'api-version': 'preview' },
      defaultHeaders: {
        'api-key': azureApiKey,
        ...(process.env.HELICONE_AUTH_API_KEY && {
          'Helicone-Auth': `Bearer ${process.env.HELICONE_AUTH_API_KEY}`,
          'Helicone-OpenAI-API-Base': azureResource,
        }),
      },
    });

    // Modern Gemini client with proper Helicone integration
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY missing');
    }
    if (!process.env.HELICONE_AUTH_API_KEY) {
      this.logger.warn('HELICONE_AUTH_API_KEY not found, Helicone logging will be disabled');
    }

    // Modern Gemini SDK with full Helicone integration
    this.geminiClient = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY!,
      ...(process.env.HELICONE_AUTH_API_KEY && {
        httpOptions: {
          baseUrl: 'https://gateway.helicone.ai',
          headers: {
            'Helicone-Auth': `Bearer ${process.env.HELICONE_AUTH_API_KEY}`,
            'Helicone-Target-URL': 'https://generativelanguage.googleapis.com',
          },
        },
      }),
    });

    this.supabase = createClient(
      this.configService.get<string>('SUPABASE_APP_URL'),
      this.configService.get<string>('SUPABASE_SERVICE_KEY')
    );

    this.initializePinecone();
  }

  private async initializePinecone() {
    try {
      this.pineconeClient = new Pinecone({
        apiKey: process.env.PINECONE_API_KEY!,
      });

      this.logger.log('Pinecone client initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Pinecone client:', error);
    }
  }

  private generateSystemPrompt(
    question: string,
    themeCriteria: string,
    sustainabilityIssues: string,
    indicatorCriteria: string,
    scoringFramework: string,
    answerOptions: string
  ): string {
    return `You are an EcoVadis sustainability reporting assistant that helps answer ecovadis questions to fill out the documentation. Your task is to answer Ecovadis questions for a company using documents uploaded by the reporting company. The goal here is to find the most relevant information to answer the question, not just any. In order to answer questions, use the two tools provided to search the Pinecone vector database deeply over a couple of iterations and retrieve specific pages from documents in the end to find details. First task is to find relevant data to Ecovadis questions for the company in their Pinecone vector database, finding as many relevant documents as possible over a few tool call iterations. Then you looks for specific pages from documents that you found might have more relevant information. Finally return the final answer as a json adding as much evidence as reasonably possible to every single answer option.


    1. Use your search_vectorstore tool to research the knowledge base and find relevant evidence. Use usually at least one query per answer option. Run the tool at least 3 consecutive iterations after having retrieved and considered the results of a previous iterations to get a broad overview of available documents but at most 6-8 iterations. Often you will not find the most relevant pieces of information in the first tool call, so you need to iterate over the vector db several times.  Search Strategy:
    - Use declarative search terms (not questions) that match embedded text
    - Search for at least one query per answer option
    - Iterate 3+ times to ensure comprehensive coverage
    - More evidence links are better than fewer
    2. Use your get_document_pages tool to retrieve specific pages from documents when you know the document ID from previous similarity searches to see more pages you have not gotten access to via the vector db yet. Use this where applicable, but don't overdo it.
    3. Iterate over multiple searches to gather comprehensive information. This means not just multiple queries per tool call but also multiple iterations after having seen the results of the previous tool call. Generally speaking, more links are better.
    4. Answers are primarily links to document pages where the evidence is found. Generate accurate, validated links to the most important pages, sorted by relevance with proper citations. For every link you provide, include a short comment, but never include document IDs as they are not useful for users. Make sure to not hallucinate anything in the comments.
    5. Provide confidence levels based on the quality and quantity of evidence found
    6. Respond to all answer options in the answer options set if possible. Better strong evidence, but better weaker evidence than no evidence at all.
    
    Each question in Ecovadis has multiple answer options, each of which requires as an answer a set of evidence documents that contain the information on the company's actions/policies etc. Crucially, the linked document pages themselves are the main answer. A question can have multiple answer options, so you can identify and map to each of them one or several document chunks to respective answer options. All relevant documents the company has are indexed in a Pinecone vector database. Reason first how to phrase the sentence or paragraph effectively that you send to the vector database i.e. that the sentence or paragraph should be similar to the embedded text rather than a question and be broad enough to cover all potential answer options/topics. You should iterate over the db to get a good overview of available documents, but limit the number of iterations to max ~6 (i.e. at least 2-3 max 6 iterations of one or more queries at once). Cohere reranker will rerank the results before you see them. Besides the similarity search, you can also use the get_document_pages tool to retrieve specific pages from documents when you know the document ID from previous similarity searches. Write a json containing the required fields of the answer i.e. a list of document chunks (each chunk representing a page from a document) where the evidence is. Only do so if there are documents that contain correct answers, otherwise return that there are no answers provided. In the array of answers you only need to provide ids of answers which have valid evidence linkable to them.

    When you have finished gathering all necessary information through tool calls, return your final response as a JSON object following the defined schema.

    Below is the issue that we need to provide evidence for, the criteria and the scoring framework. Use those to contemplate which output to create.

    Guidelines:
    - DO NOT RETURN THE JSON OBJECT DUPLICATED. It sometimes happens that you return the same output double. Only return the json once, obviously.
    - Always search the knowledge base before answering. Search longer and deeper to find more relevant content. Empirically speaking, you usually miss a lot of relevant content, so try harder and err towards too much content rather than too little. More links are better.
    - Use search_vectorstore for similarity-based searches when you need to find relevant content
    - Use get_document_pages when you have identified specific documents and want to look at particular pages
    - Use multiple search queries with different keywords
    - search until you either have a comprehensive answer or are confident that there is no more relevant information to be found
    - Cite your sources by referencing the evidence found
    - Indicate your confidence level: high (strong evidence), medium (some evidence), or low (limited evidence)
    - Be concise but comprehensive in your responses
    - Focus on actionable insights and practical guidance. Provide a comment for every link you include.
    - Order the document chunks in the response json by relevance to the question starting with the most relevant one

    **Question**
    ${question}

    **Theme**
    ${themeCriteria || 'Not provided'}

    **Sustainability Issue**
    ${sustainabilityIssues || 'Not provided'}

    **Indicator Criteria**
    ${indicatorCriteria || 'Not provided'}

    **Scoring Framework**
    ${scoringFramework || 'Not provided'}

    **Answer Options** (optional, if we have them)
    ${answerOptions || 'Not provided'}

    Return your final answer as a JSON object with the specified schema. Ensure all quotes in comments are escaped with backslash (\\") and generate no duplicate responses.`;
  }

  private readonly tools: ChatCompletionTool[] = [
    {
      type: 'function',
      function: {
        name: 'search_vectorstore',
        description:
          "Search the Pinecone vector knowledge base for relevant information about sustainability practices, ESG requirements, and EcoVadis criteria within the user's workspace. The results are going to be reranked by the cohere reranker before you see them.",
        parameters: {
          type: 'object',
          properties: {
            queries: {
              type: 'array',
              items: { type: 'string' },
              description:
                'Array of search queries to find relevant information in the knowledge base. You can provide 1-5 queries to cover different aspects of the question. Results will be deduplicated and reranked.',
              minItems: 1,
              maxItems: 5,
            },
          },
          required: ['queries'],
        },
      },
    },
    {
      type: 'function',
      function: {
        name: 'get_document_pages',
        description:
          'Retrieve all chunk texts for the given document and specific page numbers. Use this when you know the document ID and want to fetch specific pages.',
        parameters: {
          type: 'object',
          properties: {
            documentId: {
              type: 'string',
              description: 'Document UUID to fetch pages from',
            },
            pages: {
              type: 'array',
              items: { type: 'integer' },
              description: 'Array of page numbers to fetch (e.g., [1, 2, 3])',
            },
            topKPerPage: {
              type: 'integer',
              description: 'Optional limit of chunks per page (default: 5)',
            },
          },
          required: ['documentId', 'pages'],
        },
      },
    },
  ];



  /**
   * Answer an EcoVadis question using the agent with vector search capabilities
   */
  async answerEcoVadisQuestion({
    projectId,
    questionId,
  }: {
    projectId: string;
    questionId: string;
  }): Promise<EcoVadisResponse> {
    // Default to Gemini if API key is available, unless USE_OPENAI is explicitly set
    const useOpenAI = process.env.USE_OPENAI === 'true' || !process.env.GEMINI_API_KEY;

    if (useOpenAI) {
      this.logger.log(`EcoVadis answer generation started: Azure OpenAI (project: ${projectId}, question: ${questionId})`);
      return this.answerEcoVadisQuestionWithOpenAI({ projectId, questionId });
    } else {
      this.logger.log(`EcoVadis answer generation started: Gemini (project: ${projectId}, question: ${questionId})`);
      return this.answerEcoVadisQuestionWithGemini({ projectId, questionId });
    }
  }

  /**
   * Generic method to chat with any model (OpenAI or Gemini)
   * Phase 1: Function calling only (no JSON schema for Gemini)
   */
  private async chatWithModel(
    client: OpenAI | GoogleGenAI,
    model: string,
    messages: ChatCompletionMessageParam[],
    tools: ChatCompletionTool[]
  ) {
    const isGemini = model.includes('gemini');

    if (isGemini) {
      // Phase 1: Gemini function calling (no JSON schema - they are mutually exclusive)
      
      // Convert messages to proper Gemini format with role mapping
      const contents = messages.map(message => {
        // Gemini only supports 'user' and 'model' roles
        let geminiRole: 'user' | 'model';
        if (message.role === 'assistant') {
          geminiRole = 'model';
        } else {
          // Map system, user, and tool roles to 'user'
          geminiRole = 'user';
        }
        
        return {
          role: geminiRole,
          parts: [{ text: String(message.content || '') }]
        };
      });

      const result = await (client as GoogleGenAI).models.generateContent({
        model,
        contents,
        config: {
          tools: [{
            functionDeclarations: [
              {
                name: 'search_vectorstore',
                description: "Search the Pinecone vector knowledge base for relevant information about sustainability practices, ESG requirements, and EcoVadis criteria within the user's workspace",
                parameters: {
                  type: Type.OBJECT,
                  properties: {
                    queries: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: 'Array of search queries to find relevant information in the knowledge base. You can provide 1-5 queries to cover different aspects of the question. Results will be deduplicated and reranked.',
                    },
                  },
                  required: ['queries'],
                },
              },
              {
                name: 'get_document_pages',
                description: 'Retrieve all chunk texts for the given document and specific page numbers. Use this when you know the document ID and want to fetch specific pages.',
                parameters: {
                  type: Type.OBJECT,
                  properties: {
                    documentId: {
                      type: Type.STRING,
                      description: 'Document UUID to fetch pages from',
                    },
                    pages: {
                      type: Type.ARRAY,
                      items: { type: Type.NUMBER },
                      description: 'Array of page numbers to fetch (e.g., [1, 2, 3])',
                    },
                    topKPerPage: {
                      type: Type.NUMBER,
                      description: 'Optional limit of chunks per page (default: 5)',
                    },
                  },
                  required: ['documentId', 'pages'],
                },
              }
            ]
          }],
          toolConfig: {
            functionCallingConfig: {
              mode: FunctionCallingConfigMode.AUTO
            }
          },
          temperature: 0
        }
      });

      // Robust parsing for function calls with fallbacks
      const functionCalls = result.functionCalls ?? 
                           result.candidates?.flatMap(c =>
                             c.content.parts
                              .filter(p => 'functionCall' in p)
                              .map(p => p.functionCall)
                           );

      if (functionCalls && functionCalls.length > 0) {
        // Convert Gemini function calls to OpenAI format for compatibility
        const toolCalls = functionCalls.map((fc, index) => ({
          id: `call_${index}`,
          type: 'function' as const,
          function: {
            name: fc.name,
            arguments: JSON.stringify(fc.args)
          }
        }));
        
        return {
          role: 'assistant' as const,
          content: result.text || null,
          tool_calls: toolCalls,
        };
      }
      
      return {
        role: 'assistant' as const,
        content: result.text,
        tool_calls: undefined,
      };
    } else {
      try {
        this.logger.log('Azure OpenAI request: function calling phase');
        const completion = await (client as OpenAI).chat.completions.create({
          model: 'gpt-5-stefan',
          messages,
          tools,
          tool_choice: 'auto',
          verbosity: 'low',
        } as any);
        this.logger.log('Azure OpenAI success: function calling phase');
        
        const message = completion.choices[0]?.message;
        if (!message) {
          throw new Error('No message returned from OpenAI');
        }
        
        return {
          role: 'assistant' as const,
          content: message.content,
          tool_calls: message.tool_calls,
        };
      } catch (error) {
        this.logger.error(`Azure OpenAI failed: function calling phase - ${error.message}`);
        throw error;
      }
    }
  }

  /**
   * Chat with model requesting final JSON response (no tool calls)
   * Phase 2: JSON schema only (no function calling for Gemini)
   */
  private async chatWithModelFinalResponse(
    client: OpenAI | GoogleGenAI,
    model: string,
    messages: ChatCompletionMessageParam[]
  ) {
    const isGemini = model.includes('gemini');

    if (isGemini) {
      // Phase 2: Gemini JSON schema (no function calling - they are mutually exclusive)
      
      // Convert messages to proper Gemini format with role mapping
      const contents = messages.map(message => {
        // Gemini only supports 'user' and 'model' roles
        let geminiRole: 'user' | 'model';
        if (message.role === 'assistant') {
          geminiRole = 'model';
        } else {
          // Map system, user, and tool roles to 'user'
          geminiRole = 'user';
        }
        
        return {
          role: geminiRole,
          parts: [{ text: String(message.content || '') }]
        };
      });

      const result = await (client as GoogleGenAI).models.generateContent({
        model,
        contents,
        config: {
          responseMimeType: 'application/json',
          responseSchema: this.openAiResponseSchema,
          temperature: 0
        }
      });

      // In Phase 2, we should only get text responses (JSON), not function calls
      return {
        role: 'assistant' as const,
        content: result.text,
        tool_calls: undefined,
      };
    } else {
      try {
        this.logger.log('Azure OpenAI request: JSON response phase');
        const completion = await (client as OpenAI).chat.completions.create({
          model: 'gpt-5-stefan',
          messages,
          response_format: {
            type: 'json_schema',
            json_schema: {
              name: 'ecovadis_answer_schema',
              schema: this.openAiResponseSchema,
              strict: true,
            },
          },
          verbosity: 'low',
        } as any);
        this.logger.log('Azure OpenAI success: JSON response phase');
        
        const message = completion.choices[0]?.message;
        if (!message) {
          throw new Error('No message returned from OpenAI');
        }
        
        return {
          role: 'assistant' as const,
          content: message.content,
          tool_calls: undefined,
        };
      } catch (error) {
        this.logger.error(`Azure OpenAI failed: JSON response phase - ${error.message}`);
        throw error;
      }
    }
  }

  /**
   * Convert OpenAI messages to Gemini prompt format
   */
  private convertMessagesToGeminiPrompt(messages: ChatCompletionMessageParam[]): string {
    return messages
      .map((message) => {
        if (message.role === 'system') {
          return `System: ${message.content}`;
        } else if (message.role === 'user') {
          return `User: ${message.content}`;
        } else if (message.role === 'assistant') {
          return `Assistant: ${message.content}`;
        } else if (message.role === 'tool') {
          return `Tool Response: ${message.content}`;
        }
        return '';
      })
      .filter(Boolean)
      .join('\n\n');
  }

  /**
   * Generic method to handle tool calls for any vendor
   */
  private async handleToolCallsGeneric(
    client: OpenAI | GoogleGenAI,
    model: string,
    messages: ChatCompletionMessageParam[],
    toolCalls: ChatCompletionMessageToolCall[],
    workspaceId: string,  // Made required for security
    projectId?: string,
    options?: OptionData[],
    seenChunks?: SeenChunksTracker
  ): Promise<EcoVadisResponse> {
    // Execute tool calls
    for (const toolCall of toolCalls) {
      if (toolCall.function.name === 'search_vectorstore') {
        const args = JSON.parse(
          toolCall.function.arguments
        ) as VectorSearchArgs;
        const searchResult = await this.searchVectorStoreMultiple(
          args.queries,
          args.limit,
          workspaceId,
          seenChunks
        );

        messages.push({
          role: 'tool',
          tool_call_id: toolCall.id,
          content: JSON.stringify(searchResult),
        });
      } else if (toolCall.function.name === 'get_document_pages') {
        const args = JSON.parse(
          toolCall.function.arguments
        ) as GetDocumentPagesArgs;
        const documentPages = await this.getDocumentPages(args.documentId, args.pages, args.topKPerPage, workspaceId);
        messages.push({
          role: 'tool',
          tool_call_id: toolCall.id,
          content: JSON.stringify(documentPages),
        });
      }
    }

    // Continue conversation with tool results - request final JSON response
    const finalMessage = await this.chatWithModelFinalResponse(client, model, messages);
    messages.push(finalMessage);

    // Check if more tool calls are needed (recursive handling)
    if (finalMessage.tool_calls && finalMessage.tool_calls.length > 0) {
      return await this.handleToolCallsGeneric(
        client,
        model,
        messages,
        finalMessage.tool_calls,
        workspaceId,
        projectId,
        options,
        seenChunks
      );
    }

    const validOptionIds = options?.map((option) => option.id) || [];
    const result = await this.parseAgentResponse(
      finalMessage.content || '',
      messages,
      projectId || '',
      validOptionIds,
      workspaceId || ''
    );
    this.logger.log(`EcoVadis tool-based answer generation completed (project: ${projectId})`);
    return result;
  }

  /**
   * Generic method to answer EcoVadis questions - shared logic for both vendors
   */
  private async answerEcoVadisQuestionGeneric({
    projectId,
    questionId,
    client,
    model,
    vendorName,
  }: {
    projectId: string;
    questionId: string;
    client: OpenAI | GoogleGenAI;
    model: string;
    vendorName: string;
  }): Promise<EcoVadisResponse> {
    this.logger.log(
      `Answering EcoVadis question with ${vendorName} for project ${projectId}, question ${questionId}`
    );

    try {
      // 0. Get workspace ID for the project to ensure data isolation
      const workspaceId = await this.getWorkspaceIdFromProject(projectId);
      this.logger.log(`Processing question for workspace: ${workspaceId}`);

      // 1. Fetch all required data from Supabase
      const {
        projectQuestion,
        theme,
        options,
        scoringFramework,
        scoringFrameworkDrivers,
        sustainabilityIssues,
      } = await this.fetchEcoVadisData(projectId, questionId);

      // 2. Format the data for the AI agent
      const {
        question,
        themeCriteria,
        indicatorCriteria,
        answerOptions,
        formattedScoringFramework,
      } = this.formatDataForPrompt(
        projectQuestion,
        theme,
        options,
        scoringFramework,
        scoringFrameworkDrivers,
        sustainabilityIssues
      );

      const messages: ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: this.generateSystemPrompt(
            question,
            themeCriteria,
            sustainabilityIssues,
            indicatorCriteria,
            formattedScoringFramework,
            answerOptions
          ),
        },
        { role: 'user', content: question },
      ];

      const isGemini = model.includes('gemini');

      // Use the same agentic tool calling approach for both OpenAI and Gemini
      const message = await this.chatWithModel(client, model, messages, this.tools);
      messages.push(message);

      // Handle tool calls for both vendors
      if (message.tool_calls && message.tool_calls.length > 0) {
        // Initialize chunk tracker for deduplication across iterations
        const seenChunks: SeenChunksTracker = {
          chunkIds: new Set<string>(),
          chunkTexts: new Set<string>(),
        };

        return await this.handleToolCallsGeneric(
          client,
          model,
          messages,
          message.tool_calls,
          workspaceId,
          projectId,
          options,
          seenChunks
        );
      }

      // Parse and return direct response if no tool calls
      const validOptionIds = options.map((option) => option.id);
      const result = await this.parseAgentResponse(
        message.content || '',
        messages,
        projectId,
        validOptionIds,
        workspaceId
      );
      this.logger.log(`EcoVadis answer generation completed: ${vendorName} (project: ${projectId})`);
      return result;
    } catch (error) {
      this.logger.error(`EcoVadis answer generation failed: ${vendorName} (project: ${projectId}) - ${error.message}`);
      throw new Error(`Failed to answer EcoVadis question with ${vendorName}: ${error.message}`);
    }
  }

  /**
   * Answer an EcoVadis question using OpenAI with single loop approach
   */
  async answerEcoVadisQuestionWithOpenAI({
    projectId,
    questionId,
  }: {
    projectId: string;
    questionId: string;
  }): Promise<EcoVadisResponse> {
    this.logger.log(`EcoVadis answer generation started: Azure OpenAI (project: ${projectId}, question: ${questionId})`);
    
    try {
      // 1. Get workspace ID and fetch data
      const workspaceId = await this.getWorkspaceIdFromProject(projectId);
      const {
        projectQuestion,
        theme,
        options,
        scoringFramework,
        scoringFrameworkDrivers,
        sustainabilityIssues,
      } = await this.fetchEcoVadisData(projectId, questionId);

      // 2. Format data and create initial messages
      const {
        question,
        themeCriteria,
        indicatorCriteria,
        answerOptions,
        formattedScoringFramework,
      } = this.formatDataForPrompt(
        projectQuestion,
        theme,
        options,
        scoringFramework,
        scoringFrameworkDrivers,
        sustainabilityIssues
      );

      const messages: ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: this.generateSystemPrompt(
            question,
            themeCriteria,
            sustainabilityIssues,
            indicatorCriteria,
            formattedScoringFramework,
            answerOptions
          ),
        },
        { role: 'user', content: question },
      ];

      // 3. Initialize tracking
      const seenChunks: SeenChunksTracker = {
        chunkIds: new Set<string>(),
        chunkTexts: new Set<string>(),
      };
      const maxIterations = 8; // Safety cap
      let iterations = 0;

      // 4. Main loop - continue until no more tool calls
      while (iterations < maxIterations) {
        iterations++;
        
        // Call the model with tools enabled
        const response = await this.openAiClient.chat.completions.create({
          model: 'gpt-5-stefan',
          messages,
          tools: this.tools,
          tool_choice: 'auto',
          verbosity: 'low',
        } as any);

        const message = response.choices[0]?.message;
        if (!message) {
          throw new Error('No message returned from OpenAI');
        }
        
        // Add assistant message to history
        messages.push(message);

        // Check if model wants to call tools
        const toolCalls = message.tool_calls || [];
        if (toolCalls.length === 0) {
          // No more tool calls - break the loop and do final structured output call
          break;
        }

        // Execute tool calls
        for (const toolCall of toolCalls) {
          const args = JSON.parse(toolCall.function.arguments || '{}');
          let toolOutput: any;

          if (toolCall.function.name === 'search_vectorstore') {
            toolOutput = await this.searchVectorStoreMultiple(
              args.queries,
              args.limit || 10,
              workspaceId,
              seenChunks
            );
          } else if (toolCall.function.name === 'get_document_pages') {
            toolOutput = await this.getDocumentPages(
              args.documentId,
              args.pages,
              args.topKPerPage || 5,
              workspaceId
            );
          }

          // Add tool response to messages
          messages.push({
            role: 'tool',
            tool_call_id: toolCall.id,
            content: JSON.stringify(toolOutput),
          });
        }
      }

      if (iterations >= maxIterations) {
        this.logger.warn('Max iterations reached, proceeding to final response');
      }

      // 5. Final structured output call to ensure correct JSON schema
      const finalResponse = await this.openAiClient.chat.completions.create({
        model: 'gpt-5-stefan',
        messages,
        response_format: {
          type: 'json_schema',
          json_schema: {
            name: 'ecovadis_answer',
            schema: this.openAiResponseSchema,
            strict: true,
          },
        },
        verbosity: 'low',
      } as any);

      const finalMessage = finalResponse.choices[0]?.message;
      if (!finalMessage || !finalMessage.content) {
        throw new Error('No final response from model');
      }

      // 6. Parse and save results
      const parsed = JSON.parse(finalMessage.content);
      const validOptionIds = options.map(o => o.id);
      
      await this.saveAiAnswerResults(
        projectId,
        finalMessage.content,
        validOptionIds,
        workspaceId
      );

      return {
        answer: parsed,
        evidence_sources: [],
        confidence_level: 'high',
        conversationHistory: messages,
      };

    } catch (error) {
      this.logger.error(`Failed to answer EcoVadis question: ${error.message}`);
      throw error;
    }

  }



  /**
   * Answer an EcoVadis question using Gemini 2.5 Pro with JSON schema support
   * Light adapter that handles Gemini-specific quirks if needed
   */
  async answerEcoVadisQuestionWithGemini({
    projectId,
    questionId,
  }: {
    projectId: string;
    questionId: string;
  }): Promise<EcoVadisResponse> {
    // Gemini-specific adapter - add any vendor quirks here if needed
    // E.g., patching toolCalls -> tool_calls, missing IDs, etc.
    const client = this.geminiClient;
    const model = LLM_MODELS['gemini-2.5-pro'];
    const vendorName = 'Gemini 2.5 Pro';

    return this.answerEcoVadisQuestionGeneric({
      projectId,
      questionId,
      client,
      model,
      vendorName,
    });
  }



  /**
   * Search vector store with multiple queries, deduplication, and reranking
   */
  private async searchVectorStoreMultiple(
    queries: string[],
    limit: number = 10,
    workspaceId: string,  // Made required
    seenChunks?: SeenChunksTracker
  ): Promise<
    Array<{
      content: string;
      relevance_score: number;
      metadata: Record<string, any>;
    }>
  > {
    if (!queries || queries.length === 0) {
      this.logger.warn('No queries provided to searchVectorStoreMultiple');
      return [];
    }

    if (!workspaceId) {
      throw new Error('workspaceId is required for workspace isolation');
    }

    // Cap the number of queries to prevent abuse (max 5 per iteration)
    const limitedQueries = queries.slice(0, 5);
    this.logger.log(
      `Hybrid search with ${limitedQueries.length} queries: ${limitedQueries.join(', ')}`
    );

    // Search with each query in parallel, using higher k for reranking
    const vectorSearchTasks = limitedQueries.map((query, index) =>
      this.searchVectorStore(query, 50, workspaceId).then(results =>
        results.map(result => ({
          ...result,
          metadata: {
            ...result.metadata,
            source_query_index: index,
            source_query: query,
          }
        }))
      )
    );

    // Also perform keyword search in parallel
    const keywordSearchTask = this.searchKeywords(
      limitedQueries,
      20,
      workspaceId
    ).then(results =>
      results.map(result => ({
        ...result,
        metadata: {
          ...result.metadata,
          source_query_index: -1, // Keyword search doesn't belong to specific query
          source_query: 'keyword_search',
        }
      }))
    );

    const [vectorResults, keywordResults] = await Promise.all([
      Promise.all(vectorSearchTasks).then((results) => results.flat()),
      keywordSearchTask,
    ]);

    // Combine vector and keyword results
    const allResults = [...vectorResults, ...keywordResults];
    this.logger.log(
      `Hybrid search yielded ${vectorResults.length} vector + ${keywordResults.length} keyword = ${allResults.length} total results`
    );

    // Flatten and deduplicate results
    const deduplicatedResults = this.deduplicateSearchResults(
      allResults,
      seenChunks
    );

    if (deduplicatedResults.length === 0) {
      this.logger.warn('No results after deduplication');
      return [];
    }

    // Perform balanced per-query reranking
    return await this.performBalancedPerQueryReranking(
      deduplicatedResults,
      limitedQueries,
      limit
    );
  }

  /**
   * Perform balanced per-query reranking to ensure fair representation across all queries
   */
  private async performBalancedPerQueryReranking(
    deduplicatedResults: Array<{
      content: string;
      relevance_score: number;
      metadata: Record<string, any>;
    }>,
    queries: string[],
    totalLimit: number
  ): Promise<Array<{
    content: string;
    relevance_score: number;
    metadata: Record<string, any>;
  }>> {
    // Group results by source query
    const resultsByQuery = new Map<number, Array<{
      content: string;
      relevance_score: number;
      metadata: Record<string, any>;
    }>>();

    // Initialize groups for all queries
    queries.forEach((_, index) => {
      resultsByQuery.set(index, []);
    });

    // Keyword search results (source_query_index: -1)
    resultsByQuery.set(-1, []);

    // Group results by their source query
    deduplicatedResults.forEach(result => {
      const queryIndex = result.metadata.source_query_index ?? -1;
      const group = resultsByQuery.get(queryIndex) || [];
      group.push(result);
      resultsByQuery.set(queryIndex, group);
    });

    // Calculate balanced allocation per query
    const activeGroups = Array.from(resultsByQuery.entries()).filter(([_, results]) => results.length > 0);
    const numActiveGroups = activeGroups.length;
    
    if (numActiveGroups === 0) {
      this.logger.warn('No active query groups found');
      return [];
    }

    const baseAllocationPerGroup = Math.floor(totalLimit / numActiveGroups);
    const remainingSlots = totalLimit % numActiveGroups;

    this.logger.log(
      `Performing balanced reranking: ${numActiveGroups} active groups, ${baseAllocationPerGroup} base allocation per group, ${remainingSlots} remaining slots`
    );

    // Rerank each group separately and collect results
    const rerankedGroups: Array<{
      content: string;
      relevance_score: number;
      metadata: Record<string, any>;
    }> = [];

    for (let i = 0; i < activeGroups.length; i++) {
      const [queryIndex, groupResults] = activeGroups[i];
      
      // Calculate allocation for this group (distribute remaining slots fairly)
      const groupAllocation = baseAllocationPerGroup + (i < remainingSlots ? 1 : 0);
      
      if (groupResults.length === 0) continue;

      let rerankedGroupResults: Array<{
        content: string;
        relevance_score: number;
        metadata: Record<string, any>;
      }>;

      if (queryIndex === -1) {
        // Keyword search results - just take top results by existing score
        rerankedGroupResults = groupResults
          .sort((a, b) => b.relevance_score - a.relevance_score)
          .slice(0, groupAllocation)
          .map(result => ({
            ...result,
            metadata: {
              ...result.metadata,
              reranked: false,
              allocation_group: 'keyword_search',
              group_allocation: groupAllocation,
            }
          }));
      } else {
        // Vector search results - rerank with original query
        const query = queries[queryIndex];
        
        try {
          const reranked = await this.cohereService.rerankResults(
            query,
            groupResults.map(r => ({
              content: r.content,
              metadata: r.metadata,
            })),
            groupAllocation
          );

          rerankedGroupResults = reranked.map(result => ({
            content: result.content,
            relevance_score: result.relevance_score,
            metadata: {
              ...result.metadata,
              reranked: true,
              rerank_query: query,
              allocation_group: `query_${queryIndex}`,
              group_allocation: groupAllocation,
              original_index: result.original_index,
            }
          }));
        } catch (error) {
          this.logger.warn(`Failed to rerank group ${queryIndex} with query "${query}": ${error.message}`);
          // Fallback to original scoring
          rerankedGroupResults = groupResults
            .sort((a, b) => b.relevance_score - a.relevance_score)
            .slice(0, groupAllocation)
            .map(result => ({
              ...result,
              metadata: {
                ...result.metadata,
                reranked: false,
                rerank_error: error.message,
                allocation_group: `query_${queryIndex}_fallback`,
                group_allocation: groupAllocation,
              }
            }));
        }
      }

      rerankedGroups.push(...rerankedGroupResults);
      
      this.logger.log(
        `Group ${queryIndex} (${queryIndex === -1 ? 'keyword' : `query: "${queries[queryIndex]}"`}): ${groupResults.length} → ${rerankedGroupResults.length} results`
      );
    }

    // Final sort by relevance score across all groups
    const finalResults = rerankedGroups
      .sort((a, b) => b.relevance_score - a.relevance_score)
      .slice(0, totalLimit);

    this.logger.log(
      `Balanced per-query reranking complete: ${deduplicatedResults.length} → ${finalResults.length} results`
    );

    return finalResults;
  }

  /**
   * Deduplicate search results based on chunk IDs and content similarity
   */
  private deduplicateSearchResults(
    results: Array<{
      content: string;
      relevance_score: number;
      metadata: Record<string, any>;
    }>,
    seenChunks?: SeenChunksTracker
  ): Array<{
    content: string;
    relevance_score: number;
    metadata: Record<string, any>;
  }> {
    const deduplicatedResults: Array<{
      content: string;
      relevance_score: number;
      metadata: Record<string, any>;
    }> = [];

    const currentIterationChunkIds = new Set<string>();
    const currentIterationChunkTexts = new Set<string>();

    for (const result of results) {
      const chunkId = result.metadata?.chunk_id;
      const contentHash = this.hashContent(result.content);

      // Skip if we've seen this chunk ID before (across iterations)
      if (seenChunks?.chunkIds.has(chunkId)) {
        continue;
      }

      // Skip if we've seen similar content before (across iterations)
      if (seenChunks?.chunkTexts.has(contentHash)) {
        continue;
      }

      // Skip if we've seen this chunk in the current iteration
      if (
        currentIterationChunkIds.has(chunkId) ||
        currentIterationChunkTexts.has(contentHash)
      ) {
        continue;
      }

      // Add to current iteration tracking
      if (chunkId) {
        currentIterationChunkIds.add(chunkId);
        seenChunks?.chunkIds.add(chunkId);
      }
      currentIterationChunkTexts.add(contentHash);
      seenChunks?.chunkTexts.add(contentHash);

      deduplicatedResults.push(result);
    }

    this.logger.log(
      `Deduplication: ${results.length} -> ${deduplicatedResults.length} results`
    );
    return deduplicatedResults;
  }

  /**
   * Create a simple hash of content for deduplication
   */
  private hashContent(content: string): string {
    // Simple hash function - normalize whitespace and get first 200 chars
    const normalized = content.trim().replace(/\s+/g, ' ').toLowerCase();
    return normalized.substring(0, 200);
  }

  /**
   * Simple keyword-based search in Supabase as a complement to vector search
   * This provides a basic BM25-like functionality without additional infrastructure
   */
  private async searchKeywords(
    queries: string[],
    limit: number = 20,
    workspaceId: string  // Made required
  ): Promise<
    Array<{
      content: string;
      relevance_score: number;
      metadata: Record<string, any>;
    }>
  > {
    if (!queries || queries.length === 0) {
      return [];
    }

    if (!workspaceId) {
      throw new Error('workspaceId is required for workspace isolation');
    }

    try {
      // Extract keywords from all queries
      const allKeywords = queries
        .flatMap(query =>
          query.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 3) // Filter out short words

        )
        .filter((word, index, arr) => arr.indexOf(word) === index); // Remove duplicates

      if (allKeywords.length === 0) {
        return [];
      }

      this.logger.log(
        `Keyword search with terms: ${allKeywords.slice(0, 5).join(', ')}${allKeywords.length > 5 ? '...' : ''}`
      );

      // Build keyword search query using PostgreSQL full-text search
      let query = this.supabase.from('document_chunk').select(`
          id,
          content,
          page,
          documentId,
          document:documentId (
            name,
            workspaceId,
            year,
            documentType
          )
        `);

      // Add mandatory workspace filtering
      query = query.eq('document.workspaceId', workspaceId);

      // Use PostgreSQL's full-text search with OR logic for keywords
      const searchTerms = allKeywords.slice(0, 10).join(' | '); // Limit to 10 keywords, use OR logic
      query = query.textSearch('content', searchTerms);

      // Order by relevance and limit results
      query = query.limit(limit);

      const { data: chunks, error } = await query;

      if (error) {
        this.logger.error('Keyword search error:', error);
        return [];
      }

      if (!chunks || chunks.length === 0) {
        this.logger.log('No keyword search results found');
        return [];
      }

      // Calculate simple relevance scores based on keyword matches
      const results = chunks.map((chunk) => {
        const content = chunk.content || '';
        const contentLower = content.toLowerCase();

        // Count keyword matches (simple scoring)

        const matches = allKeywords.filter((keyword) =>
          contentLower.includes(keyword)
        ).length;

        const relevanceScore = Math.min(0.8, matches / allKeywords.length); // Cap at 0.8 to distinguish from vector search

        const document = Array.isArray(chunk.document)
          ? chunk.document[0]
          : chunk.document;

        return {
          content,
          relevance_score: relevanceScore,
          metadata: {
            source: 'keyword_search',
            title: document?.name || 'Unknown Document',
            chunk_id: chunk.id,
            document_id: chunk.documentId,
            page: chunk.page,
            workspace_id: document?.workspaceId,
            year: document?.year,
            document_type: document?.documentType,
            keyword_matches: matches,
            total_keywords: allKeywords.length,
            search_type: 'keyword',
            timestamp: new Date().toISOString(),
          },
        };
      });

      this.logger.log(`Keyword search returned ${results.length} results`);
      return results;
    } catch (error) {
      this.logger.error('Error in keyword search:', error);
      return [];
    }
  }

  private async searchVectorStore(
    query: string,
    limit: number = 7,
    workspaceId: string  // Made required
  ): Promise<
    Array<{
      content: string;
      relevance_score: number;
      metadata: Record<string, any>;
    }>
  > {
    this.logger.log(
      `Searching Pinecone knowledge base for: ${query} (workspace: ${workspaceId})`
    );

    if (!workspaceId) {
      throw new Error('workspaceId is required for workspace isolation');
    }

    if (!this.pineconeClient) {
      this.logger.error('Pinecone client not initialized');
      throw new Error(
        'Pinecone client not initialized. Please check PINECONE_API_KEY environment variable.'
      );
    }

    try {
      // Get index name and host from environment variables
      const indexName = process.env.PINECONE_INDEX_NAME;
      const indexHost = process.env.PINECONE_INDEX_HOST;
      const namespace = process.env.PINECONE_NAMESPACE;

      if (!indexHost) {
        throw new Error('PINECONE_INDEX_HOST environment variable is required');
      }

      // Generate embedding for the search query using Azure OpenAI
      this.logger.log('Generating embedding for search query...');
      const queryVector = await this.chatGptService.createEmbedding(query);

      this.logger.log(
        `Generated embedding with ${queryVector.length} dimensions`
      );

      // Get the index and namespace
      const index = this.pineconeClient.index(indexName, indexHost);
      const namespaceIndex = index.namespace(namespace);

      // Prepare query with workspace filtering
      const queryParams = {
        vector: queryVector,
        topK: limit,
        includeValues: false,
        includeMetadata: true,
        filter: {
          workspace_id: { $eq: workspaceId },
        },
      };

      // Perform dense vector search with workspace filtering
      const searchResult = await namespaceIndex.query(queryParams);

      this.logger.log(
        `Pinecone returned ${searchResult.matches.length} results for query: ${query}${workspaceId ? ` in workspace ${workspaceId}` : ''}`
      );

      // Transform Pinecone results to expected format
      const results = searchResult.matches.map((match) => ({
        content: String(match.metadata?.chunk_text || 'No content available'),
        relevance_score: match.score || 0.0,
        metadata: {
          source: 'pinecone_knowledge_base',
          title: match.metadata?.title,
          document_source: match.metadata?.source,
          chunk_id: match.metadata?.chunk_id || match.id,
          document_id: match.metadata?.document_id,
          page: match.metadata?.page,
          workspace_id: match.metadata?.workspace_id,
          year: match.metadata?.year,
          sub_chunk_index: match.metadata?.sub_chunk_index,
          token_count: match.metadata?.token_count,
          score: match.score,
          query: query,
          timestamp: new Date().toISOString(),
        },
      }));

      return results;
    } catch (error) {
      this.logger.error('Error searching Pinecone knowledge base:', error);

      // Fallback to placeholder if Pinecone fails
      this.logger.warn(
        'Falling back to placeholder results due to Pinecone error'
      );
      return [
        {
          content: `Error accessing Pinecone knowledge base for query: ${query}. Error: ${error.message}`,
          relevance_score: 0.1,
          metadata: {
            error: true,
            fallback: true,
            error_message: error.message,
            timestamp: new Date().toISOString(),
          },
        },
      ];
    }
  }

  /**
   * Retrieve specific document pages by document ID and page numbers
   * Queries the database directly instead of using Pinecone
   */
  private async getDocumentPages(
    documentId: string,
    pages: number[],
    topKPerPage: number = 5,
    workspaceId: string  // Made required
  ): Promise<
    Array<{
      chunkId: string;
      page: number;
      text: string;
      score: number;
      metadata: Record<string, any>;
    }>
  > {
    this.logger.log(
      `Retrieving document pages for document ${documentId}, pages: ${pages.join(', ')} (workspace: ${workspaceId})`
    );

    if (!workspaceId) {
      throw new Error('workspaceId is required for workspace isolation');
    }

    try {
      // Build the query to fetch document chunks with workspace filtering
      let query = this.supabase
        .from('document_chunk')
        .select(
          `
          id,
          documentId,
          page,
          content,
          metadataJson,
          createdAt,
          document:documentId (
            id,
            name,
            workspaceId,
            year,
            documentType
          )
        `
        )
        .eq('documentId', documentId)
        .in(
          'page',
          pages.map((p) => p.toString())
        ); // Convert pages to strings as stored in DB

      // Add mandatory workspace filtering
      query = query.eq('document.workspaceId', workspaceId);

      // Order by page number and limit results
      query = query
        .order('page', { ascending: true })
        .limit(pages.length * topKPerPage);

      const { data: documentChunks, error } = await query;

      if (error) {
        throw new Error(`Database query failed: ${error.message}`);
      }

      if (!documentChunks || documentChunks.length === 0) {
        this.logger.warn(
          `No document chunks found for document ${documentId}, pages: ${pages.join(', ')}`
        );
        return [];
      }

      this.logger.log(
        `Database returned ${documentChunks.length} chunks for document ${documentId}, pages: ${pages.join(', ')}`
      );

      // Transform results to match expected format
      const results = documentChunks.map((chunk) => {
        const document = Array.isArray(chunk.document)
          ? chunk.document[0]
          : chunk.document;

        return {
          chunkId: chunk.id,
          page: parseInt(chunk.page) || 1,
          text: chunk.content || 'No content available',
          score: 1.0, // Set to 1.0 since this is an exact match by document ID and page
          metadata: {
            source: 'database_document_pages',
            title: document?.name || 'Unknown Document',
            document_source: document?.name,
            chunk_id: chunk.id,
            document_id: chunk.documentId,
            page: chunk.page,
            workspace_id: document?.workspaceId,
            year: document?.year,
            document_type: document?.documentType,
            metadata_json: chunk.metadataJson,
            created_at: chunk.createdAt,
            query_type: 'document_pages',
            requested_pages: pages,
            timestamp: new Date().toISOString(),
          },
        };
      });

      // Sort by page number to ensure consistent ordering
      results.sort((a, b) => a.page - b.page);

      return results;
    } catch (error) {
      this.logger.error(
        'Error retrieving document pages from database:',
        error
      );

      // Return error result for debugging
      return [
        {
          chunkId: 'error',
          page: 0,
          text: `Error retrieving document pages for document ${documentId}. Error: ${error.message}`,
          score: 0.0,
          metadata: {
            error: true,
            fallback: true,
            error_message: error.message,
            document_id: documentId,
            requested_pages: pages,
            timestamp: new Date().toISOString(),
          },
        },
      ];
    }
  }

  /**
   * Parse the agent response into structured format
   */
  private async parseAgentResponse(
    content: string,
    conversationHistory: ChatCompletionMessageParam[],
    projectId: string,
    validOptionIds: string[],
    workspaceId: string
  ): Promise<EcoVadisResponse> {
    try {
      // Clean the content to handle various JSON formatting from different models
      let cleanedContent = content.trim();

      // Handle JSON wrapped in markdown code blocks (common with Gemini)
      if (
        cleanedContent.startsWith('```json') &&
        cleanedContent.endsWith('```')
      ) {
        cleanedContent = cleanedContent.slice(7, -3).trim(); // Remove ```json and ```
      } else if (
        cleanedContent.startsWith('```') &&
        cleanedContent.endsWith('```')
      ) {
        cleanedContent = cleanedContent.slice(3, -3).trim(); // Remove generic ``` blocks
      }

      // Handle cases where there might be extra text before/after JSON
      const jsonMatch = cleanedContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanedContent = jsonMatch[0];
      }


      this.logger.log(
        'Attempting to parse cleaned content:',
        cleanedContent.substring(0, 200) + '...'
      );

      // The response should be in the format defined by our JSON schema
      const parsed: EcoVadisFinalResponse = JSON.parse(cleanedContent);

      // Validate the parsed response has the expected structure
      if (!parsed.answers || !Array.isArray(parsed.answers)) {
        throw new Error('Parsed response missing required "answers" array');
      }

      // store the results in the database
      let savedResults = false;
      for (const answer of parsed.answers) {
        if (answer.document_chunks && answer.document_chunks.length > 0) {
          await this.saveAiAnswerResults(
            projectId,
            JSON.stringify(parsed),
            validOptionIds,
            workspaceId
          );
          savedResults = true;
          break; // Only save once
        }
      }

      this.logger.log(
        `Successfully parsed response with ${parsed.answers.length} answers${savedResults ? ' (saved to database)' : ''}`
      );

      // Return the parsed JSON directly as the answer, since it matches the required schema
      return {
        answer: parsed,
        evidence_sources: [], // Not used in the new schema format
        confidence_level: 'high', // Since we got a structured response
        conversationHistory,
      };
    } catch (error) {
      this.logger.error('Failed to parse agent response as JSON:', error);
      this.logger.error('Original content length:', content.length);
      this.logger.error('Content preview:', content.substring(0, 500));

      // Fallback to structured response if not JSON
      return {
        answer: {
          answers: [],
        },
        evidence_sources: [],
        confidence_level: 'low',
        conversationHistory,
      };
    }
  }

  /**
   * Fetch workspace ID for a given project
   */
  private async getWorkspaceIdFromProject(projectId: string): Promise<string> {
    const { data: project, error } = await this.supabase
      .from('project')
      .select('workspaceId')
      .eq('id', projectId)
      .single();

    if (error || !project) {
      throw new Error(
        `Failed to fetch workspace for project ${projectId}: ${error?.message}`
      );
    }

    return project.workspaceId;
  }

  /**
   * Fetch all required data from Supabase
   */
  private async fetchEcoVadisData(projectId: string, questionId: string) {
    // 1. Fetch project question details
    const { data: projectQuestionRaw, error: projectQuestionError } =
      await this.supabase
        .from('project_ecovadis_question')
        .select(
          `
        id,
        status,
        impact,
        ecovadis_question:questionId (
          id,
          questionCode,
          question,
          questionName,
          indicator,
          themeId,
          type
        )
      `
        )
        .eq('id', questionId)
        .single();

    if (projectQuestionError || !projectQuestionRaw) {
      throw new Error('Failed to fetch project question details');
    }

    // Transform the raw data to ensure proper typing
    const projectQuestion: ProjectQuestionData = {
      id: projectQuestionRaw.id,
      status: projectQuestionRaw.status,
      impact: projectQuestionRaw.impact,
      ecovadis_question: Array.isArray(projectQuestionRaw.ecovadis_question)
        ? projectQuestionRaw.ecovadis_question[0]
        : projectQuestionRaw.ecovadis_question,
    };

    // 2. Fetch theme details and sustainability issues
    const { data: theme, error: themeError } = await this.supabase
      .from('ecovadis_theme')
      .select(
        `
        id, 
        title, 
        description,
        project_ecovadis_theme!inner (
          id,
          impact,
          issues
        )
      `
      )
      .eq('id', projectQuestion.ecovadis_question.themeId)
      .eq('project_ecovadis_theme.projectId', projectId)
      .single();

    // 3. Fetch question options with their answer data
    const { data: options, error: optionsError } = await this.supabase
      .from('ecovadis_answer_option')
      .select(
        `
        id, 
        issueTitle, 
        instructions,
        project_ecovadis_answer!inner (
          id,
          response
        )
      `
      )
      .eq('questionId', projectQuestion.ecovadis_question.id)
      .eq('project_ecovadis_answer.projectId', projectId);

    // 4. Get scoring framework
    const scoringFrameworkKey = this.formatScoringFrameworkKey({
      theme: theme ? theme.title : '',
      indicator: projectQuestion.ecovadis_question.indicator,
    });

    const scoringFrameworkKeyDriver = scoringFrameworkKey + ':scoring-drivers';

    const [
      { data: scoringFrameworkData },
      { data: scoringFrameworkDriverData },
    ] = await Promise.all([
      this.supabase
        .from('kv_store')
        .select(`value`)
        .eq('key', scoringFrameworkKey)
        .single(),
      this.supabase
        .from('kv_store')
        .select(`value`)
        .eq('key', scoringFrameworkKeyDriver)
        .single(),
    ]);

    // 5. Get sustainability issues details
    let sustainabilityIssues = '';
    if (theme?.project_ecovadis_theme?.length > 0) {
      const themeSustainabilityIssue = theme.project_ecovadis_theme
        .map((itheme) => itheme.issues)
        .flat();

      if (themeSustainabilityIssue.length > 0) {
        const { data: sustainabilityIssuesData } = await this.supabase
          .from('ecovadis_sustainability_issues')
          .select('id, issue, definition, industryIssues')
          .in(
            'id',
            themeSustainabilityIssue.map((issue) => issue.issueId)
          );

        theme.project_ecovadis_theme.forEach((itheme) => {
          itheme.issues.forEach((issue) => {
            const issueData = sustainabilityIssuesData?.find(
              (data) => data.id === issue.issueId
            );
            if (issueData) {
              sustainabilityIssues += `
Issue: ${issueData.issue}
Impact: ${themeSustainabilityIssue.find((i) => i.issueId === issueData.id)?.impact || 'No impact provided'}
Definition: ${issueData.definition}
Industry Issues: ${issueData.industryIssues}
\n`;
            }
          });
        });
      }
    }

    return {
      projectQuestion: projectQuestion,
      theme: theme as ThemeData,
      options: options as OptionData[],
      scoringFramework: scoringFrameworkData?.value,
      scoringFrameworkDrivers: scoringFrameworkDriverData?.value,
      sustainabilityIssues,
    };
  }

  /**
   * Format the fetched data for AI prompt
   */
  private formatDataForPrompt(
    projectQuestion: ProjectQuestionData,
    theme: ThemeData,
    options: OptionData[],
    scoringFramework: any,
    scoringFrameworkDrivers: any,
    sustainabilityIssues: string
  ) {
    // Format questionnaire answers
    let questionnaireAnswers = '';
    questionnaireAnswers += `Question: ${projectQuestion.ecovadis_question.questionName}: ${projectQuestion.ecovadis_question.question}\n\n`;

    if (options) {
      options.forEach((option, index) => {
        questionnaireAnswers += `ANSWER ${index + 1}:\n\n`;
        questionnaireAnswers += `AnswerId: ${option.id}\n\n`;
        questionnaireAnswers += `Answer: ${option.issueTitle}\n\n`;
        questionnaireAnswers += `Support: ${option.instructions || 'No instructions provided'}\n\n`;
      });
    }

    // Prepare theme criteria
    const themeCriteria = theme
      ? `Theme: ${theme.title}\n\n${theme.description}`
      : 'Theme information not available';

    // Prepare indicator criteria
    const indicatorCriteria =
      `Indicator: ${projectQuestion.ecovadis_question.indicator}\n\n` +
      `This indicator is about your company's actions to support your sustainability policies and commitments.\n\n` +
      `The answer options in each question represent best practices for your company's size and industry. Select options that your company has already implemented and provide the documented proof of your actions.`;

    const formattedScoringFramework =
      typeof scoringFramework === 'object' && scoringFramework !== null
        ? JSON.stringify(scoringFramework)
        : scoringFramework ||
        'Scoring framework not available for this indicator';

    return {
      question: projectQuestion.ecovadis_question.question,
      themeCriteria,
      indicatorCriteria,
      answerOptions: questionnaireAnswers.trim(),
      formattedScoringFramework,
    };
  }

  /**
   * Parse AI response and save to database
   */
  private async saveAiAnswerResults(
    projectId: string,
    aiResponse: string,
    validOptionIds: string[],
    workspaceId: string
  ): Promise<any[]> {
    try {
      const parsed = JSON.parse(aiResponse);
      const attachments = await this.parseAiAnswerResponse(parsed, workspaceId);

      if (attachments.length > 0) {
        return await this.saveAiAnswerAttachments(
          projectId,
          attachments,
          validOptionIds
        );
      }

      return [];
    } catch (error) {
      this.logger.error('Error saving AI answer results:', error);
      return [];
    }
  }

  /**
   * Parse AI answer response into attachments format
   */
  private async parseAiAnswerResponse(
    response: EcoVadisFinalResponse,
    workspaceId: string
  ): Promise<ParsedAttachment[]> {
    this.logger.log('Parsing AI answer response');

    if (!response?.answers || !Array.isArray(response.answers)) {
      this.logger.error('Invalid AI response format - missing answers array');
      return [];
    }

    const attachments: ParsedAttachment[] = [];

    for (const item of response.answers) {
      if (
        !item?.answer_option?.id ||
        !item?.document_chunks ||
        !Array.isArray(item.document_chunks)
      ) {
        this.logger.warn('Skipping invalid AI answer item:', item);
        continue;
      }

      // Collect all chunk IDs for this answer option
      const chunkIds = item.document_chunks
        .map((chunk) => chunk.document_chunk_id)
        .filter((id) => id);

      if (chunkIds.length === 0) {
        this.logger.warn(
          'No valid chunk IDs found for answer option:',
          item.answer_option.id
        );
        continue;
      }

      // Query document_chunk table to get actual documentIds and page numbers
      // CRITICAL: Include workspace filtering to prevent cross-workspace document linking
      const { data: documentChunks, error } = await this.supabase
        .from('document_chunk')
        .select(`
          id, 
          documentId, 
          page,
          document:documentId (
            workspaceId
          )
        `)
        .in('id', chunkIds);

      if (error) {
        this.logger.error('Error fetching document chunks:', error);
        continue;
      }

      if (!documentChunks || documentChunks.length === 0) {
        this.logger.warn('No document chunks found for chunk IDs:', chunkIds);
        continue;
      }

      // CRITICAL SECURITY: Filter out document chunks from other workspaces
      const validDocumentChunks = documentChunks.filter((chunk) => {
        const document = Array.isArray(chunk.document) ? chunk.document[0] : chunk.document;
        const chunkWorkspaceId = document?.workspaceId;
        
        if (chunkWorkspaceId !== workspaceId) {
          this.logger.error(
            `Security violation: Chunk ${chunk.id} belongs to workspace ${chunkWorkspaceId}, but expected ${workspaceId}`
          );
          return false;
        }
        return true;
      });

      if (validDocumentChunks.length === 0) {
        this.logger.warn('No valid document chunks found after workspace filtering for chunk IDs:', chunkIds);
        continue;
      }

      if (validDocumentChunks.length < documentChunks.length) {
        this.logger.warn(
          `Filtered out ${documentChunks.length - validDocumentChunks.length} chunks from other workspaces`
        );
      }

      // Group document chunks by actual documentId, aggregating per-link comments
      const documentGroups: Record<string, ParsedAttachmentLink[]> = {};

      // Group chunks by document for page validation
      const documentChunkGroups: Record<string, { chunkId: string; page: number; comment: string }[]> = {};

      for (const chunk of item.document_chunks) {
        const chunkId = chunk.document_chunk_id;
        if (!chunkId) continue;

        // Find the actual documentId and page from the database query (use validated chunks only)
        const documentChunk = validDocumentChunks.find((dc) => dc.id === chunkId);
        if (!documentChunk) {
          this.logger.warn('Document chunk not found in database:', chunkId);
          continue;
        }

        const documentId = documentChunk.documentId as string;
        const pageNumber = parseInt(documentChunk.page?.toString() || '1', 10);

        if (!documentChunkGroups[documentId]) {documentChunkGroups[documentId] = [];}
        documentChunkGroups[documentId].push({
          chunkId,
          page: pageNumber,
          comment: (chunk as any).comment || '',
        });
      }

      // Validate page numbers for each document and create links
      for (const [documentId, chunks] of Object.entries(documentChunkGroups)) {
        // Extract page numbers
        const originalPageNumbers = chunks.map((chunk) => chunk.page);

        // Add buffer of 2 pages before and after the page range
        const pageNumbersWithBuffer = addPageRangeBuffer(originalPageNumbers);

        // Validate extended page numbers against document's actual page count
        const validPageNumbers = await validatePageNumbersAgainstDocument(
          this.supabase,
          documentId,
          pageNumbersWithBuffer
        );

        // Query for all document chunks within the extended page range
        const pageStrings = validPageNumbers.map((page) => page.toString());
        const { data: extendedDocumentChunks, error: extendedChunksError } =
          await this.supabase
            .from('document_chunk')
            .select('id, page')
            .eq('documentId', documentId)
            .in('page', pageStrings)
            .order('page', { ascending: true });

        if (extendedChunksError) {
          this.logger.error(
            'Error fetching extended document chunks:',
            extendedChunksError
          );
          continue;
        }

        if (!extendedDocumentChunks || extendedDocumentChunks.length === 0) {
          this.logger.log(
            `[AI Auto Answer] No document chunks found for extended pages in document ${documentId}`
          );
          continue;
        }

        // Create links for all chunks in the extended range
        for (const extendedChunk of extendedDocumentChunks) {
          // Find the original chunk comment if it exists
          const originalChunk = chunks.find(
            (c) => c.chunkId === extendedChunk.id
          );
          const comment = originalChunk?.comment || '';

          const link: ParsedAttachmentLink = {
            chunkId: extendedChunk.id,
            page: extendedChunk.page.toString(),
            comment: comment,
          };

          if (!documentGroups[documentId]) {
            documentGroups[documentId] = [];
          }
          documentGroups[documentId].push(link);
        }
      }

      // Create an attachment for each document with its per-link comments
      for (const [documentId, links] of Object.entries(documentGroups)) {
        // Deduplicate links by chunkId while preserving first comment
        const seen = new Set<string>();
        const dedupedLinks = links.filter((l) => {
          if (seen.has(l.chunkId)) return false;
          seen.add(l.chunkId);
          return true;
        });

        if (dedupedLinks.length === 0) continue;

        attachments.push({
          optionId: item.answer_option.id,
          documentId,
          links: dedupedLinks,
        });
      }
    }

    this.logger.log(`Parsed attachments: ${attachments.length}`);
    return attachments;
  }

  /**
   * Save AI answer attachments to database
   */
  private async saveAiAnswerAttachments(
    projectId: string,
    attachments: ParsedAttachment[],
    validOptionIds: string[]
  ): Promise<any[]> {
    const results: any[] = [];

    for (const attachment of attachments) {
      const { optionId, documentId, links } = attachment;

      try {
        // Validate that the optionId belongs to the current project's question
        if (!validOptionIds.includes(optionId)) {
          results.push({
            optionId,
            documentId,
            status: 'error',
            message:
              'Option ID does not belong to the current project question',
          });
          continue;
        }

        // Check if answer already exists for this option
        const { data: existingAnswer, error: answerFetchError } =
          await this.supabase
            .from('project_ecovadis_answer')
            .select('id')
            .eq('projectId', projectId)
            .eq('optionId', optionId)
            .maybeSingle();

        if (answerFetchError) {
          throw new Error(
            `Error fetching existing answer: ${answerFetchError.message}`
          );
        }

        let answerId;

        if (existingAnswer) {
          answerId = existingAnswer.id;
        } else {
          // Create new answer with response="true"
          const { data: newAnswer, error: createAnswerError } =
            await this.supabase
              .from('project_ecovadis_answer')
              .insert({
                projectId,
                optionId,
                response: 'true',
              })
              .select('id')
              .single();

          if (createAnswerError) {
            throw new Error(
              `Error creating new answer: ${createAnswerError.message}`
            );
          }

          answerId = newAnswer.id;
        }

        // Check for existing links to avoid duplicates
        const { data: existingLinks, error: existingLinksError } =
          await this.supabase
            .from('project_ecovadis_linked_document_chunks')
            .select('documentChunkId')
            .eq('answerId', answerId);

        if (existingLinksError) {
          throw new Error(
            `Error checking existing links: ${existingLinksError.message}`
          );
        }

        // Filter out document chunks that are already linked
        const existingChunkIds =
          existingLinks?.map((link) => link.documentChunkId) || [];
        const linksToInsert = links.filter(
          (l) => !existingChunkIds.includes(l.chunkId)
        );

        if (linksToInsert.length > 0) {
          // Create linked document chunks with per-link comments
          const linkedChunksData = linksToInsert.map((link) => ({
            answerId,
            documentChunkId: link.chunkId,
            comment: link.comment || null,
            attachment_source: 'ai',
          }));

          const { data: linkedChunks, error: linkError } = await this.supabase
            .from('project_ecovadis_linked_document_chunks')
            .insert(linkedChunksData)
            .select();

          if (linkError) {
            throw new Error(
              `Error linking document chunks: ${linkError.message}`
            );
          }

          results.push({
            optionId,
            documentId,
            status: 'success',
            answerId,
            linkedChunksCount: linkedChunks.length,
            newLinksCreated: true,
          });
        } else {
          results.push({
            optionId,
            documentId,
            status: 'success',
            answerId,
            linkedChunksCount: 0,
            newLinksCreated: false,
            message: 'All document chunks already linked to this answer',
          });
        }
      } catch (error) {
        results.push({
          optionId,
          documentId,
          status: 'error',
          message: error.message,
        });
      }
    }

    return results;
  }

  /**
   * Format scoring framework key (helper method)
   */
  private formatScoringFrameworkKey({
    theme,
    indicator,
  }: {
    theme: string;
    indicator: string;
  }): string {
    // Convert theme and indicator to lowercase and replace spaces/special chars with dashes
    const formattedTheme = theme
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9\-]/g, '');
    const formattedIndicator = indicator
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9\-]/g, '');

    return `${formattedTheme}:${formattedIndicator}`;
  }
}
