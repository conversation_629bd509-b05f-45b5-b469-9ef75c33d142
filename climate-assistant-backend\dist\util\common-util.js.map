{"version": 3, "file": "common-util.js", "sourceRoot": "", "sources": ["../../src/util/common-util.ts"], "names": [], "mappings": ";;AAAA,kDAKC;AAED,gCAMC;AAID,wEAEC;AAED,oEAEC;AAID,0CAgBC;AAQD,gDAwBC;AAUD,gFA+BC;AApHD,SAAgB,mBAAmB,CAAC,UAAkB;IACpD,IAAI,CAAC,UAAU;QAAE,OAAO,KAAK,CAAC;IAE9B,MAAM,WAAW,GAAW,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;IACtE,OAAO,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;AAChC,CAAC;AAED,SAAgB,UAAU,CAAI,KAAU,EAAE,IAAY;IACpD,MAAM,MAAM,GAAU,EAAE,CAAC;IACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;QAC5C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IACxC,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAID,SAAgB,8BAA8B,CAAC,SAAiB;IAC9D,OAAO,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC3C,CAAC;AAED,SAAgB,4BAA4B,CAAC,SAAiB;IAC5D,OAAO,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC;AAID,SAAgB,eAAe,CAAC,UAAkB;IAChD,MAAM,KAAK,GAAG,IAAI,GAAG,EAAU,CAAC;IAEhC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;QACtC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEzD,IAAI,GAAG,EAAE,CAAC;YACR,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;gBAClC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACf,CAAC;QACH,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACnB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACjD,CAAC;AAQD,SAAgB,kBAAkB,CAChC,KAAe,EACf,OAAgB;IAEhB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACvB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;IACnC,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;IAG1C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;IAC7C,MAAM,SAAS,GAAG,OAAO;QACvB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,cAAc,GAAG,CAAC,CAAC;QACvC,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC;IAGvB,MAAM,aAAa,GAAa,EAAE,CAAC;IACnC,KAAK,IAAI,CAAC,GAAG,WAAW,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;QAC9C,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;IAED,OAAO,aAAa,CAAC;AACvB,CAAC;AAUM,KAAK,UAAU,kCAAkC,CACtD,QAAa,EACb,UAAkB,EAClB,WAAqB;IAErB,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC7B,OAAO,WAAW,CAAC;IACrB,CAAC;IAGD,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;SACzC,IAAI,CAAC,gBAAgB,CAAC;SACtB,MAAM,CAAC,MAAM,CAAC;SACd,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;SAC5B,KAAK,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;SACnC,KAAK,CAAC,CAAC,CAAC,CAAC;IAEZ,IAAI,WAAW,EAAE,CAAC;QAChB,MAAM,iBAAiB,GAAG,QAAQ,CAChC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,WAAW,CAAC,IAAI,CACtD,CAAC;QAGF,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,CACzC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,IAAI,iBAAiB,CACpC,CAAC;QAEF,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC"}