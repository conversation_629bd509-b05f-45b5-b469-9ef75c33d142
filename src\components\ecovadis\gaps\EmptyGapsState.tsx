
import React from 'react';
import { FileSearch } from 'lucide-react';

interface EmptyGapsStateProps {
  title?: string;
  description?: string;
}

export const EmptyGapsState = ({ 
  title = "No matching gaps found", 
  description = "Try adjusting your filters or keyword search."
}: EmptyGapsStateProps) => {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
      <div className="w-16 h-16 mb-4 rounded-full bg-gray-100 flex items-center justify-center">
        <FileSearch className="h-8 w-8 text-gray-400" />
      </div>
      
      <h3 className="text-xl font-semibold text-glacier-darkBlue mb-2">
        {title}
      </h3>
      
      <p className="text-gray-600 max-w-md">
        {description}
      </p>
    </div>
  );
};
