import { FunctionComponent, ReactNode, useEffect, useState } from 'react';
import Markdown from 'markdown-to-jsx';
import { CopyIcon, ThumbsDown, ThumbsUp } from 'lucide-react';
import { useScrollToBottom } from 'react-scroll-to-bottom';

import { ChatMessage } from '@/models/chat.models.ts';
import { Button } from '@/components/ui/button.tsx';
import { useToast } from '@/components/ui/use-toast.ts';
import {
  MixpanelEvents,
  MixpanelService,
} from '@/services/mixpanel.service.ts';

const ChatMessageAssistantWrapper: FunctionComponent<{
  children: ReactNode;
}> = ({ children }) => {
  return (
    <div className="flex flex-row gap-[30px] mx-24 my-[36px]">
      <div className="flex-col min-w-[40px] w-[40px]">
        <img src="/glacier-chat-avatar.png" className="w-[40px] h-[40px]"></img>
      </div>
      <div className={`flex flex-col`}>{children}</div>
    </div>
  );
};

const ChatMessageAssistant: FunctionComponent<{
  text: string;
  isRendering?: boolean;
  sendMessage: (input: string) => void;
  isPending: boolean;
}> = ({ text, isRendering, sendMessage, isPending }) => {
  const { toast } = useToast();
  const scrollToBottom = useScrollToBottom();

  const copyToClipboard = async (text: string) => {
    MixpanelService.track(MixpanelEvents.textCopied());
    await navigator.clipboard.writeText(text);
    toast({
      variant: 'success',
      description: 'Text wurde kopiert',
    });
  };

  const likeClicked = async () => {
    await navigator.clipboard.writeText(text);
    MixpanelService.track(MixpanelEvents.textLiked(text));
    toast({
      variant: 'success',
      description: 'Antwort wurde als gut markiert!',
    });
  };
  const dislikeClicked = async () => {
    MixpanelService.track(MixpanelEvents.textDisliked(text));
    await navigator.clipboard.writeText(text);
    toast({
      variant: 'warning',
      description: 'Antwort wurde als schlecht markiert!',
    });
  };

  const markdownClicked = (eventTarget: EventTarget) => {
    if (isRendering || isPending) {
      return toast({
        variant: 'warning',
        description: 'Antwort wird noch geladen...',
      });
    }
    const target = eventTarget as HTMLElement;
    if (target.classList.contains('markdown-detail-button')) {
      const initiativeNumber = target.dataset['initiative'];
      sendMessage(
        `[T] Erkläre mir die Initiative [${initiativeNumber}] genauer!`
      );
    } else if (target.classList.contains('markdown-esrs-selector')) {
      target.style.backgroundColor = '#143560';
      target.style.color = 'white';

      const esrsButtons = document.querySelectorAll('.markdown-esrs-selector');
      esrsButtons.forEach((button) => {
        if (button !== target) {
          button.remove();
        }
      });
      const esrs = target.dataset['esrs'];
      const esrsDr = target.dataset['dr'];
      sendMessage(
        `!!{"key": "CSRD Reporting - ${esrsDr}","esrs": "${esrs}","dr": "${esrsDr}","type": "generate"}`
      );
    } else if (target.classList.contains('markdown-gap-evaluation')) {
      const esrsDr = target.dataset['dr'];
      sendMessage(
        `!!{"key": "CSRD Gap Evaluation - ${esrsDr}","dr": "${esrsDr}","type": "evaluate"}`
      );
    }

    scrollToBottom({
      behavior: 'smooth',
    });
  };

  return (
    <ChatMessageAssistantWrapper>
      <div
        className="pt-1 markdown-response"
        onClick={(event) => markdownClicked(event.target)}
      >
        <Markdown>{text}</Markdown>
      </div>
      {!isRendering && (
        <div className={`flex flex-row mt-2 gap-1`}>
          <Button
            variant="button"
            size="icon"
            onClick={() => copyToClipboard(text)}
          >
            <CopyIcon className={`h-5 w-5`}></CopyIcon>
          </Button>
          <Button variant="button" size="icon" onClick={() => likeClicked()}>
            <ThumbsUp className={`h-5 w-5`}></ThumbsUp>
          </Button>
          <Button variant="button" size="icon" onClick={() => dislikeClicked()}>
            <ThumbsDown className={`h-5 w-5`}></ThumbsDown>
          </Button>
        </div>
      )}
    </ChatMessageAssistantWrapper>
  );
};

const ChatMessageUser: FunctionComponent<{ text: string }> = ({ text }) => {
  return (
    <div className="flex my-[36px] flex-row justify-end">
      <div className="flex py-[12px] px-[20px] bg-gray-100 rounded-[10px] max-w-[50%] mx-24 ">
        <div className="pt-1" style={{ fontFamily: 'Pantea-Text' }}>
          {text}
        </div>
      </div>
    </div>
  );
};

const ChatMessageElement: FunctionComponent<{
  message: ChatMessage;
  isRendering?: boolean;
  isPending: boolean;
  sendMessage: (input: string) => void;
}> = ({ message, isRendering, sendMessage, isPending }) => {
  const { role, content } = message;

  if (role === 'system') {
    return undefined;
  }

  if (role === 'assistant') {
    return (
      <ChatMessageAssistant
        sendMessage={sendMessage}
        text={content}
        isRendering={isRendering}
        isPending={isPending}
      ></ChatMessageAssistant>
    );
  }

  return <ChatMessageUser text={content}></ChatMessageUser>;
};

export const ChatMessageLoading: FunctionComponent = () => {
  const [isTextVisible, setIsTextVisible] = useState(false);

  useEffect(() => {
    setTimeout(() => setIsTextVisible(true), 250);
  }, []);

  return (
    <ChatMessageAssistantWrapper>
      {isTextVisible && (
        <div className="shine pt-1">
          <div className="shine-text">
            Datenbanken werden durchsucht... (kann bis zu 60s dauern)
          </div>
          <div className="shine-helper"></div>
        </div>
      )}
    </ChatMessageAssistantWrapper>
  );
};

export { ChatMessageElement };
