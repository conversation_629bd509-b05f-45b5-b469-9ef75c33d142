export function isTextPresentInHTML(htmlString: string): boolean {
  if (!htmlString) return false;
  // Remove HTML tags using a regular expression
  const textContent: string = htmlString.replace(/<[^>]*>/g, '').trim();
  return textContent.length > 0;
}

export function chunkArray<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

//At times, we need to check if the request is for data generation or not.
// This function helps in identifying that. In such case, we append '-generation' to the request id.
export function isRequestForDataGenerationType(requestId: string) {
  return requestId.endsWith('-generation');
}

export function getGenerationIdFromRequestId(requestId: string) {
  return requestId.split('-generation')[0];
}

//Input is a string like "1-3,5,7-9"
// and output is an array of numbers [1, 2, 3, 5, 7, 8, 9]
export function parsePageRanges(pageRanges: string): number[] {
  const pages = new Set<number>();

  pageRanges.split(',').forEach((range) => {
    const [start, end] = range.trim().split('-').map(Number);

    if (end) {
      for (let i = start; i <= end; i++) {
        pages.add(i);
      }
    } else {
      pages.add(start);
    }
  });

  return Array.from(pages).sort((a, b) => a - b);
}

/**
 * Adds a buffer of 2 pages before and after the given page range
 * @param pages - Array of page numbers
 * @param maxPage - Maximum page number in the document (optional, for edge case handling)
 * @returns Array of page numbers with buffer added
 */
export function addPageRangeBuffer(
  pages: number[],
  maxPage?: number
): number[] {
  if (pages.length === 0) {
    return pages;
  }

  const minPage = Math.min(...pages);
  const maxPageInRange = Math.max(...pages);

  // Calculate buffer range
  const bufferStart = Math.max(1, minPage - 2); // Doesn't go below page 1
  const bufferEnd = maxPage
    ? Math.min(maxPage, maxPageInRange + 2)
    : maxPageInRange + 2;

  // Create new array with buffer pages
  const bufferedPages: number[] = [];
  for (let i = bufferStart; i <= bufferEnd; i++) {
    bufferedPages.push(i);
  }

  return bufferedPages;
}

/**
 * Validates page numbers against a document's actual page count
 * @param supabase - Supabase client instance
 * @param documentId - The ID of the document to validate against
 * @param pageNumbers - Array of page numbers to validate
 * @param documentName - Name of the document for logging purposes
 * @returns Promise<number[]> - Array of valid page numbers that exist within the document's page range
 */
export async function validatePageNumbersAgainstDocument(
  supabase: any,
  documentId: string,
  pageNumbers: number[]
): Promise<number[]> {
  if (pageNumbers.length === 0) {
    return pageNumbers;
  }

  // Get the maximum page number from the document to validate our extended range
  const { data: maxPageData } = await supabase
    .from('document_chunk')
    .select('page')
    .eq('documentId', documentId)
    .order('page', { ascending: false })
    .limit(1);

  if (maxPageData) {
    const maxPageInDocument = parseInt(
      maxPageData.page.split('-').pop() || maxPageData.page
    );

    // Filter out pages that exceed the document's actual page count
    const validPageNumbers = pageNumbers.filter(
      (page) => page <= maxPageInDocument
    );

    return validPageNumbers;
  }
  // Fallback if we can't determine max page - return original pages
  return pageNumbers;
}
