import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateEcovadisQuestion, updateEcovadisGap, updateEcovadisQuestionOption } from '@/api/ecovadis/ecovadis.api';
import { EcovadisQuestion, QuestionOption } from '@/types/ecovadis';
import { toast } from 'sonner';
import { fireConfetti } from '@/lib/confetti';

export const useEcovadisQuestionMutations = (projectId: string | undefined) => {
  const queryClient = useQueryClient();

  // Mutation for updating a question
  const updateQuestion = useMutation({
    mutationFn: async (updatedQuestion: {
      id: string;
      status?: string;
      impact?: string;
      estimatedScore?: number;
    }) => {
      const updatePayload = { 
        status: updatedQuestion.status,
        impact: updatedQuestion.impact,
        estimatedScore: updatedQuestion.estimatedScore || 0 
      };
      
      await updateEcovadisQuestion(updatedQuestion.id, updatePayload);
      return updatedQuestion;
    },
    onMutate: async (data) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['ecovadisProject', projectId] });
      
      // Snapshot the previous value
      const previousData = queryClient.getQueryData(['ecovadisProject', projectId]);
      
      // Optimistically update to the new value
      queryClient.setQueryData(['ecovadisProject', projectId], (oldData: any) => {
        if (!oldData) return oldData;
        
        return {
          ...oldData,
          questions: oldData.questions.map((q: any) => 
            q.id === data.id ? { ...q, status: data.status, impact: data.impact, estimatedScore: data.estimatedScore } : q
          ),
        };
      });
      
      return { previousData };
    },
    onSuccess: (data) => {
      // Manual invalidation after successful update
      queryClient.invalidateQueries({ queryKey: ['ecovadisProject', projectId] });
      
      if (data.status?.toLowerCase() === 'complete') {
        toast.success("Question marked as complete! 🌱");
      } else if (data.status) {
        toast.info("Question marked as incomplete");
      }
    },
    onError: (error, _, context) => {
      console.error('Error updating question:', error);
      // Rollback to the previous value
      if (context?.previousData) {
        queryClient.setQueryData(['ecovadisProject', projectId], context.previousData);
      }
      toast.error("Failed to update question. Please try again.");
    }
  });

  // Mutation for marking a gap as complete/incomplete
  const markGapComplete = useMutation({
    mutationFn: async ({ gapId, isComplete }: { gapId: string; isComplete: boolean }) => {
      await updateEcovadisGap(gapId, isComplete);
      return { gapId, isComplete };
    },
    onMutate: async (data) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['ecovadisProject', projectId] });
      
      // Snapshot the previous value
      const previousData = queryClient.getQueryData(['ecovadisProject', projectId]);
      
      // Optimistically update gaps in the project data
      queryClient.setQueryData(['ecovadisProject', projectId], (oldData: any) => {
        if (!oldData) return oldData;
        
        return {
          ...oldData,
          questions: oldData.questions.map((q: any) => ({
            ...q,
            gaps: Array.isArray(q.gaps) 
              ? q.gaps.map((gap: any) => 
                  gap.id === data.gapId 
                    ? { 
                        ...gap, 
                        resolved: data.isComplete, 
                        isComplete: data.isComplete 
                      } 
                    : gap
                )
              : q.gaps
          }))
        };
      });
      
      return { previousData };
    },
    onSuccess: (data) => {
      // Invalidate and refetch the project data
      queryClient.invalidateQueries({ queryKey: ['ecovadisProject', projectId] });
      
      toast.success(data.isComplete ? "Gap marked as complete" : "Gap marked as incomplete");
    },
    onError: (error, _, context) => {
      console.error('Error updating gap:', error);
      // Rollback to the previous state
      if (context?.previousData) {
        queryClient.setQueryData(['ecovadisProject', projectId], context.previousData);
      }
      toast.error("Failed to update gap status. Please try again.");
    }
  });

  // Mutation for toggling an option with optimistic updates
  const toggleOption = useMutation({
    mutationFn: async ({ optionId, selected }: { optionId: string; selected: boolean }) => {
      await updateEcovadisQuestionOption(optionId, projectId, { selected });
      return { optionId, projectId, selected };
    },
    onMutate: async (data) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['ecovadisProject', projectId] });
      
      // Snapshot the previous value
      const previousData = queryClient.getQueryData(['ecovadisProject', projectId]);
      
      // Optimistically update the option's selected state
      queryClient.setQueryData(['ecovadisProject', projectId], (oldData: any) => {
        if (!oldData) return oldData;
        
        return {
          ...oldData,
          questions: oldData.questions.map((q: any) => ({
            ...q,
            options: (q.options || []).map((option: QuestionOption) => 
              option.id === data.optionId 
                ? { 
                    ...option, 
                    selected: data.selected,
                    response: data.selected ? (option.response || "true") : null
                  } 
                : option
            )
          }))
        };
      });
      
      return { previousData };
    },
    onSuccess: (data) => {
      // Only invalidate after the mutation succeeds to avoid unnecessary refetches
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['ecovadisProject', projectId] });
      }, 300); // Small delay to ensure UI feels responsive
      
      if (data.selected) {
        toast.success("Option selected");
      } else {
        toast.success("Option deselected");
      }
    },
    onError: (error, _, context) => {
      console.error('Error toggling option:', error);
      // Rollback to the previous state
      if (context?.previousData) {
        queryClient.setQueryData(['ecovadisProject', projectId], context.previousData);
      }
      toast.error("Failed to update option. Please try again.");
    }
  });

  // Mutation for updating text responses with optimistic updates
  const updateTextResponse = useMutation({
    mutationFn: async ({ optionId, response }: { optionId: string; response: string }) => {
      await updateEcovadisQuestionOption(optionId, projectId, { 
        evidence_examples: response,
        selected: !!response
      });
      return { optionId, response };
    },
    onMutate: async (data) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['ecovadisProject', projectId] });
      
      // Snapshot the previous value
      const previousData = queryClient.getQueryData(['ecovadisProject', projectId]);
      
      // Optimistically update the option's response
      queryClient.setQueryData(['ecovadisProject', projectId], (oldData: any) => {
        if (!oldData) return oldData;
        
        return {
          ...oldData,
          questions: oldData.questions.map((q: any) => ({
            ...q,
            options: (q.options || []).map((option: QuestionOption) => 
              option.id === data.optionId 
                ? { 
                    ...option, 
                    response: data.response,
                    selected: !!data.response
                  } 
                : option
            )
          }))
        };
      });
      
      return { previousData };
    },
    onSuccess: () => {
      // Only invalidate after successful update
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['ecovadisProject', projectId] });
      }, 300);
      
      toast.success("Response updated");
    },
    onError: (error, _, context) => {
      console.error('Error updating response:', error);
      // Rollback to the previous state
      if (context?.previousData) {
        queryClient.setQueryData(['ecovadisProject', projectId], context.previousData);
      }
      toast.error("Failed to update response. Please try again.");
    }
  });

  // Mutation for marking a question as complete/incomplete
  const markQuestionComplete = useMutation({
    mutationFn: async ({ questionId, isComplete }: { questionId: string; isComplete: boolean }) => {
      const status = isComplete ? 'complete' : 'pending';
      await updateEcovadisQuestion(questionId, { status });
      return { questionId, status };
    },
    onMutate: async (data) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['ecovadisProject', projectId] });
      
      // Snapshot the previous value
      const previousData = queryClient.getQueryData(['ecovadisProject', projectId]);
      
      // Optimistically update the question's status
      queryClient.setQueryData(['ecovadisProject', projectId], (oldData: any) => {
        if (!oldData) return oldData;
        
        return {
          ...oldData,
          questions: oldData.questions.map((q: any) => 
            q.id === data.questionId 
              ? { ...q, status: data.isComplete ? 'complete' : 'incomplete' } 
              : q
          ),
        };
      });
      
      return { previousData };
    },
    onSuccess: (data) => {      
      if (data.status === 'complete') {
        fireConfetti();
        toast.success("Question marked as complete! 🌱");
      } else {
        toast.info("Question marked as incomplete");
      }
    },
    onError: (error, _, context) => {
      console.error('Error marking question complete/incomplete:', error);
      // Rollback to the previous state
      if (context?.previousData) {
        queryClient.setQueryData(['ecovadisProject', projectId], context.previousData);
      }
      toast.error("Failed to update question status. Please try again.");
    }
  });

  return {
    updateQuestion,
    markGapComplete,
    toggleOption,
    updateTextResponse,
    markQuestionComplete
  };
};
