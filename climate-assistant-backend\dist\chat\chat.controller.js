"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatController = void 0;
const common_1 = require("@nestjs/common");
const chat_service_1 = require("./chat.service");
const chat_message_dto_1 = require("./entities/chat.message.dto");
const initiative_detail_service_1 = require("./initiative-detail.service");
const csrd_reporting_service_1 = require("./csrd-reporting.service");
const swagger_1 = require("@nestjs/swagger");
const supabase_auth_guard_1 = require("../auth/supabase/supabase.auth.guard");
let ChatController = class ChatController {
    constructor(chatService, initiativeDetailService, csrdReportingService) {
        this.chatService = chatService;
        this.initiativeDetailService = initiativeDetailService;
        this.csrdReportingService = csrdReportingService;
    }
    getChats(req) {
        const userId = req.user.id;
        return this.chatService.getChats(userId);
    }
    async createEmptyHistory(req, payload) {
        const userId = req.user.id;
        return this.chatService.createEmptyHistory(userId, payload.type);
    }
    async sendMessage(req, res, payload) {
        setSSEHeaders(res);
        const userId = req.user.id;
        const { messages, historyId, internalProcessRequest } = payload;
        if (internalProcessRequest?.key) {
            const systemPrompt = this.csrdReportingService.createCsrdReportingPrompt(internalProcessRequest);
            messages.push(systemPrompt);
        }
        await this.chatService.addMessageToHistory(historyId, [...messages]);
        const content = await this.chatService.createMessageStream({
            messages,
            onMessage: (chunk) => {
                res.write(chunk);
            },
            userId,
        });
        await this.chatService.addMessageToHistory(historyId, [
            { role: 'assistant', content },
        ]);
        const history = await this.chatService.getChatHistory(historyId);
        if (history.title == null) {
            if (payload.internalProcessRequest?.key) {
                await this.chatService.storeTitle(historyId, payload.internalProcessRequest.key);
            }
            else {
                await this.chatService.generateHistoryTitle(historyId, messages[messages.length - 1]?.content ?? '', content);
            }
        }
        res.end();
    }
    async getVectorQuery(req) {
        const userId = req.user.id;
        if (req.body.database === 'company') {
            return await this.chatService.queryCompanyRelatedVectors({
                userId,
                query: req.body.query,
                threshold: req.body.threshold,
                count: req.body.count,
            });
        }
        else {
            return await this.chatService.queryInternalRelatedVectors({
                query: req.body.query,
                threshold: req.body.threshold,
                count: req.body.count,
            });
        }
    }
    getChat(id) {
        return this.chatService.getChatHistory(id);
    }
    deleteChat(id) {
        return this.chatService.deleteChatHistory(id);
    }
    updateHistoryTitle(id, updatedHistory) {
        return this.chatService.updateChatHistory(id, updatedHistory);
    }
};
exports.ChatController = ChatController;
__decorate([
    (0, common_1.Get)(''),
    (0, swagger_1.ApiOperation)({ summary: 'Get all chat histories for the user' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Chat histories retrieved successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ChatController.prototype, "getChats", null);
__decorate([
    (0, common_1.Post)('create-empty-history'),
    (0, swagger_1.ApiOperation)({ summary: 'Create an empty chat history' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Empty chat history created successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "createEmptyHistory", null);
__decorate([
    (0, common_1.Post)('send-message'),
    (0, swagger_1.ApiOperation)({ summary: 'Send a message in a chat history' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Message sent successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Res)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "sendMessage", null);
__decorate([
    (0, common_1.Post)('vector-query'),
    (0, swagger_1.ApiOperation)({ summary: 'Query vectors for chat' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Vector query executed successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getVectorQuery", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific chat history by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Chat history retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ChatController.prototype, "getChat", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a specific chat history by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Chat history deleted successfully',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ChatController.prototype, "deleteChat", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update the title of a chat history' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Chat history title updated successfully',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, chat_message_dto_1.HistoryUpdateDto]),
    __metadata("design:returntype", void 0)
], ChatController.prototype, "updateHistoryTitle", null);
exports.ChatController = ChatController = __decorate([
    (0, swagger_1.ApiTags)('chats'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.AuthGuard),
    (0, common_1.Controller)('chats'),
    __metadata("design:paramtypes", [chat_service_1.ChatService,
        initiative_detail_service_1.InitiativeDetailService,
        csrd_reporting_service_1.CsrdReportingService])
], ChatController);
function setSSEHeaders(res) {
    res.header('Content-Type', 'text/event/stream');
    res.header('Cache-control', 'no-cache');
    res.header('X-Accel-Buffering', 'no');
}
//# sourceMappingURL=chat.controller.js.map