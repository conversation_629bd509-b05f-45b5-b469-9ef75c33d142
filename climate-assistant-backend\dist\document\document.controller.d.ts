import { DocumentService } from './document.service';
import { DatapointDocumentChunkService } from 'src/datapoint-document-chunk/datapoint-document-chunk.service';
import { DataSource } from 'typeorm';
import { DocumentDatapointLinkingDto } from './entities/document-datapoint-linking.dto';
import { BulkReindexDto, BulkReindexResultDto } from './entities/bulk-reindex.dto';
import { Response } from 'express';
import { EcovadisIssueParserService } from './ecovadis-issue-parser.service';
export declare class DocumentController {
    private readonly documentService;
    private readonly datapointDocumentChunkService;
    private readonly ecovadisIssueParserService;
    private dataSource;
    constructor(documentService: DocumentService, datapointDocumentChunkService: DatapointDocumentChunkService, ecovadisIssueParserService: EcovadisIssueParserService, dataSource: DataSource);
    private readonly logger;
    getDocumentUploads(req: any): Promise<any[]>;
    saveDocument(req: any, file: Express.Multer.File): Promise<{
        message: string;
    }>;
    parseEcovadisIssues(req: any, file: Express.Multer.File): Promise<{
        message: string;
    }>;
    getDocument(id: string): Promise<{
        creator: {
            id: string;
            name: string;
        };
        datapointsCount: number;
        id: string;
        workspaceId: string;
        name: string;
        path: string;
        createdAt: Date;
        status: import("./entities/document.entity").DocumentStatus;
        workspace: import("../workspace/entities/workspace.entity").Workspace;
        chunks: import("./entities/document-chunk.entity").DocumentChunk[];
        documentType: string;
        esrsCategory: string[];
        year: number;
        month: number;
        day: number;
        remarks: string;
        createdBy: string;
    }>;
    updateDocument(req: any, id: string): Promise<{
        message: string;
    }>;
    downloadFile(req: any, id: string, res: Response): Promise<void>;
    extractChunks(req: any, id: string): Promise<{
        message: string;
    }>;
    indexChunks(req: any, id: string): Promise<{
        message: string;
    }>;
    reindexSingleDocument(req: any, documentId: string): Promise<{
        message: string;
        documentId: string;
    }>;
    reindexWorkspaceDocuments(req: any, workspaceId: string): Promise<BulkReindexResultDto>;
    bulkIndexChunks(req: any, payload: BulkReindexDto): Promise<BulkReindexResultDto>;
    linkDocumentToDatapoints(req: any, documentId: string, payload: DocumentDatapointLinkingDto): Promise<{}>;
    deleteDocument(req: any, id: string): Promise<{
        success: boolean;
    }>;
    returnAllDocumentLinks(): Promise<any>;
    getDocumentChunk(id: string): Promise<import("./entities/document-chunk.entity").DocumentChunk>;
    deleteDocumentChunk(req: any, id: string): Promise<void>;
    linkDatapointsToChunk(id: string, req: any, body: {
        datapointRequestId: string;
        linked: boolean;
    }[]): Promise<{
        message: string;
    }>;
}
