import { FunctionComponent, useCallback, useEffect, useState } from 'react';
import TextareaAutosize from 'react-textarea-autosize';
import { LoaderCircle, TrashIcon } from 'lucide-react';

import { MainLayout } from '@/components/MainLayout';
import { Button } from '@/components/ui/button.tsx';
import {
  fetchGeneratedContext,
  fetchPromptContext,
  saveUserPromptContext,
} from '@/api/user-settings/user-settings.api.ts';
import { toast } from '@/components/ui/use-toast.ts';
import {
  deleteDocumentUpload,
  fetchDocumentUploads,
  uploadDocumentForPageReview,
} from '@/api/workspace-settings/workspace-settings.api';
import type { DocumentUpload } from '@/types/project';

export const UserSettings: FunctionComponent = () => {
  const [promptContext, setPromptContext] = useState('');
  const [filetoUpload, setFiletoUpload] = useState<File | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<DocumentUpload[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isContextGenerationLoading, setIsContextGenerationLoading] =
    useState(false);

  const handleFileChange = (e: any) => {
    setFiletoUpload(e.target.files[0]);
  };

  const handleUpload = async () => {
    const formData = new FormData();
    formData.append('file', filetoUpload as Blob);

    setIsLoading(true);
    try {
      // const response = await uploadFileForPromptContext(formData);
      toast({ variant: 'success', description: 'response.message' });
    } catch (e) {
      toast({ variant: 'destructive', description: 'Fehler beim Upload' });
    } finally {
      setIsLoading(false);
      setFiletoUpload(null);
      void loadUserFileUploads();
    }
  };

  const handleUploadReview = async () => {
    const formData = new FormData();
    formData.append('file', filetoUpload as Blob);

    setIsLoading(true);
    try {
      const response = await uploadDocumentForPageReview(formData);
      // download response.store as json file
      const element = document.createElement('a');
      const file = new Blob([JSON.stringify(response.store)], {
        type: 'application/json',
      });
      element.href = URL.createObjectURL(file);
      element.download = 'review.json';
      document.body.appendChild(element);

      element.click();
      document.body.removeChild(element);

      toast({ variant: 'success', description: response.message });
    } catch (e) {
      toast({ variant: 'destructive', description: 'Fehler beim Upload' });
    } finally {
      setIsLoading(false);
      setFiletoUpload(null);
      void loadUserFileUploads();
    }
  };

  const handleSavePromptContext = async () => {
    await saveUserPromptContext(promptContext);
    toast({
      variant: 'success',
      description: 'Einstellungen wurden gespeichert!',
    });
  };

  const loadUserPromptContext = useCallback(async () => {
    const promptContext = await fetchPromptContext();
    setPromptContext(promptContext);
  }, []);

  const loadUserFileUploads = useCallback(async () => {
    const fileUploads = await fetchDocumentUploads();
    setUploadedFiles(fileUploads);
  }, []);

  const loadGeneratedContext = useCallback(async () => {
    setIsContextGenerationLoading(true);
    const generatedContext = await fetchGeneratedContext();
    setIsContextGenerationLoading(false);
    setPromptContext(generatedContext);
    toast({
      variant: 'success',
      description: 'Context wurde generiert!',
    });
  }, []);

  const handleDeleteFile = async (id: string) => {
    await deleteDocumentUpload(id);
    toast({
      variant: 'success',
      description: 'File würde gelöscht',
    });
    void loadUserFileUploads();
  };

  useEffect(() => {
    void loadUserPromptContext();
    void loadUserFileUploads();
  }, []);

  return (
    <MainLayout>
      <div className={`flex flex-col flex-1 p-24 overflow-auto`}>
        <div className={`flex flex-row justify-between`}>
          <div
            className={`text-2xl mb-14 font-semibold`}
            style={{ fontSize: `22px` }}
          >
            Einstellungen & Personalisierung
          </div>
          <Button
            variant="darkBlue"
            style={{ backgroundColor: '#143560' }}
            onClick={() => handleSavePromptContext()}
            className={`rounded-full`}
          >
            Einstellungen speichern
          </Button>
        </div>

        <div className={`mb-8`}>
          <div className={`text-lg mb-2`}>Dokumente</div>
          <div>
            Geben Sie uns möglichst viele Dokumente (nur in PDF-Format), die Ihr
            Unternehmen und Ihre geplanten Initiativen beschreiben. Wir nutzen
            diese, um Ihnen die besten Empfehlungen zu geben und den Kontext für
            den Prompt zu generieren.
          </div>
          <div className={`mt-8`}>
            <input
              type="file"
              onChange={handleFileChange}
              className={`hidden absolute left 0`}
              id={'file-upload-input'}
            />

            {uploadedFiles.length === 0 && (
              <div className={`mb-8`}>
                Es wurden noch keine Dokumente hochgeladen
              </div>
            )}

            {uploadedFiles.length > 0 && (
              <div>
                <div className={`font-semibold mt-2 mb-3 text-sm`}>
                  Bereits hochgeladene Dokumente:
                </div>
                <div className={`mb-8`}>
                  <ul>
                    {uploadedFiles.map((file) => (
                      <li
                        className={`list-item list-disc ml-8 text-sm my-2 `}
                        key={file.name}
                      >
                        <div className={`inline-flex flex-row items-center`}>
                          <span>{file.name}</span>
                          <button
                            className={`inline-flex flex-row gap-2 items-center ml-8 text-destructive border-0 hover:text-red-700`}
                            onClick={() => handleDeleteFile(file.id)}
                          >
                            <TrashIcon className={`h-4 w-4`}></TrashIcon>
                            <span>Löschen</span>
                          </button>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {filetoUpload == null && (
              <Button
                variant="outline"
                onClick={() =>
                  document.getElementById('file-upload-input')?.click()
                }
                className={`mr-4 rounded-full border-black`}
              >
                Dokument hochladen
              </Button>
            )}

            {filetoUpload && (
              <div className={`mt-5 flex gap-2`}>
                <Button
                  variant="outline"
                  onClick={() =>
                    document.getElementById('file-upload-input')?.click()
                  }
                >
                  Ausgewähltes File ändern
                </Button>

                <Button
                  variant="darkBlue"
                  onClick={handleUpload}
                  disabled={isLoading}
                  style={{ backgroundColor: '#143560' }}
                >
                  {isLoading && (
                    <LoaderCircle className="h-4 w-4 animate-spin" />
                  )}
                  <span>"{filetoUpload.name}" hochladen</span>
                </Button>

                <Button
                  variant="darkBlue"
                  onClick={handleUploadReview}
                  disabled={isLoading}
                  style={{ backgroundColor: '#143560' }}
                >
                  {isLoading && (
                    <LoaderCircle className="h-4 w-4 animate-spin" />
                  )}
                  <span>"{filetoUpload.name}" Review</span>
                </Button>
              </div>
            )}
          </div>
        </div>

        <div>
          <div className={`text-lg mt-12`}>Prompt Kontext</div>
          <div className={``}>
            Hier kannst du deine Prompt-Einstellungen anpassen. Diese nutzen wir
            als Kontext für deine Ergebnisse. Je mehr wir über dich und dein
            Unternehmen wissen, desto besser können wir dir helfen. Keine Sorge
            – deine Daten sind sicher und werden nicht an Dritte weitergegeben.
          </div>
          <div className={`mt-8 mb-8`}>
            <Button
              variant="outline"
              className={`rounded-full border-black`}
              onClick={() => loadGeneratedContext()}
              disabled={isContextGenerationLoading}
            >
              {isContextGenerationLoading && (
                <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
              )}
              Kontext aus Dokumenten genererieren
            </Button>
          </div>

          <TextareaAutosize
            value={promptContext}
            className={`w-full p-4 rounded-lg border border-gray-200 text-sm`}
            placeholder={`Fülle hier deine Unternehmensdaten ein.`}
            minRows={10}
            style={{ fontFamily: 'Pantea-Text' }}
            maxRows={20}
            onChange={(e) => setPromptContext(e.target.value)}
          ></TextareaAutosize>
        </div>
      </div>
    </MainLayout>
  );
};
