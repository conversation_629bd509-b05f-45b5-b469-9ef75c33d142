
import { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>eader, 
  <PERSON>alog<PERSON><PERSON>le, 
  <PERSON>alogClose,
  Di<PERSON>Footer
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Search, Link, Unlink } from 'lucide-react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { LinkedQuestion } from '@/types/ecovadis';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

// Modified mock projects to only include CSRD projects
const mockProjects = [
  { id: 'proj2', name: 'CSRD 2024' }
];

// Modified mock questions to only include CSRD questions
const mockQuestions = [
  { id: 'q1', code: 'BP-1_01', text: 'Basis for preparation of sustainability statement', projectId: 'proj2', projectName: 'CSRD 2024' },
  { id: 'q2', code: 'BP-1_02', text: 'Scope of consolidation of consolidated sustainability statement is same as for financial statements', projectId: 'proj2', projectName: 'CSRD 2024' },
  { id: 'q3', code: 'BP-1_03', text: 'Indication of subsidiary undertakings included in consolidation that are exempted from individual or consolidated sustainability reporting', projectId: 'proj2', projectName: 'CSRD 2024' },
  { id: 'q4', code: 'BP-1_04', text: 'Disclosure of extent to which sustainability statement covers upstream and downstream value chain', projectId: 'proj2', projectName: 'CSRD 2024' },
  { id: 'q8', code: 'ESG201', text: 'Supply Chain Audit Procedures', projectId: 'proj2', projectName: 'CSRD 2024' },
  { id: 'q9', code: 'ESG305', text: 'Water Management', projectId: 'proj2', projectName: 'CSRD 2024' }
];

interface DocumentLinksManagerProps {
  isOpen: boolean;
  onClose: () => void;
  linkedQuestions: LinkedQuestion[];
  onUpdateLinks: (links: LinkedQuestion[]) => void;
  documentId: string;
  pageNumber?: number;
}

export function DocumentLinksManager({ 
  isOpen, 
  onClose, 
  linkedQuestions = [], 
  onUpdateLinks,
  documentId,
  pageNumber
}: DocumentLinksManagerProps) {
  const [activeTab, setActiveTab] = useState<string>('linked');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [filteredQuestions, setFilteredQuestions] = useState(mockQuestions);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    filterQuestions(event.target.value, selectedProject);
  };

  const filterQuestions = (term: string, projectId: string | null) => {
    let filtered = mockQuestions;
    
    if (term) {
      filtered = filtered.filter(q => 
        q.code.toLowerCase().includes(term.toLowerCase()) || 
        q.text.toLowerCase().includes(term.toLowerCase())
      );
    }
    
    if (projectId) {
      filtered = filtered.filter(q => q.projectId === projectId);
    }
    
    setFilteredQuestions(filtered);
  };

  const handleProjectSelect = (projectId: string) => {
    setSelectedProject(projectId === selectedProject ? null : projectId);
    filterQuestions(searchTerm, projectId === selectedProject ? null : projectId);
  };

  const isLinked = (questionId: string) => {
    return linkedQuestions.some(q => q.id === questionId);
  };

  const handleToggleLink = (question: any) => {
    if (isLinked(question.id)) {
      // Remove link
      const updatedLinks = linkedQuestions.filter(q => q.id !== question.id);
      onUpdateLinks(updatedLinks);
      toast.success(`Unlinked question ${question.code}`, {
        description: `Question has been unlinked from the document${pageNumber ? ` page ${pageNumber}` : ''}.`
      });
    } else {
      // Add link with default pages if it's for a specific page
      const newLinkedQuestion: LinkedQuestion = {
        ...question,
        pages: pageNumber ? pageNumber.toString() : '1-12'
      };
      
      onUpdateLinks([...linkedQuestions, newLinkedQuestion]);
      toast.success(`Linked question ${question.code}`, {
        description: `Question has been linked to the document${pageNumber ? ` page ${pageNumber}` : ''}.`
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-glacier-darkBlue">
            CSRD Links
          </DialogTitle>
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden flex flex-col">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="flex justify-between items-center mb-4">
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="linked">
                  Linked ({linkedQuestions.length})
                </TabsTrigger>
                <TabsTrigger value="not-linked">Not Linked</TabsTrigger>
              </TabsList>
              
              <div className="flex gap-2 items-center">
                <div className="relative flex-1 max-w-xs">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search questions..."
                    value={searchTerm}
                    onChange={handleSearch}
                    className="pl-8"
                  />
                </div>
                
                <div className="flex gap-1">
                  {mockProjects.map(project => (
                    <Badge 
                      key={project.id}
                      variant={selectedProject === project.id ? "default" : "outline"}
                      className={`cursor-pointer ${selectedProject === project.id ? 'bg-glacier-darkBlue' : 'hover:bg-glacier-mint/10'}`}
                      onClick={() => handleProjectSelect(project.id)}
                    >
                      {project.name}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
            
            <div className="overflow-auto flex-1 max-h-[60vh] border rounded-md">
              <Table>
                <TableHeader className="sticky top-0 bg-white z-10">
                  <TableRow>
                    <TableHead>Code</TableHead>
                    <TableHead>Question</TableHead>
                    <TableHead className="text-right">Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredQuestions
                    .filter(q => {
                      if (activeTab === 'linked') return isLinked(q.id);
                      if (activeTab === 'not-linked') return !isLinked(q.id);
                      return true; // 'all' tab
                    })
                    .map(question => (
                      <TableRow key={question.id}>
                        <TableCell className="font-mono">{question.code}</TableCell>
                        <TableCell>
                          <div>
                            <p>{question.text}</p>
                            <p className="text-xs text-gray-500">{question.projectName}</p>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant={isLinked(question.id) ? "destructive" : "secondary"}
                            size="sm"
                            className="gap-1"
                            onClick={() => handleToggleLink(question)}
                          >
                            {isLinked(question.id) ? (
                              <>
                                <Unlink className="h-4 w-4" />
                                Unlink
                              </>
                            ) : (
                              <>
                                <Link className="h-4 w-4" />
                                Add Link
                              </>
                            )}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  
                  {filteredQuestions.filter(q => {
                    if (activeTab === 'linked') return isLinked(q.id);
                    if (activeTab === 'not-linked') return !isLinked(q.id);
                    return true;
                  }).length === 0 && (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-8 text-gray-500">
                        No questions found.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </Tabs>
        </div>
        
        <DialogFooter>
          <Button onClick={onClose} className="bg-glacier-darkBlue">
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
