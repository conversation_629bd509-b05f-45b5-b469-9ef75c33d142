// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// const SUPABASE_URL = 'http://127.0.0.1:54321';
const SUPABASE_URL = "https://utgbwodkmgowdgyltpyb.supabase.co";
// const SUPABASE_PUBLISHABLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0Z2J3b2RrbWdvd2RneWx0cHliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIzMDY3NTAsImV4cCI6MjA1Nzg4Mjc1MH0.mbbmAfkarOm_rCFnulSRQdB605fZThqMkCjNHfW4uCw";


// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);