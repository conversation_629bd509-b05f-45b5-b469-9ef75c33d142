import { Injectable, Logger } from '@nestjs/common';
import { Processor, Process, InjectQueue } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { JobProcessor, JobQueue } from 'src/types/jobs';
import { DocumentService } from 'src/document/document.service';
import { DatapointDocumentChunkService } from 'src/datapoint-document-chunk/datapoint-document-chunk.service';
import { UsersService } from 'src/users/users.service';
import { DocumentStatus } from 'src/document/entities/document.entity';
import { DatapointRequestService } from 'src/datapoint/datapoint-request.service';
import { DataRequestService } from 'src/data-request/data-request.service';
import { BULK_DATAPOINT_OPERATIONS } from 'src/data-request/constants';
import { parsePageRanges } from 'src/util/common-util';

@Injectable()
@Processor(JobProcessor.DatapointGeneration)
export class DatapointGenerationProcessor {
  private readonly logger = new Logger(DatapointGenerationProcessor.name);

  constructor(
    private readonly datapointRequestService: DatapointRequestService,
    private readonly dataRequestService: DataRequestService
    // Add other necessary services for datapoint generation
  ) {}

  @Process({ name: JobQueue.DatapointGenerate, concurrency: 10 })
  async generateDatapoint(job: Job) {
    this.logger.log(
      `Processing datapoint generation: ${job.data.datapointRequestId}`
    );
    const payload = job?.data;
    try {
      await this.datapointRequestService.generateDatapointContentWithAI({
        datapointRequestId: payload.datapointRequestId,
        userId: payload.userId,
        workspaceId: payload.workspaceId,
        useExistingReportTextForReference:
          payload.useExistingReportTextForReference,
      });
      this.dataRequestService.emitSseEvents({
        dataRequestId: payload.dataRequestId,
        datapointRequestId: payload.datapointRequestId,
        status: 'success',
        operation: BULK_DATAPOINT_OPERATIONS.GENERATE,
      });
    } catch (error) {
      this.logger.error(
        `Error generating datapoint: ${job.data.datapointId}`,
        error
      );
      this.dataRequestService.emitSseEvents({
        dataRequestId: payload.dataRequestId,
        datapointRequestId: payload.datapointRequestId,
        status: 'failed',
        operation: BULK_DATAPOINT_OPERATIONS.GENERATE,
      });
    }
  }
}

@Injectable()
@Processor(JobProcessor.DatapointReview)
export class DatapointReviewProcessor {
  private readonly logger = new Logger(DatapointReviewProcessor.name);

  constructor(
    private readonly datapointRequestService: DatapointRequestService,
    private readonly dataRequestService: DataRequestService
    // Add other necessary services for datapoint generation
  ) {}

  @Process({ name: JobQueue.DatapointReview, concurrency: 10 })
  async reviewDatapoint(job: Job) {
    this.logger.log(
      `Processing datapoint reviewing: ${job.data.datapointRequestId}`
    );
    const payload = job?.data;
    try {
      await this.datapointRequestService.reviewDatapointContentWithAI({
        datapointRequestId: payload.datapointRequestId,
        userId: payload.userId,
        workspaceId: payload.workspaceId,
      });
      this.dataRequestService.emitSseEvents({
        dataRequestId: payload.dataRequestId,
        datapointRequestId: payload.datapointRequestId,
        status: 'success',
        operation: BULK_DATAPOINT_OPERATIONS.REVIEW,
      });
    } catch (error) {
      this.logger.error(
        `Error reviewing datapoint: ${job.data.datapointId}`,
        error
      );
      this.dataRequestService.emitSseEvents({
        dataRequestId: payload.dataRequestId,
        datapointRequestId: payload.datapointRequestId,
        status: 'failed',
        operation: BULK_DATAPOINT_OPERATIONS.REVIEW,
      });
    }
  }
}

@Injectable()
@Processor(JobProcessor.ChunkExtraction)
export class ChunkExtractionProcessor {
  private readonly logger = new Logger(ChunkExtractionProcessor.name);

  constructor(
    private readonly documentService: DocumentService,
    @InjectQueue(JobProcessor.ChunkDpLinking)
    private readonly chunkLinkingQueue: Queue
  ) {}

  @Process({ name: JobQueue.ChunkExtract, concurrency: 5 })
  async handleChunkExtraction(job: Job) {
    const { documentId, pageNumbers, answerId, comment, premiumParse } =
      job.data;
    this.logger.log(`Bull Processing extraction for document: ${documentId}`);

    try {
      // call the extraction logic from DocumentService
      await this.documentService.extractDocumentChunks(
        documentId,
        premiumParse
      );

      if (pageNumbers && answerId) {
        // Convert page range string to array of page numbers
        const pages = parsePageRanges(pageNumbers);
        for (const page of pages) {
          const documentChunk =
            await this.documentService.findDocumentChunkByPage(
              documentId,
              page
            );

          if (documentChunk) {
            // Save to project_ecovadis_linked_document_chunks table
            await this.documentService.saveLinkedDocumentChunk({
              documentChunkId: documentChunk.id,
              answerId,
              comment,
            });
          }
        }
      } else {
        // add next step to the queue (linking) once extraction is done
        // await this.chunkLinkingQueue.add(
        //   JobQueue.ChunkDpLink,
        //   {
        //     documentId,
        //   },
        //   {
        //     jobId: `chunkLinking-${documentId}`,
        //     attempts: 5, // Retry up to 5 times
        //     backoff: {
        //       type: 'exponential', // Exponential backoff
        //       delay: 5000, // Start with a 5-second delay
        //     },
        //     removeOnComplete: isDevelopment ? false : true,
        //   }
        // );
      }

      // update the document status
      await this.documentService.updateDocumentStatus(documentId, {
        status: DocumentStatus.LinkingDataFinished,
      });
    } catch (error) {
      this.logger.error(`Error processing document: ${documentId}`, error);
      await this.documentService.updateDocumentStatus(documentId, {
        status: DocumentStatus.FailedExtraction,
      });
      throw error;
    }
  }
}

@Injectable()
@Processor(JobProcessor.ChunkDpLinking)
export class ChunkLinkingProcessor {
  private readonly logger = new Logger(ChunkLinkingProcessor.name);

  constructor(
    private readonly documentService: DocumentService,
    private readonly datapointDocumentChunkService: DatapointDocumentChunkService,
    private readonly userService: UsersService
  ) {}

  @Process({ name: JobQueue.ChunkDpLink, concurrency: 3 })
  async handleChunkLinking(job: Job) {
    const { documentId } = job.data;
    this.logger.log(`Bull Processing linking for document: ${documentId}`);

    try {
      // const globalAIUser = await this.userService.findGlobalGlacierAIUser();
      // await this.datapointDocumentChunkService.linkDocumentChunksToDatapoints(
      //   documentId,
      //   globalAIUser.id
      // );
      await this.documentService.updateDocumentStatus(documentId, {
        status: DocumentStatus.LinkingDataFinished,
      });
    } catch (error) {
      this.logger.error(`Error linking document: ${documentId}`, error);
      await this.documentService.updateDocumentStatus(documentId, {
        status: DocumentStatus.FailedLinking,
      });
      throw error;
    }
  }
}
