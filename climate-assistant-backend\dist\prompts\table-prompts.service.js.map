{"version": 3, "file": "table-prompts.service.js", "sourceRoot": "", "sources": ["../../src/prompts/table-prompts.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,4CAA4C;AAC5C,gEAAgE;AAEhE,iEAAmE;AAEnE,4CAA6C;AAGtC,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAGrB,sBAAsB,CAAC,YAAsB;QACnD,OAAO,wBAAY,CAAC,YAAY,CAAC,CAAC;IACpC,CAAC;IAED;QANiB,wBAAmB,GAAG,QAAQ,CAAC;QAO9C,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC;YACzC,YAAY,EAAE,KAAK;YACnB,gBAAgB,EAAE,GAAG;SACtB,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,iBAAiB,CAAC,MAAM,CAAC;QACxC,MAAM,aAAa,GAAG,iBAAiB,CAAC,aAAa,CAAC;QACtD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC;IACpD,CAAC;IAGD,oDAAoD;QAClD,OAAO;;;;;;;;iBAQM,CAAC;IAChB,CAAC;IAKD,yCAAyC,CAAC,aAAqB;QAC7D,OAAO;;;;;;;;;;;;;;;;kBAgBO,aAAa;uCACQ,CAAC;IACtC,CAAC;IAGD,wDAAwD,CACtD,cAA+B,EAC/B,eAAgC;QAEhC,MAAM,oBAAoB,GAAG,eAAe;aACzC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;aAClC,IAAI,CAAC,IAAI,CAAC,CAAC;QAId,IAAI,MAAM,GAAG,6BAA6B,CAAC;QAC3C,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE,CAAC;YAC3C,MAAM,IAAI,iBAAiB,aAAa,CAAC,WAAW,MAAM,aAAa,CAAC,IAAI;;4CAEtC,aAAa,CAAC,WAAW,4IAA4I,aAAa,CAAC,WAAW;;4DAE9K,aAAa,CAAC,yBAAyB,CAAC,EAAE;KACjG,CAAC;YAEA,MAAM,IAAI,IAAI,CAAC,qCAAqC,CAAC,aAAa,CAAC,CAAC;YACpE,MAAM,IAAI,cAAc,CAAC;QAC3B,CAAC;QAED,MAAM,IAAI,mPAAmP,oBAAoB,uBAAuB,CAAC;QACzS,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,qCAAqC,CAAC,aAA4B;QAChE,IAAI,qBAAqB,GAAG,MAAM,aAAa,CAAC,WAAW,KAAK,aAAa,CAAC,IAAI;kBACpE,aAAa,CAAC,OAAO,EAAE,CAAC;QAEtC,IAAI,aAAa,CAAC,SAAS,EAAE,CAAC;YAC5B,qBAAqB,IAAI;iBACd,aAAa,CAAC,SAAS,EAAE,CAAC;QACvC,CAAC;QACD,IAAI,aAAa,CAAC,SAAS,EAAE,CAAC;YAC5B,qBAAqB,IAAI;gCACC,aAAa,CAAC,SAAS,EAAE,CAAC;QACtD,CAAC;QACD,IAAI,aAAa,CAAC,WAAW,EAAE,CAAC;YAC9B,qBAAqB,IAAI;iBACd,aAAa,CAAC,WAAW,EAAE,CAAC;QACzC,CAAC;QACD,OAAO,qBAAqB,CAAC;IAC/B,CAAC;IAGD,+CAA+C,CAAC,EAC9C,aAAa,EACb,kBAAkB,EAClB,yBAAyB,EACzB,gBAAgB,GAMjB;QACC,MAAM,qBAAqB,GAAG,gBAAgB;aAC3C,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;aACnB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;aACrB,IAAI,EAAE,CAAC;QAEV,OAAO;;;;;;;;;6HASkH,aAAa,CAAC,WAAW;;;;;;;;;;;;;;IAclJ,yBAAyB,KAAK,EAAE,CAAC,CAAC,CAAC,kCAAkC,yBAAyB,EAAE,CAAC,CAAC,CAAC,EAAE;;;sDAGnD,kBAAkB;IACpE,qBAAqB,KAAK,EAAE,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,8FAA8F,qBAAqB,EAAE,CAAC,CAAC,CAAC,EAAE;CAChM,CAAC;IACA,CAAC;IAED,yBAAyB,CAAC,EACxB,cAAc,EACd,kBAAkB,EAClB,yBAAyB,EACzB,gBAAgB,EAChB,cAAc,EACd,YAAY,EACZ,eAAe,EACf,aAAa,EACb,qBAAqB,GAWtB;QACC,MAAM,oBAAoB,GAAG,eAAe;aACzC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;aAClC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,MAAM,qBAAqB,GAAG,gBAAgB;aAC3C,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;aACnB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;aACrB,IAAI,EAAE,CAAC;QAEV,MAAM,uBAAuB,GAAG,cAAc;YAC5C,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAC3B,cAAc,CAAC,OAAO,CAAC,yCAAqB,EAAE,IAAI,CAAC,CACpD;YACH,CAAC,CAAC,EAAE,CAAC;QAEP,MAAM,gBAAgB,GAAG,cAAc;aACpC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,WAAW,MAAM,SAAS,CAAC,IAAI,IAAI,CAAC;aACpE,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,IAAI,OAAO,GAAG,6BAA6B,CAAC;QAC5C,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE,CAAC;YAC3C,OAAO,IAAI,iBAAiB,aAAa,CAAC,WAAW,MAAM,aAAa,CAAC,IAAI;;8CAErC,aAAa,CAAC,WAAW,4IAA4I,aAAa,CAAC,WAAW;;8DAE9K,aAAa,CAAC,yBAAyB,CAAC,EAAE;OACjG,CAAC;YAEF,OAAO,IAAI,IAAI,CAAC,qCAAqC,CAAC,aAAa,CAAC,CAAC;YACrE,OAAO,IAAI,cAAc,CAAC;QAC5B,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAE7C,OAAO;+MACoM,gBAAgB;;gWAEiI,gBAAgB;;;;;;;;;;+LAUjL,gBAAgB;;;;;;oFAM3H,wBAAY,CAAC,kBAAkB,CAAC;WACzG,yBAAyB,KAAK,EAAE,CAAC,CAAC,CAAC,kCAAkC,yBAAyB,kDAAkD,CAAC,CAAC,CAAC,EAAE;QACxJ,CAAC,CAAC,qBAAqB,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,iDAAiD,qBAAqB,+EAA+E,CAAC,CAAC,CAAC,EAAE;QACxN,CAAC,CAAC,cAAc,IAAI,+BAA+B,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,yCAAqB,EAAE,IAAI,CAAC,CAAC,iUAAiU;QACtc,CAAC,CAAC,qBAAqB,IAAI,uCAAuC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,qBAAqB,CAAC,2LAA2L;;;;qCAIpR,WAAW,QAAQ,aAAa,CAAC,CAAC,CAAC,sIAAsI,GAAG,aAAa,CAAC,CAAC,CAAC,sGAAsG;;0SAE7B,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;QA2BlT,OAAO;;;;QAIP,oBAAoB;;;;;QAKpB,YAAY;;;;;yJAKqI,wBAAY,CAAC,kBAAkB,CAAC,EAAE,CAAC;IAC1L,CAAC;CAGF,CAAA;AAtRY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;;GACA,kBAAkB,CAsR9B"}