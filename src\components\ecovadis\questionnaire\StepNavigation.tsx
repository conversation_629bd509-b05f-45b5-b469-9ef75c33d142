
import React from 'react';
import { Button } from '@/components/ui/button';

interface StepNavigationProps {
  currentStep: number;
  progress: Record<number, boolean>;
  onPrevious: () => void;
  onNext: () => void;
  onSkip?: () => void;
}

export const StepNavigation: React.FC<StepNavigationProps> = ({
  currentStep,
  progress,
  onPrevious,
  onNext,
  onSkip
}) => {
  if (currentStep >= 5) return null;
  
  return (
    <div className="flex justify-between mt-8">
      <Button 
        variant="ghost" 
        onClick={onPrevious} 
        disabled={currentStep === 1} 
        className="text-glacier-darkBlue"
      >
        Previous
      </Button>
      <div className="flex items-center">
        {(currentStep === 2 || currentStep === 3) && onSkip && (
          <Button
            variant="ghost"
            className="text-glacier-darkBlue mr-2.5" 
            onClick={onSkip}
          >
            Skip this step
          </Button>
        )}
        <Button 
          onClick={onNext} 
          variant="darkBlue" 
          className="bg-glacier-darkBlue text-white"
          disabled={!progress[currentStep]}
        >
          Next Step
        </Button>
      </div>
    </div>
  );
};
