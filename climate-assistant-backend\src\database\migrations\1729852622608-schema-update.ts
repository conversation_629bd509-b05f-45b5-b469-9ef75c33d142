import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1729852622608 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // ESRS Topics
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ALTER COLUMN "paragraph" DROP NOT NULL`,
    );

    await queryRunner.query(`
        INSERT INTO esrs_topic (id, name) VALUES (11, 'General Disclosures');
        INSERT INTO esrs_topic (id, name) VALUES (12, 'Minimal Disclosure Requirement');
    `);

    // ESRS Disclosure Requirements

    await queryRunner.query(`
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (84,84,'BP-1','ESRS 2','General basis for preparation of sustainability statements');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (85,85,'BP-2','ESRS 2','Disclosures in relation to specific circumstances');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (86,86,'GOV-1','ESRS 2','The role of the administrative, management and supervisory bodies');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (87,87,'GOV-2','ESRS 2','Information provided to and sustainability matters addressed by the undertaking’s administrative, management and supervisory bodies');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (88,88,'GOV-3','ESRS 2','Integration of sustainability-related performance in incentive schemes');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (89,89,'GOV-4','ESRS 2','Statement on due diligence');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (90,90,'GOV-5','ESRS 2','Risk management and internal controls over sustainability reporting');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (91,91,'SBM-1','ESRS 2','Strategy, business model and value chain');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (92,92,'SBM-2','ESRS 2','Interests and views of stakeholders');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (93,93,'SBM-3','ESRS 2','Material impacts, risks and opportunities and their interaction with strategy and business model');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (94,94,'IRO-1','ESRS 2','Description of the process to identify and assess material impacts, risks and opportunities');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (95,95,'IRO-2','ESRS 2','Disclosure requirements in ESRS covered by the undertaking’s sustainability statement');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (96,96,'MDR-P','ESRS 2','Policies adopted to manage material sustainability matters');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (97,97,'MDR-A','ESRS 2','Actions and resources in relation to material sustainability matters');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (98,98,'MDR-M','ESRS 2','Metrics in relation to material sustainability matters');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (99,99,'MDR-T','ESRS 2','Tracking effectiveness of policies and actions through targets');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (100,100,'MDR-P','ESRS 2','Policies adopted to manage material sustainability matters');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (101,101,'MDR-P','ESRS 2','Policies adopted to manage material sustainability matters');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (102,102,'MDR-A','ESRS 2','Actions and resources in relation to material sustainability matters');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (103,103,'MDR-A','ESRS 2','Actions and resources in relation to material sustainability matters');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (104,104,'MDR-T','ESRS 2','Tracking effectiveness of policies and actions through targets');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (105,105,'MDR-T','ESRS 2','Tracking effectiveness of policies and actions through targets');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (106,106,'MDR-T','ESRS 2','Tracking effectiveness of policies and actions through targets');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (107,107,'MDR-T','ESRS 2','Tracking effectiveness of policies and actions through targets');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (108,108,'MDR-T','ESRS 2','Tracking effectiveness of policies and actions through targets');
        INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (109,109,'MDR-T','ESRS 2','Tracking effectiveness of policies and actions through targets');
    `);

    // ESRS Topic Disclosure Requirement Mapping

    await queryRunner.query(`
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (11, 84);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (11, 85);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (11, 86);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (11, 87);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (11, 88);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (11, 89);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (11, 90);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (11, 91);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (11, 92);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (11, 93);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (11, 94);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (11, 95);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (12, 96);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (12, 97);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (12, 98);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (12, 99);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (12, 100);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (12, 101);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (12, 102);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (12, 103);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (12, 104);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (12, 105);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (12, 106);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (12, 107);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (12, 108);
        INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (12, 109);
        `);

    // ESRS Datapoint

    await queryRunner.query(`
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (995,'BP-1_01', 'Basis for preparation of sustainability statement', 84);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (996,'BP-1_02', 'Scope of consolidation of consolidated sustainability statement is same as for financial statements', 84);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (997,'BP-1_03', 'Indication of subsidiary undertakings included in consolidation that are exempted from individual or consolidated sustainability reporting', 84);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (998,'BP-1_04', 'Disclosure of extent to which sustainability statement covers upstream and downstream value chain', 84);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (999,'BP-1_05', 'Option to omit specific piece of information corresponding to intellectual property, know-how or results of innovation has been used', 84);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1000,'BP-1_06', 'Option allowed by Member State to omit disclosure of impending developments or matters in course of negotiation has been used', 84);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1001,'BP-2_01', 'Disclosure of definitions of medium- or long-term time horizons', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1002,'BP-2_02', 'Disclosure of reasons for applying different definitions of time horizons', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1003,'BP-2_03', 'Disclosure of metrics that include value chain data estimated using indirect sources', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1004,'BP-2_04', 'Description of basis for preparation of metrics that include value chain data estimated using indirect sources', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1005,'BP-2_05', 'Description of resulting level of accuracy of metrics that include value chain data estimated using indirect sources', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1006,'BP-2_06', 'Description of planned actions to improve accuracy in future of metrics that include value chain data estimated using indirect sources', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1007,'BP-2_07', 'Disclosure of quantitative metrics and monetary amounts disclosed that are subject to high level of measurement uncertainty', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1008,'BP-2_08', 'Disclosure of sources of measurement uncertainty', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1009,'BP-2_09', 'Disclosure of assumptions, approximations and judgements made in measurement', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1010,'BP-2_10', 'Explanation of changes in preparation and presentation of sustainability information and reasons for them', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1011,'BP-2_11', 'Adjustment of comparative information for one or more prior periods is impracticable', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1012,'BP-2_12', 'Disclosure of difference between figures disclosed in preceding period and revised comparative figures', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1013,'BP-2_13', 'Disclosure of nature of prior period material errors', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1014,'BP-2_14', 'Disclosure of corrections for prior periods included in sustainability statement', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1015,'BP-2_15', 'Disclosure of why correction of prior period errors is not practicable', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1016,'BP-2_16', 'Disclosure of other legislation or generally accepted sustainability reporting standards and frameworks based on which information has been included in sustainability statement', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1017,'BP-2_17', 'Disclosure of reference to paragraphs of standard or framework applied', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1018,'BP-2_18', 'European standards approved by European Standardisation System (ISO/IEC or CEN/CENELEC standards) have been relied on', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1019,'BP-2_19', 'Disclosure of extent to which data and processes that are used for sustainability reporting purposes have been verified by external assurance provider and found to conform to corresponding ISO/IEC or CEN/CENELEC standard', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1020,'BP-2_20', 'List of DRs or DPs incorporated by reference', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1021,'BP-2_21', 'Topics (E4, S1, S2, S3, S4) have been assessed to be material', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1022,'BP-2_22', 'List of sustainability matters assessed to be material (phase-in)', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1023,'BP-2_23', 'Disclosure of how business model and strategy take account of impacts related to sustainability matters assessed to be material (phase-in)', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1024,'BP-2_24', 'Description of any time-bound targets set related to sustainability matters assessed to be material (phase-in) and progress made towards achieving those targets', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1025,'BP-2_25', 'Description of policies related to sustainability matters assessed to be material (phase-in)', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1026,'BP-2_26', 'Description of actions taken to identify, monitor, prevent, mitigate, remediate or bring end to actual or potential adverse impacts related to sustainability matters assessed to be material (phase-in) and result of such actions', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1027,'BP-2_27', 'Disclosure of metrics related to sustainability matters assessed to be material (phase-in)', 85);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1028,'GOV-1_01', 'Number of executive members', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1029,'GOV-1_02', 'Number of non-executive members', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1030,'GOV-1_03', 'Information about representation of employees and other workers', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1031,'GOV-1_04', 'Information about member''s experience relevant to sectors, products and geographic locations of undertaking', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1032,'GOV-1_05', 'Percentage of members of administrative, management and supervisory bodies by gender and other aspects of diversity', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1033,'GOV-1_06', 'Board''s gender diversity ratio', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1034,'GOV-1_07', 'Percentage of independent board members', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1035,'GOV-1_08', 'Information about identity of administrative, management and supervisory bodies or individual(s) within body responsible for oversight of impacts, risks and opportunities', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1036,'GOV-1_09', 'Disclosure of how body''s or individuals within body responsibilities for impacts, risks and opportunities are reflected in undertaking''s terms of reference, board mandates and other related policies', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1037,'GOV-1_10', 'Description of management''s role in governance processes, controls and procedures used to monitor, manage and oversee impacts, risks and opportunities', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1038,'GOV-1_11', 'Description of how oversight is exercised over management-level position or committee to which management''s role is delegated to', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1039,'GOV-1_12', 'Information about reporting lines to administrative, management and supervisory bodies', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1040,'GOV-1_13', 'Disclosure of how dedicated controls and procedures are integrated with other internal functions', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1041,'GOV-1_14', 'Disclosure of how administrative, management and supervisory bodies and senior executive management oversee setting of targets related to material impacts, risks and opportunities and how progress towards them is monitored', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1042,'GOV-1_15', 'Disclosure of how administrative, management and supervisory bodies determine whether appropriate skills and expertise are available or will be developed to oversee sustainability matters', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1043,'GOV-1_16', 'Information about sustainability-related expertise that bodies either directly possess or can leverage', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1044,'GOV-1_17', 'Disclosure of how sustainability-related skills and expertise relate to material impacts, risks and opportunities', 86);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1045,'GOV-2_01', 'Disclosure of whether, by whom and how frequently administrative, management and supervisory bodies are informed about material impacts, risks and opportunities, implementation of due diligence, and results and effectiveness of policies, actions, metrics and targets adopted to address them', 87);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1046,'GOV-2_02', 'Disclosure of how administrative, management and supervisory bodies consider impacts, risks and opportunities when overseeing strategy, decisions on major transactions and risk management process', 87);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1047,'GOV-2_03', 'Disclosure of list of material impacts, risks and opportunities addressed by administrative, management and supervisory bodies or their relevant committees', 87);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1048,'GOV-2_04', 'Disclosure of how governance bodies ensure that appropriate mechanism for performance monitoring is in place', 87);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1049,'GOV-3_01', 'Incentive schemes and remuneration policies linked to sustainability matters for members of administrative, management and supervisory bodies exist', 88);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1050,'GOV-3_02', 'Description of key characteristics of incentive schemes', 88);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1051,'GOV-3_03', 'Description of specific sustainability-related targets and (or) impacts used to assess performance of members of administrative, management and supervisory bodies', 88);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1052,'GOV-3_04', 'Disclosure of how sustainability-related performance metrics are considered as performance benchmarks or included in remuneration policies', 88);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1053,'GOV-3_05', 'Percentage of variable remuneration dependent on sustainability-related targets and (or) impacts', 88);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1054,'GOV-3_06', 'Description of level in undertaking at which terms of incentive schemes are approved and updated', 88);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1055,'GOV-4_01', 'Disclosure of mapping of information provided in sustainability statement about due diligence process', 89);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1056,'GOV-5_01', 'Description of scope, main features and components of risk management and internal control processes and systems in relation to sustainability reporting', 90);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1057,'GOV-5_02', 'Description of risk assessment approach followed', 90);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1058,'GOV-5_03', 'Description of main risks identified and their mitigation strategies', 90);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1059,'GOV-5_04', 'Description of how findings of risk assessment and internal controls as regards sustainability reporting process have been integrated into relevant internal functions and processes', 90);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1060,'GOV-5_05', 'Description of periodic reporting of findings of risk assessment and internal controls to administrative, management and supervisory bodies', 90);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1061,'SBM-1_01', 'Description of significant groups of products and (or) services offered', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1062,'SBM-1_02', 'Description of significant markets and (or) customer groups served', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1063,'SBM-1_03', 'Total number of employees (head count)', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1064,'SBM-1_04', 'Number of employees (head count)', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1065,'SBM-1_05', 'Description of products and services that are banned in certain markets', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1066,'SBM-1_06', 'Total revenue', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1067,'SBM-1_07', 'Revenue by significant ESRS Sectors', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1068,'SBM-1_08', 'List of additional significant ESRS sectors in which significant activities are developed or in which undertaking is or may be connected to material impacts', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1069,'SBM-1_09', 'Undertaking is active in fossil fuel (coal, oil and gas) sector', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1070,'SBM-1_10', 'Revenue from fossil fuel (coal, oil and gas) sector', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1071,'SBM-1_11', 'Revenue from coal', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1072,'SBM-1_12', 'Revenue from oil', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1073,'SBM-1_13', 'Revenue from gas', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1074,'SBM-1_14', 'Revenue from Taxonomy-aligned economic activities related to fossil gas', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1075,'SBM-1_15', 'Undertaking is active in chemicals production', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1076,'SBM-1_16', 'Revenue from chemicals production', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1077,'SBM-1_17', 'Undertaking is active in controversial weapons', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1078,'SBM-1_18', 'Revenue from controversial weapons', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1079,'SBM-1_19', 'Undertaking is active in cultivation and production of tobacco', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1080,'SBM-1_20', 'Revenue from cultivation and production of tobacco', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1081,'SBM-1_21', 'Description of sustainability-related goals in terms of significant groups of products and services, customer categories, geographical areas and relationships with stakeholders', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1082,'SBM-1_22', 'Disclosure of assessment of current significant products and (or) services, and significant markets and customer groups, in relation to sustainability-related goals', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1083,'SBM-1_23', 'Disclosure of elements of strategy that relate to or impact sustainability matters', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1084,'SBM-1_24', 'List of ESRS sectors that are significant for undertaking', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1085,'SBM-1_25', 'Description of business model and value chain', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1086,'SBM-1_26', 'Description of inputs and approach to gathering, developing and securing inputs', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1087,'SBM-1_27', 'Description of outputs and outcomes in terms of current and expected benefits for customers, investors and other stakeholders', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1088,'SBM-1_28', 'Description of main features of upstream and downstream value chain and undertakings position in value chain', 91);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1089,'SBM-2_01', 'Description of stakeholder engagement', 92);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1090,'SBM-2_02', 'Description of key stakeholders', 92);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1091,'SBM-2_03', 'Description of categories of stakeholders for which engagement occurs', 92);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1092,'SBM-2_04', 'Description of how stakeholder engagement is organised', 92);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1093,'SBM-2_05', 'Description of purpose of stakeholder engagement', 92);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1094,'SBM-2_06', 'Description of how outcome of stakeholder engagement is taken into account', 92);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1095,'SBM-2_07', 'Description of understanding of interests and views of key stakeholders as they relate to undertaking''s strategy and business model', 92);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1096,'SBM-2_08', 'Description of amendments to strategy and (or) business model', 92);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1097,'SBM-2_09', 'Description of how strategy and (or) business model have been amended or are expected to be amended to address interests and views of stakeholders', 92);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1098,'SBM-2_10', 'Description of any further steps that are being planned and in what timeline', 92);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1099,'SBM-2_11', 'Further steps that are being planned are likely to modify relationship with and views of stakeholders', 92);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1100,'SBM-2_12', 'Description of how administrative, management and supervisory bodies are informed about views and interests of affected stakeholders with regard to sustainability-related impacts', 92);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1101,'SBM-3_01', 'Description of material impacts resulting from materiality assessment', 93);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1102,'SBM-3_02', 'Description of material risks and opportunities resulting from materiality assessment', 93);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1103,'SBM-3_03', 'Disclosure of current and anticipated effects of material impacts, risks and opportunities on business model, value chain, strategy and decision-making, and how undertaking has responded or plans to respond to these effects', 93);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1104,'SBM-3_04', 'Disclosure of how material negative and positive impacts affect (or are likely to affect) people or environment', 93);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1105,'SBM-3_05', 'Disclosure of whether and how material impacts originate from or are connected to strategy and business model', 93);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1106,'SBM-3_06', 'Disclosure of reasonably expected time horizons of material impacts', 93);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1107,'SBM-3_07', 'Description of nature of activities or business relationships through which undertaking is involved with material impacts', 93);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1108,'SBM-3_08', 'Disclosure of current financial effects of material risks and opportunities on financial position, financial performance and cash flows and material risks and opportunities for which there is significant risk of material adjustment within next annual reporting period to carrying amounts of assets and liabilities reported in related financial statements', 93);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1109,'SBM-3_09', 'Disclosure of anticipated financial effects of material risks and opportunities on financial position, financial performance and cash flows over short-, medium- and long-term', 93);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1110,'SBM-3_10', 'Information about resilience of strategy and business model regarding capacity to address material impacts and risks and to take advantage of material opportunities', 93);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1111,'SBM-3_11', 'Disclosure of changes to material impacts, risks and opportunities compared to previous reporting period', 93);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1112,'SBM-3_12', 'Disclosure of specification of impacts, risks and opportunities that are covered by ESRS Disclosure Requirements as opposed to those covered by additional entity-specific disclosures', 93);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1113,'IRO-1_01', 'Description of methodologies and assumptions applied in process to identify impacts, risks and opportunities', 94);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1114,'IRO-1_02', 'Description of process to identify, assess, prioritise and monitor potential and actual impacts on people and environment, informed by due diligence process', 94);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1115,'IRO-1_03', 'Description of how process focuses on specific activities, business relationships, geographies or other factors that give rise to heightened risk of adverse impacts', 94);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1116,'IRO-1_04', 'Description of how process considers impacts with which undertaking is involved through own operations or as result of business relationships', 94);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1117,'IRO-1_05', 'Description of how process includes consultation with affected stakeholders to understand how they may be impacted and with external experts', 94);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1118,'IRO-1_06', 'Description of how process prioritises negative impacts based on their relative severity and likelihood and positive impacts based on their relative scale, scope and likelihood and determines which sustainability matters are material for reporting purposes', 94);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1119,'IRO-1_07', 'Description of process used to identify, assess, prioritise and monitor risks and opportunities that have or may have financial effects', 94);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1120,'IRO-1_08', 'Description of how connections of impacts and dependencies with risks and opportunities that may arise from those impacts and dependencies have been considered', 94);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1121,'IRO-1_09', 'Description of how likelihood, magnitude, and nature of effects of identified risks and opportunities have been assessed', 94);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1122,'IRO-1_10', 'Description of how sustainability-related risks relative to other types of risks have been prioritised', 94);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1123,'IRO-1_11', 'Description of decision-making process and related internal control procedures', 94);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1124,'IRO-1_12', 'Description of extent to which and how process to identify, assess and manage impacts and risks is integrated into overall risk management process and used to evaluate overall risk profile and risk management processes', 94);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1125,'IRO-1_13', 'Description of extent to which and how process to identify, assess and manage opportunities is integrated into overall management process', 94);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1126,'IRO-1_14', 'Description of input parameters used in process to identify, assess and manage material impacts, risks and opportunities', 94);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1127,'IRO-1_15', 'Description of how process to identify, assess and manage impacts, risks and opportunities has changed compared to prior reporting period', 94);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1128,'IRO-2_01', 'Disclosure of list of data points that derive from other EU legislation and information on their location in sustainability statement', 95);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1129,'IRO-2_02', 'Disclosure of list of ESRS Disclosure Requirements complied with in preparing sustainability statement following outcome of materiality assessment', 95);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1130,'IRO-2_03', 'Explanation of negative materiality assessment for ESRS E1 Climate change', 95);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1131,'IRO-2_04', 'Explanation of negative materiality assessment for ESRS E2 Pollution', 95);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1132,'IRO-2_05', 'Explanation of negative materiality assessment for ESRS E3 Water and marine resources', 95);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1133,'IRO-2_06', 'Explanation of negative materiality assessment for ESRS E4 Biodiversity and ecosystems', 95);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1134,'IRO-2_07', 'Explanation of negative materiality assessment for ESRS E5 Circular economy', 95);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1135,'IRO-2_08', 'Explanation of negative materiality assessment for ESRS S1 Own workforce', 95);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1136,'IRO-2_09', 'Explanation of negative materiality assessment for ESRS S2 Workers in value chain', 95);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1137,'IRO-2_10', 'Explanation of negative materiality assessment for ESRS S3 Affected communities', 95);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1138,'IRO-2_11', 'Explanation of negative materiality assessment for ESRS S4 Consumers and end-users', 95);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1139,'IRO-2_12', 'Explanation of negative materiality assessment for ESRS G1 Business conduct', 95);
        INSERT INTO esrs_datapoint (id, "datapointId", name,  "esrsDisclosureRequirementId") VALUES (1140,'IRO-2_13', 'Explanation of how material information to be disclosed in relation to material impacts, risks and opportunities has been determined', 95);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        DELETE FROM esrs_topic_disclosure_requirement WHERE "esrsDisclosureRequirementId" IN (84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109);
        DELETE FROM esrs_disclosure_requirement WHERE id IN (84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109);
        DELETE FROM esrs_topic WHERE id IN (11, 12);
        DELETE FROM esrs_datapoint WHERE id BETWEEN 995 AND 1140;
    `);
  }
}
