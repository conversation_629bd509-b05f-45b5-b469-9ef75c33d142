
import { useState, useEffect } from 'react';
import { Document, LinkedQuestion } from '@/types/ecovadis';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogClose 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { FileText, X } from 'lucide-react';
import { DocumentContentTab } from './tabs/DocumentContentTab';
import { DocumentPropertiesTab } from './tabs/DocumentPropertiesTab';
import { DocumentLinkedQuestions } from './DocumentLinkedQuestions';
import { formatDocumentType } from '@/utils/documentUtils';

interface DocumentDetailProps {
  document: Document | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (document: Document) => void;
}

export function DocumentDetail({ document, open, onOpenChange, onSave }: DocumentDetailProps) {
  const [editedDoc, setEditedDoc] = useState<Document | null>(document);
  const [activeTab, setActiveTab] = useState('content');
  const [linkedQuestions, setLinkedQuestions] = useState<LinkedQuestion[]>([]);
  
  useEffect(() => {
    setEditedDoc(document);
    if (document?.linkedQuestions) {
      setLinkedQuestions(document.linkedQuestions);
    }
  }, [document]);
  
  if (!document || !editedDoc) return null;
  
  const handleSave = (updatedDoc: Document) => {
    setEditedDoc(updatedDoc);
    onSave(updatedDoc);
  };

  const handleUpdateLinks = (updatedLinks: LinkedQuestion[]) => {
    setLinkedQuestions(updatedLinks);
    if (editedDoc) {
      const updatedDoc = {
        ...editedDoc,
        linkedQuestions: updatedLinks,
        linkedQuestionsCount: updatedLinks.length
      };
      setEditedDoc(updatedDoc);
      onSave(updatedDoc);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="text-xl font-semibold text-glacier-darkBlue">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {editedDoc.name}
            </div>
          </DialogTitle>
          <DialogClose asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <X className="h-4 w-4" />
            </Button>
          </DialogClose>
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid grid-cols-3">
              <TabsTrigger value="content">Document Content</TabsTrigger>
              <TabsTrigger value="links">EcoVadis Answers ({linkedQuestions.length})</TabsTrigger>
              {/* <TabsTrigger value="properties">Properties</TabsTrigger> */}
            </TabsList>
            
            <TabsContent value="content" className="flex-1 overflow-auto p-1">
              <DocumentContentTab document={editedDoc} />
            </TabsContent>
            
            <TabsContent value="links" className="flex-1 overflow-auto p-1">
              <DocumentLinkedQuestions 
                documentId={editedDoc.id}
                linkedQuestions={linkedQuestions}
                onUpdateLinks={handleUpdateLinks}
              />
            </TabsContent>
            
            {/* <TabsContent value="properties" className="flex-1 overflow-auto p-1">
              <DocumentPropertiesTab 
                document={editedDoc} 
                onSave={handleSave} 
              />
            </TabsContent> */}
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}
