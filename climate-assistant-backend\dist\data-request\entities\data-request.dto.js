"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenerateDataRequestReportTextTextPayload = exports.UpdateDataRequestPayload = exports.DataRequestData = exports.DatapointRequestData = void 0;
const data_request_entity_1 = require("./data-request.entity");
const datapoint_request_entity_1 = require("../../datapoint/entities/datapoint-request.entity");
class DatapointRequestData extends datapoint_request_entity_1.DatapointRequest {
}
exports.DatapointRequestData = DatapointRequestData;
class DataRequestData extends data_request_entity_1.DataRequest {
}
exports.DataRequestData = DataRequestData;
class UpdateDataRequestPayload {
}
exports.UpdateDataRequestPayload = UpdateDataRequestPayload;
class GenerateDataRequestReportTextTextPayload {
}
exports.GenerateDataRequestReportTextTextPayload = GenerateDataRequestReportTextTextPayload;
//# sourceMappingURL=data-request.dto.js.map