"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DataRequestService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataRequestService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const data_request_entity_1 = require("./entities/data-request.entity");
const datapoint_request_entity_1 = require("../datapoint/entities/datapoint-request.entity");
const prompts_service_1 = require("../prompts/prompts.service");
const project_service_1 = require("../project/project.service");
const comment_entity_1 = require("../project/entities/comment.entity");
const llm_response_util_1 = require("../util/llm-response-util");
const users_service_1 = require("../users/users.service");
const workspace_service_1 = require("../workspace/workspace.service");
const datapoint_request_service_1 = require("../datapoint/datapoint-request.service");
const user_workspace_entity_1 = require("../users/entities/user-workspace.entity");
const constants_1 = require("../constants");
const rxjs_1 = require("rxjs");
const datarequest_generation_entity_1 = require("./entities/datarequest-generation.entity");
const shared_datapoint_datarequest_service_1 = require("../shared/shared-datapoint-datarequest.service");
const llm_rate_limiter_service_1 = require("../llm-rate-limiter/llm-rate-limiter.service");
let DataRequestService = DataRequestService_1 = class DataRequestService {
    constructor(dataRequestRepository, promptService, projectService, userService, workspaceService, datapointRequestService, llmRateLimitService, datapointDataRequestSharedService, dataRequestGenerationRepository) {
        this.dataRequestRepository = dataRequestRepository;
        this.promptService = promptService;
        this.projectService = projectService;
        this.userService = userService;
        this.workspaceService = workspaceService;
        this.datapointRequestService = datapointRequestService;
        this.llmRateLimitService = llmRateLimitService;
        this.datapointDataRequestSharedService = datapointDataRequestSharedService;
        this.dataRequestGenerationRepository = dataRequestGenerationRepository;
        this.eventSubject = new rxjs_1.Subject();
        this.events$ = this.eventSubject.asObservable();
        this.logger = new common_1.Logger(DataRequestService_1.name);
    }
    emitSseEvents(event) {
        const eventPayload = {
            ...event,
            timestamp: new Date(),
        };
        this.eventSubject.next(eventPayload);
    }
    closeSseEvents() {
        this.eventSubject.complete();
    }
    async findAll(projectId) {
        return this.dataRequestRepository.find({
            where: { projectId },
        });
    }
    async findProject(dataRequestId) {
        const dataRequest = await this.dataRequestRepository.findOne({
            where: { id: dataRequestId },
            relations: {
                project: true,
                disclosureRequirement: true,
            },
        });
        return dataRequest;
    }
    async findById(dataRequestId) {
        const dataRequest = await this.dataRequestRepository.findOne({
            where: { id: dataRequestId },
        });
        return dataRequest;
    }
    async findRelatedData(dataRequestId, userId) {
        const dataRequest = await this.dataRequestRepository.findOne({
            where: { id: dataRequestId },
            relations: {
                responsiblePerson: true,
                approver: true,
                disclosureRequirement: true,
                dataRequestGenerations: true,
                comments: {
                    user: true,
                },
                commentGenerations: true,
            },
            order: {
                comments: {
                    createdAt: 'ASC',
                },
            },
        });
        if (!dataRequest) {
            throw new common_1.NotFoundException(`DataRequest with ID ${dataRequest} not found`);
        }
        const datapointRequests = await this.datapointRequestService.findAllDataPointRequests(dataRequest.id, userId);
        dataRequest.datapointRequests = datapointRequests;
        const evaluatedStatus = await this.dataRequestStatusProcessor(dataRequest);
        if (dataRequest.status !== evaluatedStatus) {
            await this.update({
                dataRequestId,
                updateDataRequestPayload: { status: evaluatedStatus },
            });
            dataRequest.status = evaluatedStatus;
        }
        return dataRequest;
    }
    async update({ dataRequestId, updateDataRequestPayload, userId, workspaceId, event = 'data_request_updated', }) {
        const dataRequest = await this.findById(dataRequestId);
        if (!dataRequest) {
            throw new common_1.NotFoundException(`Data Request not found`);
        }
        const definedPayload = Object.fromEntries(Object.entries(updateDataRequestPayload).filter(([_, value]) => value !== undefined));
        await this.dataRequestRepository.update(dataRequestId, definedPayload);
        const updated = await this.findById(dataRequestId);
        if (userId && workspaceId) {
            await this.workspaceService.storeActionHistory({
                event: event,
                ref: dataRequestId,
                workspaceId: workspaceId,
                versionData: {
                    event: event,
                    doneBy: userId,
                    data: updated,
                },
            });
        }
        return updated;
    }
    async dataRequestStatusProcessor(dataRequest) {
        const { datapointRequests, content, approvedBy } = dataRequest;
        if (dataRequest.status === data_request_entity_1.DataRequestStatus.ApprovedAnswer) {
            return data_request_entity_1.DataRequestStatus.ApprovedAnswer;
        }
        const validateDPForNotReported = datapointRequests.every((datapoint) => datapoint.status === datapoint_request_entity_1.DatapointRequestStatus.NotAnswered);
        if (validateDPForNotReported ||
            dataRequest.status === data_request_entity_1.DataRequestStatus.NotAnswered) {
            return data_request_entity_1.DataRequestStatus.NotAnswered;
        }
        datapointRequests.filter((datapoint) => {
            return (datapoint.status !== datapoint_request_entity_1.DatapointRequestStatus.NotAnswered &&
                !datapoint.content &&
                !datapoint.documentChunkCount);
        });
        if (datapointRequests.length === 0) {
            return data_request_entity_1.DataRequestStatus.NoData;
        }
        const validateDPForIncompleteData = datapointRequests.some((datapoint) => datapoint.status !== datapoint_request_entity_1.DatapointRequestStatus.CompleteData &&
            datapoint.status !== datapoint_request_entity_1.DatapointRequestStatus.NotAnswered);
        if (validateDPForIncompleteData) {
            return data_request_entity_1.DataRequestStatus.IncompleteData;
        }
        const validateDPForCompleteData = datapointRequests.every((datapoint) => datapoint.status === datapoint_request_entity_1.DatapointRequestStatus.CompleteData);
        if (validateDPForCompleteData &&
            (!content || content.trim() === '' || content.trim() === '<p></p>')) {
            return data_request_entity_1.DataRequestStatus.CompleteData;
        }
        if (approvedBy === null) {
            return data_request_entity_1.DataRequestStatus.Draft;
        }
        return data_request_entity_1.DataRequestStatus.ApprovedAnswer;
    }
    async reviewDataRequestContentWithAI({ dataRequestId, userId, workspaceId, }) {
        const dataRequest = await this.dataRequestRepository.findOne({
            where: { id: dataRequestId, status: (0, typeorm_2.Not)(data_request_entity_1.DataRequestStatus.NotAnswered) },
            relations: [
                'project',
                'comments',
                'disclosureRequirement',
                'datapointRequests.esrsDatapoint',
            ],
        });
        const isAiEvaluator = await this.userService.userHasRequiredRole(userId, [
            user_workspace_entity_1.Role.SuperAdmin,
        ]);
        this.logger.log(`Start Gap Analysis for dataRequest ${dataRequest.id}`);
        const dataRequestGapAnalysisChatCompletion = [
            {
                role: 'system',
                content: this.promptService.generateDataRequestGapAnalysisSystemPrompt({
                    esrsDisclosureRequirement: dataRequest.disclosureRequirement,
                    generationLanguage: dataRequest.project.primaryContentLanguage,
                }),
            },
            {
                role: 'system',
                content: this.promptService.generateDataRequestFullLawTextContextForReportedDatapoints(dataRequest),
            },
            {
                role: 'user',
                content: this.promptService.generateDataRequestGapAnalysisContentContext(dataRequest),
            },
        ];
        const reviewedGapAnalysisCompletionResponse = await this.llmRateLimitService.handleRequest({
            model: constants_1.LLM_MODELS['gpt-4o'],
            messages: dataRequestGapAnalysisChatCompletion,
            json: true,
            temperature: 0.3,
        });
        if (reviewedGapAnalysisCompletionResponse.status === 400) {
            throw new Error(reviewedGapAnalysisCompletionResponse.response);
        }
        const reviewedGapAnalysis = reviewedGapAnalysisCompletionResponse.response;
        this.logger.log(`Gap Analysis Tokens: ${JSON.stringify(reviewedGapAnalysisCompletionResponse.token)}`);
        const globalAIUser = await this.userService.findGlobalGlacierAIUser();
        const comments = [];
        if (reviewedGapAnalysis.gapIdentified) {
            for (const gapJson of reviewedGapAnalysis.datapointGaps) {
                const commentHtml = "<h2>" + gapJson.datapoint + "</h2>" +
                    "<p><strong>Gap:</strong> " + gapJson.gap + "</p>" +
                    "<p><strong>Recommended Actions:</strong></p>" +
                    "<ul>" +
                    gapJson.actions.map(function (action) {
                        return "<li>" + action + "</li>";
                    }).join('') +
                    "</ul>" +
                    "<p><strong>Example Text:</strong>" + gapJson.exampleText + "</p>";
                const comment = await this.projectService.addComment({
                    commentableId: dataRequest.id,
                    commentableType: comment_entity_1.CommentType.DataRequest,
                    userId: globalAIUser.id,
                    comment: commentHtml,
                    workspaceId,
                    evaluationLot: isAiEvaluator,
                });
                comments.push(comment);
            }
        }
        return comments;
    }
    async validateDataRequestGenerationRightsOrFail({ dataRequest, userId, }) {
        if (!dataRequest.disclosureRequirement.publicAccess) {
            await this.userService.userHasRequiredRoleOrFail({
                userOrId: userId,
                role: [user_workspace_entity_1.Role.SuperAdmin, user_workspace_entity_1.Role.AiContributor],
                message: 'Data request generation is not enabled for this request.',
            });
        }
        return true;
    }
    async generateDataRequestTextContentWithAI({ dataRequestId, userId, workspaceId, additionalData, }) {
        const dataRequest = await this.dataRequestRepository.findOne({
            where: { id: dataRequestId },
            relations: [
                'project',
                'disclosureRequirement',
                'datapointRequests.esrsDatapoint',
            ],
        });
        const cleanCustomUserRemark = additionalData?.additionalReportTextGenerationRules.trim() !== ''
            ? additionalData.additionalReportTextGenerationRules
            : null;
        if (cleanCustomUserRemark) {
            await this.dataRequestRepository.update({ id: dataRequestId }, {
                customUserRemark: cleanCustomUserRemark,
            });
        }
        this.logger.log(`Start Generating dataRequest ${dataRequest.id} - ${dataRequest.disclosureRequirement.dr} with AI`);
        const dataRequestGenerationChatCompletion = [
            {
                role: 'system',
                content: this.promptService.generateDataRequestContentGenerationSystemPrompt1(dataRequest.disclosureRequirement),
            },
            {
                role: 'system',
                content: this.promptService.generateDataRequestFullLawTextContextForReportedDatapoints(dataRequest),
            },
            {
                role: 'system',
                content: this.promptService.generateDataRequestContextFromDatapoints(dataRequest),
            },
            {
                role: 'system',
                content: this.promptService.generateDataRequestContentExampleIntegrated(),
            },
            {
                role: 'system',
                content: this.promptService.generateDataRequestContentGenerationSystemPrompt2({
                    disclosureRequirement: dataRequest.disclosureRequirement,
                    generationLanguage: dataRequest.project.primaryContentLanguage,
                    reportTextGenerationRules: dataRequest.project.reportTextGenerationRules,
                    customUserRemark: cleanCustomUserRemark,
                    enableDatapointTags: additionalData.enableDatapointTags,
                }),
            },
        ];
        if (additionalData.useExistingReportText) {
            dataRequestGenerationChatCompletion.push({
                role: 'user',
                content: `Following is the existing report text that was previously generated: ${dataRequest.content}`,
            });
        }
        const dataRequestGenerationChatCompletionResponse = await this.llmRateLimitService.handleRequest({
            model: constants_1.LLM_MODELS['gpt-4o'],
            messages: dataRequestGenerationChatCompletion,
            json: true,
            temperature: 0.5,
        });
        if (dataRequestGenerationChatCompletionResponse.status === 400) {
            throw new Error(dataRequestGenerationChatCompletionResponse.response);
        }
        const isAIEvaluator = await this.userService.userHasRequiredRole(userId, [
            user_workspace_entity_1.Role.SuperAdmin,
        ]);
        let generatedContent = dataRequestGenerationChatCompletionResponse.response;
        generatedContent = (0, llm_response_util_1.trimHtmlPreAndPostfix)(generatedContent);
        const dataGenerationResponse = {
            id: dataRequest.id,
            content: generatedContent,
        };
        if (isAIEvaluator) {
            const dataGeneration = await this.dataRequestGenerationRepository.create({
                data: { content: generatedContent },
                dataRequest,
                evaluatorId: userId,
            });
            await this.dataRequestGenerationRepository.save(dataGeneration);
            dataGenerationResponse.id = dataGeneration.id;
        }
        else {
            dataRequest.content = generatedContent;
            await this.dataRequestRepository.save(dataRequest);
        }
        this.logger.log(`Finished Generating dataRequest ${dataRequest.id} - ${dataRequest.disclosureRequirement.dr} with AI`);
        const globalAIUser = await this.userService.findGlobalGlacierAIUser();
        await this.workspaceService.storeActionHistory({
            event: 'data_request_ai_generated',
            ref: dataRequestId,
            workspaceId: workspaceId,
            versionData: {
                event: 'data_request_ai_generated',
                doneBy: globalAIUser.id,
                issuedBy: userId,
                data: dataRequest,
            },
        });
        return dataGenerationResponse;
    }
    async findDatapointById(datapointRequestId) {
        return await this.datapointRequestService.findDatapointWithGenerationsById(datapointRequestId);
    }
    async generateAllDatapointForDataRequest({ dataRequestId, userId, workspaceId, }) {
        let datapointRequestToGenerate = await this.datapointRequestService.findDatapointRequestsByStatus({
            dataRequestId,
            status: [datapoint_request_entity_1.DatapointRequestStatus.NoData],
        });
        datapointRequestToGenerate =
            datapointRequestToGenerate.filter((datapointRequest) => datapointRequest.datapointDocumentChunkMap.filter((mp) => mp.active)
                .length > 0) || [];
        for (const datapointRequest of datapointRequestToGenerate) {
            await this.datapointDataRequestSharedService.addDatapointToGenerationQueue({
                datapointRequest,
                userId,
                workspaceId,
                useExistingReportTextForReference: false,
            });
        }
    }
    async reviewAllDatapointForDataRequest({ dataRequestId, userId, workspaceId, }) {
        const datapointRequestToReview = await this.datapointRequestService.findDatapointRequestsExcludingStatus({
            dataRequestId,
            status: [
                datapoint_request_entity_1.DatapointRequestStatus.NoData,
                datapoint_request_entity_1.DatapointRequestStatus.NotAnswered,
            ],
        });
        for (const datapointRequest of datapointRequestToReview) {
            await this.datapointDataRequestSharedService.addDatapointToReviewQueue({
                datapointRequest,
                userId,
                workspaceId,
            });
        }
    }
    async setDatapointQueueStatusToNull(id) {
        await this.datapointRequestService.updateQueueStatus({
            datapointRequestId: id,
            queueStatus: null,
        });
    }
    async getGenerations(dataRequestId) {
        return this.dataRequestGenerationRepository.find({
            where: { dataRequest: { id: dataRequestId } },
        });
    }
    async updateGenerationStatus({ dataRequestGenerationId, status, userId, }) {
        try {
            await this.dataRequestGenerationRepository.update({
                id: dataRequestGenerationId,
            }, { status, evaluatorId: userId, evaluatedAt: new Date() });
            if (status === datarequest_generation_entity_1.dataRequestGenerationStatus.Approved) {
                return await this.dataRequestGenerationRepository.findOne({
                    where: { id: dataRequestGenerationId },
                    relations: ['dataRequest'],
                });
            }
            return null;
        }
        catch (err) {
            console.log(err);
            throw new Error(`Error while updating generation status: ${err.message}`);
        }
    }
};
exports.DataRequestService = DataRequestService;
exports.DataRequestService = DataRequestService = DataRequestService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(data_request_entity_1.DataRequest)),
    __param(8, (0, typeorm_1.InjectRepository)(datarequest_generation_entity_1.DataRequestGeneration)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        prompts_service_1.PromptService,
        project_service_1.ProjectService,
        users_service_1.UsersService,
        workspace_service_1.WorkspaceService,
        datapoint_request_service_1.DatapointRequestService,
        llm_rate_limiter_service_1.LlmRateLimiterService,
        shared_datapoint_datarequest_service_1.DatapointDataRequestSharedService,
        typeorm_2.Repository])
], DataRequestService);
//# sourceMappingURL=data-request.service.js.map