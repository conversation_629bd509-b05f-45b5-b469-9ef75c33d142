import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DatapointRequestService } from './datapoint-request.service';
import { Workspace } from '../workspace/entities/workspace.entity';
import { DatapointRequestGuard } from './datapoint-request.guard';
import { DatapointRequestController } from './datapoint-request.controller';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { ESRSDatapoint } from './entities/esrs-datapoint.entity';
import { DatapointDocumentChunk } from 'src/datapoint-document-chunk/entities/datapoint-document-chunk.entity';
import { DataRequestModule } from 'src/data-request/data-request.module';
import { PromptModule } from 'src/prompts/prompts.module';
import { ProjectModule } from 'src/project/project.module';
import { UsersModule } from 'src/users/users.module';
import { WorkspaceModule } from 'src/workspace/workspace.module';
import { DatapointGeneration } from './entities/datapoint-generation.entity';
import { ESRSTopicDatapoint } from 'src/knowledge-base/entities/esrs-topic-datapoint.entity';
import { DatapointDataRequestSharedService } from 'src/shared/shared-datapoint-datarequest.service';
import { JobProcessor } from 'src/types/jobs';
import { BullAdapter } from '@bull-board/api/bullAdapter';
import { BullModule } from '@nestjs/bull';
import { BullBoardModule } from '@bull-board/nestjs';
import { LlmRateLimiterModule } from 'src/llm-rate-limiter/llm-rate-limiter.module';
import { SupabaseAuthModule } from 'src/auth/supabase/supabase.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ESRSDatapoint,
      ESRSTopicDatapoint,
      DatapointRequest,
      Workspace,
      DatapointDocumentChunk,
      DatapointGeneration,
    ]),
    SupabaseAuthModule,
    forwardRef(() => DataRequestModule), // this is a circular dependency
    LlmRateLimiterModule,
    PromptModule,
    ProjectModule,
    UsersModule,
    WorkspaceModule,
    BullModule.registerQueue({ name: JobProcessor.DatapointGeneration }),
    BullModule.registerQueue({ name: JobProcessor.DatapointReview }),
    BullBoardModule.forFeature({
      name: JobProcessor.DatapointGeneration,
      adapter: BullAdapter,
    }),
    BullBoardModule.forFeature({
      name: JobProcessor.DatapointReview,
      adapter: BullAdapter,
    }),
  ],
  providers: [
    DatapointRequestService,
    DatapointDataRequestSharedService,
    DatapointRequestGuard,
  ],
  exports: [DatapointRequestService],
  controllers: [DatapointRequestController],
})
export class DatapointRequestModule {}
