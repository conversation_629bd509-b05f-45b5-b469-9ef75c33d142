"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const supabase_js_1 = require("@supabase/supabase-js");
const user_workspace_entity_1 = require("../../users/entities/user-workspace.entity");
let SupabaseService = class SupabaseService {
    constructor(configService) {
        this.configService = configService;
        this.supabase = (0, supabase_js_1.createClient)(this.configService.get('SUPABASE_APP_URL'), this.configService.get('SUPABASE_SERVICE_KEY'));
    }
    getClient() {
        return this.supabase;
    }
    async findByAuthId(id) {
        const { data, error } = await this.supabase
            .from('user')
            .select(`
        *,
        user_workspace(*)
      `)
            .eq('auth_id', id)
            .single();
        if (error) {
            console.error('Error fetching user:', error);
            return undefined;
        }
        return data;
    }
    async findById(id) {
        const { data, error } = await this.supabase
            .from('user')
            .select(`
        *,
        user_workspace(
          "workspaceId",
          role
        )
      `)
            .eq('id', id)
            .single();
        if (error) {
            console.error('Error fetching user:', error);
            return undefined;
        }
        return data;
    }
    async getUserRole(userId, workspaceId) {
        try {
            const { data, error } = await this.supabase
                .from('user_workspace')
                .select('role')
                .eq('userId', userId)
                .eq('workspaceId', workspaceId)
                .single();
            if (error || !data) {
                return null;
            }
            return data.role;
        }
        catch (error) {
            console.error('Error getting user role:', error);
            return null;
        }
    }
    async verifyToken(token) {
        try {
            const { data, error } = await this.supabase.auth.getUser(token);
            if (error || !data.user) {
                return null;
            }
            return data.user;
        }
        catch (error) {
            console.error('Error verifying token:', error);
            return null;
        }
    }
    async migrateUsers() {
        try {
            const { data: existingUsers, error } = await this.supabase
                .from('user')
                .select(`
          *,
          user_workspace(*)
        `)
                .is('auth_id', null);
            if (error) {
                console.error('Error fetching users:', error);
                return;
            }
            console.log(`Found ${existingUsers.length} users to migrate`);
            let successCount = 0;
            const errors = [];
            for (const user of existingUsers) {
                try {
                    const { data: authUser, error: authError } = await this.supabase.auth.admin.createUser({
                        email: user.email,
                        password: 'changeme',
                        email_confirm: true,
                        user_metadata: {
                            name: user.name,
                            workspaces: user.user_workspace.map((uw) => ({
                                workspaceId: uw.workspaceId,
                                role: uw.role,
                            })),
                        },
                    });
                    if (authError)
                        throw new Error(`Auth error for ${user.email}: ${authError.message}`);
                    const { error: updateError } = await this.supabase
                        .from('user')
                        .update({ auth_id: authUser.user.id })
                        .eq('id', user.id);
                    if (updateError)
                        throw new Error(`Error updating auth_id for ${user.email}: ${updateError.message}`);
                    successCount++;
                    console.log(`Successfully migrated user ${user.email}`);
                }
                catch (error) {
                    console.error(`Error migrating user ${user.email}:`, error);
                    errors.push({ email: user.email, error: error.message });
                }
            }
            console.log(`Migration complete. Success: ${successCount}, Errors: ${errors.length}`);
            if (errors.length > 0) {
                console.error('Migration errors:', errors);
            }
        }
        catch (error) {
            console.error('Migration script error:', error);
        }
    }
    async createUser({ email, name, password, options, }) {
        try {
            let workspaceId = options?.workspaceId;
            const role = options?.role || user_workspace_entity_1.Role.Contributor;
            if (!workspaceId && options?.workspaceName) {
                const { data: workspace, error: workspaceError } = await this.supabase
                    .from('workspace')
                    .insert({ name: options.workspaceName })
                    .select('id')
                    .single();
                if (workspaceError) {
                    console.error('Error creating workspace:', workspaceError);
                    return null;
                }
                workspaceId = workspace.id;
                console.log(`Created workspace with ID: ${workspaceId}`);
            }
            const { data: userData, error: userError } = await this.supabase
                .from('user')
                .insert({
                email,
                name,
            })
                .select('*')
                .single();
            if (userError) {
                console.error('Error creating user record:', userError);
                return null;
            }
            const userMetadata = { name, workspaces: [{ workspaceId, role }] };
            const { data: authUser, error: authError } = await this.supabase.auth.admin.createUser({
                email,
                password,
                email_confirm: true,
                user_metadata: userMetadata,
            });
            if (authError) {
                console.error('Error creating auth user:', authError);
                const { error: rollbackError } = await this.supabase
                    .from('user')
                    .delete()
                    .eq('id', userData.id);
                return null;
            }
            await this.supabase
                .from('user')
                .update({ auth_id: authUser.user.id })
                .eq('id', userData.id);
            if (workspaceId) {
                const { error: mappingError } = await this.supabase
                    .from('user_workspace')
                    .insert({
                    userId: userData.id,
                    workspaceId,
                    role,
                });
                if (mappingError) {
                    console.error('Error mapping user to workspace:', mappingError);
                }
            }
            const user = await this.findById(userData.id);
            if (!user) {
                console.error('Error fetching created user');
                return null;
            }
            return { user, workspaceId };
        }
        catch (error) {
            console.error('Error in createUser:', error);
            return null;
        }
    }
};
exports.SupabaseService = SupabaseService;
exports.SupabaseService = SupabaseService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], SupabaseService);
//# sourceMappingURL=supabase.service.js.map