{"name": "climate-assistant", "version": "0.1.01", "lockfileVersion": 3, "requires": true, "packages": {"": {"version": "0.1.01", "dependencies": {"react-multi-email": "^1.0.25"}, "devDependencies": {"husky": "^9.1.6", "prettier": "^3.3.3"}}, "node_modules/husky": {"version": "9.1.7", "resolved": "https://registry.npmjs.org/husky/-/husky-9.1.7.tgz", "integrity": "sha512-5gs5ytaNjBrh5Ow3zrvdUUY+0VxIuWVL4i9irt6friV+BqdCfmV11CQTWMiBYWHbXhco+J1kHfTOUkePhCDvMA==", "dev": true, "license": "MIT", "bin": {"husky": "bin.js"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/typicode"}}, "node_modules/prettier": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/prettier/-/prettier-3.6.2.tgz", "integrity": "sha512-I7AIg5boAr5R0FFtJ6rCfD+LFsWHp81dolrFD8S79U9tb8Az2nGrJncnMSnys+bpQJfRUzqs9hnA81OAA3hCuQ==", "dev": true, "license": "MIT", "bin": {"prettier": "bin/prettier.cjs"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/react-multi-email": {"version": "1.0.25", "resolved": "https://registry.npmjs.org/react-multi-email/-/react-multi-email-1.0.25.tgz", "integrity": "sha512-Wmv28FvIk4nWgdpHzlIPonY4iSs7bPV35+fAiWYzSBhTo+vhXfglEhjY1WnjHQINW/Pibu2xlb/q1heVuytQHQ==", "license": "MIT", "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}}}}