import * as React from 'react';
import { CheckIcon, ChevronDownIcon } from 'lucide-react';
import { Column } from '@tanstack/react-table';
import { useSearchParams, useNavigate } from 'react-router-dom';

import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '../ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Separator } from '../ui/separator';

import { cn } from '@/lib/utils';

interface DataTableFacetedFilterProps<TData, TValue> {
  column?: Column<TData, TValue>;
  title?: string;
  hidden?: boolean;
  options: {
    label: string;
    value: string;
    icon?: React.ComponentType<{ className?: string }>;
    color?: string;
  }[];
}

export function DataTableFacetedFilter<TData, TValue>({
  column,
  title,
  hidden,
  options,
}: DataTableFacetedFilterProps<TData, TValue>) {
  const facets = column?.getFacetedUniqueValues();

  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  // State to manage selected values
  const [selectedValues, setSelectedValues] = React.useState<Set<string>>(
    new Set()
  );

  // On component mount, read query parameters and set initial filter
  React.useEffect(() => {
    if (column) {
      const paramValue = searchParams.get(column.id);

      if (paramValue) {
        const values = paramValue.split(',');
        setSelectedValues(new Set(values));
        column.setFilterValue(values);
      } else {
        const filterValue = column.getFilterValue() as string[] | undefined;
        if (filterValue) {
          setSelectedValues(new Set(filterValue));
        } else {
          // Set initial filter to exclude "Not Reported" status
          if (column.id === 'status') {
            const allStatusesExceptNotReported = options
              .filter((option) => option.value !== 'not_answered')
              .map((option) => option.value);
            setSelectedValues(new Set(allStatusesExceptNotReported));
            column.setFilterValue(allStatusesExceptNotReported);
          } else {
            setSelectedValues(new Set());
          }
        }
      }
    }
    // We include column and searchParams in the dependency array to ensure it updates when they change
  }, [column, searchParams]);

  // Update URL query parameters when selected values change
  React.useEffect(() => {
    if (column) {
      const params = new URLSearchParams(searchParams);

      if (selectedValues.size > 0) {
        params.set(column.id, Array.from(selectedValues).join(','));
      } else {
        params.delete(column.id);
      }

      // Using navigate to update the URL without reloading the page
      navigate({ search: params.toString() }, { replace: true });
    }
  }, [selectedValues, column, navigate, searchParams]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            'h-8 border-dashed flex gap-2 text-glacier-bluedark font-thin',
            hidden && 'hidden'
          )}
        >
          {title}
          {selectedValues.size > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <Badge
                variant="secondary"
                className="rounded-sm px-1 font-normal lg:hidden"
              >
                {selectedValues.size}
              </Badge>
              <div className="hidden space-x-1 lg:flex">
                {selectedValues.size > 2 ? (
                  <Badge
                    variant="secondary"
                    className="rounded-sm px-1 font-normal"
                  >
                    {selectedValues.size} selected
                  </Badge>
                ) : (
                  options
                    .filter((option) => selectedValues.has(option.value))
                    .map((option) => (
                      <Badge
                        variant="secondary"
                        key={option.value}
                        className="rounded-sm px-1 font-normal"
                      >
                        {option.label}
                      </Badge>
                    ))
                )}
              </div>
            </>
          )}
          <ChevronDownIcon className="mr-2 h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-fit p-0" align="start">
        <Command>
          <CommandInput placeholder={title} />
          <CommandList>
            <CommandEmpty>No results found.</CommandEmpty>
            <CommandGroup>
              {options.map((option) => {
                const isSelected = selectedValues.has(option.value);
                return (
                  <CommandItem
                    key={option.value}
                    onSelect={() => {
                      const newSelectedValues = new Set(selectedValues);
                      if (isSelected) {
                        newSelectedValues.delete(option.value);
                      } else {
                        newSelectedValues.add(option.value);
                      }
                      setSelectedValues(newSelectedValues);
                      column?.setFilterValue(
                        newSelectedValues.size > 0
                          ? Array.from(newSelectedValues)
                          : undefined
                      );
                    }}
                  >
                    <div
                      className={cn(
                        'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                        isSelected
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50 [&_svg]:invisible'
                      )}
                    >
                      <CheckIcon className={cn('h-4 w-4')} />
                    </div>
                    {option.icon && (
                      <option.icon className="mr-2 h-4 w-4 text-muted-foreground" />
                    )}
                    <span>{option.label}</span>
                    {facets?.get(option.value) && (
                      <span className="ml-auto px-5 flex h-4 w-4 items-center justify-center font-mono text-xs">
                        {facets.get(option.value)}
                      </span>
                    )}
                  </CommandItem>
                );
              })}
            </CommandGroup>
            {selectedValues.size > 0 && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={() => {
                      setSelectedValues(new Set());
                      column?.setFilterValue(undefined);
                    }}
                    className="justify-center text-center"
                  >
                    Clear filters
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
