"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LlmRateLimiterModule = void 0;
const common_1 = require("@nestjs/common");
const llm_rate_limiter_service_1 = require("./llm-rate-limiter.service");
const bull_1 = require("@nestjs/bull");
const jobs_1 = require("../types/jobs");
const nestjs_1 = require("@bull-board/nestjs");
const bullAdapter_1 = require("@bull-board/api/bullAdapter");
const llm_module_1 = require("../llm/llm.module");
let LlmRateLimiterModule = class LlmRateLimiterModule {
};
exports.LlmRateLimiterModule = LlmRateLimiterModule;
exports.LlmRateLimiterModule = LlmRateLimiterModule = __decorate([
    (0, common_1.Module)({
        imports: [
            llm_module_1.LlmModule,
            bull_1.BullModule.registerQueue({ name: jobs_1.JobProcessor.LlmRequest }),
            nestjs_1.BullBoardModule.forFeature({
                name: jobs_1.JobProcessor.LlmRequest,
                adapter: bullAdapter_1.BullAdapter,
            }),
        ],
        providers: [llm_rate_limiter_service_1.LlmRateLimiterService],
        exports: [llm_rate_limiter_service_1.LlmRateLimiterService],
        controllers: [],
    })
], LlmRateLimiterModule);
//# sourceMappingURL=llm-rate-limiter.module.js.map