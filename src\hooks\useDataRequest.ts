import { useQuery, useQueryClient } from '@tanstack/react-query';

import { DataRequestStatus, DatapointRequestStatus } from '@/types';
import {
  generateDatapointForDataRequest,
  reviewDatapointForDataRequest,
  fetchDataRequestById,
  updateDataRequestDueDate,
  updateDataRequestPayload,
} from '@/api/data-request/data-request.api';

export function useDataRequest(id: string) {
  const queryClient = useQueryClient();
  const { data: dataRequest, isFetching } = useQuery({
    queryKey: ['dataRequest', id],
    queryFn: () => fetchDataRequestById(id),
  });

  function refetchDataRequest() {
    //Invalidate and refetch the data from react query
    queryClient.invalidateQueries({ queryKey: ['dataRequest', id] });
  }

  const updateResponsiblePerson = (responsiblePersonId: string) =>
    updateDataRequestPayload(dataRequest!.id, {
      responsiblePersonId,
    });

  const updateDueDate = (dueDate: Date) =>
    updateDataRequestDueDate(dataRequest!.id, dueDate);

  async function handleReportDataRequestChanged(checked: boolean) {
    if (dataRequest) {
      let updatedStatus;
      if (checked === false) {
        updatedStatus = DataRequestStatus.NotAnswered;
        refetchDataRequest();
      } else {
        updatedStatus = DataRequestStatus.IncompleteData;
        refetchDataRequest();
      }
      await updateDataRequestPayload(dataRequest.id, {
        status: updatedStatus,
      });
    }
  }

  const progress = () => {
    const validDatapointRequests = dataRequest!.datapointRequests.filter(
      (datapoint) => datapoint.status !== DatapointRequestStatus.NotAnswered
    );
    const totalDatapoints = validDatapointRequests.length;
    const initialValue = 0;
    const progress = validDatapointRequests.reduce((accumulator, datapoint) => {
      return (
        accumulator +
        (datapoint.status === DatapointRequestStatus.CompleteData ? 1 : 0)
      );
    }, initialValue);
    return `${progress}/${totalDatapoints}`;
  };

  async function handleBulkGenerateDatapoints() {
    await generateDatapointForDataRequest({
      dataRequestId: dataRequest!.id,
    });
    refetchDataRequest();
  }

  async function handleBulkReviewDatapoints() {
    await reviewDatapointForDataRequest({
      dataRequestId: dataRequest!.id,
    });
    refetchDataRequest();
  }

  return {
    dataRequest,
    isFetching,
    refetchDataRequest,
    updateResponsiblePerson,
    updateDueDate,
    handleReportDataRequestChanged,
    progress,
    handleBulkGenerateDatapoints,
    handleBulkReviewDatapoints,
  };
}
