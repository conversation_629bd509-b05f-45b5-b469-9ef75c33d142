import {
  IsEmail,
  <PERSON><PERSON>num,
  IsNot<PERSON>mpty,
  Is<PERSON><PERSON>al,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  Min<PERSON>eng<PERSON>,
} from 'class-validator';
import { Role } from 'src/users/entities/user-workspace.entity';
export class LoginDto {
  @IsEmail({}, { message: 'Keine gültige Email' })
  email: string;

  @IsNotEmpty({ message: 'Passwort darf nicht leer sein' })
  password: string;
}

export interface LoginResponseSuccess {
  message: string;
}

export interface LogoutResponseSuccess {
  message: string;
}

export class RegisterWithCompanyDto {
  @IsEmail({}, { message: 'Email Id is not valid' })
  email: string;

  @IsNotEmpty({ message: 'Password cannot be empty' })
  password: string;

  @IsString()
  @IsNotEmpty({ message: 'Company name cannot be empty' })
  companyName: string;

  @IsString()
  @IsOptional()
  projectType: string;

  @IsEnum(Role, { message: 'Role must be a valid enum value' })
  @IsOptional()
  role: Role;
}

export class PasswordResetRequestDto {
  @IsUUID('4', { message: 'Invalid user ID format' })
  @IsNotEmpty({ message: 'User ID cannot be empty' })
  userId: string;
}
export class CreateUserWithCompanyAndWorkspaceDto {
  @IsEmail({}, { message: 'Invalid email address' })
  email: string;

  @IsNotEmpty({ message: 'Password cannot be empty' })
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  password: string;

  @IsString()
  @IsNotEmpty({ message: 'Company name cannot be empty' })
  companyName: string;
}
