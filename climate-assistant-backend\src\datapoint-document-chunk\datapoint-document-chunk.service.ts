import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, QueryFailedError, Repository } from 'typeorm';
import throat from 'throat';

import { ChatCompletionMessageParam } from 'openai/resources/chat';
import { ChunkMatches, ESRSDatPointLinkResponse, Match } from 'src/types';
import { PromptService } from 'src/prompts/prompts.service';
import { DocumentChunk } from 'src/document/entities/document-chunk.entity';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { DatapointDocumentChunk } from './entities/datapoint-document-chunk.entity';
import {
  Document,
  DocumentStatus,
} from 'src/document/entities/document.entity';
import { ESRSDisclosureRequirement } from 'src/knowledge-base/entities/esrs-disclosure-requirement.entity';
import { UsersService } from 'src/users/users.service';
import { LLM_MODELS } from 'src/constants';
import { LlmRateLimiterService } from 'src/llm-rate-limiter/llm-rate-limiter.service';

@Injectable()
export class DatapointDocumentChunkService {
  constructor(
    private readonly llmRateLimitService: LlmRateLimiterService,
    @InjectDataSource() private dataSource: DataSource,
    private readonly promptService: PromptService,
    @InjectRepository(DocumentChunk)
    private readonly documentChunkRepository: Repository<DocumentChunk>,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
    @InjectRepository(DatapointRequest)
    private readonly datapointRequestRepository: Repository<DatapointRequest>,
    @InjectRepository(DatapointDocumentChunk)
    private readonly datapointDocumentChunkRepository: Repository<DatapointDocumentChunk>,
    @InjectRepository(ESRSDisclosureRequirement)
    private readonly esrsDisclosureRequirementRepository: Repository<ESRSDisclosureRequirement>,
    private readonly userService: UsersService
  ) {}

  private readonly logger = new Logger(DatapointDocumentChunkService.name);
  private readonly limit = throat(4); // Parallelization: Limit the number of concurrent promises

  async linkDocumentChunksToDatapoints(
    documentId: string,
    userId: string,
    maxNumberOfChunks?: number
  ): Promise<void> {
    this.logger.log(
      `Start linking Datapoints to Chunks for DocumentId ${documentId}`
    );
    const document: Document = await this.documentRepository.findOne({
      where: { id: documentId },
      relations: {
        workspace: {
          projects: true,
        },
      },
    });

    // TODO: create relation between document and project
    const projectId = document.workspace?.projects[0]?.id;
    if (!projectId) {
      this.logger.error(`Document ${documentId} does not have a project`);
      return null;
    }
    this.logger.log(
      `Start linking Datapoints to Chunks for DocumentId ${documentId}, ProjectId ${projectId}`
    );
    document.status = DocumentStatus.LinkingData;
    this.documentRepository.save(document);

    try {
      const documentChunks: DocumentChunk[] =
        await this.documentChunkRepository.find({
          where: {
            documentId: documentId,
            //matchingsJson: IsNull()
          },
          order: {
            page: 'ASC',
          },
        });

      this.logger.log(
        `${documentId}: Found ${documentChunks.length} Document Chunks for Document`
      );
      const response: ESRSDatPointLinkResponse = {
        inputTokensUsed: 0,
        outputTokensUsed: 0,
        costForDocument: 0,
        totalLinksCreated: 0,
        timeToGenerate: '0s',
        numberOfChunks: documentChunks.length,
        chunks: [],
      };
      const startTime = Date.now();
      const createdAtBatch = new Date();

      const chunksToProcess = documentChunks.slice(
        0,
        maxNumberOfChunks || documentChunks.length
      );

      const esrsDisclosrueRequirements: ESRSDisclosureRequirement[] =
        await this.esrsDisclosureRequirementRepository.find({
          relations: ['esrsDatapoints'],
        });
      const globalUser = await this.userService.findGlobalGlacierAIUser();

      const promises = chunksToProcess.map((chunk) => {
        return this.limit(async () => {
          this.logger.log(
            `${documentId}: Linking chunk ${chunk.id}, Length: ${chunk.content.length}`
          );

          const chunkContent = chunk.content;

          // OG Topical Standards classification skipped
          // Directly classifying document chunks to disclosure requirements (DR)
          this.logger.log(
            `${documentId}: ${chunk.id} Start Disclosure Requirement Classification`
          );

          const disclosureRequirementClassificationChatCompletion: ChatCompletionMessageParam[] =
            [
              {
                role: 'system',
                content:
                  this.promptService.generateDisclosureRequirementClassificationPrompt1(),
              },
              {
                role: 'user',
                content: chunkContent,
              },
              {
                role: 'system',
                content:
                  this.promptService.generateDisclosureRequirementClassificationPrompt2(
                    esrsDisclosrueRequirements
                  ),
              },
            ];

          /**
           * Example of the response from OpenAI
           *
           * {
           *  "disclosureRequirementMatches": [ "E1-1", "E1-2", "E1-5", "E1.GOV3", "E1.SBM-3", "E1.IRO-1"]
           * }
           */
          const disclosureRequirementClassificationResponse =
            await this.llmRateLimitService.handleRequest({
              model: LLM_MODELS['gpt-4o'],
              messages: disclosureRequirementClassificationChatCompletion,
              json: true,
              temperature: 0,
            });

          //  If the chunk content contains text that does not meet the OpenAI prompt policy requirements ignore the error and continue.
          if (disclosureRequirementClassificationResponse.status === 400) {
            this.logger.error(
              `${documentId}: Chunk: ${chunk.id} DR Classification failed: ${disclosureRequirementClassificationResponse.response}`
            );
            return null;
          }

          response.inputTokensUsed +=
            disclosureRequirementClassificationResponse.token.prompt_tokens;
          response.outputTokensUsed +=
            disclosureRequirementClassificationResponse.token.completion_tokens;
          response.costForDocument +=
            disclosureRequirementClassificationResponse.token.total_cost;

          const disclosureRequirementMatches: string[] =
            disclosureRequirementClassificationResponse.response.disclosureRequirementMatches?.map(
              (dr) => {
                // clean up match ID to remove single or doulbe quotes and any spaces
                const cleanMatchedId = dr
                  .replace(/'/g, '')
                  .replace(/"/g, '')
                  .replace(/\s/g, '');
                return cleanMatchedId;
              }
            ) || [];

          const chunkMatches: ChunkMatches = {
            chunkContent: chunkContent,
            topicMatches: [],
            disclosureRequirementMatches: disclosureRequirementMatches,
            datapointMatches: [],
          };

          if (
            disclosureRequirementMatches &&
            disclosureRequirementMatches.length > 0
          ) {
            this.logger.log(
              `${documentId}: Chunk: ${chunk.id} DRsMatched: ${JSON.stringify(disclosureRequirementMatches.join(', '))}`
            );
            for (const dr of disclosureRequirementMatches) {
              await new Promise((resolve) => setTimeout(resolve, 500));
              this.logger.log(
                `${documentId}: Chunk: ${chunk.id} DR Matching started: ${dr}`
              );

              const esrsDisclosureRequirementMatch: ESRSDisclosureRequirement =
                esrsDisclosrueRequirements.find((eDr) => eDr.dr === dr);
              if (!esrsDisclosureRequirementMatch) {
                return this.logger.error(
                  `${documentId}: Chunk: ${chunk.id} DR: ${dr} not found in the workspace`
                );
              }
              const esrsDatapointsToMatch =
                esrsDisclosureRequirementMatch.esrsDatapoints;

              if (esrsDatapointsToMatch?.length > 0) {
                const datapointClassificationChatCompletion: ChatCompletionMessageParam[] =
                  [
                    {
                      role: 'system',
                      content:
                        this.promptService.generateDatapointClassificationPrompt1(),
                    },
                    {
                      role: 'user',
                      content: chunkContent,
                    },
                    {
                      role: 'user',
                      content:
                        this.promptService.generateSystemPromptForAllDatapoints(
                          esrsDatapointsToMatch
                        ),
                    },
                    {
                      role: 'system',
                      content:
                        this.promptService.generateDatapointClassificationPrompt2(
                          esrsDisclosureRequirementMatch
                        ),
                    },
                  ];

                /**
                 * Example of the response from OpenAI
                 *
                 *{
                 *  "matchedDatapoints": [
                 *    {
                 *      "matchedId": "E1-1_01",
                 *    },
                 *    {
                 *      "matchedId": "E1.MDR-T_01-13",
                 *      "mdrTitle": "<h2>Target on CO2 emission of supply chain</h2>"
                 *    },
                 *    {
                 *      "matchedId": "E1.IRO-1_01",
                 *    },
                 *  ]
                 *}
                 */
                const datapointClassificationResponse =
                  await this.llmRateLimitService.handleRequest({
                    model: LLM_MODELS['gpt-4o'],
                    messages: datapointClassificationChatCompletion,
                    json: true,
                    temperature: 0,
                  });

                //  If the chunk content contains text that does not meet the OpenAI prompt policy requirements ignore the error and continue.
                if (datapointClassificationResponse.status === 400) {
                  this.logger.error(
                    `${documentId}: Chunk: ${chunk.id} DR: ${dr} DP Classification failed: ${datapointClassificationResponse.response}`
                  );
                  return null;
                }

                response.inputTokensUsed +=
                  datapointClassificationResponse.token.prompt_tokens;
                response.outputTokensUsed +=
                  datapointClassificationResponse.token.completion_tokens;
                response.costForDocument +=
                  datapointClassificationResponse.token.total_cost;

                const datapointMatches: Match[] =
                  datapointClassificationResponse.response.matchedDatapoints;

                this.logger.log(
                  `${documentId}: Chunk: ${chunk.id} DR: ${dr} DPs matched: ${JSON.stringify(datapointMatches)}`
                );

                if (datapointMatches && datapointMatches.length > 0) {
                  chunkMatches.datapointMatches.push(...datapointMatches);
                  this.logger.log(
                    `${documentId}: Chunk: ${chunk.id} DR: ${dr} DPMatched: ${JSON.stringify(datapointMatches.map((dp) => dp.matchedId).join(', '))}`
                  );

                  for (const datapointMatch of datapointMatches) {
                    if (datapointMatch.matchedId) {
                      datapointMatch.executedClassificationPrompt = true;
                      const matchingDatapointRequest =
                        await this.datapointRequestRepository
                          .createQueryBuilder('datapointRequest')
                          .innerJoinAndSelect(
                            'datapointRequest.esrsDatapoint',
                            'esrsDatapoint'
                          )
                          .innerJoinAndSelect(
                            'datapointRequest.dataRequest',
                            'dataRequest'
                          )
                          .where('esrsDatapoint.datapointId = :datapointId', {
                            datapointId: datapointMatch.matchedId,
                          })
                          .andWhere('dataRequest.projectId = :projectId', {
                            projectId: projectId,
                          })
                          .getOne(); // Assuming you want a single result, use getOne(); for multiple, use getMany()

                      if (!matchingDatapointRequest) {
                        this.logger.error(
                          `${documentId}: Chunk: ${chunk.id} Datapoint: ${datapointMatch.matchedId} not found in the workspace`
                        );
                        return;
                      } else {
                        try {
                          const datapointDocumentChunk: DatapointDocumentChunk =
                            await this.datapointDocumentChunkRepository.save({
                              documentChunkId: chunk.id,
                              datapointRequestId: matchingDatapointRequest.id,
                              createdBy: globalUser ? globalUser.id : userId,
                              createdAt: createdAtBatch,
                            });
                          response.totalLinksCreated++;
                          this.logger.log(
                            `${documentId}: Chunk: ${chunk.id} DPR: ${matchingDatapointRequest.id} DPMatched: ${datapointMatch.matchedId}, Link-ID: ${datapointDocumentChunk.id}`
                          );
                        } catch (error) {
                          if (
                            error instanceof QueryFailedError &&
                            (error as any).code === '23505' // PostgreSQL duplicate key code
                          ) {
                            return null;
                          }
                          this.logger.error(error);
                        }

                        if (datapointMatch.mdrTitle) {
                          const cleanPolicy = datapointMatch.mdrTitle
                            .replace(/<\/?h6>/g, '')
                            .trim();

                          // Check if the policy is already in the customUserRemark
                          // If not, add it to the customUserRemark
                          // If the customUserRemark is empty, add the policy as the first line
                          if (
                            !matchingDatapointRequest.customUserRemark.includes(
                              cleanPolicy
                            )
                          ) {
                            // TODO: clean up this logic
                            if (
                              !matchingDatapointRequest.customUserRemark ||
                              matchingDatapointRequest.customUserRemark.length <
                                12
                            ) {
                              matchingDatapointRequest.customUserRemark =
                                'Write among others about the following Policies/Target(s)/Action(s):' +
                                `\n- ${cleanPolicy}`;
                            } else {
                              matchingDatapointRequest.customUserRemark += `\n- ${cleanPolicy}`;
                            }
                            await this.datapointRequestRepository.save(
                              matchingDatapointRequest
                            );
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
            chunk.matchingsJson = JSON.stringify(chunkMatches);
            this.documentChunkRepository.save(chunk);
            response.chunks.push(chunkMatches);
          }
        });
      });

      await Promise.all(promises);

      this.logger.log(
        `TOTAL TOKEN USAGE FOR EXTRACTION ${documentId}: inputTokensUsed ${response.inputTokensUsed}, outputTokensUsed: ${response.outputTokensUsed}, costFordocument: ${response.costForDocument}`
      );
      const endTime = Date.now();
      response.timeToGenerate = `${(endTime - startTime) / 1000}s`;
      document.status = DocumentStatus.LinkingDataFinished;
      this.documentRepository.save(document);
      this.logger.log(
        `DatapointLinking Finihsed ${documentId}: ${document.status}`
      );
    } catch (e) {
      // Check if document still exists in the database
      const documentStillExists = await this.documentRepository.findOne({
        where: { id: documentId },
      });

      if (!documentStillExists) {
        this.logger.log(
          `Document ${documentId} was deleted during linking process. Ending processing.`
        );
        return;
      }

      this.logger.error(e);
      document.status = DocumentStatus.ErrorProcessing;
      this.documentRepository.save(document);
    }
  }
}
