
import { LoaderCircle } from 'lucide-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import * as z from 'zod';

import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form.tsx';
import { Input } from '@/components/ui/input.tsx';
import { Button } from '@/components/ui/button.tsx';
import { useAuth } from '@/context/AuthContext';

interface ILoginFormProps {
  switchToForgotPassword: () => void;
}

const formSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z
    .string()
    .min(6, { message: 'Password must be at least 6 characters' }),
});

type LoginFormValues = z.infer<typeof formSchema>;

const LoginForm = ({ switchToForgotPassword }: ILoginFormProps) => {
  const { signIn } = useAuth();
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });


  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      setIsLoggingIn(true);
      setError(null);

      await signIn({
        email: values.email,
        password: values.password,
      });

      // Redirect to dashboard or homepage after successful login
      navigate('/projects');
    } catch (err: any) {
      console.error('Login error:', err);
      setError(err.message || 'Failed to login. Please check your credentials and try again.');
    } finally {
      setIsLoggingIn(false);
    }
  }
  return (
    <div className={`flex flex-col w-full max-w-[380px]`}>
      <div className={`font-semibold text-3xl text-center mb-8`}>
        Welcome Back
      </div>
      <div className="mb-4">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="Email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="Password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {error &&
              (Array.isArray(error) ? (
                <div className="text-red-500 text-sm">{error[0]}</div>
              ) : (
                <div className="text-red-500 text-sm">{error}</div>
              ))}
            <Button variant="darkBlue" className="w-full">
              {isLoggingIn ? (
                <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              Login
            </Button>
          </form>
        </Form>
        <div style={{ textAlign: 'center' }}>
          <Button
            variant="link"
            onClick={switchToForgotPassword}
            className="text-sm"
          >
            Forgot Password?
          </Button>
        </div>
      </div>
      <div className="text-center text-xs">
        If you have any questions, feel free to reach out to us via{' '}
        <a href="mailto:<EMAIL>"><EMAIL></a>. We will
        review your reply within one business day.
      </div>
    </div>
  );
};

export default LoginForm;
