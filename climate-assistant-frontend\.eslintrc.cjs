module.exports = {
  root: true,
  env: { browser: true, es2020: true },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react-hooks/recommended',
    'eslint-config-prettier',
    'plugin:@tanstack/eslint-plugin-query/recommended',
    'plugin:prettier/recommended',
    'prettier',
  ],
  ignorePatterns: ['dist', '.eslintrc.cjs'],
  parser: '@typescript-eslint/parser',
  plugins: ['react-refresh', '@tanstack/query', 'import'],
  rules: {
    'import/order': [
      'error',
      {
        groups: [
          'external', // External modules (e.g., react, lodash)
          'internal', // Internal modules (your own modules)
          'builtin', // Node.js built-in modules (e.g., fs, path)
          'parent', // Parent imports (e.g., ../module)
          'sibling', // Sibling imports (e.g., ./module)
          'index', // Index imports (e.g., ./)
        ],
        'newlines-between': 'always', // Require a newline between different groups
      },
    ],
    'react-refresh/only-export-components': [
      'warn',
      { allowConstantExport: true },
    ],
  },
};
