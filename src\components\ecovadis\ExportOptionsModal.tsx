
import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FileDown, Send, Copy, FileText, Info } from "lucide-react";
import { toast } from "sonner";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";

interface ExportOptionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  isGlacierConnected?: boolean;
  projectId?: string;
}

export function ExportOptionsModal({
  isOpen,
  onClose,
  isGlacierConnected = false,
  projectId
}: ExportOptionsModalProps) {
  const [isSyncing, setIsSyncing] = useState(false);
  
  const handleCopyEmail = () => {
    navigator.clipboard.writeText('<EMAIL>');
    toast.success("Email copied to clipboard!", {
      description: "You can now invite <PERSON> to your Ecovadis questionnaire."
    });
  };
  
  const handleSyncAnswers = () => {
    setIsSyncing(true);
    
    // Simulate syncing
    setTimeout(() => {
      setIsSyncing(false);
      toast.success("Answers synced with Ecovadis!", {
        description: "All your answers have been updated in your Ecovadis questionnaire."
      });
      onClose();
    }, 2000);
  };
  
  const handleExportExcel = () => {
    // Simulate export
    toast.success("Export started!", {
      description: "Your Excel file will download shortly."
    });
    
    // Simulate download delay
    setTimeout(() => {
      onClose();
    }, 1000);
  };

  const handleExportExcelWithEvidence = () => {
    // Simulate export
    toast.success("Export with evidence started!", {
      description: "Your Excel file and supporting documents will download shortly."
    });
    
    // Simulate download delay
    setTimeout(() => {
      onClose();
    }, 1000);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-glacier-darkBlue">
            Export Your Answers
          </DialogTitle>
          <DialogDescription className="text-gray-600 mt-2">
            Choose how you'd like to export your answers to Ecovadis
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="bg-glacier-mint/10 border border-glacier-mint/20 rounded-lg p-4 hover:bg-glacier-mint/20 transition-colors cursor-pointer" onClick={isGlacierConnected ? handleSyncAnswers : undefined}>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <h4 className="font-semibold text-glacier-darkBlue mb-1 flex items-center gap-2 cursor-help">
                    <Send className="h-4 w-4 text-glacier-mint" /> 
                    {isGlacierConnected ? "Sync with Ecovadis" : "Connect Glacier to Ecovadis"}
                    {!isGlacierConnected && <Info className="h-4 w-4 text-gray-500" />}
                  </h4>
                </TooltipTrigger>
                <TooltipContent className="max-w-md p-4 w-[500px]">
                  <div className="bg-white rounded-md overflow-hidden">
                    <h4 className="font-semibold text-glacier-darkBlue mb-2">How to invite Glacier to Ecovadis</h4>
                    <p className="text-sm text-gray-600 mb-3">
                      Use the "Get your colleagues involved" feature in Ecovadis to invite Glacier via the <NAME_EMAIL>
                    </p>
                    <img 
                      src="/lovable-uploads/be5eabf4-14f2-4539-a21c-dde8ef8376dc.png" 
                      alt="Ecovadis invitation screen" 
                      className="w-full border border-gray-200 rounded-md" 
                    />
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <p className="text-sm text-gray-600">
              {isGlacierConnected 
                ? "Automatically update your Ecovadis questionnaire with your latest answers." 
                : "Invite Glacier to your <NAME_EMAIL>"}
            </p>
            {!isGlacierConnected && (
              <p className="text-sm text-gray-600 mt-2 italic">
                "Glacier will then automatically pre-fill your questionnaire with the answers from within glacier, as soon as you mark an answer as complete in Glacier."
              </p>
            )}
            <div className="mt-3">
              {isGlacierConnected ? (
                <Button 
                  variant="mint" 
                  size="sm" 
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSyncAnswers();
                  }}
                  disabled={isSyncing}
                >
                  {isSyncing ? "Syncing..." : "Sync answers now"} 
                </Button>
              ) : (
                <Button 
                  variant="mint" 
                  size="sm" 
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCopyEmail();
                  }}
                  className="flex items-center gap-1.5"
                >
                  <Copy className="h-3.5 w-3.5" />
                  Copy E-Mail
                </Button>
              )}
            </div>
          </div>
          
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 hover:bg-gray-100 transition-colors cursor-pointer" onClick={handleExportExcel}>
            <h4 className="font-semibold text-glacier-darkBlue mb-1 flex items-center gap-2">
              <FileDown className="h-4 w-4" /> 
              Export Excel
            </h4>
            <p className="text-sm text-gray-600">
              Download a spreadsheet of your responses to manually add to your Ecovadis questionnaire.
            </p>
            <div className="mt-3">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={(e) => {
                  e.stopPropagation();
                  handleExportExcel();
                }}
              >
                Download Excel
              </Button>
            </div>
          </div>
          
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 hover:bg-gray-100 transition-colors cursor-pointer" onClick={handleExportExcelWithEvidence}>
            <h4 className="font-semibold text-glacier-darkBlue mb-1 flex items-center gap-2">
              <FileText className="h-4 w-4" /> 
              Export Excel + Evidence Documents
            </h4>
            <p className="text-sm text-gray-600">
              Download a spreadsheet and all supporting evidence documents as a zip file.
            </p>
            <div className="mt-3">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={(e) => {
                  e.stopPropagation();
                  handleExportExcelWithEvidence();
                }}
              >
                Download Bundle
              </Button>
            </div>
          </div>
        </div>

        {/* Removed the DialogFooter with Cancel Button */}
      </DialogContent>
    </Dialog>
  );
}
