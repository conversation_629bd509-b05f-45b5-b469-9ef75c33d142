"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const auth_service_1 = require("./auth.service");
const helpers_1 = require("./helpers");
const users_service_1 = require("../users/users.service");
const auth_dto_1 = require("./auth.dto");
const roles_decorator_1 = require("../auth/roles.decorator");
const swagger_1 = require("@nestjs/swagger");
const user_workspace_entity_1 = require("../users/entities/user-workspace.entity");
const env_helper_1 = require("../env-helper");
const supabase_auth_guard_1 = require("./supabase/supabase.auth.guard");
let AuthController = class AuthController {
    constructor(authService, userService) {
        this.authService = authService;
        this.userService = userService;
    }
    async login(loginDto, res) {
        const token = await this.authService.login(loginDto.email, loginDto.password);
        res.cookie(helpers_1.JWT_COOKIE_KEY, token, {
            httpOnly: true,
            secure: true,
            sameSite: 'none',
            expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1),
        });
        return res.send({ message: 'Login successful' });
    }
    async register(registerDto, res) {
        const token = await this.authService.registerWithCompany(registerDto);
        res.cookie(helpers_1.JWT_COOKIE_KEY, token, {
            httpOnly: true,
            secure: true,
            expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1),
        });
        return res.send({ message: 'Login successful' });
    }
    async passwordResetEmail(res, req, body) {
        await this.authService.sendPasswordResetEmail(body.email, req.headers.origin);
        return res.send({ message: 'Password reset email sent successfully' });
    }
    async validatePasswordResetToken(req, body) {
        const token = await this.authService.validateToken(body.token);
        return token.user;
    }
    async passwordSubmit(req, res, body) {
        const userToken = await this.authService.resetPassword(body.password, body.token);
        res.cookie(helpers_1.JWT_COOKIE_KEY, userToken, {
            httpOnly: true,
            secure: true,
            expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1),
        });
        return res.send({ message: 'Password reset successful' });
    }
    async logout(res) {
        res.clearCookie(helpers_1.JWT_COOKIE_KEY);
        return res.send({ message: 'Logout successful' });
    }
    getProfile(req) {
        return this.userService.findById(req.user.id);
    }
    async switchWorkspace(req, body, res) {
        const userId = req.user.id;
        const { workspaceId } = body;
        const token = await this.authService.switchUserWorkspace(userId, workspaceId);
        res.cookie(helpers_1.JWT_COOKIE_KEY, token, {
            httpOnly: true,
            secure: env_helper_1.isProduction,
            expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1),
        });
        return res.send({ message: 'Switch successful' });
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, helpers_1.Public)(),
    (0, common_1.Post)('login'),
    (0, swagger_1.ApiOperation)({ summary: 'User login' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Login successful' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_dto_1.LoginDto, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, helpers_1.Public)(),
    (0, common_1.Post)('register'),
    (0, swagger_1.ApiOperation)({ summary: 'User registration' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Registration successful' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_dto_1.RegisterWithCompanyDto, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "register", null);
__decorate([
    (0, helpers_1.Public)(),
    (0, common_1.Post)('request-password-reset'),
    (0, swagger_1.ApiOperation)({ summary: 'Password reset request' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Password reset email sent successfully',
    }),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Request)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "passwordResetEmail", null);
__decorate([
    (0, helpers_1.Public)(),
    (0, common_1.Post)('validate-password-reset-token'),
    (0, swagger_1.ApiOperation)({ summary: 'Password reset token validatiaon' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Valid token',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "validatePasswordResetToken", null);
__decorate([
    (0, helpers_1.Public)(),
    (0, common_1.Post)('password-reset-submit'),
    (0, swagger_1.ApiOperation)({ summary: 'Password reset submit' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Password submitted successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Res)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "passwordSubmit", null);
__decorate([
    (0, common_1.UseGuards)(supabase_auth_guard_1.AuthGuard),
    (0, common_1.Post)('logout'),
    (0, swagger_1.ApiOperation)({ summary: 'User logout' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Logout successful' }),
    __param(0, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "logout", null);
__decorate([
    (0, common_1.UseGuards)(supabase_auth_guard_1.AuthGuard),
    (0, common_1.Get)('profile'),
    (0, swagger_1.ApiOperation)({ summary: 'Get user profile' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'User profile retrieved successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], AuthController.prototype, "getProfile", null);
__decorate([
    (0, common_1.UseGuards)(supabase_auth_guard_1.AuthGuard),
    (0, common_1.Post)('switch-workspace'),
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "switchWorkspace", null);
exports.AuthController = AuthController = __decorate([
    (0, swagger_1.ApiTags)('auth'),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService,
        users_service_1.UsersService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map