"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkspaceService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const workspace_entity_1 = require("./entities/workspace.entity");
const user_workspace_entity_1 = require("../users/entities/user-workspace.entity");
const user_entity_1 = require("../users/entities/user.entity");
const token_entity_1 = require("../users/entities/token.entity");
const email_service_1 = require("../external/email.service");
const version_history_entity_1 = require("./entities/version-history.entity");
const company_entity_1 = require("./entities/company.entity");
let WorkspaceService = class WorkspaceService {
    constructor(workspaceRepository, companyRepository, userWorkspaceRepository, tokenRepository, userRepository, versionHistoryRepository, emailService) {
        this.workspaceRepository = workspaceRepository;
        this.companyRepository = companyRepository;
        this.userWorkspaceRepository = userWorkspaceRepository;
        this.tokenRepository = tokenRepository;
        this.userRepository = userRepository;
        this.versionHistoryRepository = versionHistoryRepository;
        this.emailService = emailService;
    }
    async findById(id) {
        return this.workspaceRepository.findOne({
            where: { id },
            relations: ['companies'],
        });
    }
    async updateById(id, updates) {
        const workspace = await this.workspaceRepository.findOneBy({ id });
        if (!workspace) {
            throw new Error('Workspace not found');
        }
        Object.assign(workspace, updates);
        return this.workspaceRepository.save(workspace);
    }
    async getUsersByWorkspace(workspaceId) {
        return this.userWorkspaceRepository
            .find({
            where: { workspaceId },
            relations: ['user', 'user.tokens'],
        })
            .then((userWorkspaces) => userWorkspaces.map((uw) => {
            let status = 'INACTIVE';
            if (!!this.isUserActive(uw.user)) {
                status = 'ACTIVE';
            }
            else {
                const token = uw.user.tokens?.at(-1);
                if (token && token.type === token_entity_1.TokenType.WorkspaceInvite) {
                    status = 'INVITED';
                }
            }
            delete uw.user.password;
            delete uw.user.tokens;
            return {
                ...uw.user,
                role: uw.role,
                status,
            };
        }));
    }
    async addUserToWorkspace({ workspaceId, userId, role, }) {
        const userWorkspace = this.userWorkspaceRepository.create({
            workspaceId,
            userId,
            role,
            joinedAt: new Date(),
        });
        return this.userWorkspaceRepository.save(userWorkspace);
    }
    async getUserWorkspace({ workspaceId, userId, }) {
        return this.userWorkspaceRepository.findOne({
            where: { workspaceId, userId },
        });
    }
    async removeUserFromWorkspace({ workspaceId, userId, }) {
        await this.userWorkspaceRepository.delete({ workspaceId, userId });
    }
    async inviteUserToWorkspace({ inviteeEmail, origin, workspaceId, email, role, shouldSendEmail = true, }) {
        let user = await this.userRepository.findOne({
            where: { email },
            relations: ['userWorkspaces'],
        });
        const invitingUser = await this.userRepository.findOne({
            where: { email: inviteeEmail },
        });
        if (!user) {
            user = this.userRepository.create({ email });
            await this.userRepository.save(user);
        }
        if (this.isUserActive(user)) {
            throw new Error(`${email} is already an active user in the workspace`);
        }
        if (!user.userWorkspaces) {
            const userWorkspace = this.userWorkspaceRepository.create({
                workspaceId,
                userId: user.id,
                role,
            });
            await this.userWorkspaceRepository.save(userWorkspace);
        }
        const inviteToken = crypto.randomUUID();
        const store = await this.tokenRepository.create({
            token: inviteToken,
            user,
            type: token_entity_1.TokenType.WorkspaceInvite,
            expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 3),
        });
        await this.tokenRepository.save(store);
        if (shouldSendEmail) {
            await this.emailService.inviteUser({
                token: inviteToken,
                invitingUser,
                email,
                origin,
            });
        }
        return user;
    }
    async acceptInvitation({ workspaceId, userId, }) {
        const userWorkspace = await this.getUserWorkspace({ workspaceId, userId });
        if (userWorkspace) {
            const joinedAt = new Date();
            await this.userWorkspaceRepository.update(userWorkspace, { joinedAt });
            return userWorkspace;
        }
        throw new Error('Invitation not found');
    }
    async storeActionHistory({ workspaceId, event, ref, versionData, }) {
        const history = this.versionHistoryRepository.create({
            workspaceId,
            event,
            ref,
            versionData,
        });
        await this.versionHistoryRepository.save(history);
    }
    async canInviteUser(invitedRole, user) {
        const userWorkspace = await this.userWorkspaceRepository.findOne({
            where: { userId: user.id },
        });
        const currentUserRole = userWorkspace?.role;
        if (currentUserRole === user_workspace_entity_1.Role.SuperAdmin)
            return true;
        if (currentUserRole === user_workspace_entity_1.Role.AiContributor &&
            [user_workspace_entity_1.Role.WorkspaceAdmin, user_workspace_entity_1.Role.Contributor].includes(invitedRole)) {
            return true;
        }
        if (currentUserRole === user_workspace_entity_1.Role.WorkspaceAdmin &&
            [user_workspace_entity_1.Role.WorkspaceAdmin, user_workspace_entity_1.Role.Contributor].includes(invitedRole)) {
            return true;
        }
        return false;
    }
    isUserActive(user) {
        return !!user.password;
    }
    async getAllWorkspaces() {
        return this.workspaceRepository.find({
            order: {
                name: 'ASC',
            },
        });
    }
    async getCompanyDetailFromWorkspaceId(workspaceId) {
        return this.companyRepository.findOne({
            where: { workspaceId },
        });
    }
    async updateCompanyDetail(workspaceId, companyDetail) {
        const company = await this.companyRepository.findOne({
            where: { workspaceId },
        });
        if (!company) {
            throw new Error(`Company not found for workspaceId: ${workspaceId}`);
        }
        return this.companyRepository.save({ id: company.id, ...companyDetail });
    }
};
exports.WorkspaceService = WorkspaceService;
exports.WorkspaceService = WorkspaceService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(workspace_entity_1.Workspace)),
    __param(1, (0, typeorm_1.InjectRepository)(company_entity_1.Company)),
    __param(2, (0, typeorm_1.InjectRepository)(user_workspace_entity_1.UserWorkspace)),
    __param(3, (0, typeorm_1.InjectRepository)(token_entity_1.Token)),
    __param(4, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(5, (0, typeorm_1.InjectRepository)(version_history_entity_1.VersionHistory)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        email_service_1.EmailService])
], WorkspaceService);
//# sourceMappingURL=workspace.service.js.map