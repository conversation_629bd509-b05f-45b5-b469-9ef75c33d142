import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from '@/components/ui/accordion';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';
import { EsrsTopic, TopicLevel } from '@/types/project';

export function TopicHierarchyAccordion({
  data,
  checkedState,
  handleStatusChange,
}: {
  data: EsrsTopic[];
  checkedState: {
    [id: number]: {
      level: TopicLevel;
      active: boolean;
    };
  };
  handleStatusChange: (item: EsrsTopic, checked: boolean) => void;
}) {
  const renderItems = (items: EsrsTopic[], level: number = 1) => {
    return items.map((item) => {
      const isChecked = checkedState[item.id].active || false;

      return (
        <div
          key={item.id}
          className={cn('ml-10', level === 1 ? 'my-4' : 'my-3')}
        >
          <div className="flex items-center space-x-5">
            <div className="flex items-center space-x-2 w-28">
              <Switch
                size="sm"
                checked={isChecked}
                onCheckedChange={(checked) => handleStatusChange(item, checked)}
                id={`report-disclosure-${item.id}`}
              />
              <span className="text-xs font-semibold text-nowrap">
                {isChecked ? 'Material' : 'Not Material'}
              </span>
            </div>
            <label
              className={cn(
                'text-glacier-bluedark',
                level === 1 && 'font-semibold'
              )}
              htmlFor={`report-disclosure-${item.id}`}
            >
              {item.name}
            </label>
          </div>
          {item.children && item.children.length > 0 && (
            <div>{renderItems(item.children, level + 1)}</div>
          )}
        </div>
      );
    });
  };

  return (
    <Accordion
      type="single"
      collapsible
      className="w-full flex flex-col space-y-4"
    >
      {data.map((item) => (
        <AccordionItem
          key={item.id}
          value={String(item.id)}
          className="w-full border-none bg-slate-100 rounded-lg py-2 px-5"
        >
          <AccordionTrigger onClick={(e) => e.stopPropagation()}>
            <div className="flex items-center space-x-5">
              <div className="flex items-center space-x-2 w-36">
                <Switch
                  size="sm"
                  onClick={(e) => e.stopPropagation()}
                  checked={checkedState[item.id].active || false}
                  onCheckedChange={(checked) =>
                    handleStatusChange(item, checked)
                  }
                  id={`report-disclosure-${item.id}`}
                />
                <span className="text-xs font-semibold">
                  {checkedState[item.id] ? 'Material' : 'Not Material'}
                </span>
              </div>
              <label>{item.name}</label>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            {item.children && renderItems(item.children, 1)}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
}
