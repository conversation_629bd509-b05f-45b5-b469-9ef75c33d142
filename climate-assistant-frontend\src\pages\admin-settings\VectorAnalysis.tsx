import { FunctionComponent, useState } from 'react';

import { MainLayout } from '@/components/MainLayout';
import { Button } from '@/components/ui/button.tsx';
import { toast } from '@/components/ui/use-toast.ts';
import { queryRelatedVectors } from '@/api/chats/chats.api';

type VectorSearchFormValues = {
  query: string;
  count: number;
  threshold: number;
  database: 'internal' | 'company';
};

export type VectorSearchResponseData = {
  content: string;
  similarity: number;
  fileUploadId: string;
  id: string;
};

export const VectorSearch: FunctionComponent = () => {
  const [formValues, setFormValues] = useState<VectorSearchFormValues>({
    query: '',
    count: 3,
    threshold: 0.7,
    database: 'internal',
  });

  const [responseSet, setResponseSet] = useState<VectorSearchResponseData[]>(
    []
  );

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormValues({
      ...formValues,
      [name]: value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const data = await queryRelatedVectors(formValues);
    setResponseSet(data);
    toast({
      variant: 'success',
      description: `Found ${data.length} similar vectors`,
    });
  };

  return (
    <MainLayout>
      <form
        onSubmit={handleSubmit}
        className="w-4/5 mx-auto p-6 bg-white rounded-md shadow-md space-y-6"
      >
        <div>
          <label
            htmlFor="query"
            className="block text-sm font-medium text-gray-700"
          >
            Prompt
          </label>
          <textarea
            id="query"
            name="query"
            value={formValues.query}
            onChange={handleInputChange}
            className="mt-1 p-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={10}
            required
          />
        </div>

        <div>
          <label
            htmlFor="count"
            className="block text-sm font-medium text-gray-700"
          >
            Count
          </label>
          <input
            type="number"
            id="count"
            name="count"
            value={formValues.count}
            onChange={handleInputChange}
            className="mt-1 p-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label
            htmlFor="threshold"
            className="block text-sm font-medium text-gray-700"
          >
            Threshold (0 to 1)
          </label>
          <input
            type="number"
            id="threshold"
            name="threshold"
            value={formValues.threshold}
            onChange={handleInputChange}
            className="mt-1 p-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label
            htmlFor="database"
            className="block text-sm font-medium text-gray-700"
          >
            Database
          </label>
          <select
            id="database"
            name="database"
            value={formValues.database}
            onChange={handleInputChange}
            className="mt-1 p-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          >
            <option value="internal">Internal</option>
            <option value="company">Company</option>
          </select>
        </div>

        <div>
          <Button
            type="submit"
            variant="default"
            style={{ backgroundColor: '#143560' }}
            className={`rounded-full w-1/3`}
          >
            Submit
          </Button>
        </div>
      </form>

      {responseSet.length > 0 && <VectorSearchTable data={responseSet} />}
    </MainLayout>
  );
};

const VectorSearchTable: React.FC<{ data: VectorSearchResponseData[] }> = ({
  data,
}) => {
  return (
    <div className="max-w-4xl mx-auto mt-8">
      <table className="min-w-full bg-white border border-gray-200">
        <thead>
          <tr>
            <th className="px-4 py-2 text-left bg-gray-100 border-b border-gray-200">
              Content
            </th>
            <th className="px-4 py-2 text-left bg-gray-100 border-b border-gray-200">
              Similarity
            </th>
          </tr>
        </thead>
        <tbody>
          {data.map((item) => (
            <tr key={item.id}>
              <td className="px-4 py-2 border-b border-gray-200">
                {item.content}
              </td>
              <td className="px-4 py-2 border-b border-gray-200">
                {/* round to 5 decimal plances */}
                {item.similarity.toFixed(5)}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
