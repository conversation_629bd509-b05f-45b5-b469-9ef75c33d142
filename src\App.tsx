
import './App.css';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

import { useRouting } from '@/routes.tsx';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
    },
  },
});

function App() {
  const routing = useRouting();
  const navigate = useNavigate();

  useEffect(() => {
    // Setup axios interceptor for 401 errors
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response && error.response.status === 401) {
          // Redirect to login page on 401 Unauthorized
          navigate('/login', { replace: true });
        }
        return Promise.reject(error);
      }
    );

    axios.interceptors.request.use((config) => {
      // Add authorization header if token is available from localStorage
      const token = localStorage.getItem('access_token');
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
      }
      return config;
    }, (error) => {
      // Handle request error
      return Promise.reject(error);
    }
    );
  }, [navigate]);

  return (
    <QueryClientProvider client={queryClient}>
      {routing}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}

export default App;
