import { Modu<PERSON> } from '@nestjs/common';
import { LlmRateLimiterService } from './llm-rate-limiter.service';
import { BullModule } from '@nestjs/bull';
import { JobProcessor } from 'src/types/jobs';
import { BullBoardModule } from '@bull-board/nestjs';
import { BullAdapter } from '@bull-board/api/bullAdapter';
import { LlmModule } from 'src/llm/llm.module';

@Module({
  imports: [
    LlmModule,
    BullModule.registerQueue({ name: JobProcessor.LlmRequest }),
    BullBoardModule.forFeature({
      name: JobProcessor.LlmRequest,
      adapter: BullAdapter,
    }),
  ],
  providers: [LlmRateLimiterService],
  exports: [LlmRateLimiterService],
  controllers: [],
})
export class LlmRateLimiterModule {}
