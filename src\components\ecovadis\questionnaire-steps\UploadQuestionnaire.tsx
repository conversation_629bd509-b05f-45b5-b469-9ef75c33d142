
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Check, HelpCircle, X, Trash } from 'lucide-react';
import { 
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogClose,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Progress } from '@/components/ui/progress';
import { FileUpload } from '@/components/ui/file-upload';
import { toast } from 'sonner';

interface UploadQuestionnaireProps {
  onComplete: () => void;
}

export const UploadQuestionnaire: React.FC<UploadQuestionnaireProps> = ({ onComplete }) => {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadComplete, setUploadComplete] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  
  const excelFileTypes = ['.xlsx', '.xls'];
  
  const handleFile = (files: File[]) => {
    if (files.length > 0) {
      const uploadedFile = files[0];
      const fileExtension = uploadedFile.name.split('.').pop()?.toLowerCase();
      
      if (fileExtension && ['xlsx', 'xls'].includes(fileExtension)) {
        setFile(uploadedFile);
        simulateUpload();
      } else {
        toast.error('Invalid file format', {
          description: 'Please upload an Excel file (.xlsx or .xls)'
        });
      }
    }
  };
  
  const simulateUpload = () => {
    setUploading(true);
    setUploadProgress(0);
    
    const interval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setUploading(false);
          setUploadComplete(true);
          onComplete();
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const cancelUpload = () => {
    setUploading(false);
    setUploadProgress(0);
  };
  
  const handleDeleteConfirm = () => {
    setFile(null);
    setUploadComplete(false);
    setDeleteConfirmOpen(false);
  };
  
  return (
    <div className="flex flex-col items-center">      
      <p className="text-lg text-gray-600 mb-2 text-center max-w-2xl">
        Upload your current Ecovadis questionnaire in Excel format. We'll automatically extract all themes, questions, and existing answers.
      </p>
      
      <div className="flex items-center gap-1.5 mb-6">
        <p className="text-sm text-gray-500">
          You don't know where to get your Questionnaire Excel from?
        </p>
        
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="ghost" size="icon" className="p-0 h-auto w-auto">
              <HelpCircle className="h-4 w-4 text-glacier-darkBlue/70" />
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl w-full p-0 overflow-hidden bg-white">
            <div className="flex flex-col">
              <div className="p-4 bg-glacier-darkBlue text-white font-medium flex justify-between items-center">
                <h2>How to download your Ecovadis Questionnaire</h2>
                <DialogClose className="text-white hover:text-gray-200">
                  <X className="h-5 w-5" />
                </DialogClose>
              </div>
              <div className="p-6">
                <div className="mb-4">
                  <h3 className="text-lg font-medium text-glacier-darkBlue mb-2">Follow these steps:</h3>
                  <ol className="text-sm text-gray-700 list-decimal pl-6 mb-4 space-y-2">
                    <li>Log in to your Ecovadis account</li>
                    <li>Navigate to the "Assessment Toolkit" section</li>
                    <li>Click on "Download questionnaire"</li>
                    <li>Select "As a spreadsheet" from the dropdown</li>
                  </ol>
                </div>
                <div className="border border-gray-200 rounded overflow-hidden">
                  <img 
                    src="/lovable-uploads/61b34bf2-998c-4f88-9cfd-cd2cb719e305.png" 
                    alt="Ecovadis download questionnaire screenshot" 
                    className="w-full h-auto object-contain"
                  />
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
      
      {!uploadComplete ? (
        <div className="w-full max-w-2xl mb-6">
          <FileUpload 
            onChange={handleFile} 
            acceptedFileTypes={excelFileTypes}
            acceptedFileTypesMessage="Please upload an Excel file (.xlsx or .xls)"
          />
        </div>
      ) : (
        <div className="border border-green-200 bg-green-50 rounded-lg p-4 w-full max-w-2xl mb-6 flex items-center justify-between">
          <div className="flex items-center">
            <div className="bg-green-500 rounded-full p-1 mr-3">
              <Check className="h-5 w-5 text-white" />
            </div>
            <div className="flex flex-col">
              <p className="text-green-800 font-medium">Questionnaire uploaded successfully!</p>
              <p className="text-sm text-green-700">{file?.name}</p>
            </div>
          </div>
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => setDeleteConfirmOpen(true)}
            className="text-red-500 hover:text-red-700 hover:bg-red-50"
          >
            <Trash className="h-5 w-5" />
          </Button>
        </div>
      )}
      
      {uploading && (
        <div className="w-full max-w-2xl mb-4 bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex justify-between items-center mb-2">
            <div className="flex items-center">
              <span className="text-sm font-medium">Uploading - 1/1 files</span>
            </div>
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-6 w-6 text-gray-500"
              onClick={cancelUpload}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="flex-1">
              <Progress value={uploadProgress} className="h-2" />
            </div>
            <span className="text-xs text-gray-500">{uploadProgress}%</span>
          </div>
          
          <p className="mt-2 text-sm text-gray-500 truncate">{file?.name}</p>
        </div>
      )}

      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Questionnaire File</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this file? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="border-gray-200">Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteConfirm}
              className="bg-red-500 text-white hover:bg-red-600 focus:ring-red-500"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
