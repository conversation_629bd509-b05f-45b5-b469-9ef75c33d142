"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1721366985356 = void 0;
class SchemaUpdate1721366985356 {
    constructor() {
        this.name = 'SchemaUpdate1721366985356';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "user_prompt_context"
       (
           "userId"  uuid              NOT NULL,
           "context" character varying NOT NULL,
           CONSTRAINT "PK_fa868054a006042b047c0619387" PRIMARY KEY ("userId")
       )`);
        await queryRunner.query(`ALTER TABLE "user_prompt_context"
          ADD CONSTRAINT "FK_fa868054a006042b047c0619387" FOREIGN KEY ("userId") REFERENCES "user" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "user_prompt_context" DROP CONSTRAINT "FK_fa868054a006042b047c0619387"`);
        await queryRunner.query(`DROP TABLE "user_prompt_context"`);
    }
}
exports.SchemaUpdate1721366985356 = SchemaUpdate1721366985356;
//# sourceMappingURL=1721366985356-schema-update.js.map