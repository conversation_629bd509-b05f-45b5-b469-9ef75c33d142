import { useState } from 'react';
import {
  LoaderCircle,
  RecycleIcon,
  Check,
  ChevronsUpDown,
  PlusCircle,
} from 'lucide-react';

import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { buttonVariants } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';
import {
  switchWorkspace,
  createWorkspace,
  createStarterProject,
} from '@/api/workspace-settings/workspace-settings.api';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useAuthentication } from '@/api/authentication/authentication.query';
import { useWorkspaces } from '@/hooks/useWorkspaces';
import { Language } from '@/types';

const SuperAdminSettings = ({
  currentWorkspaceId,
}: {
  currentWorkspaceId: string;
}) => {
  const [isSaving, setIsSaving] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const { workspaces, refetchWorkspaces } = useWorkspaces();
  const [selectedWorkspace, setSelectedWorkspace] =
    useState(currentWorkspaceId);
  const [open, setOpen] = useState(false);
  const { toast } = useToast();
  const { sessionReloadMutation } = useAuthentication();

  // New state for workspace creation
  const [newWorkspaceName, setNewWorkspaceName] = useState('');
  const [newWorkspaceEmail, setNewWorkspaceEmail] = useState('');
  const [newWorkspacePassword, setNewWorkspacePassword] = useState('');
  const [creationReference, setCreationReference] = useState<{
    userId: string;
    workspaceId: string;
    projectId: string;
  } | null>(null);

  const handleSwitchWorkspace = async () => {
    try {
      setIsSaving(true);
      await switchWorkspace(selectedWorkspace);
      await sessionReloadMutation.mutateAsync();
      toast({
        variant: 'success',
        description: 'Workspace switched successfully.',
      });
      // Refresh cookies or perform any other necessary actions
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'An unknown error occurred.',
      });
      console.error('Failed to switch workspace:', error);
    }
    setIsSaving(false);
  };

  // New function to handle workspace creation
  const handleCreateWorkspace = async () => {
    if (
      !newWorkspaceName.trim() ||
      !newWorkspaceEmail.trim() ||
      !newWorkspacePassword.trim()
    ) {
      toast({
        variant: 'destructive',
        title: 'Validation Error',
        description: 'Workspace name and email are required.',
      });
      return;
    }

    try {
      setIsCreating(true);
      const createNewWorkspace = await createWorkspace({
        name: newWorkspaceName,
        email: newWorkspaceEmail,
        password: newWorkspacePassword,
      });

      const createNewProject = await createStarterProject({
        workspaceId: createNewWorkspace.workspace.id,
        userId: createNewWorkspace.user.id,
        createProjectRequest: {
          name: 'Starter Project',
          primaryContentLanguage: 'EN' as keyof Language,
          type: 'CSRD',
        },
      });

      setCreationReference({
        userId: createNewWorkspace.user.id,
        workspaceId: createNewWorkspace.workspace.id,
        projectId: createNewProject.id,
      });

      refetchWorkspaces();
      setSelectedWorkspace(createNewWorkspace.workspace.id);

      // Clear the form
      setNewWorkspaceName('');
      setNewWorkspaceEmail('');
      setNewWorkspacePassword('');

      toast({
        variant: 'success',
        description: 'Workspace created successfully.',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'An unknown error occurred.',
      });
      console.error('Failed to create workspace:', error);
    }
    setIsCreating(false);
  };

  return (
    <div className="max-w-3xl">
      <div className="mt-10 grid gap-5">
        <Label className="text-2xl font-semibold">Switch Workspace</Label>
        <p className="text-gray-600 mt-2">
          As a Super Admin, you can switch between different workspaces.
        </p>

        {/* Combobox for selecting a workspace */}
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className="w-[400px] justify-between"
            >
              {selectedWorkspace
                ? workspaces.find(
                    (workspace) => workspace.id === selectedWorkspace
                  )?.name
                : 'Select a workspace'}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[400px] p-0" side="bottom">
            <Command>
              <CommandInput placeholder="Search workspace..." />
              <CommandList>
                <CommandEmpty>No workspace found.</CommandEmpty>
                <CommandGroup>
                  {workspaces.map((workspace) => (
                    <CommandItem
                      key={workspace.id}
                      value={workspace.name + workspace.id}
                      onSelect={() => {
                        setSelectedWorkspace(workspace.id);
                        setOpen(false);
                      }}
                    >
                      <Check
                        className={cn(
                          'mr-2 h-4 w-4',
                          selectedWorkspace === workspace.id
                            ? 'opacity-100'
                            : 'opacity-0'
                        )}
                      />
                      <div className="flex flex-col gap-1">
                        <span>{workspace.name}</span>
                        <span className="text-xs text-slate-400">
                          {workspace.id}
                        </span>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        <div className="flex gap-2">
          <button
            className={cn(
              buttonVariants({ variant: 'outline' }),
              'flex gap-2',
              isSaving ? 'cursor-not-allowed opacity-50' : ''
            )}
            onClick={handleSwitchWorkspace}
            disabled={isSaving || !selectedWorkspace}
          >
            {isSaving && <LoaderCircle className="h-4 w-4 animate-spin" />}
            <RecycleIcon className="h-4 w-4" />
            Switch Workspace
          </button>
        </div>

        {/* New Workspace Creation Section */}
        <div className="mt-10">
          <Label className="text-2xl font-semibold">Create New Workspace</Label>
          <p className="text-gray-600 mt-2">
            Create a new workspace by providing a name and admin email address.
          </p>

          <div className="mt-4 grid gap-4">
            <div>
              <Label htmlFor="workspace-name" className="mb-2 block">
                Workspace Name
              </Label>
              <Input
                id="workspace-name"
                placeholder="Enter workspace name"
                value={newWorkspaceName}
                onChange={(e) => setNewWorkspaceName(e.target.value)}
                className="w-[400px]"
              />
            </div>

            <div>
              <Label htmlFor="workspace-email" className="mb-2 block">
                Admin Email
              </Label>
              <Input
                id="workspace-email"
                type="email"
                placeholder="Enter admin email address"
                value={newWorkspaceEmail}
                onChange={(e) => setNewWorkspaceEmail(e.target.value)}
                className="w-[400px]"
              />
            </div>

            <div>
              <Label htmlFor="workspace-password" className="mb-2 block">
                Login Password
              </Label>
              <Input
                id="workspace-password"
                type="text"
                placeholder="Enter a password"
                value={newWorkspacePassword}
                onChange={(e) => setNewWorkspacePassword(e.target.value)}
                className="w-[400px]"
              />
            </div>

            {/* alert message after creation */}

            {creationReference && (
              <div className="bg-blue-100 border-l-4 border-blue-500 p-4">
                <p>Workspace created successfully. </p>
                <p>User ID: {creationReference.userId}</p>
                <p>Workspace ID: {creationReference.workspaceId}</p>
                <p>Project ID: {creationReference.projectId}</p>
              </div>
            )}

            <div className="flex gap-2">
              <button
                className={cn(
                  buttonVariants({ variant: 'default' }),
                  'flex gap-2',
                  isCreating ? 'cursor-not-allowed opacity-50' : ''
                )}
                onClick={handleCreateWorkspace}
                disabled={isCreating || !newWorkspaceName || !newWorkspaceEmail}
              >
                {isCreating && (
                  <LoaderCircle className="h-4 w-4 animate-spin" />
                )}
                <PlusCircle className="h-4 w-4" />
                Create Workspace
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuperAdminSettings;
