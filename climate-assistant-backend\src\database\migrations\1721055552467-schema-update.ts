import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1721055552467 implements MigrationInterface {
  name = 'SchemaUpdate1721055552467';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "chat_history"
          ALTER COLUMN "title" DROP DEFAULT`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "chat_history"
          ALTER COLUMN "title" SET DEFAULT ''`,
    );
  }
}
