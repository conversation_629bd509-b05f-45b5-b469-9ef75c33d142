
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { FileUp, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { DragDropZone } from '@/components/ui/documents/DragDropZone';
import { FileList } from '@/components/ui/documents/FileList';
import { uploadEcovadisQuestionnaire } from '@/api/projects/projects.api';

interface UploadQuestionnaireDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string;
  onUploadSuccess: () => void;
}

export const UploadQuestionnaireDialog: React.FC<UploadQuestionnaireDialogProps> = ({
  open,
  onOpenChange,
  projectId,
  onUploadSuccess
}) => {
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  
  const handleFileSelect = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xlsx,.xls';
    input.onchange = (e: any) => {
      const file = e.target.files[0];
      if (file && (file.name.endsWith('.xlsx') || file.name.endsWith('.xls'))) {
        setUploadFile(file);
      } else {
        toast.error("Invalid file format", {
          description: "Please upload an Excel file (.xlsx or .xls)"
        });
      }
    };
    input.click();
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        setUploadFile(file);
      } else {
        toast.error("Invalid file format", {
          description: "Please upload an Excel file (.xlsx or .xls)"
        });
      }
    }
  };

  const handleDeleteFile = () => {
    setUploadFile(null);
  };
  
  const handleUploadQuestionnaire = async () => {
    if (!uploadFile) {
      toast.error("No file selected", {
        description: "Please select an Excel file to upload your questionnaire."
      });
      return;
    }

    setIsUploading(true);

    try {
      // Use our API method to upload the questionnaire
      await uploadEcovadisQuestionnaire(projectId, uploadFile);
      
      toast.success("Questionnaire uploaded successfully", {
        description: "Your EcoVadis questionnaire has been imported."
      });
      
      setIsUploading(false);
      onOpenChange(false);
      setUploadFile(null);
      onUploadSuccess();
    } catch (err) {
      console.error('Error uploading questionnaire:', err);
      toast.error("Upload failed", {
        description: "There was an error uploading your questionnaire. Please try again."
      });
      setIsUploading(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="text-xl">Upload Questionnaire</DialogTitle>
          <DialogDescription className="text-gray-600 text-base pt-2">
            Upload your EcoVadis questionnaire Excel file. We'll automatically extract all themes, questions, and options.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-3">
          <ul className="space-y-2 text-sm">
            <li className="flex items-start gap-2">
              <div className="bg-blue-100 p-1 rounded-full mt-0.5">
                <FileUp className="h-3.5 w-3.5 text-blue-600" />
              </div>
              <span>Download your questionnaire Excel file from the EcoVadis platform</span>
            </li>
            <li className="flex items-start gap-2">
              <div className="bg-blue-100 p-1 rounded-full mt-0.5">
                <FileUp className="h-3.5 w-3.5 text-blue-600" />
              </div>
              <span>Upload the file here without modifying its structure</span>
            </li>
            <li className="flex items-start gap-2">
              <div className="bg-blue-100 p-1 rounded-full mt-0.5">
                <FileUp className="h-3.5 w-3.5 text-blue-600" />
              </div>
              <span>Only Excel files (.xlsx, .xls) are supported</span>
            </li>
          </ul>
          
          {!uploadFile ? (
            <DragDropZone
              isDragging={isDragging}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onFileSelect={handleFileSelect}
            />
          ) : (
            <FileList 
              files={[uploadFile]} 
              onDeleteClick={handleDeleteFile}
            />
          )}
          
          <div className="text-sm flex items-center gap-2 text-amber-600 bg-amber-50 p-2.5 rounded-lg">
            <AlertCircle className="h-4 w-4" />
            <span>Make sure you're uploading the correct questionnaire version for this project.</span>
          </div>
        </div>
        
        <DialogFooter className="gap-3 sm:gap-0">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            className="mt-0"
          >
            Cancel
          </Button>
          <Button 
            variant='forest'
            disabled={!uploadFile || isUploading} 
            onClick={handleUploadQuestionnaire}
          >
            {isUploading ? "Uploading..." : "Upload Questionnaire"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
