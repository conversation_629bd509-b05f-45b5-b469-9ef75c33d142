import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, Repository } from 'typeorm';
import { Language, Project } from './entities/project.entity';
import {
  CreateProjectRequest,
  ProjectData,
  UpdateProjectRequest,
} from './entities/project.dto';
import { Comment, CommentType } from './entities/comment.entity';
import {
  DataRequest,
  DataRequestStatus,
} from '../data-request/entities/data-request.entity';
import { KnowledgeBaseService } from '../knowledge-base/knowledge-base.service';
import { ESRSDatapoint } from '../datapoint/entities/esrs-datapoint.entity';
import { User } from 'src/users/entities/user.entity';
import { Workspace } from 'src/workspace/entities/workspace.entity';
import { Role } from 'src/users/entities/user-workspace.entity';
import { WorkspaceService } from 'src/workspace/workspace.service';
import type { ChatCompletionMessageParam } from 'openai/resources';
import { PromptService } from 'src/prompts/prompts.service';
import * as htmlDocx from 'html-docx-js';
import { marked } from 'marked';
import { MaterialESRSTopic } from './entities/material-esrs-topic.entity';
import {
  DatapointRequest,
  DatapointRequestStatus,
} from 'src/datapoint/entities/datapoint-request.entity';
import { ESRSTopicDatapoint } from 'src/knowledge-base/entities/esrs-topic-datapoint.entity';
import { ProjectDatapointRequestService } from './project-dp.service';
import { ESRSTopicLevel } from 'src/knowledge-base/entities/esrs-topic.entity';
import { chunkArray } from 'src/util/common-util';
import {
  CommentGeneration,
  CommentStatus,
} from './entities/comment-generation.entity';
import { ESRS_SORT_ORDER, GLOBAL_AI_USER_UUID } from 'src/util/config';
import { LLM_MODELS } from 'src/constants';
import { LlmRateLimiterService } from 'src/llm-rate-limiter/llm-rate-limiter.service';
import { generateExcelReport } from './helpers/export-project-excel';

@Injectable()
export class ProjectService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Project)
    private readonly projectRepository: Repository<Project>,
    @InjectRepository(Comment)
    private readonly commentRepository: Repository<Comment>,
    @InjectRepository(CommentGeneration)
    private readonly commentGenerationRepository: Repository<CommentGeneration>,
    @InjectRepository(DataRequest)
    private readonly dataRequestRepository: Repository<DataRequest>,
    @InjectRepository(MaterialESRSTopic)
    private readonly materialESRSTopicRepository: Repository<MaterialESRSTopic>,
    @InjectRepository(ESRSTopicDatapoint)
    private readonly esrsTopicDatapointRepository: Repository<ESRSTopicDatapoint>,
    @InjectRepository(DatapointRequest)
    private readonly datapointRequestRepository: Repository<DatapointRequest>,
    private readonly knowledgeBaseService: KnowledgeBaseService,
    private readonly projectDatapointRequestService: ProjectDatapointRequestService,
    private readonly workspaceService: WorkspaceService,
    private readonly llmRateLimitService: LlmRateLimiterService,

    private readonly promptService: PromptService,
    private dataSource: DataSource
  ) {}

  async findAll(workspaceId: string): Promise<Project[]> {
    return this.projectRepository.find({
      where: { workspaceId },
    });
  }

  async findActiveCSRDProjectByUser(userId: string): Promise<Project> {
    const user: User = await this.userRepository.findOne({
      where: { id: userId },
      relations: {
        userWorkspaces: {
          workspace: {
            projects: true,
          },
        },
      },
    });
    //Get first project of first workspace linked
    if (user.userWorkspaces && user.userWorkspaces.length > 0) {
      const workspace: Workspace = user.userWorkspaces[0].workspace;
      if (workspace.projects && workspace.projects.length > 0) {
        return workspace.projects[0];
      }
    }
    return null;
  }

  async create({
    workspaceId,
    userId,
    createProjectRequest,
  }: {
    workspaceId: string;
    userId: string;
    createProjectRequest: CreateProjectRequest;
  }): Promise<Project> {
    const project = this.projectRepository.create({
      name: createProjectRequest.name,
      primaryContentLanguage: createProjectRequest.primaryContentLanguage,
      workspaceId,
      createdBy: userId,
    });

    const store = await this.projectRepository.save(project);

    if (createProjectRequest.type === 'CSRD') {
      await this.initializeCSRDProjectData(store.id);
    }

    return store;
  }

  /**
   * Initialize CSRD project data by creating all required data requests and datapoints
   * using raw SQL queries
   * @param projectId The UUID of the project to initialize
   */
  async initializeCSRDProjectData(projectId: string): Promise<void> {
    // Use a transaction to ensure data consistency
    await this.dataSource.transaction(async (entityManager) => {
      // 1. Insert all DataRequests based on ESRS Disclosure Requirements that don't already exist
      await entityManager.query(
        `
          INSERT INTO data_request("dataRequestTypeId", "dataRequestType", "status", "content", "projectId")
          SELECT "id" as "dataRequestTypeId", 'ESRS' as "dataRequestType", 'no_data' as "status", '' as "content", $1 as "projectId" 
          FROM esrs_disclosure_requirement "dr1" 
          WHERE "id" NOT IN (
              SELECT "dr2"."dataRequestTypeId" as "id" 
              FROM "data_request" "dr2" 
              WHERE "dr2"."projectId" = $1
          )
        `,
        [projectId]
      );

      // 2. Insert all Datapoints for every DataRequest that don't already exist
      await entityManager.query(
        `
          INSERT INTO datapoint_request("esrsDatapointId", "dataRequestId", "status", "content")
          SELECT "esrs_datapoint"."id" as "esrsDatapointId", "dr"."id" as "dataRequestId", 'no_data' as "status", '' as "content"
          FROM "data_request" "dr" 
          JOIN "esrs_disclosure_requirement" "edr" ON "edr"."id" = "dr"."dataRequestTypeId" 
          JOIN "esrs_datapoint" ON "esrs_datapoint"."esrsDisclosureRequirementId" = "edr"."id"
          WHERE "dr"."projectId" = $1
          AND "esrs_datapoint"."id" NOT IN (
              SELECT "dpr2"."esrsDatapointId" as "id" 
              FROM "datapoint_request" "dpr2" 
              JOIN "data_request" "dr2" ON "dpr2"."dataRequestId" = "dr2"."id" 
              WHERE "dr2"."projectId" = $1
          )
        `,
        [projectId]
      );

      // 3. Set voluntary Datapoints to not Reported
      await entityManager.query(
        `
          UPDATE datapoint_request 
          SET "status" = 'not_answered' 
          FROM esrs_datapoint "edp", data_request "dr" 
          WHERE "dr"."id" = datapoint_request."dataRequestId" 
          AND "edp"."id" = datapoint_request."esrsDatapointId" 
          AND "edp"."optional" = true
          AND "dr"."projectId" = $1
        `,
        [projectId]
      );
    });
  }

  async findById(projectId: string): Promise<Project> {
    const project = await this.projectRepository.findOne({
      where: { id: projectId },
    });
    if (!project) {
      throw new NotFoundException(`Project with ID ${projectId} not found`);
    }
    return project;
  }

  async findData(projectId: string): Promise<ProjectData> {
    const project = await this.projectRepository.findOne({
      where: { id: projectId },
      relations: [
        'workspace',
        'creator',
        'materialTopics',
        'dataRequests',
        'dataRequests.responsiblePerson',
        'dataRequests.approver',
        'dataRequests.disclosureRequirement',
      ],
    });
    if (!project) {
      throw new NotFoundException(`Project with ID ${projectId} not found`);
    }
    return project;
  }

  async update({
    projectId,
    workspaceId,
    createdBy,
    updateProjectRequest,
  }: {
    projectId: string;
    workspaceId: string;
    createdBy: string;
    updateProjectRequest: UpdateProjectRequest;
  }): Promise<Project> {
    const project = await this.findById(projectId);
    if (!project) {
      throw new NotFoundException(`Project not found`);
    }
    if (workspaceId !== project.workspaceId) {
      throw new UnauthorizedException(`Project is not from this workspace`);
    }

    if (
      updateProjectRequest.reportTextGenerationRules &&
      updateProjectRequest.reportTextGenerationRules.length > 3
    ) {
      const validate = await this.validateReportTextGenerationRules(
        updateProjectRequest.reportTextGenerationRules,
        updateProjectRequest.primaryContentLanguage
      );
      if (validate.status === 403) {
        throw new ForbiddenException(validate.reason);
      }
    }

    Object.assign(project, updateProjectRequest);
    const store = await this.projectRepository.save(project);

    await this.workspaceService.storeActionHistory({
      event: 'project_updated',
      ref: projectId,
      workspaceId: workspaceId,
      versionData: {
        event: 'project_updated',
        doneBy: createdBy,
        data: store,
      },
    });

    return store;
  }

  async delete({
    projectId,
    workspaceId,
    userId,
  }: {
    projectId: string;
    workspaceId: string;
    userId: string;
  }): Promise<void> {
    const project = await this.findById(projectId);
    if (!project) {
      throw new NotFoundException(`Project not found`);
    }
    if (workspaceId !== project.workspaceId) {
      throw new UnauthorizedException(`Project is not from this workspace`);
    }
    await this.projectRepository.remove(project);
  }

  async findComment({ commentId }: { commentId: string }): Promise<Comment> {
    const commentData = await this.commentRepository.findOne({
      where: { id: commentId },
      relations: ['user'],
    });
    if (!commentData) {
      throw new NotFoundException(`Comment not found`);
    }
    return commentData;
  }

  async addComment({
    commentableId,
    commentableType,
    userId,
    workspaceId,
    comment,
    evaluationLot,
  }: {
    commentableId: string;
    commentableType: CommentType;
    userId: string;
    workspaceId: string;
    comment: string;
    evaluationLot?: boolean;
  }): Promise<Comment> {
    if (evaluationLot) {
      const storeComment = this.commentGenerationRepository.create({
        commentableId: commentableId,
        commentableType: commentableType,
        userId,
        comment,
        status: CommentStatus.Pending,
      });
      await this.commentGenerationRepository.save(storeComment);

      await this.workspaceService.storeActionHistory({
        event: commentableType + '_comment_gen_created',
        ref: commentableId,
        workspaceId: workspaceId,
        versionData: {
          event: 'comment_gen_created',
          doneBy: userId,
          data: storeComment,
        },
      });
    } else {
      const storeComment = this.commentRepository.create({
        commentableId: commentableId,
        commentableType: commentableType,
        userId,
        comment,
        resolved: false,
      });
      await this.commentRepository.save(storeComment);

      await this.workspaceService.storeActionHistory({
        event: commentableType + '_comment_created',
        ref: commentableId,
        workspaceId: workspaceId,
        versionData: {
          event: 'comment_created',
          doneBy: userId,
          data: storeComment,
        },
      });

      return await this.findComment({ commentId: storeComment.id });
    }
  }

  async updateComment({
    commentId,
    userId,
    workspaceId,
    comment,
  }: {
    commentId: string;
    userId: string;
    workspaceId: string;
    comment: string;
  }): Promise<Comment> {
    const commentData = await this.commentRepository.findOne({
      where: { id: commentId },
    });
    if (!commentData) {
      throw new NotFoundException(`Comment not found`);
    }
    commentData.userId = userId;
    commentData.comment = comment;
    await this.commentRepository.save(commentData);

    await this.workspaceService.storeActionHistory({
      event: commentData.commentableType + '_comment_updated',
      ref: commentId,
      workspaceId: workspaceId,
      versionData: {
        event: 'comment_updated',
        doneBy: userId,
        data: commentData,
      },
    });

    return commentData;
  }

  async updateCommentGenerationStatus({
    commentId,
    userId,
    workspaceId,
    data,
  }: {
    commentId: string;
    userId: string;
    workspaceId: string;
    data: {
      status: CommentStatus;
      evaluatorComment: string;
    };
  }): Promise<CommentGeneration> {
    const commentData = await this.commentGenerationRepository.findOne({
      where: { id: commentId },
    });
    if (!commentData) {
      throw new NotFoundException(`Comment not found`);
    }

    if (data.status === CommentStatus.Approved) {
      await this.addComment({
        commentableId: commentData.commentableId,
        commentableType: commentData.commentableType,
        userId: commentData.userId,
        workspaceId: workspaceId,
        comment: commentData.comment,
        evaluationLot: false,
      });
    }

    commentData.status = data.status;
    commentData.evaluatorComment = data.evaluatorComment;
    commentData.evaluatorId = userId;
    commentData.evaluatedAt = new Date();
    await this.commentGenerationRepository.save(commentData);

    return await this.commentGenerationRepository.findOne({
      where: { id: commentId },
    });
  }

  async deleteComment({
    commentId,
    userId,
    workspaceId,
  }: {
    commentId: string;
    userId: string;
    workspaceId: string;
  }): Promise<void> {
    const commentData = await this.commentRepository.findOne({
      where: { id: commentId },
    });
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['userWorkspaces'],
    });
    if (!commentData) {
      throw new NotFoundException(`Comment not found`);
    }

    // TODO: Refactor this to use a guard
    if (
      userId !== commentData.userId &&
      !(
        commentData.userId === GLOBAL_AI_USER_UUID &&
        user.userWorkspaces[0].role === Role.AiContributor
      ) &&
      user.userWorkspaces[0].role !== Role.SuperAdmin
    ) {
      throw new UnauthorizedException(`Comment is not from this user`);
    }

    await this.workspaceService.storeActionHistory({
      event: commentData.commentableType + '_comment_deleted',
      ref: commentId,
      workspaceId: workspaceId,
      versionData: {
        event: 'comment_deleted',
        doneBy: userId,
        data: commentData,
      },
    });

    await this.commentRepository.remove(commentData);
  }

  async resolveComment({
    commentId,
    resolution,
  }: {
    commentId: string;
    resolution: boolean;
  }): Promise<Comment> {
    const commentData = await this.commentRepository.findOne({
      where: { id: commentId },
    });
    if (!commentData) {
      throw new NotFoundException(`Comment not found`);
    }
    commentData.resolved = resolution;
    await this.commentRepository.save(commentData);

    return await this.findComment({ commentId });
  }

  async findAssignedESRSDatapoints({
    esrs,
    workspaceId,
  }): Promise<ESRSDatapoint[]> {
    const project = await this.projectRepository.findOne({
      where: { workspaceId },
    });

    const drs = await this.dataRequestRepository.find({
      where: { projectId: project.id },
      relations: ['datapointRequests'],
    });

    const dprs = drs.map((dr) => dr.datapointRequests).flat();

    const esrsDatapoints =
      await this.knowledgeBaseService.getEsrsDatapointsByStandard(esrs);

    const filteredEsrsDatapoints = esrsDatapoints
      .map((esrsDatapoint) => {
        return {
          ...esrsDatapoint,
          datapointRequestId: dprs.find(
            (dpr) => dpr.esrsDatapointId === esrsDatapoint.id
          )?.id,
        };
      })
      .filter((esrsDatapoint) => {
        return esrsDatapoint.datapointRequestId !== undefined;
      });

    return filteredEsrsDatapoints;
  }

  private async validateReportTextGenerationRules(
    text: string,
    language: Language = Language.EN
  ): Promise<{
    status: 200 | 403;
    reason: string;
  }> {
    const datapointGenerationChatCompletion: ChatCompletionMessageParam[] = [
      {
        role: 'system',
        content:
          this.promptService.validateReportTextGenerationRulesPrompt(language),
      },
      {
        role: 'user',
        content: text,
      },
    ];
    const datapointGenerationChatCompletionResponse =
      await this.llmRateLimitService.handleRequest({
        model: LLM_MODELS['gpt-4o'],
        messages: datapointGenerationChatCompletion,
        json: true,
        temperature: 0.5,
      });

    if (datapointGenerationChatCompletionResponse.status === 400) {
      return {
        status: 403,
        reason: datapointGenerationChatCompletionResponse.response,
      };
    }

    return datapointGenerationChatCompletionResponse.response;
  }

  async generateDocx(projectId: string): Promise<Buffer | Blob> {
    const dataRequests = await this.dataRequestRepository.find({
      where: { projectId, status: DataRequestStatus.ApprovedAnswer },
      relations: ['disclosureRequirement'],
    });

    if (dataRequests.length === 0) {
      throw new NotFoundException(
        'No approved reporttexts found for this project'
      );
    }

    const markdownContent = dataRequests
      .sort((a, b) => {
        // First sort by ESRS category using the predefined mapping
        const esrsSortOrderA =
          ESRS_SORT_ORDER[a.disclosureRequirement.esrs] || 999; // Default to a high number if not found
        const esrsSortOrderB =
          ESRS_SORT_ORDER[b.disclosureRequirement.esrs] || 999;

        if (esrsSortOrderA !== esrsSortOrderB) {
          return esrsSortOrderA - esrsSortOrderB;
        }

        // If ESRS categories are the same, sort by disclosure requirement sort order
        return a.disclosureRequirement.sort - b.disclosureRequirement.sort;
      })
      .map((dataRequest) => dataRequest.content)
      .join('\n\n');

    const htmlContent =
      '<meta charset="UTF-8">' + (await marked(markdownContent));
    const docxBlob = htmlDocx.asBlob(htmlContent) as Blob;
    const arrayBuffer = await docxBlob.arrayBuffer();
    const docxBuffer = Buffer.from(arrayBuffer);
    return docxBuffer;
  }

  async generateXlsx(projectId: string): Promise<Buffer> {
    const datapoints = await this.dataRequestRepository.query(
      `
          SELECT
            edr.esrs,
            edr.dr,
            edp."datapointId",
            edp.name as "datapoint",
            edp."dataType",
            dpr.content as "datapointText",
            dpc.comment AS "datapointGaps"
          FROM data_request dr
            JOIN esrs_disclosure_requirement edr ON dr."dataRequestTypeId" = edr.id
            JOIN datapoint_request dpr ON dr.id = dpr."dataRequestId"
            JOIN esrs_datapoint edp ON dpr."esrsDatapointId" = edp.id
            LEFT JOIN comment drc ON dr.id = drc."commentableId" AND drc."commentableType" = 'data_request' AND drc.resolved = false
            LEFT JOIN comment dpc ON dpr.id = dpc."commentableId" and dpc."commentableType" = 'datapoint_request' AND dpc.resolved = false
          WHERE dr."projectId" = $1 AND dpr.content IS NOT NULL AND dpr.content != ''
          ORDER BY edp."datapointId"
        `,
      [projectId]
    );

    const xlsBuffer = await generateExcelReport(datapoints);
    return xlsBuffer;
  }

  async findMaterialityStatus(projectId: string) {
    const project = await this.projectRepository.findOne({
      where: { id: projectId },
      relations: ['materialTopics'],
    });

    const esrsTopics = await this.knowledgeBaseService.getEsrsTopics();

    return { project, esrsTopics };
  }

  // TODO: Refactor this method for performance
  async updateMaterialityStatus(
    projectId: string,
    materialTopics: {
      esrsTopicId: number;
      level: ESRSTopicLevel;
      active: boolean;
    }[]
  ) {
    const project = await this.projectRepository.findOne({
      where: { id: projectId },
    });

    if (!project) {
      throw new Error('Project not found');
    }

    const topicDatapointCounts = await this.esrsTopicDatapointRepository
      .createQueryBuilder('etd')
      .select('etd.esrsDatapointId', 'esrsDatapointId')
      .addSelect('COUNT(*)', 'count')
      .where('etd.esrsTopicId IN (:...topicIds)', {
        topicIds: materialTopics.map((m) => m.esrsTopicId),
      })
      .groupBy('etd.esrsDatapointId')
      .getRawMany();

    const levelHierarchy = {
      [ESRSTopicLevel.TOPIC]: 0,
      [ESRSTopicLevel.SUB_TOPIC]: 1,
      [ESRSTopicLevel.SUB_SUB_TOPIC]: 2,
    };

    const topicDatapointCollection = topicDatapointCounts.reduce((acc, tdc) => {
      acc[tdc.esrsDatapointId] = {
        count: tdc.count,
        level: 0,
        material: false,
      };
      return acc;
    }, {});

    for (const mt of materialTopics) {
      let materialTopic = await this.materialESRSTopicRepository.findOne({
        where: {
          project: { id: projectId },
          esrsTopic: { id: mt.esrsTopicId },
        },
      });

      if (materialTopic) {
        materialTopic.active = mt.active;
        await this.materialESRSTopicRepository.save(materialTopic);
      } else {
        materialTopic = this.materialESRSTopicRepository.create({
          projectId: projectId,
          esrsTopicId: mt.esrsTopicId,
          active: mt.active,
        });
        await this.materialESRSTopicRepository.save(materialTopic);
      }

      // Begin updating datapointRequest statuses
      // Step 1: Find all ESRSDatapointIds associated with the esrsTopicId
      const topicDatapoints = await this.esrsTopicDatapointRepository.find({
        where: { esrsTopicId: mt.esrsTopicId },
        select: {
          esrsDatapointId: true,
        },
      });

      const esrsDatapointIds = topicDatapoints.map((td) => td.esrsDatapointId);

      if (esrsDatapointIds.length > 0) {
        // Step 2: Find DatapointRequests in the project associated with these datapoints
        const datapointRequests = (await this.datapointRequestRepository
          .createQueryBuilder('datapointRequest')
          .innerJoinAndSelect('datapointRequest.esrsDatapoint', 'esrsDatapoint')
          .innerJoin('datapointRequest.dataRequest', 'dataRequest')
          .where('datapointRequest.esrsDatapointId IN (:...esrsDatapointIds)', {
            esrsDatapointIds,
          })
          .andWhere('dataRequest.projectId = :projectId', { projectId })
          .select([
            'datapointRequest.id',
            'datapointRequest.dataRequestId',
            'datapointRequest.esrsDatapointId',
            'datapointRequest.status',
            'esrsDatapoint.optional',
          ])
          .getMany()) as unknown as {
          id: string;
          dataRequestId: string;
          esrsDatapointId: number;
          status: DatapointRequestStatus;
          esrsDatapoint: {
            optional: boolean;
          };
        }[];

        // Step 3: Update their status to 'not_answered' if the topic is not active
        const concurrency = 10; // set whatever batch size you need
        const datapointRequestChunks = chunkArray(
          datapointRequests,
          concurrency
        );
        for (const chunk of datapointRequestChunks) {
          await Promise.all(
            chunk.map(async (dpRequest) => {
              let dpMateriality = false;
              const topicDatapoint =
                topicDatapointCollection[dpRequest.esrsDatapointId];
              if (levelHierarchy[mt.level] >= topicDatapoint.level) {
                if (mt.active) {
                  // store materiality status of datapoint
                  dpMateriality = true;
                  topicDatapoint.material = true;
                } else if (topicDatapoint.level === levelHierarchy[mt.level]) {
                  // check if any other material topic is associated with the datapoint
                  dpMateriality = topicDatapoint.material;
                } else {
                  dpMateriality = false;
                  topicDatapoint.material = false;
                }
                topicDatapoint.level = levelHierarchy[mt.level];
              }

              if (dpMateriality) {
                dpRequest.status =
                  await this.projectDatapointRequestService.datapointRequestStatusProcessor(
                    dpRequest.id,
                    dpRequest.esrsDatapoint.optional
                  );
              } else {
                dpRequest.status = DatapointRequestStatus.NotAnswered;
              }

              await this.datapointRequestRepository.update(
                { id: dpRequest.id },
                { status: dpRequest.status }
              );
            })
          );
        }

        // Step 4: Update DataRequest status if all its DatapointRequests are 'not_answered'
        const dataRequestIds = [
          ...new Set(datapointRequests.map((dp) => dp.dataRequestId)),
        ];

        const dataRequestIdChunks = chunkArray(dataRequestIds, concurrency);
        for (const chunk of dataRequestIdChunks) {
          await Promise.all(
            chunk.map(async (dataRequestId) => {
              const dataRequest = (await this.dataRequestRepository
                .createQueryBuilder('dataRequest')
                .leftJoinAndSelect(
                  'dataRequest.datapointRequests',
                  'datapointRequest'
                )
                .leftJoinAndSelect(
                  'datapointRequest.datapointDocumentChunkMap',
                  'datapointDocumentChunkMap'
                )
                .select([
                  'dataRequest.id',
                  'dataRequest.content',
                  'dataRequest.status',
                  'dataRequest.approvedBy',
                  'datapointRequest.id',
                  'datapointRequest.status',
                  'datapointRequest.content',
                  'datapointDocumentChunkMap.id',
                  'datapointDocumentChunkMap.documentChunkId',
                ])
                .where('dataRequest.id = :dataRequestId', { dataRequestId })
                .getOne()) as unknown as {
                id: string;
                content: string;
                status: DataRequestStatus;
                approvedBy: string;
                datapointRequests: {
                  id: string;
                  status: DatapointRequestStatus;
                  content: string;
                  datapointDocumentChunkMap: {
                    id: string;
                    documentChunkId: string;
                  }[];
                }[];
              };

              if (dataRequest) {
                dataRequest.status =
                  this.projectDatapointRequestService.dataRequestStatusProcessor(
                    dataRequest
                  );
                await this.dataRequestRepository.update(
                  { id: dataRequest.id },
                  { status: dataRequest.status }
                );
              }
            })
          );
        }
      }

      await this.workspaceService.storeActionHistory({
        event: 'materiality_status_updated',
        ref: projectId,
        workspaceId: project.workspaceId,
        versionData: {
          event: 'materiality_status_updated',
          doneBy: project.createdBy,
          data: { materialTopics },
        },
      });
    }

    return await this.findMaterialityStatus(projectId);
  }

  async isDatapointMaterial({
    projectId,
    esrsDatapointId,
  }: {
    projectId: string;
    esrsDatapointId: number;
  }): Promise<boolean> {
    // Fetch all topics associated with the datapoint
    const topicDatapoints = await this.esrsTopicDatapointRepository.find({
      where: { esrsDatapointId },
    });

    const esrsTopicIds = topicDatapoints.map((td) => td.esrsTopicId);

    if (esrsTopicIds.length === 0) {
      // No associated topics, consider as non-material
      return false;
    }

    // Check if any associated topic is active (material) in the project
    const materialTopics = await this.materialESRSTopicRepository.find({
      where: {
        project: { id: projectId },
        esrsTopicId: In(esrsTopicIds),
        active: true,
      },
    });

    return materialTopics.length > 0;
  }

  async getProjectEsrsTopics({
    projectId,
    esrsTopicIds,
  }: {
    projectId: string;
    esrsTopicIds: number[];
  }) {
    const projectEsrsTopics = await this.materialESRSTopicRepository.find({
      where: {
        project: { id: projectId },
        esrsTopicId: In(esrsTopicIds),
      },
    });

    return projectEsrsTopics;
  }
}
