import { supabase } from "@/integrations/supabase/client";
import { GeneratedGap, ReviewGapRequest } from "@/types/generated-gaps";

/**
 * Get generated gaps for Super Admin review
 */
export const getGeneratedGaps = async (params?: {
  status?: string;
  projectId?: string;
  questionId?: string;
}): Promise<{ gaps: GeneratedGap[]; count: number }> => {
  let url = 'get-generated-gaps';
  
  if (params) {
    const searchParams = new URLSearchParams();
    if (params.status) {
      searchParams.append('status', params.status);
    }
    if (params.projectId) {
      searchParams.append('projectId', params.projectId);
    }
    if (params.questionId) {
      searchParams.append('questionId', params.questionId);
    }
    
    if (searchParams.toString()) {
      url += `?${searchParams.toString()}`;
    }
  }

  const { data, error } = await supabase.functions.invoke(url, {
    method: 'GET'
  });

  if (error) {
    console.error('Error fetching generated gaps:', error);
    throw new Error('Failed to fetch generated gaps');
  }

  return data;
};

/**
 * Review a generated gap (approve, disapprove, or regenerate)
 */
export const reviewGeneratedGap = async (request: ReviewGapRequest): Promise<{
  success: boolean;
  message: string;
  status: string;
}> => {
  const { data, error } = await supabase.functions.invoke('review-generated-gap', {
    body: request
  });

  if (error) {
    console.error('Error reviewing generated gap:', error);
    throw new Error('Failed to review generated gap');
  }

  return data;
};
