-- 360-Watch Findings
INSERT INTO kv_store (key, value) VALUES
('360-watch-findings:environment:scoring-drivers',
 '{"core-drivers":["Nature of the event","Severity of negative events","Timing of events","Number of severe cases","Number of minor cases","Monetary sanctions","Public ranking/recognition","Media coverage"],"extra-drivers":[]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('360-watch-findings:ethics:scoring-drivers',
 '{"core-drivers":["Nature of the event","Severity of negative events","Timing of events","Number of severe cases","Number of minor cases","Monetary sanctions","Public ranking/recognition","Media coverage"],"extra-drivers":[]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('360-watch-findings:labor_&_human_rights:scoring-drivers',
 '{"core-drivers":["Nature of the event","Severity of negative events","Timing of events","Number of severe cases","Number of minor cases","Monetary sanctions","Public ranking/recognition","Media coverage"],"extra-drivers":[]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('360-watch-findings:sustainable_procurement:scoring-drivers',
 '{"core-drivers":["Nature of the event","Severity of negative events","Timing of events","Number of severe cases","Number of minor cases","Monetary sanctions","Public ranking/recognition","Media coverage"],"extra-drivers":[]}'::jsonb);

-- Certifications
INSERT INTO kv_store (key, value) VALUES
('certifications:environment:scoring-drivers',
 '{"core-drivers":["Issuer","Validity","Scope","Relevance"],"extra-drivers":["Certification coverage by geography and business segment"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('certifications:ethics:scoring-drivers',
 '{"core-drivers":["Issuer","Validity","Scope","Relevance"],"extra-drivers":["Certification coverage by geography and business segment"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('certifications:general:scoring-drivers',
 '{"core-drivers":["Issuer","Validity","Scope","Relevance"],"extra-drivers":["Certification coverage by geography and business segment"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('certifications:labor_&_human_rights:scoring-drivers',
 '{"core-drivers":["Issuer","Validity","Scope","Relevance"],"extra-drivers":["Certification coverage by geography and business segment"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('certifications:sustainable_procurement:scoring-drivers',
 '{"core-drivers":["Issuer","Validity","Scope","Relevance"],"extra-drivers":["Certification coverage by geography and business segment"]}'::jsonb);

-- Commitments
INSERT INTO kv_store (key, value) VALUES
('commitments:environment:scoring-drivers',
 '{"core-drivers":["Relevance of commitment","Target coverage","Target verification","Ambition level"],"extra-drivers":["Alignment with industry initiatives","Alignment with science-based targets"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('commitments:ethics:scoring-drivers',
 '{"core-drivers":["Relevance of commitment","Target coverage","Target verification","Ambition level"],"extra-drivers":["Alignment with industry initiatives","Alignment with science-based targets"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('commitments:general:scoring-drivers',
 '{"core-drivers":["Relevance of commitment","Target coverage","Target verification","Ambition level"],"extra-drivers":["Alignment with industry initiatives","Alignment with science-based targets"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('commitments:labor_&_human_rights:scoring-drivers',
 '{"core-drivers":["Relevance of commitment","Target coverage","Target verification","Ambition level"],"extra-drivers":["Alignment with industry initiatives","Alignment with science-based targets"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('commitments:sustainable_procurement:scoring-drivers',
 '{"core-drivers":["Relevance of commitment","Target coverage","Target verification","Ambition level"],"extra-drivers":["Alignment with industry initiatives","Alignment with science-based targets"]}'::jsonb);

-- Policies
INSERT INTO kv_store (key, value) VALUES
('policies:environment:scoring-drivers',
 '{"core-drivers":["Relevance of policy","Coverage","Verification","Ambition level"],"extra-drivers":["Alignment with regulatory requirements","Alignment with industry initiatives"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('policies:ethics:scoring-drivers',
 '{"core-drivers":["Relevance of policy","Coverage","Verification","Ambition level"],"extra-drivers":["Alignment with regulatory requirements","Alignment with industry initiatives"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('policies:general:scoring-drivers',
 '{"core-drivers":["Relevance of policy","Coverage","Verification","Ambition level"],"extra-drivers":["Alignment with regulatory requirements","Alignment with industry initiatives"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('policies:labor_&_human_rights:scoring-drivers',
 '{"core-drivers":["Relevance of policy","Coverage","Verification","Ambition level"],"extra-drivers":["Alignment with regulatory requirements","Alignment with industry initiatives"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('policies:sustainable_procurement:scoring-drivers',
 '{"core-drivers":["Relevance of policy","Coverage","Verification","Ambition level"],"extra-drivers":["Alignment with regulatory requirements","Alignment with industry initiatives"]}'::jsonb);

-- Reporting
INSERT INTO kv_store (key, value) VALUES
('reporting:environment:scoring-drivers',
 '{"core-drivers":["Availability","Verification","Recency","Standards used"],"extra-drivers":["Materiality analysis disclosed","Visualisation of progress and performance"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('reporting:ethics:scoring-drivers',
 '{"core-drivers":["Availability","Verification","Recency","Standards used"],"extra-drivers":["Materiality analysis disclosed","Visualisation of progress and performance"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('reporting:general:scoring-drivers',
 '{"core-drivers":["Availability","Verification","Recency","Standards used"],"extra-drivers":["Materiality analysis disclosed","Visualisation of progress and performance"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('reporting:labor_&_human_rights:scoring-drivers',
 '{"core-drivers":["Availability","Verification","Recency","Standards used"],"extra-drivers":["Materiality analysis disclosed","Visualisation of progress and performance"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('reporting:sustainable_procurement:scoring-drivers',
 '{"core-drivers":["Availability","Verification","Recency","Standards used"],"extra-drivers":["Materiality analysis disclosed","Visualisation of progress and performance"]}'::jsonb);


-- Measures
INSERT INTO kv_store (key, value) VALUES
('measures:environment:scoring-drivers',
 '{"core-drivers":["Scope","Evidence","Relevance","Number of measures","Coverage level"],"extra-drivers":["Risk assessment included","Continuous improvement process","Alignment with external frameworks or standards","Employee engagement or training on measures","Stakeholder involvement","Evidence of impact"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('measures:ethics:scoring-drivers',
 '{"core-drivers":["Scope","Evidence","Relevance","Number of measures","Coverage level"],"extra-drivers":["Risk assessment included","Continuous improvement process","Alignment with external frameworks or standards","Employee engagement or training on measures","Stakeholder involvement","Evidence of impact"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('measures:labor_&_human_rights:scoring-drivers',
 '{"core-drivers":["Scope","Evidence","Relevance","Number of measures","Coverage level"],"extra-drivers":["Risk assessment included","Continuous improvement process","Alignment with external frameworks or standards","Employee engagement or training on measures","Stakeholder involvement","Evidence of impact"]}'::jsonb);

INSERT INTO kv_store (key, value) VALUES
('measures:sustainable_procurement:scoring-drivers',
 '{"core-drivers":["Scope","Evidence","Relevance","Number of measures","Coverage level"],"extra-drivers":["Risk assessment included","Continuous improvement process","Alignment with external frameworks or standards","Employee engagement or training on measures","Stakeholder involvement","Evidence of impact"]}'::jsonb);
