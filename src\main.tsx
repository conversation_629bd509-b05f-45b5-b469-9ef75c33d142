import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import axios from 'axios';

import './fonts.css';
import './index.css';
import './globals.css';

import App from './App.tsx';

import { TooltipProvider } from '@/components/ui/tooltip.tsx';
import { Toaster } from '@/components/ui/toaster.tsx';
import ErrorBoundary from '@/components/ErrorBoundary.tsx';
import { AuthProvider } from './context/AuthContext.tsx';

const queryClient = new QueryClient();
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <ReactQueryDevtools />
      <BrowserRouter>
        <ErrorBoundary>
          <TooltipProvider>
          <AuthProvider>
            <App />
          </AuthProvider>
          </TooltipProvider>
          <Toaster />
        </ErrorBoundary>
      </BrowserRouter>
    </QueryClientProvider>
  </React.StrictMode>
);
