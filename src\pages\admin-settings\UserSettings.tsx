import { FunctionComponent, useCallback, useEffect, useState } from 'react';
import { LoaderCircle } from 'lucide-react';

import { MainLayout } from '@/components/MainLayout';
import { Button } from '@/components/ui/button.tsx';
import { toast } from '@/components/ui/use-toast.ts';
import {
  deleteKnowledgeBaseFile,
  fetchKnowledgebaseFiles,
  uploadKnowledgeBaseFile,
} from '@/api/admin-settings/admin-settings.api.ts';
import type { DocumentUpload } from '@/types/project';

export const AdminSettings: FunctionComponent = () => {
  const [filetoUpload, setFiletoUpload] = useState<File | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState([] as DocumentUpload[]);
  const [isLoading, setIsLoading] = useState(false);

  const handleFileChange = (e: any) => {
    setFiletoUpload(e.target.files[0]);
  };

  const handleUpload = async () => {
    const formData = new FormData();
    formData.append('file', filetoUpload as Blob);

    setIsLoading(true);
    try {
      const response = await uploadKnowledgeBaseFile(formData);
      toast({ variant: 'success', description: response.message });
    } catch (e) {
      toast({ variant: 'destructive', description: 'Fehler beim Upload' });
    } finally {
      setIsLoading(false);
      setFiletoUpload(null);
      void loadUserFileUploads();
    }
  };

  const loadUserFileUploads = useCallback(async () => {
    const fileUploads = await fetchKnowledgebaseFiles();
    setUploadedFiles(fileUploads);
  }, []);

  const handleDeleteFile = async (id: string) => {
    await deleteKnowledgeBaseFile(id);
    toast({
      variant: 'success',
      description: 'File würde gelöscht',
    });
    void loadUserFileUploads();
  };

  useEffect(() => {
    void loadUserFileUploads();
  }, []);

  return (
    <MainLayout>
      <div className={`flex flex-col flex-1 p-24 overflow-auto`}>
        <div className={`text-2xl mb-7`}>Admin Einstellungen</div>

        <div className={`mb-8`}>
          <div className={`text-xl`}>Knowledgebase Files</div>
          <div className={`text-gray-500`}>
            Die Informationen dieser Files stehen allen Unternehmen zur
            Verfügung
          </div>
          <div className={`mt-3`}>
            <input
              type="file"
              onChange={handleFileChange}
              className={`hidden absolute left 0`}
              id={'file-upload-input'}
            />

            {uploadedFiles.length === 0 && (
              <div className={`text-gray-400 mb-2`}>
                Es wurden noch keine Files hochgeladen
              </div>
            )}

            {uploadedFiles.length > 0 && (
              <div>
                <div className={`font-semibold mt-2`}>
                  Bereits hochgeladene Files:
                </div>
                <div>
                  <ul className={`mb-3`}>
                    {uploadedFiles.map((file) => (
                      <li
                        className={`list-item list-disc ml-8`}
                        key={file.name}
                      >
                        <span>{file.name}</span>
                        <button
                          className={`ml-3 text-destructive border-0 hover:text-red-700`}
                          onClick={() => handleDeleteFile(file.id)}
                        >
                          Löschen
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {filetoUpload == null && (
              <Button
                variant="outline"
                onClick={() =>
                  document.getElementById('file-upload-input')?.click()
                }
                className={`mr-4`}
              >
                File auswählen
              </Button>
            )}

            {filetoUpload && (
              <div className={`mt-5`}>
                <Button
                  variant="outline"
                  onClick={() =>
                    document.getElementById('file-upload-input')?.click()
                  }
                  className={`mr-4`}
                >
                  Ausgewähltes File ändern
                </Button>

                <Button
                  variant="darkBlue"
                  onClick={handleUpload}
                  disabled={isLoading}
                  style={{ backgroundColor: '#143560' }}
                >
                  {isLoading && (
                    <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  <span>"{filetoUpload.name}" hochladen</span>
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
};
