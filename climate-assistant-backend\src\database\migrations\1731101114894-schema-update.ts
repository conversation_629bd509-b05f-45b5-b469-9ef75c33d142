import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1731101114894 implements MigrationInterface {
  name = 'SchemaUpdate1731101114894';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "document_chunk" ADD "metadataJson" json`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "document_chunk" DROP COLUMN "metadataJson"`,
    );
  }
}
