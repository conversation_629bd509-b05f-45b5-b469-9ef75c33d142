### Hierarchical Structure
The EcoVadis assessment system follows a clear hierarchical structure:

```
Topic (Theme)
├── Indicator
    ├── Question
        ├── Option 1
        │   └── Answer (with potential document chunks)
        ├── Option 2
        │   └── Answer (with potential document chunks)
        └── Option N
            └── Answer (with potential document chunks)
```

**Detailed Breakdown:**

1. **Topic (Theme)** - Top-level sustainability categories
   - Environment (🌱)
   - Labor & Human Rights (👥)
   - Ethics (🔷)
   - Sustainable Procurement (💼)
   - General (🌍)

2. **Indicator** - Specific assessment areas within each topic
   - POLICIES, ENDORSEMENTS, MEASURES
   - CERTIFICATIONS, COVERAGE, REPORTING
   - WATCH_FINDINGS

3. **Question** - Specific assessment questions within each topic-indicator combination
   - Each question has a unique code and detailed text
   - Questions target specific sustainability practices

4. **Option** - Available answer choices for each question
   - Represent best practices for company implementation
   - Each option can be selected independently (checkbox behavior)

5. **Answer** - User's response to a specific option within a project
   - Links to the selected option
   - Can contain text responses and document evidence

6. **Document Chunks** - Supporting evidence attached to answers
   - Multiple document chunks can be linked to each answer
   - Provide proof of implementation for selected options
