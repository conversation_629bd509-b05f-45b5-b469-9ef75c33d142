
import { useState } from 'react';
import { Upload, FileText, Search, Filter, ArrowUpDown } from 'lucide-react';
import { MainLayout } from '@/components/MainLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { UploadDocumentsButton } from '@/components/documents/UploadDocuments';
import { useDocuments } from '@/hooks/useDocuments';
import DocumentsProvider from '@/context/documentsContext';
import { DocumentsTable } from '@/components/ui/documents/DocumentsTable';

export const Documents = () => {
  const { uploadedFiles = [], loading, refetchDocuments } = useDocuments();
  const [searchQuery, setSearchQuery] = useState('');
  
  return (
    <MainLayout>
      <div className="m-4">
        <div className="flex justify-between items-center mb-6 mt-8">
          <div>
            <h1 className="text-2xl font-bold text-glacier-darkBlue">Documents</h1>
            <p className="text-gray-500 mt-1">
              Upload, organize, and manage your sustainability and compliance documents.
            </p>
          </div>
          <div className="flex space-x-3">
            {/* <Button 
              variant="outline"
              className="bg-white text-glacier-darkBlue border-glacier-darkBlue/20"
              onClick={() => {}}
            >
              <FileText className="h-4 w-4 mr-2" />
              Documents check list
            </Button> */}
            
            <DocumentsProvider refetchDocuments={refetchDocuments}>
              <UploadDocumentsButton refreshData={refetchDocuments} />
            </DocumentsProvider>
          </div>
        </div>
        
        <div className="bg-white rounded-md shadow-sm border border-gray-200">
          <div className="p-4 flex justify-between items-center">
            <div className="relative w-full max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search documents..."
                className="pl-9 w-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" className="flex items-center">
                <Filter className="h-4 w-4 mr-1.5" />
                Filter
              </Button>
              
              <Button variant="outline" size="sm" className="flex items-center">
                <ArrowUpDown className="h-4 w-4 mr-1.5" />
                Sort
              </Button>
            </div>
          </div>
          
          <DocumentsProvider refetchDocuments={refetchDocuments}>
            <DocumentsTable 
              documents={uploadedFiles || []}
              searchQuery={searchQuery}
              loading={loading}
            />
          </DocumentsProvider>
        </div>
      </div>
    </MainLayout>
  );
};

export default Documents;
