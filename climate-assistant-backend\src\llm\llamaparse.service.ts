import axios from 'axios';
import * as fs from 'fs';
import * as FormData from 'form-data';

const LLAMA_CLOUD_API_BASE_URL = 'https://api.cloud.eu.llamaindex.ai/api';

/**
 * Parse a document using LlamaIndex Cloud API
 *
 * @param path Local file path to be uploaded
 * @param premiumMode Enable premium parsing mode
 * @param pageSeparator Separator to use between pages if splitting
 * @param splitByPage Whether to split the document by pages
 * @returns object containing the parsed text and metadata
 */
export async function parseDocumentWithLlamaparseApi({
  filePath,
  premiumMode = true,
  pageSeparator = '\n=================\n',
  splitByPage = false,
}: {
  filePath: string;
  premiumMode: boolean;
  pageSeparator?: string;
  splitByPage?: boolean;
}): Promise<{
  text: string;
  metadata: { source: string };
}> {
  const LLAMA_CLOUD_API_KEY = process.env.LLAMA_CLOUD_API_KEY;

  if (!LLAMA_CLOUD_API_KEY) {
    throw new Error('LLAMA_CLOUD_API_KEY environment variable is not set');
  }

  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    // 1) Prepare the file and form data for upload
    const formData = new FormData();

    // Add the file from the filesystem
    formData.append('file', fs.createReadStream(filePath));

    // Add parameters
    formData.append('premium_mode', premiumMode.toString());
    formData.append('split_by_page', splitByPage.toString());
    formData.append('page_separator', pageSeparator);
    formData.append('output_tables_as_HTML', 'true');

    // 2) Upload file and get job ID
    console.log('Uploading file to LlamaIndex Cloud API...');
    const uploadResponse = await axios.post(
      `${LLAMA_CLOUD_API_BASE_URL}/parsing/upload`,
      formData,
      {
        headers: {
          Authorization: `Bearer ${LLAMA_CLOUD_API_KEY}`,
          ...formData.getHeaders(),
        },
        timeout: 2 * 60 * 1000, // 2 minutes timeout
      }
    );

    // Check that we have a job_id in the response
    const jobId = uploadResponse.data.id;
    if (!jobId) {
      console.error(
        'API Response:',
        JSON.stringify(uploadResponse.data, null, 2)
      );
      throw new Error('No job_id returned from the API');
    }

    console.log(`Parsing job started with ID: ${jobId}`);

    // 3) Poll job status until complete
    let isComplete = false;
    let maxAttempts = 2 * 60;
    let attempts = 0;
    let retryDelay = 10000; // Start with 10 seconds

    while (!isComplete && attempts < maxAttempts) {
      attempts++;
      console.log(
        `Checking job status (attempt ${attempts}/${maxAttempts})...`
      );

      try {
        const statusResponse = await axios.get(
          `${LLAMA_CLOUD_API_BASE_URL}/parsing/job/${jobId}`,
          {
            headers: {
              Authorization: `Bearer ${LLAMA_CLOUD_API_KEY}`,
            },
            timeout: 10000, // 10 second timeout
          }
        );

        console.log(`Current status: ${statusResponse.data.status}`);

        if (statusResponse.data.status === 'SUCCESS') {
          isComplete = true;
        } else if (
          statusResponse.data.status === 'FAILED' ||
          statusResponse.data.status === 'ERROR' ||
          statusResponse.data.status === 'CANCELLED'
        ) {
          throw new Error(
            `Parsing job failed: ${statusResponse.data.error || 'Unknown error'}`
          );
        } else {
          // Exponential backoff with max of 30 seconds
          retryDelay = Math.min(retryDelay * 2, 30000);
          await new Promise((resolve) => setTimeout(resolve, retryDelay));
        }
      } catch (pollError) {
        console.log(
          `Polling attempt ${attempts} failed, retrying in ${retryDelay / 1000} seconds...`
        );

        // Exponential backoff with max of 30 seconds
        retryDelay = Math.min(retryDelay * 1.5, 30000);
        await new Promise((resolve) => setTimeout(resolve, retryDelay));

        if (attempts >= maxAttempts) {
          throw new Error(`Maximum polling attempts (${maxAttempts}) reached`);
        }
      }
    }

    if (!isComplete) {
      throw new Error('Parsing job timed out');
    }

    // 4) Get results in markdown format
    const resultResponse = await axios.get(
      `${LLAMA_CLOUD_API_BASE_URL}/parsing/job/${jobId}/result/markdown`,
      {
        headers: {
          Authorization: `Bearer ${LLAMA_CLOUD_API_KEY}`,
        },
        timeout: 30000, // 30 second timeout
      }
    );

    // Make sure we have markdown content
    if (!resultResponse.data || !resultResponse.data.markdown) {
      console.error(
        'Result Response:',
        JSON.stringify(resultResponse.data, null, 2)
      );
      throw new Error('No markdown content returned from the API');
    }

    const markdownContent = resultResponse.data.markdown;
    console.log('Parsing complete');

    return {
      text: markdownContent,
      metadata: { source: filePath },
    };
  } catch (error) {
    console.error('Error while parsing document:', error);

    if (axios.isAxiosError(error)) {
      console.error('API Error:', error.response?.data || error.message);
      throw new Error(
        `API request failed: ${error.response?.data ? JSON.stringify(error.response.data) : error.message}`
      );
    }

    throw error;
  }
}
