import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { InfoIcon } from 'lucide-react';

import { Button } from '../ui/button';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';
import { Switch } from '../ui/switch';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { DatapointRequestData } from '@/types/project';

const generateDatapointWithAISchema = z.object({
  additionalReportTextGenerationRules: z.string().optional(),
  useExistingReportText: z.boolean().optional(),
});

export type GenerateDatapointWithAIFormData = z.infer<
  typeof generateDatapointWithAISchema
>;

export function AiGenerateDatapointConfirmModal({
  open,
  setOpen,
  callback,
  datapointRequest,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  callback: (data: GenerateDatapointWithAIFormData) => void;
  datapointRequest: DatapointRequestData;
}) {
  const { control, register, handleSubmit } =
    useForm<GenerateDatapointWithAIFormData>({
      defaultValues: {
        additionalReportTextGenerationRules:
          datapointRequest.customUserRemark || '',
        useExistingReportText: false,
      },
      resolver: zodResolver(generateDatapointWithAISchema),
    });

  const onSubmit = async (data: GenerateDatapointWithAIFormData) => {
    callback(data);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[625px]">
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-8">
          <DialogHeader>
            <DialogTitle>
              Generate datapoint {datapointRequest.esrsDatapoint.datapointId}
            </DialogTitle>
          </DialogHeader>

          <div className="grid gap-2">
            <Label>Context</Label>
            <DialogDescription className="mb-2">
              {datapointRequest.documentChunkCount} Document Links
            </DialogDescription>

            <div className="flex items-center gap-3 mb-3">
              <Controller
                control={control}
                name="useExistingReportText"
                render={({ field: { onChange, value, ref } }) => (
                  <Switch
                    size="sm"
                    id={`useExistingReportText-${datapointRequest.id}`}
                    checked={value}
                    onCheckedChange={onChange}
                    disabled={datapointRequest.content === ''}
                    ref={ref}
                  />
                )}
              />
              <Label
                className="text-nowrap"
                htmlFor={`useExistingReportText-${datapointRequest.id}`}
              >
                Use existing text
              </Label>
              <Tooltip delayDuration={0}>
                <TooltipTrigger onClick={(e) => e.preventDefault()}>
                  <InfoIcon className="w-4 h-4" />
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  When toggled on, the already created reporting text is used as
                  a basis for regeneration.
                </TooltipContent>
              </Tooltip>
            </div>

            <Label htmlFor="additionalReportTextGenerationRules">
              Additional Context (optional)
            </Label>

            <Textarea
              id="additionalReportTextGenerationRules"
              placeholder="Enter additional context for text generation, if available ..."
              autoCapitalize="none"
              autoCorrect="off"
              rows={5}
              {...register('additionalReportTextGenerationRules')}
            />
            <DialogDescription className="pt-3">
              {datapointRequest.content === '' ? (
                <span>
                  You are generating a new datapoint information. Generation may
                  take up to 30 seconds.
                </span>
              ) : (
                <div className="space-y-1">
                  <span className="text-yellow-600">
                    WARNING: This will overwrite the existing datapoint text. To
                    keep it, copy it before generating.
                  </span>
                  <div className="text-gray-500 text-sm">
                    Glacier AI can make mistakes
                  </div>
                </div>
              )}
            </DialogDescription>
          </div>

          <DialogFooter>
            <Button type="submit" variant='darkBlue'>Generate and overwrite</Button>
            <Button
              type="button"
              onClick={() => setOpen(false)}
              variant="secondary"
            >
              Cancel
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
