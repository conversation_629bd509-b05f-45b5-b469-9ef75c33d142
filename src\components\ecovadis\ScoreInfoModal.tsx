
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { useHtmlContent } from '@/hooks/useHtmlContent';
import { Skeleton } from '@/components/ui/skeleton';
import { EcovadisQuestion } from '@/types/ecovadis';

  // Get the indicator from the question
const getIndicator = (question) => {
  return String(question.indicator || "Policies");
};

// Clean topic name for use in scoring framework
const getCleanTopicName = (question) => {
  if (!question.topic) return "Policies";
  
  // Remove emojis and clean up the topic name
  return question.topic
    .replace(/[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{1FA70}-\u{1FAFF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu, '')
    .trim();
};

interface ScoreInfoModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  indicator?: string;
  question?: EcovadisQuestion;
}

export const ScoreInfoModal = ({ 
  open, 
  onOpenChange, 
  indicator, 
  question
}: ScoreInfoModalProps) => {

  const { content, isLoading, error } = useHtmlContent(getIndicator(question), getCleanTopicName(question));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[80vw] max-h-[90vh] overflow-auto p-6">
        <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogClose>
        
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-8 w-[300px]" />
            <Skeleton className="h-4 w-[250px]" />
            <Skeleton className="h-40 w-full" />
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <h3 className="text-lg font-semibold text-red-500">Error Loading Content</h3>
            <p className="mt-2 text-gray-600">{error}</p>
          </div>
        ) : (
          <div
            className="scoring-framework-content"
            dangerouslySetInnerHTML={{ __html: content }}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};
