"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersModule = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("./users.service");
const typeorm_1 = require("@nestjs/typeorm");
const user_entity_1 = require("./entities/user.entity");
const user_prompt_context_entity_1 = require("./entities/user-prompt-context.entity");
const workspace_entity_1 = require("../workspace/entities/workspace.entity");
const user_workspace_entity_1 = require("./entities/user-workspace.entity");
const token_entity_1 = require("./entities/token.entity");
const users_controller_1 = require("./users.controller");
const chat_gpt_service_1 = require("../llm/chat-gpt.service");
const perplexity_service_1 = require("../util/perplexity.service");
const prompt_context_generation_service_1 = require("./prompt-context-generation.service");
const multi_question_search_engine_service_1 = require("../util/multi-question-search-engine.service");
const email_service_1 = require("../external/email.service");
const company_entity_1 = require("../workspace/entities/company.entity");
const email_module_1 = require("../external/email.module");
const workspace_service_1 = require("../workspace/workspace.service");
const workspace_module_1 = require("../workspace/workspace.module");
const supabase_module_1 = require("../auth/supabase/supabase.module");
let UsersModule = class UsersModule {
};
exports.UsersModule = UsersModule;
exports.UsersModule = UsersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                user_entity_1.User,
                user_prompt_context_entity_1.UserPromptContext,
                token_entity_1.Token,
                company_entity_1.Company,
                workspace_entity_1.Workspace,
                user_workspace_entity_1.UserWorkspace,
            ]),
            email_module_1.EmailModule,
            workspace_module_1.WorkspaceModule,
            supabase_module_1.SupabaseAuthModule,
        ],
        providers: [
            users_service_1.UsersService,
            chat_gpt_service_1.ChatGptService,
            perplexity_service_1.PerplexityService,
            prompt_context_generation_service_1.PromptContextGenerationService,
            multi_question_search_engine_service_1.MultiQuestionSearchEngine,
            email_service_1.EmailService,
            workspace_service_1.WorkspaceService,
        ],
        exports: [users_service_1.UsersService, typeorm_1.TypeOrmModule],
        controllers: [users_controller_1.UsersController],
    })
], UsersModule);
//# sourceMappingURL=users.module.js.map