import { useState } from 'react';
import { UploadIcon } from 'lucide-react';

import { DocumentSettingsForm } from './DocumentSettingsForm';

import { Button } from '@/components/ui/button';
import { fileUploadSchema } from '@/validators/file-upload';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

export function UploadDocumentsButton({
  refreshData,
}: {
  refreshData: () => void;
}) {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <Button
        variant="default"
        className="rounded-full w-fit"
        onClick={() => setIsOpen(true)}
      >
        <UploadIcon className="h-4 w-4 mr-2" />
        Upload Document
      </Button>
      <DialogContent className="mt-2 bg-white/90 backdrop-blur-2xl max-w-max">
        <DialogHeader>
          <DialogTitle>Upload Document</DialogTitle>
        </DialogHeader>
        <DocumentSettingsForm
          callback={() => {
            refreshData();
            setIsOpen(false);
          }}
          formSchema={fileUploadSchema}
        />
      </DialogContent>
    </Dialog>
  );
}
