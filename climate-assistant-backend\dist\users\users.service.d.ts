import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { UserPromptContext } from './entities/user-prompt-context.entity';
import { Workspace } from '../workspace/entities/workspace.entity';
import { Role, UserWorkspace } from './entities/user-workspace.entity';
import { Company } from '../workspace/entities/company.entity';
import { Token } from './entities/token.entity';
import { EmailService } from '../external/email.service';
import { CreateUserWithCompanyAndWorkspaceDto } from '../auth/auth.dto';
import { PromptContextGenerationService } from './prompt-context-generation.service';
import { WorkspaceService } from 'src/workspace/workspace.service';
export declare class UsersService {
    private userRepository;
    private tokenRepository;
    private userPromptContextRepository;
    private companyRepository;
    private workspaceRepository;
    private userWorkspaceRepository;
    private emailService;
    private readonly promptContextGenerationService;
    private readonly workspaceService;
    constructor(userRepository: Repository<User>, tokenRepository: Repository<Token>, userPromptContextRepository: Repository<UserPromptContext>, companyRepository: Repository<Company>, workspaceRepository: Repository<Workspace>, userWorkspaceRepository: Repository<UserWorkspace>, emailService: EmailService, promptContextGenerationService: PromptContextGenerationService, workspaceService: WorkspaceService);
    private readonly GLOBAL_AI_USER_UUID;
    private readonly GLOBAL_AI_USER_EMAIL;
    private readonly GLOBAL_AI_USER_NAME;
    findById(id: User['id']): Promise<User | undefined>;
    findByEmailWithPassword(email: User['email']): Promise<User | null>;
    findByEmail(email: User['email']): Promise<User | null>;
    getUserPromptContext(userId: User['id']): Promise<string>;
    saveUserPromptContext(userId: User['id'], context: string): Promise<void>;
    getGeneratedPromptContext(userId: User['id']): Promise<string>;
    createUser(email: string): Promise<User>;
    createUserWithCompanyAndWorkspace(createUserDto: CreateUserWithCompanyAndWorkspaceDto): Promise<{
        user: User;
        company: Company;
        workspace: Workspace;
    }>;
    findFirstWorkspaceIdByUser(user: User): Promise<{
        workspaceId: string;
        companyId: string;
    }>;
    sendPasswordResetEmail({ email, origin, shouldSendEmail, }: {
        email: string;
        origin: string;
        shouldSendEmail: boolean;
    }): Promise<void>;
    validateToken(token: string): Promise<Token>;
    resetUserPassword(userToken: Token, password: string): Promise<User>;
    findGlobalGlacierAIUser(): Promise<User>;
    userHasRequiredRole(userOrId: string, role: Role[]): Promise<boolean>;
    userHasRequiredRoleOrFail({ userOrId, role, message, }: {
        userOrId: string;
        role: Role[];
        message?: string;
    }): Promise<boolean>;
    switchWorkspace(userId: string, workspaceId: string): Promise<User>;
}
