
import React from 'react';

interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
  completedSteps: Record<number, boolean>;
}

export const StepIndicator: React.FC<StepIndicatorProps> = ({ 
  currentStep, 
  totalSteps,
  completedSteps 
}) => {
  return (
    <div className="flex flex-col items-center mb-8">
      <div className="flex space-x-1 items-center w-full max-w-lg">
        {Array.from({ length: totalSteps }).map((_, index) => {
          const step = index + 1;
          const isActive = step === currentStep;
          const isCompleted = completedSteps[step];
          
          return (
            <React.Fragment key={index}>
              {index > 0 && (
                <div 
                  className={`h-1 flex-1 rounded-full transition-colors duration-300 ${
                    isCompleted || completedSteps[index]
                      ? 'bg-glacier-mint/50'
                      : 'bg-gray-200'
                  }`} 
                />
              )}
              <div 
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors duration-300 ${
                  isActive 
                    ? 'bg-glacier-mint text-glacier-darkBlue' 
                    : isCompleted 
                      ? 'bg-glacier-mint/80 text-white' 
                      : 'bg-gray-200 text-gray-500'
                }`}
              >
                {isCompleted ? (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                  </svg>
                ) : (
                  step
                )}
              </div>
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};
