
import { ReactNode } from 'react';
import { FolderPlus, AlertTriangle, Info } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

type EmptyStateType = 'default' | 'warning' | 'info';

interface EmptyStateProps {
  title: string;
  description: string;
  icon?: ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
  };
  type?: EmptyStateType;
  className?: string;
}

export function EmptyState({
  title,
  description,
  icon,
  action,
  secondaryAction,
  type = 'default',
  className,
}: EmptyStateProps) {
  const getDefaultIcon = () => {
    switch (type) {
      case 'warning':
        return <AlertTriangle className="h-12 w-12 text-yellow-500" />;
      case 'info':
        return <Info className="h-12 w-12 text-blue-500" />;
      default:
        return <FolderPlus className="h-12 w-12 text-gray-400" />;
    }
  };

  const getContainerClass = () => {
    switch (type) {
      case 'warning':
        return 'bg-yellow-50 border-yellow-100';
      case 'info':
        return 'bg-blue-50 border-blue-100';
      default:
        return 'bg-gray-50 border-gray-100';
    }
  };

  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center p-8 text-center rounded-lg border',
        getContainerClass(),
        className
      )}
    >
      <div className="mb-4">{icon || getDefaultIcon()}</div>
      <h3 className="font-semibold text-lg mb-2">{title}</h3>
      <p className="text-gray-500 mb-6 max-w-md">{description}</p>
      
      {action && (
        <div className="space-x-3">
          <Button
            onClick={action.onClick}
            variant="darkBlue"
          >
            {action.label}
          </Button>
          
          {secondaryAction && (
            <Button
              onClick={secondaryAction.onClick}
              variant="outline"
            >
              {secondaryAction.label}
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
