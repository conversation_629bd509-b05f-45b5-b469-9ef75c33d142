import './App.css';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { useRouting } from '@/routes.tsx';
import { setupAxiosInterceptors } from '@/lib/utils.ts';

function App() {
  const routing = useRouting();
  const navigate = useNavigate();

  useEffect(() => {
    setupAxiosInterceptors(navigate);
  }, []);

  return <>{routing}</>;
}

export default App;
