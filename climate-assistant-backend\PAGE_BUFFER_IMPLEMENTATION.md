# Page Range Buffer Implementation

## Overview
This implementation adds a buffer of 2 pages before and after user-defined page ranges to account for potential misalignment between actual PDF pagination and user-indicated page numbers.

## Files Modified

### 1. `src/util/common-util.ts`
**Added two new utility functions:**

#### `addPageRangeBuffer(pages: number[], maxPage?: number): number[]`
- Core function that adds ±2 page buffer
- Handles edge cases (beginning/end of document)
- Parameters:
  - `pages`: Array of page numbers
  - `maxPage`: Optional maximum page number for edge case handling
- Returns: Extended array of page numbers

#### `addPageRangeBufferWithLogging(pages: number[], maxPage?: number, context: string): number[]`
- Wrapper function that includes logging
- Logs original and extended page ranges
- Parameters:
  - `pages`: Array of page numbers
  - `maxPage`: Optional maximum page number
  - `context`: Context string for logging (e.g., "questionnaire upload")
- Returns: Extended array of page numbers

### 2. `src/auth/supabase/questionnaire.service.ts`
**Modified `processDocumentsForAnswer` function:**
- Added import for `addPageRangeBufferWithLogging`
- Integrated buffer functionality in questionnaire upload process
- Added logging to track original vs extended page ranges

## Implementation Details

### Buffer Logic
```typescript
// Example: User specifies pages 20-28
// Original: [20, 21, 22, 23, 24, 25, 26, 27, 28]
// Extended: [18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]
```

### Edge Case Handling
- **Beginning of document**: If user specifies pages starting at 1, buffer only extends forward
- **End of document**: If `maxPage` is provided, buffer respects document boundaries
- **Empty arrays**: Returns empty array without modification

### Logging Output
```
[questionnaire upload - document: example.pdf] Page range buffer applied:
  Original pages: [20, 21, 22, 23, 24, 25, 26, 27, 28]
  Extended pages: [18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]
  Buffer added: 4 pages
```

## Current Integration

### ✅ Questionnaire Upload
- **Status**: Implemented
- **Location**: `processDocumentsForAnswer` function
- **Function**: `addPageRangeBufferWithLogging`
- **Logging**: Enabled with document name context

### ✅ Manual Attachment
- **Status**: Implemented
- **Location**: `ecovadis-ui/supabase/functions/_shared/utils.ts`
- **Function**: `parsePagesString` (updated with buffer functionality)
- **Files Modified**:
  - `_shared/utils.ts`: Added buffer functions and updated `parsePagesString`
  - `ecovadis-document-link/index.ts`: Updated calls to use buffer
  - `ecovadis-questionnaire-import/index.ts`: Added buffer functions and updated `parsePageNumbers`
- **Logging**: Enabled with context for manual attachment and questionnaire import

### 🔄 AI Linking (Pending)
- **Status**: Not yet implemented
- **Location**: AI answer generation functions
- **Next Step**: Integrate buffer functionality

## Testing

### Test Cases Covered
1. **Single range**: `[5,6,7]` → `[3,4,5,6,7,8,9]`
2. **Range at page 1**: `[1,2,3]` → `[1,2,3,4,5]`
3. **Middle range**: `[10,11,12]` → `[8,9,10,11,12,13,14]`
4. **Empty array**: `[]` → `[]`
5. **Single page**: `[5]` → `[3,4,5,6,7]`

### Test File
- `test-page-buffer.js`: Simple test script to verify functionality

## Next Steps

1. **Test the implementation** with actual questionnaire upload ✅
2. **Extend to manual attachment** by modifying `parsePagesString` in Supabase functions ✅
3. **Extend to AI linking** by integrating buffer in AI answer generation
4. **Add maxPage parameter** for better edge case handling
5. **Consider database schema changes** to store extended ranges separately if needed

## Benefits

1. **Improved accuracy**: Captures context around user-specified pages
2. **Handles misalignment**: Accounts for title pages, annexes, etc.
3. **Transparent to users**: Automatic buffer application
4. **Comprehensive logging**: Tracks all buffer applications
5. **Edge case safe**: Handles document boundaries gracefully 