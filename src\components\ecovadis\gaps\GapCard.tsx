
import { FileT<PERSON><PERSON>, ChevronDown, <PERSON>Question, Trash2, Loader2 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { GapItem } from "@/types/ecovadis";
import { Badge } from "@/components/ui/badge";
import { Link, useLocation } from "react-router-dom";
import { MarkCompleteToggle } from "./MarkCompleteToggle";
import { MarkdownRenderer } from "@/components/ui/markdown-renderer";
import { Chip } from "@/components/ui/chip";
import { useUserRole } from "@/hooks/useUserRole";
import { USER_ROLE } from "@/constants/workspaceConstants";
import { formatDate } from "@/lib/utils";
import { MemberSelector } from "@/components/dashboard/MemberSelector";

interface GapCardProps {
  gap: GapItem;
  onMarkAsComplete: (id: string, complete: boolean) => void;
  onDeleteGap?: (id: string) => void;
  onFixGapClick: () => void;
  onAssignUser?: (id: string, userId: string | null) => void;
  users?: Array<{ id: string, name: string }>;
  isDeleting?: boolean;
  currentActionGapId?: string;
}

export const GapCard = ({ 
  gap, 
  onMarkAsComplete,
  onDeleteGap,
  onFixGapClick,
  onAssignUser,
  users = [],
  isDeleting = false,
  currentActionGapId
}: GapCardProps) => {
  const location = useLocation();
  const isAllGapsPage = location.pathname === "/improvements";
  const { hasRole } = useUserRole();
  const isSuperAdmin = hasRole([USER_ROLE.SuperAdmin]);

  // Map the backend data structure to the UI expected properties
  const title = gap.gaps?.Title || gap.title || "";
  const description = gap.gaps?.Description || gap.description || "";
  const relatedDocument = gap.documents || [];
  const isComplete = gap.resolved || gap.isComplete || false;

  const handleMarkComplete = (isComplete: boolean) => {
    onMarkAsComplete(gap.id, isComplete);
  };

  const handleDeleteClick = () => {
    if (onDeleteGap) {
      onDeleteGap(gap.id);
    }
  };

  const handleAssignUser = async (userId: string) => {
    if (onAssignUser) {
      await onAssignUser(gap.id, userId);
    }
  };

  return (
    <Card className={`border-l-4 ${isComplete ? 'border-l-[#38A169] bg-[#F2FCE2]/30' : 'border-l-glacier-darkBlue'} transition-colors duration-300`}>
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row justify-between gap-4">
          {/* Left side content */}
          <div className="flex-1">
            <h3 className="text-lg font-medium text-glacier-darkBlue mb-2">
              <MarkdownRenderer text={title} /></h3>
            <p className="text-sm text-gray-700 mb-3">
              <MarkdownRenderer text={description} />
            </p>
            
            {/* Add timestamp display */}
            {gap.createdAt && (
              <p className="text-xs text-gray-500 mb-3">
                Created: {formatDate(gap.createdAt)}
              </p>
            )}
            
            {relatedDocument?.map((doc, index) => (
              <a key={index}
                  href={`/documents/${doc.id}`}
                  target="_blank"
                 className="flex items-center gap-1 text-sm">
                <FileText className="h-4 w-4 text-gray-500" />
                <span className="font-medium underline">{doc.name}</span>
              </a>
            ))}

            {isAllGapsPage && gap.questionId && gap.projectName && gap.questionCode && (
              <Link to={`/ecovadis/question/${gap.questionId}`} className="mt-2 inline-block">
                <Badge variant="secondary" className="text-xs cursor-pointer hover:bg-blue-200/80 flex items-center gap-1">
                  <FileQuestion className="h-3 w-3 mr-1" />
                  {gap.projectName} | {gap.questionCode}
                </Badge>
              </Link>
            )}
          </div>
          
          {/* Right side controls */}
          <div className="flex flex-col md:items-end gap-3">
            <div className="flex items-center gap-2">
              <MarkCompleteToggle 
                isComplete={isComplete} 
                onToggleComplete={handleMarkComplete}
                disableCompleteAction={currentActionGapId === gap.id}
              />
              {isSuperAdmin && onDeleteGap && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDeleteClick}
                  disabled={isDeleting}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  {isDeleting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                </Button>
              )}
            </div>
            
            {/* Assignment section */}
            {onAssignUser && users.length > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Assign to:</span>
                <MemberSelector
                  action={handleAssignUser}
                  currentUser={gap.assignedTo}
                  placeholder="Select user"
                  members={users}
                />
              </div>
            )}
          </div>
        </div>
        
        {/* Display indicator and questionCode as chip if available */}
        {gap.ecovadis_question && (
          <div className="flex justify-start mt-2 mb-2">
            {isAllGapsPage && gap.projectId && gap.questionId  ? (
              <Link to={`/ecovadis-project/${gap.projectId}?questionId=${gap.questionId}`}>
                <Chip 
                  variant="primary"
                  className="text-xs cursor-pointer hover:bg-glacier-darkBlue hover:text-white"
                >
                  {gap.ecovadis_question.indicator} | {gap.ecovadis_question.questionCode}
                </Chip>
              </Link>
            ) : (
              <Chip variant="primary" className="text-xs">
                {gap.ecovadis_question.indicator} | {gap.ecovadis_question.questionCode}
              </Chip>
            )}
          </div>
        )}
        
        {/* Bottom CTA button */}
        <div className="mt-4">
          <Button 
            variant="outline" 
            size="sm" 
            className="text-sm"
            onClick={onFixGapClick}
          >
            How to fix this Gap
            <ChevronDown className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
