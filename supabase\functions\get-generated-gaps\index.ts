// @ts-expect-error Deno serve import
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { authValidator } from "../_shared/authValidator.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { USER_ROLE } from "../_shared/constants.ts";

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }

  try {
    const { user, error, supabaseClient, response: errResponse } = await authValidator(req);
    
    if (!user || error || !supabaseClient) {
      return errResponse;
    }

    // Check if user is Super Admin
    const userRole = user.user_workspace?.[0]?.role;
    if (userRole !== USER_ROLE.SuperAdmin) {
      return new Response(JSON.stringify({
        error: 'Unauthorized. Only Super Admins can access generated gaps.'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 403
      });
    }

    // Get filter parameters from query string
    const url = new URL(req.url);
    const status = url.searchParams.get('status');
    const projectId = url.searchParams.get('projectId');
    const questionId = url.searchParams.get('questionId');

    // Build query
    let query = supabaseClient
      .from('project_ecovadis_gap_generation')
      .select(`
        *,
        project:projectId (
          id,
          name
        ),
        ecovadis_question:questionId (
          id,
          questionCode,
          question,
          indicator,
          ecovadis_theme:themeId (
            id,
            title
          )
        ),
        reviewer:reviewedBy (
          id,
          name,
          email
        )
      `)
      .order('createdAt', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }
      
    // Filter by project if specified
    if (projectId) {
      query = query.eq('projectId', projectId);
    }

    // Filter by question if specified
    if (questionId) {
      query = query.eq('questionId', questionId);
    }

    const { data: generatedGaps, error: gapsError } = await query;

    if (gapsError) {
      console.error('Error fetching generated gaps:', gapsError);
      return new Response(JSON.stringify({
        error: 'Failed to fetch generated gaps'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }

    // Collect all document IDs from gaps
    const allDocumentIds: string[] = [];
    (generatedGaps || []).forEach(gap => {
      if (gap.documents && Array.isArray(gap.documents)) {
        allDocumentIds.push(...gap.documents);
      }
    });
    
    // Get unique document IDs
    const uniqueDocumentIds = [...new Set(allDocumentIds)].filter(Boolean);
    
    // Fetch document names for all gap documents
    let documentsMap = {};
    if (uniqueDocumentIds.length > 0) {
      const { data: documentsData, error: documentsError } = await supabaseClient
        .from('document')
        .select('id, name')
        .in('id', uniqueDocumentIds);
      
      if (documentsError) {
        console.error('Error fetching documents:', documentsError);
      } else {
        documentsMap = (documentsData || []).reduce((map, doc) => ({ ...map, [doc.id]: doc.name }), {});
      }
    }

    // Process gaps to include document information
    const processedGaps = (generatedGaps || []).map(gap => ({
      ...gap,
      // Map document IDs to document objects with name
      documents: Array.isArray(gap.documents) 
        ? gap.documents.map(docId => ({
            id: docId,
            name: documentsMap[docId] || 'Unknown Document',
          }))
        : []
    }));

    return new Response(JSON.stringify({
      gaps: processedGaps,
      count: processedGaps.length
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 200
    });

  } catch (error) {
    console.error('Error in get-generated-gaps function:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});
