# README: Migration Guide for Adding or Updating ESRS Disclosure Requirements

This document provides a step-by-step reference for creating or updating ESRS disclosure requirements (DRs) and associated data points (DPs) in our system. This includes the addition of new disclosure requirements, linking them to topics, inserting new ESRS datapoints, and ensuring that corresponding `data_request` and `datapoint_request` entries are correctly managed for all projects.

---

## 1. Overview

1. **esrs_disclosure_requirement (DR)**: Defines a disclosure requirement and its properties (e.g., identifier, sort order, related ESRS, name).
2. **esrs_topic_disclosure_requirement**: Links an ESRS topic (e.g., "G1") with the newly created or existing DR.
3. **esrs_datapoint**: Represents individual datapoints for a DR.
4. **data_request**: Ties a project to a specific DR, indicating that the project must fulfill the given DR.
5. **datapoint_request**: Ties each datapoint to the data request, indicating that these datapoints must be answered/filled by the project.

---

## 2. Steps

### 2.1. Insert or Update the Disclosure Requirement

When adding a new DR:

```sql
INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name)
VALUES (<NEW_DR_ID>, <SORT_ORDER>, '<DR_CODE>', '<ESRS_CODE>', '<DR_NAME>');
```

- **`<NEW_DR_ID>`**: Unique numeric ID for the new DR.
- **`<SORT_ORDER>`**: Sorting value within the topic.
- **`<DR_CODE>`**: Short label for the new DR (e.g., `G1.MDR`).
- **`<ESRS_CODE>`**: The ESRS identifier (e.g., `G1`, `E1`, etc.).
- **`<DR_NAME>`**: Descriptive name of the DR.

If updating an existing DR (for example, changing the name or ESRS code), you can do:

```sql
UPDATE esrs_disclosure_requirement
SET dr = '<NEW_DR_CODE>',
    esrs = '<NEW_ESRS_CODE>',
    name = '<NEW_DR_NAME>'
WHERE id = <EXISTING_DR_ID>;
```

### 2.2. Link the DR to the Relevant Topic (If Needed)

If a new DR is introduced and must be linked to a topic:

```sql
INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId")
VALUES (<TOPIC_ID>, <NEW_DR_ID>);
```

- **`<TOPIC_ID>`**: The ID of the ESRS topic (e.g., `10`).

### 2.3. Insert or Update ESRS Datapoints

If the new DR requires new datapoints or you need to update existing datapoints:

- **Inserting new datapoints**:

```sql
INSERT INTO esrs_datapoint ("datapointId", "esrsDisclosureRequirementId")
VALUES
('<DATAPOINT_CODE_1>', <NEW_DR_ID>),
('<DATAPOINT_CODE_2>', <NEW_DR_ID>);
```

- **Updating existing datapoints** to point to the new DR:

```sql
UPDATE esrs_datapoint
SET "esrsDisclosureRequirementId" = <NEW_DR_ID>
WHERE "datapointId" IN ('<DATAPOINT_CODE_1>', '<DATAPOINT_CODE_2>', ...);
```

### 2.4. Create New Data Requests for All Projects

A new `data_request` entry must be created for **every** project for the new DR. This ensures that each project now has a record to fulfill the new disclosure requirement.

```sql
INSERT INTO data_request("dataRequestTypeId", "dataRequestType", "status", "content", "projectId")
SELECT DISTINCT 
    <NEW_DR_ID> AS "dataRequestTypeId",
    'ESRS' AS "dataRequestType",
    'no_data'::data_request_status_enum AS "status",
    '' AS "content",
    dr."projectId"
FROM data_request dr
WHERE NOT EXISTS (
  SELECT 1 FROM data_request x
  WHERE x."projectId" = dr."projectId"
    AND x."dataRequestTypeId" = <NEW_DR_ID>
);
```

### 2.5. Update or Insert Datapoint Requests

1. **If the datapoint already existed under a different DR**, you may need to update existing `datapoint_request` records to reference the correct `dataRequestId`.

2. **If the datapoints are new**, you need to create new `datapoint_request` records for each `data_request` created in the previous step.

#### 2.5.1. Updating Datapoint Requests to Use the Newly Created `dataRequestId`

```sql
UPDATE datapoint_request dpr
SET "dataRequestId" = drNew.id
FROM data_request drOld
JOIN data_request drNew ON drNew."projectId" = drOld."projectId" AND drNew."dataRequestTypeId" = <NEW_DR_ID>
WHERE dpr."dataRequestId" = drOld.id
  AND dpr."esrsDatapointId" IN (
      SELECT ed.id
      FROM esrs_datapoint ed
      WHERE ed."esrsDisclosureRequirementId" = <NEW_DR_ID>
  );
```

#### 2.5.2. Inserting New Datapoint Requests (If Not Already Existing)

```sql
INSERT INTO datapoint_request("esrsDatapointId", "dataRequestId", "status", "content")
SELECT ed.id, dr.id, 'no_data', ''
FROM esrs_datapoint ed
JOIN data_request dr ON dr."dataRequestTypeId" = ed."esrsDisclosureRequirementId"
WHERE ed."esrsDisclosureRequirementId" = <NEW_DR_ID>
  AND NOT EXISTS (
      SELECT 1
      FROM datapoint_request dpr2
      WHERE dpr2."esrsDatapointId" = ed.id
        AND dpr2."dataRequestId" = dr.id
  );
```

---

## 3. Additional Notes

1. **Check for duplicates**: Always ensure you are not creating duplicate records. Use `WHERE NOT EXISTS` where appropriate.
2. **Transaction management**: Prefer running these updates within a single transaction to avoid partial updates if any step fails.
3. **Order of operations**: Perform insert/update operations in the sequence above to avoid foreign key or reference errors.
4. **Testing**: Always run these scripts in a staging or testing environment before deploying to production.

---

## 4. Example

Below is a concise example (without explanations) that creates a new DR `G1.MDR`, links it to a topic, updates existing datapoints, creates necessary data requests, and updates/inserts datapoint requests:

```sql
BEGIN;

-- 1) Create new DR
INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name)
VALUES (97, 97, 'G1.MDR', 'G1', 'Policies, Actions and Targets related to business conduct');

-- 2) Link DR to topic
INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId")
VALUES (10, 97);

-- 3) Update existing datapoints to reference new DR
UPDATE esrs_datapoint
SET "esrsDisclosureRequirementId" = 97
WHERE "datapointId" IN ('G1.MDR-P_01-06','G1.MDR-P_07-09','G1.MDR-A_01-12','G1.MDR-A_13-15');

-- 4) Create new DataRequest for each project if needed
INSERT INTO data_request("dataRequestTypeId","dataRequestType","status","content","projectId")
SELECT DISTINCT 97 as "dataRequestTypeId", 'ESRS' as "dataRequestType", 'no_data'::data_request_status_enum as "status", '' as "content", dr."projectId"
FROM data_request dr
WHERE NOT EXISTS (
    SELECT 1 FROM data_request x
    WHERE x."projectId" = dr."projectId"
      AND x."dataRequestTypeId" = 97
);

-- 5) Update datapoint_requests for the new DR
UPDATE datapoint_request dpr
SET "dataRequestId" = drNew.id
FROM data_request drOld
JOIN data_request drNew ON drNew."projectId" = drOld."projectId"
                       AND drNew."dataRequestTypeId" = 97
WHERE dpr."dataRequestId" = drOld.id
  AND dpr."esrsDatapointId" IN (
      SELECT ed.id
      FROM esrs_datapoint ed
      WHERE ed."esrsDisclosureRequirementId" = 97
  );

-- 6) Insert any missing datapoint_requests
INSERT INTO datapoint_request("esrsDatapointId", "dataRequestId", "status", "content")
SELECT ed.id, dr.id, 'no_data', ''
FROM esrs_datapoint ed
JOIN data_request dr ON dr."dataRequestTypeId" = ed."esrsDisclosureRequirementId"
WHERE ed."esrsDisclosureRequirementId" = 97
  AND NOT EXISTS (
      SELECT 1
      FROM datapoint_request dpr2
      WHERE dpr2."esrsDatapointId" = ed.id
        AND dpr2."dataRequestId" = dr.id
  );

COMMIT;
```
