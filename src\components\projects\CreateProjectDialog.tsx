
import React, { useState } from 'react';
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate } from 'react-router-dom';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { supabase } from "@/integrations/supabase/client";

import { toast } from '@/components/ui/use-toast';
import { ShimmerButton } from "@/components/ui/shimmer-button";

const formSchema = z.object({
  name: z.string().min(1, { message: 'Project name is required' }),
  type: z.string().min(1, { message: 'Project type is required' }),
  deadline: z.date().optional(),
});

type ProjectFormValues = z.infer<typeof formSchema>;

interface CreateProjectDialogProps {
  onProjectCreated?: (project: ProjectFormValues) => void;
  trigger?: React.ReactNode;
  buttonText?: string;
}

const CreateProjectDialog: React.FC<CreateProjectDialogProps> = ({ 
  onProjectCreated, 
  trigger,
  buttonText = "Create New Project"
}: CreateProjectDialogProps) => {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  
  const form = useForm<ProjectFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      type: "ecovadis",
    },
  });

  async function onSubmit(data: ProjectFormValues) {
    setIsLoading(true);
    
    try {
      // Call our edge function to create the project
      const { data: responseData, error } = await supabase.functions.invoke('create-project', {
        body: {
          name: data.name,
          type: data.type,
          deadline: data.deadline
        }
      });

      if (error) {
        console.error('Error creating project:', error);
        toast({
          title: 'Error creating project',
          description: error.message || 'Something went wrong. Please try again.',
          variant: 'destructive'
        });
        return;
      }

      if (onProjectCreated) {
        onProjectCreated(data);
      } else {
        // If no callback is provided, automatically navigate to the new project
        const project = responseData?.project;
        if (project?.id) {
          if (data.type === 'ecovadis') {
            navigate(`/ecovadis-project/${project.id}`);
          } else {
            navigate(`/projects/${project.id}`);
          }
        }
      }
      
      toast({
        title: 'Project created successfully',
        description: `${data.name} has been added to your dashboard.`,
      });
      
      setOpen(false);
      form.reset();
    } catch (error) {
      console.error('Error creating project:', error);
      toast({
        title: 'Error creating project',
        description: 'Something went wrong. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild id="create-project-trigger">
        {trigger || (
          <ShimmerButton 
            shimmerColor="#6DD4AD" 
            background="#143560" 
            className="font-medium"
          >
            {buttonText}
          </ShimmerButton>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-glacier-darkBlue">Create New Project</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            Fill in the details below to create a new sustainability project.
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 pt-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-glacier-darkBlue">Project Name</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter project name" 
                      {...field} 
                      className="border-input focus-visible:ring-glacier-mint"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <DialogFooter className="pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setOpen(false)}
                className="border-glacier-darkBlue text-glacier-darkBlue hover:bg-glacier-gray"
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                variant="darkBlue"
                className="bg-glacier-darkBlue text-white"
                disabled={isLoading}
              >
                {isLoading ? 'Creating...' : 'Create Project'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateProjectDialog;
