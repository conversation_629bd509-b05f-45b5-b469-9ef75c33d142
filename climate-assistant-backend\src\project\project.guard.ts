import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { ProjectService } from './project.service';

@Injectable()
export class ProjectGuard implements CanActivate {
  constructor(private readonly projectService: ProjectService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const projectId = request.params.projectId;
    const workspaceId = request.user.workspaceId;

    const project = await this.projectService.findById(projectId);

    if (project.workspaceId !== workspaceId) {
      throw new UnauthorizedException(`Project is not from this workspace`);
    }

    return true;
  }
}
