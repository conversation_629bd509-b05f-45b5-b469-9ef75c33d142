import { Repository } from 'typeorm';
import { DatapointRequestData, DataRequestData, GenerateDataRequestReportTextTextPayload, UpdateDataRequestPayload } from './entities/data-request.dto';
import { DataRequest, DataRequestStatus } from './entities/data-request.entity';
import { PromptService } from 'src/prompts/prompts.service';
import { ProjectService } from 'src/project/project.service';
import { Comment } from 'src/project/entities/comment.entity';
import { UsersService } from 'src/users/users.service';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { DatapointRequestService } from 'src/datapoint/datapoint-request.service';
import { Observable } from 'rxjs';
import { DatapointGenerationEvent } from './constants';
import { DataRequestGeneration, dataRequestGenerationStatus } from './entities/datarequest-generation.entity';
import { DatapointDataRequestSharedService } from 'src/shared/shared-datapoint-datarequest.service';
import { LlmRateLimiterService } from 'src/llm-rate-limiter/llm-rate-limiter.service';
export declare class DataRequestService {
    private readonly dataRequestRepository;
    private readonly promptService;
    private readonly projectService;
    private readonly userService;
    private readonly workspaceService;
    private readonly datapointRequestService;
    private readonly llmRateLimitService;
    private readonly datapointDataRequestSharedService;
    private readonly dataRequestGenerationRepository;
    constructor(dataRequestRepository: Repository<DataRequest>, promptService: PromptService, projectService: ProjectService, userService: UsersService, workspaceService: WorkspaceService, datapointRequestService: DatapointRequestService, llmRateLimitService: LlmRateLimiterService, datapointDataRequestSharedService: DatapointDataRequestSharedService, dataRequestGenerationRepository: Repository<DataRequestGeneration>);
    private readonly eventSubject;
    readonly events$: Observable<DatapointGenerationEvent>;
    private readonly logger;
    emitSseEvents(event: DatapointGenerationEvent): void;
    closeSseEvents(): void;
    findAll(projectId: string): Promise<DataRequest[]>;
    findProject(dataRequestId: string): Promise<DataRequest>;
    findById(dataRequestId: string): Promise<DataRequest>;
    findRelatedData(dataRequestId: string, userId?: string): Promise<DataRequestData>;
    update({ dataRequestId, updateDataRequestPayload, userId, workspaceId, event, }: {
        dataRequestId: string;
        updateDataRequestPayload: UpdateDataRequestPayload;
        userId?: string;
        workspaceId?: string;
        event?: string;
    }): Promise<DataRequest>;
    dataRequestStatusProcessor(dataRequest: DataRequestData): Promise<DataRequestStatus>;
    reviewDataRequestContentWithAI({ dataRequestId, userId, workspaceId, }: {
        dataRequestId: string;
        userId: string;
        workspaceId: string;
    }): Promise<Comment[]>;
    validateDataRequestGenerationRightsOrFail({ dataRequest, userId, }: {
        dataRequest: DataRequest;
        userId: string;
    }): Promise<boolean>;
    generateDataRequestTextContentWithAI({ dataRequestId, userId, workspaceId, additionalData, }: {
        dataRequestId: string;
        userId: string;
        workspaceId: string;
        additionalData: GenerateDataRequestReportTextTextPayload;
    }): Promise<{
        content: string;
        id: string;
    }>;
    findDatapointById(datapointRequestId: string): Promise<DatapointRequestData>;
    generateAllDatapointForDataRequest({ dataRequestId, userId, workspaceId, }: {
        dataRequestId: string;
        userId: string;
        workspaceId: string;
    }): Promise<void>;
    reviewAllDatapointForDataRequest({ dataRequestId, userId, workspaceId, }: {
        dataRequestId: string;
        userId: string;
        workspaceId: string;
    }): Promise<void>;
    setDatapointQueueStatusToNull(id: string): Promise<void>;
    getGenerations(dataRequestId: string): Promise<DataRequestGeneration[]>;
    updateGenerationStatus({ dataRequestGenerationId, status, userId, }: {
        dataRequestGenerationId: string;
        status: dataRequestGenerationStatus;
        userId: string;
        workspaceId: string;
    }): Promise<DataRequestGeneration>;
}
