import { DataSource, DataSourceOptions } from 'typeorm';
export declare const NODE_ENV: "development" | "production" | undefined;
export declare const isDevelopment: boolean;
export declare const isProduction: boolean;
export declare const getEnvFilePath: () => string;
export declare const getDBHost: () => string;
export declare const getRedisHost: () => string;
export declare const lovableAppRegex: RegExp;
export declare const createDataSourceWithVectorSupport: (options?: DataSourceOptions) => DataSource;
