-- Function to include workspace roles in JWT
CREATE OR REPLACE FUNCTION auth.jwt() RETURNS jsonb 
LANGUAGE sql STABLE SECURITY DEFINER 
AS $$
    SELECT 
    coalesce(
        nullif(current_setting('request.jwt.claim', true), ''),
        nullif(current_setting('request.jwt_claim', true), '')
    )::jsonb ||
    jsonb_build_object(
        'role', auth.role(),
        'aud', coalesce(
        nullif(current_setting('request.jwt.aud', true), ''),
        nullif(current_setting('request.jwt_aud', true), '')
        ),
        'email', (SELECT email FROM auth.users WHERE id = auth.uid()),
        'workspaces', (
        SELECT jsonb_object_agg("workspaceId", role)
        FROM public.user_workspace
        WHERE "userId" = auth.uid()
        )
    );
$$;
