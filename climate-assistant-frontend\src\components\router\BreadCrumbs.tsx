import React from 'react';
import { useLocation, Link } from 'react-router-dom';
import { match } from 'path-to-regexp';

import { RouteConfig, routeConfig } from './Routes';

import {
  Breadcrumb,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface BreadcrumbItemType {
  name: string;
  path: string;
}

interface BreadcrumbEllipsisItem {
  isEllipsis: true;
  items: BreadcrumbItemType[];
}

type BreadcrumbItemUnion = BreadcrumbItemType | BreadcrumbEllipsisItem;

export function Breadcrumbs() {
  const location = useLocation();
  const pathname = location.pathname;

  // Split the pathname into segments
  const pathnames = pathname.split('/').filter((x) => x);

  // Build cumulative paths for each segment
  const cumulativePaths = pathnames.map((_, index) => {
    return '/' + pathnames.slice(0, index + 1).join('/');
  });

  // Map cumulative paths to route names
  const crumbs: BreadcrumbItemType[] = cumulativePaths.map((path) => {
    // Find the route that matches this path
    const route = matchPathToRoute(path, routeConfig);
    const name =
      route && route.handle && route.handle.breadcrumb
        ? route.handle.breadcrumb
        : getLastSegment(path);

    return { name, path };
  });

  // Define the maximum number of visible breadcrumb items
  const MAX_VISIBLE_ITEMS = 4;

  let breadcrumbItems: BreadcrumbItemUnion[] = [];

  if (crumbs.length <= MAX_VISIBLE_ITEMS) {
    breadcrumbItems = crumbs;
  } else {
    // Keep the first and last items, collapse the middle ones into a dropdown
    breadcrumbItems = [
      crumbs[0],
      { isEllipsis: true, items: crumbs.slice(1, crumbs.length - 1) },
      crumbs[crumbs.length - 1],
    ];
  }

  // If on one of the base page dont know breadcrumbs
  if (!breadcrumbItems || breadcrumbItems.length < 2) {
    return <></>;
  }

  return (
    <Breadcrumb className="mb-6">
      <BreadcrumbList>
        {breadcrumbItems.map((crumb, index) => {
          if ('isEllipsis' in crumb && crumb.isEllipsis) {
            return (
              <React.Fragment key={`ellipsis-${index}`}>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <DropdownMenu>
                    <DropdownMenuTrigger className="flex items-center gap-1">
                      <BreadcrumbEllipsis className="h-4 w-4" />
                      <span className="sr-only">Toggle menu</span>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start">
                      {crumb.items.map((item) => (
                        <DropdownMenuItem key={item.path} asChild>
                          <Link to={item.path}>{item.name}</Link>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </BreadcrumbItem>
              </React.Fragment>
            );
          } else {
            const crumbItem = crumb as BreadcrumbItemType;
            return (
              <React.Fragment key={crumbItem.path}>
                {index !== 0 && <BreadcrumbSeparator />}
                <BreadcrumbItem>
                  {index < breadcrumbItems.length - 1 ? (
                    <Link to={crumbItem.path}>{crumbItem.name}</Link>
                  ) : (
                    <BreadcrumbPage>{crumbItem.name}</BreadcrumbPage>
                  )}
                </BreadcrumbItem>
              </React.Fragment>
            );
          }
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}

function matchPathToRoute(
  path: string,
  routes: RouteConfig[]
): RouteConfig | null {
  for (const route of routes) {
    const { path: routePath, children } = route;

    if (routePath && routePath !== '*') {
      // Ensure routePath and path both start with '/'
      const adjustedRoutePath = routePath.startsWith('/')
        ? routePath
        : '/' + routePath;
      const adjustedPath = path.startsWith('/') ? path : '/' + path;

      // Convert route path to match function
      const matcher = match(adjustedRoutePath, { decode: decodeURIComponent });
      const matched = matcher(adjustedPath);

      if (matched) {
        return route;
      }
    }

    if (children) {
      const matchedRoute = matchPathToRoute(path, children);
      if (matchedRoute) {
        return matchedRoute;
      }
    }
  }
  return null;
}

function getLastSegment(path: string): string {
  const segments = path.split('/').filter((x) => x);
  return segments[segments.length - 1];
}
