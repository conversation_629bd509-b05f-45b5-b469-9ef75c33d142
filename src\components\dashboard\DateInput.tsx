import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';

import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

export function DatePicker({ date, setDate }: { date: Date; setDate: any }) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <span className="flex items-center space-x-2 cursor-pointer">
          <span className="font-medium text-sm">
            {date ? format(date, 'PPP') : 'Pick a date'}
          </span>
          <CalendarIcon className="mr-2 h-4 w-4" />
        </span>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          selected={date}
          onSelect={setDate}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}
