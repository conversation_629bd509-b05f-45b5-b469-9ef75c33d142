import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getWorkspaceGaps, updateEcovadisGap, deleteEcovadisGap, assignGapToUser } from '@/api/ecovadis/ecovadis.api';
import { GapItem, WorkspaceGap } from '@/types/ecovadis';
import { useToast } from '@/components/ui/use-toast';
import { useParams } from 'react-router-dom';

export const useWorkspaceGaps = (questionId: string | null) => {
  const { id: projectId } = useParams<{ id: string }>();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Convert workspace gaps to the GapItem format used in the UI
  const mapWorkspaceGapsToGapItems = (workspaceGaps: WorkspaceGap[]): GapItem[] => {
    return workspaceGaps.map(gap => ({
      id: gap.id,
      questionId: gap.questionId,
      projectId: gap.projectId,
      title: gap.gaps?.Title || '',
      description: gap.gaps?.Description || '',
      relatedDocument: gap.gaps?.["Related Document"] || [],
      questionCode: gap.questionCode,
      projectName: gap.projectName,
      topic: gap.questionName || '',
      assignedTo: gap.assigneeId,
      resolved: gap.resolved || false,
      status: gap.status,
      type: gap.type || 'evidence_gap',
      gaps: gap.gaps,
      documents: gap.documents,
      ecovadis_question: gap.ecovadis_question,
      createdAt: gap.createdAt,
    }));
  };

  // Use React Query for fetching gaps
  const { data: gaps = [], isLoading, error, refetch } = useQuery({
    queryKey: ['workspaceGaps', questionId],
    queryFn: async () => {
      const gapsData = await getWorkspaceGaps(questionId, projectId);
      return mapWorkspaceGapsToGapItems(gapsData);
    },
  });

  // Delete gap mutation
  const deleteGapMutation = useMutation({
    mutationFn: deleteEcovadisGap,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspaceGaps'] });
      toast({
        variant: 'success',
        description: 'Gap deleted successfully.'
      });
    },
    onError: () => {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to delete gap. Please try again.'
      });
    },
  });

  const handleDeleteGap = async (gapId: string) => {
    try {
      await deleteGapMutation.mutateAsync(gapId);
      queryClient.invalidateQueries({ queryKey: ['workspaceGaps'] });
      return true;
    } catch (err) {
      return false;
    }
  };

  // Mark gap complete mutation with optimistic updates
  const markGapCompleteMutation = useMutation({
    mutationFn: ({ gapId, isComplete }: { gapId: string; isComplete: boolean }) => 
      updateEcovadisGap(gapId, isComplete),
    onMutate: async ({ gapId, isComplete }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['workspaceGaps', questionId] });
      
      // Snapshot the previous value
      const previousGaps = queryClient.getQueryData(['workspaceGaps', questionId]);
      
      // Optimistically update the cache
      queryClient.setQueryData(['workspaceGaps', questionId], (old: GapItem[] | undefined) => {
        if (!old) return old;
        return old.map(gap => 
          gap.id === gapId 
            ? { ...gap, resolved: isComplete, isComplete }
            : gap
        );
      });
      
      return { previousGaps };
    },
    onError: (err, { gapId }, context) => {
      // Rollback to previous state on error
      if (context?.previousGaps) {
        queryClient.setQueryData(['workspaceGaps', questionId], context.previousGaps);
      }
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to update gap status. Please try again.'
      });
    },
    onSuccess: () => {
      // Optionally show success message and ensure data consistency
      toast({
        variant: 'success',
        description: 'Gap status updated successfully.'
      });
    },
  });

  const handleMarkGapComplete = async (gapId: string, isComplete: boolean): Promise<void> => {
    await markGapCompleteMutation.mutateAsync({ gapId, isComplete });
  }

  const handleAssignGap = async (gapId: string, userId: string | null): Promise<void> => {
    try {
      await assignGapToUser(gapId, userId);
      queryClient.invalidateQueries({ queryKey: ['workspaceGaps'] });
      toast({
        variant: 'default',
        title: 'Success',
        description: userId ? 'Gap assigned successfully.' : 'Gap unassigned successfully.'
      });
    } catch (err) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to assign gap. Please try again.'
      });
    }
  }

  const refetchGaps = async () => {
    await queryClient.invalidateQueries({ queryKey: ['workspaceGaps', questionId] });
    return refetch();
  };

  return {
    gaps,
    isLoading,
    error,
    refetchGaps,
    handleDeleteGap,
    handleMarkGapComplete,
    handleAssignGap
  };
};
