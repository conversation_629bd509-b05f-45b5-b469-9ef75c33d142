"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CronService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CronService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const bull_1 = require("@nestjs/bull");
const typeorm_1 = require("@nestjs/typeorm");
const env_helper_1 = require("../env-helper");
const typeorm_2 = require("typeorm");
const document_entity_1 = require("../document/entities/document.entity");
const jobs_1 = require("../types/jobs");
let CronService = CronService_1 = class CronService {
    constructor(chunkExtractionQueue, chunkLinkingQueue, documentRepository) {
        this.chunkExtractionQueue = chunkExtractionQueue;
        this.chunkLinkingQueue = chunkLinkingQueue;
        this.documentRepository = documentRepository;
        this.logger = new common_1.Logger(CronService_1.name);
    }
    async checkUnprocessedDocuments() {
        this.logger.log('Checking for unprocessed documents...');
        const includedStatuses = [
            document_entity_1.DocumentStatus.NotProcessed,
            document_entity_1.DocumentStatus.ExtractingData,
            document_entity_1.DocumentStatus.DataExtractionFinished,
            document_entity_1.DocumentStatus.LinkingData,
        ];
        const unprocessedDocuments = await this.documentRepository.find({
            where: {
                status: (0, typeorm_2.In)(includedStatuses),
                createdAt: (0, typeorm_2.LessThan)(new Date(Date.now() - 10 * 60 * 1000)),
            },
        });
        for (const document of unprocessedDocuments) {
            this.logger.log(`Processing document: ${document.id}`);
            try {
                switch (document.status) {
                    case document_entity_1.DocumentStatus.NotProcessed:
                    case document_entity_1.DocumentStatus.ExtractingData:
                        this.logger.log(`Adding document to extraction queue: ${document.id}`);
                        await this.chunkExtractionQueue.add(jobs_1.JobQueue.ChunkExtract, {
                            documentId: document.id,
                        }, {
                            jobId: `chunkExtraction-${document.id}`,
                            attempts: 5,
                            backoff: {
                                type: 'exponential',
                                delay: 5000,
                            },
                            removeOnComplete: env_helper_1.isDevelopment ? false : true,
                        });
                        this.documentRepository.update(document.id, {
                            status: document_entity_1.DocumentStatus.QueuedForExtraction,
                        });
                        break;
                    case document_entity_1.DocumentStatus.DataExtractionFinished:
                    case document_entity_1.DocumentStatus.LinkingData:
                        this.logger.log(`Adding document to linking queue: ${document.id}`);
                        this.documentRepository.update(document.id, {
                            status: document_entity_1.DocumentStatus.LinkingDataFinished,
                        });
                        break;
                    default:
                        this.logger.log(`Invalid status for document: ${document.id}: ${document.status}`);
                        break;
                }
            }
            catch (error) {
                this.logger.error(error);
            }
        }
    }
};
exports.CronService = CronService;
__decorate([
    (0, schedule_1.Cron)('*/10 * * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronService.prototype, "checkUnprocessedDocuments", null);
exports.CronService = CronService = CronService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, bull_1.InjectQueue)(jobs_1.JobProcessor.ChunkExtraction)),
    __param(1, (0, bull_1.InjectQueue)(jobs_1.JobProcessor.ChunkDpLinking)),
    __param(2, (0, typeorm_1.InjectRepository)(document_entity_1.Document)),
    __metadata("design:paramtypes", [Object, Object, typeorm_2.Repository])
], CronService);
//# sourceMappingURL=cron.service.js.map