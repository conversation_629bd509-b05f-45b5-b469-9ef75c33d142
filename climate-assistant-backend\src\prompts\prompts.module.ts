import { Module } from '@nestjs/common';
import { PromptService } from './prompts.service';
import { MDRPromptService } from './mdr-prompts.service';
import { NumericsPromptService } from './numerics-prompts.service';
import { NormalDpPromptService } from './datapoint-generation-prompts.service';
import { TablePromptService } from './table-prompts.service';

@Module({
  imports: [],
  providers: [
    PromptService,
    MDRPromptService,
    NumericsPromptService,
    TablePromptService,
    NormalDpPromptService,
  ],
  exports: [
    PromptService,
    MDRPromptService,
    NumericsPromptService,
    TablePromptService,
    NormalDpPromptService,
  ],
  controllers: [],
})
export class PromptModule {}
