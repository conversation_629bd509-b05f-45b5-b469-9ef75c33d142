
import * as React from "react";
import { VariantProps, cva } from "class-variance-authority";
import { cn } from "@/lib/utils";

const chipVariants = cva(
  "inline-flex items-center rounded-md px-2 py-1 text-xs font-medium",
  {
    variants: {
      variant: {
        default: "bg-gray-100 text-gray-700",
        blue: "bg-blue-100 text-blue-700",
        green: "bg-green-100 text-green-700",
        red: "bg-red-100 text-red-700",
        yellow: "bg-yellow-100 text-yellow-700",
        orange: "bg-orange-100 text-orange-700",
        purple: "bg-purple-100 text-purple-700",
        primary: "bg-glacier-darkBlue/10 text-glacier-darkBlue border border-glacier-darkBlue/20",
        grey: "bg-[#F1F1F1] text-glacier-darkBlue border border-gray-200",
        success: "bg-[#38A169]/10 text-[#38A169] border border-[#38A169]/20",
      },
      filled: {
        true: "",
        false: "",
      }
    },
    compoundVariants: [
      {
        variant: "primary",
        filled: true,
        className: "bg-glacier-darkBlue text-white border-none",
      },
      {
        variant: "blue",
        filled: true,
        className: "bg-blue-600 text-white",
      },
      {
        variant: "green",
        filled: true,
        className: "bg-green-600 text-white",
      },
      {
        variant: "red",
        filled: true,
        className: "bg-red-600 text-white",
      },
      {
        variant: "yellow",
        filled: true,
        className: "bg-yellow-600 text-white",
      },
      {
        variant: "orange",
        filled: true,
        className: "bg-orange-600 text-white",
      },
      {
        variant: "purple",
        filled: true,
        className: "bg-purple-600 text-white",
      },
      {
        variant: "grey",
        filled: true,
        className: "bg-[#718096] text-white border-none",
      },
      {
        variant: "success",
        filled: true,
        className: "bg-[#38A169] text-white border-none",
      },
    ],
    defaultVariants: {
      variant: "default",
      filled: false,
    },
  }
);

export interface ChipProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof chipVariants> {
  icon?: React.ReactNode;
  dismissible?: boolean;
  onDismiss?: () => void;
}

export const Chip = React.forwardRef<HTMLDivElement, ChipProps>(
  ({ className, variant, filled, icon, dismissible, onDismiss, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(chipVariants({ variant, filled }), className)}
        {...props}
      >
        {icon && <span className="mr-1">{icon}</span>}
        {children}
        {dismissible && (
          <button
            type="button"
            className="ml-1 -mr-1 h-3.5 w-3.5 rounded-full text-current opacity-60 hover:bg-gray-200 hover:opacity-100"
            onClick={onDismiss}
            aria-hidden="true"
          >
            <span className="sr-only">Dismiss</span>
            <svg
              className="h-3.5 w-3.5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        )}
      </div>
    );
  }
);

Chip.displayName = "Chip";
