"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isTextPresentInHTML = isTextPresentInHTML;
exports.chunkArray = chunkArray;
exports.isRequestForDataGenerationType = isRequestForDataGenerationType;
exports.getGenerationIdFromRequestId = getGenerationIdFromRequestId;
exports.parsePageRanges = parsePageRanges;
exports.addPageRangeBuffer = addPageRangeBuffer;
exports.validatePageNumbersAgainstDocument = validatePageNumbersAgainstDocument;
function isTextPresentInHTML(htmlString) {
    if (!htmlString)
        return false;
    const textContent = htmlString.replace(/<[^>]*>/g, '').trim();
    return textContent.length > 0;
}
function chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
}
function isRequestForDataGenerationType(requestId) {
    return requestId.endsWith('-generation');
}
function getGenerationIdFromRequestId(requestId) {
    return requestId.split('-generation')[0];
}
function parsePageRanges(pageRanges) {
    const pages = new Set();
    pageRanges.split(',').forEach((range) => {
        const [start, end] = range.trim().split('-').map(Number);
        if (end) {
            for (let i = start; i <= end; i++) {
                pages.add(i);
            }
        }
        else {
            pages.add(start);
        }
    });
    return Array.from(pages).sort((a, b) => a - b);
}
function addPageRangeBuffer(pages, maxPage) {
    if (pages.length === 0) {
        return pages;
    }
    const minPage = Math.min(...pages);
    const maxPageInRange = Math.max(...pages);
    const bufferStart = Math.max(1, minPage - 2);
    const bufferEnd = maxPage
        ? Math.min(maxPage, maxPageInRange + 2)
        : maxPageInRange + 2;
    const bufferedPages = [];
    for (let i = bufferStart; i <= bufferEnd; i++) {
        bufferedPages.push(i);
    }
    return bufferedPages;
}
async function validatePageNumbersAgainstDocument(supabase, documentId, pageNumbers) {
    if (pageNumbers.length === 0) {
        return pageNumbers;
    }
    const { data: maxPageData } = await supabase
        .from('document_chunk')
        .select('page')
        .eq('documentId', documentId)
        .order('page', { ascending: false })
        .limit(1);
    if (maxPageData) {
        const maxPageInDocument = parseInt(maxPageData.page.split('-').pop() || maxPageData.page);
        const validPageNumbers = pageNumbers.filter((page) => page <= maxPageInDocument);
        return validPageNumbers;
    }
    return pageNumbers;
}
//# sourceMappingURL=common-util.js.map