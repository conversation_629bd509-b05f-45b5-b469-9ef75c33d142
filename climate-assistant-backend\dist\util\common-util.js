"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isTextPresentInHTML = isTextPresentInHTML;
exports.chunkArray = chunkArray;
exports.isRequestForDataGenerationType = isRequestForDataGenerationType;
exports.getGenerationIdFromRequestId = getGenerationIdFromRequestId;
exports.parsePageRanges = parsePageRanges;
function isTextPresentInHTML(htmlString) {
    if (!htmlString)
        return false;
    const textContent = htmlString.replace(/<[^>]*>/g, '').trim();
    return textContent.length > 0;
}
function chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
}
function isRequestForDataGenerationType(requestId) {
    return requestId.endsWith('-generation');
}
function getGenerationIdFromRequestId(requestId) {
    return requestId.split('-generation')[0];
}
function parsePageRanges(pageRanges) {
    const pages = new Set();
    pageRanges.split(',').forEach((range) => {
        const [start, end] = range.trim().split('-').map(Number);
        if (end) {
            for (let i = start; i <= end; i++) {
                pages.add(i);
            }
        }
        else {
            pages.add(start);
        }
    });
    return Array.from(pages).sort((a, b) => a - b);
}
//# sourceMappingURL=common-util.js.map