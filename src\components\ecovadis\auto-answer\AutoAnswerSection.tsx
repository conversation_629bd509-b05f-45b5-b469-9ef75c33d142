
import { useState, useEffect } from "react";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Bo<PERSON>, Info, Trash2 } from "lucide-react";
import { EcovadisQuestion, QuestionOption } from "@/types/ecovadis";
import { toast } from "sonner";
import { fireConfetti } from "@/lib/confetti";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ShimmerButton } from "@/components/ui/shimmer-button";
import { useUserRole } from "@/hooks/useUserRole";
import { USER_ROLE } from "@/constants/workspaceConstants";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";

interface AutoAnswerSectionProps {
  question: EcovadisQuestion;
  onUpdateQuestion: (updatedQuestion: EcovadisQuestion) => void;
  onOpenScoreInfo?: () => void;
  onAnsweringComplete?: () => void;
}

export const AutoAnswerSection = ({
  question,
  onUpdateQuestion,
  onOpenScoreInfo,
  onAnsweringComplete
}: AutoAnswerSectionProps) => {
  const [isAnswering, setIsAnswering] = useState(false);
  const [progress, setProgress] = useState(0);
  const [stage, setStage] = useState("");
  const [documentsAttached, setDocumentsAttached] = useState(false);
  const { hasRole } = useUserRole();
  const isSuperAdmin = hasRole([USER_ROLE.SuperAdmin]);

  const handleAutoAnswer = () => {
    if (!isSuperAdmin) return;
    
    setIsAnswering(true);
    setProgress(0);
    setStage("Searching for evidence documents");
    
    setTimeout(() => {
      setProgress(30);
      setStage("Analyzing documents for relevant evidence");
      
      setTimeout(() => {
        setProgress(60);
        setStage("Deriving the best answers based on evidence");
        
        setTimeout(() => {
          setProgress(90);
          setStage("Attaching documents with relevant page numbers");
          
          const updatedOptions = question.options?.map((option, index) => {
            if (question.topic === "👥 Labor & Human Rights") {
              // Employee Health and Safety
              if ((index === 0 || index === 1) && (
                question.name?.toLowerCase().includes("health and safety") || 
                question.name?.toLowerCase().includes("workplace safety") || 
                question.name?.toLowerCase().includes("safety") ||
                option.text.toLowerCase().includes("employee health and safety") ||
                option.text.toLowerCase().includes("safety measures")
              )) {
                return {
                  ...option,
                  selected: true,
                  attachedDocuments: [
                    {
                      id: "auto-doc-ehs1",
                      name: "Glacier Safety Training Overview",
                      pages: "Full document",
                      comment: "Outlines mandatory safety training and procedures for all employees, including emergency response protocols and workplace safety expectations"
                    },
                    {
                      id: "auto-doc-ehs2",
                      name: "Whistleblower Policy_Glacier",
                      pages: "2",
                      comment: "Mentions health and safety-related violations as part of the reporting scope, ensuring employees can confidentially report unsafe conditions"
                    },
                    {
                      id: "auto-doc-ehs3",
                      name: "Breastfeeding Policy_Glacier",
                      pages: "1",
                      comment: "Ensures safe and hygienic facilities for breastfeeding, supporting workplace well-being for parents"
                    }
                  ]
                };
              } 
              // Default labor rights policy if no specific match and is first option
              else if (index === 0 && !option.selected) {
                return {
                  ...option,
                  selected: true,
                  attachedDocuments: [
                    {
                      id: "auto-doc18",
                      name: "Code of ethics guide_Glacier",
                      pages: "8-15",
                      comment: "Contains our comprehensive labor and human rights policies"
                    },
                    {
                      id: "auto-doc19",
                      name: "Responsibility Report 2023",
                      pages: "40-48",
                      comment: "Details our labor rights practices, commitments and achievements"
                    }
                  ]
                };
              }
            } else {
              if (index === 0 && !option.selected) {
                return {
                  ...option,
                  selected: true,
                  attachedDocuments: [{
                    id: "auto-doc1",
                    name: "Sustainability Report 2023.pdf",
                    pages: "42-45",
                    comment: "Contains relevant information about our certifications"
                  }]
                };
              }
            }
            return option;
          });
          
          const updatedQuestion = {
            ...question,
            options: updatedOptions
          };
          
          onUpdateQuestion(updatedQuestion);
          setProgress(100);
          setStage("Complete! Question has been answered");
          
          setTimeout(() => {
            setIsAnswering(false);
            setDocumentsAttached(true);
            toast.success("AI has auto-answered this question! ⚡️", {
              description: "Options selected and documents have been attached with relevant page numbers."
            });
            fireConfetti();
            if (onAnsweringComplete) {
              onAnsweringComplete();
            }
          }, 1500);
        }, 3000);
      }, 3000);
    }, 3000);
  };

  return (
    <div className="mt-4 my-0">
      {isAnswering ? (
        <div className="p-4 bg-glacier-mint/10 border border-glacier-mint/30 rounded-lg">
          <div className="flex items-center gap-3 mb-2">
            <Bot className="h-5 w-5 text-glacier-darkBlue animate-pulse" />
            <h3 className="text-glacier-darkBlue font-semibold">AI is auto-answering this question</h3>
          </div>
          <p className="text-sm text-gray-600 mb-3">{stage}</p>
          <Progress value={progress} className="h-2 bg-gray-200" />
        </div>
      ) : (
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            <div>
              <Button 
                onClick={isSuperAdmin ? handleAutoAnswer : undefined} 
                className="bg-glacier-darkBlue hover:bg-glacier-darkBlue/90 flex items-center gap-2 mt-4"
                disabled={!isSuperAdmin}
              >
                <Bot className="h-4 w-4" />
                AI Auto-Answer
              </Button>
            </div>
          </TooltipTrigger>
          {!isSuperAdmin && (
            <TooltipContent side="bottom">
              <p>Only Super Admins can use AI Auto-Answer</p>
            </TooltipContent>
          )}
        </Tooltip>
      )}

      {documentsAttached && !isAnswering && (
        <div className="mt-4 p-4 border border-green-200 rounded-lg bg-green-50">
          <h4 className="text-green-700 font-medium mb-2 flex items-center gap-2">
            <Info className="h-4 w-4" />
            Documents have been attached
          </h4>
          <p className="text-sm text-gray-600 mb-3">
            The AI has selected appropriate options and attached relevant documents with page references.
          </p>
          <div className="space-y-2">
            {question.options?.filter(option => option.selected)?.map(option => (
              <div key={option.id} className="text-sm">
                <p className="font-medium text-gray-700">{option.text}</p>
                <div className="ml-2 mt-1 space-y-1">
                  {option.attachedDocuments?.map(doc => (
                    <div key={doc.id} className="flex items-center justify-between p-2 bg-white rounded border border-gray-200">
                      <div>
                        <p className="font-medium">{doc.name}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-gray-500">Pages: {doc.pages}</span>
                          <Input 
                            type="text" 
                            placeholder="Page number" 
                            defaultValue={doc.pages}
                            className="h-6 w-24 text-xs"
                          />
                        </div>
                        {doc.comment && (
                          <p className="text-xs text-gray-500 mt-1">{doc.comment}</p>
                        )}
                      </div>
                      <Button size="sm" variant="ghost" className="p-1 h-auto">
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
