"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulkReindexDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class BulkReindexDto {
}
exports.BulkReindexDto = BulkReindexDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of document IDs to reindex. If not provided, workspaceId must be specified.',
        required: false,
        type: [String],
        example: ['doc-123', 'doc-456'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ValidateIf)((o) => !o.workspaceId || o.documentIds),
    __metadata("design:type", Array)
], BulkReindexDto.prototype, "documentIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Workspace ID to reindex all documents within. If not provided, documentIds must be specified.',
        required: false,
        type: String,
        example: 'workspace-789',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.ValidateIf)((o) => !o.documentIds || o.workspaceId),
    __metadata("design:type", String)
], BulkReindexDto.prototype, "workspaceId", void 0);
//# sourceMappingURL=bulk-reindex.dto.js.map