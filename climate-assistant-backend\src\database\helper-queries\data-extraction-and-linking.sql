
# Get files that are not linked yet
SELECT count(*), "documentId" FROM document_chunk where "matchingsJson"='' GROUP BY("documentId") LIMIT 100


# Get finished links 
Select esrs_datapoint."datapointId", content from esrs_datapoint cross join document_chunk where document_chunk."matchingsJson" like '%' || esrs_datapoint."datapointId" ||'%'

Select esrs_disclosure_requirement.dr, content from esrs_disclosure_requirement cross join document_chunk where document_chunk."matchingsJson" like '%' || esrs_disclosure_requirement.dr ||'%'



# Insert all Datapoints

insert INTO data_request Select from esrs_disclosure_requirement