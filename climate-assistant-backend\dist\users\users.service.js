"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("./entities/user.entity");
const user_prompt_context_entity_1 = require("./entities/user-prompt-context.entity");
const workspace_entity_1 = require("../workspace/entities/workspace.entity");
const user_workspace_entity_1 = require("./entities/user-workspace.entity");
const company_entity_1 = require("../workspace/entities/company.entity");
const token_entity_1 = require("./entities/token.entity");
const email_service_1 = require("../external/email.service");
const prompt_context_generation_service_1 = require("./prompt-context-generation.service");
const workspace_service_1 = require("../workspace/workspace.service");
const bcrypt = require("bcrypt");
const config_1 = require("../util/config");
let UsersService = class UsersService {
    constructor(userRepository, tokenRepository, userPromptContextRepository, companyRepository, workspaceRepository, userWorkspaceRepository, emailService, promptContextGenerationService, workspaceService) {
        this.userRepository = userRepository;
        this.tokenRepository = tokenRepository;
        this.userPromptContextRepository = userPromptContextRepository;
        this.companyRepository = companyRepository;
        this.workspaceRepository = workspaceRepository;
        this.userWorkspaceRepository = userWorkspaceRepository;
        this.emailService = emailService;
        this.promptContextGenerationService = promptContextGenerationService;
        this.workspaceService = workspaceService;
        this.GLOBAL_AI_USER_UUID = config_1.GLOBAL_AI_USER_UUID;
        this.GLOBAL_AI_USER_EMAIL = '<EMAIL>';
        this.GLOBAL_AI_USER_NAME = 'Glacier AI';
    }
    async findById(id) {
        return this.userRepository.findOne({
            where: { id },
            relations: ['userWorkspaces'],
        });
    }
    async findByEmailWithPassword(email) {
        return this.userRepository.findOne({
            where: { email: email.toLowerCase() },
            select: ['id', 'name', 'email', 'password'],
        });
    }
    async findByEmail(email) {
        return this.userRepository.findOne({
            where: { email: email.toLowerCase() },
        });
    }
    async getUserPromptContext(userId) {
        const userPromptSettings = await this.userPromptContextRepository.findOne({
            where: { userId },
        });
        return userPromptSettings?.context ?? '';
    }
    async saveUserPromptContext(userId, context) {
        await this.userPromptContextRepository.upsert({ context, userId }, [
            'userId',
        ]);
    }
    async getGeneratedPromptContext(userId) {
        const generatedContext = this.promptContextGenerationService.generatePromptContext(userId);
        return generatedContext;
    }
    async createUser(email) {
        const existingUser = await this.findByEmail(email.toLowerCase());
        if (existingUser) {
            throw new common_1.ConflictException('User with this email already exists');
        }
        const user = this.userRepository.create({
            email: email.toLowerCase(),
        });
        await this.userRepository.save(user);
        return user;
    }
    async createUserWithCompanyAndWorkspace(createUserDto) {
        const { email, password, companyName } = createUserDto;
        const existingUser = await this.findByEmail(email);
        if (existingUser) {
            throw new common_1.ConflictException('User with this email already exists');
        }
        const workspace = this.workspaceRepository.create({
            name: companyName,
        });
        await this.workspaceRepository.save(workspace);
        const company = this.companyRepository.create({
            name: companyName,
            workspace: workspace,
        });
        await this.companyRepository.save(company);
        const hashedPassword = await bcrypt.hash(password, 10);
        const user = this.userRepository.create({
            name: companyName,
            email: email.toLowerCase(),
            password: hashedPassword,
        });
        await this.userRepository.save(user);
        const userWorkspace = this.userWorkspaceRepository.create({
            user,
            workspace,
            role: user_workspace_entity_1.Role.Contributor,
        });
        await this.userWorkspaceRepository.save(userWorkspace);
        return {
            user,
            company,
            workspace,
        };
    }
    async findFirstWorkspaceIdByUser(user) {
        const userWorkspace = await this.userWorkspaceRepository.findOne({
            where: { user: user },
        });
        if (!userWorkspace) {
            let name = user.name;
            if (user.name === null) {
                name = user.email.split('@')[0];
                await this.userRepository.update(user.id, { name: name });
            }
            const workspace = this.workspaceRepository.create({
                name: name,
            });
            await this.workspaceRepository.save(workspace);
            const company = this.companyRepository.create({
                name: name,
                workspace: workspace,
            });
            await this.companyRepository.save(company);
            const userWorkspace = this.userWorkspaceRepository.create({
                user,
                workspace,
                role: user_workspace_entity_1.Role.Contributor,
            });
            await this.userWorkspaceRepository.save(userWorkspace);
            return {
                companyId: company.id,
                workspaceId: workspace.id,
            };
        }
        const company = await this.companyRepository.findOne({
            where: { workspace: userWorkspace.workspace },
        });
        return {
            companyId: company.id,
            workspaceId: userWorkspace.workspaceId,
        };
    }
    async sendPasswordResetEmail({ email, origin, shouldSendEmail, }) {
        const user = await this.userRepository.findOne({
            where: { email: email.toLowerCase() },
            relations: ['userWorkspaces'],
        });
        if (!user) {
            throw new common_1.UnprocessableEntityException('User not found');
        }
        const resetToken = crypto.randomUUID();
        await this.tokenRepository.delete({
            user: user,
            type: token_entity_1.TokenType.PasswordReset,
        });
        const store = await this.tokenRepository.create({
            token: resetToken,
            user,
            type: token_entity_1.TokenType.PasswordReset,
            expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24),
        });
        await this.tokenRepository.save(store);
        await this.emailService.sendPasswordReset({
            email,
            userName: user.name,
            resetToken,
            origin,
        });
        try {
            await this.workspaceService.storeActionHistory({
                event: 'password_reset_requested',
                ref: String(store.userId),
                workspaceId: user.userWorkspaces[0].workspaceId,
                versionData: {
                    event: 'password_reset_requested',
                    doneBy: user.id,
                    data: 'password reset request initiated',
                },
            });
        }
        catch (err) {
            console.log(err);
        }
    }
    async validateToken(token) {
        const userToken = await this.tokenRepository.findOne({
            where: { token },
            relations: ['user'],
        });
        if (!userToken?.expiresAt ||
            new Date(userToken.expiresAt).getTime() < new Date().getTime()) {
            throw new common_1.ConflictException('Token Expired');
        }
        return userToken;
    }
    async resetUserPassword(userToken, password) {
        const hashedPassword = await bcrypt.hash(password, 10);
        await this.userRepository.update({ id: userToken.user.id }, { password: hashedPassword });
        const user = await this.userRepository.findOne({
            where: { id: userToken.user.id },
            relations: ['userWorkspaces'],
        });
        try {
            await this.tokenRepository.delete({
                user: user,
                type: (0, typeorm_2.In)([token_entity_1.TokenType.PasswordReset, token_entity_1.TokenType.WorkspaceInvite]),
            });
            await this.workspaceService.storeActionHistory({
                event: 'password_update_success',
                ref: String(userToken.userId),
                workspaceId: user.userWorkspaces[0].workspaceId,
                versionData: {
                    event: 'password_update_success',
                    doneBy: user.id,
                    data: 'password reset successful',
                },
            });
        }
        catch (err) {
            console.log(err);
        }
        return user;
    }
    async findGlobalGlacierAIUser() {
        return await this.userRepository.findOne({
            where: {
                id: this.GLOBAL_AI_USER_UUID,
                email: this.GLOBAL_AI_USER_EMAIL,
                name: this.GLOBAL_AI_USER_NAME,
            },
        });
    }
    async userHasRequiredRole(userOrId, role) {
        let user;
        if (typeof userOrId === 'string') {
            user = await this.findById(userOrId);
        }
        else {
            user = userOrId;
        }
        if (role.includes(user.userWorkspaces[0].role)) {
            return true;
        }
        return false;
    }
    async userHasRequiredRoleOrFail({ userOrId, role, message, }) {
        let user;
        if (typeof userOrId === 'string') {
            user = await this.findById(userOrId);
        }
        else {
            user = userOrId;
        }
        if (role.includes(user.userWorkspaces[0].role)) {
            return true;
        }
        throw new common_1.ForbiddenException(message || 'You do not have the right permissions');
    }
    async switchWorkspace(userId, workspaceId) {
        await this.userWorkspaceRepository.update({ userId }, { workspaceId });
        return this.findById(userId);
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(token_entity_1.Token)),
    __param(2, (0, typeorm_1.InjectRepository)(user_prompt_context_entity_1.UserPromptContext)),
    __param(3, (0, typeorm_1.InjectRepository)(company_entity_1.Company)),
    __param(4, (0, typeorm_1.InjectRepository)(workspace_entity_1.Workspace)),
    __param(5, (0, typeorm_1.InjectRepository)(user_workspace_entity_1.UserWorkspace)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        email_service_1.EmailService,
        prompt_context_generation_service_1.PromptContextGenerationService,
        workspace_service_1.WorkspaceService])
], UsersService);
//# sourceMappingURL=users.service.js.map