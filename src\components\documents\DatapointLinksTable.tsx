import { ColumnDef } from '@tanstack/react-table';
import { Link2Icon, Link2OffIcon } from 'lucide-react';

import { DataTableMin } from '../data-table/DataTableMin';
import { Button } from '../ui/button';

import { ESRSDatapoint } from '@/types';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ESRS_TOPIC_SEGMENTS } from '@/lib/config';
import { DatapointRequestLinkage } from '@/types/document';
import { useDatapointLinks } from '@/hooks/useDatapointLinks';

export interface LinkedESRSDatapoint extends ESRSDatapoint {
  datapointRequestId: string;
  linked: boolean;
}

export function DatapointLinksTable({
  documentChunkId,
  datapointRequests,
  mutate,
}: {
  documentChunkId: string;
  datapointRequests: DatapointRequestLinkage[];
  mutate: () => void;
}) {
  const {
    esrs,
    setEsrs,
    filter,
    setFilter,
    loading,
    esrsDatapoints,
    esrsDatapointsFiltered,
    // handleUpdateSet,
    handleUpdateSubmit,
  } = useDatapointLinks({ documentChunkId, datapointRequests, mutate });

  const datapointLinksColumns: ColumnDef<any>[] = [
    {
      accessorKey: 'datapointId',
      cell: ({ row }) => (
        <div>
          <span className="font-semibold text-nowrap">
            {row.getValue('datapointId')}
          </span>
        </div>
      ),
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'name',
      cell: ({ row }) => (
        <div className="mx-10">
          <span className="text-left">{row.getValue('name')}</span>
        </div>
      ),
      filterFn: (row, id, value) => value.includes(row.getValue(id)),
    },
    {
      accessorKey: 'linked',
      cell: ({ row }) => (
        <div className="w-full flex justify-end">
          {row.getValue('linked') === true ? (
            <Button
              variant="outline"
              size="xs"
              onClick={() => handleUpdateSubmit(row.original.id, false)}
            >
              <Link2OffIcon className="h-4 w-4 mr-1" />
              Remove Link
            </Button>
          ) : (
            <Button
              size="xs"
              onClick={() => handleUpdateSubmit(row.original.id, true)}
            >
              <Link2Icon className="h-4 w-4 mr-1" />
              Add Link
            </Button>
          )}
        </div>
      ),
    },
  ];

  return (
    <div>
      <div className="flex items-center justify-between mb-3">
        <Tabs
          defaultValue={filter}
          className="w-[400px]"
          onValueChange={(value) => setFilter(value)}
        >
          <TabsList>
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="Linked">
              Linked ({esrsDatapoints.filter((d) => d.linked).length})
            </TabsTrigger>
            <TabsTrigger value="password">Not Linked</TabsTrigger>
          </TabsList>
        </Tabs>
        <Select onValueChange={setEsrs} defaultValue={esrs} disabled={loading}>
          <SelectTrigger className="w-[140px] h-8">
            <SelectValue placeholder="ESRS" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Topics</SelectItem>
            {ESRS_TOPIC_SEGMENTS.map((esrs) => (
              <SelectItem key={esrs} value={esrs}>
                {esrs}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <DataTableMin
        loading={loading}
        data={esrsDatapointsFiltered}
        columns={datapointLinksColumns}
      />

      <div className="border-t pt-3 space-x-2 flex justify-end">
        <Button variant="outlinepop" className="h-9" onClick={mutate}>
          Close
        </Button>
        {/* <Button className="h-9" onClick={handleUpdateSubmit} disabled={loading}>
          Save Links
        </Button> */}
      </div>
    </div>
  );
}
