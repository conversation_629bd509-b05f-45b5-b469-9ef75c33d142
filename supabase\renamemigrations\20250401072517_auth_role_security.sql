-- Enable RLS on all tables
ALTER TABLE public.user ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.workspace ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_workspace ENABLE ROW LEVEL SECURITY;

-- User profiles policies
DROP POLICY IF EXISTS "Users can view their own profile" ON public.user;
CREATE POLICY "Users can view their own profile" 
    ON public.user FOR SELECT 
    USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update their own profile" ON public.user;
CREATE POLICY "Users can update their own profile" 
    ON public.user FOR UPDATE 
    USING (auth.uid() = id);

-- Workspace policies
DROP POLICY IF EXISTS "Users can view workspaces they're members of" ON public.workspace;
CREATE POLICY "Users can view workspaces they're members of" 
    ON public.workspace FOR SELECT 
    USING (
    auth.uid() IN (
        SELECT "userId" FROM public.user_workspace
        WHERE "workspaceId" = id
    )
    );

DROP POLICY IF EXISTS "Workspace admins can update workspace" ON public.workspace;
CREATE POLICY "Workspace admins can update workspace" 
    ON public.workspace FOR UPDATE 
    USING (
    EXISTS (
        SELECT 1 FROM public.user_workspace
        WHERE "userId" = auth.uid()
        AND "workspaceId" = id
        AND role IN ('WORKSPACE_ADMIN', 'SUPER_ADMIN')
    )
    );

    -- Allow SUPER_ADMINs to create workspaces
DROP POLICY IF EXISTS "Super admins can create workspaces" ON public.workspace;
CREATE POLICY "Super admins can create workspaces" 
    ON public.workspace FOR INSERT 
    TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.user_workspace
            WHERE "userId" = auth.uid()
            AND role = 'SUPER_ADMIN'
        )
    );

-- Allow SUPER_ADMINs to delete workspaces
DROP POLICY IF EXISTS "Super admins can delete workspaces" ON public.workspace;
CREATE POLICY "Super admins can delete workspaces" 
    ON public.workspace FOR DELETE 
    USING (
        EXISTS (
            SELECT 1 FROM public.user_workspace
            WHERE "userId" = auth.uid()
            AND role = 'SUPER_ADMIN'
        )
    );

-- Workspace members policies
DROP POLICY IF EXISTS "Users can view members of their workspaces" ON public.user_workspace;
CREATE POLICY "Users can view members of their workspaces" 
    ON public.user_workspace FOR SELECT 
    USING (
    auth.uid() IN (
        SELECT "userId" FROM public.user_workspace
        WHERE "workspaceId" = public.user_workspace."workspaceId"
    )
    );

DROP POLICY IF EXISTS "Workspace admins can manage members" ON public.user_workspace;
CREATE POLICY "Workspace admins can manage members" 
    ON public.user_workspace FOR ALL 
    USING (
    EXISTS (
        SELECT 1 FROM public.user_workspace
        WHERE "userId" = auth.uid()
        AND "workspaceId" = user_workspace."workspaceId"
        AND role IN ('WORKSPACE_ADMIN', 'SUPER_ADMIN')
    )
    );