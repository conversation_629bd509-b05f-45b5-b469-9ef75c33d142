import { ColumnType, DataSource, DataSourceOptions } from 'typeorm';

export const NODE_ENV = process.env.NODE_ENV as
  | 'development'
  | 'production'
  | undefined;

export const isDevelopment = NODE_ENV === 'development';
export const isProduction = NODE_ENV === 'production';
export const getEnvFilePath = () => {
  return isProduction ? undefined : '../.env'; // on production .env is provided via docker compose
};

export const getDBHost = () => {
  return isProduction ? process.env.BACKEND_DB_HOST : 'localhost';
};

export const getRedisHost = () => {
  return isProduction ? process.env.REDIS_HOST : 'localhost';
};

export const lovableAppRegex =
  /^https:\/\/.*\.(lovable\.app|lovableproject\.com)$/;

export const createDataSourceWithVectorSupport = (
  options?: DataSourceOptions
) => {
  const dataSource = new DataSource(options);
  dataSource.driver.supportedDataTypes.push('vector' as ColumnType);
  dataSource.driver.withLengthColumnTypes.push('vector' as ColumnType);
  return dataSource;
};
