import { ConfigService } from '@nestjs/config';
import { SupabaseClient } from '@supabase/supabase-js';
declare enum OptionType {
    CHECKBOX = "checkbox",
    TEXT = "text",
    TEXT_WITH_CHECKBOX = "text-with-checkbox"
}
interface RowData {
    [key: string]: any;
}
interface NormalizedRow {
    theme: string;
    themeImpact: string;
    indicator: string;
    indicatorImpact: string;
    questionName: string;
    questionCode: string;
    questionImpact: string;
    question: string;
    optionText: string;
    optionInstructions: string;
    parentCredit: string;
    assignedTo?: string;
    userInput?: string;
    supportingDocs: {
        title: string;
        supportingValues?: string;
        supportsAnswerText?: boolean;
        pageNumbers?: string;
        type?: OptionType;
    }[];
    [key: string]: any;
}
interface ImportStats {
    themes: {
        created: number;
        existing: number;
    };
    questions: {
        created: number;
        existing: number;
    };
    options: {
        created: number;
        existing: number;
    };
    projectThemes: {
        created: number;
        existing: number;
    };
    projectQuestions: {
        created: number;
        existing: number;
    };
    answers: {
        created: number;
        existing: number;
    };
    documentLinks: {
        created: number;
        existing: number;
    };
}
export declare class QuestionnaireService {
    private configService;
    private supabase;
    constructor(configService: ConfigService);
    getClient(): SupabaseClient;
    determineOptionType(supportingValue: string): OptionType;
    processAnswerContent(userInput: string, supportingValues: string): {
        response: string;
        comment: string;
    };
    processExcelQuestionnaire(filePath: string, projectId: string): Promise<void>;
    normalizeExcelData(jsonData: RowData[], headerMap: Record<string, string>): NormalizedRow[];
    processQuestionnaire(data: NormalizedRow[], projectId: string): Promise<{
        success: boolean;
        stats: ImportStats;
        message: string;
    }>;
    processDocumentsForAnswer({ projectId, answerId, supportingDocs, supportingValues, pageNumbers, stats, }: {
        projectId: string;
        answerId: string;
        supportingDocs: string;
        supportingValues?: string;
        pageNumbers?: string;
        stats: ImportStats;
    }): Promise<void>;
    linkDocumentChunkToAnswer({ answerId, documentChunkId, comment, stats, }: {
        answerId: string;
        documentChunkId: string;
        comment: string;
        stats: ImportStats;
    }): Promise<void>;
    parsePageNumbers(pageStr: string): number[];
    createHeaderMapByPosition(headers: string[]): Record<string, string>;
}
export {};
