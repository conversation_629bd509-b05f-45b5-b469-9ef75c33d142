import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1737723978093 implements MigrationInterface {
  name = 'SchemaUpdate1737723978093';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" ADD "publicAccess" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_request" ADD "publicAccess" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "data_request" DROP COLUMN "publicAccess"`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" DROP COLUMN "publicAccess"`,
    );
  }
}
