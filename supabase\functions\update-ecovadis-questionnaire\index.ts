
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the Auth context
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Get the authorization header from the request
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Missing Authorization header');
    }

    // Get form data from the request
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const projectId = formData.get('projectId') as string;

    if (!file || !projectId) {
      throw new Error('Missing required fields: file or projectId');
    }

    console.log(`Received update request for project ${projectId}`);

    // Verify the current questionnaire exists for this project
    const { data: existingQuestionnaire, error: questionnaireError } = await supabase
      .from('ecovadis_questionnaire')
      .select('*')
      .eq('project_id', projectId)
      .eq('is_current', true)
      .single();

    if (questionnaireError && questionnaireError.code !== 'PGRST116') {
      throw new Error(`Error checking existing questionnaire: ${questionnaireError.message}`);
    }

    // Update the status of the current questionnaire if it exists
    if (existingQuestionnaire) {
      const { error: updateError } = await supabase
        .from('ecovadis_questionnaire')
        .update({ is_current: false })
        .eq('id', existingQuestionnaire.id);

      if (updateError) {
        throw new Error(`Error updating existing questionnaire: ${updateError.message}`);
      }
    }

    // Create a unique file path
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
    const filePath = `questionnaires/${projectId}/${fileName}`;

    // Upload the file to storage
    const { error: uploadError } = await supabase.storage
      .from('ecovadis')
      .upload(filePath, file, {
        contentType: file.type,
        upsert: true,
      });

    if (uploadError) {
      throw new Error(`Error uploading file: ${uploadError.message}`);
    }

    // Create a new questionnaire record
    const { data: newQuestionnaire, error: insertError } = await supabase
      .from('ecovadis_questionnaire')
      .insert({
        project_id: projectId,
        file_path: filePath,
        is_current: true,
        version: (existingQuestionnaire?.version || 0) + 1,
      })
      .select()
      .single();

    if (insertError) {
      throw new Error(`Error creating new questionnaire record: ${insertError.message}`);
    }

    console.log(`Successfully updated questionnaire for project ${projectId}`);

    // TODO: In a real implementation, you would process the Excel file here
    // For now, we'll just return success

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Questionnaire updated successfully', 
        questionnaire: newQuestionnaire 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  } catch (error) {
    console.error('Error in update-ecovadis-questionnaire function:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
