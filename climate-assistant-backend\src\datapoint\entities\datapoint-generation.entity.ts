import {
  <PERSON>umn,
  CreateDate<PERSON>olumn,
  <PERSON>tity,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { DatapointRequest } from './datapoint-request.entity';
import { User } from '../../users/entities/user.entity';

export enum datapointGenerationStatus {
  Pending = 'pending',
  Approved = 'approved',
  Rejected = 'rejected',
  MinorChanges = 'minorChanges',
}

//This entity is used to store the temporary generated datapoint
// text until the admin user approves the generated text, after that,
// it's moved to the datapoint request entity in content field
@Entity()
export class DatapointGeneration {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'json' })
  data: any;

  @Column({ type: 'uuid', nullable: true })
  evaluatorId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'evaluatorId' })
  evaluator: User;

  @Column({ nullable: true, type: Date, default: null })
  evaluatedAt: Date;

  @CreateDateColumn({ nullable: true })
  createdAt: Date;

  @ManyToOne(
    () => DatapointRequest,
    (datapointRequest) => datapointRequest.datapointGenerations,
  )
  @JoinColumn({ name: 'datapointRequestId' })
  datapointRequest: DatapointRequest;

  @Column({
    type: 'enum',
    enum: datapointGenerationStatus,
    default: datapointGenerationStatus.Pending,
  })
  status: datapointGenerationStatus;
}
