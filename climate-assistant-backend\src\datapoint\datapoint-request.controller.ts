import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  SetMetadata,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DatapointRequestService } from './datapoint-request.service';
import type { GenerateDatapointRequestTextPayload } from './entities/datapoint-request.dto';
import { UseGuards } from '@nestjs/common';
import { DatapointRequestGuard } from './datapoint-request.guard';
import { Roles } from 'src/auth/roles.decorator';
import { Role } from 'src/users/entities/user-workspace.entity';
import { AuthGuard } from 'src/auth/supabase/supabase.auth.guard';
import { datapointGenerationStatus } from './entities/datapoint-generation.entity';
import { DatapointDataRequestSharedService } from 'src/shared/shared-datapoint-datarequest.service';
import { DatapointRequest } from './entities/datapoint-request.entity';
import { User } from 'src/users/entities/user.entity';

@ApiTags('Data Request')
@UseGuards(AuthGuard)
@Controller('datapoint-request')
export class DatapointRequestController {
  constructor(
    private readonly datapointRequestService: DatapointRequestService,
    private readonly datapointDataRequestSharedService: DatapointDataRequestSharedService
  ) {}

  @UseGuards(DatapointRequestGuard)
  @Get('/:datapointRequestId')
  @ApiOperation({ summary: 'Get a specific datapoint request by ID' })
  @ApiResponse({
    status: 200,
    description: 'Datapoint request retrieved successfully',
  })
  async getDataRequest(
    @Param('datapointRequestId') datapointRequestId: string
  ) {
    return await this.datapointRequestService.findData(datapointRequestId);
  }

  @UseGuards(DatapointRequestGuard)
  @Get('/:datapointRequestId/material-topics')
  @ApiOperation({
    summary: 'Get material topics specific datapoint request by ID',
  })
  @ApiResponse({
    status: 200,
    description: 'All topics related to datapoint fetched successfully',
  })
  async getMaterialTopics(
    @Param('datapointRequestId') datapointRequestId: string
  ) {
    return await this.datapointRequestService.loadMaterialTopics(
      datapointRequestId
    );
  }

  @UseGuards(DatapointRequestGuard)
  @Put('/:datapointRequestId')
  @ApiOperation({ summary: 'Update a specific datapoint request by ID' })
  @ApiResponse({
    status: 200,
    description: 'Datapoint request updated successfully',
  })
  async updateDatapointRequest(
    @Param('datapointRequestId') datapointRequestId: string,
    @Body() updateDatapointRequestPayload: Partial<DatapointRequest>,
    @Req() req
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const datapointRequest = await this.datapointRequestService.update({
      datapointRequestId,
      updateDatapointRequestPayload,
      userId,
      workspaceId,
    });
    return datapointRequest;
  }

  @UseGuards(DatapointRequestGuard)
  @Post('/:datapointRequestId/review-with-ai')
  @Roles(
    Role.SuperAdmin,
    Role.WorkspaceAdmin,
    Role.AiContributor,
    Role.AiReviewer,
    Role.Contributor
  )
  @ApiOperation({ summary: 'Review datapoint request content with AI' })
  @ApiResponse({
    status: 200,
    description: 'Datapoint request content reviewed successfully',
  })
  async reviewContentWithAi(
    @Param('datapointRequestId') datapointRequestId: string,
    @Req() req
  ): Promise<void> {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const datapointRequest = req.datapointRequest;
    return await this.datapointDataRequestSharedService.addDatapointToReviewQueue(
      {
        datapointRequest,
        userId,
        workspaceId,
      }
    );
    // return await this.datapointRequestService.reviewDatapointContentWithAI({
    //   datapointRequestId,
    //   userId,
    //   workspaceId,
    // });
  }

  @UseGuards(DatapointRequestGuard)
  @SetMetadata('customCheck', 'generateWithAI')
  @Post('/:datapointRequestId/generate-with-ai')
  @Roles(
    Role.SuperAdmin,
    Role.WorkspaceAdmin,
    Role.AiContributor,
    Role.Contributor
  )
  @ApiOperation({ summary: 'Generate datapoint request content with AI' })
  @ApiResponse({
    status: 200,
    description: 'Datapoint request content generated successfully',
  })
  async generateContentWithAi(
    @Param('datapointRequestId') datapointRequestId: string,
    @Body() additionalData: GenerateDatapointRequestTextPayload,
    @Req() req
  ): Promise<void> {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const datapointRequest = req.datapointRequest;

    if (additionalData.additionalReportTextGenerationRules) {
      await this.datapointRequestService.update({
        datapointRequestId: datapointRequestId,
        updateDatapointRequestPayload: {
          customUserRemark: additionalData.additionalReportTextGenerationRules,
        },
        userId,
        workspaceId,
      });
    }

    return await this.datapointDataRequestSharedService.addDatapointToGenerationQueue(
      {
        datapointRequest,
        userId,
        workspaceId,
        useExistingReportTextForReference: additionalData.useExistingReportText,
      }
    );

    // return await this.datapointRequestService.generateDatapointContentWithAI({
    //   datapointRequestId,
    //   userId,
    //   workspaceId,
    //   additionalData,
    // });
  }

  @Get('/:datapointRequestId/citations')
  @ApiOperation({ summary: 'Get citations specific datapoint request by ID' })
  @ApiResponse({
    status: 200,
  })
  async getDataRequestCitations(
    @Param('datapointRequestId') datapointRequestId: string,
    @Query() { citationId }: { citationId: string }
  ) {
    return await this.datapointRequestService.loadDatapointCitations(
      datapointRequestId,
      citationId
    );
  }

  @UseGuards(DatapointRequestGuard)
  @Put('/:datapointRequestId/citations')
  @ApiOperation({
    summary: 'Update citations specific datapoint request by ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Datapoint request citations updated successfully',
  })
  async updateDataRequestCitations(
    @Param('datapointRequestId') datapointRequestId: string,
    @Body()
    payload: {
      citationId: string;
      index: number;
    },
    @Req() req
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const updatedDatapointRequest =
      await this.datapointRequestService.updateContentAndReplaceCitation({
        datapointRequestId,
        citationId: payload.citationId,
        index: payload.index,
        userId,
        workspaceId,
      });
    return updatedDatapointRequest;
  }

  @Roles(Role.SuperAdmin)
  @Put('/generation-status/:datapointGenerationId')
  @ApiOperation({
    summary: 'Update generation status specific datapoint request by ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Datapoint request generation status updated successfully',
  })
  async updateDatapointGenerationStatus(
    @Param('datapointGenerationId') datapointGenerationId: string,
    @Req() req,
    @Body()
    payload: {
      status: datapointGenerationStatus;
    }
  ): Promise<{
    content?: string;
    status: datapointGenerationStatus;
    evaluator?: User;
    evaluatedAt?: Date;
  }> {
    return await this.datapointRequestService.updateGenerationStatus({
      datapointGenerationId,
      status: payload.status,
      userId: req.user.id,
      workspaceId: req.user.workspaceId,
    });
  }

  @UseGuards(DatapointRequestGuard)
  @Get('/:datapointRequestId/document-links')
  @ApiOperation({
    summary: 'Get document links specific datapoint request by ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Datapoint request document links fetched successfully',
  })
  async getDocumentLinksForDatapointRequest(
    @Param('datapointRequestId') datapointRequestId: string
  ) {
    return await this.datapointRequestService.loadDocumentLinks(
      datapointRequestId
    );
  }
}
