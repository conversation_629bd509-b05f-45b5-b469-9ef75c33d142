
import React from 'react';
import ResetPasswordForm from './ResetPasswordForm';
import InvalidToken from './InvalidToken';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';

const ResetPassword: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  // Handle case where user is already logged in
  if (user) {
    navigate('/');
    return null;
  }

  // Get token from URL
  const queryParams = new URLSearchParams(window.location.search);
  const token = queryParams.get('token');

  if (!token) {
    return <InvalidToken />;
  }

  return <ResetPasswordForm token={token} userEmail={user.email} />;
};

export default ResetPassword;
