import React, { useState, useEffect } from 'react';
import { Lock } from 'lucide-react';

import { Button } from '@/components/ui/button.tsx';
import { HotjarService } from '@/services/hotjar.service.ts';
import { MixpanelService } from '@/services/mixpanel.service.ts';

enum COOKIE_CONSENT {
  ALL = '0',
  FUNCTIONAL = '1',
}

const CookieBanner: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const consent = localStorage.getItem('cookieConsent');
    if (!consent) {
      setIsVisible(true);
    } else if (consent === COOKIE_CONSENT.ALL) {
      HotjarService.init();
      MixpanelService.init();
    }
  }, [isVisible]);

  const handleAccept = (value: COOKIE_CONSENT) => {
    localStorage.setItem('cookieConsent', value);
    setIsVisible(false);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed shadow-[35px_0_60px_-15px_rgba(0,0,0,0.5)] flex items-center rounded-t-xl opacity-95 bottom-0 bg-white w-full p-8">
      <div className="w-1/2">
        <h3 className="font-semibold text-xl mb-4">
          Your privacy matters to us <Lock className="w-4 h-4 ml-2 inline" />
        </h3>
        <p>
          We use cookies to provide you with secure and personalized experience.
          Your data is protected and never shared. Please select how you would
          like us to use cookies. To learn more, see our{' '}
          <a href="https://glacier.eco/datenschutz">
            Glacier Privacy Statement
          </a>
          .
        </p>
      </div>
      <div className="flex flex-col md:flex-row  items-center md:flex-end ml-auto gap-4">
        <Button
          variant="outline"
          onClick={() => handleAccept(COOKIE_CONSENT.FUNCTIONAL)}
        >
          Accept Only Necessary Cookies
        </Button>
        <Button
          variant="darkBlue"
          onClick={() => handleAccept(COOKIE_CONSENT.ALL)}
          style={{ backgroundColor: '#143560' }}
        >
          Accept All Cookies
        </Button>
      </div>
    </div>
  );
};

export default CookieBanner;
