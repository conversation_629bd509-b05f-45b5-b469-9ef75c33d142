import axios from 'axios';

import { API_URL } from '@/api/apiConstants';
import {
  CommentData,
  CommentStatus,
  CreateProjectRequest,
  EsrsTopic,
  MaterialESRSTopic,
  Project,
  ProjectData,
  TopicLevel,
  UpdateProjectRequest,
} from '@/types/project';
import { CommentType, ESRSDatapoint } from '@/types';

export const fetchProjects = async (): Promise<Project[]> => {
  const response = await axios.get<Project[]>(`${API_URL}/projects`);
  return response.data;
};

export const fetchProjectById = async (
  projectId: string
): Promise<ProjectData> => {
  const response = await axios.get<ProjectData>(
    `${API_URL}/projects/${projectId}`
  );
  return response.data;
};

export const createProject = async (
  createProjectRequest: CreateProjectRequest
): Promise<Project> => {
  const response = await axios.post<Project>(
    `${API_URL}/projects`,
    createProjectRequest
  );
  return response.data;
};

export const updateProject = async (
  projectId: string,
  updateProjectRequest: UpdateProjectRequest
): Promise<Project> => {
  const response = await axios.put<Project>(
    `${API_URL}/projects/${projectId}`,
    updateProjectRequest
  );
  return response.data;
};

export const deleteProject = async (projectId: string): Promise<void> => {
  await axios.delete(`${API_URL}/projects/${projectId}`);
};

export const createComment = async ({
  projectId,
  commentData,
}: {
  projectId: string;
  commentData: {
    comment: string;
    commentableType: CommentType;
    commentableId: string;
  };
}): Promise<CommentData> => {
  const create = await axios.post(
    `${API_URL}/projects/${projectId}/comment/create`,
    commentData
  );

  return create.data;
};

export const updateComment = async ({
  projectId,
  commentId,
  comment,
}: {
  projectId: string;
  commentId: string;
  comment: string;
}): Promise<CommentData> => {
  const update = await axios.put(
    `${API_URL}/projects/${projectId}/comment/${commentId}`,
    {
      comment,
    }
  );

  return update.data;
};

export const updateCommentStatus = async ({
  projectId,
  commentId,
  status,
  evaluatorComment,
}: {
  projectId: string;
  commentId: string;
  status: CommentStatus;
  evaluatorComment: string;
}): Promise<CommentData> => {
  const update = await axios.put(
    `${API_URL}/projects/${projectId}/comment/${commentId}/status`,
    {
      status,
      evaluatorComment,
    }
  );

  return update.data;
};

export const deleteComment = async ({
  projectId,
  commentId,
}: {
  projectId: string;
  commentId: string;
}): Promise<void> => {
  await axios.delete(`${API_URL}/projects/${projectId}/comment/${commentId}`);
};

export const resolveComment = async ({
  projectId,
  commentId,
  resolve,
}: {
  projectId: string;
  commentId: string;
  resolve: boolean;
}): Promise<CommentData> => {
  const update = await axios.put(
    `${API_URL}/projects/${projectId}/comment/${commentId}/resolve`,
    {
      resolve,
    }
  );
  return update.data;
};

export const fetchProjectEsrsDatapoints = async (
  esrs: string
): Promise<(ESRSDatapoint & { datapointRequestId: string })[]> => {
  const response = await axios.get(
    `${API_URL}/projects/esrs-datapoints?esrs=${esrs}`
  );
  return response.data;
};

export const downloadDataRequestReport = async (
  projectId: string
): Promise<any> => {
  const response = await axios.get(
    `${API_URL}/projects/${projectId}/export-reporttext`,
    {
      responseType: 'arraybuffer',
    }
  );

  return response.data;
};

export const fetchMaterialityStatus = async (
  projectId: string
): Promise<{
  project: Project & { materialTopics: MaterialESRSTopic[] };
  esrsTopics: EsrsTopic[];
}> => {
  const response = await axios.get(
    `${API_URL}/projects/${projectId}/materiality`
  );
  return response.data;
};

export const updateMaterialityStatus = async (
  projectId: string,
  materialTopics: { esrsTopicId: number; active: boolean; level: TopicLevel }[]
): Promise<{
  status: string;
  project: Project & { materialTopics: MaterialESRSTopic[] };
  esrsTopics: EsrsTopic[];
}> => {
  const response = await axios.put(
    `${API_URL}/projects/${projectId}/materiality`,
    {
      materialTopics,
    }
  );

  return response.data;
};
