import { Workspace } from '../../workspace/entities/workspace.entity';
import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  Index,
  CreateDateColumn,
} from 'typeorm';

export interface IVersionHistory {
  event: string;
  doneBy: string;
  issuedBy?: string;
  data: any;
}

@Entity()
export class VersionHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar' })
  event: string;

  @Column('uuid')
  @Index()
  workspaceId: string;

  @Column('uuid')
  @Index()
  ref: string;

  @Column({ type: 'json' })
  versionData: IVersionHistory;

  @ManyToOne(() => Workspace, (workspace) => workspace.versionHistories)
  @JoinColumn({ name: 'workspaceId' })
  workspace: Workspace;

  @CreateDateColumn()
  createdAt: Date;
}
