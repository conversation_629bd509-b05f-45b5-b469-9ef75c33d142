import axios from 'axios';

import { API_URL } from '@/api/apiConstants';

export const fetchPromptContext = async (): Promise<string> => {
  const response = await axios.get<{
    context: string;
  }>(`${API_URL}/users/prompt-context`);
  return response.data.context;
};

export const fetchGeneratedContext = async (): Promise<string> => {
  const response = await axios.get<{
    context: string;
  }>(`${API_URL}/users/generated-prompt-context`);
  return response.data.context;
};

export const saveUserPromptContext = async (context: string) => {
  const response = await axios.post<{ success: boolean }>(
    `${API_URL}/users/user-prompt-settings`,
    { context }
  );
  return response.data;
};
