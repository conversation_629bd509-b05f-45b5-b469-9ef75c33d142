
import { useState } from 'react';
import { LinkedQuestion } from '@/types/ecovadis';
import { 
  <PERSON>, 
  Card<PERSON>ontent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { ChevronRight, Plus, FileText, X } from 'lucide-react';

interface DocumentLinkedQuestionsProps {
  documentId: string;
  linkedQuestions: LinkedQuestion[];
  onUpdateLinks?: (links: LinkedQuestion[]) => void;
}

export function DocumentLinkedQuestions({ 
  documentId, 
  linkedQuestions, 
  onUpdateLinks 
}: DocumentLinkedQuestionsProps) {
  // Mock data for each question
  const getLinkedQuestionMockData = (questionId: string) => {
    // This is a placeholder - in a real app this would be fetched from an API
    return {
      optionText: "Yes, with supporting documentation",
      comment: "See page 15-16 for details on our sustainability policy implementation."
    };
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-xl text-glacier-darkBlue flex items-center justify-between">
            <span>EcoVadis Answers</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {linkedQuestions.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[150px]">Question Code</TableHead>
                    <TableHead>Question</TableHead>
                    <TableHead>Answer Option</TableHead>
                    <TableHead>Comment</TableHead>
                    <TableHead>Project</TableHead>
                    <TableHead>Pages</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {linkedQuestions.map(question => {
                    const mockData = getLinkedQuestionMockData(question.id);
                    return (
                      <TableRow key={question.id}>
                        <TableCell className="font-medium">{question.code}</TableCell>
                        <TableCell>{question.text}</TableCell>
                        <TableCell>{mockData.optionText}</TableCell>
                        <TableCell className="max-w-[300px] truncate">{mockData.comment}</TableCell>
                        <TableCell>{question.projectName}</TableCell>
                        <TableCell>{question.pages || 'N/A'}</TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>No questions are linked to this document.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
