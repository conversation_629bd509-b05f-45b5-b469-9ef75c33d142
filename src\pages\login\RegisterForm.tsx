import { LoaderCircle } from 'lucide-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';

interface IRegisterFormProps {
  switchToLogin: () => void;
}

const formSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z
    .string()
    .min(8, { message: 'Password must be at least 8 characters' })
    .regex(/[A-Z]/, { message: 'Password must contain at least one uppercase letter' })
    .regex(/[a-z]/, { message: 'Password must contain at least one lowercase letter' })
    .regex(/[0-9]/, { message: 'Password must contain at least one number' }),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

const RegisterForm = ({ switchToLogin }: IRegisterFormProps) => {
  const { signUp } = useAuth();
  const [isRegistering, setIsRegistering] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      setIsRegistering(true);
      setError(null);

      await signUp({
        email: values.email,
        password: values.password,
        name: values.name,
      });
      setSuccess(true);

      // Redirect to login after successful registration
      setTimeout(() => {
        switchToLogin();
      }, 3000);
    } catch (err: any) {
      console.error('Registration error:', err);
      setError(err.message || 'Failed to register. Please try again.');
    } finally {
      setIsRegistering(false);
    }
  }

  return (
    <div className="flex flex-col w-full max-w-[380px] bg-white p-6 rounded-xl shadow-sm">
      <div className="font-semibold text-2xl md:text-3xl text-center mb-6">
        Create Your Account
      </div>
      {success ? (
        <div className="bg-green-50 p-4 rounded-lg text-green-800 mb-4">
          <p className="font-medium">Registration successful!</p>
          <p>Please check your email to verify your account. You'll be redirected to login shortly.</p>
        </div>
      ) : (
        <div className="mb-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">Full Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your full name" className="rounded-lg" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">Email</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your email" className="rounded-lg" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">Password</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="Create a password" className="rounded-lg" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">Confirm Password</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="Confirm your password" className="rounded-lg" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {error && (
                <div className="text-red-500 text-sm bg-red-50 p-2 rounded-lg">{error}</div>
              )}
              <Button 
                type="submit" 
                className="w-full rounded-lg py-2.5 mt-2" 
                style={{ backgroundColor: '#143560' }}
                disabled={isRegistering}
              >
                {isRegistering ? (
                  <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                Register
              </Button>
            </form>
          </Form>
          <div className="text-center mt-4">
            <span className="text-sm text-gray-600">Already have an account?</span>{' '}
            <Button
              variant="link"
              onClick={switchToLogin}
              className="text-sm text-blue-600 hover:text-blue-800 p-0"
            >
              Login
            </Button>
          </div>
        </div>
      )}
      <div className="text-center mt-4 text-xs text-gray-500 px-4">
        By registering, you agree to our Terms of Service and Privacy Policy. If you have any questions, feel free to reach out to us via{' '}
        <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>.
      </div>
    </div>
  );
};

export default RegisterForm;