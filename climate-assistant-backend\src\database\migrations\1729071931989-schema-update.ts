import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1729071931989 implements MigrationInterface {
  name = 'SchemaUpdate1729071931989';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "document_chunk" ALTER COLUMN "matchingsJson" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ALTER COLUMN "paragraph" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ALTER COLUMN "relatedAR" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ALTER COLUMN "lawText" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ALTER COLUMN "lawTextAR" DROP DEFAULT`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ALTER COLUMN "lawTextAR" SET DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ALTER COLUMN "lawText" SET DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ALTER COLUMN "relatedAR" SET DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ALTER COLUMN "paragraph" SET DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_chunk" ALTER COLUMN "matchingsJson" SET DEFAULT ''`,
    );
  }
}
