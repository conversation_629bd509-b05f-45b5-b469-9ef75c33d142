"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MERGED_CELL_REGEX = exports.CITATION_CLIENT_REGEX = void 0;
exports.trimHtmlPreAndPostfix = trimHtmlPreAndPostfix;
exports.extractCitationsFromReportTextGeneration = extractCitationsFromReportTextGeneration;
function trimHtmlPreAndPostfix(llmResponse) {
    if (llmResponse) {
        llmResponse = llmResponse.replace(`\`\`\`html`, '');
        llmResponse = llmResponse.replace(`\`\`\``, '');
    }
    return llmResponse;
}
function fixMalformedJson(input) {
    try {
        let fixedInput = input.replace(/(\[)(\s*)(".*?")(\s*:\s*".*?")(\s*)(\])/g, (_, open, space1, key, value, space2, close) => {
            return `${open}${space1}{${key}${value}}${space2}${close}`;
        });
        return JSON.parse(fixedInput);
    }
    catch (error) {
        throw new Error('Invalid input format. Could not convert to JSON.');
    }
}
function processNumericCitationParts(citations, documentChunksIndex) {
    const entries = [];
    if (Array.isArray(citations)) {
        for (const chunkObj of citations) {
            for (const key in chunkObj) {
                if (Object.prototype.hasOwnProperty.call(chunkObj, key)) {
                    const text = chunkObj[key];
                    const idMatch = key.match(/chunk-(\d+)/);
                    const index = idMatch ? parseInt(idMatch[1], 10) - 1 : 0;
                    entries.push({ id: documentChunksIndex[index], text });
                }
            }
        }
    }
    return entries;
}
function extractCitationsFromReportTextGeneration(input, documentChunksIndex) {
    const citation = {};
    let occurrence = 0;
    const regex = /<(source|sources-options)>\s*([\s\S]*?)\s*<\/\1>/g;
    const newstring = input.replace(regex, (match, tag, innerContent) => {
        occurrence++;
        const key = `dpcite-${occurrence}`;
        let citationsArray = [];
        let replacementSnippet = `[${key}|color-#eab308`;
        if (tag === 'source') {
            try {
                const arr = JSON.parse(innerContent);
                if (Array.isArray(arr)) {
                    arr.forEach((item) => {
                        const idMatch = item.match(/chunk-(\d+)/);
                        const index = idMatch ? parseInt(idMatch[1], 10) - 1 : 0;
                        citationsArray.push({
                            id: documentChunksIndex[index],
                            active: true,
                        });
                    });
                }
            }
            catch (error) {
                console.error(`Error parsing JSON for ${key} in <source>:`, error);
            }
            replacementSnippet += `|text-"[source]"]`;
        }
        else if (tag === 'sources-options') {
            try {
                const aiCitationobj = fixMalformedJson(innerContent);
                const activeEntries = processNumericCitationParts(aiCitationobj.active, documentChunksIndex);
                const inactiveEntries = processNumericCitationParts(aiCitationobj.inactive, documentChunksIndex);
                citationsArray = [
                    ...activeEntries.map((entry) => ({
                        id: entry.id,
                        text: entry.text,
                        active: true,
                    })),
                    ...inactiveEntries.map((entry) => ({
                        id: entry.id,
                        text: entry.text,
                        active: false,
                    })),
                ];
                if (activeEntries.length > 0) {
                    replacementSnippet += `|text-"${activeEntries[0].text}"`;
                }
                else {
                    replacementSnippet += `|text-"[source]"`;
                }
            }
            catch (error) {
                console.error(`Error parsing JSON for ${key} in <sources-options>:`, error);
            }
            replacementSnippet += `]`;
        }
        citation[key] = citationsArray;
        return replacementSnippet;
    });
    return { reportText: newstring, citation };
}
exports.CITATION_CLIENT_REGEX = /\[dpcite-(\d+)\|color-([#a-fA-F0-9]+)\|text-"([^"]+)"\]/g;
exports.MERGED_CELL_REGEX = /(\\?\|)(?:<r(\d+)#c(\d+)>(.*?)|<rm>(.*?)<\/rm>)(\\?\|)/g;
//# sourceMappingURL=llm-response-util.js.map