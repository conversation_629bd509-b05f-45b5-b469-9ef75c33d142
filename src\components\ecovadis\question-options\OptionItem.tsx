
import { useState } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { FileText, Upload, Loader2, Eye } from "lucide-react";
import { QuestionOption, isCheckboxChecked, getTextValueForAnswerOption } from "@/types/ecovadis";
import { DocumentsList } from "@/components/ecovadis/documents/DocumentsList";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { toast } from "sonner";
import { useToast } from "@/components/ui/use-toast";

interface OptionItemProps {
  option: QuestionOption;
  expanded: boolean;
  onToggleExpand: (optionId: string) => void;
  onToggleOption: (optionId: string) => Promise<void>;
  onUpdateResponse: (optionId: string, response: string) => Promise<void>;
  onOpenDocumentDialog: (optionId: string, docId: string | null) => void;
  onOpenUploadDialog: (optionId: string) => void;
  onOpenEvidenceDialog: (evidence: string) => void;
  onDetachDocument: (optionId: string, docId: string) => void;
  onRemoveDocument: (optionId: string, docId: string, chunkIds?: string[]) => void;
  isLoading: boolean;
  isAnswered: boolean;
  detachingDocIds?: string[];
  updatingDocIds?: string[];
}

export const OptionItem = ({
  option,
  expanded,
  onToggleExpand,
  onToggleOption,
  onUpdateResponse,
  onOpenDocumentDialog,
  onOpenUploadDialog,
  onOpenEvidenceDialog,
  onDetachDocument,
  onRemoveDocument,
  isLoading,
  isAnswered,
  detachingDocIds = [],
  updatingDocIds = []
}: OptionItemProps) => {
  const [textResponse, setTextResponse] = useState(getTextValueForAnswerOption(option.response || null, option.supportsAnswerText));
  const [isSavingText, setIsSavingText] = useState(false);
  const { toast } = useToast();

  const isSelected = isCheckboxChecked(option.response || null);
  const showTextInput = option.supportsAnswerText && isSelected;

  const handleSaveTextResponse = async () => {
    if (!textResponse.trim()) {
      toast({
        variant: "destructive",
        title: "Response Required",
        description: "Please enter a response before saving.",
      })
      return;
    }

    setIsSavingText(true);
    try {
      await onUpdateResponse(option.id, textResponse);
      toast({
        title: "Response Saved",
        description: "Your response has been saved successfully.",
        variant: "success",
      })
    } catch (error) {
      console.error("Error saving response:", error);
      toast({
        title: "Failed to Save Response",
        description: "There was an error saving your response.",
        variant: "destructive",
      })
    } finally {
      setIsSavingText(false);
    }
  };

  return (
    <AccordionItem 
      value={option.id} 
      className="rounded-md p-4 space-y-2 bg-gray-50/50 mb-4 border-none"
    >
      <div className="flex items-start gap-3">
        <div className="relative mt-1">
          <Checkbox 
            id={`option-${option.id}`} 
            checked={isSelected} 
            onCheckedChange={() => onToggleOption(option.id)} 
            disabled={isLoading}
          />
          {isLoading && (
            <Loader2 className="h-4 w-4 animate-spin absolute top-0 left-0 text-primary" />
          )}
        </div>
        
        <div className="space-y-2 flex-1">
          <div className="flex items-center justify-between">
            <AccordionTrigger 
              onClick={(e) => {
                e.preventDefault();
                onToggleExpand(option.id);
              }}
              className="p-0 hover:no-underline"
            >
              <label 
                htmlFor={`option-${option.id}`} 
                className="font-medium text-glacier-darkBlue cursor-pointer text-left"
              >
                {option.text}
              </label>
            </AccordionTrigger>
          </div>
          
          <AccordionContent>
            {option.evidenceExamples && option.evidenceExamples !== "N/A" && (
              <div className="flex items-center justify-between mb-3">
                <div className="text-sm text-glacier-darkBlue">
                  {option.supportsAnswerText ? "Instructions:" : "Examples of documents to attach"}
                  <button 
                    className="text-sm text-primary underline ml-2" 
                    onClick={() => onOpenEvidenceDialog(option.evidenceExamples)}
                  >
                    Show more
                  </button>
                </div>
              </div>
            )}

            {showTextInput && (
              <div className="space-y-3 mb-3">
                <label className="text-sm font-medium text-glacier-darkBlue">
                  {option.supportsAnswerText ? "Response:" : "Additional Response (Optional):"}
                </label>
                <Textarea
                  value={textResponse}
                  onChange={(e) => setTextResponse(e.target.value)}
                  placeholder={option.supportsAnswerText ? "Enter your response here..." : "Provide additional context or details..."}
                  className="min-h-[120px]"
                  disabled={isLoading || isSavingText}
                />
                
                <div className="flex justify-end">
                  <Button
                    onClick={handleSaveTextResponse}
                    disabled={!textResponse.trim() || isLoading || isSavingText}
                    className="bg-glacier-darkBlue hover:bg-glacier-darkBlue/90"
                  >
                    {isSavingText && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Save Response
                  </Button>
                </div>
              </div>
            )}

            {isSelected && (
              <>
                <div className="mb-3">
                  <h4 className="text-sm font-semibold text-glacier-darkBlue mb-2">
                    Add new document:
                  </h4>
                  {isAnswered && (
                    <div className="flex items-start gap-3">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="flex items-center gap-2 text-xs rounded-full" 
                        onClick={() => onOpenDocumentDialog(option.id, null)}
                      >
                        <FileText className="h-3.5 w-3.5" />
                        Attach from existing Documents
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="flex items-center gap-2 text-xs rounded-full" 
                        onClick={() => onOpenUploadDialog(option.id)}
                      >
                        <Upload className="h-3.5 w-3.5" />
                        Upload New Document
                      </Button>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-semibold text-glacier-darkBlue">
                    Attached evidence documents:
                  </h4>
                </div>
                
                <DocumentsList
                  documents={option.attachedDocuments || []} 
                  onEdit={docId => onOpenDocumentDialog(option.id, docId)} 
                  onRemove={(docId, chunkIds) => onRemoveDocument(option.id, docId, chunkIds)}
                  detachingDocIds={detachingDocIds}
                  updatingDocIds={updatingDocIds}
                />
              </>
            )}
          </AccordionContent>
        </div>
      </div>
    </AccordionItem>
  );
};
