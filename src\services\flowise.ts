import socketIOClient from 'socket.io-client';
import { useCallback, useEffect, useState } from 'react';

import { ChatMessage } from '@/models/chat.models.ts';

const FLOWISE_URL = import.meta.env.VITE_FLOWISE_URL;
const socket = socketIOClient(FLOWISE_URL);

const systemMessage: ChatMessage = {
  role: 'system',
  content: 'Please answer in German',
};

export const useFlowise = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [pendingMessage, setPendingMessage] = useState<string>('');
  const [socketIOClientId, setSocketIOClientId] = useState('');

  const sendFlowiseMessage = async (question: string, chatId: string) => {
    setPendingMessage('');
    setMessages((prevMessages) => [
      ...prevMessages,
      { role: 'user', content: question },
      { role: 'assistant', content: '' },
    ]);

    await fetch(`${FLOWISE_URL}/api/v1/prediction/${chatId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        question,
        socketIOClientId: socketIOClientId,
        history: [systemMessage, ...messages], // TODO: history not working..
      }),
    });
  };

  const messageReceived = useCallback(
    (message: string) => {
      setPendingMessage(
        (previousPendingMessage) => previousPendingMessage + message
      );

      const newMessage: ChatMessage = {
        role: 'assistant',
        content: pendingMessage,
      };

      setMessages((prevMessages) => [...prevMessages.slice(0, -1), newMessage]);
    },
    [pendingMessage]
  );

  useEffect(() => {
    socket.on('connect', () => {
      if (socket.id) {
        setSocketIOClientId(socket.id);
      } else {
        console.error('No socket.id');
      }
    });

    socket.on('token', (token) => {
      messageReceived(token);
    });

    // Cleanup the event listener on component unmount
    return () => {
      socket.off('token');
    };
  }, [messageReceived]);

  return { sendFlowiseMessage, messages };
};
