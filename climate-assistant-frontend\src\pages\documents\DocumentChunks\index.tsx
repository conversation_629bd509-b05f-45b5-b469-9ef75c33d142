import React, { useCallback, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import PageLoading from './loading';

import { MainLayout } from '@/components/MainLayout';
import {
  fetchDocumentData,
  startDocumentChunkDatapointLinking,
  startDocumentExtraction,
} from '@/api/documents/documents.api';
import {
  DocumentChunkMetadataJson,
  DocumentChunksEntity,
  DocumentData,
  DocumentStatus,
} from '@/types/document';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { DatapointLinks } from '@/components/documents/DatapointLinksModal';
import { MarkdownRenderer } from '@/components/ui/markdown-renderer';
import {
  cn,
  getFirstPageNumber,
  isJson,
  userHasRequiredRole,
} from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { documentStatuses } from '@/components/documents/DocumentsTableConfig';
import { DocumentStatusLabel } from '@/components/documents/DocumentStatusLabel';
import { useToast } from '@/components/ui/use-toast';
import { useAuthentication } from '@/api/authentication/authentication.query';
import { DatapointLinksDeleteModal } from '@/components/documents/FatapointLinksDeleteModal';
import { DocumentSettingsForm } from '@/components/documents/DocumentSettingsForm';
import { documentSettingsSchema } from '@/validators/file-upload';
import { USER_ROLE } from '@/constants/workspaceConstants';

export const DocumentChunksPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();

  const [documentData, setDocumentData] = useState<DocumentData | null>(null);
  const [isExpanded, setIsExpanded] = useState<Record<string, boolean>>({});
  const [loading, setLoading] = useState(true);
  const { user } = useAuthentication();

  const loadData = useCallback(async () => {
    setLoading(true);
    const data = await fetchDocumentData(id!);
    setDocumentData(data);
    setLoading(false);
  }, []);

  useEffect(() => {
    void loadData();
  }, []);

  if (!documentData || loading) return <PageLoading />;

  const totalDatapointLinks = documentData.chunks.reduce(
    (acc, chunk) =>
      acc + chunk.datapointDocumentChunkMap.filter((map) => map.active).length,
    0
  );
  const totalParagraphs = documentData.chunks.length;

  const toggleExpanded = (id: string) => {
    setIsExpanded((prevState) => ({
      ...prevState,
      [id]: !prevState[id],
    }));
  };

  const startExtraction = async () => {
    setDocumentData({
      ...documentData,
      status: DocumentStatus.ExtractingData,
    });
    toast({
      variant: 'default',
      description: 'Document Extraction started. This may take a few minutes.',
    });
    const response = await startDocumentExtraction(documentData.id);
    if (response && response.message) {
      toast({
        variant: 'success',
        description: `Document Extraction for ${documentData.name} successfully finished`,
      });
      setDocumentData({
        ...documentData,
        status: DocumentStatus.DataExtractionFinished,
      });
    } else {
      toast({
        variant: 'destructive',
        description: 'There was an error while Extracting Document',
      });
    }
    await loadData();
  };

  const startLinkingData = async (testMode: boolean) => {
    if (!userHasRequiredRole([USER_ROLE.SuperAdmin], user)) {
      toast({
        title: `You are not allowed to Link Data`,
        variant: 'destructive',
      });
      return;
    }
    setDocumentData({
      ...documentData,
      status: DocumentStatus.LinkingData,
    });
    toast({
      variant: 'default',
      description: 'Document Linking started. This may take a few minutes.',
    });
    const response = await startDocumentChunkDatapointLinking(
      documentData.id,
      testMode
    );
    if (response) {
      toast({
        variant: 'success',
        description: `Document Linking for ${documentData.name} successfully finished`,
      });
      setDocumentData({
        ...documentData,
        status: DocumentStatus.LinkingDataFinished,
      });
    } else {
      toast({
        variant: 'destructive',
        description: `There was an error while Linking Document Paragraphs of ${documentData.name}`,
      });
    }
    await loadData();
  };

  const documentStatus = documentStatuses.find(
    (status) => status.value === documentData.status
  );

  const clonedStatus = Object.assign({}, documentStatus);
  if (
    documentData.datapointsCount > 0 &&
    documentData.status === DocumentStatus.DataExtractionFinished
  ) {
    clonedStatus!.label = `Relevant for ${documentData.datapointsCount} Datapoints`;
  }

  function getExtractedFromDetail(chunk: DocumentChunksEntity) {
    const { name } = documentData || {};
    const xlsExtensions = ['.xlsx', '.xls', '.csv'];
    const isXls = xlsExtensions.some((ext) => name?.endsWith(ext));
    return isXls && chunk.metadataJson?.headings
      ? chunk.metadataJson.headings.join(' - ')
      : `Page ${chunk.page}`;
  }

  return (
    <MainLayout>
      <div className="space-y-5 mb-16">
        <div className="space-y-8">
          <div className="flex flex-col space-y-6">
            <div className="flex flex-col space-y-3">
              <h2 className="text-3xl tracking-wide font-bold">
                {documentData?.name}
              </h2>
              <span className="mr-1 text-glacier-bluedark">
                Uploaded by:{' '}
                <strong className="font-semibold">
                  {documentData.creator?.name || 'admin'}
                </strong>
              </span>
              {clonedStatus && (
                <div className="flex text-glacier-bluedark">
                  <span className="mr-1">Status: </span>
                  <DocumentStatusLabel
                    status={clonedStatus}
                  ></DocumentStatusLabel>
                </div>
              )}
            </div>
            <h3 className="text-2xl tracking-wide font-bold">
              Document Information
            </h3>
            {documentData && (
              <DocumentSettingsForm
                documentData={{
                  documentId: documentData.id,
                  month: documentData.month || undefined,
                  day: documentData.day || undefined,
                  year: documentData.year || 2024,
                  esrsCategory: documentData.esrsCategory || [],
                  documentType: documentData.documentType || '',
                  remarks: documentData.remarks || '',
                }}
                formSchema={documentSettingsSchema}
                callback={loadData}
              />
            )}
          </div>

          {userHasRequiredRole([USER_ROLE.SuperAdmin], user) && (
            <>
              <h3 className="text-2xl tracking-wide font-bold">
                Datapoint Links
              </h3>
              <p className="text-glacier-bluedark mb-0">
                Extracted Paragraphs:{' '}
                <span className="font-semibold">{totalParagraphs}</span>
              </p>
              <p className="text-glacier-bluedark mt-0">
                Datapoint Links Total:{' '}
                <span className="font-semibold">{totalDatapointLinks}</span>
              </p>
              <Button
                className="mr-2"
                onClick={startExtraction}
                disabled={documentData.status !== DocumentStatus.NotProcessed}
              >
                Start Extraction
              </Button>
              <Button
                onClick={() => startLinkingData(true)}
                disabled={
                  documentData.status !== DocumentStatus.DataExtractionFinished
                }
              >
                Start Linking Data
              </Button>
              <Button onClick={() => startLinkingData(false)} className="ml-2">
                Start Linking Data (ADMIN MODE)
              </Button>
            </>
          )}

          <Accordion type="multiple" className="w-full space-y-3">
            {chunkSorter(documentData?.chunks || [], isExpanded).map(
              (chunk) => {
                return (
                  <AccordionItem
                    key={chunk.id}
                    value={chunk.id}
                    className="bg-slate-50 px-5 py-2 rounded-lg"
                  >
                    <AccordionTrigger onClick={() => toggleExpanded(chunk.id)}>
                      <div>
                        <div className="flex gap-10 items-center">
                          <span className="text-sm">
                            Extracted from {getExtractedFromDetail(chunk)}
                          </span>
                          <div className="flex items-center space-x-2">
                            <DatapointLinks
                              documentChunkId={chunk.id}
                              datapointRequests={chunk.datapointDocumentChunkMap.map(
                                (map) => {
                                  return {
                                    ...map.datapointRequest,
                                    linked: map.active,
                                  };
                                }
                              )}
                              mutate={loadData}
                            />
                            {userHasRequiredRole(
                              [USER_ROLE.SuperAdmin],
                              user
                            ) && (
                              <DatapointLinksDeleteModal
                                documentChunkId={chunk.id}
                                mutate={loadData}
                              />
                            )}
                          </div>
                        </div>
                        {!chunk.showFullContent && (
                          <MarkdownRenderer
                            className={cn(
                              'space-y-2 text-[16px] tracking-wide leading-6 text-left font-normal mt-4 w-[90%] transition-opacity duration-500',
                              chunk.showFullContent
                                ? 'opacity-0'
                                : 'opacity-100'
                            )}
                            text={chunk.truncatedContent}
                          />
                        )}
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="w-full max-w-[90vw] overflow-x-auto">
                      <MarkdownRenderer
                        className={cn(
                          'space-y-2 text-[16px] tracking-wide leading-6 w-[90%] transition-opacity duration-500 opacity-0',
                          chunk.showFullContent ? 'opacity-100' : 'opacity-0'
                        )}
                        text={chunk.content}
                      />
                    </AccordionContent>
                  </AccordionItem>
                );
              }
            )}
          </Accordion>
        </div>
      </div>
    </MainLayout>
  );
};

export default DocumentChunksPage;

function chunkSorter(
  chunks: DocumentChunksEntity[],
  isExpanded: { [key: string]: boolean }
): (DocumentChunksEntity & {
  metadataJson: DocumentChunkMetadataJson;
  truncatedContent: string;
  showFullContent: boolean;
})[] {
  return chunks
    .sort((a, b) => {
      const pageA = getFirstPageNumber(a.page);
      const pageB = getFirstPageNumber(b.page);

      if (pageA !== pageB) {
        return pageA - pageB;
      }

      const metadataA = isJson(a.metadataJson)
        ? JSON.parse(a.metadataJson)
        : {};
      const metadataB = isJson(b.metadataJson)
        ? JSON.parse(b.metadataJson)
        : {};

      const chunkA = metadataA.chunkNumber || 0;
      const chunkB = metadataB.chunkNumber || 0;

      return chunkA - chunkB;
    })
    .map((chunk) => {
      let truncatedContent = chunk.content.slice(0, 100);
      const truncated = chunk.content.length > 100;
      const isChunkJson = isJson(chunk.metadataJson);

      if (truncated && isChunkJson && truncatedContent.split('|').length > 3) {
        const metadata = JSON.parse(
          chunk.metadataJson
        ) as DocumentChunkMetadataJson;
        truncatedContent = `${
          metadata.headings?.join(' | ') || ''
        } sheet, set ${metadata.chunkNumber || ''}`;
      } else if (truncated) {
        truncatedContent += '...';
      }

      const showFullContent = isExpanded[chunk.id];

      return {
        ...chunk,
        metadataJson: isJson(chunk.metadataJson)
          ? JSON.parse(chunk.metadataJson)
          : {},
        truncatedContent,
        showFullContent,
      };
    });
}
