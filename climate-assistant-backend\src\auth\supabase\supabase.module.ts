import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SupabaseService } from './supabase.service';
import { AuthGuard } from './supabase.auth.guard';
import { SupabaseController } from './supabase.controller';
import { QuestionnaireService } from './questionnaire.service';
import { EnhancedEcoVadisAnswerAgentService } from './answer-linking.service';
import { LlmModule } from '../../llm/llm.module';

@Module({
  imports: [ConfigModule, LlmModule],
  providers: [
    SupabaseService,
    QuestionnaireService,
    AuthGuard,
    EnhancedEcoVadisAnswerAgentService,
  ],
  controllers: [SupabaseController],
  exports: [SupabaseService, AuthGuard, EnhancedEcoVadisAnswerAgentService],
})
export class SupabaseAuthModule {}
