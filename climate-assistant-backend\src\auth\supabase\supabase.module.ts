import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SupabaseService } from './supabase.service';
import { AuthGuard } from './supabase.auth.guard';
import { SupabaseController } from './supabase.controller';
import { QuestionnaireService } from './questionnaire.service';
import { EnhancedEcoVadisAnswerAgentService } from './answer-linking.service';
import { EcoVadisQuestionChunkLinkingService } from './ecovadis-question-chunk-linking.service';
import { LlmModule } from '../../llm/llm.module';
import { LlmRateLimiterModule } from '../../llm-rate-limiter/llm-rate-limiter.module';
import { QueuesModule } from '../../queues/queues.module';
// Import required entities
import { DocumentChunk } from '../../document/entities/document-chunk.entity';
import { Document } from '../../document/entities/document.entity';
import { Project } from '../../project/entities/project.entity';
import { User } from '../../users/entities/user.entity';
import { UserWorkspace } from '../../users/entities/user-workspace.entity';
import { Workspace } from '../../workspace/entities/workspace.entity';
import {
  ProjectEcoVadisQuestionLinkedDocumentChunk,
  ProjectEcoVadisQuestion,
  EcoVadisTheme,
  EcoVadisAnswerOption,
  ProjectEcoVadisAnswer,
  EcoVadisSustainabilityIssue,
  ProjectEcoVadisLinkedDocumentChunks,
} from '../../ecovadis/entities/ecovadis.entity';

@Module({
  imports: [
    ConfigModule,
    LlmModule,
    LlmRateLimiterModule,
    QueuesModule,
    TypeOrmModule.forFeature([
      // User entities
      User,
      UserWorkspace,
      Workspace,
      // Document entities
      DocumentChunk,
      Document,
      // Project entity
      Project,
      // EcoVadis entities
      ProjectEcoVadisQuestionLinkedDocumentChunk,
      ProjectEcoVadisQuestion,
      EcoVadisTheme,
      EcoVadisAnswerOption,
      ProjectEcoVadisAnswer,
      EcoVadisSustainabilityIssue,
      ProjectEcoVadisLinkedDocumentChunks,
    ]),
  ],
  providers: [
    SupabaseService,
    QuestionnaireService,
    AuthGuard,
    EnhancedEcoVadisAnswerAgentService,
    EcoVadisQuestionChunkLinkingService,
  ],
  controllers: [SupabaseController],
  exports: [
    SupabaseService,
    AuthGuard,
    EnhancedEcoVadisAnswerAgentService,
    EcoVadisQuestionChunkLinkingService,
  ],
})
export class SupabaseAuthModule {}
