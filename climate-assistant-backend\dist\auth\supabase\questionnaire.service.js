"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionnaireService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const supabase_js_1 = require("@supabase/supabase-js");
const xlsx_1 = require("xlsx");
const fs_1 = require("fs");
const ANSWER_DELIMITER = 'my answer';
const COMMENT_DELIMITER = 'my comments about the document';
var OptionType;
(function (OptionType) {
    OptionType["CHECKBOX"] = "checkbox";
    OptionType["TEXT"] = "text";
    OptionType["TEXT_WITH_CHECKBOX"] = "text-with-checkbox";
})(OptionType || (OptionType = {}));
const impactMap = {
    High: 'High',
    high: 'High',
    HIGH: 'High',
    Medium: 'Medium',
    medium: 'Medium',
    MEDIUM: 'Medium',
    Low: 'Low',
    low: 'Low',
    LOW: 'Low',
    'N/A': null,
    'n/a': null,
    'N/a': null,
};
const indicatorMap = {
    Policies: 'POLICIES',
    policies: 'POLICIES',
    Policy: 'POLICIES',
    policy: 'POLICIES',
    Endorsements: 'ENDORSEMENTS',
    endorsements: 'ENDORSEMENTS',
    Endorsement: 'ENDORSEMENTS',
    endorsement: 'ENDORSEMENTS',
    Measures: 'MEASURES',
    measures: 'MEASURES',
    Measure: 'MEASURES',
    measure: 'MEASURES',
    Certifications: 'CERTIFICATIONS',
    certifications: 'CERTIFICATIONS',
    Certification: 'CERTIFICATIONS',
    certification: 'CERTIFICATIONS',
    Coverage: 'COVERAGE',
    coverage: 'COVERAGE',
    Reporting: 'REPORTING',
    reporting: 'REPORTING',
    Report: 'REPORTING',
    report: 'REPORTING',
    '360° Watch Findings': 'WATCH_FINDINGS',
    '360° watch findings': 'WATCH_FINDINGS',
    '360° News': 'WATCH_FINDINGS',
    '360° news': 'WATCH_FINDINGS',
    '360 Watch Findings': 'WATCH_FINDINGS',
    '360 News': 'WATCH_FINDINGS',
};
const DEFAULT_INDICATOR = 'MEASURES';
const columnOrderMap = {
    0: 'theme',
    1: 'themeImpact',
    2: 'indicator',
    3: 'indicatorImpact',
    4: 'questionName',
    5: 'questionCode',
    6: 'questionImpact',
    7: 'parentCredit',
    8: 'question',
    9: 'optionText',
    10: 'optionInstructions',
    11: 'assignedTo',
    12: 'userInput',
    13: 'supportingDocs',
    14: 'supportingValues',
    15: 'pageNumbers',
};
let QuestionnaireService = class QuestionnaireService {
    constructor(configService) {
        this.configService = configService;
        this.supabase = (0, supabase_js_1.createClient)(this.configService.get('SUPABASE_APP_URL'), this.configService.get('SUPABASE_SERVICE_KEY'));
    }
    getClient() {
        return this.supabase;
    }
    determineOptionType(supportingValue) {
        if (!supportingValue)
            return OptionType.TEXT;
        const content = supportingValue.toLowerCase().trim();
        if (content.startsWith(COMMENT_DELIMITER)) {
            return OptionType.CHECKBOX;
        }
        if (content.startsWith(ANSWER_DELIMITER)) {
            return content.includes(COMMENT_DELIMITER)
                ? OptionType.TEXT_WITH_CHECKBOX
                : OptionType.TEXT;
        }
        return OptionType.CHECKBOX;
    }
    processAnswerContent(userInput, supportingValues) {
        const optionType = this.determineOptionType(supportingValues || '');
        switch (optionType) {
            case OptionType.TEXT:
                return {
                    response: supportingValues || '',
                    comment: '',
                };
            case OptionType.TEXT_WITH_CHECKBOX: {
                const content = supportingValues || '';
                const answerMatch = content.match(new RegExp(`${ANSWER_DELIMITER}(.*?)(?=${COMMENT_DELIMITER}|$)`, 'is'));
                const commentMatch = content.match(new RegExp(`${COMMENT_DELIMITER}(.*?)$`, 'i'));
                return {
                    response: answerMatch ? answerMatch[1].trim() : '',
                    comment: commentMatch ? commentMatch[1].trim() : '',
                };
            }
            default:
                return {
                    response: userInput,
                    comment: supportingValues || '',
                };
        }
    }
    async processExcelQuestionnaire(filePath, projectId) {
        const buffer = await fs_1.promises.readFile(filePath);
        const workbook = (0, xlsx_1.read)(buffer, { type: 'array' });
        const sheetNames = Object.keys(workbook.Sheets);
        const questionnaireSheet = sheetNames.find((name) => name.toLowerCase().includes('questionnaire') ||
            name.toLowerCase().includes('fragebogen'));
        if (!questionnaireSheet) {
            let bestSheet = '';
            let bestScore = 0;
            for (const name of sheetNames) {
                const sheet = workbook.Sheets[name];
                const actualRange = sheet['!ref']
                    ? xlsx_1.utils.decode_range(sheet['!ref'])
                    : null;
                const jsonData = xlsx_1.utils.sheet_to_json(sheet, {
                    header: 1,
                    defval: '',
                    range: actualRange,
                    raw: false,
                });
                if (jsonData.length > 0) {
                    const headers = jsonData[0];
                    let score = 0;
                    if (headers.some((h) => h && /theme/i.test(h)))
                        score += 2;
                    if (headers.some((h) => h && /question/i.test(h)))
                        score += 2;
                    if (headers.some((h) => h && /indicator/i.test(h)))
                        score += 1;
                    if (score > bestScore) {
                        bestScore = score;
                        bestSheet = name;
                    }
                }
            }
            if (bestScore > 0) {
                console.info(`Found likely questionnaire sheet: ${bestSheet}`);
            }
            else {
                throw new Error('No suitable questionnaire sheet found');
            }
        }
        const sheetName = questionnaireSheet || sheetNames[0];
        if (!workbook.Sheets[sheetName]) {
            throw new Error(`Sheet "${sheetName}" not found in the workbook`);
        }
        const jsonData = xlsx_1.utils.sheet_to_json(workbook.Sheets[sheetName]);
        if (jsonData.length === 0) {
            throw new Error('No data found in the sheet');
        }
        const keyPositions = new Map();
        const optimisticHeaders = {};
        jsonData.forEach((obj, rowIndex) => {
            Object.keys(obj).forEach((key, colIndex) => {
                if (!keyPositions.has(key)) {
                    const existingKeysAtOrAfter = Array.from(keyPositions.entries()).filter(([_, pos]) => pos >= colIndex);
                    if (existingKeysAtOrAfter.length > 0) {
                        existingKeysAtOrAfter.forEach(([existingKey, existingPos]) => {
                            keyPositions.set(existingKey, existingPos + 1);
                        });
                    }
                    keyPositions.set(key, colIndex);
                }
            });
        });
        const sortedKeys = Array.from(keyPositions.entries())
            .sort(([, posA], [, posB]) => posA - posB)
            .map(([key]) => key);
        sortedKeys.forEach((key) => {
            optimisticHeaders[key] = '';
        });
        const headers = Object.keys(optimisticHeaders);
        const headerMap = this.createHeaderMapByPosition(headers);
        console.log('Mapped headers:', headerMap);
        const requiredFields = [
            'theme',
            'questionName',
            'questionCode',
            'question',
        ];
        const missingFields = requiredFields.filter((field) => !headerMap[field]);
        if (missingFields.length > 0) {
            throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
        }
        const normalizedData = this.normalizeExcelData(jsonData, headerMap);
        const result = await this.processQuestionnaire(normalizedData, projectId);
    }
    normalizeExcelData(jsonData, headerMap) {
        const normalizedRows = [];
        let currentQuestionData = {};
        for (const row of jsonData) {
            const questionCodeHeader = headerMap['questionCode'];
            const hasQuestionCode = questionCodeHeader &&
                row[questionCodeHeader] &&
                String(row[questionCodeHeader]).trim();
            if (hasQuestionCode) {
                currentQuestionData = {};
                for (const [standardField, headerName] of Object.entries(headerMap)) {
                    if (headerName &&
                        row[headerName] !== undefined &&
                        row[headerName] !== '') {
                        if (![
                            'optionText',
                            'optionInstructions',
                            'userInput',
                            'supportingDocs',
                            'supportingValues',
                            'pageNumbers',
                        ].includes(standardField)) {
                            currentQuestionData[standardField] = String(row[headerName]);
                        }
                    }
                }
            }
            const optionTextHeader = headerMap['optionText'];
            const hasOptionText = optionTextHeader &&
                row[optionTextHeader] &&
                String(row[optionTextHeader]).trim();
            const supportingDocsHeader = headerMap['supportingDocs'];
            const hasSupportingDocs = supportingDocsHeader &&
                row[supportingDocsHeader] &&
                String(row[supportingDocsHeader]).trim();
            if (hasOptionText || (hasQuestionCode && !hasSupportingDocs)) {
                const normalizedRow = {
                    theme: '',
                    themeImpact: '',
                    indicator: '',
                    indicatorImpact: '',
                    questionName: '',
                    questionCode: '',
                    questionImpact: '',
                    question: '',
                    optionText: '',
                    optionInstructions: '',
                    parentCredit: '',
                    assignedTo: '',
                    userInput: '',
                    supportingDocs: [],
                    ...currentQuestionData,
                };
                if (hasOptionText) {
                    normalizedRow.optionText = String(row[optionTextHeader]);
                }
                const optionInstructionsHeader = headerMap['optionInstructions'];
                if (optionInstructionsHeader && row[optionInstructionsHeader]) {
                    normalizedRow.optionInstructions = String(row[optionInstructionsHeader]);
                }
                const assignedToHeader = headerMap['assignedTo'];
                if (assignedToHeader && row[assignedToHeader]) {
                    normalizedRow.assignedTo = String(row[assignedToHeader]);
                }
                const userInputHeader = headerMap['userInput'];
                if (userInputHeader && row[userInputHeader]) {
                    normalizedRow.userInput = String(row[userInputHeader]);
                }
                if (hasSupportingDocs) {
                    const docInfo = {
                        title: String(row[supportingDocsHeader]),
                        supportingValues: row[headerMap['supportingValues']]
                            ? String(row[headerMap['supportingValues']])
                            : '',
                        pageNumbers: row[headerMap['pageNumbers']]
                            ? String(row[headerMap['pageNumbers']])
                            : '',
                    };
                    normalizedRow.supportingDocs.push(docInfo);
                }
                normalizedRows.push(normalizedRow);
            }
            else if (hasSupportingDocs && normalizedRows.length > 0) {
                const currentOptionRow = normalizedRows[normalizedRows.length - 1];
                const docInfo = {
                    title: String(row[supportingDocsHeader]),
                    supportingValues: row[headerMap['supportingValues']]
                        ? String(row[headerMap['supportingValues']])
                        : '',
                    pageNumbers: row[headerMap['pageNumbers']]
                        ? String(row[headerMap['pageNumbers']])
                        : '',
                    type: this.determineOptionType(String(row[headerMap['supportingValues']] || '')),
                };
                currentOptionRow.supportingDocs.push(docInfo);
                const optionInstructionsHeader = headerMap['optionInstructions'];
                if (optionInstructionsHeader &&
                    row[optionInstructionsHeader] &&
                    !currentOptionRow.optionInstructions) {
                    currentOptionRow.optionInstructions = String(row[optionInstructionsHeader]);
                }
                const assignedToHeader = headerMap['assignedTo'];
                if (assignedToHeader &&
                    row[assignedToHeader] &&
                    !currentOptionRow.assignedTo) {
                    currentOptionRow.assignedTo = String(row[assignedToHeader]);
                }
                const userInputHeader = headerMap['userInput'];
                if (userInputHeader &&
                    row[userInputHeader] &&
                    !currentOptionRow.userInput) {
                    currentOptionRow.userInput = String(row[userInputHeader]);
                }
            }
        }
        return normalizedRows;
    }
    async processQuestionnaire(data, projectId) {
        const stats = {
            themes: { created: 0, existing: 0 },
            questions: { created: 0, existing: 0 },
            options: { created: 0, existing: 0 },
            projectThemes: { created: 0, existing: 0 },
            projectQuestions: { created: 0, existing: 0 },
            answers: { created: 0, existing: 0 },
            documentLinks: { created: 0, existing: 0 },
        };
        const themeMap = new Map();
        const questionMap = new Map();
        const questionGroups = new Map();
        for (const row of data) {
            const questionCode = row.questionCode;
            if (questionCode) {
                if (!questionGroups.has(questionCode)) {
                    questionGroups.set(questionCode, []);
                }
                questionGroups.get(questionCode)?.push(row);
            }
        }
        const themeSortCounter = new Map();
        const questionGroupsArray = Array.from(questionGroups.entries());
        const BATCH_SIZE = 10;
        for (let i = 0; i < questionGroupsArray.length; i += BATCH_SIZE) {
            const batch = questionGroupsArray.slice(i, i + BATCH_SIZE);
            await Promise.all(batch.map(async ([questionCode, rows]) => {
                if (rows.length === 0) {
                    return;
                }
                const firstRow = rows[0];
                const themeName = firstRow.theme;
                const themeImpact = impactMap[firstRow.themeImpact] || null;
                if (themeName && !themeMap.has(themeName)) {
                    const { data: existingTheme } = await this.supabase
                        .from('ecovadis_theme')
                        .select('id')
                        .eq('title', themeName)
                        .maybeSingle();
                    let themeId;
                    if (existingTheme) {
                        themeId = existingTheme.id;
                        stats.themes.existing++;
                    }
                    else {
                        const { data: newTheme, error } = await this.supabase
                            .from('ecovadis_theme')
                            .insert({
                            title: themeName,
                            description: `Theme for ${themeName}`,
                        })
                            .select('id')
                            .single();
                        if (error)
                            throw new Error(`Failed to create theme: ${error.message}`);
                        themeId = newTheme.id;
                        stats.themes.created++;
                    }
                    themeMap.set(themeName, themeId);
                    const { data: existingProjectTheme } = await this.supabase
                        .from('project_ecovadis_theme')
                        .select('id')
                        .eq('projectId', projectId)
                        .eq('themeId', themeId)
                        .maybeSingle();
                    if (!existingProjectTheme) {
                        const { error } = await this.supabase
                            .from('project_ecovadis_theme')
                            .insert({
                            projectId,
                            themeId,
                            impact: themeImpact,
                        });
                        if (error)
                            throw new Error(`Failed to create project-theme relation: ${error.message}`);
                        stats.projectThemes.created++;
                    }
                    else {
                        stats.projectThemes.existing++;
                    }
                    themeSortCounter.set(themeName, 0);
                }
                const questionName = firstRow.questionName;
                const questionText = firstRow.question;
                let indicatorValue = DEFAULT_INDICATOR;
                if (firstRow.indicator && indicatorMap[firstRow.indicator]) {
                    indicatorValue = indicatorMap[firstRow.indicator];
                }
                const questionImpact = impactMap[firstRow.questionImpact] || null;
                if (questionCode &&
                    questionName &&
                    questionText &&
                    !questionMap.has(questionCode)) {
                    const themeId = themeMap.get(firstRow.theme);
                    if (!themeId) {
                        console.warn(`Theme ID not found for question ${questionCode}. Skipping.`);
                        return;
                    }
                    const currentSortOrder = themeSortCounter.get(firstRow.theme) || 0;
                    themeSortCounter.set(firstRow.theme, currentSortOrder + 1);
                    const { data: existingQuestion } = await this.supabase
                        .from('ecovadis_question')
                        .select('id')
                        .eq('questionCode', questionCode)
                        .maybeSingle();
                    let questionId;
                    if (existingQuestion) {
                        questionId = existingQuestion.id;
                        await this.supabase
                            .from('ecovadis_question')
                            .update({ sort: currentSortOrder })
                            .eq('id', questionId);
                        stats.questions.existing++;
                    }
                    else {
                        const { data: newQuestion, error } = await this.supabase
                            .from('ecovadis_question')
                            .insert({
                            themeId,
                            questionCode,
                            questionName,
                            question: questionText,
                            indicator: indicatorValue,
                            sort: currentSortOrder,
                        })
                            .select('id')
                            .single();
                        if (error)
                            throw new Error(`Failed to create question: ${error.message}`);
                        questionId = newQuestion.id;
                        stats.questions.created++;
                    }
                    questionMap.set(questionCode, questionId);
                    const { data: existingProjectQuestion } = await this.supabase
                        .from('project_ecovadis_question')
                        .select('id')
                        .eq('projectId', projectId)
                        .eq('questionId', questionId)
                        .maybeSingle();
                    if (!existingProjectQuestion) {
                        const { error } = await this.supabase
                            .from('project_ecovadis_question')
                            .insert({
                            projectId,
                            questionId,
                            impact: questionImpact,
                            status: 'pending',
                        });
                        if (error)
                            throw new Error(`Failed to create project-question relation: ${error.message}`);
                        stats.projectQuestions.created++;
                    }
                    else {
                        stats.projectQuestions.existing++;
                    }
                    let optionIndex = 0;
                    const optionGroups = [];
                    let currentOptionGroup = null;
                    for (const row of rows) {
                        const optionText = row.optionText;
                        const userInput = row.userInput;
                        const effectiveOptionText = optionText || userInput || '';
                        if (effectiveOptionText && effectiveOptionText !== 'N/A') {
                            currentOptionGroup = {
                                optionRow: row,
                            };
                            optionGroups.push(currentOptionGroup);
                        }
                    }
                    for (const optionGroup of optionGroups) {
                        const row = optionGroup.optionRow;
                        let effectiveOptionText = row.optionText || row.userInput || '';
                        if (row.questionName.toLocaleLowerCase() == 'other information') {
                            effectiveOptionText = 'Other Information';
                        }
                        let { data: existingOption } = await this.supabase
                            .from('ecovadis_answer_option')
                            .select('id')
                            .eq('questionId', questionId)
                            .eq('issueTitle', effectiveOptionText)
                            .maybeSingle();
                        if (!existingOption) {
                            const optionInstructionsHeader = row.optionInstructions || '';
                            const { data: createOption, error } = await this.supabase
                                .from('ecovadis_answer_option')
                                .insert({
                                questionId,
                                issueTitle: effectiveOptionText,
                                instructions: optionInstructionsHeader,
                                sort: optionIndex,
                            })
                                .select('id')
                                .single();
                            existingOption = createOption;
                            if (error)
                                throw new Error(`Failed to create answer option: ${error.message}`);
                            stats.options.created++;
                        }
                        else {
                            const { error: updateError } = await this.supabase
                                .from('ecovadis_answer_option')
                                .update({ sort: optionIndex })
                                .eq('id', existingOption.id);
                            if (updateError) {
                                console.error(`Failed to update sort order for option ${existingOption.id}: ${updateError.message}`);
                                throw new Error(`Failed to update answer option sort order: ${updateError.message}`);
                            }
                            stats.options.existing++;
                        }
                        let userInput = row.userInput?.trim().toLowerCase();
                        if (!userInput || userInput === '' || userInput === 'no') {
                            userInput = '';
                        }
                        const { data: existingAnswer } = await this.supabase
                            .from('project_ecovadis_answer')
                            .select('id')
                            .eq('projectId', projectId)
                            .eq('optionId', existingOption.id)
                            .maybeSingle();
                        let answerId;
                        if (existingAnswer) {
                            const { error } = await this.supabase
                                .from('project_ecovadis_answer')
                                .update({ response: userInput })
                                .eq('id', existingAnswer.id);
                            if (error)
                                throw new Error(`Failed to update answer: ${error.message}`);
                            answerId = existingAnswer.id;
                            stats.answers.existing++;
                        }
                        else {
                            const { data: newAnswer, error } = await this.supabase
                                .from('project_ecovadis_answer')
                                .insert({
                                projectId,
                                optionId: existingOption.id,
                                response: userInput,
                            })
                                .select('id')
                                .single();
                            if (error)
                                throw new Error(`Failed to create answer: ${error.message}`);
                            answerId = newAnswer.id;
                            stats.answers.created++;
                        }
                        if (userInput !== '') {
                            for (const docRow of optionGroup.optionRow.supportingDocs) {
                                const { response, comment } = this.processAnswerContent(userInput, docRow.supportingValues?.trim() || '');
                                if (response ||
                                    !docRow.supportingValues ||
                                    optionGroup.optionRow.questionName.toLowerCase() ===
                                        'other information') {
                                    await this.supabase
                                        .from('project_ecovadis_answer')
                                        .update({ response, supportsAnswerText: true })
                                        .eq('id', answerId);
                                }
                                const supportingDocs = docRow.title?.trim();
                                const pageNumbers = docRow.pageNumbers?.toString().trim();
                                if (supportingDocs && answerId) {
                                    await this.processDocumentsForAnswer({
                                        projectId,
                                        answerId,
                                        supportingDocs,
                                        supportingValues: comment,
                                        pageNumbers,
                                        stats,
                                    });
                                }
                            }
                        }
                        optionIndex++;
                    }
                }
            }));
        }
        return {
            success: true,
            stats,
            message: 'Questionnaire imported successfully',
        };
    }
    async processDocumentsForAnswer({ projectId, answerId, supportingDocs, supportingValues, pageNumbers, stats, }) {
        const { data: projectData } = await this.supabase
            .from('project')
            .select('workspaceId')
            .eq('id', projectId)
            .single();
        if (!projectData) {
            console.warn(`Project ${projectId} not found when processing documents`);
            return;
        }
        const workspaceId = projectData.workspaceId;
        const { data: documents } = await this.supabase
            .from('document')
            .select('id, name')
            .eq('workspaceId', workspaceId)
            .ilike('name', supportingDocs);
        if (documents && documents.length > 0) {
            for (const document of documents) {
                const pageNumbersArray = pageNumbers
                    ? this.parsePageNumbers(pageNumbers)
                    : [];
                let chunks = [];
                if (pageNumbersArray.length === 0) {
                    const { data: documentChunks, error } = await this.supabase
                        .from('document_chunk')
                        .select('id')
                        .eq('documentId', document.id)
                        .order('page', { ascending: true });
                    chunks = documentChunks || [];
                }
                else {
                    const pageStrings = pageNumbersArray.map((page) => page.toString());
                    const { data: documentChunks, error } = await this.supabase
                        .from('document_chunk')
                        .select('id')
                        .eq('documentId', document.id)
                        .in('page', pageStrings)
                        .order('page', { ascending: true });
                    chunks = documentChunks || [];
                }
                const chunkIds = chunks.map((chunk) => chunk.id);
                for (let i = 0; i < chunkIds.length; i += 10) {
                    const batch = chunkIds.slice(i, i + 10);
                    await Promise.all(batch.map((chunkId) => this.linkDocumentChunkToAnswer({
                        answerId,
                        documentChunkId: chunkId,
                        comment: i === 0 && supportingValues ? supportingValues : '',
                        stats,
                    })));
                }
            }
        }
    }
    async linkDocumentChunkToAnswer({ answerId, documentChunkId, comment, stats, }) {
        const { data: existingLink } = await this.supabase
            .from('project_ecovadis_linked_document_chunks')
            .select('id')
            .eq('answerId', answerId)
            .eq('documentChunkId', documentChunkId)
            .maybeSingle();
        if (!existingLink) {
            const { error } = await this.supabase
                .from('project_ecovadis_linked_document_chunks')
                .insert({
                answerId,
                documentChunkId,
                comment,
            });
            if (error)
                throw new Error(`Failed to link document chunk: ${error.message}`);
            stats.documentLinks.created++;
        }
        else {
            if (comment) {
                await this.supabase
                    .from('project_ecovadis_linked_document_chunks')
                    .update({ comment })
                    .eq('id', existingLink.id);
            }
            stats.documentLinks.existing++;
        }
    }
    parsePageNumbers(pageStr) {
        if (pageStr.toLowerCase() === 'toutes') {
            return [];
        }
        const pages = [];
        const parts = pageStr.split(',').map((p) => p.trim());
        for (const part of parts) {
            if (part.includes('-')) {
                const [start, end] = part
                    .split('-')
                    .map((num) => parseInt(num.trim(), 10));
                if (!isNaN(start) && !isNaN(end)) {
                    for (let i = start; i <= end; i++) {
                        pages.push(i);
                    }
                }
            }
            else {
                const pageNum = parseInt(part.trim(), 10);
                if (!isNaN(pageNum)) {
                    pages.push(pageNum);
                }
            }
        }
        return pages;
    }
    createHeaderMapByPosition(headers) {
        const headerMap = {};
        console.log(headers);
        for (const [indexStr, standardField] of Object.entries(columnOrderMap)) {
            const index = parseInt(indexStr, 10);
            if (index < headers.length) {
                headerMap[standardField] = headers[index];
            }
        }
        return headerMap;
    }
};
exports.QuestionnaireService = QuestionnaireService;
exports.QuestionnaireService = QuestionnaireService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], QuestionnaireService);
//# sourceMappingURL=questionnaire.service.js.map