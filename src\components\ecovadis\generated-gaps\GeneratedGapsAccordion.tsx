import React from 'react';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Clock, AlertTriangle } from 'lucide-react';
import { useGeneratedGapsForQuestion } from '@/hooks/useGeneratedGaps';
import { useUserRole } from '@/hooks/useUserRole';
import { USER_ROLE } from '@/constants/workspaceConstants';
import { GeneratedGapDetailCard } from './GeneratedGapDetailCard';

interface GeneratedGapsAccordionProps {
  questionId?: string;
}

export const GeneratedGapsAccordion: React.FC<GeneratedGapsAccordionProps> = ({
  questionId
}) => {
  const { hasRole } = useUserRole();
  const isSuperAdmin = hasRole([USER_ROLE.SuperAdmin]);
  
  const { data: generatedGapsData, isLoading } = useGeneratedGapsForQuestion(questionId);

  // Don't render anything if user is not a Super Admin
  if (!isSuperAdmin) {
    return null;
  }

  // Don't render if no question ID
  if (!questionId) {
    return null;
  }

  const generatedGaps = generatedGapsData?.gaps || [];
  const pendingCount = generatedGaps.filter(gap => gap.status === 'pending_review').length;
  const totalCount = generatedGaps.length;

  // Don't render if no generated gaps
  if (totalCount === 0 && !isLoading) {
    return null;
  }

  const getBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending_review':
        return 'outline' as const;
      case 'approved':
        return 'default' as const;
      case 'rejected':
        return 'destructive' as const;
      case 'regenerating':
        return 'secondary' as const;
      default:
        return 'outline' as const;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending_review':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200';
      case 'approved':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'rejected':
        return 'bg-red-50 text-red-700 border-red-200';
      case 'regenerating':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      default:
        return '';
    }
  };

  return (
    <div className="mt-6 border-t pt-6">
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="generated-gaps">
          <AccordionTrigger className="hover:no-underline">
            <div className="flex items-center justify-between w-full pr-4">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-amber-500" />
                  <span className="font-medium">Generated Gaps Review</span>
                </div>
                <div className="text-sm text-gray-500">
                  ({totalCount} total)
                </div>
              </div>
              {pendingCount > 0 && (
                <Badge 
                  variant="outline" 
                  className="bg-yellow-50 text-yellow-700 border-yellow-200 ml-2"
                >
                  <Clock className="w-3 h-3 mr-1" />
                  {pendingCount} pending
                </Badge>
              )}
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-4">
            {isLoading ? (
              <div className="flex items-center justify-center p-8">
                <div className="text-gray-500">Loading generated gaps...</div>
              </div>
            ) : generatedGaps.length > 0 ? (
              <div className="space-y-4">
                <div className="text-sm text-gray-600 mb-4">
                  Generated gaps for this question that require Super Admin review. Only approved gaps will be visible to non-Super Admin users.
                </div>
                
                {/* Group gaps by status */}
                {(['pending_review', 'approved', 'rejected', 'regenerating'] as const).map(status => {
                  const statusGaps = generatedGaps.filter(gap => gap.status === status);
                  if (statusGaps.length === 0) return null;
                  
                  const statusLabels = {
                    pending_review: 'Pending Review',
                    approved: 'Approved', 
                    rejected: 'Rejected',
                    regenerating: 'Regenerating'
                  };

                  return (
                    <div key={status} className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Badge 
                          variant="outline" 
                          className={getStatusColor(status)}
                        >
                          {statusLabels[status]} ({statusGaps.length})
                        </Badge>
                      </div>       
                      <div className="space-y-3 ml-4">
                        {statusGaps.map(gap => (
                          <GeneratedGapDetailCard 
                            key={gap.id} 
                            gap={gap}
                            questionId={questionId}
                          />
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center p-8 text-gray-500">
                No generated gaps for this question yet.
              </div>
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};
