import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON>n,
  CreateDateColumn,
  Column,
  Index,
} from 'typeorm';
import { Project } from './project.entity';
import { ESRSTopic } from '../../knowledge-base/entities/esrs-topic.entity';

@Entity()
export class MaterialESRSTopic {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  projectId: string;

  @ManyToOne(() => Project, (project) => project.materialTopics)
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @Column()
  @Index()
  esrsTopicId: number;

  @ManyToOne(() => ESRSTopic)
  @JoinColumn({ name: 'esrsTopicId' })
  esrsTopic: ESRSTopic;

  @Column({ default: true })
  active: boolean;

  @CreateDateColumn()
  createdAt: Date;
}
