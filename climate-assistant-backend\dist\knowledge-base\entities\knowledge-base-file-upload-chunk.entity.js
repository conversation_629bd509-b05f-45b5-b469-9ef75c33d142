"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeBaseFileUploadChunk = void 0;
const typeorm_1 = require("typeorm");
const knowledge_base_file_upload_entity_1 = require("./knowledge-base-file-upload.entity");
let KnowledgeBaseFileUploadChunk = class KnowledgeBaseFileUploadChunk {
};
exports.KnowledgeBaseFileUploadChunk = KnowledgeBaseFileUploadChunk;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], KnowledgeBaseFileUploadChunk.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => knowledge_base_file_upload_entity_1.KnowledgeBaseFileUpload, (fileUpload) => fileUpload.chunks),
    (0, typeorm_1.JoinColumn)({ name: 'fileUploadId' }),
    __metadata("design:type", knowledge_base_file_upload_entity_1.KnowledgeBaseFileUpload)
], KnowledgeBaseFileUploadChunk.prototype, "fileUpload", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], KnowledgeBaseFileUploadChunk.prototype, "fileUploadId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], KnowledgeBaseFileUploadChunk.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'vector',
        length: 1536,
        nullable: true,
        select: false,
    }),
    __metadata("design:type", Array)
], KnowledgeBaseFileUploadChunk.prototype, "embedding", void 0);
exports.KnowledgeBaseFileUploadChunk = KnowledgeBaseFileUploadChunk = __decorate([
    (0, typeorm_1.Entity)({ synchronize: false })
], KnowledgeBaseFileUploadChunk);
//# sourceMappingURL=knowledge-base-file-upload-chunk.entity.js.map