export enum AI_ACTIONS {
  initiativeCreation = 'initiativeCreation',
  regulatoryHelp = 'regulatoryHelp',
  csrdCreation = 'csrdCreation',
  transitionPlan = 'transitionPlan',
}

export enum Language {
  BG = 'Bulgarian',
  HR = 'Croatian',
  CS = 'Czech',
  DA = 'Danish',
  NL = 'Dutch',
  EN = 'English',
  ET = 'Estonian',
  FI = 'Finnish',
  FR = 'French',
  DE = 'German',
  EL = 'Greek',
  HU = 'Hungarian',
  GA = 'Irish',
  IT = 'Italian',
  LV = 'Latvian',
  LT = 'Lithuanian',
  MT = 'Maltese',
  PL = 'Polish',
  PT = 'Portuguese',
  RO = 'Romanian',
  SK = 'Slovak',
  SL = 'Slovene',
  ES = 'Spanish',
  SV = 'Swedish',
}

export enum DataRequestStatus {
  NoData = 'no_data',
  IncompleteData = 'incomplete_data',
  Draft = 'draft',
  CompleteData = 'complete_data',
  ApprovedAnswer = 'approved_answer',
  NotAnswered = 'not_answered',
}

export enum DatapointRequestStatus {
  NotAnswered = 'not_answered',
  IncompleteData = 'incomplete_data',
  NoData = 'no_data',
  CompleteData = 'complete_data',
  QueuedForGeneration = 'queued_for_generation',
}

export enum CommentType {
  DatapointRequest = 'datapoint_request',
  DataRequest = 'data_request',
}

export interface ESRSTopic {
  id: number;
  name: string;
}

export interface ESRSDisclosureRequirement {
  id: number;
  sort: number;
  dr: string;
  esrs: string;
  name: string;
  publicAccess: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ESRSDatapoint {
  id: number;
  datapointId: string;
  name: string;
  publicAccess: boolean;
  dataType: string;
  lawText?: string;
  lawTextAR?: string;
  paragraph?: string;
  relatedAR?: string;
}

export interface ESRSTopicDisclosureRequirement {
  id: number;
  esrsTopicId: string;
  esrsDisclosureRequirementId: number;
}

export interface DataRequestPayload {
  content?: string; //This is for reportText
  dueDate?: string;
  responsiblePersonId?: string;
  approvedBy?: string | null;
  approvedAt?: string | null;
  status?: DataRequestStatus;
}
