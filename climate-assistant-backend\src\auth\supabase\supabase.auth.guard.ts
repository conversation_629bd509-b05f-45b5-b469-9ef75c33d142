import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';
import { Reflector } from '@nestjs/core';
import { SupabaseService } from './supabase.service';
import { IS_PUBLIC_KEY } from 'src/auth/helpers';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private supabaseService: SupabaseService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    console.log('AuthGuard: Checking if user is authenticated');

    const request = context.switchToHttp().getRequest();
    const token = this.extractToken(request);

    if (!token) {
      throw new UnauthorizedException();
    }

    try {
      const { data: session, error } = await this.supabaseService
        .getClient()
        .auth.getUser(token);

      if (!session) {
        throw new UnauthorizedException('Supabase authentication failed');
      }

      const user = await this.supabaseService.findByAuthId(session.user.id);

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      const requiredRoles = this.reflector.get<string[]>(
        'roles',
        context.getHandler()
      );

      const baseWorkspaceId =
        session.user.user_metadata.workspaces &&
        session.user.user_metadata.workspaces.length > 0
          ? session.user.user_metadata.workspaces[0].workspaceId
          : null;

      request['user'] = {
        sub: user.id,
        id: user.id,
        email: user.email,
        workspaceId: baseWorkspaceId,
      };

      if (!requiredRoles) {
        return true;
      }

      // Use Supabase to check roles instead of local database
      const userRole = await this.supabaseService.getUserRole(
        user.id,
        baseWorkspaceId?.workspaceId
      );

      if (!userRole) {
        return false;
      }

      return requiredRoles.includes(userRole);
    } catch {
      throw new UnauthorizedException();
    }
  }

  private extractToken(request: Request): string | undefined {
    // First check for Bearer token in Authorization header
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7); // Remove 'Bearer ' prefix
    }

    // Fall back to checking cookies if no Bearer token found
    return request?.cookies?.['access_token'];
  }
}
