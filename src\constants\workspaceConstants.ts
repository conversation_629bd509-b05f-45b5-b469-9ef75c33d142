
export enum USER_ROLE {
  SuperAdmin = 'SUPER_ADMIN',
  WorkspaceAdmin = 'WORKSPACE_ADMIN',
  AiContributor = 'AI_CONTRIBUTOR',
  AiReviewer = 'AI_ONLY_REVIEW',
  Contributor = 'CONTRIBUTOR',
}

export const WORKSPACE_ROLES_MAPPING_FOR_UI: {
  [key in USER_ROLE]: string;
} = {
  SUPER_ADMIN: 'Super Admin',
  WORKSPACE_ADMIN: 'Workspace Admin',
  AI_CONTRIBUTOR: 'AI Contributor',
  AI_ONLY_REVIEW: 'AI Reviewer',
  CONTRIBUTOR: 'Contributor',
};
