import { Repository } from 'typeorm';
import { Workspace } from './entities/workspace.entity';
import { Role, UserWorkspace } from '../users/entities/user-workspace.entity';
import { User } from '../users/entities/user.entity';
import { Token } from '../users/entities/token.entity';
import { EmailService } from '../external/email.service';
import { IVersionHistory, VersionHistory } from './entities/version-history.entity';
import { Company } from './entities/company.entity';
export declare class WorkspaceService {
    private readonly workspaceRepository;
    private readonly companyRepository;
    private readonly userWorkspaceRepository;
    private tokenRepository;
    private readonly userRepository;
    private readonly versionHistoryRepository;
    private emailService;
    constructor(workspaceRepository: Repository<Workspace>, companyRepository: Repository<Company>, userWorkspaceRepository: Repository<UserWorkspace>, tokenRepository: Repository<Token>, userRepository: Repository<User>, versionHistoryRepository: Repository<VersionHistory>, emailService: EmailService);
    findById(id: Workspace['id']): Promise<Workspace | undefined>;
    updateById(id: string, updates: Partial<Workspace>): Promise<Workspace>;
    getUsersByWorkspace(workspaceId: string): Promise<User[]>;
    addUserToWorkspace({ workspaceId, userId, role, }: {
        workspaceId: string;
        userId: string;
        role: Role;
    }): Promise<UserWorkspace>;
    getUserWorkspace({ workspaceId, userId, }: {
        workspaceId: string;
        userId: string;
    }): Promise<UserWorkspace | undefined>;
    removeUserFromWorkspace({ workspaceId, userId, }: {
        workspaceId: string;
        userId: string;
    }): Promise<void>;
    inviteUserToWorkspace({ inviteeEmail, origin, workspaceId, email, role, shouldSendEmail, }: {
        inviteeEmail: string;
        origin: string;
        workspaceId: string;
        email: string;
        role: Role;
        shouldSendEmail: boolean;
    }): Promise<User>;
    acceptInvitation({ workspaceId, userId, }: {
        workspaceId: string;
        userId: string;
    }): Promise<UserWorkspace>;
    storeActionHistory({ workspaceId, event, ref, versionData, }: {
        workspaceId: string;
        event: string;
        ref: string;
        versionData: IVersionHistory;
    }): Promise<void>;
    canInviteUser(invitedRole: Role, user: User): Promise<boolean>;
    isUserActive(user: User): boolean;
    getAllWorkspaces(): Promise<Workspace[]>;
    getCompanyDetailFromWorkspaceId(workspaceId: string): Promise<Company>;
    updateCompanyDetail(workspaceId: string, companyDetail: Company): Promise<Company>;
}
