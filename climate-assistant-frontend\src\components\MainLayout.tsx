import { FunctionComponent, ReactNode } from 'react';

import { Breadcrumbs } from './router/BreadCrumbs';

import { TopNavigation } from '@/components/TopNavigation';

export const MainLayout: FunctionComponent<{ children: ReactNode }> = ({
  children,
}) => {
  return (
    <div className="flex flex-row min-h-screen">
      <div className="flex flex-1 flex-col bg-white px-4 lg:px-16">
        <TopNavigation />
        <Breadcrumbs />
        <div>{children}</div>
      </div>
    </div>
  );
};
