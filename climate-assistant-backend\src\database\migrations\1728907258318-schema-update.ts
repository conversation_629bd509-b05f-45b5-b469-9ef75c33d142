import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1728907258318 implements MigrationInterface {
  name = 'SchemaUpdate1728907258318';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "company" ALTER COLUMN "generalCompanyProfile" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "company" ALTER COLUMN "reportTextGenerationRules" DROP NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "company" ALTER COLUMN "reportTextGenerationRules" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "company" ALTER COLUMN "generalCompanyProfile" SET NOT NULL`,
    );
  }
}
