import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1721055638014 implements MigrationInterface {
  name = 'SchemaUpdate1721055638014';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "chat_history"
          ALTER COLUMN "title" DROP NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "chat_history"
          ALTER COLUMN "title" SET NOT NULL`,
    );
  }
}
