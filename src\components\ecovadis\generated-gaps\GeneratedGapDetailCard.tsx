import React, { useState } from 'react';
import { GeneratedGap } from '@/types/generated-gaps';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  CheckCircle, 
  XCircle, 
  RefreshCw, 
  Clock, 
  FileText,
  Calendar,
  User
} from 'lucide-react';
import { useReviewGeneratedGap } from '@/hooks/useGeneratedGaps';
import { formatDistanceToNow } from 'date-fns';
import { MarkdownRenderer } from '@/components/ui/markdown-renderer';
import { cn } from '@/lib/utils';

interface GeneratedGapDetailCardProps {
  gap: GeneratedGap;
  questionId: string;
}

type ReviewAction = 'approve' | 'disapprove' | 'regenerate';

export const GeneratedGapDetailCard: React.FC<GeneratedGapDetailCardProps> = ({ gap, questionId }) => {
  const [showReviewDialog, setShowReviewDialog] = useState(false);
  const [selectedAction, setSelectedAction] = useState<ReviewAction | ''>('');
  const [feedback, setFeedback] = useState('');
  
  const reviewMutation = useReviewGeneratedGap(questionId);

  const handleActionSelect = (action: ReviewAction) => {
    setSelectedAction(action);
    setFeedback('');
    setShowReviewDialog(true);
  };

  const handleSubmitReview = async () => {
    if (!selectedAction) return;
    
    const requiresFeedback = selectedAction === 'disapprove' || selectedAction === 'regenerate';
    if (requiresFeedback && !feedback.trim()) {
      return; // Validation handled by the dialog
    }

    try {
      await reviewMutation.mutateAsync({
        gapId: gap.id,
        action: selectedAction,
        feedback: feedback.trim() || undefined
      });
      setShowReviewDialog(false);
      setFeedback('');
      setSelectedAction('');
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending_review':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
          <Clock className="w-3 h-3 mr-1" />
          Pending Review
        </Badge>;
      case 'approved':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          <CheckCircle className="w-3 h-3 mr-1" />
          Approved
        </Badge>;
      case 'rejected':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
          <XCircle className="w-3 h-3 mr-1" />
          Rejected
        </Badge>;
      case 'regenerating':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          <RefreshCw className="w-3 h-3 mr-1" />
          Regenerating
        </Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getActionTitle = (action: ReviewAction) => {
    switch (action) {
      case 'approve':
        return 'Approve Gap';
      case 'disapprove':
        return 'Disapprove Gap';
      case 'regenerate':
        return 'Regenerate Gap';
      default:
        return 'Review Gap';
    }
  };

  const getActionDescription = (action: ReviewAction) => {
    switch (action) {
      case 'approve':
        return 'This will approve the generated gap and make it visible to all users in the project.';
      case 'disapprove':
        return 'This will reject the generated gap. Please provide a reason for the rejection.';
      case 'regenerate':
        return 'This will trigger AI regeneration with your feedback to improve the gap analysis.';
      default:
        return '';
    }
  };

  // Parse the individual gap data from generatedContent
  const gapData = gap.generatedContent;

  const requiresFeedback = selectedAction === 'disapprove' || selectedAction === 'regenerate';

  return (
    <>
      <Card className={cn(
        'border-l-4 transition-colors duration-300',
        {
          'border-l-green-400 bg-green-50/30': gap.status === 'approved',
          'border-l-red-400 bg-red-50/30': gap.status === 'rejected',
          'border-l-blue-400 bg-blue-50/30': gap.status === 'regenerating',
          'border-l-yellow-400 bg-yellow-50/30': gap.status === 'pending_review'
        }
      )}>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row justify-between gap-4">
            {/* Left side content */}
            <div className="flex-1">
              {/* Gap title and status */}
              <div className="flex items-start justify-between mb-3">
                <h3 className="text-lg font-medium text-glacier-darkBlue flex-1">
                  <MarkdownRenderer text={gapData?.gap || 'Untitled Gap'} />
                </h3>
                <div className="ml-4">
                  {getStatusBadge(gap.status)}
                </div>
              </div>

              {/* Gap type and category */}
              <div className="flex gap-2 mb-3">
                <Badge variant="secondary" className="text-xs">
                  {gapData?.gap_type || 'Unknown Type'}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {gapData?.pillar_category || 'Unknown Category'}
                </Badge>
              </div>

              {/* Gap description */}
              <div className="mb-4">
                <p className="text-sm text-gray-700">
                  <MarkdownRenderer text={gapData?.description || 'No description available'} />
                </p>
              </div>

              {/* Sample text if available */}
              {gapData?.sample_text && (
                <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                  <p className="text-xs font-medium mb-1 text-gray-600">Sample Text:</p>
                  <p className="text-sm text-gray-700 italic">
                    <MarkdownRenderer text={gapData.sample_text} />
                  </p>
                </div>
              )}

              {/* Recommended actions */}
              {gapData?.recommended_actions && gapData.recommended_actions.length > 0 && (
                <div className="mb-4">
                  <p className="text-sm font-medium mb-2 text-gray-700">Recommended Actions:</p>
                  <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                    {gapData.recommended_actions.map((action, index) => (
                      <li key={index}>
                        <MarkdownRenderer text={action} />
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Affected documents */}
              {gap.documents?.map((doc, index) => (
              <a key={index}
                  href={`/documents/${doc.id}`}
                  target="_blank"
                 className="flex items-center gap-1 text-sm">
                <FileText className="h-4 w-4 text-gray-500" />
                <span className="font-medium underline">{doc.name}</span>
              </a>
            ))}

              {/* Timestamps */}
              <div className="flex flex-wrap items-center gap-4 text-xs text-gray-500 mb-3">
                <div className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  Created {formatDistanceToNow(new Date(gap.createdAt), { addSuffix: true })}
                </div>
                {gap.reviewer && (
                  <div className="flex items-center gap-1">
                    <User className="w-3 h-3" />
                    Reviewed by {gap.reviewer.name}
                  </div>
                )}
              </div>

              {/* Review feedback */}
              {gap.feedback && (
                <div className="p-3 bg-gray-50 rounded-lg mb-4">
                  <p className="text-sm font-medium mb-1">Review Feedback:</p>
                  <p className="text-sm text-gray-700">{gap.feedback}</p>
                </div>
              )}
            </div>

            {/* Right side actions */}
            <div className="flex flex-col gap-2 min-w-[200px]">
              {gap.status === 'pending_review' && (
                <>
                  <div className="text-sm font-medium text-gray-700 mb-2">
                    Review Action:
                  </div>
                  <Select 
                    onValueChange={(value) => handleActionSelect(value as ReviewAction)}
                    disabled={reviewMutation.isPending}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select action" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="approve">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          Approve
                        </div>
                      </SelectItem>
                      <SelectItem value="disapprove">
                        <div className="flex items-center gap-2">
                          <XCircle className="w-4 h-4 text-red-600" />
                          Disapprove
                        </div>
                      </SelectItem>
                      {/* <SelectItem value="regenerate">
                        <div className="flex items-center gap-2">
                          <RefreshCw className="w-4 h-4 text-blue-600" />
                          Regenerate
                        </div>
                      </SelectItem> */}
                    </SelectContent>
                  </Select>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Review Dialog */}
      <Dialog open={showReviewDialog} onOpenChange={setShowReviewDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{getActionTitle(selectedAction as ReviewAction)}</DialogTitle>
            <DialogDescription>
              {getActionDescription(selectedAction as ReviewAction)}
            </DialogDescription>
          </DialogHeader>
          
          {requiresFeedback && (
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {selectedAction === 'disapprove' ? 'Reason for rejection:' : 'Feedback for regeneration:'}
                <span className="text-red-500">*</span>
              </label>
              <Textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                placeholder={
                  selectedAction === 'disapprove'
                    ? 'Please explain why this gap analysis should be rejected...'
                    : 'Please provide specific feedback on how to improve the gap analysis...'
                }
                rows={4}
                className="resize-none"
              />
              {requiresFeedback && !feedback.trim() && (
                <p className="text-sm text-red-600">This field is required</p>
              )}
            </div>
          )}

          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowReviewDialog(false)}
              disabled={reviewMutation.isPending}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSubmitReview}
              disabled={reviewMutation.isPending || (requiresFeedback && !feedback.trim())}
              className={cn({
                'bg-green-600 hover:bg-green-700': selectedAction === 'approve',
                'bg-red-600 hover:bg-red-700': selectedAction === 'disapprove',
                'bg-blue-600 hover:bg-blue-700': selectedAction === 'regenerate'
              })}
            >
              {reviewMutation.isPending ? 'Processing...' : getActionTitle(selectedAction as ReviewAction)}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
