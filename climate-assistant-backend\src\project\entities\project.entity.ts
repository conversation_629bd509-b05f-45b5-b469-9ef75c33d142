import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>reateDateColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { MaterialESRSTopic } from './material-esrs-topic.entity';
import { DataRequest } from '../../data-request/entities/data-request.entity';
import { Workspace } from '../../workspace/entities/workspace.entity';

// enums/language.enum.ts
export enum Language {
  BG = 'BG', // Bulgarian
  HR = 'HR', // Croatian
  CS = 'CS', // Czech
  DA = 'DA', // Danish
  NL = 'NL', // Dutch
  EN = 'EN', // English
  ET = 'ET', // Estonian
  FI = 'FI', // Finnish
  FR = 'FR', // French
  DE = 'DE', // German
  EL = 'EL', // Greek
  HU = 'HU', // Hungarian
  GA = 'GA', // Irish
  IT = 'IT', // Italian
  LV = 'LV', // Latvian
  LT = 'LT', // Lithuanian
  MT = 'MT', // Maltese
  PL = 'PL', // Polish
  PT = 'PT', // Portuguese
  RO = 'RO', // Romanian
  SK = 'SK', // Slovak
  SL = 'SL', // Slovene
  ES = 'ES', // Spanish
  SV = 'SV', // Swedish
}

@Entity()
export class Project {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  workspaceId: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ nullable: true })
  reportingYear: string;

  @Column({ type: 'text', default: '' })
  reportTextGenerationRules: string;

  @Column({ type: 'enum', enum: Language })
  primaryContentLanguage: Language;

  @Column('uuid')
  createdBy: string;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => Workspace, (workspace) => workspace.projects)
  @JoinColumn({ name: 'workspaceId' })
  workspace: Workspace;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'createdBy' })
  creator: User;

  @OneToMany(() => MaterialESRSTopic, (materialTopic) => materialTopic.project)
  materialTopics: MaterialESRSTopic[];

  @OneToMany(() => DataRequest, (dataRequest) => dataRequest.project)
  dataRequests: DataRequest[];
}
