"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1721055552467 = void 0;
class SchemaUpdate1721055552467 {
    constructor() {
        this.name = 'SchemaUpdate1721055552467';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "chat_history"
          ALTER COLUMN "title" DROP DEFAULT`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "chat_history"
          ALTER COLUMN "title" SET DEFAULT ''`);
    }
}
exports.SchemaUpdate1721055552467 = SchemaUpdate1721055552467;
//# sourceMappingURL=1721055552467-schema-update.js.map