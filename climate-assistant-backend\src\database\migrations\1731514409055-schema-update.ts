import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1731514409055 implements MigrationInterface {
  name = 'SchemaUpdate1731514409055';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" ADD "reportTextGenerationRules" text NOT NULL DEFAULT ''`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" DROP COLUMN "reportTextGenerationRules"`,
    );
  }
}
