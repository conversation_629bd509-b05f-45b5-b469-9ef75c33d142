// @ts-expect-error TODO look into this later
import { type SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Types
type ScoreLevel = 'Outstanding' | 'Advanced' | 'Good' | 'Partial' | 'Insufficient';

export interface LLMGapAnalysis {
  metadata: Metadata;
  detailed_analysis: DetailedAnalysis;
  current_score_assessment: CurrentScoreAssessment;
  action_prioritization: string;
  conclusion: string;
}
export interface Metadata {
  document_ids?: (string)[] | null;
  themes?: (string)[] | null;
  indicators?: (string)[] | null;
}
export interface DetailedAnalysis {
  document_review: string;
  criteria_checklist: string;
  gap_analysis?: (GapAnalysisEntity)[] | null;
}
export interface GapAnalysisEntity {
  gap_type: string;
  gap: string;
  description: string;
  affected_documents?: (string)[] | null;
  recommended_actions?: (string)[] | null;
  sample_text: string;
}
export interface CurrentScoreAssessment {
  score: number;
  level: ScoreLevel;
  scoring_breakdown_markdown: string;
}

type StoreResponseResult = {
  success: boolean;
  gapsInserted: string[];
  error?: string;
  details?: string;
};

/**
 * Stores the Dify response in the database
 * @param difyResponse The Dify response to store
 * @param supabaseClient An authenticated Supabase client
 * @returns A result object indicating success and stored data IDs
 */
export async function storeLLMGapAnalysisResponse({
  gapsListed,
  supabaseClient,
  projectId,
  questionId,
  projectQuestionId
}: {
  gapsListed: GapAnalysisEntity[],
  supabaseClient: SupabaseClient,
  projectId: string,
  questionId: string,
  projectQuestionId: string,
}): Promise<StoreResponseResult> {
  let gapAnalysis: Record<string, string>[] = [];

  if (gapsListed && Array.isArray(gapsListed)) {
    gapAnalysis = gapsListed.map(gap => {
      return {
        Title: gap.gap || "",
        Description: gap.description || "",
        "Sample Text": gap.sample_text || "",
        "Related Document": Array.isArray(gap.affected_documents) ? gap.affected_documents.join(", ") : "",
        "Recommended Actions": Array.isArray(gap.recommended_actions) ? gap.recommended_actions.join("\n") : "",
        // "Impact/Effort Evaluation": gap.impact_effort || "Not specified"
      };
    });
  }

  // 4. Process and store gap analysis data
  const gapsInserted: string[] = [];

  // First, check for existing gaps to avoid duplication
  type ExistingGap = { id: string; gaps: Record<string, string> };

  const { data: existingGaps, error: existingGapsError } = await supabaseClient
    .from('project_ecovadis_gaps')
    .select('id, gaps')
    .eq('questionId', questionId)
    .eq('projectId', projectId);

  if (existingGapsError) {
    console.error('Error checking existing gaps:', existingGapsError);
  }

  // Function to check if a gap already exists (based on title)
  const gapExists = (newGap: Record<string, string>, existingGaps: ExistingGap[] | null): boolean => {
    if (!existingGaps || existingGaps.length === 0) return false;

    return existingGaps.some(existing => {
      try {
        const existingGapData = existing.gaps;
        return existingGapData &&
          existingGapData.Title &&
          newGap.Title &&
          existingGapData.Title.toLowerCase() === newGap.Title.toLowerCase();
      } catch (e) {
        return false;
      }
    });
  };

  // Process each gap
  for (const gap of gapAnalysis) {
    // Skip if gap already exists
    if (gapExists(gap, existingGaps)) continue;

    // Extract document references from the "Related Document" field
    const documentRefs = gap["Related Document"] ?
      gap["Related Document"].split(',').map(doc => {
        // validate if proper uuid
        const trimmed = doc.trim();
        if (!trimmed.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)) {
          console.error('Invalid document ID:', trimmed);
          return null;
        }
        return trimmed;
      })
        .filter(doc => doc !== null) as string[]
      : [];

    // Insert the new gap
    const gapEntry = {
      questionId: questionId,
      projectId: projectId,
      gaps: gap,
      documents: documentRefs,
      resolved: false,
      assigneeId: null,
      deadline: null
    };

    const { data: newGap, error: newGapError } = await supabaseClient
      .from('project_ecovadis_gaps')
      .insert(gapEntry)
      .select('id')
      .single();

    if (newGapError) {
      console.error('Error creating gap:', newGapError);
    } else if (newGap) {
      gapsInserted.push(newGap.id);
    }
  }

  // 6. Return success response
  return {
    success: true,
    gapsInserted: gapsInserted,
  };
}

