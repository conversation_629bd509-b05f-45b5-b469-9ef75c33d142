import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { Language } from 'src/project/entities/project.entity';
export declare class NumericsPromptService {
    private readonly turndownService;
    constructor();
    generateNumericDatapointContentGenerationSystemPrompt({ esrsDatapoint, generationLanguage, reportTextGenerationRules, customUserRemark, currentContent, otherDatapoints, generalCompanyProfile, reportingYear, linkedChunks, }: {
        esrsDatapoint: ESRSDatapoint;
        generationLanguage: Language;
        reportTextGenerationRules: string;
        customUserRemark: string;
        currentContent: string;
        linkedChunks: string;
        generalCompanyProfile: string;
        reportingYear: string;
        otherDatapoints: ESRSDatapoint[];
    }): string;
}
