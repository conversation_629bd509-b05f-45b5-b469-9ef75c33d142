import { SupabaseService } from './supabase.service';
import { Role } from 'src/users/entities/user-workspace.entity';
import { QuestionnaireService } from './questionnaire.service';
import { EnhancedEcoVadisAnswerAgentService, EcoVadisResponse } from './answer-linking.service';
export declare class SupabaseController {
    private readonly supabaseService;
    private readonly questionnaireService;
    private readonly ecoVadisAnswerAgentService;
    constructor(supabaseService: SupabaseService, questionnaireService: QuestionnaireService, ecoVadisAnswerAgentService: EnhancedEcoVadisAnswerAgentService);
    getProfile(req: any): Promise<import("../../users/entities/user.entity").User>;
    initiateUserMigration(req: any): Promise<{
        message: string;
    }>;
    createUser(createUserDto: {
        email: string;
        name: string;
        password: string;
        options: {
            workspaceId?: string;
            workspaceName?: string;
            role?: Role;
        };
    }): Promise<{
        create: {
            user: import("../../users/entities/user.entity").User;
            workspaceId?: string;
        };
    }>;
    uploadQuestionnaire(req: any, projectId: string, file: Express.Multer.File): Promise<{
        message: string;
        result: void;
    }>;
    answerEcoVadisQuestion(req: any, requestDto: {
        questionId: string;
        projectId: string;
        useGemini: boolean;
    }): Promise<{
        message: string;
        result: EcoVadisResponse;
    }>;
}
