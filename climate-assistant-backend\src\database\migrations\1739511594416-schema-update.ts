import { MigrationInterface, QueryRunner } from 'typeorm';

// Migration Reason
// To add a new column queue_status so that the main status of DP or DR continues being in it's old state.
// queue_Status will only be used to store the current queue status of the dp or dr
export class SchemaUpdate1739511594416 implements MigrationInterface {
  name = 'SchemaUpdate1739511594416';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE "datapoint_request" SET "status" = 'incomplete_data' WHERE "status" = 'queued_for_generation'`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."datapoint_request_queuestatus_enum" AS ENUM('queued_for_generation', 'queued_for_review')`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" ADD "queueStatus" "public"."datapoint_request_queuestatus_enum" DEFAULT null`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."data_request_queuestatus_enum" AS ENUM('queued_for_generation', 'queued_for_review')`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_request" ADD "queueStatus" "public"."data_request_queuestatus_enum" DEFAULT null`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."datapoint_request_status_enum" RENAME TO "datapoint_request_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."datapoint_request_status_enum" AS ENUM('not_answered', 'incomplete_data', 'no_data', 'complete_data')`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" ALTER COLUMN "status" TYPE "public"."datapoint_request_status_enum" USING "status"::"text"::"public"."datapoint_request_status_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."datapoint_request_status_enum_old"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."datapoint_request_status_enum_old" AS ENUM('not_answered', 'incomplete_data', 'no_data', 'complete_data', 'queued_for_generation')`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" ALTER COLUMN "status" TYPE "public"."datapoint_request_status_enum_old" USING "status"::"text"::"public"."datapoint_request_status_enum_old"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."datapoint_request_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."datapoint_request_status_enum_old" RENAME TO "datapoint_request_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_request" DROP COLUMN "queueStatus"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."data_request_queuestatus_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" DROP COLUMN "queueStatus"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."datapoint_request_queuestatus_enum"`,
    );
  }
}
