"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DocumentController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentController = void 0;
const common_1 = require("@nestjs/common");
const upload_utils_1 = require("../util/upload-utils");
const swagger_1 = require("@nestjs/swagger");
const document_service_1 = require("./document.service");
const datapoint_document_chunk_service_1 = require("../datapoint-document-chunk/datapoint-document-chunk.service");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const bulk_reindex_dto_1 = require("./entities/bulk-reindex.dto");
const roles_decorator_1 = require("../auth/roles.decorator");
const user_workspace_entity_1 = require("../users/entities/user-workspace.entity");
const document_guard_1 = require("./document.guard");
const supabase_auth_guard_1 = require("../auth/supabase/supabase.auth.guard");
const path_1 = require("path");
const fs_1 = require("fs");
const ecovadis_issue_parser_service_1 = require("./ecovadis-issue-parser.service");
let DocumentController = DocumentController_1 = class DocumentController {
    constructor(documentService, datapointDocumentChunkService, ecovadisIssueParserService, dataSource) {
        this.documentService = documentService;
        this.datapointDocumentChunkService = datapointDocumentChunkService;
        this.ecovadisIssueParserService = ecovadisIssueParserService;
        this.dataSource = dataSource;
        this.logger = new common_1.Logger(DocumentController_1.name);
    }
    async getDocumentUploads(req) {
        const workspace = req.user.workspaceId;
        const documentUploads = await this.documentService.getWorkspaceDocumentUploads(workspace);
        return documentUploads;
    }
    async saveDocument(req, file) {
        const workspaceId = req.user.workspaceId;
        const userId = req.user.id;
        const { path, originalname } = file;
        const { documentType, esrsCategory: esrsCategoryString, year: yearString, month: monthString, day: dayString, remarks, answerId, pageNumbers, premiumParse: premiumParseString, } = req.body;
        const esrsCategory = esrsCategoryString
            ? esrsCategoryString.split(',')
            : [];
        const year = yearString ? parseInt(yearString) : null;
        const month = monthString && monthString !== '' && parseInt(monthString)
            ? parseInt(monthString)
            : null;
        const day = dayString && dayString !== '' && parseInt(dayString)
            ? parseInt(dayString)
            : null;
        const premiumParse = premiumParseString === 'true';
        await this.documentService.saveDocument({
            originalname,
            path,
            workspaceId,
            userId,
            documentType,
            esrsCategory,
            year,
            month,
            day,
            remarks,
            pageNumbers,
            answerId,
            premiumParse,
        });
        return { message: 'Document uploaded and queued for extraction' };
    }
    async parseEcovadisIssues(req, file) {
        const { path } = file;
        const { projectId } = req.body;
        await this.ecovadisIssueParserService.parseEcovadisSustainabilityIssuePDF(path, projectId);
        return { message: 'Document uploaded and extracted' };
    }
    async getDocument(id) {
        return await this.documentService.getDocumentData(id);
    }
    async updateDocument(req, id) {
        const workspaceId = req.user.workspaceId;
        const userId = req.user.id;
        const { documentType, esrsCategory, year, month, day, remarks } = req.body;
        await this.documentService.updateDocumentSettings({
            id,
            workspaceId,
            userId,
            documentType,
            esrsCategory,
            year,
            month,
            day,
            remarks,
        });
        return { message: 'Document updated successfully' };
    }
    async downloadFile(req, id, res) {
        const document = req.document;
        const filePath = (0, path_1.join)(process.cwd(), document.path);
        try {
            await fs_1.promises.access(filePath);
        }
        catch (err) {
            throw new common_1.NotFoundException(`File does not exist on the server.`);
        }
        const safeFileName = document.name.replace(/[^\w.-]/g, '_');
        res.setHeader('Content-Disposition', `attachment; filename="${safeFileName}"`);
        res.setHeader('Content-Type', 'application/octet-stream');
        res.status(common_1.HttpStatus.OK);
        const fileStream = (0, fs_1.createReadStream)(filePath);
        fileStream.pipe(res);
    }
    async extractChunks(req, id) {
        this.logger.log('Document Extraction Started');
        await this.documentService.extractDocumentChunks(id, req.body.premiumMode || true);
        return { message: 'Document Extraction Finished' };
    }
    async indexChunks(req, id) {
        this.logger.log('Document Indexing Started');
        await this.documentService.reindexDocument(id);
        return { message: 'Document Indexing Finished' };
    }
    async reindexSingleDocument(req, documentId) {
        this.logger.log(`Super Admin reindex started for document: ${documentId}`);
        await this.documentService.reindexDocument(documentId);
        this.logger.log(`Super Admin reindex completed for document: ${documentId}`);
        return {
            message: 'Document reindexed successfully',
            documentId: documentId,
        };
    }
    async reindexWorkspaceDocuments(req, workspaceId) {
        this.logger.log(`Super Admin workspace reindex started for workspace: ${workspaceId}`);
        const results = await this.documentService.bulkReindexDocuments({
            workspaceId: workspaceId,
        });
        this.logger.log(`Super Admin workspace reindex completed for workspace: ${workspaceId}. Success: ${results.success.length}, Failed: ${results.failed.length}`);
        return {
            message: `Workspace reindex completed. Processed ${results.success.length + results.failed.length} documents.`,
            results,
        };
    }
    async bulkIndexChunks(req, payload) {
        this.logger.log('Bulk Document Indexing Started');
        const results = await this.documentService.bulkReindexDocuments({
            documentIds: payload.documentIds,
            workspaceId: payload.workspaceId,
        });
        this.logger.log(`Bulk Document Indexing Finished. Success: ${results.success.length}, Failed: ${results.failed.length}`);
        return {
            message: 'Bulk Document Indexing Finished',
            results,
        };
    }
    async linkDocumentToDatapoints(req, documentId, payload) {
        const response = await this.datapointDocumentChunkService.linkDocumentChunksToDatapoints(documentId, req.user.id, payload.maxNumberOfChunks);
        return JSON.stringify(response);
    }
    async deleteDocument(req, id) {
        const workspaceId = req.user.workspaceId;
        await this.documentService.deleteDocument(id, workspaceId);
        return { success: true };
    }
    async returnAllDocumentLinks() {
        const result = await this.dataSource.query(`Select esrs_datapoint."datapointId", content from esrs_datapoint cross join document_chunk where document_chunk."matchingsJson" like '%' || esrs_datapoint."datapointId" ||'%'`);
        return result;
    }
    async getDocumentChunk(id) {
        return await this.documentService.getDocumentChunk(id);
    }
    async deleteDocumentChunk(req, id) {
        const workspaceId = req.user.workspaceId;
        const userId = req.user.id;
        return await this.documentService.deleteDocumentChunk({
            id,
            workspaceId,
            userId,
        });
    }
    async linkDatapointsToChunk(id, req, body) {
        const userId = req.user.id;
        await this.documentService.bulkUpdateDocumentChunkMap({
            documentChunkId: id,
            userId,
            data: body,
        });
        return { message: 'Datapoints linked to document chunk' };
    }
};
exports.DocumentController = DocumentController;
__decorate([
    (0, common_1.Get)(''),
    (0, swagger_1.ApiOperation)({ summary: 'Get all document uploads' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document uploads retrieved successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "getDocumentUploads", null);
__decorate([
    (0, common_1.Post)(''),
    (0, common_1.UseInterceptors)(upload_utils_1.fileInterceptor),
    (0, swagger_1.ApiOperation)({ summary: 'Upload a new document' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Document uploaded successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "saveDocument", null);
__decorate([
    (0, common_1.Post)('ecovadis-issue-parser'),
    (0, common_1.UseInterceptors)(upload_utils_1.fileInterceptor),
    (0, swagger_1.ApiOperation)({ summary: 'Upload a new document for Ecovadis issue parsing' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Document uploaded successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "parseEcovadisIssues", null);
__decorate([
    (0, common_1.UseGuards)(document_guard_1.DocumentGuard),
    (0, common_1.Get)('/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific document by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Document retrieved successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "getDocument", null);
__decorate([
    (0, common_1.UseGuards)(document_guard_1.DocumentGuard),
    (0, common_1.Put)('/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update document settings' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Document settings updated' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "updateDocument", null);
__decorate([
    (0, common_1.UseGuards)(document_guard_1.DocumentGuard),
    (0, common_1.Get)(':id/download'),
    (0, common_1.Header)('Access-Control-Expose-Headers', 'Content-Disposition'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "downloadFile", null);
__decorate([
    (0, common_1.UseGuards)(document_guard_1.DocumentGuard),
    (0, common_1.Post)('/:id/extract-chunks'),
    (0, swagger_1.ApiOperation)({ summary: 'Extract chunks from a document' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document chunks extracted successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "extractChunks", null);
__decorate([
    (0, common_1.UseGuards)(document_guard_1.DocumentGuard),
    (0, common_1.Post)('/:id/index-chunks'),
    (0, swagger_1.ApiOperation)({ summary: 'Index chunks from a document' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document chunks indexed successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "indexChunks", null);
__decorate([
    (0, common_1.Post)('/:id/reindex'),
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin),
    (0, swagger_1.ApiOperation)({
        summary: 'Reindex a single document (Super Admin only)',
        description: 'Deletes existing vectors and reindexes all chunks for a specific document',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document reindexed successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "reindexSingleDocument", null);
__decorate([
    (0, common_1.Post)('/workspace/:workspaceId/reindex'),
    (0, swagger_1.ApiOperation)({
        summary: 'Reindex all documents in a workspace (Super Admin only)',
        description: 'Deletes existing vectors and reindexes all documents within a workspace',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Workspace documents reindexed successfully',
        type: 'BulkReindexResultDto',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('workspaceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "reindexWorkspaceDocuments", null);
__decorate([
    (0, common_1.Post)('/bulk/index-chunks'),
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin),
    (0, swagger_1.ApiOperation)({
        summary: 'Bulk reindex multiple documents or all documents in a workspace',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Bulk document reindexing completed',
        type: 'BulkReindexResultDto',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, bulk_reindex_dto_1.BulkReindexDto]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "bulkIndexChunks", null);
__decorate([
    (0, common_1.UseGuards)(document_guard_1.DocumentGuard),
    (0, common_1.Post)('/:id/link-to-datapoints'),
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin),
    (0, swagger_1.ApiOperation)({ summary: 'Link document to datapoints' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document linked to datapoints successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "linkDocumentToDatapoints", null);
__decorate([
    (0, common_1.UseGuards)(document_guard_1.DocumentGuard),
    (0, common_1.Delete)('/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a specific document by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Document deleted successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "deleteDocument", null);
__decorate([
    (0, common_1.Get)('/all-links'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all document links' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document links retrieved successfully',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "returnAllDocumentLinks", null);
__decorate([
    (0, common_1.UseGuards)(document_guard_1.DocumentChunkGuard),
    (0, common_1.Get)('/chunk/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific document chunk by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document chunk retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "getDocumentChunk", null);
__decorate([
    (0, common_1.Delete)('/chunk/:id'),
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a specific document chunk by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document chunk deleted successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "deleteDocumentChunk", null);
__decorate([
    (0, common_1.UseGuards)(document_guard_1.DocumentChunkGuard),
    (0, common_1.Post)('/chunk/:id/link-datapoints'),
    (0, swagger_1.ApiOperation)({ summary: 'Link datapoints to a document chunk' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoints linked to document chunk successfully',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Array]),
    __metadata("design:returntype", Promise)
], DocumentController.prototype, "linkDatapointsToChunk", null);
exports.DocumentController = DocumentController = DocumentController_1 = __decorate([
    (0, swagger_1.ApiTags)('documents'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.AuthGuard),
    (0, common_1.Controller)('documents'),
    __param(3, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [document_service_1.DocumentService,
        datapoint_document_chunk_service_1.DatapointDocumentChunkService,
        ecovadis_issue_parser_service_1.EcovadisIssueParserService,
        typeorm_2.DataSource])
], DocumentController);
//# sourceMappingURL=document.controller.js.map