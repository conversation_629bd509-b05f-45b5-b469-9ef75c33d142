import { CanActivate, ExecutionContext } from '@nestjs/common';
import { DocumentService } from './document.service';
export declare class DocumentGuard implements CanActivate {
    private readonly documentService;
    constructor(documentService: DocumentService);
    canActivate(context: ExecutionContext): Promise<boolean>;
}
export declare class DocumentChunkGuard implements CanActivate {
    private readonly documentService;
    constructor(documentService: DocumentService);
    canActivate(context: ExecutionContext): Promise<boolean>;
}
