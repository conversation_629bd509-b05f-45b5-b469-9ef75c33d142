import { Language } from './project/entities/project.entity';

export enum LLM_MODELS {
  'gpt-4o' = 'gpt-4o',
  'gpt-4o-mini' = 'gpt-4o-mini',
  'o1-preview' = 'o1-preview',
  'o1' = 'o1',
  'o3-mini' = 'o3-mini',
  'deepseek-r1' = 'deepseek-r1',
  'o3' = 'o3',
  'o4-mini' = 'o4-mini',
  // Gemini models via OpenAI compatibility layer
  'gemini-2.5-pro' = 'gemini-2.5-pro',
  'gemini-2.0-flash' = 'gemini-2.0-flash',
  'gemini-2.5-flash' = 'gemini-2.5-flash',
}

export const LANGUAGE_MAP: Record<Language, string> = {
  [Language.BG]: 'Bulgarian',
  [Language.HR]: 'Croatian',
  [Language.CS]: 'Czech',
  [Language.DA]: 'Danish',
  [Language.NL]: 'Dutch',
  [Language.EN]: 'English',
  [Language.ET]: 'Estonian',
  [Language.FI]: 'Finnish',
  [Language.FR]: 'French',
  [Language.DE]: 'German',
  [Language.EL]: 'Greek',
  [Language.HU]: 'Hungarian',
  [Language.GA]: 'Irish',
  [Language.IT]: 'Italian',
  [Language.LV]: 'Latvian',
  [Language.LT]: 'Lithuanian',
  [Language.MT]: 'Maltese',
  [Language.PL]: 'Polish',
  [Language.PT]: 'Portuguese',
  [Language.RO]: 'Romanian',
  [Language.SK]: 'Slovak',
  [Language.SL]: 'Slovene',
  [Language.ES]: 'Spanish',
  [Language.SV]: 'Swedish',
};
