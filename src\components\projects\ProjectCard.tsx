
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Trash2, Calendar, PenLine, ArrowRight, Clock, Check, CircleDot, AlertCircle } from 'lucide-react';
import { EditProjectDialog } from './EditProjectDialog';
import { Project } from '@/types/project';

export interface ProjectCardProps {
  project: Project;
  onDelete: (id: string) => void;
  onUpdate?: (id: string, updatedProject: Project) => void;
}

export const ProjectCard: React.FC<ProjectCardProps> = ({ project, onDelete, onUpdate }) => {
  const navigate = useNavigate();

  const getProjectTypeColor = () => {
    switch(project.type) {
      case 'ecovadis':
        return 'bg-blue-100 text-blue-800';
      case 'csrd':
        return 'bg-green-100 text-green-800';
      case 'customer':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  const handleDeleteClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    const confirmDelete = window.confirm(`Are you sure you want to delete "${project.name}"?`);
    if (confirmDelete) {
      onDelete(project.id);
    }
  };

  const getProjectLink = () => {
    if (project.type?.toLowerCase() === "ecovadis") {
      return `/ecovadis-project/${project.id}`;
    }
    return `/dashboard`;
  }

  const isCustomerQuestionnaire = project.type === "customer";

  const progress = project.progress || {
    percentage: 0,
    complete: 0,
    incomplete: 0,
    gaps: 0
  };

  const handleUpdate = (updatedProject: Pick<Project, "name">) => {
      onUpdate(project.id, { ...project, ...updatedProject });
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-5">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{project.name}</h3>
            <div className="mt-2">
              <span className={`px-2 py-1 rounded-md text-xs font-medium ${getProjectTypeColor()}`}>
                {project.type?.charAt(0).toUpperCase() + project.type?.slice(1)}
              </span>
            </div>
            </div>
            <div className="flex gap-1">
            <EditProjectDialog 
              project={project} 
              onUpdate={handleUpdate} 
              trigger={
                <Button variant="ghost" size="icon">
                  <PenLine className="h-4 w-4" />
                </Button>
              }
            />

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="ghost" size="icon" disabled={true}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Project</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete "{project.name}"? This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={() => onDelete(project.id)}>
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
        {/* Progress percentage and label */}
        <div className="mb-4">
          <div className="flex justify-between text-sm mb-1">
            <span>Progress</span>
            <span>{progress.percentage}% complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className="bg-blue-600 h-2.5 rounded-full" 
              style={{ width: `${progress.percentage}%` }}
            ></div>
          </div>
        </div>

        {/* Status chips breakdown */}
        <div className="grid grid-cols-3 gap-1 mb-3">
          <div className="flex items-center">
            <Check className="h-4 w-4 mr-1.5 text-green-500" />
            <div className="text-xs text-gray-600">
              <span className="font-medium">{progress.complete}</span> Complete
            </div>
          </div>
          <div className="flex items-center">
            <CircleDot className="h-4 w-4 mr-1.5 text-amber-500" />
            <div className="text-xs text-gray-600">
              <span className="font-medium">{progress.incomplete}</span> Incomplete
            </div>
          </div>
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 mr-1.5 text-red-500" />
            <div className="text-xs text-gray-600">
              <span className="font-medium">{progress.gaps}</span> Gaps
            </div>
          </div>
          </div>
      </CardContent>

      <CardFooter className="p-4 pt-0 flex justify-end">
        {isCustomerQuestionnaire ? (
          <div className="flex items-center text-gray-500">
            <span className="text-sm font-medium flex items-center cursor-default">
              Coming Soon
              <Clock className="h-4 w-4 ml-1" />
            </span>
          </div>
        ) : (
          <Button variant="link" onClick={() => navigate(getProjectLink())} className="text-blue-600 p-0">
            View Details
            <ArrowRight className="h-4 w-4 ml-1" />
          </Button>
        )}
      </CardFooter>
      </Card>
  );
};
