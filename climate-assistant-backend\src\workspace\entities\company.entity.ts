// entities/company.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Workspace } from './workspace.entity';

@Entity()
export class Company {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'text', nullable: true })
  generalCompanyProfile: string;

  @Column({ type: 'text', nullable: true })
  reportTextGenerationRules: string;

  @Column({ type: 'uuid' })
  workspaceId: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @ManyToOne(() => Workspace, (workspace) => workspace.companies)
  @JoinColumn({ name: 'workspaceId' })
  workspace: Workspace;
}
