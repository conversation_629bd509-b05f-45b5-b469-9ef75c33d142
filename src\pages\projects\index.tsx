
import { useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'sonner';
import { MainLayout } from '@/components/MainLayout';
import { EmptyState } from '@/components/ui/empty-state';
import { ProjectCard } from '@/components/projects/ProjectCard';
import CreateProjectDialog from '@/components/projects/CreateProjectDialog';
import { Award, ClipboardList, FileText } from 'lucide-react';
import { useProjects, useUpdateProject } from '@/hooks/useProjects';
import { ProjectsLoadingState } from '@/components/projects/ProjectsLoadingState';
import { Project } from '@/types/project';
import { useUserRole } from '@/hooks/useUserRole';
import { canCreateProject } from '@/utils/userWorkspaceUtils';

const ProjectsPage = () => {
  const { data: projects, isLoading: loading, isError: error } = useProjects();
  const updateProjectMutation = useUpdateProject();
  const { getUserRole } = useUserRole();
  const userRole = getUserRole();
  const canCreate = canCreateProject(userRole);
  
  // Handle project creation (still saving to localStorage for backward compatibility)
  const handleProjectCreated = (projectData: any) => {
    const newProject = {
      id: uuidv4(),
      name: projectData.name,
      type: projectData.type || 'ecovadis', // Ensure type is always set
      deadline: projectData.deadline,
      progress: {
        percentage: 0,
        complete: 0,
        incomplete: 0,
        gaps: 0
      }
    };
    
    toast.success("Project created", {
      description: `${newProject.name} has been added to your projects.`,
    });
    
    // Refresh the page to load the new project
    window.location.reload();
  };
  
  const handleDeleteProject = (id: string) => {
    
    // Refresh the page to update the projects list
    window.location.reload();
  };

  const handleUpdateProject = (id: string, updatedProject: Pick<Project, 'name'>) => {
    try {
      // Update with Supabase
      updateProjectMutation.mutate({
        projectId: id,
        updates: {name: updatedProject.name }
      });
      
    } catch (error) {
      console.error('Error updating project:', error);
    }
  };

  // Handle error in fetching projects
  useEffect(() => {
    if (error) {
      toast.error("Error loading projects", {
        description: error,
      });
    }
  }, [error]);

  return (
    <MainLayout>
      <div className='m-4'>
      <div className="mb-6 mt-8">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-glacier-darkBlue">Projects</h1>
          {canCreate && <CreateProjectDialog onProjectCreated={handleProjectCreated} />}
        </div>
        <p className="text-gray-500 mt-1">
          Create and manage your sustainability projects
        </p>
      </div>

      {loading ? (
        <ProjectsLoadingState />
      ) : projects?.length === 0 ? (
        <EmptyState 
          title="No Projects Yet" 
          description="Create your first sustainability project to begin tracking and improving your performance." 
          icon={<div className="flex space-x-2">
            <Award className="h-8 w-8 text-gray-400" />
            <ClipboardList className="h-8 w-8 text-gray-400" />
            <FileText className="h-8 w-8 text-gray-400" />
          </div>}
          action={canCreate ? {
            label: "Create New Project",
            onClick: () => document.getElementById('create-project-trigger')?.click()
          } : undefined}
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map((project) => (
            <ProjectCard 
              key={project.id} 
              project={project} 
              onDelete={handleDeleteProject}
              onUpdate={handleUpdateProject}
            />
          ))}
        </div>
      )}
      </div>
    </MainLayout>
  );
};

export default ProjectsPage;
