/**
 * Converts an EcoVadis theme and indicator to a standardized key string
 * @param {string} theme - The EcoVadis theme
 * @param {string} indicator - The EcoVadis indicator enum value
 * @returns {string} Formatted key string
 */
export function formatScoringFrameworkKey({theme, indicator}: {theme: string, indicator: string}): string {
    // Step 1: Process the theme
    // Convert to lowercase and replace spaces with underscores
    let processedTheme = theme.toLowerCase().trim().replace(/\s+/g, '_');
    
    // Handle special cases and normalize theme names
    if (processedTheme === 'arbeits-_&_menschenrechte') {
        processedTheme = 'labor_&_human_rights';
    } else if (processedTheme === 'ethik') {
        processedTheme = 'ethics';
    } else if (processedTheme === 'umwelt' || processedTheme === 'environment_dep') {
        processedTheme = 'environment';
    } else if (processedTheme === 'nachhaltige_beschaffung') {
        processedTheme = 'sustainable_procurement';
    } else if (processedTheme === 'allgemeines' || processedTheme === 'general') {
        // Not sure how to handle general themes, defaulting to empty
        processedTheme = '';
    }
    
    // Step 2: Process the indicator to get the prefix
    let prefix;
    switch (indicator) {
        case 'WATCH_FINDINGS':
            prefix = '360-watch-findings';
            break;
        default:
            prefix = indicator.toLowerCase().replace(/_/g, '-');
            break;
    }
    
    // Step 3: Combine prefix and theme to create the key
    return processedTheme ? `${prefix}:${processedTheme}` : prefix;
}