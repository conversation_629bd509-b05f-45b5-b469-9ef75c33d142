
import { X } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

type ChecklistItem = {
  id: string;
  label: string;
  checked: boolean;
  matchingDocuments: string[];
};

interface DocumentsChecklistModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DocumentsChecklistModal({
  open,
  onOpenChange,
}: DocumentsChecklistModalProps) {
  // Sample checklist data
  const checklistItems: ChecklistItem[] = [
    {
      id: "ghg-policy",
      label: "Policy for GHGs",
      checked: true,
      matchingDocuments: ["ESG Strategie.pdf", "Supply Chain.pdf", "ESG Strategie.pdf"],
    },
    {
      id: "csr-policy",
      label: "CSR Policy",
      checked: false,
      matchingDocuments: [],
    },
    {
      id: "sustainability-reports",
      label: "Sustainability Reports",
      checked: false,
      matchingDocuments: [],
    },
    {
      id: "audit-reports",
      label: "Audit Reports",
      checked: false,
      matchingDocuments: [],
    },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md md:max-w-lg">
        <DialogHeader>
          <DialogTitle className="text-xl font-medium text-glacier-darkBlue">
            Document check list
          </DialogTitle>
        </DialogHeader>
        
        <button
          onClick={() => onOpenChange(false)}
          className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
          aria-label="Close"
        >
          <X className="h-4 w-4 text-glacier-darkBlue" />
        </button>
        
        <div className="px-1">
          <p className="text-sm text-gray-600 mb-4">
            After you upload a matching document, its checklist item will be checked off automatically.
          </p>
          
          <div className="border-t border-gray-200 pt-4">
            <ul className="space-y-6">
              {checklistItems.map((item) => (
                <li key={item.id} className="flex items-start">
                  <div className="flex gap-3 items-center w-1/2">
                    <Checkbox id={item.id} checked={item.checked} className="mt-1 h-5 w-5" />
                    <label
                      htmlFor={item.id}
                      className="text-base font-medium text-glacier-darkBlue leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {item.label}
                    </label>
                  </div>
                  
                  <div className="w-1/2 flex flex-col gap-2">
                    {item.matchingDocuments.length > 0 ? (
                      item.matchingDocuments.map((doc, index) => (
                        <span key={index} className="text-sm text-glacier-darkBlue underline">
                          {doc}
                        </span>
                      ))
                    ) : (
                      <span className="text-sm text-gray-400 italic">No matching documents</span>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
