import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Workspace } from '../../workspace/entities/workspace.entity';
import { DocumentChunk } from './document-chunk.entity';
import { User } from '../../users/entities/user.entity';

export enum DocumentStatus {
  NotProcessed = 'not_processed',
  QueuedForExtraction = 'queued_for_extraction',
  ExtractingData = 'in_extraction',
  FailedExtraction = 'failed_extraction',
  DataExtractionFinished = 'data_extraction_finished',
  QueuedForLinking = 'queued_for_linking',
  LinkingData = 'linking_data',
  FailedLinking = 'failed_linking',
  LinkingDataFinished = 'linking_data_finished',
  ErrorProcessing = 'error_processing',
}

@Entity()
export class Document {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  workspaceId: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  path: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({
    type: 'enum',
    enum: DocumentStatus,
    default: DocumentStatus.NotProcessed,
  })
  status: DocumentStatus;

  @ManyToOne(() => Workspace, (workspace) => workspace.documents)
  @JoinColumn({ name: 'workspaceId' })
  workspace: Workspace;

  @OneToMany(() => DocumentChunk, (chunk) => chunk.document)
  chunks: DocumentChunk[];

  @Column({ type: 'varchar', nullable: true })
  documentType: string;

  @Column({ type: 'simple-json', nullable: true })
  esrsCategory: string[];

  @Column({ type: 'int', nullable: true })
  year: number;

  @Column({ type: 'int', nullable: true })
  month: number;

  @Column({ type: 'int', nullable: true })
  day: number;

  @Column({ type: 'text', nullable: true })
  remarks: string;

  @Column({ type: 'uuid', nullable: true })
  createdBy: string;

  @ManyToOne(() => User, (user) => user.documents)
  @JoinColumn({ name: 'createdBy' })
  creator: User;
}
