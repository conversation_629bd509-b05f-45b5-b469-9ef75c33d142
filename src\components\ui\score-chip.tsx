
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export interface ScoreChipProps {
  score?: number | null;
  className?: string;
}

export const ScoreChip: React.FC<ScoreChipProps> = ({ score, className }) => {
  if (score === undefined || score === null) {
    return (
      <Badge variant="outline" className={cn("bg-gray-100 text-gray-700", className)}>
        N/A
      </Badge>
    );
  }

  let colorClass = "";
  
  if (score >= 75) {
    colorClass = "bg-green-100 text-green-800";
  } else if (score >= 50) {
    colorClass = "bg-amber-100 text-amber-800";
  } else {
    colorClass = "bg-red-100 text-red-800";
  }

  return (
    <Badge variant="outline" className={cn(colorClass, className)}>
      {score}
    </Badge>
  );
};

export default ScoreChip;
