
import {
  ArrowDownIcon,
  ArrowRightIcon,
  ArrowUpIcon,
  LoaderIcon,
  CheckIcon,
  X,
} from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { Link } from 'react-router-dom';

import { buttonVariants } from '../ui/button';
import { DataTableColumnHeader } from '../data-table/DataTableColumnHeader';

import { DocumentStatusLabel } from './DocumentStatusLabel';
import { DocumentsTableActions } from './DocumentsTableActions';

import type { ESRSCategoryIds } from '@/constants/documentsConstants';
import { ESRS_CATEGORIES } from '@/constants/documentsConstants';
import { DocumentStatus } from '@/types/document';
import { Checkbox } from '@/components/ui/checkbox';
import { COLUMNS_ID_MAPPING } from '@/constants/table-constants';

export const labels = [
  {
    value: 'bug',
    label: 'Bug',
  },
  {
    value: 'feature',
    label: 'Feature',
  },
  {
    value: 'documentation',
    label: 'Documentation',
  },
];

export const documentStatuses = [
  {
    value: DocumentStatus.NotProcessed,
    label: 'Not processed',
    icon: LoaderIcon,
    color: '#718096', // Darker gray for "Not Reported"
  },
  {
    value: DocumentStatus.ExtractingData,
    label: 'Extracting Data',
    icon: LoaderIcon,
    color: '#DD6B20', // Darker orange
  },
  {
    value: DocumentStatus.QueuedForExtraction,
    label: 'Queued for Extraction',
    icon: LoaderIcon,
    color: '#DD6B20', // Darker orange
  },
  {
    value: DocumentStatus.DataExtractionFinished,
    label: 'Data extracted',
    icon: CheckIcon,
    color: '#3182CE', // Darker blue
  },
  {
    value: DocumentStatus.LinkingData,
    label: 'Linking Data',
    icon: LoaderIcon,
    color: '#D69E2E', // Darker yellow
  },
  {
    value: DocumentStatus.QueuedForLinking,
    label: 'Queued for linking',
    icon: LoaderIcon,
    color: '#D69E2E', // Darker yellow
  },
  {
    value: DocumentStatus.LinkingDataFinished,
    label: 'Data Linked',
    icon: CheckIcon,
    color: '#38A169', // Darker green for
  },
  {
    value: DocumentStatus.FailedLinking,
    label: 'Failed Linking',
    icon: X,
    color: '#E53E3E', // Darker red
  },
  {
    value: DocumentStatus.FailedExtraction,
    label: 'Failed Extraction',
    icon: X,
    color: '#E53E3E', // Darker red
  },
  {
    value: DocumentStatus.ErrorProcessing,
    label: 'Error Processing',
    icon: X,
    color: '#E53E3E', // Darker red
  },
];

export const priorities = [
  {
    label: 'Low',
    value: 'low',
    icon: ArrowDownIcon,
  },
  {
    label: 'Medium',
    value: 'medium',
    icon: ArrowRightIcon,
  },
  {
    label: 'High',
    value: 'high',
    icon: ArrowUpIcon,
  },
];

export const documentsListingColumns: ColumnDef<any>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() ? "indeterminate" : false)
        }
        onCheckedChange={(value: any) =>
          table.toggleAllPageRowsSelected(!!value)
        }
        aria-label="Select all"
        className="translate-y-[2px] scale-110"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value: any) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px] scale-110"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title={COLUMNS_ID_MAPPING['name']}
      />
    ),
    cell: ({ row }) => {
      return (
        <Link
          className={buttonVariants({
            variant: 'link',
            className: '!p-0 text-wrap',
          })}
          to={`${row.original.id}`}
        >
          {row.getValue('name')}
        </Link>
      );
    },
  },
  {
    accessorKey: 'publishedOn',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title={COLUMNS_ID_MAPPING['publishedOn']}
      />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {row.original.day ? `${row.original.day}.` : ''}
            {row.original.month ? `${row.original.month}.` : ''}
            {row.original.year}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title={COLUMNS_ID_MAPPING['createdAt']}
      />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {new Date(row.getValue('createdAt')).toLocaleDateString('de-DE', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
            })}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'creator',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title={COLUMNS_ID_MAPPING['creator']}
      />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {row.getValue('creator')
              ? (row.getValue('creator') as { name: string }).name
              : ''}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'esrsCategory',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title={COLUMNS_ID_MAPPING['esrsCategory']}
      />
    ),
    cell: ({ row }) => {
      const categories = row.getValue('esrsCategory') as ESRSCategoryIds[];
      const esrsOrderforUI = Object.keys(ESRS_CATEGORIES);
      categories.sort(
        (c1, c2) => esrsOrderforUI.indexOf(c1) - esrsOrderforUI.indexOf(c2)
      );
      const categoryDetails = (categories || []).map(
        (category) => ESRS_CATEGORIES[category]
      );

      return (
        <div className="flex space-x-1">
          {categoryDetails?.map((category) => (
            <span
              className="inline-block rounded-md px-2 py-1 text-white whitespace-nowrap"
              style={{ backgroundColor: category.color }}
              key={category.label}
            >
              {category.label}
            </span>
          ))}
        </div>
      );
    },
    filterFn: (row, id, value) => {
      return value.some((val: string) => {
        const rowVals = row.getValue(id);
        if (Array.isArray(rowVals)) {
          return rowVals.includes(val);
        }
        return false;
      });
    },
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title={COLUMNS_ID_MAPPING['status']}
      />
    ),
    cell: ({ row }) => {
      const status = documentStatuses.find(
        (status: any) => status.value === row.getValue('status')
      );

      if (!status) {
        return null;
      }

      const clonedStatus = Object.assign({}, status);
      if (
        row.original.datapointsCount > 0 ||
        clonedStatus.value === DocumentStatus.LinkingDataFinished
      ) {
        clonedStatus.label = `Relevant for ${row.original.datapointsCount} Datapoints`;
      }

      return <DocumentStatusLabel status={clonedStatus}></DocumentStatusLabel>;
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'actions',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Actions" />
    ),
    cell: ({ row }) => <DocumentsTableActions id={row.original.id} />,
  },
];
