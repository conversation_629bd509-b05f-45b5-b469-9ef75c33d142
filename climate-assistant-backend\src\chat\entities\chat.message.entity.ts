import {
  <PERSON>umn,
  CreateDateColumn,
  <PERSON>tity,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ChatHistory } from './chat-history.entity';

@Entity()
export class ChatMessage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => ChatHistory, (chatHistory) => chatHistory.messages, {
    onDelete: 'CASCADE',
  })
  chatHistory: ChatHistory;

  @Column({
    type: 'varchar',
  })
  content: string;

  @Column({
    type: 'enum',
    enum: ['user', 'system', 'assistant', 'function'],
  })
  role: 'user' | 'system' | 'assistant' | 'function';

  @CreateDateColumn()
  createdAt: Date;
}
