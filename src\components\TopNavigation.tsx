import { Link } from 'react-router-dom';
import { FunctionComponent, useState } from 'react';
import { LogOutIcon, SettingsIcon, CircleHelp } from 'lucide-react';

import { Button, buttonVariants } from './ui/button';
import HelpDialog from './HelpDialog';

import { useAuthentication } from '@/api/authentication/authentication.query';
import { SideBarToggleButton, StartNewChatButton } from '@/components/Sidebar';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { topNavigationItems } from '@/lib/config';

const TopNavigation: FunctionComponent<{
  isSidebarOpen?: boolean;
  onSidebarToggle?: () => void;
}> = ({ isSidebarOpen, onSidebarToggle }) => {
  const [isHelpDialogOpen, setIsHelpDialogOpen] = useState<boolean>(false);
  const { logout } = useAuthentication();
  return (
    <>
      <div className="h-[90px] flex flex-row justify-between items-center">
        {onSidebarToggle && (
          <div>
            {!isSidebarOpen && (
              <div className={`flex flex-row items-center -mt-4 -ml-2`}>
                <SideBarToggleButton onClick={onSidebarToggle} />
                <StartNewChatButton />
              </div>
            )}
          </div>
        )}
        <div>
          <Link to="/" className="flex items-center space-x-2">
            <img
              className="h-[40px] -mt-2 cursor-pointer"
              src="/logo.png"
              alt="Glacier logo"
            ></img>
          </Link>
        </div>

        <div className="flex items-center justify-end gap-32">
          <div className="flex items-center justify-between gap-8">
            {topNavigationItems.map((item, index) => (
              <Link
                key={index}
                to={item.href}
                className={buttonVariants({
                  variant: 'link',
                })}
              >
                {item.title}
              </Link>
            ))}
          </div>

          <div className="flex items-center justify-end gap-5">
            <Tooltip delayDuration={0}>
              <TooltipTrigger>
                <CircleHelp
                  className="w-4 h-4 cursor-pointer"
                  onClick={() => setIsHelpDialogOpen(true)}
                />
              </TooltipTrigger>
              <TooltipContent side="bottom">Help</TooltipContent>
            </Tooltip>

            <Tooltip delayDuration={0}>
              <TooltipTrigger>
                <Link to={`/dashboard/settings`}>
                  <SettingsIcon className="w-4 h-4" />
                </Link>
              </TooltipTrigger>
              <TooltipContent side="bottom">Settings</TooltipContent>
            </Tooltip>

            <Tooltip delayDuration={0}>
              <TooltipTrigger>
                <Button variant="link" onClick={() => logout()}>
                  <LogOutIcon className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">Logout</TooltipContent>
            </Tooltip>
          </div>
        </div>
      </div>
      <HelpDialog
        isHelpDialogOpen={isHelpDialogOpen}
        setIsHelpDialogOpen={setIsHelpDialogOpen}
      />
    </>
  );
};

export { TopNavigation };
