"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const document_service_1 = require("./document.service");
const document_chunk_entity_1 = require("./entities/document-chunk.entity");
const document_entity_1 = require("./entities/document.entity");
const document_controller_1 = require("./document.controller");
const workspace_service_1 = require("../workspace/workspace.service");
const workspace_entity_1 = require("../workspace/entities/workspace.entity");
const document_parser_service_1 = require("./document-parser.service");
const ecovadis_issue_parser_service_1 = require("./ecovadis-issue-parser.service");
const datapoint_document_chunk_entity_1 = require("../datapoint-document-chunk/entities/datapoint-document-chunk.entity");
const prompts_service_1 = require("../prompts/prompts.service");
const workspace_module_1 = require("../workspace/workspace.module");
const datapoint_document_chunk_module_1 = require("../datapoint-document-chunk/datapoint-document-chunk.module");
const datapoint_request_entity_1 = require("../datapoint/entities/datapoint-request.entity");
const llm_rate_limiter_module_1 = require("../llm-rate-limiter/llm-rate-limiter.module");
const project_ecovadis_linked_document_chunks_entity_1 = require("../ecovadis/entities/project-ecovadis-linked-document-chunks.entity");
const queues_module_1 = require("../queues/queues.module");
const supabase_module_1 = require("../auth/supabase/supabase.module");
const chat_gpt_service_1 = require("../llm/chat-gpt.service");
const ecovadis_entity_1 = require("../ecovadis/entities/ecovadis.entity");
const dify_service_1 = require("../llm/dify.service");
let DocumentModule = class DocumentModule {
};
exports.DocumentModule = DocumentModule;
exports.DocumentModule = DocumentModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                document_chunk_entity_1.DocumentChunk,
                document_entity_1.Document,
                workspace_entity_1.Workspace,
                datapoint_request_entity_1.DatapointRequest,
                datapoint_document_chunk_entity_1.DatapointDocumentChunk,
                project_ecovadis_linked_document_chunks_entity_1.ProjectEcovadisLinkedDocumentChunks,
                ecovadis_entity_1.EcoVadisTheme,
                ecovadis_entity_1.ProjectEcoVadisTheme,
                ecovadis_entity_1.EcoVadisSustainabilityIssue,
                ecovadis_entity_1.EcoVadisQuestion,
                ecovadis_entity_1.EcoVadisAnswerOption,
                ecovadis_entity_1.ProjectEcoVadisAnswer,
            ]),
            supabase_module_1.SupabaseAuthModule,
            workspace_module_1.WorkspaceModule,
            llm_rate_limiter_module_1.LlmRateLimiterModule,
            datapoint_document_chunk_module_1.DatapointDocumentChunkModule,
            queues_module_1.QueuesModule,
        ],
        providers: [
            document_service_1.DocumentService,
            workspace_service_1.WorkspaceService,
            document_parser_service_1.DocumentParserService,
            dify_service_1.DifyService,
            ecovadis_issue_parser_service_1.EcovadisIssueParserService,
            chat_gpt_service_1.ChatGptService,
            prompts_service_1.PromptService,
        ],
        exports: [document_service_1.DocumentService],
        controllers: [document_controller_1.DocumentController],
    })
], DocumentModule);
//# sourceMappingURL=document.module.js.map