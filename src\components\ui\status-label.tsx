interface Status {
  value: string | number;
  label: string;
  color: string;
  icon?: any;
}

export const StatusLabel = ({
  status,
  statuses,
}: {
  status: string | number;
  statuses: Status[];
}) => {
  const matchedStatus = statuses.find(
    (tryStatus: any) => tryStatus.value === status
  )!;
  if (matchedStatus) {
    return (
      <div
        className="flex items-center rounded-md py-1 px-3 w-fit text-xs whitespace-nowrap font-normal"
        style={{
          backgroundColor: `${matchedStatus.color}1A`,
          color: matchedStatus.color,
        }}
      >
        {/* {matchedStatus.icon && (
            <matchedStatus.icon
              style={{
                color: matchedStatus.color,
              }}
              className="mr-2 h-4 w-4 text-muted-foreground"
            />
          )} */}
        {matchedStatus.label}
      </div>
    );
  } else {
    return <div></div>;
  }
};
