import { useEffect, useState } from 'react';
import { format } from 'date-fns';
import { CircleCheckBigIcon, CircleXIcon } from 'lucide-react';

import { toast } from '../../ui/use-toast';
import { MarkdownRenderer } from '../../ui/markdown-renderer';

import { CommentRejectionReasonModal } from './CommentRejectionReasonInputModal';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { CommentStatus, type CommentGenerationData } from '@/types/project';
import { updateCommentStatus } from '@/api/project-settings/project-settings.api';
import { fireConfetti } from '@/lib/confetti';
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Accordion,
} from '@/components/ui/accordion';

export const CommentGenerationSection = ({
  projectId,
  savedComments,
  updateCallback,
}: {
  projectId: string;
  savedComments: CommentGenerationData[];
  updateCallback?: () => void;
}) => {
  const [comments, setComments] =
    useState<CommentGenerationData[]>(savedComments);
  const [commentToReject, setCommentToReject] = useState<string | null>(null);

  useEffect(() => {
    setComments(savedComments);
  }, [savedComments]);

  async function evaluateComment({
    commentId,
    status,
    evaluatorComment,
  }: {
    commentId: string;
    status: CommentStatus;
    evaluatorComment: string;
  }) {
    try {
      await updateCommentStatus({
        projectId,
        commentId,
        status,
        evaluatorComment,
      });

      if (status === CommentStatus.Approved) {
        fireConfetti();
      }

      toast({
        title:
          status === CommentStatus.Approved
            ? 'Comment Approved'
            : 'Comment Rejected',
        variant: 'success',
      });

      if (updateCallback) {
        updateCallback();
      }
    } catch (error) {
      toast({
        title: 'Error adding comment',
        variant: 'destructive',
      });
    }
  }

  const filteredComments = comments
    .filter((comment) => comment.status !== CommentStatus.Approved)
    .sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

  const pendingCommentsCount = filteredComments.filter(
    (comment) => comment.status === CommentStatus.Pending
  ).length;

  return (
    <Accordion
      type="single"
      collapsible
      className="bg-slate-200 p-2 rounded-sm mt-4"
    >
      <AccordionItem value="comments">
        <AccordionTrigger className="text-left w-full">
          <h2 className="text-xl font-semibold">
            {pendingCommentsCount}/{filteredComments.length} AI Comments to
            Review
          </h2>
        </AccordionTrigger>
        <AccordionContent>
          <div className="mt-4 space-y-4">
            {filteredComments.length > 0 ? (
              filteredComments.map((comment) => (
                <div
                  key={comment.id}
                  className={`p-4 border-l-2 ${
                    comment.resolved ? 'border-green-500' : 'border-yellow-500'
                  }`}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="font-semibold">
                        Status: {comment.status}
                      </h3>
                      <p className="text-xs text-gray-800">
                        {format(
                          new Date(comment.createdAt),
                          'dd.MM.yyyy HH:mm'
                        )}
                      </p>
                      {comment.evaluatorComment &&
                        comment.evaluatorComment.length > 0 && (
                          <p className="bg-red-500 mt-1 py-0.5 px-2 rounded text-sm text-white">
                            Rejection Reason: {comment.evaluatorComment}
                          </p>
                        )}
                    </div>

                    <div className="flex flex-col justify-end gap-3">
                      <div className="flex justify-end items-center gap-2">
                        <Button
                          size={'xs'}
                          variant={'outline'}
                          onClick={() =>
                            evaluateComment({
                              commentId: comment.id,
                              status: CommentStatus.Approved,
                              evaluatorComment: '',
                            })
                          }
                          disabled={
                            comment.status === CommentStatus.Approved ||
                            comment.status === CommentStatus.Rejected
                          }
                          className={cn(
                            comment.status === CommentStatus.Approved
                              ? 'bg-green-500 text-white'
                              : 'text-green-600 border-green-600 hover:bg-green-600 hover:text-white'
                          )}
                        >
                          <CircleCheckBigIcon className={`w-3 h-3 mr-2`} />
                          Approve
                        </Button>
                        <Button
                          size={'xs'}
                          variant={'outline'}
                          onClick={() => setCommentToReject(comment.id)}
                          disabled={
                            comment.status === CommentStatus.Approved ||
                            comment.status === CommentStatus.Rejected
                          }
                          className={cn(
                            comment.status === CommentStatus.Rejected
                              ? 'bg-red-500 text-white'
                              : 'text-red-500 border-red-500 hover:bg-red-500 hover:text-white'
                          )}
                        >
                          <CircleXIcon className={`w-3 h-3 mr-2`} />
                          Reject
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="mt-4">
                    <MarkdownRenderer
                      className="text-sm"
                      text={comment.comment}
                    />
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-400">No comments available.</p>
            )}
          </div>
        </AccordionContent>
      </AccordionItem>

      {commentToReject && (
        <CommentRejectionReasonModal
          open={!!commentToReject}
          setOpen={() => setCommentToReject(null)}
          commentId={commentToReject}
          callback={(data, commentId) => {
            evaluateComment({
              commentId: commentId,
              status: CommentStatus.Rejected,
              evaluatorComment: data.comment || '',
            });
          }}
        />
      )}
    </Accordion>
  );
};
