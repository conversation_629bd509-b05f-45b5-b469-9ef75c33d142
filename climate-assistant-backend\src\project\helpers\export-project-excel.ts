import * as ExcelJS from 'exceljs';
import { marked } from 'marked';
import * as cheerio from 'cheerio';

interface DatapointInfo {
  esrs: string;
  dr: string;
  datapointId: string;
  datapoint: string;
  dataType: string;
  datapointText: string;
  datapointGaps: string;
}

const spanRegex = /<span[^>]*>(.*?)<\/span>/g;

export async function generateExcelReport(
  datapoints: DatapointInfo[]
): Promise<Buffer> {
  try {
    // 1. Execute the query using TypeORM

    // 2. Create Excel workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'ESG Data Tool';
    workbook.lastModifiedBy = 'ESG Data Tool';
    workbook.created = new Date();
    workbook.modified = new Date();

    const worksheet = workbook.addWorksheet('Datapoints Report', {
      views: [{ state: 'frozen', xSplit: 0, ySplit: 1 }], // Freeze the header row
    });

    // 3. Define columns with appropriate widths
    worksheet.columns = [
      { header: 'ESRS Topic', key: 'esrs', width: 10 },
      { header: 'Disclosure Requirement ID', key: 'dr', width: 15 },
      { header: 'Datapoint ID', key: 'datapointId', width: 15 },
      { header: 'Title', key: 'datapoint', width: 30 },
      { header: 'Data Type', key: 'dataType', width: 15 },
      { header: 'Datapoint Text', key: 'datapointText', width: 40 },
      { header: 'Erklärung der Gap', key: 'gapExplanation', width: 40 },
      { header: 'Recommended Actions', key: 'recommendedActions', width: 40 },
      { header: 'Example Text', key: 'exampleText', width: 40 },
      { header: 'Disclaimer', key: 'disclaimer', width: 40 },
      { header: 'Kommentar Kunde', key: 'customerComment', width: 40 },
    ];

    // 4. Style header row
    const headerRow = worksheet.getRow(1);
    headerRow.height = 25;
    headerRow.font = { bold: true, size: 12 };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFD3D3D3' }, // Light gray background
    };
    headerRow.alignment = { vertical: 'middle', horizontal: 'center' };

    // Add borders to headers
    headerRow.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });

    // 5. Process each datapoint row
    for (let i = 0; i < datapoints.length; i++) {
      const datapoint = datapoints[i] as DatapointInfo;
      const rowIndex = i + 2; // Row 1 is header

      // Process datapointText - convert markdown to HTML to plain text
      let datapointTextContent = '';
      if (datapoint.datapointText && datapoint.datapointText !== '') {
        try {
          const htmlContent =
            '<meta charset="UTF-8">' + (await marked(datapoint.datapointText));
          datapointTextContent = convertHtmlToPlainText(htmlContent);
        } catch (error) {
          console.error('Error processing datapointText:', error);
          datapointTextContent = datapoint.datapointText; // Fallback to raw content
        }
      }

      // Extract data from datapointGaps
      let gapExplanation = '';
      let recommendedActions = '';
      let exampleText = '';
      let disclaimer = '';

      if (datapoint.datapointGaps && datapoint.datapointGaps !== '') {
        try {
          gapExplanation = extractGapExplanation(datapoint.datapointGaps);
          recommendedActions = extractRecommendedActions(
            datapoint.datapointGaps
          );
          exampleText = extractExampleText(datapoint.datapointGaps);
          disclaimer = extractDisclaimer(datapoint.datapointGaps);
        } catch (error) {
          console.error('Error extracting gap information:', error);
        }
      }

      // Add row to worksheet
      const row = worksheet.addRow({
        esrs: datapoint.esrs || '',
        dr: datapoint.dr || '',
        datapointId: datapoint.datapointId || '',
        datapoint: datapoint.datapoint || '',
        dataType: datapoint.dataType || '',
        datapointText: datapointTextContent,
        gapExplanation,
        recommendedActions,
        exampleText,
        disclaimer,
        customerComment: '',
      });

      // Apply row styling
      row.height = 120; // Set a reasonable height for multi-line content

      // Apply cell styling
      row.eachCell((cell, colNumber) => {
        // Add borders
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        // Apply text wrapping and vertical alignment
        cell.alignment = {
          wrapText: true,
          vertical: 'top',
        };

        let INFO_COLUMNS = 6;

        // Apply specific styling based on column
        if (colNumber === INFO_COLUMNS + 1) {
          // Erklärung der Gap column
          // Light yellow background for gap explanation
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFFF2CC' },
          };
        } else if (colNumber === INFO_COLUMNS + 2) {
          // Recommended Actions column
          // Light blue background for recommendations
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFD9E1F2' },
          };
        } else if (colNumber === INFO_COLUMNS + 3) {
          // Example Text column
          // Light green background for examples
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE2EFDA' },
          };
        } else if (colNumber === INFO_COLUMNS + 4) {
          // Disclaimer column
          // Light red background for disclaimers
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFCE4D6' },
          };
        }
      });
    }

    // 6. Generate buffer and return
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  } catch (error) {
    console.error('Error generating Excel report:', error);
    throw new Error(`Failed to generate Excel report: ${error.message}`);
  }
}

// Helper functions for HTML processing and extraction

function convertHtmlToPlainText(html: string): string {
  try {
    const $ = cheerio.load(html);

    // Replace list items with bullet points
    $('li').each((_, el) => {
      $(el).prepend('• ');
    });

    // Preserve newlines for paragraphs and list items
    $('p, li').each((_, el) => {
      $(el).append('\n');
    });

    // Get the text content
    let text = $.text();

    // Clean up extra whitespace
    text = text
      .replace(/\n\s+/g, '\n') // Remove whitespace after newlines
      .replace(/\n+/g, '\n') // Collapse multiple newlines
      .trim();

    return text;
  } catch (error) {
    console.error('Error converting HTML to plain text:', error);
    // Strip HTML tags as a fallback
    return html.replace(/<[^>]*>/g, '').trim();
  }
}

function extractGapExplanation(html: string): string {
  try {
    const $ = cheerio.load(html);

    // Try to find the gap explanation paragraph
    // Pattern from the example: <p><strong>Gap Identified:</strong> <p>Die Offenlegung...</p></p>
    const gapP = $('p:contains("Gap Identified:") p');
    if (gapP.length > 0) {
      return gapP.text().trim();
    }

    // Fallback to regex if needed
    const regex = /<strong>Gap Identified:<\/strong>.*?<p>(.*?)<\/p>/s;
    const match = html.match(regex);

    const cleanedGap = match
      ? match[1].replace(spanRegex, (match, p1) => p1)
      : '';
    return cleanedGap.trim();
  } catch (error) {
    console.error('Error extracting gap explanation:', error);
    return '';
  }
}

function extractRecommendedActions(html: string): string {
  try {
    const $ = cheerio.load(html);

    // Pattern from the example: <p><strong>Recommended Actions:</strong></p><ul><li><p>...</p></li>...</ul>
    const actionItems = $('p:contains("Recommended Actions:") + ul li p');

    if (actionItems.length > 0) {
      const items = actionItems
        .map((_, el) => {
          return `• ${$(el).text().trim()}`;
        })
        .get();

      const actions = items.join('\n');

      // const cleanedActions = actions
      //   ? actions[1].replace(spanRegex, (match, p1) => p1)
      //   : '';

      return actions.trim();
    }

    // Find the paragraph containing "Recommended Actions:" text
    const recommendedHeader = $(
      'p strong:contains("Recommended Actions:")'
    ).closest('p');

    // Get the following unordered list
    const actionList = recommendedHeader.nextAll('ul').first();
    if (actionList.length > 0) {
      // Extract each list item's text
      const items = actionList
        .find('li p')
        .map((_, el) => {
          return `• ${$(el).text().trim()}`;
        })
        .get();

      return items.join('\n');
    }

    // Fallback to regex
    const regex = /<strong>Recommended Actions:<\/strong><\/p><ul>(.*?)<\/ul>/s;
    const match = html.match(regex);
    if (!match) return '';

    const listItemRegex = /<li><p>(.*?)<\/p><\/li>/g;
    const items = [];
    let listItemMatch;

    while ((listItemMatch = listItemRegex.exec(match[1])) !== null) {
      items.push(`• ${listItemMatch[1].trim()}`);
    }

    const actions = items.join('\n');

    const cleanedActions = actions
      ? actions[1].replace(spanRegex, (match, p1) => p1)
      : '';
    return cleanedActions.trim();
  } catch (error) {
    console.error('Error extracting recommended actions:', error);
    return '';
  }
}

function extractExampleText(html: string): string {
  try {
    const $ = cheerio.load(html);

    // Pattern from the example: <p><strong>Example Text:</strong><p>...</p></p>
    const exampleP = $('p:contains("Example Text:") p');

    if (exampleP.length > 0) {
      return exampleP.text().trim();
    }

    // Fallback to regex
    const regex = /<strong>Example Text:<\/strong><p>(.*?)<\/p>/s;
    const match = html.match(regex);

    const cleanedText = match
      ? match[1].replace(spanRegex, (match, p1) => p1)
      : '';
    return cleanedText.trim();
  } catch (error) {
    console.error('Error extracting example text:', error);
    return '';
  }
}

function extractDisclaimer(html: string): string {
  try {
    const $ = cheerio.load(html);

    // Pattern from the example: <p><strong>Disclaimer:</strong> <p>...</p></p>
    const disclaimerP = $('p:contains("Disclaimer:") p');

    if (disclaimerP.length > 0) {
      return disclaimerP.text().trim();
    }

    // Fallback to regex
    const regex = /<strong>Disclaimer:<\/strong>\s*<p>(.*?)<\/p>/s;
    const match = html.match(regex);

    const cleanedDisclaimer = match
      ? match[1].replace(spanRegex, (match, p1) => p1)
      : '';
    return cleanedDisclaimer.trim();
  } catch (error) {
    console.error('Error extracting disclaimer:', error);
    return '';
  }
}
