import { FunctionComponent } from 'react';

import { MainLayout } from '@/components/MainLayout';
import { useMateriality } from '@/hooks/useMateriality';
import { UpdateMaterilaityButton } from '@/components/materiality/UpdateMaterialityModal';
import { TopicHierarchyAccordion } from '@/components/materiality/TopicHierarchyAccordion';

export const MaterialitySettings: FunctionComponent = () => {
  const {
    data,
    checkedState,
    isLoading,
    isSaving,
    handleStatusChange,
    handleSaveData,
  } = useMateriality();

  return (
    <MainLayout>
      <div className="pb-60">
        <div className="flex gap-4 w-full items-center justify-between mt-8 mb-12">
          <h1 className="text-4xl font-bold">Material Topics</h1>
          <UpdateMaterilaityButton
            saveData={handleSaveData}
            isSaving={isSaving || isLoading}
          />
        </div>
        <div>
          <TopicHierarchyAccordion
            data={data}
            checkedState={checkedState}
            handleStatusChange={handleStatusChange}
          />
        </div>
      </div>
    </MainLayout>
  );
};
