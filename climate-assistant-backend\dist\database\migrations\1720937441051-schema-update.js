"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1720937441051 = void 0;
class SchemaUpdate1720937441051 {
    constructor() {
        this.name = 'SchemaUpdate1720937441051';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TYPE "public"."chat_message_role_enum" AS ENUM('user', 'system', 'assistant')`);
        await queryRunner.query(`CREATE TABLE "chat_message"
       (
           "id"            uuid                              NOT NULL DEFAULT uuid_generate_v4(),
           "content"       character varying                 NOT NULL,
           "role"          "public"."chat_message_role_enum" NOT NULL,
           "chatHistoryId" uuid,
           CONSTRAINT "PK_3cc0d85193aade457d3077dd06b" PRIMARY KEY ("id")
       )`);
        await queryRunner.query(`CREATE TABLE "chat_history"
       (
           "id"     uuid NOT NULL DEFAULT uuid_generate_v4(),
           "userId" uuid,
           CONSTRAINT "PK_cf76a7693b0b075dd86ea05f21d" PRIMARY KEY ("id")
       )`);
        await queryRunner.query(`ALTER TABLE "chat_message"
          ADD CONSTRAINT "FK_4c772df41e9f3e87b644158fea7" FOREIGN KEY ("chatHistoryId") REFERENCES "chat_history" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "chat_history"
          ADD CONSTRAINT "FK_6bac64204c7b416f465e17957ed" FOREIGN KEY ("userId") REFERENCES "user" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "chat_history" DROP CONSTRAINT "FK_6bac64204c7b416f465e17957ed"`);
        await queryRunner.query(`ALTER TABLE "chat_message" DROP CONSTRAINT "FK_4c772df41e9f3e87b644158fea7"`);
        await queryRunner.query(`DROP TABLE "chat_history"`);
        await queryRunner.query(`DROP TABLE "chat_message"`);
        await queryRunner.query(`DROP TYPE "public"."chat_message_role_enum"`);
    }
}
exports.SchemaUpdate1720937441051 = SchemaUpdate1720937441051;
//# sourceMappingURL=1720937441051-schema-update.js.map