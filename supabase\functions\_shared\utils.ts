export function convertToRanges(numbers: number[]): string[] {
    if (numbers.length === 0) return [];
    
    // Sort the array and remove duplicates
    const sorted = [...new Set(numbers)].sort((a, b) => a - b);
    
    const ranges: string[] = [];
    let rangeStart = sorted[0];
    let rangeEnd = sorted[0];
    
    for (let i = 1; i < sorted.length; i++) {
      const current = sorted[i];
      const previous = sorted[i - 1];
      
      // Check if numbers are consecutive
      if (current === previous + 1) {
        // Extend the current range
        rangeEnd = current;
      } else {
        // Add the completed range to our result
        if (rangeStart === rangeEnd) {
          ranges.push(`${rangeStart}`);
        } else {
          ranges.push(`${rangeStart}-${rangeEnd}`);
        }
        
        // Start a new range
        rangeStart = current;
        rangeEnd = current;
      }
    }
    
    // Add the last range
    if (rangeStart === rangeEnd) {
      ranges.push(`${rangeStart}`);
    } else {
      ranges.push(`${rangeStart}-${rangeEnd}`);
    }
    
    return ranges;
  }

/**
 * Parse a string containing page numbers into an array of numbers.
 * Handles formats like "1-5, 7, 10-12" or "1,2,3" or "1-10".
 * 
 * @param pages String containing page numbers and ranges
 * @returns Array of parsed page numbers
 */
export function parsePagesString(pages: string): number[] {
  const pageNumbers: number[] = [];
  
  if (!pages || !pages.trim()) {
    return pageNumbers;
  }

  const pageParts = pages.split(',').map(p => p.trim());
          
  for (const part of pageParts) {
    if (part.includes('-')) {
      const [start, end] = part.split('-').map(Number);
      if (!isNaN(start) && !isNaN(end)) {
        for (let i = start; i <= end; i++) {
          pageNumbers.push(i);
        }
      }
    } else {
      const num = Number(part);
      if (!isNaN(num)) {
        pageNumbers.push(num);
      }
    }
  }

  return pageNumbers;
}
