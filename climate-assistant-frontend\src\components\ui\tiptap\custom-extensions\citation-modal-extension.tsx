import { Node, mergeAttributes, InputRule } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { CitationListModal } from '../custom-components/citation-list-modal';

export const CitationModalExtension = Node.create({
  name: 'modalLink',
  priority: 1000,
  group: 'inline',
  inline: true,
  atom: true,
  marks: '',
  content: '', // leaf node

  addAttributes() {
    return {
      index: {
        default: null,
      },
      refid: {
        default: null,
      },
      color: {
        default: '#000000',
      },
      text: {
        default: 'Click here',
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'span[data-modal-link]',
        getAttrs: (dom: HTMLElement) => {
          const index = dom.getAttribute('data-index');
          const refid = dom.getAttribute('data-refid');
          const color = dom.getAttribute('data-color') || '#000000';
          const text =
            dom.getAttribute('data-text') || dom.textContent || 'Click here';

          return { index, refid, color, text };
        },
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    const { color, text, refid, index } = HTMLAttributes;

    return [
      'span',
      mergeAttributes(this.options.HTMLAttributes, {
        'data-modal-link': 'true',
        'data-index': index,
        'data-refid': refid,
        'data-color': color,
      }),
      text,
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(CitationListModal);
  },

  // Adjust the input rule regex based on the new markup:
  // [dpcite-<index>|color-<hex>|text-"information"]
  addInputRules() {
    // Example regex:
    // \[dpcite-(\d+)\|color-([#a-fA-F0-9]+)\|text-"([^"]+)"\]$
    const regex = /\[dpcite-(\d+)\|color-([#a-fA-F0-9]+)\|text-"([^"]+)"\]$/;
    const type = this.type;

    return [
      new InputRule({
        find: regex,
        handler: ({ state, range, match }) => {
          const { tr } = state;
          const { from, to } = range;
          const [, index, color, text] = match;

          if (match[0]) {
            tr.replaceWith(from, to, type.create({ index, color, text }));
          }
        },
      }),
    ];
  },
});
