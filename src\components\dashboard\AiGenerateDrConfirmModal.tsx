import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

import { Button } from '../ui/button';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { DataRequestData } from '@/types/project';
import { Switch } from '../ui/switch';
import { InfoIcon } from 'lucide-react';

const generateReportTextWithAISchema = z.object({
  additionalReportTextGenerationRules: z.string().optional(),
  enableDatapointTags: z.boolean().optional(),
  useExistingReportText: z.boolean().optional(),
});

export type GenerateReportTextWithAIFormData = z.infer<
  typeof generateReportTextWithAISchema
>;

export function AiGenerateReportTextConfirmModal({
  open,
  setOpen,
  callback,
  dataRequest,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  callback: (data: GenerateReportTextWithAIFormData) => void;
  dataRequest: DataRequestData;
}) {
  const { control, register, handleSubmit } =
    useForm<GenerateReportTextWithAIFormData>({
      defaultValues: {
        additionalReportTextGenerationRules: dataRequest.customUserRemark || '',
        enableDatapointTags: false,
        useExistingReportText: false,
      },
      resolver: zodResolver(generateReportTextWithAISchema),
    });

  const onSubmit = async (data: GenerateReportTextWithAIFormData) => {
    callback(data);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[625px]">
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-5">
          <DialogHeader>
            <DialogTitle>
              Generate report text for {dataRequest.disclosureRequirement.esrs}:{' '}
              {dataRequest.disclosureRequirement.name}
            </DialogTitle>
          </DialogHeader>

          <div className="grid gap-2">
            <Label>Context</Label>
            <DialogDescription className="flex gap-5 mb-2 py-2">
              <div className="flex items-center gap-3">
                <Controller
                  control={control}
                  name="enableDatapointTags"
                  render={({ field: { onChange, value, ref } }) => (
                    <Switch
                      size="sm"
                      id={`enableDatapointTags-${dataRequest.id}`}
                      checked={value}
                      onCheckedChange={onChange}
                      ref={ref}
                    />
                  )}
                />
                <Label
                  className="text-nowrap"
                  htmlFor={`enableDatapointTags-${dataRequest.id}`}
                >
                  Generate with datapoint tags
                </Label>
              </div>
              <div className="flex items-center gap-3">
                <Controller
                  control={control}
                  name="useExistingReportText"
                  render={({ field: { onChange, value, ref } }) => (
                    <Switch
                      size="sm"
                      id={`useExistingReportText-${dataRequest.id}`}
                      checked={value}
                      onCheckedChange={onChange}
                      disabled={dataRequest.content === ''}
                      ref={ref}
                    />
                  )}
                />
                <Label
                  className="text-nowrap"
                  htmlFor={`useExistingReportText-${dataRequest.id}`}
                >
                  Use existing text
                </Label>
                <Tooltip delayDuration={0}>
                  <TooltipTrigger onClick={(e) => e.preventDefault()}>
                    <InfoIcon className="w-4 h-4" />
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    When toggled on, the already created reporting text is used
                    as a basis for regeneration.
                  </TooltipContent>
                </Tooltip>
              </div>
            </DialogDescription>
            <Label htmlFor="additionalReportTextGenerationRules">
              Additional Context (optional)
            </Label>

            <Textarea
              id="additionalReportTextGenerationRules"
              placeholder="Enter additional context for text generation, if available ..."
              autoCapitalize="none"
              autoCorrect="off"
              rows={5}
              {...register('additionalReportTextGenerationRules')}
            />
            <DialogDescription className="pt-3">
              {dataRequest.content === '' ? (
                <span>
                  You are generating a new report text. Generation may take up
                  to 30 seconds.
                </span>
              ) : (
                <div className="space-y-1">
                  {dataRequest.content !== '' && (
                    <span className="text-yellow-600">
                      WARNING: This will overwrite the existing report text. To
                      keep it, copy it before generating.
                    </span>
                  )}
                  <div className="text-gray-500 text-sm">
                    Glacier AI can make mistakes
                  </div>
                </div>
              )}
            </DialogDescription>
          </div>

          <DialogFooter>
            <Button type="submit" variant='darkBlue'>Generate and overwrite</Button>
            <Button
              type="button"
              onClick={() => setOpen(false)}
              variant="secondary"
            >
              Cancel
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
