
import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary: "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive: "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline hover:text-glacier-blue",
        forest: "border-transparent bg-green-700 text-white hover:bg-green-700/90",
        button: "bg-transparent border-0 hover:bg-gray-200 text-gray-500",
        outlinepop: "border border-slate-800 bg-background hover:text-primary-foreground hover:bg-glacier-bluedark",
        mint: "border-transparent bg-glacier-mint text-glacier-darkBlue hover:bg-glacier-mint/90",
        darkBlue: "border-transparent bg-glacier-darkBlue text-white hover:bg-glacier-darkBlue/90",
        warning: "border-transparent bg-yellow-500 text-white hover:bg-yellow-600",
        info: "border-transparent bg-blue-500 text-white hover:bg-blue-600",
        blue: "border-transparent bg-blue-500 text-white hover:bg-blue-600",
        success: "border-transparent bg-green-500 text-white hover:bg-green-600"
      },
      size:{
        sm: "text-xs",
        md: "text-sm",
        lg: "text-base",
      },
    },
    defaultVariants: {
      variant: "default",
    },

  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
