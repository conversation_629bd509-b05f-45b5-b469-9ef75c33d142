-- Active: 1724827555825@@127.0.0.1@5432@glacier
-- Create a user in Backend
-- Use https://bcrypt-generator.com/ to generate auser
-- Log in -> The Workspace and project are automatically created

-- Get ProjectId from Backend with the following Query based on the user Id:
SELECT "workspace"."id" as "Workspace Id", "user"."id" as "User Id", "project"."id" as "ProjectId", *  
FROM public."user" join "user_workspace" on "user_workspace"."userId" = "user"."id" 
join "workspace" on "user_workspace"."workspaceId" = "workspace"."id"
join "project" on "project"."workspaceId" = "workspace"."id"
ORDER BY "user"."id" ASC; 
 

-- Inserting all DataRequests based on all existing ESRS Datarequests
-- Usage: Change the ID used for the projectId
INSERT INTO data_request("dataRequestTypeId", "dataRequestType", "status", "content", "projectId")
select "id" as "dataRequestTypeId", 'ESRS' as "dataRequestType", 'no_data' as "status", '' as "content", 'e3030e1e-82de-48b7-bb48-341c98ee37b5' as "projectId" 
from esrs_disclosure_requirement "dr1" 
where "id" not in (select "dr2"."dataRequestTypeId" as "id" from "data_request" "dr2" where "dr2"."projectId" = 'e3030e1e-82de-48b7-bb48-341c98ee37b5') and "dr1"."dr"='E5-3';

-- Inserting all Datapoints for every DataRequest

-- Select all esrs-datapoints that relate to esrs data requests for a given project
--  Join them with the datapoints

INSERT INTO datapoint_request("esrsDatapointId", "dataRequestId", "status", "content")
SELECT "esrs_datapoint"."id" as "esrsDatapointId", "dr"."id" as "dataRequestId", 'no_data' as "status", '' as "content"
FROM "data_request" "dr" JOIN "esrs_disclosure_requirement" "edr" ON "edr"."id" = "dr"."dataRequestTypeId" JOIN "esrs_datapoint" on "esrs_datapoint"."esrsDisclosureRequirementId" = "edr"."id"
WHERE "dr"."projectId" = 'e3030e1e-82de-48b7-bb48-341c98ee37b5' and "esrs_datapoint"."id" not in (
SELECT "dpr2"."esrsDatapointId" as "id" 
FROM "datapoint_request" "dpr2" 
JOIN "data_request" "dr2" on "dpr2"."dataRequestId" = "dr2"."id" 
where "dr2"."projectId" = 'e3030e1e-82de-48b7-bb48-341c98ee37b5'
);
-- Find all datapoint-requests for a given project

SELECT *  
FROM "datapoint_request" "dpr" 
JOIN "data_request" on "dpr"."dataRequestId" = "data_request"."id" 
where "data_request"."projectId" = 'b1c70416-39d6-4799-9d29-01ea88d07d1e'
-- Result should be 994

