# Database Backup Procedures

This document outlines the steps required to create a backup of the PostgreSQL database used by the Glacier Climate Assistant backend and download it to your local machine.

> **Note:** Adjust the commands as necessary for your environment and update filenames/dates as needed.

---

## Prerequisites

- **SSH Access:** Ensure you have SSH access to the server.
- **Docker:** The database is running inside a Docker container.
- **SCP:** Install SCP (or a similar file transfer tool) on your local machine.
- **Credentials:** Verify that the PostgreSQL credentials (username, database name, etc.) match those in your `.env` file.

---

## Backup Steps

### 1. SSH into the Server

Connect to the server using your SSH credentials:

```bash
ssh glacieruser@*************
```

---

### 2. Open a Shell in the PostgreSQL Container

Identify the PostgreSQL container (typically named something like `climate-assistant-backend-db-1`) and open a shell inside it:

```bash
docker exec -it climate-assistant-backend-db-1 bash
```

---

### 3. Run the Database Dump

Inside the container, execute the `pg_dump` command to create an SQL dump of your database. This example excludes specific tables (such as those containing vector data):

```bash
pg_dump -U admin -d glacier -a --inserts \
  --exclude-table=public.file_upload_chunk \
  --exclude-table=public.knowledge_base_file_upload_chunk \
  -f /var/lib/postgresql/data/backup-YYYY-MM-DD.sql
```

- **Parameters:**
  - `-U admin`: Uses the PostgreSQL user `admin`.
  - `-d glacier`: Dumps the `glacier` database.
  - `-a --inserts`: Dumps only the data with insert statements.
  - `--exclude-table`: Excludes tables not required in the backup.
  - `-f /var/lib/postgresql/data/backup-YYYY-MM-DD.sql`: Specifies the output file (replace `YYYY-MM-DD` with the current date or a suitable identifier).

---

### 4. Move the Backup File to an Accessible Directory

To access the backup from outside the container, move the file to a directory mounted on the host system:

```bash
mv backup-YYYY-MM-DD.sql /home/<USER>/climate-assistant
```

---

### 5. Download the Backup File to Your Local Machine

Exit the container and, from your local machine, run the following command to copy the backup file using SCP:

```bash
scp glacieruser@*************:/home/<USER>/climate-assistant/backup-YYYY-MM-DD.sql /path/to/local/directory
```

- Replace `/path/to/local/directory` with your desired local path.
- Ensure you update `backup-YYYY-MM-DD.sql` to match the filename used in the dump step.

---

## Additional Notes

- **Regular Backups:** Schedule regular backups (using cron jobs or similar mechanisms) to minimize data loss.
- **Customization:** Modify the `pg_dump` command options to include or exclude additional tables as necessary.
- **Verification:** After downloading, verify the integrity of the backup file by checking its size and contents.
- **Documentation:** For more details on `pg_dump` and PostgreSQL backup strategies, consult the [PostgreSQL documentation](https://www.postgresql.org/docs/current/backup-dump.html).
