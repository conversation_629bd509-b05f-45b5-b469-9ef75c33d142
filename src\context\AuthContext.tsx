
// src/contexts/AuthContext.tsx
import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Session, User } from '@supabase/supabase-js';

type WorkspaceRole = {
  [workspaceId: string]: string;
};

type AuthContextType = {
  session: Session | null;
  user: User | null;
  workspaces: WorkspaceRole;
  currentWorkspace: string | null;
  loading: boolean;
  signIn: (
    params: { email: string; password: string }
  ) => Promise<void>;
  signUp: (
    params: { email: string; password: string; name: string }
  ) => Promise<void>;
  signOut: () => Promise<void>;
  setCurrentWorkspace: (workspaceId: string) => void;
  hasRole: (workspaceId: string, roles: string[]) => boolean;
};

// LocalStorage configuration
const AUTH_COOKIE_NAME = 'access_token';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [workspaces, setWorkspaces] = useState<WorkspaceRole>({});
  const [currentWorkspace, setCurrentWorkspace] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // Set auth in localStorage
  const setAuthLocalStorage = (session: Session | null) => {
    if (session?.access_token) {
      localStorage.setItem(AUTH_COOKIE_NAME, session.access_token);
    } else {
      localStorage.removeItem(AUTH_COOKIE_NAME);
    }
  };

  // Update local state with session data
  const updateSessionState = (session: Session | null) => {
    setSession(session);
    setUser(session?.user ?? null);

    // Extract workspaces from JWT if available
    const workspacesData = session?.user?.app_metadata?.workspaces as WorkspaceRole;
    if (workspacesData) {
      setWorkspaces(workspacesData);

      // Set first workspace as current if not set
      if (!currentWorkspace && Object.keys(workspacesData).length > 0) {
        setCurrentWorkspace(Object.keys(workspacesData)[0]);
      }
    }
  };

  useEffect(() => {
    const initAuth = async () => {
      try {
        // Check for existing token first
        const localStorageToken = localStorage.getItem(AUTH_COOKIE_NAME);

        if (localStorageToken) {
          // If token exists but session doesn't, try to refresh session
          const { data } = await supabase.auth.getSession();
          if (data.session) {
            updateSessionState(data.session);
            // Ensure localStorage is updated with latest token
            setAuthLocalStorage(data.session);
          } else {
            // Invalid token, clean up
            localStorage.removeItem(AUTH_COOKIE_NAME);
          }
        } else {
          // No token, just get session normally
          const { data: { session } } = await supabase.auth.getSession();
          updateSessionState(session);
          // Set localStorage if session exists
          setAuthLocalStorage(session);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        setLoading(false);
      }
    };

    initAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      updateSessionState(session);

      // Update localStorage when auth state changes
      if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
        setAuthLocalStorage(session);
      } else if (event === 'SIGNED_OUT') {
        setAuthLocalStorage(null);
      }

      setLoading(false);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signIn = async ({
    email,
    password
  }: {email: string, password: string}) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({ email, password });
      if (error) throw error;

      // Explicitly set the localStorage after successful sign-in
      if (data?.session) {
        setAuthLocalStorage(data.session);
      }
    } catch (error) {
      console.error('Error signing in:', error);
      throw error;
    }
  };

  const signUp = async ({
    email,
    password,
    name
  }:{email: string, password: string, name: string}) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      });
      if (error) throw error;

      // Set localStorage if session is available (auto sign-in case)
      if (data?.session) {
        setAuthLocalStorage(data.session);
      }
    } catch (error) {
      console.error('Error signing up:', error);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      // Clear localStorage on sign out
      localStorage.removeItem(AUTH_COOKIE_NAME);
      setCurrentWorkspace(null);
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  };

  const hasRole = (workspaceId: string, roles: string[]) => {
    if (!workspaces || !workspaces[workspaceId]) return false;
    return roles.includes(workspaces[workspaceId]);
  };

  return (
    <AuthContext.Provider
      value={{
        session,
        user,
        workspaces,
        currentWorkspace,
        loading,
        signIn,
        signUp,
        signOut,
        setCurrentWorkspace,
        hasRole,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
