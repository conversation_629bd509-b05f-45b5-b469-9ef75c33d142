import { useState } from 'react';
import { LoaderCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input.tsx';
import { Button } from '@/components/ui/button.tsx';
import { useAuthentication } from '@/api/authentication/authentication.query';

const formSchema = z
  .object({
    password: z
      .string()
      .min(6, { message: 'Password must be at least 6 characters' }),
    confirmPassword: z
      .string()
      .min(6, { message: 'Password must be at least 6 characters' }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

const ResetPasswordForm = ({
  token,
  userEmail,
}: {
  token: string | null;
  userEmail: string | null;
}) => {
  const [error, setError] = useState('');

  const { resetPassword, restPasswordLoading } = useAuthentication();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      if (!token) {
        throw new Error('Token is missing');
      }
      await resetPassword({ password: values.password, token });
    } catch (err: any) {
      setError(err.message);
    }
  }

  return (
    <div className={`flex flex-col w-full max-w-[380px]`}>
      <div className={`font-semibold text-3xl text-center mb-8`}>
        Set new password
        <p className="text-xl text-gray-400">{userEmail}</p>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>New Password</FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    placeholder="New Password"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Confirm Password</FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    placeholder="Confirm Password"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {error && <div className="text-red-500 text-sm">{error}</div>}
          <Button
            type="submit"
            className="w-full"
            disabled={restPasswordLoading}
          >
            {restPasswordLoading ? (
              <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Change Password
          </Button>
        </form>
      </Form>
      <div className="text-center mt-4 text-xs">
        If you have any questions, feel free to reach out to us via{' '}
        <a href="mailto:<EMAIL>"><EMAIL></a>. We will
        review your reply within one business day.
      </div>
    </div>
  );
};

export default ResetPasswordForm;
