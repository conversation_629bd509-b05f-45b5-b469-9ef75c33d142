# **Server Side Events**

Server Side Events (SSE) are a way to push events from the server to the client.

## **How to use SSE**

### Backend

1. Add the `@Sse` decorator to the `.controller` method for the endpoint that will be used to send the event stream.
2. The method should return an `Observable<MessageEvent>` (use RXJS Observable).

3. In the respective `.service.ts` file, create an event$ attributes and emitSseEvents method:

4. In the respective `.controller` file, import the `createSseStream` function from `@/util/sse.util` and use it to create the event stream.

5. Create an event stream by calling the `events$` observable and pipe it through the necessary RXJS operators.
   a. Filter the event stream to only emit events that match the event type.
   b. Map the event stream to the event payload. Use `switchMap` for async operations else just use `map`.
   c. Return the event stream after computation.

6. Return the event stream from the controller method.

Example:

data-request.controller.ts

```typescript
@Sse('/events/datapoint/:dataRequestId')
  subscribeToDataRequestEvents(
    @Param('dataRequestId') dataRequestId: string,
    @Res() response: Response,
  ): Observable<MessageEvent> {
    const eventStream = this.dataRequestService.events$.pipe(
      filter(
        (event: DatapointGenerationEvent) =>
          event.dataRequestId === dataRequestId,
      ),
      switchMap(async (event) => {
        if (event.status === 'failed') {
          await this.dataRequestService.setDatapointStatusToNoData(
            event.datapointRequestId,
          );
        }
        const datapoint = await this.dataRequestService.findDatapointById(
          event.datapointRequestId,
        );

        event.datapointRequest = datapoint;
        return {
          data: JSON.stringify(event),
          type: 'message',
        } as MessageEvent;
      }),
    );
    return createSseStream(response, eventStream, {
      data: { connected: true },
      type: 'connection',
    } as MessageEvent);
  }
```

data-request.service.ts

```typescript
private readonly eventSubject = new Subject<ExampleEventInterface>();

public readonly events$: Observable<ExampleEventInterface> =
this.eventSubject.asObservable();

emitSseEvents(event: ExampleEventInterface) {
    const eventPayload = {
        ...event,
        timestamp: new Date(),
    };
    this.eventSubject.next(eventPayload);
}

closeSseEvents() {
    this.eventSubject.complete();
}
```

### Frontend

1. Subscribe to the Observable in the frontend.

2. Use `new EventSource` to create a new SSE connection. (instead of axios or fetch)

```typescript
export const subscribeToEvent = async () => {
  const url = `${API_URL}/example-endpoint`;
  const eventSource = new EventSource(url, { withCredentials: true });
  return eventSource;
};
```

3. On a UI component level, you can now use the `subscribeToEvent` function to subscribe to the Observable.

```typescript
const eventSource = await subscribeToEvent();
eventSource.onmessage = (event) => {
  const eventData = JSON.parse(event.data);
};
```

4. You can now handle the incoming events in the `onmessage` callback.

```typescript
eventSource.onmessage = (event) => {
  const eventData = JSON.parse(event.data);
};
```

5. You can now close the SSE connection by calling the `closeSseEvents` method.

```typescript
eventSource.close();
```

6. Do not forget to close the event when the component unmounts.

```typescript
useEffect(() => {
  return () => eventSource.close();
}, []);
```

**NOTE** Refer to example in the datarequest service for a complete example, which uses SSE along with a queue to process datapoint generation.
