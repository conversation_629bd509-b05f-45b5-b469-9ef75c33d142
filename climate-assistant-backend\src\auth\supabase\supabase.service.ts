import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Role } from 'src/users/entities/user-workspace.entity';
import { User } from 'src/users/entities/user.entity';

@Injectable()
export class SupabaseService {
  private supabase: SupabaseClient;

  constructor(private configService: ConfigService) {
    this.supabase = createClient(
      this.configService.get<string>('SUPABASE_APP_URL'),
      this.configService.get<string>('SUPABASE_SERVICE_KEY')
    );
  }

  getClient(): SupabaseClient {
    return this.supabase;
  }

  async findByAuthId(id: User['id']): Promise<User | undefined> {
    const { data, error } = await this.supabase
      .from('user')
      .select(
        `
        *,
        user_workspace(*)
      `
      )
      .eq('auth_id', id)
      .single();

    if (error) {
      console.error('Error fetching user:', error);
      return undefined;
    }

    return data as User | undefined;
  }

  async findById(id: User['id']): Promise<User | undefined> {
    const { data, error } = await this.supabase
      .from('user')
      .select(
        `
        *,
        user_workspace(
          "workspaceId",
          role
        )
      `
      )
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching user:', error);
      return undefined;
    }

    return data as User | undefined;
  }

  async getUserRole(
    userId: string,
    workspaceId: string
  ): Promise<string | null> {
    try {
      const { data, error } = await this.supabase
        .from('user_workspace')
        .select('role')
        .eq('userId', userId)
        .eq('workspaceId', workspaceId)
        .single();

      if (error || !data) {
        return null;
      }

      return data.role;
    } catch (error) {
      console.error('Error getting user role:', error);
      return null;
    }
  }

  async verifyToken(token: string): Promise<any> {
    try {
      const { data, error } = await this.supabase.auth.getUser(token);

      if (error || !data.user) {
        return null;
      }

      return data.user;
    } catch (error) {
      console.error('Error verifying token:', error);
      return null;
    }
  }

  async migrateUsers() {
    try {
      const { data: existingUsers, error } = await this.supabase
        .from('user')
        .select(
          `
          *,
          user_workspace(*)
        `
        )
        .is('auth_id', null); // Only migrate users without auth_id

      if (error) {
        console.error('Error fetching users:', error);
        return;
      }

      console.log(`Found ${existingUsers.length} users to migrate`);

      let successCount = 0;
      const errors = [];

      // Process each user
      for (const user of existingUsers) {
        try {
          // Create the user in Supabase Auth
          const { data: authUser, error: authError } =
            await this.supabase.auth.admin.createUser({
              email: user.email,
              password: 'changeme',
              email_confirm: true,
              user_metadata: {
                name: user.name,
                workspaces: user.user_workspace.map((uw) => ({
                  workspaceId: uw.workspaceId,
                  role: uw.role,
                })),
              },
            });

          if (authError)
            throw new Error(
              `Auth error for ${user.email}: ${authError.message}`
            );

          // Update the user's auth_id
          const { error: updateError } = await this.supabase
            .from('user')
            .update({ auth_id: authUser.user.id })
            .eq('id', user.id);

          if (updateError)
            throw new Error(
              `Error updating auth_id for ${user.email}: ${updateError.message}`
            );

          successCount++;
          console.log(`Successfully migrated user ${user.email}`);
        } catch (error) {
          console.error(`Error migrating user ${user.email}:`, error);
          errors.push({ email: user.email, error: error.message });
        }
      }

      console.log(
        `Migration complete. Success: ${successCount}, Errors: ${errors.length}`
      );

      if (errors.length > 0) {
        console.error('Migration errors:', errors);
      }
    } catch (error) {
      console.error('Migration script error:', error);
    }
  }

  async createUser({
    email,
    name,
    password,
    options,
  }: {
    email: string;
    name: string;
    password: string;
    options: { workspaceId?: string; workspaceName?: string; role?: Role };
  }): Promise<{ user: User; workspaceId?: string } | null> {
    try {
      let workspaceId = options?.workspaceId;
      const role = options?.role || Role.Contributor;

      // If workspaceName is provided, create the workspace first
      if (!workspaceId && options?.workspaceName) {
        const { data: workspace, error: workspaceError } = await this.supabase
          .from('workspace')
          .insert({ name: options.workspaceName })
          .select('id')
          .single();

        if (workspaceError) {
          console.error('Error creating workspace:', workspaceError);
          return null;
        }

        workspaceId = workspace.id;
        console.log(`Created workspace with ID: ${workspaceId}`);
      }

      const { data: userData, error: userError } = await this.supabase
        .from('user')
        .insert({
          email,
          name,
        })
        .select('*')
        .single();

      if (userError) {
        console.error('Error creating user record:', userError);
        return null;
      }

      // Create the user in Supabase Auth
      const userMetadata: {
        name: string;
        workspaces: { workspaceId: string; role: Role }[];
      } = { name, workspaces: [{ workspaceId, role }] };

      const { data: authUser, error: authError } =
        await this.supabase.auth.admin.createUser({
          email,
          password,
          email_confirm: true,
          user_metadata: userMetadata,
        });

      if (authError) {
        console.error('Error creating auth user:', authError);
        // Rollback user creation in the database
        const { error: rollbackError } = await this.supabase
          .from('user')
          .delete()
          .eq('id', userData.id);
        return null;
      }

      // Create the user record in the database
      await this.supabase
        .from('user')
        .update({ auth_id: authUser.user.id })
        .eq('id', userData.id);

      // If we have a workspace, create the user-workspace mapping
      if (workspaceId) {
        const { error: mappingError } = await this.supabase
          .from('user_workspace')
          .insert({
            userId: userData.id,
            workspaceId,
            role,
          });

        if (mappingError) {
          console.error('Error mapping user to workspace:', mappingError);
          // We could consider rolling back here, but we'll keep the user
          // since it's already created
        }
      }

      // Fetch the complete user data with workspace info
      const user = await this.findById(userData.id);
      if (!user) {
        console.error('Error fetching created user');
        return null;
      }

      return { user, workspaceId };
    } catch (error) {
      console.error('Error in createUser:', error);
      return null;
    }
  }
}
