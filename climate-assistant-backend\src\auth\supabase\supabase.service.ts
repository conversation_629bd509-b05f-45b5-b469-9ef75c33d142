import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Role } from 'src/users/entities/user-workspace.entity';
import { User } from 'src/users/entities/user.entity';
import { UserWorkspace } from 'src/users/entities/user-workspace.entity';
import { Workspace } from 'src/workspace/entities/workspace.entity';
import {
  Document,
  DocumentStatus,
} from 'src/document/entities/document.entity';
import { ProjectEcoVadisQuestionLinkedDocumentChunk } from 'src/ecovadis/entities/ecovadis.entity';
import { JobProcessor, JobQueue } from 'src/types/jobs';

@Injectable()
export class SupabaseService {
  private supabase: SupabaseClient;

  constructor(
    private configService: ConfigService,
    @InjectQueue(JobProcessor.ChunkEcovadisLink)
    private readonly chunkEcovadisLinkingQueue: Queue,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserWorkspace)
    private readonly userWorkspaceRepository: Repository<UserWorkspace>,
    @InjectRepository(Workspace)
    private readonly workspaceRepository: Repository<Workspace>,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
    @InjectRepository(ProjectEcoVadisQuestionLinkedDocumentChunk)
    private readonly projectEcoVadisQuestionLinkedDocumentChunkRepository: Repository<ProjectEcoVadisQuestionLinkedDocumentChunk>
  ) {
    this.supabase = createClient(
      this.configService.get<string>('SUPABASE_APP_URL'),
      this.configService.get<string>('SUPABASE_SERVICE_KEY')
    );
  }

  getClient(): SupabaseClient {
    return this.supabase;
  }

  async findByAuthId(id: User['id']): Promise<User | undefined> {
    try {
      const user = await this.userRepository.findOne({
        where: { auth_id: id },
        relations: ['userWorkspaces'],
      });

      const profile = {
        ...user,
        user_workspace: user.userWorkspaces,
      };

      return profile || undefined;
    } catch (error) {
      console.error('Error fetching user:', error);
      return undefined;
    }
  }

  async findById(id: User['id']): Promise<User | undefined> {
    try {
      const user = await this.userRepository.findOne({
        where: { id },
        relations: ['userWorkspaces'],
      });

      const profile = {
        ...user,
        user_workspace: user.userWorkspaces,
      };

      return profile || undefined;
    } catch (error) {
      console.error('Error fetching user:', error);
      return undefined;
    }
  }

  async getUserRole(
    userId: string,
    workspaceId: string
  ): Promise<string | null> {
    try {
      const userWorkspace = await this.userWorkspaceRepository.findOne({
        where: {
          userId,
          workspaceId,
        },
        select: ['role'],
      });

      return userWorkspace?.role || null;
    } catch (error) {
      console.error('Error getting user role:', error);
      return null;
    }
  }

  async verifyToken(token: string): Promise<any> {
    try {
      const { data, error } = await this.supabase.auth.getUser(token);

      if (error || !data.user) {
        return null;
      }

      return data.user;
    } catch (error) {
      console.error('Error verifying token:', error);
      return null;
    }
  }

  async migrateUsers() {
    try {
      const existingUsers = await this.userRepository.find({
        where: { auth_id: null },
        relations: ['userWorkspaces'],
      });

      console.log(`Found ${existingUsers.length} users to migrate`);

      let successCount = 0;
      const errors = [];

      // Process each user
      for (const user of existingUsers) {
        try {
          // Create the user in Supabase Auth
          const { data: authUser, error: authError } =
            await this.supabase.auth.admin.createUser({
              email: user.email,
              password: 'changeme',
              email_confirm: true,
              user_metadata: {
                name: user.name,
                workspaces: user.userWorkspaces.map((uw) => ({
                  workspaceId: uw.workspaceId,
                  role: uw.role,
                })),
              },
            });

          if (authError)
            throw new Error(
              `Auth error for ${user.email}: ${authError.message}`
            );

          // Update the user's auth_id using TypeORM
          await this.userRepository.update(
            { id: user.id },
            { auth_id: authUser.user.id }
          );

          successCount++;
          console.log(`Successfully migrated user ${user.email}`);
        } catch (error) {
          console.error(`Error migrating user ${user.email}:`, error);
          errors.push({ email: user.email, error: error.message });
        }
      }

      console.log(
        `Migration complete. Success: ${successCount}, Errors: ${errors.length}`
      );

      if (errors.length > 0) {
        console.error('Migration errors:', errors);
      }
    } catch (error) {
      console.error('Migration script error:', error);
    }
  }

  async createUser({
    email,
    name,
    password,
    options,
  }: {
    email: string;
    name: string;
    password: string;
    options: { workspaceId?: string; workspaceName?: string; role?: Role };
  }): Promise<{ user: User; workspaceId?: string } | null> {
    try {
      let workspaceId = options?.workspaceId;
      const role = options?.role || Role.Contributor;

      // If workspaceName is provided, create the workspace first
      if (!workspaceId && options?.workspaceName) {
        const workspace = this.workspaceRepository.create({
          name: options.workspaceName,
        });
        const savedWorkspace = await this.workspaceRepository.save(workspace);
        workspaceId = savedWorkspace.id;
        console.log(`Created workspace with ID: ${workspaceId}`);
      }

      // Create the user record in the database using TypeORM
      const userData = this.userRepository.create({
        email,
        name,
      });
      const savedUser = await this.userRepository.save(userData);

      // Create the user in Supabase Auth
      const userMetadata: {
        name: string;
        workspaces: { workspaceId: string; role: Role }[];
      } = { name, workspaces: [{ workspaceId, role }] };

      const { data: authUser, error: authError } =
        await this.supabase.auth.admin.createUser({
          email,
          password,
          email_confirm: true,
          user_metadata: userMetadata,
        });

      if (authError) {
        console.error('Error creating auth user:', authError);
        // Rollback user creation in the database
        await this.userRepository.delete(savedUser.id);
        return null;
      }

      // Update the user's auth_id using TypeORM
      await this.userRepository.update(
        { id: savedUser.id },
        { auth_id: authUser.user.id }
      );

      // If we have a workspace, create the user-workspace mapping
      if (workspaceId) {
        try {
          const userWorkspace = this.userWorkspaceRepository.create({
            userId: savedUser.id,
            workspaceId,
            role,
          });
          await this.userWorkspaceRepository.save(userWorkspace);
        } catch (mappingError) {
          console.error('Error mapping user to workspace:', mappingError);
          // We could consider rolling back here, but we'll keep the user
          // since it's already created
        }
      }

      // Fetch the complete user data with workspace info
      const user = await this.findById(savedUser.id);
      if (!user) {
        console.error('Error fetching created user');
        return null;
      }

      return { user, workspaceId };
    } catch (error) {
      console.error('Error in createUser:', error);
      return null;
    }
  }

  /**
   * Queue all documents in a workspace for EcoVadis question linking for a specific project
   */
  async queueAllDocumentsForEcoVadisLinking(
    workspaceId: string,
    projectId: string,
    maxChunks?: number
  ): Promise<number> {
    try {
      // First, get all distinct document IDs that are already linked to this project
      // Query through ProjectEcoVadisQuestionLinkedDocumentChunk table to get documentIds
      const linkedChunks =
        await this.projectEcoVadisQuestionLinkedDocumentChunkRepository
          .createQueryBuilder('chunkLinkTable')
          .leftJoinAndSelect(
            'chunkLinkTable.projectQuestion',
            'projectQuestion'
          )
          .leftJoinAndSelect('chunkLinkTable.documentChunk', 'chunk')
          .where('projectQuestion.projectId = :projectId', { projectId })
          .getMany();

      // Extract distinct document IDs that are already linked
      const alreadyLinkedDocumentIds = new Set(
        linkedChunks
          .map((link) => link.documentChunk?.documentId)
          .filter(Boolean)
      );

      console.log(
        `Found ${alreadyLinkedDocumentIds.size} documents already linked to project ${projectId}`
      );

      // Get all documents in the workspace that have been extracted using TypeORM
      const allDocuments = await this.documentRepository
        .createQueryBuilder('document')
        .where('document.workspaceId = :workspaceId', { workspaceId })
        .andWhere('document.status IN (:...statuses)', {
          statuses: [
            DocumentStatus.DataExtractionFinished,
            DocumentStatus.LinkingDataFinished,
          ],
        })
        .select(['document.id'])
        .getMany();

      if (!allDocuments || allDocuments.length === 0) {
        return 0;
      }

      // Filter out documents that are already linked
      const documentsToLink = allDocuments.filter(
        (document) => !alreadyLinkedDocumentIds.has(document.id)
      );

      console.log(
        `Queueing ${documentsToLink.length} documents out of ${allDocuments.length} total documents`
      );

      if (documentsToLink.length === 0) {
        return 0;
      }

      // Queue each document for linking
      const queuePromises = documentsToLink.map((document) =>
        this.chunkEcovadisLinkingQueue.add(
          JobQueue.ChunkEcovadisLink,
          {
            documentId: document.id,
            projectId,
            maxChunks,
          },
          {
            jobId: `chunkEcovadisLinking-${document.id}-${projectId}`,
            attempts: 3,
            backoff: {
              type: 'exponential',
              delay: 2000,
            },
            removeOnComplete: 5, // Keep last 5 completed jobs
            removeOnFail: 10, // Keep last 10 failed jobs
          }
        )
      );

      await Promise.all(queuePromises);

      return documentsToLink.length;
    } catch (error) {
      console.error('Error queueing documents for EcoVadis linking:', error);
      throw error;
    }
  }
}
