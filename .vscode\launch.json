{"version": "0.2.0", "configurations": [{"name": "Launch Full Stack", "type": "chrome", "request": "launch", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/climate-assistant-frontend/src", "preLaunchTask": "Start All"}, {"name": "Debug Backend", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "start:debug"], "cwd": "${workspaceFolder}/climate-assistant-backend", "console": "integratedTerminal", "restart": true, "sourceMaps": true, "outFiles": ["${workspaceFolder}/climate-assistant-backend/dist/**/*.js"], "skipFiles": ["<node_internals>/**"]}]}