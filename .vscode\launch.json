{"version": "0.2.0", "configurations": [{"name": "Debug Backend Only", "type": "node", "request": "launch", "program": "${workspaceFolder}/climate-assistant-backend/node_modules/@nestjs/cli/bin/nest.js", "args": ["start", "--debug", "--watch"], "cwd": "${workspaceFolder}/climate-assistant-backend", "console": "integratedTerminal", "restart": true, "sourceMaps": true, "outFiles": ["${workspaceFolder}/climate-assistant-backend/dist/**/*.js"], "skipFiles": ["<node_internals>/**"], "env": {"NODE_ENV": "development"}}, {"name": "Debug Frontend Only", "type": "chrome", "request": "launch", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/climate-assistant-frontend/src", "preLaunchTask": "Start Frontend"}, {"name": "Debug Full Stack (Backend + Frontend)", "type": "chrome", "request": "launch", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/climate-assistant-frontend/src", "preLaunchTask": "Start All Debug"}]}