
import { AlertCircle, X } from 'lucide-react';
import { VariantProps } from 'class-variance-authority';
import { useState } from 'react';

import { Button } from '../ui/button';

import {
  Alert,
  AlertDescription,
  AlertTitle,
  alertVariants,
} from '@/components/ui/alert';

export function InfoboxCompletionSteps({
  variant,
}: {
  variant?: VariantProps<typeof alertVariants>['variant'];
}) {
  const [hide, setHide] = useState(false);

  return (
    <Alert variant={variant} hidden={hide}>
      <div className="flex items-center gap-6">
        <AlertCircle className="h-5 w-5" />
        <div className="w-full">
          <div className="flex justify-between">
            <AlertTitle>Complete information in 3 steps</AlertTitle>
            <Button
              variant="link"
              onClick={() => setHide(true)}
              className="p-0 m-0 h-auto rounded-md"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <AlertDescription>
            <ol className="text-slate-900">
              <li>
                Step 1: Upload existing Documents and let them be extracted
                automatically by Glacier
              </li>
              <li>
                Step 2: Review the existing information gaps and add information
                to close gaps.
              </li>
              <li>
                Step 3: Resolve all Information Gaps, to mark a datapoint as
                complete
              </li>
            </ol>
          </AlertDescription>
        </div>
      </div>
    </Alert>
  );
}
