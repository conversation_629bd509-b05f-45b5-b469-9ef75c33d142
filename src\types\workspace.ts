export interface Workspace {
  id: string;
  name: string;
  createdAt: Date;
}

export interface UploadedDocument {
  id: string;
  name: string;
  createdAt: Date;
  workspaceId: string;
}

export interface DocumentChunk {
  id: string;
  documentId: string;
  content: string;
  page: string;
  matchingsJson: string;
  createdAt: Date;
}

export interface DocumentChunkData extends DocumentChunk {
  document: UploadedDocument;
}
