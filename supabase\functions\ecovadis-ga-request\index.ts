// supabase/functions/ecovadis-ga-request

// @ts-ignore
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { authValidator } from "../_shared/authValidator.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { formatScoringFrameworkKey } from "../_config/ecovadis/scoring-framework.ts";
import { DifyClient } from "../_shared/difyRequests.ts";
import { storeLLMGapAnalysisResponse } from "../ecovadis-ga-collector/process.ts";
import { groupEvidenceDocuments } from "./utils.ts";


serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }

  try {
    // Get the request body
    const { projectId, projectQuestionId } = await req.json();
    if (!projectId || !projectQuestionId) {
      return new Response(JSON.stringify({
        error: 'Project ID and Question ID are required'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }

    const { user, error, supabaseClient, response: errResponse } = await authValidator(req);

    if (!user || error || !supabaseClient) {
      return errResponse;
    }

    // 1. Fetch project question details
    const { data: projectQuestion, error: projectQuestionError } = await supabaseClient
      .from('project_ecovadis_question')
      .select(`
        id,
        status,
        impact,
        ecovadis_question:questionId (
          id,
          questionCode,
          question,
          questionName,
          indicator,
          themeId,
          type
        )
      `)
      .eq('id', projectQuestionId)
      .single();

    if (projectQuestionError) {
      console.error('Error fetching project question:', projectQuestionError);
      return new Response(JSON.stringify({
        error: 'Failed to fetch project question details'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }

    if (!projectQuestion) {
      return new Response(JSON.stringify({
        error: 'Question not found for this project'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 404
      });
    }

    // 2. Fetch theme details and sustainability issues
    const { data: theme, error: themeError } = await supabaseClient
      .from('ecovadis_theme')
      .select(`
        id, 
        title, 
        description,
        project_ecovadis_theme!inner (
          id,
          impact,
          issues
        )
      `)
      .eq('id', projectQuestion.ecovadis_question.themeId)
      .eq('project_ecovadis_theme.projectId', projectId)
      .single();

    if (themeError) {
      console.error('Error fetching theme:', themeError);
    }

    // 3. Fetch question options with their answer data
    const { data: options, error: optionsError } = await supabaseClient
      .from('ecovadis_answer_option')
      .select(`
        id, 
        issueTitle, 
        instructions,
        project_ecovadis_answer: project_ecovadis_answer (
          id,
          response
        )
      `)
      .eq('questionId', projectQuestion.ecovadis_question.id)
      .eq('project_ecovadis_answer.projectId', projectId);

    if (optionsError) {
      console.error('Error fetching options and answers:', optionsError);
    }

    // 4. Get all answer IDs to fetch linked documents
    const answerIds: string[] = [];
    if (options) {
      options.forEach(option => {
        if (option.project_ecovadis_answer && option.project_ecovadis_answer.length > 0) {
          answerIds.push(option.project_ecovadis_answer[0].id);
        }
      });
    }

    // 5. Fetch linked document chunks
    const { data: linkedDocumentChunks, error: linkedDocumentChunksError } = await supabaseClient
      .from('project_ecovadis_linked_document_chunks')
      .select(`
        id,
        comment,
        answerId,
        documentChunkId,
        document_chunk: documentChunkId (
          id,
          page,
          content,
          document: documentId (
            id,
            name
          )
        )
      `)
      .in('answerId', answerIds.length > 0 ? answerIds : ['00000000-0000-0000-0000-000000000000']);

    if (linkedDocumentChunksError) {
      console.error('Error fetching linked document chunks:', linkedDocumentChunksError);
    }

    // 6. Get indicator description
    const scoringFrameworkKey = formatScoringFrameworkKey({
      theme: theme ? theme.title : '',
      indicator: projectQuestion.ecovadis_question.indicator
    })

    const scoringFrameworkKeyDriver = scoringFrameworkKey + ':scoring-drivers'

    const [
      { data: scoringFrameworkData },
      { data: scoringFrameworkDriverData }
    ] = await Promise.all([
      supabaseClient
        .from('kv_store')
        .select(`value`)
        .eq('key', scoringFrameworkKey)
        .single(),
      supabaseClient
        .from('kv_store')
        .select(`value`)
        .eq('key', scoringFrameworkKeyDriver)
        .single()
    ]);

    const scoringFramework = scoringFrameworkData?.value;
    const scoringFrameworkDrivers = scoringFrameworkDriverData?.value;

    // 8. Format document evidence
    const evidenceDocuments: string[] = groupEvidenceDocuments(linkedDocumentChunks);

    // 9. Format questionnaire answers
    let questionnaireAnswers = "";
    let questionnaireCurrentAnswer = "";

    // Add the main question
    questionnaireAnswers += `Question: ${projectQuestion.ecovadis_question.question}\n\n`;

    // Add each answer option and its response
    if (options) {
      options.forEach((option, index) => {
        // Only include options that have been answered
        if (option.project_ecovadis_answer && option.project_ecovadis_answer.length > 0) {
          questionnaireAnswers += `Answer ${index + 1} for ${option.project_ecovadis_answer.id}: ${option.issueTitle}\n\n`;
          questionnaireAnswers += `Support ${index + 1}: ${option.instructions || 'No instructions provided'}\n\n`;

          // Add the response if available
          if (option.project_ecovadis_answer[0].response) {
            questionnaireCurrentAnswer += `Answer ${index + 1} for ${option.project_ecovadis_answer.id}: ${option.issueTitle}\n\n`;
            questionnaireCurrentAnswer += `Support ${index + 1}: ${option.instructions || 'No instructions provided'}\n\n`;
            questionnaireCurrentAnswer += `Additional comments ${index + 1}: ${option.project_ecovadis_answer[0].response}\n\n`;
          }
        }
      });
    }

    if (questionnaireAnswers.trim() === "") {
      questionnaireCurrentAnswer = "No answers provided for this question";
    }

    // 10. Prepare theme criteria
    const themeCriteria = theme ?
      `Theme: ${theme.title}\n\n${theme.description}` :
      'Theme information not available';

    // 11. Prepare indicator criteria
    const indicatorCriteria = `Indicator: ${projectQuestion.ecovadis_question.indicator}\n\n` +
      `This indicator is about your company's actions to support your sustainability policies and commitments.\n\n` +
      `The answer options in each question represent best practices for your company's size and industry. Select options that your company has already implemented and provide the documented proof of your actions.`;

    // 12. Prepare the final payload
    const themeSustainabilityIssue: {
      impact: string;
      issueId: string;
    }[] = theme && theme.project_ecovadis_theme.length > 0 ?
        theme.project_ecovadis_theme.map((itheme) => itheme.issues).flat() : [];

    let sustainabilityIssues = ''

    if (themeSustainabilityIssue && themeSustainabilityIssue.length > 0) {
      const { data: sustainabilityIssuesData, error: sustainabilityIssuesError } = await supabaseClient
        .from('ecovadis_sustainability_issues')
        .select('id, issue, definition, industryIssues')
        .in('id', themeSustainabilityIssue.map((issue) => issue.issueId));

      theme.project_ecovadis_theme.forEach((itheme) => {
        itheme.issues.forEach((issue) => {
          const issueData = sustainabilityIssuesData?.find((data) => data.id === issue.issueId);
          if (issueData) {
            sustainabilityIssues += `
              Issue: ${issueData.issue}
              Impact: ${themeSustainabilityIssue.find((i) => i.issueId === issueData.id)?.impact || 'No impact provided'}
              Definition: ${issueData.definition}
              Industry Issues: ${issueData.industryIssues}
              \n`;
          }
        });
      });
    }

    const payload = {
      project_id: projectId,
      question_id: projectQuestionId,
      sustainability_issues: sustainabilityIssues,
      evidence_document: evidenceDocuments.length > 0 ?
        evidenceDocuments.join('\n') :
        "No evidence documents found",
      theme_criteria: themeCriteria,
      indicator_criteria: indicatorCriteria,
      scoring_framework: (typeof scoringFramework === 'object' && scoringFramework !== null) ?
        JSON.stringify(scoringFramework) :
        (scoringFramework || "Scoring framework not available for this indicator"),
        scoring_core_drivers: (typeof scoringFrameworkDrivers === 'object' && scoringFrameworkDrivers !== null && scoringFrameworkDrivers["core-drivers"]) ?
        JSON.stringify(scoringFrameworkDrivers["core-drivers"]) :
        ("Scoring framework drivers not available for this indicator"),
      scoring_extra_drivers: (typeof scoringFrameworkDrivers === 'object' && scoringFrameworkDrivers !== null && scoringFrameworkDrivers["extra-drivers"]) ?
        JSON.stringify(scoringFrameworkDrivers["extra-drivers"]) :
        ("Scoring framework drivers not available for this indicator"),
      questionnaire_answer_options: questionnaireAnswers.trim(),
      questionnaire_answers: questionnaireCurrentAnswer.trim(),
      example_formatting: ""
    };

    // 13. Make dify request
    const difyClient = new DifyClient({
      apiKey: 'app-WiRmWsnLmsSxQ1Dzz77VlO91'
    });

    let difyResult: any = null
    try {
      difyResult = await difyClient.runWorkflow({
        inputs: payload,
        response_mode: 'blocking',
        user: user.id,
      });
    } catch (error) {
      console.error('Error calling Dify API:', error);
      return new Response(JSON.stringify({
        error: 'Failed to get a response from Dify',
        details: error.message,
        payload
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }

    if (!difyResult) {
      return new Response(JSON.stringify({
        error: 'Failed to get a response from Dify',
        payload
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }

    const store = await storeLLMGapAnalysisResponse({
      supabaseClient: supabaseClient,
      projectId: projectId,
      questionId: projectQuestion.ecovadis_question.id,
      projectQuestionId: projectQuestionId,
      llmGapAnalysis: difyResult.data.outputs! as any,
    })

    return new Response(JSON.stringify({ result: difyResult.data.outputs, payload }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});