// supabase/functions/document-analysis

// @ts-expect-error TODO look into this later
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { authValidator } from "../_shared/authValidator.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { DifyClient } from "../_shared/difyRequests.ts";


serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
  
  try {
    // Get the request body
    const { documentId } = await req.json();
    if (!documentId) {
      return new Response(JSON.stringify({
        error: 'Document ID is required'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }

    const { user, error, supabaseClient, response: errResponse } = await authValidator(req);
    
    if (!user || error || !supabaseClient) {
      return errResponse;
    }
    
    // 1. Get workspace details to fetch company name
    const workspaceId = user.user_workspace[0].workspaceId;
    const { data: workspace, error: workspaceError } = await supabaseClient
      .from('workspace')
      .select(`
        id,
        name
      `)
      .eq('id', workspaceId)
      .single();

    if (workspaceError) {
      console.error('Error fetching workspace:', workspaceError);
      return new Response(JSON.stringify({
        error: 'Failed to fetch workspace details'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }
    
    // 2. Fetch document details including name and year
    const { data: document, error: documentError } = await supabaseClient
      .from('document')
      .select(`
        id,
        name,
        year
      `)
      .eq('id', documentId)
      .single();
    
    if (documentError) {
      console.error('Error fetching document:', documentError);
      return new Response(JSON.stringify({
        error: 'Failed to fetch document details'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }
    
    if (!document) {
      return new Response(JSON.stringify({
        error: 'Document not found'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 404
      });
    }
    
    // 3. Fetch all document chunks for this document
    const { data: allChunks, error: chunksError } = await supabaseClient
      .from('document_chunk')
      .select(`
        id,
        page,
        content
      `)
      .eq('documentId', documentId)
      .order('page', { ascending: true });
    
    if (chunksError) {
      console.error('Error fetching document chunks:', chunksError);
      return new Response(JSON.stringify({
        error: 'Failed to fetch document chunks'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }
    
    if (!allChunks || allChunks.length === 0) {
      return new Response(JSON.stringify({
        error: 'No document chunks found'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 404
      });
    }
    
    // 4. Get first 5 pages and last 2 pages with smart selection
    const totalPages = allChunks.length;
    let firstPages: any[] = [];
    let lastPages: any[] = [];
    
    if (totalPages <= 6) {
      // If document has 6 or fewer pages, take all pages as "first pages"
      firstPages = allChunks;
      lastPages = [];
    } else {
      // Take first 5 pages
      firstPages = allChunks.slice(0, 5);
      // Take last 2 pages, but ensure no overlap with first pages
      lastPages = allChunks.slice(-2);
    }
    
    // 5. Format the content for Dify with clear sections
    let formattedContent = '';
    
    if (firstPages.length > 0) {
      formattedContent += totalPages <= 6 
        ? `=== ALL PAGES (${totalPages} pages total) ===\n\n`
        : `=== FIRST 5 PAGES ===\n\n`;
      
      formattedContent += firstPages
        .map(chunk => `Page ${chunk.page}:\n${chunk.content}`)
        .join('\n\n---\n\n');
    }
    
    if (lastPages.length > 0) {
      formattedContent += '\n\n\n=== LAST 2 PAGES ===\n\n';
      formattedContent += lastPages
        .map(chunk => `Page ${chunk.page}:\n${chunk.content}`)
        .join('\n\n---\n\n');
    }
    
    // 6. Prepare the payload for Dify
    const payload = {
      document_id: documentId,
      company_name: workspace.name,
      document_name: document.name,
      submission_date: String(document.year || 'unknown'),
      document_content: formattedContent,
      known_alias: ''
    };
    // 7. Make dify request
    const difyClient = new DifyClient({
      apiKey: 'app-jYGHJR64jIvwWA8eqQdQ52dE'
    });

    let difyResult: any = null;
    try {
      difyResult = await difyClient.runWorkflow({
        inputs: payload,
        response_mode: 'blocking',
        user: user.id,
      });
    } catch (error) {
      console.error('Error calling Dify API:', error);
      return new Response(JSON.stringify({
        error: 'Failed to get a response from Dify',
        details: error.message,
        payload
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }

    if (!difyResult) {      
      return new Response(JSON.stringify({
        error: 'Failed to get a response from Dify',
        payload
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }

    return new Response(JSON.stringify({ 
      result: difyResult.data.outputs, 
      document: {
        id: document.id,
        name: document.name,
        year: document.year
      },
      workspace: {
        id: workspace.id,
        company_name: workspace.name
      },
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});