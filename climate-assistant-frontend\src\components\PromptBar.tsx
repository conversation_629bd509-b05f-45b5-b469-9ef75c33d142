import { FunctionComponent } from 'react';
import { ArrowUp } from 'lucide-react';
import TextareaAutosize from 'react-textarea-autosize';

import { Button } from '@/components/ui/button.tsx';

const PromptBar: FunctionComponent<{
  input: string;
  onInputChange: (value: string) => void;
  onSendButton: () => void;
  isSendDisabled: boolean;
}> = ({ input, onInputChange, onSendButton, isSendDisabled }) => {
  return (
    <div className="flex flex-row relative">
      <TextareaAutosize
        maxRows={14}
        autoFocus
        className="w-full bg-gray-100 pl-12 pr-5 py-5 min-h-[67px] h-fit rounded-[40px] "
        placeholder="Gib hier deine Frage ein"
        value={input}
        onChange={(e) => onInputChange(e.target.value)}
        onKeyPress={(e) => {
          if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            if (!isSendDisabled) {
              onSendButton();
            }
          }
        }}
      ></TextareaAutosize>
      <Button
        disabled={isSendDisabled}
        variant="outline"
        size="icon"
        className={`absolute right-3 bottom-3 bg-transparent rounded-3xl ${isSendDisabled ? 'bg-gray-300 hover:bg-gray-300' : 'bg-gray-800 hover:bg-gray-500'}`}
        onClick={() => onSendButton()}
      >
        <ArrowUp className={`text-gray-100`}></ArrowUp>
      </Button>
    </div>
  );
};

export { PromptBar };
