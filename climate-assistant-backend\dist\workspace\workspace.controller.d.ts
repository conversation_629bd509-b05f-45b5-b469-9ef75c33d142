import { WorkspaceService } from './workspace.service';
import { Role } from 'src/users/entities/user-workspace.entity';
export declare class WorkspaceController {
    private readonly workspaceService;
    constructor(workspaceService: WorkspaceService);
    getWorkspaceDetails(req: any): Promise<import("./entities/workspace.entity").Workspace>;
    getUsersByWorkspace(req: any): Promise<import("../users/entities/user.entity").User[]>;
    getAllWorkspaces(): Promise<import("./entities/workspace.entity").Workspace[]>;
    addUserToWorkspace(req: any, body: {
        emails: string[];
        role: Role;
    }): Promise<{
        success?: string;
        failure?: string;
    }>;
    getWorkspaceById(workspaceId: string): Promise<import("./entities/workspace.entity").Workspace>;
    updateWorkspaceById(req: any, body: any): Promise<import("./entities/workspace.entity").Workspace>;
    updateCompanyDetail(req: any, body: any): Promise<import("./entities/company.entity").Company>;
}
