import { DatapointRequest } from './project';
import { IUser } from './user';

export interface DocumentData {
  id: string;
  workspaceId: string;
  name: string;
  path: string;
  month: number | null;
  day: number | null;
  year: number | null;
  esrsCategory: string[] | null;
  documentType: string | null;
  remarks: string | null;
  createdAt: string;
  creator: Pick<IUser, 'id' | 'name'>;
  status: DocumentStatus;
  chunks: DocumentChunksEntity[];
  datapointsCount: number;
}

export enum DocumentStatus {
  NotProcessed = 'not_processed',
  QueuedForExtraction = 'queued_for_extraction',
  ExtractingData = 'in_extraction',
  FailedExtraction = 'failed_extraction',
  DataExtractionFinished = 'data_extraction_finished',
  QueuedForLinking = 'queued_for_linking',
  LinkingData = 'linking_data',
  FailedLinking = 'failed_linking',
  LinkingDataFinished = 'linking_data_finished',
  ErrorProcessing = 'error_processing',
}

export interface DocumentChunksEntity {
  id: string;
  documentId: string;
  page: string;
  content: string;
  matchingsJson: string;
  metadataJson: any;
  datapointDocumentChunkMap: DatapointDocumentChunkMapEntity[];
}

export interface DocumentChunkMetadataJson {
  headings?: string[];
  sourceDocumentName?: string;
  mainSections?: string[];
  pageNumber: string;
  chunkNumber: number;
}

export interface DatapointDocumentChunkMapEntity {
  id: number;
  active: boolean;
  datapointRequest: DatapointRequest;
}

export interface DatapointRequestLinkage extends DatapointRequest {
  linked: boolean;
}
