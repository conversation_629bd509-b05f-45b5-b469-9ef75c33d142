
import React, { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Project } from '@/types/project';
import { fireConfetti } from '@/lib/confetti';
import { CheckCircle, ArrowRight, FileText } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface SetupCompleteProps {
  project: Project;
}

export const SetupComplete: React.FC<SetupCompleteProps> = ({ project }) => {
  const navigate = useNavigate();
  
  useEffect(() => {
    // Fire confetti when this component mounts
    fireConfetti();
  }, []);
  
  return (
    <div className="flex flex-col items-center">
      <div className="mb-8 text-glacier-mint">
        <CheckCircle className="h-20 w-20" />
      </div>
      
      <p className="text-lg text-gray-600 mb-10 max-w-2xl text-center">
        Your Ecovadis Project is now set up. Glacier AI will help you improve your rating. 
        You can start working on questionnaire or view your documents.
      </p>
      
      <div className="flex gap-4">
        <Button
          onClick={() => navigate('/ecovadis')}
          className="bg-glacier-darkBlue text-white flex items-center gap-2"
        >
          Start Working on Questionnaire
          <ArrowRight className="h-4 w-4" />
        </Button>
        
        <Button
          onClick={() => navigate('/documents')}
          variant="outline"
          className="border-glacier-darkBlue text-glacier-darkBlue flex items-center gap-2"
        >
          <FileText className="h-4 w-4" />
          Show Documents
        </Button>
      </div>
    </div>
  );
};
