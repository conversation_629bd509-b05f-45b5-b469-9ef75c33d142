
import { FunctionComponent, ReactNode } from 'react';
import { SideNavigation } from './SideNavigation';
import { cn } from '@/lib/utils';

export const MainLayout: FunctionComponent<{ children: ReactNode }> = ({
  children,
}) => {
  return (
    <div className="flex flex-row min-h-screen">
      <SideNavigation />
      <div className="flex flex-1 flex-col bg-white ml-[var(--sidebar-width)]">
        <div>{children}</div>
      </div>
    </div>
  );
};

export interface PageHeaderProps {
  title: string;
  description?: string;
  className?: string;
  action?: React.ReactNode;
}

export const PageHeader: React.FC<PageHeaderProps> = ({ 
  title, 
  description, 
  className,
  action
}) => {
  return (
    <div className={cn("pb-5 mb-8 border-b border-gray-200", className)}>
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-glacier-darkBlue">{title}</h1>
          {description && (
            <p className="mt-2 text-gray-600">{description}</p>
          )}
        </div>
        {action && (
          <div className="mt-1">{action}</div>
        )}
      </div>
    </div>
  );
};

// Add the PageLayout component as an alias to MainLayout for compatibility
export const PageLayout = MainLayout;
