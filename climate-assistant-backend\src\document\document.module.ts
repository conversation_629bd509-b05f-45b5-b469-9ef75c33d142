import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DocumentService } from './document.service';
import { DocumentChunk } from './entities/document-chunk.entity';
import { Document } from './entities/document.entity';
import { DocumentController } from './document.controller';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { Workspace } from 'src/workspace/entities/workspace.entity';
import { DocumentParserService } from './document-parser.service';
import { EcovadisIssueParserService } from './ecovadis-issue-parser.service';
import { DatapointDocumentChunk } from 'src/datapoint-document-chunk/entities/datapoint-document-chunk.entity';
import { PromptService } from 'src/prompts/prompts.service';
import { WorkspaceModule } from 'src/workspace/workspace.module';
import { DatapointDocumentChunkModule } from 'src/datapoint-document-chunk/datapoint-document-chunk.module';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { LlmRateLimiterModule } from 'src/llm-rate-limiter/llm-rate-limiter.module';
import { ProjectEcovadisLinkedDocumentChunks } from '../ecovadis/entities/project-ecovadis-linked-document-chunks.entity';
import { QueuesModule } from 'src/queues/queues.module';
import { SupabaseAuthModule } from 'src/auth/supabase/supabase.module';
import { ChatGptService } from 'src/llm/chat-gpt.service';
import {
  EcoVadisAnswerOption,
  EcoVadisQuestion,
  EcoVadisSustainabilityIssue,
  EcoVadisTheme,
  ProjectEcoVadisAnswer,
  ProjectEcoVadisTheme,
} from 'src/ecovadis/entities/ecovadis.entity';
import { DifyService } from 'src/llm/dify.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DocumentChunk,
      Document,
      Workspace,
      DatapointRequest,
      DatapointDocumentChunk,
      ProjectEcovadisLinkedDocumentChunks,
      EcoVadisTheme,
      ProjectEcoVadisTheme,
      EcoVadisSustainabilityIssue,
      EcoVadisQuestion,
      EcoVadisAnswerOption,
      ProjectEcoVadisAnswer,
    ]),
    SupabaseAuthModule,
    WorkspaceModule,
    LlmRateLimiterModule,
    DatapointDocumentChunkModule,
    QueuesModule, // Import to access queues
  ],
  providers: [
    DocumentService,
    WorkspaceService,
    DocumentParserService,
    DifyService,
    EcovadisIssueParserService,
    ChatGptService,
    PromptService,
  ],
  exports: [DocumentService],
  controllers: [DocumentController],
})
export class DocumentModule {}
