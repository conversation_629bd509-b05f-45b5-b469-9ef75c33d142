import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

@Entity('project_ecovadis_linked_document_chunks')
export class ProjectEcovadisLinkedDocumentChunks {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  documentChunkId: string;

  @Column({ type: 'uuid' })
  answerId: string;

  @Column({ type: 'varchar' })
  comment: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;
}
