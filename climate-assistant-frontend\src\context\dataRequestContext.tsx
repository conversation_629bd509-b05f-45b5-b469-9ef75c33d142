import { createContext, useContext, useEffect, useState } from 'react';

import { toast } from '@/components/ui/use-toast';
import { subscribeToDataRequestEvents } from '@/api/data-request/data-request.api';
import { DatapointRequestData } from '@/types/project';

interface DataRequestContextProps {
  refetchDataRequest: () => void;
  setupEventSource: (dataRequestId: string) => Promise<EventSource>;
  eventSource: EventSource | null;
  closeEventSource: () => void;
}

interface DataRequestProviderProps {
  children: React.ReactNode;
  refetchDataRequest: () => void;
}

const DataRequestContext = createContext<DataRequestContextProps | undefined>(
  undefined
);

export const DataRequestProvider: React.FC<DataRequestProviderProps> = ({
  children,
  refetchDataRequest,
}) => {
  const [eventSource, setEventSource] = useState<EventSource | null>(null);

  const setupEventSource = async (dataRequestId: string) => {
    if (eventSource) {
      eventSource.close();
    }

    const newEventSource = await subscribeToDataRequestEvents({
      dataRequestId: dataRequestId,
    });

    newEventSource.onmessage = (event) => {
      const eventData = JSON.parse(event.data);
      const esrsDatapointTag =
        eventData.datapointRequest.esrsDatapoint.datapointId;
      const datapointRequest: DatapointRequestData = eventData.datapointRequest;
      const { status: messageStatus, operation } = eventData;

      if (messageStatus === 'success') {
        toast({
          title: `Datapoint ${operation} completed`,
          description: `Datapoint ${esrsDatapointTag} ${operation} completed`,
          variant: 'success',
        });
      }
      if (messageStatus === 'failed') {
        toast({
          title: `Datapoint ${operation} failed`,
          description: `Datapoint ${esrsDatapointTag} ${operation} failed`,
          variant: 'destructive',
        });
      }
      if (datapointRequest) {
        refetchDataRequest();
      }
    };

    setEventSource(newEventSource);
    return newEventSource;
  };

  const closeEventSource = () => {
    if (eventSource) {
      console.log('Closing event source');
      eventSource.close();
      setEventSource(null);
    }
  };

  useEffect(() => {
    return () => {
      closeEventSource();
    };
  }, []);

  return (
    <DataRequestContext.Provider
      value={{
        refetchDataRequest,
        setupEventSource,
        eventSource,
        closeEventSource,
      }}
    >
      {children}
    </DataRequestContext.Provider>
  );
};

export const useDataRequestContext = (): DataRequestContextProps => {
  const context = useContext(DataRequestContext);
  if (!context) {
    throw new Error(
      'useDataRequestContext must be used within a DatapointProvider'
    );
  }
  return context;
};

export default DataRequestContext;
