
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateEcovadisGap } from '@/api/ecovadis/ecovadis.api';
import { toast } from 'sonner';

export const useEcovadisGapsMutation = () => {
  const queryClient = useQueryClient();

  const markGapComplete = useMutation({
    mutationFn: ({ gapId, isComplete }: { gapId: string; isComplete: boolean }) => {
      return updateEcovadisGap(gapId, isComplete);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspaceGaps'] });
    },
    onError: (error) => {
      console.error('Error updating gap completion status:', error);
      toast.error('Failed to update gap status');
    }
  });

  return {
    markGapComplete
  };
};
