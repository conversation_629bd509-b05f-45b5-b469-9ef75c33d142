
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { GapFixModalContent } from './GapFixModalContent';
import { GapItem, LinkedDocument } from '@/types/ecovadis';

interface GapFixModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  relatedDocument: LinkedDocument[];
  gap?: GapItem;
}

export const GapFixModal: React.FC<GapFixModalProps> = ({ 
  open, 
  onOpenChange, 
  relatedDocument,
  gap
}) => {
  if(!gap || !open) return null;
  const gapTitle = gap.gaps?.Title || gap.title || "";
  const recommendedActions = gap.gaps?.["Recommended Actions"] || "";
  const impact = gap.gaps?.["Impact/Effort Evaluation"] || "";
  const exampleText = gap.gaps?.["Sample Text"] || "";
  const description = gap.gaps?.["Description"] || "";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">Let's close this Evidence Gap.</DialogTitle>
        </DialogHeader>
        
        <GapFixModalContent 
          relatedDocument={relatedDocument}
          recommendedActions={recommendedActions}
          exampleText={exampleText}
          impact={impact}
          gapTitle={gapTitle}
          description={description}
          onComplete={() => onOpenChange(false)}
          onClose={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
};
