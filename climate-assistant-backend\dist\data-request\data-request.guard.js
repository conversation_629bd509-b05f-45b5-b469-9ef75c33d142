"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataRequestGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const data_request_service_1 = require("./data-request.service");
let DataRequestGuard = class DataRequestGuard {
    constructor(dataRequestService, reflector) {
        this.dataRequestService = dataRequestService;
        this.reflector = reflector;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const dataRequestId = request.params.dataRequestId;
        const workspaceId = request.user.workspaceId;
        const dataRequest = await this.dataRequestService.findProject(dataRequestId);
        const project = dataRequest.project;
        if (project.workspaceId !== workspaceId) {
            throw new common_1.UnauthorizedException(`Project is not from this workspace`);
        }
        const customCheck = this.reflector.get('customCheck', context.getHandler());
        switch (customCheck) {
            case 'generateWithAI':
                return this.dataRequestService.validateDataRequestGenerationRightsOrFail({
                    dataRequest,
                    userId: request.user.id,
                });
            default:
                return true;
        }
    }
};
exports.DataRequestGuard = DataRequestGuard;
exports.DataRequestGuard = DataRequestGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [data_request_service_1.DataRequestService,
        core_1.Reflector])
], DataRequestGuard);
//# sourceMappingURL=data-request.guard.js.map