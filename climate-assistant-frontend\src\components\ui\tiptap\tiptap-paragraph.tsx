import OrderedList from '@tiptap/extension-ordered-list';
import BulletList from '@tiptap/extension-bullet-list';
import Subscript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import Underline from '@tiptap/extension-underline';
import Paragraph from '@tiptap/extension-paragraph';
import Heading, { Level } from '@tiptap/extension-heading';
import Link from '@tiptap/extension-link';
import { mergeAttributes } from '@tiptap/core';

export const StyledOrderedList = OrderedList.extend({
  renderHTML({ HTMLAttributes }) {
    return [
      'ol',
      mergeAttributes(HTMLAttributes, {
        class: 'ml-6 list-decimal',
      }),
      0,
    ];
  },
});

export const StyledBulletList = BulletList.extend({
  renderHTML({ HTMLAttributes }) {
    return [
      'ul',
      mergeAttributes(HTMLAttributes, {
        class: 'ml-6 list-disc',
      }),
      0,
    ];
  },
});

export const StyledSubscript = Subscript.extend({
  renderHTML({ HTMLAttributes }) {
    return ['sub', HTMLAttributes, 0];
  },
});

export const StyledSuperscript = Superscript.extend({
  renderHTML({ HTMLAttributes }) {
    return ['sup', HTMLAttributes, 0];
  },
});

export const StyledUnderline = Underline.extend({
  renderHTML({ HTMLAttributes }) {
    return [
      'span',
      mergeAttributes(HTMLAttributes, {
        class: 'underline',
      }),
      0,
    ];
  },
});

export const StyledParagraph = Paragraph.extend({
  renderHTML({ HTMLAttributes }) {
    return ['p', HTMLAttributes, 0];
  },
});

export const StyledLink = Link.extend({
  renderHTML({ HTMLAttributes }) {
    return [
      'a',
      mergeAttributes(HTMLAttributes, {
        class: 'underline cursor-pointer',
      }),
      0,
    ];
  },
});

export const StyledHeading = Heading.configure({ levels: [1, 2, 3] }).extend({
  renderHTML({ node, HTMLAttributes }) {
    const hasLevel = this.options.levels.includes(node.attrs.level);
    const level: Level = hasLevel ? node.attrs.level : this.options.levels[0];

    const classes: Partial<Record<Level, string>> = {
      1: 'text-4xl',
      2: 'text-3xl',
      3: 'text-2xl',
    };

    return [
      `h${level}`,
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        class: `${classes[level]}`,
      }),
      0,
    ];
  },
});
