import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1721910372542 implements MigrationInterface {
  name = 'SchemaUpdate1721910372542';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "chat_message"
          ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "chat_history"
          ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "chat_history" DROP COLUMN "createdAt"`,
    );
    await queryRunner.query(
      `ALTER TABLE "chat_message" DROP COLUMN "createdAt"`,
    );
  }
}
