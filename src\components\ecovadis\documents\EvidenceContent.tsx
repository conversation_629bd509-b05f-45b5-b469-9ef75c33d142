
import React from 'react';

interface EvidenceContentProps {
  content: string;
}

export const EvidenceContent: React.FC<EvidenceContentProps> = ({ content }) => {
  if (!content) return null;
  
  return (
    <div className="max-h-[60vh] overflow-y-auto">
      <div className="prose prose-sm max-w-none">
        <div 
          className="text-sm text-gray-700" 
          dangerouslySetInnerHTML={{ 
            __html: content
              .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
              .replace(/\n/g, '<br />') 
          }} 
        />
      </div>
    </div>
  );
};
