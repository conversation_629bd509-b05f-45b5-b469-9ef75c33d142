'use client';

import { useState } from 'react';
import {
  Column,
  ColumnDef,
  ColumnFiltersState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';
import { Skeleton } from '../ui/skeleton';

import { DataTablePagination } from './DataTablePagination';
import { DataTableToolbar } from './DataTableToolbar';

import { DEFAULT_TABLE_STATE, TABLE_TYPES } from '@/constants/table-constants';
import useSessionStorage from '@/hooks/useSessionStorage';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  tableId: TABLE_TYPES; //identifying the table type
  data: TData[];
  bulkActions?: (selectedRows: number[]) => React.ReactNode[];
  columnActions?: {
    columnName: string;
    actions: (column: Column<TData, unknown>) => React.ReactNode;
  }[];
  loading?: boolean;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  tableId,
  bulkActions,
  columnActions,
  loading,
}: DataTableProps<TData, TValue>) {
  const [tableState, setTableState] = useSessionStorage<
    typeof DEFAULT_TABLE_STATE
  >(tableId, DEFAULT_TABLE_STATE);
  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  const { sorting, pagination } = tableState;

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      pagination,
    },
    enableRowSelection: true,
    autoResetAll: false,
    autoResetPageIndex: false,
    onRowSelectionChange: setRowSelection,
    onColumnFiltersChange: (updater) => {
      const newState =
        typeof updater === 'function' ? updater(columnFilters) : updater;
      setColumnFilters(newState);
      setTableState(DEFAULT_TABLE_STATE);
    },
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    onSortingChange: (updater) => {
      const newState =
        typeof updater === 'function' ? updater(tableState.sorting) : updater;
      const newTableState = {
        sorting: [...newState],
        pagination: { ...pagination },
      };
      setTableState(newTableState);
    },
    onPaginationChange: (updater) => {
      const newState =
        typeof updater === 'function'
          ? updater(tableState.pagination)
          : updater;
      const newTableState = {
        sorting: [...sorting],
        pagination: { ...newState },
      };
      setTableState(newTableState);
    },
  });

  return (
    <div className="space-y-4 w-full">
      <DataTableToolbar
        table={table}
        bulkActions={bulkActions}
        columnActions={columnActions}
      />
      <div>
        <Table>
          <TableHeader className="bg-gray-100 rounded-lg overflow-hidden">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="border-none">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      colSpan={header.colSpan}
                      style={{
                        minWidth: header.id === 'name' ? '30vw' : 'auto',
                      }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              [1, 2, 3, 4, 5].map((i) => (
                <TableRow key={i} className="border-none">
                  {columns.map((column, index) => (
                    <TableCell key={column.id || index} className="px-1 py-2.5">
                      <Skeleton className="w-full h-[25px] rounded-md" />
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  className="border-none h-16"
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      className="overflow-none overflow-y-auto p-2"
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} showSelectedRows={true} />
    </div>
  );
}
