import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1737441070357 implements MigrationInterface {
  name = 'SchemaUpdate1737441070357';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."datapoint_generation_status_enum" AS ENUM('pending', 'approved', 'rejected')`,
    );
    await queryRunner.query(
      `CREATE TABLE "datapoint_generation" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "data" json NOT NULL, "status" "public"."datapoint_generation_status_enum" NOT NULL DEFAULT 'pending', "datapointRequestId" uuid, CONSTRAINT "PK_67f175f650f94abf28fe2536522" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_generation" ADD CONSTRAINT "FK_4b2adcda7c85ad1d3a69c6c7329" FOREIGN KEY ("datapointRequestId") REFERENCES "datapoint_request"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_generation" DROP CONSTRAINT "FK_4b2adcda7c85ad1d3a69c6c7329"`,
    );
    await queryRunner.query(`DROP TABLE "datapoint_generation"`);
    await queryRunner.query(
      `DROP TYPE "public"."datapoint_generation_status_enum"`,
    );
  }
}
