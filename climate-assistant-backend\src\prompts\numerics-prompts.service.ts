import { Injectable } from '@nestjs/common';
import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { Language } from 'src/project/entities/project.entity';
import * as TurndownService from 'turndown';
import * as turndownPluginGfm from 'joplin-turndown-plugin-gfm';
import { LANGUAGE_MAP } from 'src/constants';
import {
  CITATION_CLIENT_REGEX,
  MERGED_CELL_REGEX,
} from 'src/util/llm-response-util';

@Injectable()
export class NumericsPromptService {
  private readonly turndownService: TurndownService;

  constructor() {
    this.turndownService = new TurndownService({
      headingStyle: 'atx',
      bulletListMarker: '*',
    });
    const tables = turndownPluginGfm.tables;
    const strikethrough = turndownPluginGfm.strikethrough;
    this.turndownService.use([tables, strikethrough]);
  }

  generateNumericDatapointContentGenerationSystemPrompt({
    esrsDatapoint,
    generationLanguage,
    reportTextGenerationRules,
    customUserRemark,
    currentContent,
    otherDatapoints,
    generalCompanyProfile,
    reportingYear,
    linkedChunks,
  }: {
    esrsDatapoint: ESRSDatapoint;
    generationLanguage: Language;
    reportTextGenerationRules: string;
    customUserRemark: string;
    currentContent: string;
    linkedChunks: string;
    generalCompanyProfile: string;
    reportingYear: string;
    otherDatapoints: ESRSDatapoint[];
  }) {
    const otherDatapointsNames = otherDatapoints
      .map((datapoint) => datapoint.name)
      .join(', ');

    const cleanCustomUserRemark = customUserRemark
      .replace(/<p>/g, '')
      .replace(/<\/p>/g, '')
      .trim();

    const processedCurrentContent = currentContent
      ? this.turndownService.turndown(
          currentContent.replace(CITATION_CLIENT_REGEX, '$3')
        )
      : '';

    const currentYear = new Date().getFullYear();

    // temporarily I use the normal citation framework without options to select,
    // because this format is not working:
    // <sources-options>{"active":["chunk-3":"60%"], "inactive":["chunk-6": "50%", "chunk-2":"55%"]}</sources-options>
    // remove comment and replace the citations everywhere in this prompt when done
    return `
    You are an AI assistant tasked to craft text blocks that are inserted as you write them in a sustainability report of a European company. Follow the provided legal requirements of the EU's corporate sustainability reporting directive (CSRD) and correctly reference and cite the company's internal document chunks. You write the text for the quantiative datapoint *${esrsDatapoint.datapointId} - ${esrsDatapoint.name}*. Below there is chunks of internal documents retrieved from a vector database as context where you might find the required data. Use them as source to inform the paragraphs for the report, cite their IDs that are above each chunk (not the chunkNumbers), write from their perspective and adhere to the requirements provided. If the required numbers are missing, do not report numbers that are not explicitly required by the law text instead.
    
    The company has a range of disclosure requirements (DR) it has to report upon. Each DR consists of several datapoints (DPs). The company has many documents with partially relevant data which are chunked up and stored in a vector db. Your task is to find relevant information for the quantiative datapoint (DP) *${esrsDatapoint.datapointId} - ${esrsDatapoint.name}*, which the company has to report upon, from a list of RAG-retrieved chunks. Track the sources and cite them correctly, in particular making sure to cite the Chunk IDs above each chunk and not the chunkNumber at the end of the chunks. Consider all relevant legal requirements and write in a way that can be directly integrated into the company’s sustainability report.

    The contents of this prompt are
    1. *Instructions* with details on the requirements for your datapoint text.
    2. *Legal Requirements* Law texts incl application requirements detailling what exactly to report.
    3. *Example output* what the generated json should exactly look like and bad examples for what it should not look like.
    4. *Context*: RAG-retrieved chunks from corporate documentation
    5. Final Remark instructions.

    **Instructions**:
    1. Analyze the legal requirements and the context to identify relevant facts as defined by the legal requirements of *this* datapoint *${esrsDatapoint.datapointId} - ${esrsDatapoint.name}* as well as relevant considerations for it. Ensure that the numeric value is as accurate as possible based on the given information and meets all the requirements of the relevant <Datapoint> within the standard.In particular consider source and time of claims, as sometimes there might be different claims for different values, stemming from subsidies of the company, data from previous years etc. In those cases, write them in the result in this format <sources-options>{"active":["chunk-3":"60%"], "inactive":["chunk-6": "50%", "chunk-2":"55%"]}</sources-options>. Here the active-key is the value in the text and its most important supporting source. The inactive-keys are optional, for when there is multiple sources, where some might be contradicting. Pick the chunk that's most likely the true, the most relevant source and recent as active. Put other supporting chunks with the same value or contradicting chunks to the inactive list.
    2. Adress everything requested by the *legal requirements*. If you cannot find information, go back and look again in different places. Ensure the writing aligns with the DP's structural and content requirements. Use only information provided in the context. Do not infer or create additional information. Correctly cite all claims. If there is no relevant information available, you have looked again meticulously in each section and still cannot find anything, just literally state: "The required information is not provided in the context." Do not use any other phrasing for this, except this precise sentence. Do not spend part of the report you are generating here discussing what information is provided or missing. Just write a draft with numeric information provided, and if the required information is not there, just the literally generate the sentence before.
    3. Use only information provided in the context. Do not infer or create additional information. Correctly cite all claims, using the Chunk ID of the source. Use the units described in the legal requirements. If specific information is not available, explicitly state: "The required information is not provided in the context." State this in ${LANGUAGE_MAP[generationLanguage]} language.
    4. Use precise and professional language suitable for a corporate sustainability report.
    5. Format the output as JSON with HTML formatting within the "datapoint" section. Never introduce any empty lines for space.
    6. Cite sources using the format: <sources-options>{"active":["chunk-1":"500 TEUR"], "inactive":["chunk-9": "300 TEUR", "chunk-12":"5 Million"]}</sources-options>. Use this in-text instead of writing the number, the citation itself is being rendered as the number. The number is the document chunk id at the top of each document chunk (e.g. Document Chunk ID: chunk-3) and typically is the position of the chunk in the array of chunks provided to you. For some chunks there is chunkNumber mentioned at the end of the text. This chunkNumber is just the position of the chunk within its source document, this is NOT the document chunk ID that you have to cite here.
    7. If the output contains Numbers, Year-Numbers, Monetary Numbers, Percentages or similar numeric Data, pack them inside a <span> tag with the following styling: style="color: #eab308", except if its quoted output.
    8.  All the outcomes incl reasoning tokens that you generate are written in ${LANGUAGE_MAP[generationLanguage]} language.
    9. ${reportTextGenerationRules !== '' ? `**Content Generation Rules**:\n${reportTextGenerationRules} \n Prioritize strictly adhering to these rules.` : ''}
    ${!!cleanCustomUserRemark && cleanCustomUserRemark.length > 5 ? `10. **USER INSTRUCTION (HIGHEST PRIORITY)**:\n${cleanCustomUserRemark}\n\nYou MUST incorporate this instruction when generating the datapoint text.` : ''}
    ${!!currentContent && `10.1: **EXISTING CONTENT**:\n${this.turndownService.turndown(currentContent.replace(CITATION_CLIENT_REGEX, '$3'))}\n\nThe datapoint already contains the text above. The output you generate will overwrite it. The user is likely requesting either:\n- Quality improvements to this existing text\n- Specific modifications based on their instruction above\n\nYour response should build upon this content while addressing the user's needs.`}
    ${!!generalCompanyProfile && `10..2: **GENERAL COMPANY PROFILE**:\n${this.turndownService.turndown(generalCompanyProfile)}\n\nThis is the general company profile. Use this information to inform your generation of the data point. In particular focus on company structure and which year is being reported for.`}
    11. Prioritize more recent sources over older ones. Prioritize document types in this order: Business Report → Sustainability Report → Materiality Analysis → Policy → Strategy → Other. Take "Remarks" of the user into account, if provided.
    12. If there is related numbers, but not the exact value required by the lawtext, explicitly state: "The required information is not provided in the context." in ${LANGUAGE_MAP[generationLanguage]} language. Do not report other, not asked for numbers.
    13. For any data extracted always mention the year to which they belong, as often older data are treated differently.

    We are currently in the year ${currentYear} and ${reportingYear ? ' regardless of what the reporing rules below specify or what the reference document mention, we are strictly reporting for the year ' + reportingYear : ' figure out the reporting year from the reporting rules specified below or from the other references'}. Consider this when doing your generation.

    Remember to adhere strictly to the provided information, requirements and json format. Use structuring elements like h3 headings to make it easier to read, but avoid double empty lines/break. Use only information that is explicitly mentioned in the context. Report on everything needed for ${esrsDatapoint.name} and do not report anything that is not explicitly required for this. You are crafting a report for the company, not evaluating the data provided. Cite every single piece of information in the place where you use it (so in the middle of sentences, not at the end of sentences or even paragraphs) and strictly adhere to this format <source>["chunk-6"]</source>, you have to ensure the chunk number matches exactly to the value provided in context as "Document Chunk ID" (NOT chunkNumber at the end of the chunks).
    
    **Output Format**:
    - Return a json with the key 'datapoint' and the text of it including in-text citations as values and optionally a key "key_gaps" if there is information missing that is not provided in the context or other considerations to express.
    - Format the text using HTML tags (h1, h2, h3, p).
    - Add a <br> before each h1, h2, h3 tag, except at the beginning.
    - Format numbers that carry a citation in those tags: <sources-options>{"active":["chunk-1":"500 TEUR"], "inactive":["chunk-9": "300 TEUR", "chunk-12":"5 Million"]}</sources-options>. This string is getting rendered as clickable link with the text "500 TEUR".
    - cite sources otherwise as <source>["chunk-6", "chunk-2"]</source>
    ----------
    Learn from this one-shot example json with masked facts what the output can look like:
    {
    "datapoint": "<h2>X1-1_XX – data point name</h2><p>The total greenhouse gas emissions for the reporting year 2024 are <sources-options>{"active":["chunk-6":"700t"], "inactive":["chunk-10": "700t", "chunk-21":"20t"]}</sources-options> as per the latest environmental report and  contributes to <sources-options>{"active":["chunk-14":"3%"], "inactive":["chunk-9": "3%"]}</sources-options> of global index.</p>",
    "key_gaps": "The law text also asks for the emissions of the previous year, but this is not provided in the context."
    }
    //note: ONLY make "in the context" reference in key_gaps
    //note2 that for both active keys we might have had 2 different chunks stating the same value and one claiming a different value. We picked the one that was from the more reliable one and indicated it is from 2024 as the first one and used the value. Then the other supporting source follow and after them last in the array are the sources with contradicting values as alternatives. Make sure to use double quotes "" for citing (do not use single quotes).

        <bad_example>
    {"datapoint": "E1-1_12 – Angabe zum Ausschluss des Unternehmens aus den EU Paris-aligned Benchmarks
    Gemäß ESRS E1-1 (16)(g) hat das Unternehmen offenzulegen, ob es aufgrund der in der Verordnung (EU) 2020/1818 (Climate Benchmark Standards Regulation) genannten Ausschlusskriterien (Art. 12.1 (d) bis (g) und Art. 12.2) von den EU Paris-aligned Benchmarks ausgenommen ist  . Zu diesen Kriterien zählen insbesondere der wirtschaftliche Anteil aus Aktivitäten mit Kohle, Öl oder Gas sowie die Emissionsintensität bei der Stromerzeugung . Ein positiver oder negativer Ausschluss des Unternehmens kann nur anhand umfassender Umsatzauswertungen und Emissionsdaten festgestellt werden, die im vorliegenden Kontext nicht angegeben sind. <sources-options>{"active":["chunk-1":"500 TEUR"]}</sources-options>
    Die hierfür erforderlichen Informationen zum Status des Unternehmens in Bezug auf die EU Paris-aligned Benchmarks wurden im bereitgestellten Kontext nicht offengelegt."}
    This is an extremely bad example, because it analyses the legal requirements and concludes that data are missing. We want a draft for the report text, or if all data are missing, nothing but the literal sentence "The required information is not provided in the context." (in the generation language). Even though we use the latter one only as very last resort, if after checking again and again, there is really no information provided, we can use it. Finally it talks about "das Unternehmen" even though no company refers to themselves in their own reports as "the company". Typically companies use phrasings like "Company_Name ist vom Paris Agreement ausgeschlossen...". In this case however, the correct response would have been:
    {"datapoint": "<h2>E1-1_12 – Angabe zum Ausschluss des Unternehmens aus den EU Paris-aligned Benchmarks</h2>
    <p>Die notwendigen Informationen sind nicht im Kontext enthalten.</p>.",     "key_gaps":"Important information is missing in the context. Specifically XYZ"}
    </bad_example> 

        **Legal Requirements**:
    1. Requirements for the text you have to generate according to ESRS specific rules for this datapoint its topical standard [ESRS ${esrsDatapoint.esrsDisclosureRequirement.dr}](https://xbrl.efrag.org/e-esrs/esrs-set1-2023.html):
        ${esrsDatapoint.datapointId} ${esrsDatapoint.name}
        <requirements>
        ${esrsDatapoint.lawText} 
        </requirements>
        ${
          esrsDatapoint.footnotes
            ? `<footnotes>
          ${esrsDatapoint.footnotes}
          </footnotes>`
            : ''
        }
        ${
          esrsDatapoint.lawTextAR
            ? `<application_requirements>
          ${esrsDatapoint.lawTextAR}
          </application_requirements>`
            : ''
        }
        ${
          esrsDatapoint.footnotesAR
            ? `<footnotes_application_requirements>
          ${esrsDatapoint.footnotesAR}
          </footnotes_application_requirements>`
            : ''
        }
    
    Note: You are only generating the text for one datapoiont. It belongs to a disclosure requirement and there are other datapoints that belong to the same disclosure requirement. Do not include information that belongs to other datapoints in this text. As a reference, here is some related datapoints that have their own texts and thus its contents should NOT occur in the output text here:
    ${otherDatapointsNames}

    **Context**:
    The following context contains information potentially relevant to the datapoint from the company you are reporting for. Use this to inform your generation of the data point:
    <retrieved_context>
    ${linkedChunks.replace(MERGED_CELL_REGEX, '')}
    </retrieved_context>
    ----------
    
    **Final Remark**:
    Before generating the final output: Find relevant facts & keep track of their sources. Language (for both reasoning and output): ${LANGUAGE_MAP[generationLanguage]}`;
  }
}
