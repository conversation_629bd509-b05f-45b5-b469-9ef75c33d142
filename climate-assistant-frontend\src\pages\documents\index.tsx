import { FunctionComponent } from 'react';

import { DataTable } from '@/components/data-table/DataTable';
import { DataTableFacetedFilter } from '@/components/data-table/DataTableFacetedFilter';
import {
  documentsListingColumns,
  documentStatuses,
} from '@/components/documents/DocumentsTableConfig';
import { MainLayout } from '@/components/MainLayout';
import { UploadDocumentsButton } from '@/components/documents/UploadDocuments';
import { useDocuments } from '@/hooks/useDocuments';
import { TABLE_TYPES } from '@/constants/table-constants';
import { ESRS_CATEGORIES } from '@/constants/documentsConstants';
import DocumentsProvider from '@/context/documentsContext';

export const Documents: FunctionComponent = () => {
  const { uploadedFiles = [], loading, refetchDocuments } = useDocuments();

  return (
    <MainLayout>
      <div
        className={`flex flex-col items-center flex-1 justify-center pb-60 max-w-[90vw]`}
      >
        <div className="flex justify-between w-full items-center mb-8 mt-12">
          <h1 className={`text-4xl font-bold`}>Documents</h1>
          <UploadDocumentsButton refreshData={refetchDocuments} />
        </div>
        <DocumentsProvider refetchDocuments={refetchDocuments}>
          <DataTable
            data={uploadedFiles}
            loading={loading}
            tableId={TABLE_TYPES.DOCUMENT}
            columnActions={[
              {
                columnName: 'status',
                actions: (column) => (
                  <DataTableFacetedFilter
                    column={column}
                    title="Status"
                    options={documentStatuses}
                  />
                ),
              },
              {
                columnName: 'esrsCategory',
                actions: (column) => (
                  <DataTableFacetedFilter
                    column={column}
                    title="Related Areas"
                    options={Object.values(ESRS_CATEGORIES)}
                  />
                ),
              },
            ]}
            columns={documentsListingColumns}
          />
        </DocumentsProvider>
      </div>
    </MainLayout>
  );
};
