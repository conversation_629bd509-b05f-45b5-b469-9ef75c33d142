# **Self-Hosted Monitoring and Logging Setup for Climate Assistant**

## **Overview**

This document outlines the configuration of a **self-hosted monitoring and logging stack** for the **Climate Assistant** application, a Dockerized **NestJS backend** with a **React frontend**. The setup includes:

- **Metrics Collection**: `Prometheus` and `cAdvisor` for container performance monitoring.
- **Logging**: `Loki` for collecting application logs from Docker containers.
- **Log Aggregation**: `Promtail` for shipping logs from containers to Loki.
- **Visualization**: `Grafana` for monitoring logs and metrics.
- **Reverse Proxy**: `Nginx`, exposing **Grafana** at `/grafana`.

This setup ensures efficient **monitoring, logging, and alerting** for both the **backend** and **frontend** services.

---

## **1. System Architecture**

### **Components**

1. **Frontend (`React`)**: Serves the Climate Assistant UI.
2. **Backend (`NestJS`)**: Handles API requests and business logic.
3. **Database (`PostgreSQL`)**: Stores application data.
4. **Redis**: Caching and job queueing.
5. **Prometheus**: Scrapes `cAdvisor`, `Promtail`, and `Postgres Exporter` metrics.
6. **cAdvisor**: Collects container-level metrics (CPU, Memory, Network, etc.).
7. **Loki**: Stores application logs.
8. **Promtail**: Reads logs from **backend, frontend, Redis**, and forwards them to **Loki**.
9. **Grafana**: Displays logs and metrics via dashboards.
10. **PgAdmin**: Web UI for managing the PostgreSQL database.
11. **Nginx**: Reverse proxy for exposing **Grafana** and handling SSL via **Certbot**.

### **Data Flow**

```
[Frontend] → cAdvisor → Prometheus → Grafana
[Backend] → cAdvisor → Prometheus → Grafana
[Database] → Postgres Exporter → Prometheus → Grafana
[Docker Logs] → Promtail → Loki → Grafana
```

---

## **2. Installation and Setup**

### **2.1 Define Volumes (Persistent Storage)**

To persist data, add the following named volumes in `docker-compose.yml`:

```yaml
volumes:
  prometheus_data:
  loki_data:
  promtail_data:
  grafana_data:
  cadvisor_data:
  backend-db-data:
  pgadmin-data:
```

### **2.2 Configure `docker-compose.yml` for Monitoring Services**

#### **cAdvisor (Container Metrics Collection)**

- Collects resource usage per container.

#### **Prometheus (Time-Series Metrics Database)**

- Stores metrics scraped from `cAdvisor`, `Promtail`, and `Postgres Exporter`.

#### **Loki (Log Storage)**

- Stores and indexes logs from **backend, frontend, Redis**, and **database**.

#### **Promtail (Log Aggregation)**

- Reads logs from **Docker containers** and forwards them to Loki.

#### **Grafana (Data Visualization)**

- Displays logs and performance metrics via dashboards.

#### **Postgres Exporter**

- Exposes database performance metrics for `Prometheus`.

---

## **3. Configuring Nginx for Grafana Exposure**

Modify `nginx` configuration to expose Grafana at `/grafana`:

```nginx
location /grafana/ {
    proxy_pass http://grafana:3000/grafana/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    proxy_http_version 1.1;
    proxy_set_header Connection "";
    proxy_set_header Referer "";
    proxy_set_header X-Frame-Options "SAMEORIGIN";
}
```

---

## **4. Querying Logs in Grafana (Loki + LogQL)**

### **4.1 Basic Queries**

- Show logs from the backend service:
  ```logql
  {container_name="climate-assistant-backend"}
  ```
- Show logs from the frontend service:
  ```logql
  {container_name="climate-assistant-frontend"}
  ```
- Filter logs containing **"ERROR"**:
  ```logql
  {container_name="climate-assistant-backend"} |= "ERROR"
  ```

### **4.2 JSON Logs Filtering (Structured Logging)**

If JSON-based logs are used:

```logql
{container_name="climate-assistant-backend"} | json | log_level = "error"
```

---

## **5. Dropping Unwanted Logs (Filtering in Promtail)**

To ignore **health check logs**, modify `promtail` pipeline:

```yaml
pipeline_stages:
  - match:
      selector: '{container_name="climate-assistant-backend"}'
      stages:
        - drop:
            expression: 'GET /health'
```

---

## **6. Starting the Monitoring Stack**

Start all services using:

```bash
docker-compose up -d --build
```

Check logs:

```bash
docker-compose logs -f
```

---

## **7. Accessing Grafana**

Once running, access **Grafana** at:

```
https://app.glacier.eco/grafana
```

Login credentials:

- **Username**: `<EMAIL>`
- **Password**: `ENV.GF_SECURITY_ADMIN_PASSWORD`

Add the following **data sources** in **Grafana → Configuration**:

1. **Prometheus**: `http://prometheus:9090`
2. **Loki**: `http://loki:3100`

---

## **8. Importing Prebuilt Dashboards in Grafana**

### **8.1 cAdvisor Dashboard for Docker Metrics**

1. Navigate to **Dashboards → Import**.
2. Enter **Dashboard ID**: `193` (cAdvisor).
3. Select **Prometheus** as the data source.
4. Click **Import**.

This displays:

- CPU & Memory usage per container.
- Network traffic.
- Disk I/O.

### **8.2 Loki Logging Dashboard**

1. Go to **Dashboards → Import**.
2. Enter **Dashboard ID**: `13639` (Loki logs).
3. Select **Loki** as the data source.
4. Click **Import**.

This provides:

- Log filtering by container.
- Error tracking.

---

## **9. Setting Up Alerts in Grafana**

### **9.1 High CPU Usage Alert**

1. Go to **Alerts → New Alert Rule**.
2. Use **PromQL**:
   ```promql
   rate(container_cpu_usage_seconds_total{container_label_com_docker_compose_service="climate-assistant-backend"}[5m]) * 100
   ```
3. Set **threshold > 80%**.
4. Add notification (Slack, email).
5. Save alert.

### **9.2 Log-based Alert for High Error Rate**

1. Use **LogQL**:
   ```logql
   count_over_time({container_name="climate-assistant-backend"} |= "ERROR" [1m])
   ```
2. Set **threshold > 50 logs per minute**.
3. Save alert.

---

## **10. Scaling the Monitoring Stack**

### **10.1 Optimizing Prometheus Storage**

Modify `docker-compose.yml`:

```yaml
command:
  - '--storage.tsdb.retention.time=30d'
```

Restart:

```bash
docker-compose restart prometheus
```

### **10.2 Scaling Loki**

Enable caching for improved log queries:

```yaml
storage_config:
  boltdb_shipper:
    cache_ttl: 24h
```

Restart:

```bash
docker-compose restart loki
```

---

## **11. Troubleshooting Issues**

### **11.1 Logs Not Showing in Grafana**

- Check `Promtail` logs:
  ```bash
  docker-compose logs promtail
  ```
- Check `Loki` logs:
  ```bash
  docker-compose logs loki
  ```

### **11.2 High CPU Usage in Prometheus**

Check top resource-consuming containers:

```promql
topk(5, rate(container_cpu_usage_seconds_total[5m]) * 100)
```

---

## **12. Summary**

✅ **Prometheus + cAdvisor** for performance monitoring.  
✅ **Loki + Promtail** for log aggregation.  
✅ **Grafana dashboards** for insights.  
✅ **Nginx reverse proxy** to expose Grafana.  
✅ **Alerts for CPU spikes & error logs.**

With this stack, you have **real-time observability and proactive monitoring! 🚀**
