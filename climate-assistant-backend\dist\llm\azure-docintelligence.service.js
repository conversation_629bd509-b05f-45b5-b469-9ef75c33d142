"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseDocumentWithAzureDocumentIntelligence = parseDocumentWithAzureDocumentIntelligence;
const ai_document_intelligence_1 = require("@azure-rest/ai-document-intelligence");
const identity_1 = require("@azure/identity");
const ai_document_intelligence_2 = require("@azure-rest/ai-document-intelligence");
const fs = require("fs");
async function parseDocumentWithAzureDocumentIntelligence({ filePath, modelId = 'prebuilt-layout', locale = 'en-US', features = [], }) {
    const endpoint = process.env.DOCUMENT_INTELLIGENCE_ENDPOINT;
    const apiKey = process.env.DOCUMENT_INTELLIGENCE_API_KEY;
    if (!endpoint) {
        throw new Error('DOCUMENT_INTELLIGENCE_ENDPOINT environment variable is not set');
    }
    try {
        if (!fs.existsSync(filePath)) {
            throw new Error(`File not found: ${filePath}`);
        }
        console.log('Initializing Azure Document Intelligence client...');
        const client = apiKey
            ? (0, ai_document_intelligence_1.default)(endpoint, { key: apiKey })
            : (0, ai_document_intelligence_1.default)(endpoint, new identity_1.DefaultAzureCredential());
        console.log('Reading file and converting to base64...');
        const base64Source = fs.readFileSync(filePath, { encoding: 'base64' });
        console.log(`Starting document analysis with model: ${modelId}...`);
        const initialResponse = await client
            .path('/documentModels/{modelId}:analyze', modelId)
            .post({
            contentType: 'application/json',
            body: {
                base64Source,
            },
            queryParameters: {
                locale,
                outputContentFormat: 'markdown',
                ...(features.length > 0 && { features }),
            },
        });
        if ((0, ai_document_intelligence_2.isUnexpected)(initialResponse)) {
            console.log(initialResponse.body);
            throw new Error(`Analysis failed: ${initialResponse.body.error?.message || 'Unknown error'}`);
        }
        console.log('Document analysis started, polling for results...');
        const poller = (0, ai_document_intelligence_2.getLongRunningPoller)(client, initialResponse);
        const result = (await poller.pollUntilDone())
            .body;
        if (result.status !== 'succeeded') {
            throw new Error(`Analysis failed with status: ${result.status}`);
        }
        if (!result.analyzeResult?.content) {
            throw new Error('No content returned from the analysis');
        }
        console.log('Document analysis complete');
        return {
            text: result.analyzeResult.content,
            metadata: {
                source: filePath,
                contentFormat: result.analyzeResult.contentFormat || 'markdown',
                pageCount: result.analyzeResult.pages?.length,
                apiVersion: result.analyzeResult.apiVersion,
            },
        };
    }
    catch (error) {
        console.error('Error while parsing document:', error);
        if (error instanceof Error) {
            throw new Error(`Document analysis failed: ${error.message}`);
        }
        throw error;
    }
}
//# sourceMappingURL=azure-docintelligence.service.js.map