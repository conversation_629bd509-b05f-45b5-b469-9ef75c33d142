
import { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { DocumentDetailsResponse, fetchDocumentDetails } from '@/api/documents/documents.api';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Brain, Loader2 } from 'lucide-react';
import { formatDocumentType } from '@/utils/documentUtils';
import { format } from 'date-fns';
import { DocumentContentTab } from '@/components/ui/documents/tabs/DocumentContentTab';
import { DocumentGapsTab } from '@/components/ui/documents/DocumentGapsTab';
import { MainLayout, PageHeader } from '@/components/MainLayout';
import { userHasRequiredRole } from '@/lib/utils';
import { USER_ROLE } from '@/constants/workspaceConstants';
import { useAuthentication } from '@/api/authentication/authentication.query';
import { analyzeDocument, DocumentAnalysisResponse } from '@/api/documents/document-analysis.api';

const DocumentDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [document, setDocument] = useState<DocumentDetailsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [analysisResult, setAnalysisResult] = useState<DocumentAnalysisResponse | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisError, setAnalysisError] = useState<string | null>(null);

  const { user } = useAuthentication();
  const isSuperAdminUser = useMemo(
    () => userHasRequiredRole([USER_ROLE.SuperAdmin], user),
    [user]
  );



  useEffect(() => {
    const loadDocument = async () => {
      if (id) {
        try {
          const data = await fetchDocumentDetails(id);
          setDocument(data);
        } catch (error) {
          console.error('Error loading document:', error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadDocument();
  }, [id]);

  const groupedQuestions = useMemo(() => {
    if (!document?.linkedQuestions) return [];
    return Object.values(document.linkedQuestions.reduce((acc, question) => {
      // Create a unique key based on question code, project and answer option
      const key = `${question.questionCode}-${question.issueTitle || ''}`;
      
      if (!acc[key]) {
        acc[key] = true
      }

      return acc;
    }, {} as Record<string, boolean>));
}, [document?.linkedQuestions]);

  const handleBackClick = () => {
    navigate('/documents');
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="m-4">
          <PageHeader
            title="Loading Document..." 
            description="Please wait while we load the document details."
          />
        </div>
      </MainLayout>
    );
  }

  if (!document) {
    return (
      <MainLayout>
        <div className="m-4">
          <PageHeader 
            title="Document Not Found" 
            description="The requested document could not be found."
          />
          <Button onClick={handleBackClick} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Documents
          </Button>
        </div>
      </MainLayout>
    );
  }

  const handleSave = (updatedDoc: DocumentDetailsResponse) => {
    setDocument(updatedDoc);
  };

  const handleAnalyzeDocument = async () => {
    if (!id) return;
    
    setIsAnalyzing(true);
    setAnalysisError(null);
    
    try {
      const result = await analyzeDocument(id);
      setAnalysisResult(result);
    } catch (error) {
      console.error('Error analyzing document:', error);
      setAnalysisError(error instanceof Error ? error.message : 'Failed to analyze document');
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <MainLayout>
      <div className="m-4">
        <div className="mb-6">
          <div className="flex items-center space-x-2">
            <Button onClick={handleBackClick} variant="outline" size="icon" className="h-8 w-8">
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <PageHeader 
              title={document.name}
              description={`${formatDocumentType(document.documentType || '')} • ${format(new Date(document.createdAt), 'MMMM d, yyyy')}`}
            />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow overflow-hidden">
          <Tabs defaultValue="content" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="content">Document Content</TabsTrigger>
              <TabsTrigger value="gaps">EcoVadis Answers ({groupedQuestions.length})</TabsTrigger>
              {isSuperAdminUser && (
              <TabsTrigger value="document-qa">Evalute Document</TabsTrigger>
              )}
              {/* <TabsTrigger value="properties">Properties</TabsTrigger> */}
            </TabsList>
            
            <TabsContent value="content" className="p-4">
              <DocumentContentTab document={document} />
            </TabsContent>
            
            <TabsContent value="gaps" className="p-4">
              <DocumentGapsTab document={document} />
            </TabsContent>

            {isSuperAdminUser && (
            <TabsContent value="document-qa" className="p-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Document Analysis</h3>
                  <Button 
                    onClick={handleAnalyzeDocument}
                    disabled={isAnalyzing}
                    className="flex items-center gap-2"
                    variant='outline'
                  >
                    {isAnalyzing ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Brain className="h-4 w-4" />
                    )}
                    {isAnalyzing ? 'Analyzing...' : 'Analyze Document'}
                  </Button>
                </div>
                
                {analysisError && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <p className="text-red-800 text-sm">
                      <strong>Error:</strong> {analysisError}
                    </p>
                  </div>
                )}
                
                {analysisResult && (
                  <div className="space-y-4">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-medium text-blue-900 mb-2">Document Metadata</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-blue-800">Company:</span>
                          <p className="text-blue-700">{analysisResult.workspace?.company_name || 'N/A'}</p>
                        </div>
                        <div>
                          <span className="font-medium text-blue-800">Document:</span>
                          <p className="text-blue-700">{analysisResult.document?.name || 'N/A'}</p>
                        </div>
                        <div>
                          <span className="font-medium text-blue-800">Year:</span>
                          <p className="text-blue-700">{analysisResult.document?.year || 'N/A'}</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-3">Analysis Results</h4>
                      <div className="bg-white border rounded p-3 max-h-96 overflow-y-auto">
                        <pre className="text-xs whitespace-pre-wrap text-gray-700">
                          {JSON.stringify(analysisResult.result, null, 2)}
                        </pre>
                      </div>
                    </div>
                  </div>
                )}
                
                {!analysisResult && !isAnalyzing && !analysisError && (
                  <div className="text-center py-8 text-gray-500">
                    <Brain className="h-12 w-12 mx-auto mb-3 text-gray-400" />
                    <p>Click "Analyze Document" to generate an AI analysis of this document</p>
                  </div>
                )}
              </div>
            </TabsContent>
            )
            }
            
            {/* <TabsContent value="properties" className="p-4">
              <DocumentPropertiesTab 
                document={document} 
                onSave={handleSave}
              />
            </TabsContent> */}
          </Tabs>
        </div>
      </div>
    </MainLayout>
  );
};

export default DocumentDetail;
