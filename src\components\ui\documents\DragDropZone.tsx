
import React from 'react';
import { Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface DragDropZoneProps {
  isDragging: boolean;
  onDragOver: (e: React.DragEvent) => void;
  onDragLeave: () => void;
  onDrop: (e: React.DragEvent) => void;
  onFileSelect: () => void;
}

export function DragDropZone({
  isDragging,
  onDragOver,
  onDragLeave,
  onDrop,
  onFileSelect
}: DragDropZoneProps) {
  return (
    <div
      onDragOver={onDragOver}
      onDragLeave={onDragLeave}
      onDrop={onDrop}
      className={`
        border-2 border-dashed rounded-xl p-8
        flex flex-col items-center justify-center text-center
        transition-all duration-200
        ${isDragging 
          ? 'border-glacier-mint bg-glacier-mint/5' 
          : 'border-gray-200 hover:border-glacier-mint/50 hover:bg-gray-50'
        }
      `}
    >
      <div className="w-12 h-12 rounded-full bg-glacier-mint/20 flex items-center justify-center mb-4">
        <Upload className="h-6 w-6 text-glacier-darkBlue" />
      </div>
      
      <h3 className="text-lg font-medium text-glacier-darkBlue mb-2">
        Drop your documents here
      </h3>
      
      <p className="text-sm text-gray-500 mb-4 max-w-md">
        Upload sustainability reports, policies, or other ESG documentation in PDF, DOCX, or XLSX format
      </p>
      
      <div className="flex flex-col sm:flex-row gap-4">
        <Button 
          onClick={onFileSelect}
          className="bg-glacier-darkBlue hover:bg-glacier-darkBlueAlt"
        >
          Select Files
        </Button>
      </div>
    </div>
  );
}
