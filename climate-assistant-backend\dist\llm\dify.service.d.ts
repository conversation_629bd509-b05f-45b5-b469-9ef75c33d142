import { ConfigService } from '@nestjs/config';
export interface DifyCreateDocumentRequest {
    name: string;
    text: string;
    indexing_technique: 'high_quality' | 'economy';
    process_rule: {
        mode: 'automatic' | 'custom';
    };
}
export interface DifyBaseResponse {
    batch: string;
}
export interface DifyDocument {
    id: string;
    position: number;
    data_source_type: 'upload_file' | string;
    data_source_info: {
        upload_file_id: string;
    };
    dataset_process_rule_id: string;
    name: string;
    created_from: 'api' | 'web' | string;
    created_by: string;
    created_at: number;
    tokens: number;
    indexing_status: 'waiting' | 'parsing' | 'indexing' | 'completed' | 'error' | string;
    error: string | null;
    enabled: boolean;
    disabled_at: number | null;
    disabled_by: string | null;
    archived: boolean;
    display_status: 'queuing' | 'indexing' | 'ready' | 'error' | string;
    word_count: number;
    hit_count: number;
    doc_form: 'text_model' | string;
}
export interface DifyUpdateMetadataResponse extends DifyBaseResponse {
    status: number;
}
export interface DifyCreateDocumentResponse extends DifyBaseResponse {
    document: DifyDocument;
}
export declare class DifyService {
    private configService;
    private readonly logger;
    private readonly client;
    private readonly datasetId;
    private readonly metadataFieldMappings;
    constructor(configService: ConfigService);
    updateDocumentMetadata(difyDocumentId: string, metadata: {
        workspace_id?: string;
        document_chunk_id?: string;
        project_id?: string;
        document_id?: string;
        page?: string;
    }): Promise<DifyUpdateMetadataResponse>;
    createDocumentFromText({ name, text, indexingTechnique, processMode, }: {
        name: string;
        text: string;
        indexingTechnique?: 'high_quality' | 'economy';
        processMode?: 'automatic' | 'custom';
    }): Promise<DifyCreateDocumentResponse>;
    createMetadataOperationData(difyDocumentId: string, metadata: {
        workspace_id?: string;
        document_chunk_id?: string;
        project_id?: string;
        document_id?: string;
        page?: string;
    }): Array<{
        document_id: string;
        metadata_list: Array<{
            id: string;
            name: string;
            type: string;
            value: string;
        }>;
    }>;
}
