
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useForm } from 'react-hook-form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface CompanyInformationProps {
  onComplete: () => void;
}

type FormValues = {
  companyName: string;
  employees: string;
  industry: string;
  countries: string;
  website: string;
};

// Options for dropdowns
const employeeOptions = [
  { value: "1-10", label: "1-10 employees" },
  { value: "11-50", label: "11-50 employees" },
  { value: "51-200", label: "51-200 employees" },
  { value: "201-500", label: "201-500 employees" },
  { value: "501-1000", label: "501-1,000 employees" },
  { value: "1001-5000", label: "1,001-5,000 employees" },
  { value: "5001-10000", label: "5,001-10,000 employees" },
  { value: "10000+", label: "10,000+ employees" }
];

const industryOptions = [
  { value: "agriculture", label: "Agriculture" },
  { value: "automotive", label: "Automotive" },
  { value: "chemicals", label: "Chemicals" },
  { value: "construction", label: "Construction" },
  { value: "consumer_goods", label: "Consumer Goods" },
  { value: "education", label: "Education" },
  { value: "energy", label: "Energy & Utilities" },
  { value: "financial", label: "Financial Services" },
  { value: "food", label: "Food & Beverage" },
  { value: "healthcare", label: "Healthcare" },
  { value: "hospitality", label: "Hospitality & Tourism" },
  { value: "it", label: "Information Technology" },
  { value: "manufacturing", label: "Manufacturing" },
  { value: "media", label: "Media & Entertainment" },
  { value: "mining", label: "Mining & Metals" },
  { value: "pharmaceuticals", label: "Pharmaceuticals" },
  { value: "real_estate", label: "Real Estate" },
  { value: "retail", label: "Retail" },
  { value: "telecommunications", label: "Telecommunications" },
  { value: "transportation", label: "Transportation & Logistics" },
  { value: "other", label: "Other" }
];

export const CompanyInformation: React.FC<CompanyInformationProps> = ({ onComplete }) => {
  const form = useForm<FormValues>({
    defaultValues: {
      companyName: '',
      employees: '',
      industry: '',
      countries: '',
      website: ''
    }
  });
  
  // Monitor form completeness and call onComplete when all fields are filled
  useEffect(() => {
    const subscription = form.watch((data) => {
      if (data.companyName && 
          data.employees && 
          data.industry && 
          data.countries && 
          data.website) {
        onComplete();
      }
    });
    
    return () => subscription.unsubscribe();
  }, [form, onComplete]);
  
  return (
    <div className="flex flex-col items-center">
      <p className="text-lg text-gray-600 mb-10 text-center max-w-2xl">
        Provide details about your company to help tailor the gap analysis and improvement recommendations.
      </p>
      
      <div className="w-full max-w-2xl">
        <Form {...form}>
          <form className="space-y-6" onSubmit={(e) => e.preventDefault()}>
            <FormField
              control={form.control}
              name="companyName"
              rules={{ required: "Company name is required" }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-glacier-darkBlue">Company Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter your company name" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="employees"
                rules={{ required: "Number of employees is required" }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-glacier-darkBlue">Number of Employees</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select company size" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {employeeOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="industry"
                rules={{ required: "Industry is required" }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-glacier-darkBlue">Primary Industry</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select industry" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {industryOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
            </div>
            
            <FormField
              control={form.control}
              name="countries"
              rules={{ required: "Countries of operation are required" }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-glacier-darkBlue">Countries of Operation</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g. United States, Germany, China" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="website"
              rules={{ 
                required: "Website URL is required",
                pattern: {
                value: /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/,
                  message: "Please enter a valid website URL"
                }
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-glacier-darkBlue">Website URL</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="https://www.example.com" 
                      type="url"
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Enter your company's official website
                  </FormDescription>
                </FormItem>
              )}
            />
          </form>
        </Form>
      </div>
    </div>
  );
};
