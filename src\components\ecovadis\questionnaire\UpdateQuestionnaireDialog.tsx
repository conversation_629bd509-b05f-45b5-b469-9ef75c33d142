
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { FileUp, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { DragDropZone } from '@/components/ui/documents/DragDropZone';
import { FileList } from '@/components/ui/documents/FileList';
import { updateEcovadisQuestionnaire } from '@/api/ecovadis/ecovadis.api';

interface UpdateQuestionnaireDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId?: string;
}

export const UpdateQuestionnaireDialog: React.FC<UpdateQuestionnaireDialogProps> = ({
  open,
  onOpenChange,
  projectId
}) => {
  const [updateFile, setUpdateFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  
  const handleFileSelect = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xlsx,.xls';
    input.onchange = (e: any) => {
      const file = e.target.files[0];
      if (file && (file.name.endsWith('.xlsx') || file.name.endsWith('.xls'))) {
        setUpdateFile(file);
      } else {
        toast.error("Invalid file format", {
          description: "Please upload an Excel file (.xlsx or .xls)"
        });
      }
    };
    input.click();
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        setUpdateFile(file);
      } else {
        toast.error("Invalid file format", {
          description: "Please upload an Excel file (.xlsx or .xls)"
        });
      }
    }
  };

  const handleDeleteFile = () => {
    setUpdateFile(null);
  };
  
  const handleUpdateQuestionnaire = async () => {
    if (!updateFile || !projectId) {
      toast.error("Unable to update questionnaire", {
        description: !updateFile 
          ? "Please select an Excel file to update your questionnaire."
          : "Project ID is missing. Please try again."
      });
      return;
    }

    setIsUploading(true);

    try {
      // Call our API method to update the questionnaire
      await updateEcovadisQuestionnaire(projectId, updateFile);
      
      setIsUploading(false);
      onOpenChange(false);
      setUpdateFile(null);
      
      toast.success("Questionnaire updated successfully", {
        description: "Your questionnaire has been updated with the latest version."
      });
    } catch (err) {
      console.error('Error updating questionnaire:', err);
      toast.error("Update failed", {
        description: "There was an error updating your questionnaire. Please try again."
      });
      setIsUploading(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl">Update Questionnaire</DialogTitle>
          <DialogDescription className="text-glacier-darkBlue/80 text-base pt-2">
            Upload a newer version of your Ecovadis questionnaire. This will:
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-3">
          <ul className="space-y-2 text-sm">
            <li className="flex items-start gap-2">
              <div className="bg-glacier-mint/20 p-1 rounded-full mt-0.5">
                <FileUp className="h-3.5 w-3.5 text-glacier-darkBlue" />
              </div>
              <span>Keep all existing questions and your current answers</span>
            </li>
            <li className="flex items-start gap-2">
              <div className="bg-glacier-mint/20 p-1 rounded-full mt-0.5">
                <FileUp className="h-3.5 w-3.5 text-glacier-darkBlue" />
              </div>
              <span>Add any new questions from the updated questionnaire</span>
            </li>
            <li className="flex items-start gap-2">
              <div className="bg-glacier-mint/20 p-1 rounded-full mt-0.5">
                <FileUp className="h-3.5 w-3.5 text-glacier-darkBlue" />
              </div>
              <span>Remove questions that are no longer in the new version</span>
            </li>
          </ul>
          
          {!updateFile ? (
            <DragDropZone
              isDragging={isDragging}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onFileSelect={handleFileSelect}
            />
          ) : (
            <FileList 
              files={[updateFile]} 
              onDeleteClick={handleDeleteFile}
            />
          )}
          
          <div className="text-sm flex items-center gap-2 text-amber-600 bg-amber-50 p-2.5 rounded-lg">
            <AlertCircle className="h-4 w-4" />
            <span>This action cannot be undone. Make sure to have a backup of your questionnaire.</span>
          </div>
        </div>
        
        <DialogFooter className="gap-3 sm:gap-0">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            className="mt-0"
          >
            Cancel
          </Button>
          <Button 
            disabled={!updateFile || isUploading} 
            onClick={handleUpdateQuestionnaire}
            className="bg-glacier-darkBlue text-white"
          >
            {isUploading ? "Updating..." : "Update Questionnaire"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
