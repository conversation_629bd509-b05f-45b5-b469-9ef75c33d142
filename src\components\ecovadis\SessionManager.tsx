
import { useEffect } from "react";
import { EcovadisQuestion } from "@/types/ecovadis";

interface SessionManagerProps {
  id: string | undefined;
  isAutoAnswering: boolean;
  setIsAutoAnswering: (value: boolean) => void;
  autoAnswerProgress: number;
  setAutoAnswerProgress: (value: number) => void;
  autoAnswerStage: string;
  setAutoAnswerStage: (value: string) => void;
  question: EcovadisQuestion | null;
  handleGapAnalysis: () => void;
}

export const SessionManager = ({
  id,
  isAutoAnswering,
  setIsAutoAnswering,
  autoAnswerProgress,
  setAutoAnswerProgress,
  autoAnswerStage,
  setAutoAnswerStage,
  question,
  handleGapAnalysis
}: SessionManagerProps) => {
  useEffect(() => {
    const autoAnswering = sessionStorage.getItem('autoAnswering');
    const storedQuestionId = sessionStorage.getItem('questionId');
    if (autoAnswering === 'true' && storedQuestionId === id) {
      setIsAutoAnswering(true);
      const progress = sessionStorage.getItem('autoAnswerProgress');
      const stage = sessionStorage.getItem('autoAnswerStage');
      if (progress) setAutoAnswerProgress(parseInt(progress));
      if (stage) setAutoAnswerStage(stage);
      if (progress === '100') {
        setTimeout(() => {
          setIsAutoAnswering(false);
          sessionStorage.removeItem('autoAnswering');
          sessionStorage.removeItem('autoAnswerProgress');
          sessionStorage.removeItem('autoAnswerStage');
          sessionStorage.removeItem('questionId');
          
          // No longer clear or modify document comments
          // This ensures comments are only updated through direct user input
        }, 1500);
      }
    }
    
    const gapAnalysisRunning = sessionStorage.getItem('gapAnalysisRunning');
    const gapQuestionId = sessionStorage.getItem('gapQuestionId');
    if (gapAnalysisRunning === 'true' && gapQuestionId === id) {
      const gapProgress = sessionStorage.getItem('gapAnalysisProgress');
      if (gapProgress === '100') {
        handleGapAnalysis();
      }
    }
  }, [id, setIsAutoAnswering, setAutoAnswerProgress, setAutoAnswerStage, handleGapAnalysis, question]);

  return null; // This is a non-UI component that just manages session state
};
