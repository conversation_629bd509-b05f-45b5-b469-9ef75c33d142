import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1729696992733 implements MigrationInterface {
  name = 'SchemaUpdate1729696992733';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ADD "footnotes" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ADD "footnotesAR" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_disclosure_requirement" ADD "drDescription" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_disclosure_requirement" ADD "drObjective" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_disclosure_requirement" ADD "lawText" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_disclosure_requirement" ADD "lawTextAR" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ALTER COLUMN "relatedAR" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ALTER COLUMN "lawText" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ALTER COLUMN "lawTextAR" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_document_chunk" DROP CONSTRAINT "FK_00a077be01d7bfaf0bcd5f7bbfb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_document_chunk" ALTER COLUMN "datapointRequestId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_document_chunk" ADD CONSTRAINT "FK_00a077be01d7bfaf0bcd5f7bbfb" FOREIGN KEY ("datapointRequestId") REFERENCES "datapoint_request"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_document_chunk" DROP CONSTRAINT "FK_00a077be01d7bfaf0bcd5f7bbfb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_document_chunk" ALTER COLUMN "datapointRequestId" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_document_chunk" ADD CONSTRAINT "FK_00a077be01d7bfaf0bcd5f7bbfb" FOREIGN KEY ("datapointRequestId") REFERENCES "datapoint_request"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ALTER COLUMN "lawTextAR" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ALTER COLUMN "lawText" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ALTER COLUMN "relatedAR" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_disclosure_requirement" DROP COLUMN "lawTextAR"`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_disclosure_requirement" DROP COLUMN "lawText"`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_disclosure_requirement" DROP COLUMN "drObjective"`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_disclosure_requirement" DROP COLUMN "drDescription"`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" DROP COLUMN "footnotesAR"`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" DROP COLUMN "footnotes"`,
    );
  }
}
