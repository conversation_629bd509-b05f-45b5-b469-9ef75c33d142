
import React from 'react';

interface AutoFillProgressProps {
  questionsCompleted: number;
  totalQuestions: number;
  currentQuestionName: string;
  stage: string;
  percentage: number;
}

export const AutoFillProgress: React.FC<AutoFillProgressProps> = ({
  questionsCompleted,
  totalQuestions,
  currentQuestionName,
  stage,
  percentage
}) => {
  return (
    <div className="fixed inset-0 bg-gray-800/60 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-lg shadow-xl">
        <h3 className="text-lg font-semibold text-glacier-darkBlue mb-4">
          Auto-filling Questionnaire
        </h3>
        
        <div className="mb-4">
          <div className="flex justify-between text-sm mb-1">
            <span className="text-gray-600">Processing question {questionsCompleted + 1} of {totalQuestions}</span>
            <span className="font-medium">{Math.round((questionsCompleted / totalQuestions) * 100)}%</span>
          </div>
          <div className="w-full h-2 bg-gray-100 rounded-full">
            <div 
              className="h-full bg-glacier-mint rounded-full transition-all duration-300" 
              style={{ width: `${(questionsCompleted / totalQuestions) * 100}%` }}
            ></div>
          </div>
        </div>
        
        <div className="mb-6">
          <h4 className="font-medium text-sm text-glacier-darkBlue mb-2">
            Current Question:
          </h4>
          <p className="text-sm text-gray-600 mb-4 border-l-2 border-glacier-mint pl-3 py-1">
            {currentQuestionName || "Initializing..."}
          </p>
          
          <h4 className="font-medium text-sm text-glacier-darkBlue mb-2">
            Current Stage:
          </h4>
          <div className="text-sm text-gray-600">
            <div className="mb-2">{stage}</div>
            <div className="w-full h-1.5 bg-gray-100 rounded-full">
              <div 
                className="h-full bg-glacier-mint/70 rounded-full transition-all duration-300" 
                style={{ width: `${percentage}%` }}
              ></div>
            </div>
          </div>
        </div>
        
        <p className="text-xs text-center text-gray-500">
          Please don't close this window. Auto-fill is processing your questions.
        </p>
      </div>
    </div>
  );
};
