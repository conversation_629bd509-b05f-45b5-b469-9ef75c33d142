import { ConfigService } from '@nestjs/config';
import { ChatCompletionMessageParam } from 'openai/resources';
import { ChatGptService } from '../../llm/chat-gpt.service';
import { CohereService } from '../../llm/cohere.service';
export interface EcoVadisFinalResponse {
    answers: {
        answer_option: {
            id: string;
            answer_name: string;
        };
        document_chunks: {
            document_chunk_id: string;
            comment: string;
        }[];
    }[];
}
export interface EcoVadisResponse {
    answer: EcoVadisFinalResponse;
    evidence_sources: Array<{
        content: string;
        relevance_score: number;
    }>;
    confidence_level: 'high' | 'medium' | 'low';
    conversationHistory: ChatCompletionMessageParam[];
    saveResults?: any[];
}
export declare class EnhancedEcoVadisAnswerAgentService {
    private configService;
    private chatGptService;
    private cohereService;
    private readonly logger;
    private readonly openAiClient;
    private readonly geminiClient;
    private pineconeClient;
    private supabase;
    private readonly openAiResponseSchema;
    constructor(configService: ConfigService, chatGptService: ChatGptService, cohereService: CohereService);
    private initializePinecone;
    private generateSystemPrompt;
    private readonly tools;
    answerEcoVadisQuestion({ projectId, questionId, }: {
        projectId: string;
        questionId: string;
    }): Promise<EcoVadisResponse>;
    private chatWithModel;
    private chatWithModelFinalResponse;
    private convertMessagesToGeminiPrompt;
    private handleToolCallsGeneric;
    private answerEcoVadisQuestionGeneric;
    answerEcoVadisQuestionWithOpenAI({ projectId, questionId, }: {
        projectId: string;
        questionId: string;
    }): Promise<EcoVadisResponse>;
    answerEcoVadisQuestionWithGemini({ projectId, questionId, }: {
        projectId: string;
        questionId: string;
    }): Promise<EcoVadisResponse>;
    private searchVectorStoreMultiple;
    private performBalancedPerQueryReranking;
    private deduplicateSearchResults;
    private hashContent;
    private searchKeywords;
    private searchVectorStore;
    private getDocumentPages;
    private parseAgentResponse;
    private getWorkspaceIdFromProject;
    private fetchEcoVadisData;
    private formatDataForPrompt;
    private saveAiAnswerResults;
    private parseAiAnswerResponse;
}
