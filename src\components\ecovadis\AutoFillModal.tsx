
import { useState } from 'react';
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Bot, FileText } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useUserRole } from '@/hooks/useUserRole';
import { USER_ROLE } from '@/constants/workspaceConstants';

interface AutoFillModalProps {
  isOpen: boolean;
  onClose: () => void;
  onStart: () => void;
}

export function AutoFillModal({
  isOpen,
  onClose,
  onStart,
}: AutoFillModalProps) {
  const { hasRole } = useUserRole();
  const isSuperAdmin = hasRole([USER_ROLE.SuperAdmin]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-glacier-darkBlue">
            Auto-Fill with Glacier AI ✨
          </DialogTitle>
          <DialogDescription className="text-gray-600 mt-2">
            <span>Glacier AI will analyze your documents and automatically fill out the entire questionnaire, run gap analysis, and calculate your score.</span>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-3 py-2">
          <div className="bg-glacier-mint/10 border border-glacier-mint/20 rounded-lg p-4">
            <h4 className="font-semibold text-glacier-darkBlue mb-2">What will happen:</h4>
            <ul className="text-sm text-gray-600 space-y-2">
              <li className="flex items-start gap-2">
                <span className="text-glacier-mint">✓</span> 
                <span>Screen through your entire evidence document base</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-glacier-mint">✓</span> 
                <span>Answer all questions with appropriate evidence</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-glacier-mint">✓</span> 
                <span>Run gap analysis for each question</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-glacier-mint">✓</span> 
                <span>Calculate your estimated Ecovadis score</span>
              </li>
            </ul>
          </div>
          
          {!isSuperAdmin && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 className="font-semibold text-red-600 mb-1">Permission Restricted</h4>
              <p className="text-sm text-gray-600">
                Only Super Admin users can use the Auto-Fill feature. Please contact your administrator.
              </p>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-end gap-2 pt-2">
          <Button 
            variant="outline" 
            onClick={onClose} 
            className="border-gray-300"
          >
            Cancel
          </Button>
          <Button 
            onClick={onStart} 
            className="bg-glacier-darkBlue hover:bg-glacier-darkBlue/90 text-white"
            disabled={!isSuperAdmin}
          >
            Start Auto-Fill
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
