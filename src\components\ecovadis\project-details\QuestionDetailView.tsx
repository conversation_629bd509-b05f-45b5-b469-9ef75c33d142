
import React from 'react';
import { EcovadisQuestion, GapItem } from '@/types/ecovadis';
import { QuestionHeader } from './question-sections/QuestionHeader';
import { QuestionText } from './question-sections/QuestionText';
import { QuestionTabs } from './question-sections/QuestionTabs';
import { useEcovadisQuestionMutations } from '@/hooks/useEcovadisQuestion';
import { DocumentUpload } from '@/types/project';
import { QUESTION_STATUS } from '@/constants/projectConstants';

interface QuestionDetailViewProps {
  selectedQuestion: EcovadisQuestion;
  gaps: GapItem[];
  handleMarkGapComplete: (gapId: string, isComplete: boolean) => void;
  handleUpdateOptions: (updatedOptions: any[]) => void;
  setQuestions: React.Dispatch<React.SetStateAction<EcovadisQuestion[]>>;
  documents?: DocumentUpload[];
}

export const QuestionDetailView: React.FC<QuestionDetailViewProps> = ({
  selectedQuestion,
  gaps,
  handleMarkGapComplete,
  handleUpdateOptions,
  setQuestions,
  documents = [],
}) => {
  
  const { markQuestionComplete } = useEcovadisQuestionMutations();
  
  if (!selectedQuestion) return null;

  // Map backend fields to UI fields if needed
  const adaptedQuestion = {
    ...selectedQuestion,
    text: selectedQuestion.question,
    code: selectedQuestion.questionCode,
    name: selectedQuestion.questionName,
  };

  // Handle mark complete/incomplete
  const onHandleMarkQuestionComplete = () => {
    const isCurrentlyComplete = selectedQuestion.status?.toLowerCase() === QUESTION_STATUS.COMPLETE;
    markQuestionComplete.mutate({
      questionId: selectedQuestion.projectQuestionId,
      isComplete: !isCurrentlyComplete
    });
  };

  return (
    <div className="space-y-6">
      <QuestionHeader 
        selectedQuestion={adaptedQuestion}
        handleMarkQuestionComplete={onHandleMarkQuestionComplete}
      />
      
      <QuestionText text={adaptedQuestion.text} />

      <QuestionTabs
        selectedQuestion={adaptedQuestion}
        gaps={gaps}
        handleMarkGapComplete={handleMarkGapComplete}
        handleUpdateOptions={handleUpdateOptions}
        setQuestions={setQuestions}
        documents={documents}
      />
    </div>
  );
};
