{"version": 3, "file": "supabase.service.js", "sourceRoot": "", "sources": ["../../../src/auth/supabase/supabase.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,2CAA+C;AAC/C,uDAAqE;AACrE,sFAAgE;AAIzD,IAAM,eAAe,GAArB,MAAM,eAAe;IAG1B,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAC9C,IAAI,CAAC,QAAQ,GAAG,IAAA,0BAAY,EAC1B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,kBAAkB,CAAC,EAClD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,sBAAsB,CAAC,CACvD,CAAC;IACJ,CAAC;IAED,SAAS;QACP,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,EAAc;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ;aACxC,IAAI,CAAC,MAAM,CAAC;aACZ,MAAM,CACL;;;OAGD,CACA;aACA,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC;aACjB,MAAM,EAAE,CAAC;QAEZ,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,IAAwB,CAAC;IAClC,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAc;QAC3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ;aACxC,IAAI,CAAC,MAAM,CAAC;aACZ,MAAM,CACL;;;;;;OAMD,CACA;aACA,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;aACZ,MAAM,EAAE,CAAC;QAEZ,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,IAAwB,CAAC;IAClC,CAAC;IAED,KAAK,CAAC,WAAW,CACf,MAAc,EACd,WAAmB;QAEnB,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ;iBACxC,IAAI,CAAC,gBAAgB,CAAC;iBACtB,MAAM,CAAC,MAAM,CAAC;iBACd,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;iBACpB,EAAE,CAAC,aAAa,EAAE,WAAW,CAAC;iBAC9B,MAAM,EAAE,CAAC;YAEZ,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;gBACnB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAa;QAC7B,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAEhE,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ;iBACvD,IAAI,CAAC,MAAM,CAAC;iBACZ,MAAM,CACL;;;SAGD,CACA;iBACA,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAEvB,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;gBAC9C,OAAO;YACT,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,SAAS,aAAa,CAAC,MAAM,mBAAmB,CAAC,CAAC;YAE9D,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,MAAM,MAAM,GAAG,EAAE,CAAC;YAGlB,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;gBACjC,IAAI,CAAC;oBAEH,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,GACxC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;wBACxC,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,QAAQ,EAAE,UAAU;wBACpB,aAAa,EAAE,IAAI;wBACnB,aAAa,EAAE;4BACb,IAAI,EAAE,IAAI,CAAC,IAAI;4BACf,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;gCAC3C,WAAW,EAAE,EAAE,CAAC,WAAW;gCAC3B,IAAI,EAAE,EAAE,CAAC,IAAI;6BACd,CAAC,CAAC;yBACJ;qBACF,CAAC,CAAC;oBAEL,IAAI,SAAS;wBACX,MAAM,IAAI,KAAK,CACb,kBAAkB,IAAI,CAAC,KAAK,KAAK,SAAS,CAAC,OAAO,EAAE,CACrD,CAAC;oBAGJ,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ;yBAC/C,IAAI,CAAC,MAAM,CAAC;yBACZ,MAAM,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;yBACrC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBAErB,IAAI,WAAW;wBACb,MAAM,IAAI,KAAK,CACb,8BAA8B,IAAI,CAAC,KAAK,KAAK,WAAW,CAAC,OAAO,EAAE,CACnE,CAAC;oBAEJ,YAAY,EAAE,CAAC;oBACf,OAAO,CAAC,GAAG,CAAC,8BAA8B,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;gBAC1D,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,IAAI,CAAC,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC;oBAC5D,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC3D,CAAC;YACH,CAAC;YAED,OAAO,CAAC,GAAG,CACT,gCAAgC,YAAY,aAAa,MAAM,CAAC,MAAM,EAAE,CACzE,CAAC;YAEF,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtB,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,EACf,KAAK,EACL,IAAI,EACJ,QAAQ,EACR,OAAO,GAMR;QACC,IAAI,CAAC;YACH,IAAI,WAAW,GAAG,OAAO,EAAE,WAAW,CAAC;YACvC,MAAM,IAAI,GAAG,OAAO,EAAE,IAAI,IAAI,4BAAI,CAAC,WAAW,CAAC;YAG/C,IAAI,CAAC,WAAW,IAAI,OAAO,EAAE,aAAa,EAAE,CAAC;gBAC3C,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ;qBACnE,IAAI,CAAC,WAAW,CAAC;qBACjB,MAAM,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC;qBACvC,MAAM,CAAC,IAAI,CAAC;qBACZ,MAAM,EAAE,CAAC;gBAEZ,IAAI,cAAc,EAAE,CAAC;oBACnB,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,cAAc,CAAC,CAAC;oBAC3D,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,WAAW,GAAG,SAAS,CAAC,EAAE,CAAC;gBAC3B,OAAO,CAAC,GAAG,CAAC,8BAA8B,WAAW,EAAE,CAAC,CAAC;YAC3D,CAAC;YAED,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ;iBAC7D,IAAI,CAAC,MAAM,CAAC;iBACZ,MAAM,CAAC;gBACN,KAAK;gBACL,IAAI;aACL,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC;iBACX,MAAM,EAAE,CAAC;YAEZ,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,SAAS,CAAC,CAAC;gBACxD,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,MAAM,YAAY,GAGd,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;YAElD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,GACxC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;gBACxC,KAAK;gBACL,QAAQ;gBACR,aAAa,EAAE,IAAI;gBACnB,aAAa,EAAE,YAAY;aAC5B,CAAC,CAAC;YAEL,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,SAAS,CAAC,CAAC;gBAEtD,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ;qBACjD,IAAI,CAAC,MAAM,CAAC;qBACZ,MAAM,EAAE;qBACR,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACzB,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,MAAM,IAAI,CAAC,QAAQ;iBAChB,IAAI,CAAC,MAAM,CAAC;iBACZ,MAAM,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;iBACrC,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;YAGzB,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ;qBAChD,IAAI,CAAC,gBAAgB,CAAC;qBACtB,MAAM,CAAC;oBACN,MAAM,EAAE,QAAQ,CAAC,EAAE;oBACnB,WAAW;oBACX,IAAI;iBACL,CAAC,CAAC;gBAEL,IAAI,YAAY,EAAE,CAAC;oBACjB,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,YAAY,CAAC,CAAC;gBAGlE,CAAC;YACH,CAAC;YAGD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC9C,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;gBAC7C,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AArRY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;qCAIwB,sBAAa;GAHrC,eAAe,CAqR3B"}