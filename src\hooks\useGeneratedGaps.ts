import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getGeneratedGaps, reviewGeneratedGap } from '@/api/generated-gaps/generated-gaps.api';
import { ReviewGapRequest } from '@/types/generated-gaps';
import { toast } from '@/components/ui/use-toast';
import { useParams } from 'react-router-dom';

/**
 * Hook to fetch generated gaps for Super Admin review
 */
export const useGeneratedGaps = (params?: {
  status?: string;
  projectId?: string;
  questionId?: string;
}) => {
  return useQuery({
    queryKey: ['generated-gaps', params],
    queryFn: () => getGeneratedGaps(params),
    staleTime: 30000, // 30 seconds
  });
};

/**
 * Hook to fetch generated gaps for a specific question
 */
export const useGeneratedGapsForQuestion = (questionId?: string) => {
  return useQuery({
    queryKey: ['generatedGaps', questionId],
    queryFn: () => getGeneratedGaps({ questionId }),
    enabled: !!questionId,
    staleTime: 30000, // 30 seconds
  });
};

/**
 * Hook to review generated gaps
 */
export const useReviewGeneratedGap = (questionId: string) => {
  const queryClient = useQueryClient();
  const { id: projectId } = useParams<{ id: string }>();

  return useMutation({
    mutationFn: reviewGeneratedGap,
    onSuccess: (data, variables) => {
      // Invalidate generated gaps queries to refresh the UI
      queryClient.invalidateQueries({ queryKey: ['generatedGaps', questionId] });
      
      // Invalidate workspace gaps to show newly approved gaps
      queryClient.invalidateQueries({ queryKey: ['workspaceGaps', questionId] });

      // If gap was approved, also invalidate the project data to update scores
      if (variables.action === 'approve') {
        queryClient.invalidateQueries({ queryKey: ['ecovadisProject', projectId] });
      }
      
      // Show success toast
      toast({
        title: "Gap Review Complete",
        description: data.message,
        variant: "success",
      });
    },
    onError: (error) => {
      console.error("Gap review failed:", error);
      
      // Show error toast
      toast({
        title: "Gap Review Failed",
        description: "There was an error reviewing the generated gap. Please try again.",
        variant: "destructive",
      });
    }
  });
};
