"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseAuthModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const supabase_service_1 = require("./supabase.service");
const supabase_auth_guard_1 = require("./supabase.auth.guard");
const supabase_controller_1 = require("./supabase.controller");
const questionnaire_service_1 = require("./questionnaire.service");
const answer_linking_service_1 = require("./answer-linking.service");
const llm_module_1 = require("../../llm/llm.module");
let SupabaseAuthModule = class SupabaseAuthModule {
};
exports.SupabaseAuthModule = SupabaseAuthModule;
exports.SupabaseAuthModule = SupabaseAuthModule = __decorate([
    (0, common_1.Module)({
        imports: [config_1.ConfigModule, llm_module_1.LlmModule],
        providers: [
            supabase_service_1.SupabaseService,
            questionnaire_service_1.QuestionnaireService,
            supabase_auth_guard_1.AuthGuard,
            answer_linking_service_1.EnhancedEcoVadisAnswerAgentService,
        ],
        controllers: [supabase_controller_1.SupabaseController],
        exports: [supabase_service_1.SupabaseService, supabase_auth_guard_1.AuthGuard, answer_linking_service_1.EnhancedEcoVadisAnswerAgentService],
    })
], SupabaseAuthModule);
//# sourceMappingURL=supabase.module.js.map