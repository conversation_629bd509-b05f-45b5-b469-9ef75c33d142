import { DocumentChunk } from '../../document/entities/document-chunk.entity';
import { Project } from '../../project/entities/project.entity';
import { User } from '../../users/entities/user.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  PrimaryColumn,
} from 'typeorm';

// Enums
export enum ProjectType {
  CSRD = 'CSRD',
  EcoVadis = 'EcoVadis',
}

export enum EcoVadisIndicator {
  POLICIES = 'POLICIES',
  ENDORSEMENTS = 'ENDORSEMENTS',
  MEASURES = 'MEASURES',
  CERTIFICATIONS = 'CERTIFICATIONS',
  COVERAGE = 'COVERAGE',
  REPORTING = 'REPORTING',
  WATCH_FINDINGS = 'WATCH_FINDINGS',
}

export enum ImpactScore {
  High = 'High',
  Medium = 'Medium',
  Low = 'Low',
}

export enum EcoVadisScoreLevel {
  Outstanding = 'Outstanding',
  Advanced = 'Advanced',
  Good = 'Good',
  Partial = 'Partial',
  Insufficient = 'Insufficient',
}

// Entities
@Entity('ecovadis_theme')
export class EcoVadisTheme {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column({ type: 'text' })
  description: string;

  @CreateDateColumn()
  createdAt: Date;

  @OneToMany(() => EcoVadisQuestion, (question) => question.theme)
  questions: EcoVadisQuestion[];

  @OneToMany(() => ProjectEcoVadisTheme, (projectTheme) => projectTheme.theme)
  projectThemes: ProjectEcoVadisTheme[];
}

@Entity('project_ecovadis_theme')
export class ProjectEcoVadisTheme {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  themeId: string;

  @Column({ nullable: true })
  projectId: string;

  @Column({
    type: 'enum',
    enum: ImpactScore,
  })
  impact: ImpactScore;

  @Column('jsonb', { nullable: true, default: '[]' })
  issues: Array<{ issueId: string; impact: ImpactScore }>;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => EcoVadisTheme, (theme) => theme.projectThemes)
  @JoinColumn({ name: 'themeId' })
  theme: EcoVadisTheme;

  @ManyToOne(() => Project)
  @JoinColumn({ name: 'projectId' })
  project: Project;
}

@Entity('ecovadis_question')
export class EcoVadisQuestion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  themeId: string;

  @Column()
  questionCode: string;

  @Column({
    type: 'enum',
    enum: EcoVadisIndicator,
  })
  indicator: EcoVadisIndicator;

  @Column()
  questionName: string;

  @Column()
  question: string;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => EcoVadisTheme, (theme) => theme.questions)
  @JoinColumn({ name: 'themeId' })
  theme: EcoVadisTheme;

  @OneToMany(() => EcoVadisAnswerOption, (option) => option.question)
  answerOptions: EcoVadisAnswerOption[];

  @OneToMany(
    () => ProjectEcoVadisQuestion,
    (projectQuestion) => projectQuestion.question
  )
  projectQuestions: ProjectEcoVadisQuestion[];

  @OneToMany(() => ProjectEcoVadisGaps, (gap) => gap.question)
  gaps: ProjectEcoVadisGaps[];
}

@Entity('project_ecovadis_question_score')
export class ProjectEcoVadisQuestionScore {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  questionId: string;

  @Column()
  score: number;

  @Column({
    type: 'enum',
    enum: EcoVadisScoreLevel,
  })
  level: EcoVadisScoreLevel;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'text', nullable: true })
  breakdown: string;

  @Column({ type: 'text', nullable: true })
  conclusion: string;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => EcoVadisQuestion)
  @JoinColumn({ name: 'questionId' })
  question: EcoVadisQuestion;

  @OneToMany(
    () => ProjectEcoVadisQuestionScoreHistory,
    (history) => history.score
  )
  history: ProjectEcoVadisQuestionScoreHistory[];
}

@Entity('project_ecovadis_question_score_history')
export class ProjectEcoVadisQuestionScoreHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  scoreId: string;

  @Column()
  score: number;

  @Column({
    type: 'enum',
    enum: EcoVadisScoreLevel,
  })
  level: EcoVadisScoreLevel;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'text', nullable: true })
  breakdown: string;

  @Column({ type: 'text', nullable: true })
  conclusion: string;

  @Column()
  version: number;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => ProjectEcoVadisQuestionScore, (score) => score.history)
  @JoinColumn({ name: 'scoreId' })
  currentScore: ProjectEcoVadisQuestionScore;
}

@Entity('project_ecovadis_question')
export class ProjectEcoVadisQuestion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  questionId: string;

  @Column({ nullable: true })
  projectId: string;

  @Column({
    type: 'enum',
    enum: ImpactScore,
  })
  impact: ImpactScore;

  @Column()
  status: string;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => EcoVadisQuestion, (question) => question.projectQuestions)
  @JoinColumn({ name: 'questionId' })
  question: EcoVadisQuestion;

  @ManyToOne(() => Project)
  @JoinColumn({ name: 'projectId' })
  project: Project;
}

@Entity('project_ecovadis_gaps')
export class ProjectEcoVadisGaps {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  questionId: string;

  @Column({ nullable: true })
  projectId: string;

  @Column({ type: 'json' })
  gaps: Record<string, any>;

  @Column('simple-array', { nullable: true })
  documents: string[];

  @Column({ nullable: true })
  resolved: boolean;

  @Column({ nullable: true })
  assigneeId: string;

  @Column({ type: 'timestamp', nullable: true })
  deadline: Date;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => EcoVadisQuestion, (question) => question.gaps)
  @JoinColumn({ name: 'questionId' })
  question: EcoVadisQuestion;

  @ManyToOne(() => Project)
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'assigneeId' })
  assignee: User;
}

@Entity('ecovadis_answer_option')
export class EcoVadisAnswerOption {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  questionId: string;

  @Column()
  issueTitle: string;

  @Column({ type: 'text' })
  instructions: string;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => EcoVadisQuestion, (question) => question.answerOptions)
  @JoinColumn({ name: 'questionId' })
  question: EcoVadisQuestion;

  @OneToMany(() => ProjectEcoVadisAnswer, (answer) => answer.option)
  answers: ProjectEcoVadisAnswer[];
}

@Entity('project_ecovadis_answer')
export class ProjectEcoVadisAnswer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  projectId: string;

  @Column({ nullable: true })
  optionId: string;

  @Column()
  response: string;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => Project)
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @ManyToOne(() => EcoVadisAnswerOption, (option) => option.answers)
  @JoinColumn({ name: 'optionId' })
  option: EcoVadisAnswerOption;

  @OneToMany(
    () => ProjectEcoVadisLinkedDocumentChunks,
    (linkedChunks) => linkedChunks.answer
  )
  linkedChunks: ProjectEcoVadisLinkedDocumentChunks[];
}

@Entity('project_ecovadis_linked_document_chunks')
export class ProjectEcoVadisLinkedDocumentChunks {
  @PrimaryColumn()
  id: number;

  @Column({ nullable: true })
  answerId: string;

  @Column({ nullable: true })
  documentChunkId: string;

  @Column({ type: 'text', nullable: true })
  comment: string;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => ProjectEcoVadisAnswer, (answer) => answer.linkedChunks)
  @JoinColumn({ name: 'answerId' })
  answer: ProjectEcoVadisAnswer;

  @ManyToOne(() => DocumentChunk)
  @JoinColumn({ name: 'documentChunkId' })
  documentChunk: DocumentChunk;
}

@Entity('ecovadis_sustainability_issues')
export class EcoVadisSustainabilityIssue {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'text' })
  issue: string;

  @Column({ type: 'text' })
  definition: string;

  @Column({ type: 'text' })
  industryIssues: string;

  @CreateDateColumn()
  createdAt: Date;
}
