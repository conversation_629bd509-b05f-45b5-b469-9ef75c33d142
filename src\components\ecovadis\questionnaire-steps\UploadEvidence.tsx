import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FileText, Check, X, Trash } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { FileUpload } from '@/components/ui/file-upload';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface UploadEvidenceProps {
  onComplete: () => void;
}

export const UploadEvidence: React.FC<UploadEvidenceProps> = ({
  onComplete
}) => {
  const [files, setFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadingFiles, setUploadingFiles] = useState<{file: File, progress: number}[]>([]);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<number | null>(null);
  
  const handleFiles = (newFiles: File[]) => {
    const filesToUpload = newFiles.map(file => ({
      file,
      progress: 0
    }));
    
    setUploadingFiles(filesToUpload);
    setUploading(true);
    simulateUpload(filesToUpload);
  };
  
  const simulateUpload = (filesToUpload: {file: File, progress: number}[]) => {
    const intervals = filesToUpload.map((fileObj, index) => {
      return setInterval(() => {
        setUploadingFiles(prev => {
          const updated = [...prev];
          if (updated[index]) {
            if (updated[index].progress >= 100) {
              clearInterval(intervals[index]);
              
              const allDone = updated.every(f => f.progress >= 100);
              if (allDone) {
                setTimeout(() => {
                  setUploading(false);
                  setUploadingFiles([]);
                  setFiles(prev => [...prev, ...filesToUpload.map(f => f.file)]);
                  
                  if (files.length > 0 || filesToUpload.length > 0) {
                    onComplete();
                  }
                }, 500);
              }
              
              return updated;
            }
            
            updated[index] = {
              ...updated[index],
              progress: updated[index].progress + 10
            };
          }
          return updated;
        });
      }, 200 + (index * 50));
    });
    
    return intervals;
  };
  
  const cancelUpload = (index: number) => {
    setUploadingFiles(prev => prev.filter((_, i) => i !== index));
    
    if (uploadingFiles.length <= 1) {
      setUploading(false);
    }
  };
  
  const removeFile = (index: number) => {
    setFiles(prev => {
      const updated = [...prev];
      updated.splice(index, 1);
      
      if (updated.length === 0) {
        // Note: onComplete could be renamed to something like toggleComplete
        // and be called with false to mark as incomplete
      }
      
      return updated;
    });
  };
  
  const handleDeleteClick = (index: number) => {
    setFileToDelete(index);
    setDeleteConfirmOpen(true);
  };
  
  const handleDeleteConfirm = () => {
    if (fileToDelete !== null) {
      removeFile(fileToDelete);
      setDeleteConfirmOpen(false);
      setFileToDelete(null);
    }
  };
  
  const handleDeleteCancel = () => {
    setDeleteConfirmOpen(false);
    setFileToDelete(null);
  };
  
  return (
    <div className="flex flex-col items-center">      
      <p className="text-lg text-gray-600 mb-10 text-center max-w-2xl">
        Upload all potential documents that can serve as evidence for your Ecovadis rating. These will be automatically analyzed and linked to relevant questions.
      </p>
      
      <div className="w-full max-w-2xl mb-8">
        <FileUpload onChange={handleFiles} />
      </div>
      
      {uploading && uploadingFiles.length > 0 && (
        <div className="w-full max-w-2xl">
          <div className="mb-3">
            <h3 className="font-medium text-glacier-darkBlue">
              Uploading - {uploadingFiles.length} file{uploadingFiles.length !== 1 ? 's' : ''}
            </h3>
          </div>
          
          <div className="space-y-3 mb-6">
            {uploadingFiles.map((fileObj, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-lg p-3">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 text-glacier-darkBlue mr-2" />
                    <span className="text-sm font-medium truncate max-w-xs">{fileObj.file.name}</span>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-6 w-6 text-gray-500"
                    onClick={() => cancelUpload(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="flex items-center space-x-2">
                  <div className="flex-1">
                    <Progress value={fileObj.progress} className="h-2" />
                  </div>
                  <span className="text-xs text-gray-500">{fileObj.progress}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {files.length > 0 && (
        <div className="w-full max-w-2xl">
          <h3 className="font-medium text-glacier-darkBlue mb-3">
            Uploaded files ({files.length})
          </h3>
          <div className="space-y-2">
            {files.map((file, index) => (
              <div key={index} className="border border-green-200 bg-green-50 rounded-lg p-3 flex justify-between items-center">
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-glacier-darkBlue mr-3" />
                  <span className="text-sm truncate max-w-xs">{file.name}</span>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  onClick={() => handleDeleteClick(index)}
                >
                  <Trash className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
      
      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete File</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this file? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleDeleteCancel} className="border-gray-200">Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteConfirm}
              className="bg-red-500 text-white hover:bg-red-600 focus:ring-red-500"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
