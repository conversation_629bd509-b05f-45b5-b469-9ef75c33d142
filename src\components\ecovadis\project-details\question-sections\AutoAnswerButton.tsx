
import React from 'react';
import { AutoAnswerSection } from '@/components/ecovadis/auto-answer/AutoAnswerSection';
import { EcovadisQuestion } from '@/types/ecovadis';

interface AutoAnswerButtonProps {
  question: EcovadisQuestion;
  onUpdateQuestion: (updatedQuestion: EcovadisQuestion) => void;
}

export const AutoAnswerButton: React.FC<AutoAnswerButtonProps> = ({
  question,
  onUpdateQuestion
}) => {
  return (
    <div className="flex justify-end">
      <AutoAnswerSection
        question={question}
        onUpdateQuestion={onUpdateQuestion}
      />
    </div>
  );
};
