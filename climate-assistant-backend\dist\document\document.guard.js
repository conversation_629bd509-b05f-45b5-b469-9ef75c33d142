"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentChunkGuard = exports.DocumentGuard = void 0;
const common_1 = require("@nestjs/common");
const document_service_1 = require("./document.service");
let DocumentGuard = class DocumentGuard {
    constructor(documentService) {
        this.documentService = documentService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const documentId = request.params.id;
        const workspaceId = request.user.workspaceId;
        const document = await this.documentService.findDocumentById(documentId);
        if (!document) {
            throw new common_1.UnauthorizedException(`Document not found`);
        }
        if (document.workspaceId !== workspaceId) {
            throw new common_1.UnauthorizedException(`Document is not from this workspace`);
        }
        request.document = document;
        return true;
    }
};
exports.DocumentGuard = DocumentGuard;
exports.DocumentGuard = DocumentGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [document_service_1.DocumentService])
], DocumentGuard);
let DocumentChunkGuard = class DocumentChunkGuard {
    constructor(documentService) {
        this.documentService = documentService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const documentChunkId = request.params.id;
        const workspaceId = request.user.workspaceId;
        const documentChunk = await this.documentService.findDocumentChunkById(documentChunkId);
        if (!documentChunk) {
            throw new common_1.UnauthorizedException(`DocumentChunk not found`);
        }
        if (documentChunk.document.workspaceId !== workspaceId) {
            throw new common_1.UnauthorizedException(`DocumentCunk is not from this workspace`);
        }
        return true;
    }
};
exports.DocumentChunkGuard = DocumentChunkGuard;
exports.DocumentChunkGuard = DocumentChunkGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [document_service_1.DocumentService])
], DocumentChunkGuard);
//# sourceMappingURL=document.guard.js.map