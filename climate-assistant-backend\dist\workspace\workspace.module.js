"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkspaceModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const workspace_service_1 = require("./workspace.service");
const workspace_entity_1 = require("./entities/workspace.entity");
const document_chunk_entity_1 = require("../document/entities/document-chunk.entity");
const document_entity_1 = require("../document/entities/document.entity");
const user_workspace_entity_1 = require("../users/entities/user-workspace.entity");
const user_entity_1 = require("../users/entities/user.entity");
const token_entity_1 = require("../users/entities/token.entity");
const email_service_1 = require("../external/email.service");
const workspace_controller_1 = require("./workspace.controller");
const chat_gpt_service_1 = require("../llm/chat-gpt.service");
const version_history_entity_1 = require("./entities/version-history.entity");
const email_module_1 = require("../external/email.module");
const company_entity_1 = require("./entities/company.entity");
const supabase_module_1 = require("../auth/supabase/supabase.module");
let WorkspaceModule = class WorkspaceModule {
};
exports.WorkspaceModule = WorkspaceModule;
exports.WorkspaceModule = WorkspaceModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                workspace_entity_1.Workspace,
                document_chunk_entity_1.DocumentChunk,
                document_entity_1.Document,
                user_workspace_entity_1.UserWorkspace,
                company_entity_1.Company,
                user_entity_1.User,
                token_entity_1.Token,
                version_history_entity_1.VersionHistory,
            ]),
            email_module_1.EmailModule,
            supabase_module_1.SupabaseAuthModule,
        ],
        providers: [workspace_service_1.WorkspaceService, email_service_1.EmailService, chat_gpt_service_1.ChatGptService],
        exports: [workspace_service_1.WorkspaceService, typeorm_1.TypeOrmModule, email_service_1.EmailService],
        controllers: [workspace_controller_1.WorkspaceController],
    })
], WorkspaceModule);
//# sourceMappingURL=workspace.module.js.map