import { DatapointRequestData } from 'src/data-request/entities/data-request.dto';
import { DatapointRequestService } from 'src/datapoint/datapoint-request.service';
import { Queue } from 'bull';
export declare class DatapointDataRequestSharedService {
    private readonly datapointRequestService;
    private readonly datapointGenerationQueue;
    private readonly datapointReviewQueue;
    constructor(datapointRequestService: DatapointRequestService, datapointGenerationQueue: Queue, datapointReviewQueue: Queue);
    private readonly logger;
    addDatapointToGenerationQueue({ datapointRequest, userId, workspaceId, useExistingReportTextForReference, }: {
        datapointRequest: DatapointRequestData;
        userId: string;
        workspaceId: string;
        useExistingReportTextForReference: boolean;
    }): Promise<void>;
    addDatapointToReviewQueue({ datapointRequest, userId, workspaceId, }: {
        datapointRequest: DatapointRequestData;
        userId: string;
        workspaceId: string;
    }): Promise<void>;
}
