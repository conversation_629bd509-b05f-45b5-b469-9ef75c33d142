import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1736399617808 implements MigrationInterface {
  name = 'SchemaUpdate1736399617808';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."document_status_enum" ADD VALUE 'queued_for_extraction'`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."document_status_enum" ADD VALUE 'failed_extraction'`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."document_status_enum" ADD VALUE 'queued_for_linking'`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."document_status_enum" ADD VALUE 'failed_linking'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
