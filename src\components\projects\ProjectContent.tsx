
import React from 'react';
import { format } from 'date-fns';
import { Calendar, Languages, FileText, Settings } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Project } from '@/types/project';
import { EditProjectDialog } from './EditProjectDialog';

interface ProjectContentProps {
  project: Project;
  onProjectUpdate: (project: Project) => void;
}

export function ProjectContent({ project, onProjectUpdate }: ProjectContentProps) {
  const getLanguageName = (code: string) => {
    const languages = {
      en: 'English',
      de: 'German',
      fr: 'French',
      es: 'Spanish',
    };
    return languages[code as keyof typeof languages] || code;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Project Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Project Name</h3>
                <p className="text-base">{project.name}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Created</h3>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>{format(new Date(project.createdAt), "MMMM d, yyyy")}</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Reporting Year</h3>
                <div className="flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>{project.reportingYear}</span>
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Primary Content Language</h3>
                <div className="flex items-center">
                  <Languages className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>{getLanguageName(project.primaryContentLanguage)}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end gap-2">
          <EditProjectDialog 
            project={project}
            onUpdate={(updatedProject) => onProjectUpdate({...project, ...updatedProject})}
            trigger={
              <Button variant="outline" className="flex items-center">
                <Settings className="h-4 w-4 mr-2" />
                Edit Project
              </Button>
            }
          />
        </CardFooter>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Project Content</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">This project is currently empty. Use the buttons below to add content.</p>
          
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center text-left">
              <div className="flex items-center w-full">
                <div className="bg-blue-100 rounded-full p-2 mr-3">
                  <FileText className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-medium">Document Collection</h3>
                  <p className="text-sm text-muted-foreground">Upload and manage project documents</p>
                </div>
              </div>
            </Button>
            
            <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center text-left">
              <div className="flex items-center w-full">
                <div className="bg-green-100 rounded-full p-2 mr-3">
                  <FileText className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <h3 className="font-medium">Material Topics</h3>
                  <p className="text-sm text-muted-foreground">Define material topics for reporting</p>
                </div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
