import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  DatapointRequest,
  DatapointRequestStatus,
} from 'src/datapoint/entities/datapoint-request.entity';
import { DataRequestStatus } from 'src/data-request/entities/data-request.entity';
import { isTextPresentInHTML } from 'src/util/common-util';

export interface DatapointRequestStatusReviewData {
  id: string;
  content: string;
  esrsDatapoint: {
    id: string;
  };
  dataRequest: {
    id: string;
  };
  comments: {
    id: string;
    resolved: boolean;
  }[];
  datapointDocumentChunkMap: {
    id: string;
    documentChunk: {
      id: string;
      document: {
        id: string;
      };
    };
  }[];
}

@Injectable()
export class ProjectDatapointRequestService {
  constructor(
    @InjectRepository(DatapointRequest)
    private readonly datapointRequestRepository: Repository<DatapointRequest>,
  ) {}

  async findDatapointRequestCollectiveData(
    datapointRequestId: string,
  ): Promise<DatapointRequestStatusReviewData> {
    const datapointRequest = await this.datapointRequestRepository
      .createQueryBuilder('dpRequest')
      .select(['dpRequest.id', 'dpRequest.content'])
      .leftJoin('dpRequest.esrsDatapoint', 'esrsDatapoint')
      .addSelect(['esrsDatapoint.id'])
      .leftJoin('dpRequest.dataRequest', 'dataRequest')
      .addSelect(['dataRequest.id'])
      .leftJoin('dpRequest.comments', 'comments')
      .addSelect(['comments.id', 'comments.resolved'])
      .leftJoin(
        'dpRequest.datapointDocumentChunkMap',
        'datapointDocumentChunkMap',
      )
      .addSelect(['datapointDocumentChunkMap.id'])
      .leftJoin('datapointDocumentChunkMap.documentChunk', 'documentChunk')
      .addSelect(['documentChunk.id'])
      .leftJoin('documentChunk.document', 'document')
      .addSelect(['document.id'])
      .where('dpRequest.id = :id', { id: datapointRequestId })
      .getOne();

    if (!datapointRequest) {
      throw new NotFoundException(
        `Datapoint with ID ${datapointRequest} not found`,
      );
    }

    return datapointRequest as unknown as DatapointRequestStatusReviewData;
  }

  // This is slightly different from the DatapointRequestService to handle Not Answered status
  async datapointRequestStatusProcessor(
    datapointRequestId: string,
    optionalDatapoint: boolean,
  ): Promise<DatapointRequestStatus> {
    if (optionalDatapoint) {
      return DatapointRequestStatus.NotAnswered;
    }

    const datapointRequest =
      await this.findDatapointRequestCollectiveData(datapointRequestId);
    if (!datapointRequest) {
      throw new NotFoundException(`Datapoint Request not found`);
    }

    const {
      content,
      datapointDocumentChunkMap: datapointDocumentChunkMaps,
      comments,
    } = datapointRequest;

    // "No Data"
    // DP: Will be displayed red if there is no content and no document-chunks linked for the datapoint
    if (!isTextPresentInHTML(content)) {
      return DatapointRequestStatus.NoData;
    }

    // "Incomplete Data"
    // DP: Will be displayed if there is at least one unresolved comment and content is available
    const hasUnresolvedComments = comments.some((comment) => !comment.resolved);
    if (hasUnresolvedComments && content) {
      return DatapointRequestStatus.IncompleteData;
    }

    // "Complete Data"
    // DP: Will be displayed if there is saved content and no unresolved comments
    const noUnresolvedComments = comments.every((comment) => comment.resolved);
    if (content && noUnresolvedComments) {
      return DatapointRequestStatus.CompleteData;
    }

    // Default to No Data if no other conditions are met
    return DatapointRequestStatus.NoData;
  }

  dataRequestStatusProcessor({
    status,
    content,
    approvedBy,
    datapointRequests,
  }: {
    id: string;
    content: string;
    status: DataRequestStatus;
    approvedBy: string;
    datapointRequests: {
      id: string;
      status: DatapointRequestStatus;
      content: string;
      datapointDocumentChunkMap: { id: string }[];
    }[];
  }): DataRequestStatus {
    // "Approved Answer"
    // DR: Will be displayed, if the DR has the status "Approved Answer"
    if (status === DataRequestStatus.ApprovedAnswer) {
      return DataRequestStatus.ApprovedAnswer;
    }

    // "Not reported"
    // DR: Will be displayed, if all the datapoints inside the "Request for Data" are marked as "Not Reported" or the Status is "Not Reported"
    const validateDPForNotReported = datapointRequests.every(
      (datapoint) => datapoint.status === DatapointRequestStatus.NotAnswered,
    );

    if (validateDPForNotReported) {
      return DataRequestStatus.NotAnswered;
    }

    // "No Data"
    // DR: Will be displayed red and is shown, if there is no content and no document-chunks linked for every datapoint of the disclosure requirement

    datapointRequests.filter((datapoint) => {
      return (
        datapoint.status !== DatapointRequestStatus.NotAnswered &&
        !datapoint.content &&
        !(
          datapoint.datapointDocumentChunkMap &&
          datapoint.datapointDocumentChunkMap.length
        )
      );
    });

    if (datapointRequests.length === 0) {
      return DataRequestStatus.NoData;
    }

    // "Incomplete Data"
    // DR: Will be displayed yellow and is shown, if at least one of the datapoints inside the DR is not of status "Complete Data" or "Not Reported"

    const validateDPForIncompleteData = datapointRequests.some(
      (datapoint) =>
        datapoint.status !== DatapointRequestStatus.CompleteData &&
        datapoint.status !== DatapointRequestStatus.NotAnswered,
    );

    if (validateDPForIncompleteData) {
      return DataRequestStatus.IncompleteData;
    }

    // "Complete Data or Ready for Draft"
    // DR: Will be displayed, if all datapoints inside the DR have the status "Complete Data", but no content (reporttext) was yet saved

    const validateDPForCompleteData = datapointRequests.every(
      (datapoint) => datapoint.status === DatapointRequestStatus.CompleteData,
    );

    if (
      validateDPForCompleteData &&
      (!content || content.trim() === '' || content.trim() === '<p></p>')
    ) {
      return DataRequestStatus.CompleteData;
    }

    // "Draft"
    // DR: Will be displayed, if there exists a draft of an answer for the DR and all Datapoints have the status "Complete Data"
    if (approvedBy === null) {
      return DataRequestStatus.Draft;
    }

    return DataRequestStatus.ApprovedAnswer;
  }
}
