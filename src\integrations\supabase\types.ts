export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      chat_history: {
        Row: {
          createdAt: string
          id: string
          title: string | null
          userId: string
        }
        Insert: {
          createdAt?: string
          id?: string
          title?: string | null
          userId: string
        }
        Update: {
          createdAt?: string
          id?: string
          title?: string | null
          userId?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_6bac64204c7b416f465e17957ed"
            columns: ["userId"]
            isOneToOne: false
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_message: {
        Row: {
          chatHistoryId: string | null
          content: string
          createdAt: string
          id: string
          role: Database["public"]["Enums"]["chat_message_role_enum"]
        }
        Insert: {
          chatHistoryId?: string | null
          content: string
          createdAt?: string
          id?: string
          role: Database["public"]["Enums"]["chat_message_role_enum"]
        }
        Update: {
          chatHistoryId?: string | null
          content?: string
          createdAt?: string
          id?: string
          role?: Database["public"]["Enums"]["chat_message_role_enum"]
        }
        Relationships: [
          {
            foreignKeyName: "FK_4c772df41e9f3e87b644158fea7"
            columns: ["chatHistoryId"]
            isOneToOne: false
            referencedRelation: "chat_history"
            referencedColumns: ["id"]
          },
        ]
      }
      comment: {
        Row: {
          comment: string
          commentableId: string
          commentableType: Database["public"]["Enums"]["comment_commentabletype_enum"]
          createdAt: string
          id: string
          resolved: boolean
          userId: string
        }
        Insert: {
          comment: string
          commentableId: string
          commentableType: Database["public"]["Enums"]["comment_commentabletype_enum"]
          createdAt?: string
          id?: string
          resolved: boolean
          userId: string
        }
        Update: {
          comment?: string
          commentableId?: string
          commentableType?: Database["public"]["Enums"]["comment_commentabletype_enum"]
          createdAt?: string
          id?: string
          resolved?: boolean
          userId?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_c0354a9a009d3bb45a08655ce3b"
            columns: ["userId"]
            isOneToOne: false
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
        ]
      }
      comment_generation: {
        Row: {
          comment: string
          commentableId: string
          commentableType: Database["public"]["Enums"]["comment_commentabletype_enum"]
          createdAt: string
          evaluatedAt: string | null
          evaluatorComment: string | null
          evaluatorId: string | null
          id: string
          status: Database["public"]["Enums"]["comment_generation_status_enum"]
          userId: string
        }
        Insert: {
          comment: string
          commentableId: string
          commentableType: Database["public"]["Enums"]["comment_commentabletype_enum"]
          createdAt?: string
          evaluatedAt?: string | null
          evaluatorComment?: string | null
          evaluatorId?: string | null
          id?: string
          status?: Database["public"]["Enums"]["comment_generation_status_enum"]
          userId: string
        }
        Update: {
          comment?: string
          commentableId?: string
          commentableType?: Database["public"]["Enums"]["comment_commentabletype_enum"]
          createdAt?: string
          evaluatedAt?: string | null
          evaluatorComment?: string | null
          evaluatorId?: string | null
          id?: string
          status?: Database["public"]["Enums"]["comment_generation_status_enum"]
          userId?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_fa3f507677dc586d6d9aa1f6c23"
            columns: ["userId"]
            isOneToOne: false
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
        ]
      }
      company: {
        Row: {
          createdAt: string
          generalCompanyProfile: string | null
          id: string
          name: string
          reportTextGenerationRules: string | null
          workspaceId: string
        }
        Insert: {
          createdAt?: string
          generalCompanyProfile?: string | null
          id?: string
          name: string
          reportTextGenerationRules?: string | null
          workspaceId: string
        }
        Update: {
          createdAt?: string
          generalCompanyProfile?: string | null
          id?: string
          name?: string
          reportTextGenerationRules?: string | null
          workspaceId?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_517322010b04597bd8475f6f81e"
            columns: ["workspaceId"]
            isOneToOne: false
            referencedRelation: "workspace"
            referencedColumns: ["id"]
          },
        ]
      }
      data_request: {
        Row: {
          approvedAt: string | null
          approvedBy: string | null
          content: string
          createdAt: string
          customUserRemark: string | null
          dataRequestType: string
          dataRequestTypeId: number
          dueDate: string | null
          id: string
          projectId: string
          queueStatus:
            | Database["public"]["Enums"]["data_request_queuestatus_enum"]
            | null
          responsiblePersonId: string | null
          status: Database["public"]["Enums"]["data_request_status_enum"]
          updatedAt: string
        }
        Insert: {
          approvedAt?: string | null
          approvedBy?: string | null
          content?: string
          createdAt?: string
          customUserRemark?: string | null
          dataRequestType: string
          dataRequestTypeId: number
          dueDate?: string | null
          id?: string
          projectId: string
          queueStatus?:
            | Database["public"]["Enums"]["data_request_queuestatus_enum"]
            | null
          responsiblePersonId?: string | null
          status: Database["public"]["Enums"]["data_request_status_enum"]
          updatedAt?: string
        }
        Update: {
          approvedAt?: string | null
          approvedBy?: string | null
          content?: string
          createdAt?: string
          customUserRemark?: string | null
          dataRequestType?: string
          dataRequestTypeId?: number
          dueDate?: string | null
          id?: string
          projectId?: string
          queueStatus?:
            | Database["public"]["Enums"]["data_request_queuestatus_enum"]
            | null
          responsiblePersonId?: string | null
          status?: Database["public"]["Enums"]["data_request_status_enum"]
          updatedAt?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_4572010e83d95ebe4bfbab75431"
            columns: ["dataRequestTypeId"]
            isOneToOne: false
            referencedRelation: "esrs_disclosure_requirement"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_45bd67eaca95cdb8d83c5343633"
            columns: ["responsiblePersonId"]
            isOneToOne: false
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_7df0e25371a1a5bd8029a5d0569"
            columns: ["projectId"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_d05073e6e4886714a8cbbb72f03"
            columns: ["approvedBy"]
            isOneToOne: false
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
        ]
      }
      data_request_generation: {
        Row: {
          createdAt: string | null
          data: Json
          dataRequestId: string | null
          evaluatedAt: string | null
          evaluatorId: string | null
          id: string
          status: Database["public"]["Enums"]["data_request_generation_status_enum"]
        }
        Insert: {
          createdAt?: string | null
          data: Json
          dataRequestId?: string | null
          evaluatedAt?: string | null
          evaluatorId?: string | null
          id?: string
          status?: Database["public"]["Enums"]["data_request_generation_status_enum"]
        }
        Update: {
          createdAt?: string | null
          data?: Json
          dataRequestId?: string | null
          evaluatedAt?: string | null
          evaluatorId?: string | null
          id?: string
          status?: Database["public"]["Enums"]["data_request_generation_status_enum"]
        }
        Relationships: [
          {
            foreignKeyName: "FK_bf8f064dcc74c3cd021b27228c2"
            columns: ["dataRequestId"]
            isOneToOne: false
            referencedRelation: "data_request"
            referencedColumns: ["id"]
          },
        ]
      }
      datapoint_document_chunk: {
        Row: {
          active: boolean
          createdAt: string
          createdBy: string
          datapointRequestId: string | null
          documentChunkId: string
          id: number
          key_information: string | null
          modifiedBy: string | null
        }
        Insert: {
          active?: boolean
          createdAt?: string
          createdBy: string
          datapointRequestId?: string | null
          documentChunkId: string
          id?: number
          key_information?: string | null
          modifiedBy?: string | null
        }
        Update: {
          active?: boolean
          createdAt?: string
          createdBy?: string
          datapointRequestId?: string | null
          documentChunkId?: string
          id?: number
          key_information?: string | null
          modifiedBy?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "FK_00a077be01d7bfaf0bcd5f7bbfb"
            columns: ["datapointRequestId"]
            isOneToOne: false
            referencedRelation: "datapoint_request"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_10196ce6a73cda30b0ff2f1fe4a"
            columns: ["createdBy"]
            isOneToOne: false
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_2a5f6913cfb1a14844a6ae4652d"
            columns: ["documentChunkId"]
            isOneToOne: false
            referencedRelation: "document_chunk"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_863692eb2ba2706d98fef661edb"
            columns: ["modifiedBy"]
            isOneToOne: false
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
        ]
      }
      datapoint_generation: {
        Row: {
          createdAt: string | null
          data: Json
          datapointRequestId: string | null
          evaluatedAt: string | null
          evaluatorId: string | null
          id: string
          status: Database["public"]["Enums"]["datapoint_generation_status_enum"]
        }
        Insert: {
          createdAt?: string | null
          data: Json
          datapointRequestId?: string | null
          evaluatedAt?: string | null
          evaluatorId?: string | null
          id?: string
          status?: Database["public"]["Enums"]["datapoint_generation_status_enum"]
        }
        Update: {
          createdAt?: string | null
          data?: Json
          datapointRequestId?: string | null
          evaluatedAt?: string | null
          evaluatorId?: string | null
          id?: string
          status?: Database["public"]["Enums"]["datapoint_generation_status_enum"]
        }
        Relationships: [
          {
            foreignKeyName: "FK_4b2adcda7c85ad1d3a69c6c7329"
            columns: ["datapointRequestId"]
            isOneToOne: false
            referencedRelation: "datapoint_request"
            referencedColumns: ["id"]
          },
        ]
      }
      datapoint_request: {
        Row: {
          content: string
          createdAt: string
          customUserRemark: string
          dataRequestId: string
          esrsDatapointId: number
          id: string
          metadata: string | null
          queueStatus:
            | Database["public"]["Enums"]["datapoint_request_queuestatus_enum"]
            | null
          status: Database["public"]["Enums"]["datapoint_request_status_enum"]
          updatedAt: string
        }
        Insert: {
          content: string
          createdAt?: string
          customUserRemark?: string
          dataRequestId: string
          esrsDatapointId: number
          id?: string
          metadata?: string | null
          queueStatus?:
            | Database["public"]["Enums"]["datapoint_request_queuestatus_enum"]
            | null
          status: Database["public"]["Enums"]["datapoint_request_status_enum"]
          updatedAt?: string
        }
        Update: {
          content?: string
          createdAt?: string
          customUserRemark?: string
          dataRequestId?: string
          esrsDatapointId?: number
          id?: string
          metadata?: string | null
          queueStatus?:
            | Database["public"]["Enums"]["datapoint_request_queuestatus_enum"]
            | null
          status?: Database["public"]["Enums"]["datapoint_request_status_enum"]
          updatedAt?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_0cbeff414e1139c22da793547e5"
            columns: ["esrsDatapointId"]
            isOneToOne: false
            referencedRelation: "esrs_datapoint"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_802ef2f8443c803fce0ea914096"
            columns: ["dataRequestId"]
            isOneToOne: false
            referencedRelation: "data_request"
            referencedColumns: ["id"]
          },
        ]
      }
      document: {
        Row: {
          createdAt: string
          createdBy: string | null
          day: number | null
          documentType: string | null
          esrsCategory: string | null
          id: string
          month: number | null
          name: string
          path: string
          remarks: string | null
          status: Database["public"]["Enums"]["document_status_enum"]
          workspaceId: string
          year: number | null
        }
        Insert: {
          createdAt?: string
          createdBy?: string | null
          day?: number | null
          documentType?: string | null
          esrsCategory?: string | null
          id?: string
          month?: number | null
          name: string
          path: string
          remarks?: string | null
          status?: Database["public"]["Enums"]["document_status_enum"]
          workspaceId: string
          year?: number | null
        }
        Update: {
          createdAt?: string
          createdBy?: string | null
          day?: number | null
          documentType?: string | null
          esrsCategory?: string | null
          id?: string
          month?: number | null
          name?: string
          path?: string
          remarks?: string | null
          status?: Database["public"]["Enums"]["document_status_enum"]
          workspaceId?: string
          year?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "FK_41070461ad429c324c367525371"
            columns: ["workspaceId"]
            isOneToOne: false
            referencedRelation: "workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_a581782d3fe36e6cb98e40b0572"
            columns: ["createdBy"]
            isOneToOne: false
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
        ]
      }
      document_chunk: {
        Row: {
          content: string
          createdAt: string
          documentId: string
          id: string
          matchingsJson: string | null
          metadataJson: Json | null
          page: string
        }
        Insert: {
          content: string
          createdAt?: string
          documentId: string
          id?: string
          matchingsJson?: string | null
          metadataJson?: Json | null
          page: string
        }
        Update: {
          content?: string
          createdAt?: string
          documentId?: string
          id?: string
          matchingsJson?: string | null
          metadataJson?: Json | null
          page?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_3e9a852328831b703e5ef175ca8"
            columns: ["documentId"]
            isOneToOne: false
            referencedRelation: "document"
            referencedColumns: ["id"]
          },
        ]
      }
      document_version: {
        Row: {
          ancestor: string
          documentId: string
          id: string
          version: number | null
        }
        Insert: {
          ancestor: string
          documentId: string
          id?: string
          version?: number | null
        }
        Update: {
          ancestor?: string
          documentId?: string
          id?: string
          version?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "FK_document_version_ancestor"
            columns: ["ancestor"]
            isOneToOne: false
            referencedRelation: "document_version"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_document_version_document"
            columns: ["documentId"]
            isOneToOne: false
            referencedRelation: "document"
            referencedColumns: ["id"]
          },
        ]
      }
      ecovadis_answer_option: {
        Row: {
          createdAt: string
          id: string
          instructions: string | null
          issueTitle: string
          questionId: string
          sort: number
        }
        Insert: {
          createdAt?: string
          id?: string
          instructions?: string | null
          issueTitle: string
          questionId: string
          sort?: number
        }
        Update: {
          createdAt?: string
          id?: string
          instructions?: string | null
          issueTitle?: string
          questionId?: string
          sort?: number
        }
        Relationships: [
          {
            foreignKeyName: "FK_ecovadis_answer_option_question"
            columns: ["questionId"]
            isOneToOne: false
            referencedRelation: "ecovadis_question"
            referencedColumns: ["id"]
          },
        ]
      }
      ecovadis_question: {
        Row: {
          createdAt: string
          id: string
          indicator: Database["public"]["Enums"]["ecovadis_indicator"]
          question: string
          questionCode: string
          questionName: string | null
          scoringFramework: string | null
          sort: number
          themeId: string
          type: Database["public"]["Enums"]["ecovadis_answer_type"]
        }
        Insert: {
          createdAt?: string
          id?: string
          indicator: Database["public"]["Enums"]["ecovadis_indicator"]
          question: string
          questionCode: string
          questionName?: string | null
          scoringFramework?: string | null
          sort?: number
          themeId: string
          type?: Database["public"]["Enums"]["ecovadis_answer_type"]
        }
        Update: {
          createdAt?: string
          id?: string
          indicator?: Database["public"]["Enums"]["ecovadis_indicator"]
          question?: string
          questionCode?: string
          questionName?: string | null
          scoringFramework?: string | null
          sort?: number
          themeId?: string
          type?: Database["public"]["Enums"]["ecovadis_answer_type"]
        }
        Relationships: [
          {
            foreignKeyName: "FK_question_theme"
            columns: ["themeId"]
            isOneToOne: false
            referencedRelation: "ecovadis_theme"
            referencedColumns: ["id"]
          },
        ]
      }
      ecovadis_theme: {
        Row: {
          createdAt: string
          description: string | null
          id: string
          title: string
        }
        Insert: {
          createdAt?: string
          description?: string | null
          id?: string
          title: string
        }
        Update: {
          createdAt?: string
          description?: string | null
          id?: string
          title?: string
        }
        Relationships: []
      }
      esrs_datapoint: {
        Row: {
          conditional: boolean
          createdAt: string
          datapointId: string
          dataType: string | null
          esrsDisclosureRequirementId: number | null
          exampleOutput: string | null
          footnotes: string | null
          footnotesAR: string | null
          id: number
          lawText: string | null
          lawTextAR: string | null
          name: string
          optional: boolean
          paragraph: string | null
          publicAccess: boolean
          relatedAR: string | null
          updatedAt: string
        }
        Insert: {
          conditional?: boolean
          createdAt?: string
          datapointId: string
          dataType?: string | null
          esrsDisclosureRequirementId?: number | null
          exampleOutput?: string | null
          footnotes?: string | null
          footnotesAR?: string | null
          id?: number
          lawText?: string | null
          lawTextAR?: string | null
          name: string
          optional?: boolean
          paragraph?: string | null
          publicAccess?: boolean
          relatedAR?: string | null
          updatedAt?: string
        }
        Update: {
          conditional?: boolean
          createdAt?: string
          datapointId?: string
          dataType?: string | null
          esrsDisclosureRequirementId?: number | null
          exampleOutput?: string | null
          footnotes?: string | null
          footnotesAR?: string | null
          id?: number
          lawText?: string | null
          lawTextAR?: string | null
          name?: string
          optional?: boolean
          paragraph?: string | null
          publicAccess?: boolean
          relatedAR?: string | null
          updatedAt?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_809f5e1cde50fe0978efc545ead"
            columns: ["esrsDisclosureRequirementId"]
            isOneToOne: false
            referencedRelation: "esrs_disclosure_requirement"
            referencedColumns: ["id"]
          },
        ]
      }
      esrs_disclosure_requirement: {
        Row: {
          createdAt: string
          dr: string
          drDescription: string | null
          drObjective: string | null
          esrs: string
          id: number
          lawText: string | null
          lawTextAR: string | null
          name: string
          publicAccess: boolean
          sort: number
          updatedAt: string
        }
        Insert: {
          createdAt?: string
          dr: string
          drDescription?: string | null
          drObjective?: string | null
          esrs: string
          id?: number
          lawText?: string | null
          lawTextAR?: string | null
          name: string
          publicAccess?: boolean
          sort: number
          updatedAt?: string
        }
        Update: {
          createdAt?: string
          dr?: string
          drDescription?: string | null
          drObjective?: string | null
          esrs?: string
          id?: number
          lawText?: string | null
          lawTextAR?: string | null
          name?: string
          publicAccess?: boolean
          sort?: number
          updatedAt?: string
        }
        Relationships: []
      }
      esrs_topic: {
        Row: {
          description: string | null
          id: number
          level: Database["public"]["Enums"]["esrs_topic_level_enum"]
          name: string
          parentId: number | null
        }
        Insert: {
          description?: string | null
          id?: number
          level?: Database["public"]["Enums"]["esrs_topic_level_enum"]
          name: string
          parentId?: number | null
        }
        Update: {
          description?: string | null
          id?: number
          level?: Database["public"]["Enums"]["esrs_topic_level_enum"]
          name?: string
          parentId?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "FK_eeffc3f8ec9b2cc7f62baae3d48"
            columns: ["parentId"]
            isOneToOne: false
            referencedRelation: "esrs_topic"
            referencedColumns: ["id"]
          },
        ]
      }
      esrs_topic_datapoint: {
        Row: {
          esrsDatapointId: number
          esrsTopicId: number
        }
        Insert: {
          esrsDatapointId: number
          esrsTopicId: number
        }
        Update: {
          esrsDatapointId?: number
          esrsTopicId?: number
        }
        Relationships: [
          {
            foreignKeyName: "FK_7bad764f0b00ce815e9d0cd5541"
            columns: ["esrsDatapointId"]
            isOneToOne: false
            referencedRelation: "esrs_datapoint"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_c04b9f351d040532cb312e01973"
            columns: ["esrsTopicId"]
            isOneToOne: false
            referencedRelation: "esrs_topic"
            referencedColumns: ["id"]
          },
        ]
      }
      esrs_topic_disclosure_requirement: {
        Row: {
          esrsDisclosureRequirementId: number
          esrsTopicId: number
        }
        Insert: {
          esrsDisclosureRequirementId: number
          esrsTopicId: number
        }
        Update: {
          esrsDisclosureRequirementId?: number
          esrsTopicId?: number
        }
        Relationships: [
          {
            foreignKeyName: "FK_40a3bc24828423a91684a5ce477"
            columns: ["esrsDisclosureRequirementId"]
            isOneToOne: false
            referencedRelation: "esrs_disclosure_requirement"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_e208d4f71abc19d8c0a5eaaf267"
            columns: ["esrsTopicId"]
            isOneToOne: false
            referencedRelation: "esrs_topic"
            referencedColumns: ["id"]
          },
        ]
      }
      file_upload: {
        Row: {
          createdAt: string
          id: string
          name: string
          path: string
          userId: string
        }
        Insert: {
          createdAt?: string
          id?: string
          name: string
          path: string
          userId: string
        }
        Update: {
          createdAt?: string
          id?: string
          name?: string
          path?: string
          userId?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_ef625dca9c38989d7eca19474fe"
            columns: ["userId"]
            isOneToOne: false
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
        ]
      }
      knowledge_base_file_upload: {
        Row: {
          id: string
          name: string
          path: string
        }
        Insert: {
          id?: string
          name: string
          path: string
        }
        Update: {
          id?: string
          name?: string
          path?: string
        }
        Relationships: []
      }
      material_esrs_topic: {
        Row: {
          active: boolean
          createdAt: string
          esrsTopicId: number
          id: string
          projectId: string
        }
        Insert: {
          active?: boolean
          createdAt?: string
          esrsTopicId: number
          id?: string
          projectId: string
        }
        Update: {
          active?: boolean
          createdAt?: string
          esrsTopicId?: number
          id?: string
          projectId?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_50a1eb248445b84935b069fc0c8"
            columns: ["projectId"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_ed602f423d73ef38205a16a56f7"
            columns: ["esrsTopicId"]
            isOneToOne: false
            referencedRelation: "esrs_topic"
            referencedColumns: ["id"]
          },
        ]
      }
      migration_table: {
        Row: {
          id: number
          name: string
          timestamp: number
        }
        Insert: {
          id?: number
          name: string
          timestamp: number
        }
        Update: {
          id?: number
          name?: string
          timestamp?: number
        }
        Relationships: []
      }
      migrations: {
        Row: {
          id: number
          name: string
          timestamp: number
        }
        Insert: {
          id?: number
          name: string
          timestamp: number
        }
        Update: {
          id?: number
          name?: string
          timestamp?: number
        }
        Relationships: []
      }
      project: {
        Row: {
          createdAt: string
          createdBy: string
          id: string
          name: string
          primaryContentLanguage: Database["public"]["Enums"]["project_primarycontentlanguage_enum"]
          reportingYear: string | null
          reportTextGenerationRules: string
          type: Database["public"]["Enums"]["project_type"]
          workspaceId: string
        }
        Insert: {
          createdAt?: string
          createdBy: string
          id?: string
          name: string
          primaryContentLanguage: Database["public"]["Enums"]["project_primarycontentlanguage_enum"]
          reportingYear?: string | null
          reportTextGenerationRules?: string
          type: Database["public"]["Enums"]["project_type"]
          workspaceId: string
        }
        Update: {
          createdAt?: string
          createdBy?: string
          id?: string
          name?: string
          primaryContentLanguage?: Database["public"]["Enums"]["project_primarycontentlanguage_enum"]
          reportingYear?: string | null
          reportTextGenerationRules?: string
          type?: Database["public"]["Enums"]["project_type"]
          workspaceId?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_c224ab17df530651e53a398ed92"
            columns: ["workspaceId"]
            isOneToOne: false
            referencedRelation: "workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_c714b0a5eaf71cc3a36c242d2e9"
            columns: ["createdBy"]
            isOneToOne: false
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
        ]
      }
      project_ecovadis_answer: {
        Row: {
          createdAt: string
          id: string
          optionId: string
          projectId: string
          response: string | null
        }
        Insert: {
          createdAt?: string
          id?: string
          optionId: string
          projectId: string
          response?: string | null
        }
        Update: {
          createdAt?: string
          id?: string
          optionId?: string
          projectId?: string
          response?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "FK_ecovadis_answer_option"
            columns: ["optionId"]
            isOneToOne: false
            referencedRelation: "ecovadis_answer_option"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_ecovadis_answer_project"
            columns: ["projectId"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["id"]
          },
        ]
      }
      project_ecovadis_gaps: {
        Row: {
          assigneeId: string | null
          createdAt: string
          deadline: string | null
          documents: string[] | null
          gaps: Json | null
          id: string
          projectId: string
          questionId: string
          resolved: boolean | null
        }
        Insert: {
          assigneeId?: string | null
          createdAt?: string
          deadline?: string | null
          documents?: string[] | null
          gaps?: Json | null
          id?: string
          projectId: string
          questionId: string
          resolved?: boolean | null
        }
        Update: {
          assigneeId?: string | null
          createdAt?: string
          deadline?: string | null
          documents?: string[] | null
          gaps?: Json | null
          id?: string
          projectId?: string
          questionId?: string
          resolved?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "FK_project_ecovadis_gaps_assignee"
            columns: ["assigneeId"]
            isOneToOne: false
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_project_ecovadis_gaps_project"
            columns: ["projectId"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_project_ecovadis_gaps_question"
            columns: ["questionId"]
            isOneToOne: false
            referencedRelation: "ecovadis_question"
            referencedColumns: ["id"]
          },
        ]
      }
      project_ecovadis_linked_document_chunks: {
        Row: {
          answerId: string
          comment: string | null
          createdAt: string
          documentChunkId: string
          id: number
        }
        Insert: {
          answerId: string
          comment?: string | null
          createdAt?: string
          documentChunkId: string
          id?: number
        }
        Update: {
          answerId?: string
          comment?: string | null
          createdAt?: string
          documentChunkId?: string
          id?: number
        }
        Relationships: [
          {
            foreignKeyName: "FK_linked_chunks_answer"
            columns: ["answerId"]
            isOneToOne: false
            referencedRelation: "project_ecovadis_answer"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_linked_chunks_document_chunk"
            columns: ["documentChunkId"]
            isOneToOne: false
            referencedRelation: "document_chunk"
            referencedColumns: ["id"]
          },
        ]
      }
      project_ecovadis_question: {
        Row: {
          createdAt: string
          id: string
          impact: Database["public"]["Enums"]["impact_score"] | null
          projectId: string
          questionId: string
          status: Database["public"]["Enums"]["question_status_enum"]
        }
        Insert: {
          createdAt?: string
          id?: string
          impact?: Database["public"]["Enums"]["impact_score"] | null
          projectId: string
          questionId: string
          status?: Database["public"]["Enums"]["question_status_enum"]
        }
        Update: {
          createdAt?: string
          id?: string
          impact?: Database["public"]["Enums"]["impact_score"] | null
          projectId?: string
          questionId?: string
          status?: Database["public"]["Enums"]["question_status_enum"]
        }
        Relationships: [
          {
            foreignKeyName: "FK_project_ecovadis_question_project"
            columns: ["projectId"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_project_ecovadis_question_question"
            columns: ["questionId"]
            isOneToOne: false
            referencedRelation: "ecovadis_question"
            referencedColumns: ["id"]
          },
        ]
      }
      project_ecovadis_question_score: {
        Row: {
          breakdown: string | null
          conclusion: string | null
          createdAt: string
          description: string | null
          id: string
          level: Database["public"]["Enums"]["ecovadis_score_level"]
          questionId: string
          score: number
        }
        Insert: {
          breakdown?: string | null
          conclusion?: string | null
          createdAt?: string
          description?: string | null
          id?: string
          level: Database["public"]["Enums"]["ecovadis_score_level"]
          questionId: string
          score: number
        }
        Update: {
          breakdown?: string | null
          conclusion?: string | null
          createdAt?: string
          description?: string | null
          id?: string
          level?: Database["public"]["Enums"]["ecovadis_score_level"]
          questionId?: string
          score?: number
        }
        Relationships: [
          {
            foreignKeyName: "FK_project_ecovadis_question_score_project_question"
            columns: ["questionId"]
            isOneToOne: false
            referencedRelation: "project_ecovadis_question"
            referencedColumns: ["id"]
          },
        ]
      }
      project_ecovadis_question_score_history: {
        Row: {
          breakdown: string | null
          conclusion: string | null
          createdAt: string
          description: string | null
          id: string
          level: Database["public"]["Enums"]["ecovadis_score_level"]
          score: number
          scoreId: string
          version: number
        }
        Insert: {
          breakdown?: string | null
          conclusion?: string | null
          createdAt?: string
          description?: string | null
          id?: string
          level: Database["public"]["Enums"]["ecovadis_score_level"]
          score: number
          scoreId: string
          version: number
        }
        Update: {
          breakdown?: string | null
          conclusion?: string | null
          createdAt?: string
          description?: string | null
          id?: string
          level?: Database["public"]["Enums"]["ecovadis_score_level"]
          score?: number
          scoreId?: string
          version?: number
        }
        Relationships: [
          {
            foreignKeyName: "FK_project_ecovadis_score_history_score"
            columns: ["scoreId"]
            isOneToOne: false
            referencedRelation: "project_ecovadis_question_score"
            referencedColumns: ["id"]
          },
        ]
      }
      project_ecovadis_theme: {
        Row: {
          createdAt: string
          id: string
          impact: Database["public"]["Enums"]["impact_score"] | null
          issues: string[] | null
          projectId: string
          themeId: string
        }
        Insert: {
          createdAt?: string
          id?: string
          impact?: Database["public"]["Enums"]["impact_score"] | null
          issues?: string[] | null
          projectId: string
          themeId: string
        }
        Update: {
          createdAt?: string
          id?: string
          impact?: Database["public"]["Enums"]["impact_score"] | null
          issues?: string[] | null
          projectId?: string
          themeId?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_project_ecovadis_theme_project"
            columns: ["projectId"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_project_ecovadis_theme_theme"
            columns: ["themeId"]
            isOneToOne: false
            referencedRelation: "ecovadis_theme"
            referencedColumns: ["id"]
          },
        ]
      }
      token: {
        Row: {
          createdAt: string
          expiresAt: string
          id: number
          token: string
          type: Database["public"]["Enums"]["token_type_enum"]
          userId: string
        }
        Insert: {
          createdAt?: string
          expiresAt: string
          id?: number
          token: string
          type: Database["public"]["Enums"]["token_type_enum"]
          userId: string
        }
        Update: {
          createdAt?: string
          expiresAt?: string
          id?: number
          token?: string
          type?: Database["public"]["Enums"]["token_type_enum"]
          userId?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_94f168faad896c0786646fa3d4a"
            columns: ["userId"]
            isOneToOne: false
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
        ]
      }
      user: {
        Row: {
          auth_id: string | null
          createdAt: string
          email: string
          id: string
          name: string | null
          password: string | null
        }
        Insert: {
          auth_id?: string | null
          createdAt?: string
          email: string
          id?: string
          name?: string | null
          password?: string | null
        }
        Update: {
          auth_id?: string | null
          createdAt?: string
          email?: string
          id?: string
          name?: string | null
          password?: string | null
        }
        Relationships: []
      }
      user_profiles: {
        Row: {
          created_at: string | null
          email: string
          id: string
          name: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          id: string
          name?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          id?: string
          name?: string | null
        }
        Relationships: []
      }
      user_prompt_context: {
        Row: {
          context: string
          userId: string
        }
        Insert: {
          context: string
          userId: string
        }
        Update: {
          context?: string
          userId?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_fa868054a006042b047c0619387"
            columns: ["userId"]
            isOneToOne: true
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
        ]
      }
      user_workspace: {
        Row: {
          createdAt: string
          joinedAt: string | null
          role: Database["public"]["Enums"]["user_workspace_role_enum"] | null
          userId: string
          workspaceId: string
        }
        Insert: {
          createdAt?: string
          joinedAt?: string | null
          role?: Database["public"]["Enums"]["user_workspace_role_enum"] | null
          userId: string
          workspaceId: string
        }
        Update: {
          createdAt?: string
          joinedAt?: string | null
          role?: Database["public"]["Enums"]["user_workspace_role_enum"] | null
          userId?: string
          workspaceId?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_46438fa9a476521c49324b59843"
            columns: ["workspaceId"]
            isOneToOne: false
            referencedRelation: "workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_4ea12fabb12c08c3dc8839d0932"
            columns: ["userId"]
            isOneToOne: false
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
        ]
      }
      version_history: {
        Row: {
          createdAt: string
          event: string
          id: string
          ref: string
          versionData: Json
          workspaceId: string
        }
        Insert: {
          createdAt?: string
          event: string
          id?: string
          ref: string
          versionData: Json
          workspaceId: string
        }
        Update: {
          createdAt?: string
          event?: string
          id?: string
          ref?: string
          versionData?: Json
          workspaceId?: string
        }
        Relationships: [
          {
            foreignKeyName: "FK_1c0f9ac1bd6cabda5b40031637c"
            columns: ["workspaceId"]
            isOneToOne: false
            referencedRelation: "workspace"
            referencedColumns: ["id"]
          },
        ]
      }
      workspace: {
        Row: {
          createdAt: string
          id: string
          name: string
        }
        Insert: {
          createdAt?: string
          id?: string
          name: string
        }
        Update: {
          createdAt?: string
          id?: string
          name?: string
        }
        Relationships: []
      }
      workspace_members: {
        Row: {
          created_at: string | null
          joined_at: string | null
          role: Database["public"]["Enums"]["role_enum"] | null
          user_id: string
          workspace_id: string
        }
        Insert: {
          created_at?: string | null
          joined_at?: string | null
          role?: Database["public"]["Enums"]["role_enum"] | null
          user_id: string
          workspace_id: string
        }
        Update: {
          created_at?: string | null
          joined_at?: string | null
          role?: Database["public"]["Enums"]["role_enum"] | null
          user_id?: string
          workspace_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "workspace_members_workspace_id_fkey"
            columns: ["workspace_id"]
            isOneToOne: false
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
        ]
      }
      project_ecovadis_gap_generation: {
        Row: {
          createdAt: string
          feedback: string | null
          generatedContent: Json
          id: string
          projectId: string
          questionId: string
          reviewedAt: string | null
          reviewedBy: string | null
          status: Database["public"]["Enums"]["generated_gaps_status"]
          updatedAt: string | null
        }
        Insert: {
          createdAt?: string
          feedback?: string | null
          generatedContent: Json
          id?: string
          projectId: string
          questionId: string
          reviewedAt?: string | null
          reviewedBy?: string | null
          status?: Database["public"]["Enums"]["generated_gaps_status"]
          updatedAt?: string | null
        }
        Update: {
          createdAt?: string
          feedback?: string | null
          generatedContent?: Json
          id?: string
          projectId?: string
          questionId?: string
          reviewedAt?: string | null
          reviewedBy?: string | null
          status?: Database["public"]["Enums"]["generated_gaps_status"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "generated_gaps_project_id_fkey"
            columns: ["projectId"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "generated_gaps_question_id_fkey"
            columns: ["questionId"]
            isOneToOne: false
            referencedRelation: "ecovadis_question"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "generated_gaps_reviewed_by_fkey"
            columns: ["reviewedBy"]
            isOneToOne: false
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      workspace_ecovadis_gaps: {
        Row: {
          assigneeid: string | null
          deadline: string | null
          documents: string[] | null
          gaps: Json | null
          id: string | null
          projectid: string | null
          projectname: string | null
          question: string | null
          questioncode: string | null
          questionid: string | null
          questionname: string | null
          resolved: boolean | null
          status: Database["public"]["Enums"]["question_status_enum"] | null
          workspaceid: string | null
        }
        Relationships: [
          {
            foreignKeyName: "FK_c224ab17df530651e53a398ed92"
            columns: ["workspaceid"]
            isOneToOne: false
            referencedRelation: "workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_project_ecovadis_gaps_assignee"
            columns: ["assigneeid"]
            isOneToOne: false
            referencedRelation: "user"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_project_ecovadis_gaps_project"
            columns: ["projectid"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "FK_project_ecovadis_gaps_question"
            columns: ["questionid"]
            isOneToOne: false
            referencedRelation: "ecovadis_question"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      binary_quantize: {
        Args: { "": string } | { "": unknown }
        Returns: unknown
      }
      halfvec_avg: {
        Args: { "": number[] }
        Returns: unknown
      }
      halfvec_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      halfvec_send: {
        Args: { "": unknown }
        Returns: string
      }
      halfvec_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      hnsw_bit_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnsw_halfvec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnsw_sparsevec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnswhandler: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflat_bit_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflat_halfvec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflathandler: {
        Args: { "": unknown }
        Returns: unknown
      }
      l2_norm: {
        Args: { "": unknown } | { "": unknown }
        Returns: number
      }
      l2_normalize: {
        Args: { "": string } | { "": unknown } | { "": unknown }
        Returns: string
      }
      sparsevec_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      sparsevec_send: {
        Args: { "": unknown }
        Returns: string
      }
      sparsevec_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      uuid_generate_v1: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_generate_v1mc: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_generate_v3: {
        Args: { namespace: string; name: string }
        Returns: string
      }
      uuid_generate_v4: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_generate_v5: {
        Args: { namespace: string; name: string }
        Returns: string
      }
      uuid_nil: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_dns: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_oid: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_url: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_x500: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      vector_avg: {
        Args: { "": number[] }
        Returns: string
      }
      vector_dims: {
        Args: { "": string } | { "": unknown }
        Returns: number
      }
      vector_norm: {
        Args: { "": string }
        Returns: number
      }
      vector_out: {
        Args: { "": string }
        Returns: unknown
      }
      vector_send: {
        Args: { "": string }
        Returns: string
      }
      vector_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
    }
    Enums: {
      chat_message_role_enum: "user" | "system" | "assistant" | "function"
      comment_commentabletype_enum: "datapoint_request" | "data_request"
      comment_generation_status_enum: "pending" | "approved" | "rejected"
      data_request_generation_status_enum: "pending" | "approved" | "rejected"
      data_request_queuestatus_enum:
        | "queued_for_generation"
        | "queued_for_review"
      data_request_status_enum:
        | "no_data"
        | "incomplete_data"
        | "draft"
        | "complete_data"
        | "approved_answer"
        | "not_answered"
      datapoint_generation_status_enum:
        | "pending"
        | "approved"
        | "rejected"
        | "minorChanges"
      datapoint_request_queuestatus_enum:
        | "queued_for_generation"
        | "queued_for_review"
      datapoint_request_status_enum:
        | "not_answered"
        | "incomplete_data"
        | "no_data"
        | "complete_data"
      document_status_enum:
        | "not_processed"
        | "in_extraction"
        | "data_extraction_finished"
        | "linking_data"
        | "linking_data_finished"
        | "error_processing"
        | "queued_for_extraction"
        | "failed_extraction"
        | "queued_for_linking"
        | "failed_linking"
      ecovadis_answer_type: "checkbox" | "text"
      ecovadis_indicator:
        | "POLICIES"
        | "ENDORSEMENTS"
        | "MEASURES"
        | "CERTIFICATIONS"
        | "COVERAGE"
        | "REPORTING"
        | "WATCH_FINDINGS"
      ecovadis_score_level:
        | "Outstanding"
        | "Advanced"
        | "Good"
        | "Partial"
        | "Insufficient"
      generated_gaps_status:
        | "pending_review"
        | "approved" 
        | "rejected"
        | "regenerating"
      esrs_topic_level_enum: "topic" | "sub-topic" | "sub-sub-topic"
      impact_score: "High" | "Medium" | "Low"
      project_primarycontentlanguage_enum:
        | "BG"
        | "HR"
        | "CS"
        | "DA"
        | "NL"
        | "EN"
        | "ET"
        | "FI"
        | "FR"
        | "DE"
        | "EL"
        | "HU"
        | "GA"
        | "IT"
        | "LV"
        | "LT"
        | "MT"
        | "PL"
        | "PT"
        | "RO"
        | "SK"
        | "SL"
        | "ES"
        | "SV"
      project_type: "CSRD" | "EcoVadis"
      question_status_enum: "pending" | "complete"
      role_enum:
        | "SUPER_ADMIN"
        | "WORKSPACE_ADMIN"
        | "AI_CONTRIBUTOR"
        | "AI_ONLY_REVIEW"
        | "CONTRIBUTOR"
      token_type_enum: "passwordReset" | "workspaceInvite"
      user_workspace_role_enum:
        | "SUPER_ADMIN"
        | "WORKSPACE_ADMIN"
        | "AI_CONTRIBUTOR"
        | "AI_ONLY_REVIEW"
        | "CONTRIBUTOR"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      chat_message_role_enum: ["user", "system", "assistant", "function"],
      comment_commentabletype_enum: ["datapoint_request", "data_request"],
      comment_generation_status_enum: ["pending", "approved", "rejected"],
      data_request_generation_status_enum: ["pending", "approved", "rejected"],
      data_request_queuestatus_enum: [
        "queued_for_generation",
        "queued_for_review",
      ],
      data_request_status_enum: [
        "no_data",
        "incomplete_data",
        "draft",
        "complete_data",
        "approved_answer",
        "not_answered",
      ],
      datapoint_generation_status_enum: [
        "pending",
        "approved",
        "rejected",
        "minorChanges",
      ],
      datapoint_request_queuestatus_enum: [
        "queued_for_generation",
        "queued_for_review",
      ],
      datapoint_request_status_enum: [
        "not_answered",
        "incomplete_data",
        "no_data",
        "complete_data",
      ],
      document_status_enum: [
        "not_processed",
        "in_extraction",
        "data_extraction_finished",
        "linking_data",
        "linking_data_finished",
        "error_processing",
        "queued_for_extraction",
        "failed_extraction",
        "queued_for_linking",
        "failed_linking",
      ],
      ecovadis_answer_type: ["checkbox", "text"],
      ecovadis_indicator: [
        "POLICIES",
        "ENDORSEMENTS",
        "MEASURES",
        "CERTIFICATIONS",
        "COVERAGE",
        "REPORTING",
        "WATCH_FINDINGS",
      ],
      ecovadis_score_level: [
        "Outstanding",
        "Advanced",
        "Good",
        "Partial",
        "Insufficient",
      ],
      generated_gaps_status: [
        "pending_review",
        "approved", 
        "rejected",
        "regenerating"
      ],
      esrs_topic_level_enum: ["topic", "sub-topic", "sub-sub-topic"],
      impact_score: ["High", "Medium", "Low"],
      project_primarycontentlanguage_enum: [
        "BG",
        "HR",
        "CS",
        "DA",
        "NL",
        "EN",
        "ET",
        "FI",
        "FR",
        "DE",
        "EL",
        "HU",
        "GA",
        "IT",
        "LV",
        "LT",
        "MT",
        "PL",
        "PT",
        "RO",
        "SK",
        "SL",
        "ES",
        "SV",
      ],
      project_type: ["CSRD", "EcoVadis"],
      question_status_enum: ["pending", "complete"],
      role_enum: [
        "SUPER_ADMIN",
        "WORKSPACE_ADMIN",
        "AI_CONTRIBUTOR",
        "AI_ONLY_REVIEW",
        "CONTRIBUTOR",
      ],
      token_type_enum: ["passwordReset", "workspaceInvite"],
      user_workspace_role_enum: [
        "SUPER_ADMIN",
        "WORKSPACE_ADMIN",
        "AI_CONTRIBUTOR",
        "AI_ONLY_REVIEW",
        "CONTRIBUTOR",
      ],
    },
  },
} as const
