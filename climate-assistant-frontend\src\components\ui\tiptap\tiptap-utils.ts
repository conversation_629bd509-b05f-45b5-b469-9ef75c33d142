export function preprocessTiptapContent(
  content: string,
  refId?: string
): string {
  const processedContent = content.replace(
    /\[dpcite-(\d+)\|color-([#a-fA-F0-9]+)\|text-"([^"]+)"\]/g,
    (_match, index, color, text) => {
      return `<span data-modal-link="true" data-index="${index}" ${refId ? `data-refid="${refId}"` : ''} data-color="${color}" data-text="${text}"></span>`;
    }
  );

  //   console.log('processedContent', processedContent);
  return processedContent;
}
