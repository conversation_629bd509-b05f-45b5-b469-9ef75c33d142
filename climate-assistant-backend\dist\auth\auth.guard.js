"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthGuard = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const constants_1 = require("./constants");
const helpers_1 = require("./helpers");
const core_1 = require("@nestjs/core");
const user_workspace_entity_1 = require("../users/entities/user-workspace.entity");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("@nestjs/typeorm");
let AuthGuard = class AuthGuard {
    constructor(jwtService, reflector, userWorkspaceRepository) {
        this.jwtService = jwtService;
        this.reflector = reflector;
        this.userWorkspaceRepository = userWorkspaceRepository;
    }
    async canActivate(context) {
        const isPublic = this.reflector.getAllAndOverride(helpers_1.IS_PUBLIC_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (isPublic) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const token = this.extractFromCookies(request);
        if (!token) {
            throw new common_1.UnauthorizedException();
        }
        try {
            const payload = await this.jwtService.verifyAsync(token, {
                secret: constants_1.jwtConstants.secret,
            });
            request['user'] = payload;
        }
        catch {
            throw new common_1.UnauthorizedException();
        }
        const requiredRoles = this.reflector.get('roles', context.getHandler());
        if (!requiredRoles) {
            return true;
        }
        const userWorkspace = await this.userWorkspaceRepository.findOne({
            where: { userId: request.user.id, workspaceId: request.user.workspaceId },
        });
        const userRole = userWorkspace.role;
        return requiredRoles.includes(userRole);
    }
    extractFromCookies(request) {
        return request?.cookies['token'];
    }
};
exports.AuthGuard = AuthGuard;
exports.AuthGuard = AuthGuard = __decorate([
    (0, common_1.Injectable)(),
    __param(2, (0, typeorm_2.InjectRepository)(user_workspace_entity_1.UserWorkspace)),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        core_1.Reflector,
        typeorm_1.Repository])
], AuthGuard);
//# sourceMappingURL=auth.guard.js.map