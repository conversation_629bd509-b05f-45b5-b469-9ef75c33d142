import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { read, utils, WorkBook } from 'xlsx';
import { promises as fs } from 'fs';
import {
  addPageRangeBuffer,
  validatePageNumbersAgainstDocument,
} from '../../util/common-util';

const ANSWER_DELIMITER = 'my answer';
const COMMENT_DELIMITER = 'my comments about the document';

enum OptionType {
  CHECKBOX = 'checkbox',
  TEXT = 'text',
  TEXT_WITH_CHECKBOX = 'text-with-checkbox',
}

type ImpactScore = 'High' | 'Medium' | 'Low' | null;

type EcoVadisIndicator =
  | 'POLICIES'
  | 'ENDORSEMENTS'
  | 'MEASURES'
  | 'CERTIFICATIONS'
  | 'COVERAGE'
  | 'REPORTING'
  | 'WATCH_FINDINGS';

interface RowData {
  [key: string]: any;
}

interface NormalizedRow {
  theme: string;
  themeImpact: string;
  indicator: string;
  indicatorImpact: string;
  questionName: string;
  questionCode: string;
  questionImpact: string;
  question: string;
  optionText: string;
  optionInstructions: string;
  parentCredit: string;
  assignedTo?: string;
  userInput?: string;
  supportingDocs: {
    title: string;
    supportingValues?: string;
    supportsAnswerText?: boolean;
    pageNumbers?: string;
    type?: OptionType;
  }[];
  [key: string]: any;
}

interface ImportStats {
  themes: { created: number; existing: number };
  questions: { created: number; existing: number };
  options: { created: number; existing: number };
  projectThemes: { created: number; existing: number };
  projectQuestions: { created: number; existing: number };
  answers: { created: number; existing: number };
  documentLinks: { created: number; existing: number };
}

// Define our impact mappings with common variations
const impactMap: Record<string, ImpactScore> = {
  High: 'High',
  high: 'High',
  HIGH: 'High',
  Medium: 'Medium',
  medium: 'Medium',
  MEDIUM: 'Medium',
  Low: 'Low',
  low: 'Low',
  LOW: 'Low',
  'N/A': null,
  'n/a': null,
  'N/a': null,
};

// Define our indicator mappings to match the database enum exactly
// Add common variations of each indicator to handle different wordings
const indicatorMap: Record<string, EcoVadisIndicator> = {
  Policies: 'POLICIES',
  policies: 'POLICIES',
  Policy: 'POLICIES',
  policy: 'POLICIES',
  Endorsements: 'ENDORSEMENTS',
  endorsements: 'ENDORSEMENTS',
  Endorsement: 'ENDORSEMENTS',
  endorsement: 'ENDORSEMENTS',
  Measures: 'MEASURES',
  measures: 'MEASURES',
  Measure: 'MEASURES',
  measure: 'MEASURES',
  Certifications: 'CERTIFICATIONS',
  certifications: 'CERTIFICATIONS',
  Certification: 'CERTIFICATIONS',
  certification: 'CERTIFICATIONS',
  Coverage: 'COVERAGE',
  coverage: 'COVERAGE',
  Reporting: 'REPORTING',
  reporting: 'REPORTING',
  Report: 'REPORTING',
  report: 'REPORTING',
  '360° Watch Findings': 'WATCH_FINDINGS',
  '360° watch findings': 'WATCH_FINDINGS',
  '360° News': 'WATCH_FINDINGS',
  '360° news': 'WATCH_FINDINGS',
  '360 Watch Findings': 'WATCH_FINDINGS',
  '360 News': 'WATCH_FINDINGS',
};

// Default indicator to use when mapping fails
const DEFAULT_INDICATOR: EcoVadisIndicator = 'MEASURES';

const columnOrderMap = {
  0: 'theme',
  1: 'themeImpact',
  2: 'indicator',
  3: 'indicatorImpact',
  4: 'questionName',
  5: 'questionCode',
  6: 'questionImpact',
  7: 'parentCredit',
  8: 'question',
  9: 'optionText',
  10: 'optionInstructions',
  11: 'assignedTo',
  12: 'userInput',
  13: 'supportingDocs',
  14: 'supportingValues',
  15: 'pageNumbers',
};

@Injectable()
export class QuestionnaireService {
  private supabase: SupabaseClient;

  constructor(private configService: ConfigService) {
    this.supabase = createClient(
      this.configService.get<string>('SUPABASE_APP_URL'),
      this.configService.get<string>('SUPABASE_SERVICE_KEY')
    );
  }

  getClient(): SupabaseClient {
    return this.supabase;
  }

  // Helper function to determine question type based on supporting value content
  determineOptionType(supportingValue: string): OptionType {
    if (!supportingValue) return OptionType.TEXT;

    const content = supportingValue.toLowerCase().trim();

    if (content.startsWith(COMMENT_DELIMITER)) {
      return OptionType.CHECKBOX;
    }

    if (content.startsWith(ANSWER_DELIMITER)) {
      return content.includes(COMMENT_DELIMITER)
        ? OptionType.TEXT_WITH_CHECKBOX
        : OptionType.TEXT;
    }

    return OptionType.CHECKBOX;
  }

  // Helper function to process answer content based on type
  processAnswerContent(
    userInput: string,
    supportingValues: string
  ): { response: string; comment: string } {
    const optionType = this.determineOptionType(supportingValues || '');

    switch (optionType) {
      case OptionType.TEXT:
        return {
          response: supportingValues || '',
          comment: '',
        };

      case OptionType.TEXT_WITH_CHECKBOX: {
        const content = supportingValues || '';
        const answerMatch = content.match(
          new RegExp(`${ANSWER_DELIMITER}(.*?)(?=${COMMENT_DELIMITER}|$)`, 'is')
        );
        const commentMatch = content.match(
          new RegExp(`${COMMENT_DELIMITER}(.*?)$`, 'i')
        );

        return {
          response: answerMatch ? answerMatch[1].trim() : '',
          comment: commentMatch ? commentMatch[1].trim() : '',
        };
      }

      default: // checkbox
        return {
          response: userInput,
          comment: supportingValues || '',
        };
    }
  }

  async processExcelQuestionnaire(filePath: string, projectId: string) {
    const buffer = await fs.readFile(filePath);
    const workbook = read(buffer, { type: 'array' }) as WorkBook;

    // Try to find a sheet with "Questionnaire" in its name (case-insensitive)
    const sheetNames = Object.keys(workbook.Sheets);
    const questionnaireSheet = sheetNames.find(
      (name) =>
        name.toLowerCase().includes('questionnaire') ||
        name.toLowerCase().includes('fragebogen')
    );

    // If no exact match, try to find any sheet that looks like it might be a questionnaire
    // (has key column names like "Question" or "Theme")
    if (!questionnaireSheet) {
      let bestSheet = '';
      let bestScore = 0;

      for (const name of sheetNames) {
        const sheet = workbook.Sheets[name];
        const actualRange = sheet['!ref']
          ? utils.decode_range(sheet['!ref'])
          : null;
        const jsonData = utils.sheet_to_json(sheet, {
          header: 1,
          defval: '', // Default value for empty cells
          range: actualRange, // Explicit range
          raw: false, // Ensure consistent string formatting
        }) as string[][];

        if (jsonData.length > 0) {
          const headers = jsonData[0];
          let score = 0;

          // Check if key column names exist
          if (headers.some((h) => h && /theme/i.test(h))) score += 2;
          if (headers.some((h) => h && /question/i.test(h))) score += 2;
          if (headers.some((h) => h && /indicator/i.test(h))) score += 1;

          if (score > bestScore) {
            bestScore = score;
            bestSheet = name;
          }
        }
      }

      if (bestScore > 0) {
        console.info(`Found likely questionnaire sheet: ${bestSheet}`);
      } else {
        throw new Error('No suitable questionnaire sheet found');
      }
    }

    const sheetName = questionnaireSheet || sheetNames[0]; // Fallback to first sheet if all else fails

    if (!workbook.Sheets[sheetName]) {
      throw new Error(`Sheet "${sheetName}" not found in the workbook`);
    }

    // Convert the sheet to JSON with headers
    const jsonData = utils.sheet_to_json(
      workbook.Sheets[sheetName]
    ) as RowData[];

    if (jsonData.length === 0) {
      throw new Error('No data found in the sheet');
    }

    const keyPositions = new Map();
    const optimisticHeaders = {};

    jsonData.forEach((obj, rowIndex) => {
      Object.keys(obj).forEach((key, colIndex) => {
        if (!keyPositions.has(key)) {
          // Check if position is already occupied
          const existingKeysAtOrAfter = Array.from(
            keyPositions.entries()
          ).filter(([_, pos]) => pos >= colIndex);

          if (existingKeysAtOrAfter.length > 0) {
            // Shift all keys at this position and after by 1
            existingKeysAtOrAfter.forEach(([existingKey, existingPos]) => {
              keyPositions.set(existingKey, existingPos + 1);
            });
          }

          // Insert new key at desired position
          keyPositions.set(key, colIndex);
        }
      });
    });

    // Sort keys by their original column position
    const sortedKeys = Array.from(keyPositions.entries())
      .sort(([, posA], [, posB]) => posA - posB)
      .map(([key]) => key);

    sortedKeys.forEach((key) => {
      optimisticHeaders[key] = '';
    });

    // Get all headers from the first row
    const headers = Object.keys(optimisticHeaders);

    // console.log('Headers:', headers.length, headers);

    // return new Response(JSON.stringify({
    //   message: 'Headers mapped successfully'
    // }), {
    //   headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    // });

    // Map headers to standardized field names
    const headerMap = this.createHeaderMapByPosition(headers);

    // Log which headers were matched for debugging
    console.log('Mapped headers:', headerMap);

    // Check if we have the minimum required fields
    const requiredFields = [
      'theme',
      'questionName',
      'questionCode',
      'question',
    ];
    const missingFields = requiredFields.filter((field) => !headerMap[field]);

    if (missingFields.length > 0) {
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }

    // Process the data with context carrying (handling empty cells)
    const normalizedData = this.normalizeExcelData(jsonData, headerMap);

    // Process the data and insert into database
    const result = await this.processQuestionnaire(normalizedData, projectId);
  }

  // Function to normalize Excel data by carrying forward values for empty cells
  // and accumulating supporting docs into the current option row
  normalizeExcelData(
    jsonData: RowData[],
    headerMap: Record<string, string>
  ): NormalizedRow[] {
    const normalizedRows: NormalizedRow[] = [];

    // Track current question-level data to carry forward
    let currentQuestionData: Partial<NormalizedRow> = {};

    for (const row of jsonData) {
      // Check if this row contains a new question (has questionCode)
      const questionCodeHeader = headerMap['questionCode'];
      const hasQuestionCode =
        questionCodeHeader &&
        row[questionCodeHeader] &&
        String(row[questionCodeHeader]).trim();

      // If this is a new question row, update our current question data
      if (hasQuestionCode) {
        currentQuestionData = {};

        // Extract all question-level fields from this row
        for (const [standardField, headerName] of Object.entries(headerMap)) {
          if (
            headerName &&
            row[headerName] !== undefined &&
            row[headerName] !== ''
          ) {
            // Skip option-specific and supporting doc fields - these don't get carried forward
            if (
              ![
                'optionText',
                'optionInstructions',
                'userInput',
                'supportingDocs',
                'supportingValues',
                'pageNumbers',
              ].includes(standardField)
            ) {
              currentQuestionData[standardField] = String(row[headerName]);
            }
          }
        }
      }

      // Check if this row has option text (indicating a new option)
      const optionTextHeader = headerMap['optionText'];
      const hasOptionText =
        optionTextHeader &&
        row[optionTextHeader] &&
        String(row[optionTextHeader]).trim();

      // Check for supporting documents
      const supportingDocsHeader = headerMap['supportingDocs'];
      const hasSupportingDocs =
        supportingDocsHeader &&
        row[supportingDocsHeader] &&
        String(row[supportingDocsHeader]).trim();

      if (hasOptionText || (hasQuestionCode && !hasSupportingDocs)) {
        // Create a new option row
        const normalizedRow: NormalizedRow = {
          theme: '',
          themeImpact: '',
          indicator: '',
          indicatorImpact: '',
          questionName: '',
          questionCode: '',
          questionImpact: '',
          question: '',
          optionText: '',
          optionInstructions: '',
          parentCredit: '',
          assignedTo: '',
          userInput: '',
          supportingDocs: [],
          ...currentQuestionData, // Carry forward question-level data
        };

        // Set option-specific fields from current row
        if (hasOptionText) {
          normalizedRow.optionText = String(row[optionTextHeader]);
        }

        const optionInstructionsHeader = headerMap['optionInstructions'];
        if (optionInstructionsHeader && row[optionInstructionsHeader]) {
          normalizedRow.optionInstructions = String(
            row[optionInstructionsHeader]
          );
        }

        const assignedToHeader = headerMap['assignedTo'];
        if (assignedToHeader && row[assignedToHeader]) {
          normalizedRow.assignedTo = String(row[assignedToHeader]);
        }

        const userInputHeader = headerMap['userInput'];
        if (userInputHeader && row[userInputHeader]) {
          normalizedRow.userInput = String(row[userInputHeader]);
        }

        // Add supporting document if present in this row
        if (hasSupportingDocs) {
          const docInfo = {
            title: String(row[supportingDocsHeader]),
            supportingValues: row[headerMap['supportingValues']]
              ? String(row[headerMap['supportingValues']])
              : '',
            pageNumbers: row[headerMap['pageNumbers']]
              ? String(row[headerMap['pageNumbers']])
              : '',
          };
          normalizedRow.supportingDocs.push(docInfo);
        }

        normalizedRows.push(normalizedRow);
      } else if (hasSupportingDocs && normalizedRows.length > 0) {
        // This is a supporting document row - add it to the most recent option row
        const currentOptionRow = normalizedRows[normalizedRows.length - 1];

        const docInfo = {
          title: String(row[supportingDocsHeader]),
          supportingValues: row[headerMap['supportingValues']]
            ? String(row[headerMap['supportingValues']])
            : '',
          pageNumbers: row[headerMap['pageNumbers']]
            ? String(row[headerMap['pageNumbers']])
            : '',
          type: this.determineOptionType(
            String(row[headerMap['supportingValues']] || '')
          ),
        };

        currentOptionRow.supportingDocs.push(docInfo);

        // Also check for any additional option-level fields that might be in this row
        const optionInstructionsHeader = headerMap['optionInstructions'];
        if (
          optionInstructionsHeader &&
          row[optionInstructionsHeader] &&
          !currentOptionRow.optionInstructions
        ) {
          currentOptionRow.optionInstructions = String(
            row[optionInstructionsHeader]
          );
        }

        const assignedToHeader = headerMap['assignedTo'];
        if (
          assignedToHeader &&
          row[assignedToHeader] &&
          !currentOptionRow.assignedTo
        ) {
          currentOptionRow.assignedTo = String(row[assignedToHeader]);
        }

        const userInputHeader = headerMap['userInput'];
        if (
          userInputHeader &&
          row[userInputHeader] &&
          !currentOptionRow.userInput
        ) {
          currentOptionRow.userInput = String(row[userInputHeader]);
        }
      }
    }

    return normalizedRows;
  }

  // Process the questionnaire data and insert into database
  async processQuestionnaire(
    data: NormalizedRow[],
    projectId: string
  ): Promise<{ success: boolean; stats: ImportStats; message: string }> {
    const stats: ImportStats = {
      themes: { created: 0, existing: 0 },
      questions: { created: 0, existing: 0 },
      options: { created: 0, existing: 0 },
      projectThemes: { created: 0, existing: 0 },
      projectQuestions: { created: 0, existing: 0 },
      answers: { created: 0, existing: 0 },
      documentLinks: { created: 0, existing: 0 },
    };

    const themeMap = new Map<string, string>(); // Track themes to avoid duplicates
    const questionMap = new Map<string, string>(); // Track questions to avoid duplicates

    // Group rows by question code to handle multi-row options for the same question
    const questionGroups = new Map<string, NormalizedRow[]>();

    // First pass: group rows by question code
    for (const row of data) {
      const questionCode = row.questionCode;
      if (questionCode) {
        if (!questionGroups.has(questionCode)) {
          questionGroups.set(questionCode, []);
        }
        questionGroups.get(questionCode)?.push(row);
      }
    }

    // Track question sort order per theme
    const themeSortCounter = new Map<string, number>();

    // Process each question group (a question and all its options)
    // Convert questionGroups to array and process in batches of 10
    const questionGroupsArray = Array.from(questionGroups.entries());
    const BATCH_SIZE = 10;
    // Process in batches
    for (let i = 0; i < questionGroupsArray.length; i += BATCH_SIZE) {
      const batch = questionGroupsArray.slice(i, i + BATCH_SIZE);

      await Promise.all(
        batch.map(async ([questionCode, rows]) => {
          if (rows.length === 0) {
            return;
          }

          // Use the first row for question and theme info
          const firstRow = rows[0];
          const themeName = firstRow.theme;
          const themeImpact = impactMap[firstRow.themeImpact] || null;

          // 1. Process theme
          if (themeName && !themeMap.has(themeName)) {
            // Check if theme exists
            const { data: existingTheme } = await this.supabase
              .from('ecovadis_theme')
              .select('id')
              .eq('title', themeName)
              .maybeSingle();

            let themeId: string;
            if (existingTheme) {
              themeId = existingTheme.id;
              stats.themes.existing++;
            } else {
              // Create theme
              const { data: newTheme, error } = await this.supabase
                .from('ecovadis_theme')
                .insert({
                  title: themeName,
                  description: `Theme for ${themeName}`,
                })
                .select('id')
                .single();

              if (error)
                throw new Error(`Failed to create theme: ${error.message}`);
              themeId = newTheme.id;
              stats.themes.created++;
            }

            themeMap.set(themeName, themeId);

            // Create project-theme relation if doesn't exist
            const { data: existingProjectTheme } = await this.supabase
              .from('project_ecovadis_theme')
              .select('id')
              .eq('projectId', projectId)
              .eq('themeId', themeId)
              .maybeSingle();

            if (!existingProjectTheme) {
              const { error } = await this.supabase
                .from('project_ecovadis_theme')
                .insert({
                  projectId,
                  themeId,
                  impact: themeImpact,
                });

              if (error)
                throw new Error(
                  `Failed to create project-theme relation: ${error.message}`
                );
              stats.projectThemes.created++;
            } else {
              stats.projectThemes.existing++;
            }

            // Initialize sort counter for this theme
            themeSortCounter.set(themeName, 0);
          }

          // 2. Process question
          const questionName = firstRow.questionName;
          const questionText = firstRow.question;

          // Get indicator with default fallback to prevent null values
          let indicatorValue: EcoVadisIndicator = DEFAULT_INDICATOR;
          if (firstRow.indicator && indicatorMap[firstRow.indicator]) {
            indicatorValue = indicatorMap[firstRow.indicator];
          }

          const questionImpact = impactMap[firstRow.questionImpact] || null;

          if (
            questionCode &&
            questionName &&
            questionText &&
            !questionMap.has(questionCode)
          ) {
            const themeId = themeMap.get(firstRow.theme);

            if (!themeId) {
              console.warn(
                `Theme ID not found for question ${questionCode}. Skipping.`
              );
              return;
            }

            // Get the current sort order for this theme
            const currentSortOrder = themeSortCounter.get(firstRow.theme) || 0;
            // Increment the sort counter for the next question in this theme
            themeSortCounter.set(firstRow.theme, currentSortOrder + 1);

            // Check if question exists
            const { data: existingQuestion } = await this.supabase
              .from('ecovadis_question')
              .select('id')
              .eq('questionCode', questionCode)
              .maybeSingle();

            let questionId: string;
            if (existingQuestion) {
              questionId = existingQuestion.id;

              // Update the sort order of existing question
              await this.supabase
                .from('ecovadis_question')
                .update({ sort: currentSortOrder })
                .eq('id', questionId);

              stats.questions.existing++;
            } else {
              // Create question, ensuring indicator is never null
              const { data: newQuestion, error } = await this.supabase
                .from('ecovadis_question')
                .insert({
                  themeId,
                  questionCode,
                  questionName,
                  question: questionText,
                  indicator: indicatorValue,
                  sort: currentSortOrder, // Set the sort order for new questions
                })
                .select('id')
                .single();

              if (error)
                throw new Error(`Failed to create question: ${error.message}`);
              questionId = newQuestion.id;
              stats.questions.created++;
            }

            questionMap.set(questionCode, questionId);

            // Create project-question relation if doesn't exist
            const { data: existingProjectQuestion } = await this.supabase
              .from('project_ecovadis_question')
              .select('id')
              .eq('projectId', projectId)
              .eq('questionId', questionId)
              .maybeSingle();

            if (!existingProjectQuestion) {
              const { error } = await this.supabase
                .from('project_ecovadis_question')
                .insert({
                  projectId,
                  questionId,
                  impact: questionImpact,
                  status: 'pending',
                });

              if (error)
                throw new Error(
                  `Failed to create project-question relation: ${error.message}`
                );
              stats.projectQuestions.created++;
            } else {
              stats.projectQuestions.existing++;
            }

            // 3. Process all options for this question from all related rows
            // Replace the option processing section in processQuestionnaire function
            // Starting from "// 3. Process all options for this question from all related rows"

            // 3. Process all options for this question from all related rows
            let optionIndex = 0;

            // First, group rows by actual answer options and their associated document rows
            const optionGroups: { optionRow: NormalizedRow }[] = [];
            let currentOptionGroup: { optionRow: NormalizedRow } | null = null;

            for (const row of rows) {
              const optionText = row.optionText;
              const userInput = row.userInput;
              const effectiveOptionText = optionText || userInput || '';

              if (effectiveOptionText && effectiveOptionText !== 'N/A') {
                // This is a new answer option
                currentOptionGroup = {
                  optionRow: row,
                };
                optionGroups.push(currentOptionGroup);
              }
            }

            // Now process each option group (option + all its associated document rows)
            for (const optionGroup of optionGroups) {
              const row = optionGroup.optionRow;
              let effectiveOptionText = row.optionText || row.userInput || '';
              if (row.questionName.toLocaleLowerCase() == 'other information') {
                effectiveOptionText = 'Other Information';
              }

              // Check if option exists
              let { data: existingOption } = await this.supabase
                .from('ecovadis_answer_option')
                .select('id')
                .eq('questionId', questionId)
                .eq('issueTitle', effectiveOptionText)
                .maybeSingle();

              if (!existingOption) {
                // Get optionInstructionsHeader from appropriate column
                const optionInstructionsHeader = row.optionInstructions || '';

                const { data: createOption, error } = await this.supabase
                  .from('ecovadis_answer_option')
                  .insert({
                    questionId,
                    issueTitle: effectiveOptionText,
                    instructions: optionInstructionsHeader,
                    sort: optionIndex,
                  })
                  .select('id')
                  .single();

                existingOption = createOption;

                if (error)
                  throw new Error(
                    `Failed to create answer option: ${error.message}`
                  );
                stats.options.created++;
              } else {
                // Update the sort order of the existing option
                const { error: updateError } = await this.supabase
                  .from('ecovadis_answer_option')
                  .update({ sort: optionIndex })
                  .eq('id', existingOption.id);

                if (updateError) {
                  console.error(
                    `Failed to update sort order for option ${existingOption.id}: ${updateError.message}`
                  );
                  throw new Error(
                    `Failed to update answer option sort order: ${updateError.message}`
                  );
                }

                stats.options.existing++;
              }

              // Check if the option has a "yes" response
              let userInput = row.userInput?.trim().toLowerCase();
              if (!userInput || userInput === '' || userInput === 'no') {
                userInput = '';
              }
              // Check if answer already exists
              const { data: existingAnswer } = await this.supabase
                .from('project_ecovadis_answer')
                .select('id')
                .eq('projectId', projectId)
                .eq('optionId', existingOption.id)
                .maybeSingle();

              let answerId: string;
              if (existingAnswer) {
                // Update existing answer
                const { error } = await this.supabase
                  .from('project_ecovadis_answer')
                  .update({ response: userInput })
                  .eq('id', existingAnswer.id);
                if (error)
                  throw new Error(`Failed to update answer: ${error.message}`);

                answerId = existingAnswer.id;
                stats.answers.existing++;
              } else {
                // Create answer
                const { data: newAnswer, error } = await this.supabase
                  .from('project_ecovadis_answer')
                  .insert({
                    projectId,
                    optionId: existingOption.id,
                    response: userInput,
                  })
                  .select('id')
                  .single();

                if (error)
                  throw new Error(`Failed to create answer: ${error.message}`);
                answerId = newAnswer.id;
                stats.answers.created++;
              }

              if (userInput !== '') {
                for (const docRow of optionGroup.optionRow.supportingDocs) {
                  const { response, comment } = this.processAnswerContent(
                    userInput,
                    docRow.supportingValues?.trim() || ''
                  );
                  if (
                    response ||
                    !docRow.supportingValues ||
                    optionGroup.optionRow.questionName.toLowerCase() ===
                      'other information'
                  ) {
                    await this.supabase
                      .from('project_ecovadis_answer')
                      .update({ response, supportsAnswerText: true })
                      .eq('id', answerId);
                  }
                  const supportingDocs = docRow.title?.trim();
                  const pageNumbers = docRow.pageNumbers?.toString().trim();

                  if (supportingDocs && answerId) {
                    await this.processDocumentsForAnswer({
                      projectId,
                      answerId,
                      supportingDocs,
                      supportingValues: comment,
                      pageNumbers,
                      stats,
                    });
                  }
                }
              }

              // Increment the option sort order
              optionIndex++;
            }
          }
        })
      );
    }

    return {
      success: true,
      stats,
      message: 'Questionnaire imported successfully',
    };
  }

  // Add this new function after the linkDocumentChunkToAnswer function

  // Helper function to process all documents for a specific answer
  async processDocumentsForAnswer({
    projectId,
    answerId,
    supportingDocs,
    supportingValues,
    pageNumbers,
    stats,
  }: {
    projectId: string;
    answerId: string;
    supportingDocs: string;
    supportingValues?: string;
    pageNumbers?: string;
    stats: ImportStats;
  }) {
    // Get workspace ID from the project
    const { data: projectData } = await this.supabase
      .from('project')
      .select('workspaceId')
      .eq('id', projectId)
      .single();

    if (!projectData) {
      console.warn(`Project ${projectId} not found when processing documents`);
      return;
    }

    const workspaceId = projectData.workspaceId;

    // Find documents by name in the workspace
    const { data: documents } = await this.supabase
      .from('document')
      .select('id, name')
      .eq('workspaceId', workspaceId)
      .ilike('name', supportingDocs);

    if (documents && documents.length > 0) {
      // For each document, fetch all relevant chunks in a single query
      for (const document of documents) {
        // Parse page numbers - could be ranges like "1-3, 5, 7-9"
        const originalPageNumbersArray = pageNumbers
          ? this.parsePageNumbers(pageNumbers)
          : [];

        let chunks: { id: string }[] = [];

        if (originalPageNumbersArray.length === 0) {
          // If no specific pages provided, get all chunks for this document
          const { data: documentChunks, error } = await this.supabase
            .from('document_chunk')
            .select('id')
            .eq('documentId', document.id)
            .order('page', { ascending: true });

          chunks = documentChunks || [];
        } else {
          // Add buffer of 2 pages before and after the page range
          const pageNumbersArray = addPageRangeBuffer(originalPageNumbersArray);

          // Log the buffer application for testing purposes
          console.log(
            `[Questionnaire upload - document: ${document.name}] Page range buffer applied:`
          );
          console.log(
            `  Original pages: [${originalPageNumbersArray.join(', ')}]`
          );
          console.log(`  Extended pages: [${pageNumbersArray.join(', ')}]`);
          console.log(
            `  Buffer added: ${pageNumbersArray.length - originalPageNumbersArray.length} pages`
          );

          // Validate page numbers against document's actual page count
          const validPageNumbers = await validatePageNumbersAgainstDocument(
            this.supabase,
            document.id,
            pageNumbersArray,
            document.name
          );

          // Get all specified chunks in a single query using 'in'
          const pageStrings = validPageNumbers.map((page) => page.toString());
          const { data: documentChunks, error } = await this.supabase
            .from('document_chunk')
            .select('id')
            .eq('documentId', document.id)
            .in('page', pageStrings)
            .order('page', { ascending: true });

          chunks = documentChunks || [];
        }

        // Link chunks in batches of 10
        const chunkIds = chunks.map((chunk) => chunk.id);
        for (let i = 0; i < chunkIds.length; i += 10) {
          const batch = chunkIds.slice(i, i + 10);
          await Promise.all(
            batch.map((chunkId) =>
              this.linkDocumentChunkToAnswer({
                answerId,
                documentChunkId: chunkId,
                comment: i === 0 && supportingValues ? supportingValues : '',
                stats,
              })
            )
          );
        }
      }
    }
  }

  // Helper function to link document chunks to answers
  async linkDocumentChunkToAnswer({
    answerId,
    documentChunkId,
    comment,
    stats,
  }: {
    answerId: string;
    documentChunkId: string;
    comment: string;
    stats: ImportStats;
  }) {
    // Check if link already exists
    const { data: existingLink } = await this.supabase
      .from('project_ecovadis_linked_document_chunks')
      .select('id')
      .eq('answerId', answerId)
      .eq('documentChunkId', documentChunkId)
      .maybeSingle();

    if (!existingLink) {
      const { error } = await this.supabase
        .from('project_ecovadis_linked_document_chunks')
        .insert({
          answerId,
          documentChunkId,
          comment,
        });

      if (error)
        throw new Error(`Failed to link document chunk: ${error.message}`);
      stats.documentLinks.created++;
    } else {
      // Optionally update the comment if needed
      if (comment) {
        await this.supabase
          .from('project_ecovadis_linked_document_chunks')
          .update({ comment })
          .eq('id', existingLink.id);
      }
      stats.documentLinks.existing++;
    }
  }

  // Helper function to parse page numbers from string like "1-3, 5, 7-9"
  parsePageNumbers(pageStr: string): number[] {
    // if "toutes"
    if (pageStr.toLowerCase() === 'toutes') {
      return [];
    }

    const pages: number[] = [];

    // Split by commas
    const parts = pageStr.split(',').map((p) => p.trim());

    for (const part of parts) {
      if (part.includes('-')) {
        // Handle ranges like "1-3"
        const [start, end] = part
          .split('-')
          .map((num) => parseInt(num.trim(), 10));
        if (!isNaN(start) && !isNaN(end)) {
          for (let i = start; i <= end; i++) {
            pages.push(i);
          }
        }
      } else {
        // Handle single numbers
        const pageNum = parseInt(part.trim(), 10);
        if (!isNaN(pageNum)) {
          pages.push(pageNum);
        }
      }
    }

    return pages;
  }

  // Helper function to find the matching column name from the actual headers
  createHeaderMapByPosition(headers: string[]): Record<string, string> {
    const headerMap: Record<string, string> = {};
    console.log(headers);

    // Map each standardized field to the header at the corresponding position
    for (const [indexStr, standardField] of Object.entries(columnOrderMap)) {
      const index = parseInt(indexStr, 10);
      if (index < headers.length) {
        headerMap[standardField] = headers[index];
      }
    }

    return headerMap;
  }
}
