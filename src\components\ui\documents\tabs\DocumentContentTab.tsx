
import { Document } from '@/types/ecovadis';
import { Card, CardContent } from '@/components/ui/card';
import { MarkdownRenderer } from '../../markdown-renderer';

interface DocumentContentTabProps {
  document: Document;
}

export function DocumentContentTab({ document }: DocumentContentTabProps) {
  if (!document.chunks || document.chunks.length === 0) {
    return (
      <div className="py-10 text-center text-gray-500">
        <p>Content is being processed or unavailable</p>
      </div>
    );
  }
  
  return document.chunks
  .sort((a, b) => Number(a.page) - Number(b.page))
  .map((page) => (
    <Card key={page.pageNumber} className="mb-6" >
      <CardContent className="pt-6">
        <h3 className="text-sm font-medium text-glacier-darkBlue mb-2">Page {page.page}</h3>
        <div className="prose text-xs text-gray-700 break-words w-full max-w-[70vw] overflow-x-auto">
          <MarkdownRenderer text={page.content} />
        </div>
        
        {page.images && page.images.length > 0 && (
          <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {page.images.map((image, index) => (
              <div key={index} className="border rounded-md overflow-hidden">
                <img src={image} alt={`Image ${index + 1} from page ${page.page}`} className="w-full h-auto" />
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  ));

}
