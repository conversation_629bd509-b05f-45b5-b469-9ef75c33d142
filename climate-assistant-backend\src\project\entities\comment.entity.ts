import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  Index,
  CreateDateColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { DatapointRequest } from '../../datapoint/entities/datapoint-request.entity';
import { DataRequest } from '../../data-request/entities/data-request.entity';

// enums/comment_type.enum.ts
export enum CommentType {
  DatapointRequest = 'datapoint_request',
  DataRequest = 'data_request',
}

@Entity()
@Index(['commentableId', 'commentableType'])
export class Comment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  userId: string;

  @Column('uuid')
  commentableId: string;

  @Column({ type: 'enum', enum: CommentType })
  commentableType: CommentType;

  @Column({ type: 'text' })
  comment: string;

  @Column({ type: 'boolean' })
  resolved: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => User, (user) => user.comments)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => DataRequest, (dataRequest) => dataRequest.comments, {
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'commentableId' })
  dataRequest: DataRequest;

  @ManyToOne(
    () => DatapointRequest,
    (datapointRequest) => datapointRequest.comments,
    { createForeignKeyConstraints: false },
  )
  @JoinColumn({ name: 'commentableId' })
  datapointRequest: DatapointRequest;
}
