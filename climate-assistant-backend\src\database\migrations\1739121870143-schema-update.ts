import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * @description
 * Adds a `customUserRemark` column to the `data_request` table, allowing users to provide
 * additional context for AI-generated report text. This enables more precise and tailored
 * text generation based on user-defined rules or remarks.
 *
 * @migration
 * - **Up Migration:** Adds a `customUserRemark` column of type `text` to the `data_request` table.
 * - **Down Migration:** Removes the `customUserRemark` column from the `data_request` table.
 */
export class SchemaUpdate1739121870143 implements MigrationInterface {
  name = 'SchemaUpdate1739121870143';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "data_request" ADD "customUserRemark" text`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "data_request" DROP COLUMN "customUserRemark"`,
    );
  }
}
