
import { useState } from 'react';
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Search } from "lucide-react";
import { QuestionTopic, QuestionStatus, QuestionImpact, QuestionScore } from "@/types/ecovadis";

interface SearchFiltersProps {
  onSearchChange: (value: string) => void;
  onTopicChange: (value: string) => void;
  onStatusChange: (value: string) => void;
  onImpactChange: (value: string) => void;
  onScoreChange: (value: string) => void;
  onGapsChange: (value: string) => void;
}

export const SearchFilters = ({ 
  onSearchChange, 
  onTopicChange, 
  onStatusChange,
  onImpactChange,
  onScoreChange,
  onGapsChange
}: SearchFiltersProps) => {
  const topics: QuestionTopic[] = [
    '🌍 General',
    '🌱 Environment',
    '👥 Labor & Human Rights',
    '💼 Sustainable Procurement',
    '🔷 Ethics'
  ];
  
  const statuses: QuestionStatus[] = ['Complete', 'Incomplete', 'Not applicable'];
  
  const impacts: QuestionImpact[] = ['High', 'Medium', 'Low', 'N/A'];
  
  const scores: QuestionScore[] = [0, 25, 50, 75, 100];
  
  const gapsOptions = ['0', '1-2', '3+'];
  
  return (
    <div className="mb-6 space-y-3">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Search by question name or code..."
          className="pl-10"
          onChange={(e) => onSearchChange(e.target.value)}
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        <Select onValueChange={onTopicChange}>
          <SelectTrigger>
            <SelectValue placeholder="Filter by topic" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Topics</SelectItem>
            {topics.map(topic => (
              <SelectItem key={topic} value={topic}>{topic}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select onValueChange={onStatusChange}>
          <SelectTrigger>
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            {statuses.map(status => (
              <SelectItem key={status.toString()} value={status.toString()}>{status}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select onValueChange={onImpactChange}>
          <SelectTrigger>
            <SelectValue placeholder="Filter by impact" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Impacts</SelectItem>
            {impacts.map(impact => (
              <SelectItem key={impact} value={impact}>{impact}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select onValueChange={onScoreChange}>
          <SelectTrigger>
            <SelectValue placeholder="Filter by score" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Scores</SelectItem>
            {scores.map(score => (
              <SelectItem key={score.toString()} value={score.toString()}>Score: {score}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select onValueChange={onGapsChange}>
          <SelectTrigger>
            <SelectValue placeholder="Filter by gaps" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Gaps</SelectItem>
            {gapsOptions.map(option => (
              <SelectItem key={option} value={option}>{option} gaps</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};
