
import confetti from 'canvas-confetti';

export const fireConfetti = () => {
  const count = 200;
  const defaults = {
    origin: { y: 0.7 },
  };

  function fire(particleRatio: number, opts: any) {
    confetti({
      ...defaults,
      ...opts,
      particleCount: Math.floor(count * particleRatio),
    });
  }

  fire(0.25, {
    spread: 26,
    startVelocity: 55,
    colors: ['#9b87f5', '#7E69AB', '#6E59A5']
  });
  fire(0.2, {
    spread: 60,
    colors: ['#1A1F2C', '#D6BCFA', '#9b87f5']
  });
  fire(0.35, {
    spread: 100,
    decay: 0.91,
    scalar: 0.8,
    colors: ['#6E59A5', '#9b87f5', '#D6BCFA']
  });
  fire(0.1, {
    spread: 120,
    startVelocity: 25,
    decay: 0.92,
    scalar: 1.2,
    colors: ['#9b87f5', '#7E69AB', '#D6BCFA']
  });
  fire(0.1, {
    spread: 120,
    startVelocity: 45,
    colors: ['#D6BCFA', '#9b87f5', '#7E69AB']
  });
};
