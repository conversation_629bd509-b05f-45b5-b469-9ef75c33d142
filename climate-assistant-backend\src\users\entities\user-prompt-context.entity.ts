import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>n, OneToOne, PrimaryColumn } from 'typeorm';
import { User } from './user.entity';

@Entity()
export class UserPromptContext {
  @PrimaryColumn({ type: 'uuid' })
  userId: string;

  @OneToOne(() => User, (user) => user.userPromptContext)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'varchar' })
  context: string;
}
