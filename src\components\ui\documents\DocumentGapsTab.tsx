
import { useMemo } from 'react';
import { DocumentDetailsResponse } from '@/api/documents/documents.api';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { convertToRanges } from '@/lib/utils';

interface DocumentGapsTabProps {
  document: DocumentDetailsResponse;
}

interface GroupedQuestion {
  id: string; // Added missing id property
  questionCode: string;
  projectName: string;
  text: string;
  pages: number[];
  comments: string[];
  issueTitle?: string;
}

export function DocumentGapsTab({ document }: DocumentGapsTabProps) {
  const groupedQuestions = useMemo(() => {
    if (!document.linkedQuestions) return [];
    return Object.values(document.linkedQuestions.reduce((acc, question) => {
      // Create a unique key based on question code, project and answer option
      const key = `${question.questionCode}-${question.issueTitle || ''}`;
      
      if (!acc[key]) {
        acc[key] = {
          id: question.id || key, // Use question.id or generate one from the key
          questionCode: question.questionCode || '',
          projectName: question.projectName || '-',
          text: question.text,
          pages: [],
          comments: [],
          issueTitle: question.issueTitle
        };
      }

      // Add pages and comments if they're not already included
      if (question.pages && !acc[key].pages.includes(Number(question.pages))) {
        acc[key].pages.push(Number(question.pages));
      }
      if (question.comment && !acc[key].comments.includes(question.comment)) {
        acc[key].comments.push(question.comment);
      }

      return acc;
    }, {} as Record<string, GroupedQuestion>));
  }, [document.linkedQuestions]);

  if (!document.linkedQuestions || document.linkedQuestions.length === 0) {
    return (
      <div className="py-10 text-center text-gray-500">
        <p>No EcoVadis questions are linked to this document</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-2/6">Question</TableHead>
            <TableHead className="w-1/6">Issue</TableHead>
            <TableHead className="w-1/6">Project</TableHead>
            <TableHead className="w-1/6">Pages</TableHead>
            <TableHead className="w-2/6">Comment</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {groupedQuestions.map((question) => (
            <TableRow key={question.id}>
              <TableCell>
                <div className="font-medium text-glacier-darkBlue">{question.text}</div>
                {question.questionCode && (
                  <div className="text-xs text-gray-400 mt-1">
                    {question.questionCode}
                  </div>
                )}
              </TableCell>
              <TableCell>
                <div className="text-gray-900">{question.issueTitle || '-'}</div>
              </TableCell>
              <TableCell>
                <div className="text-gray-900">{question.projectName || '-'}</div>
              </TableCell>
              <TableCell>
                <div>{convertToRanges(question.pages).join(', ')}</div>
              </TableCell>
              <TableCell>
                {question.comments.length > 0 ? (
                  <div className="space-y-1">
                    {question.comments.map((comment, index) => (
                      <div
                        key={index}
                        className="text-sm line-clamp-2"
                        style={{
                          display: '-webkit-box',
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          WebkitLineClamp: 2,
                        }}
                        title={comment} // Tooltip to show full comment on hover
                      >
                        {comment}
                      </div>
                    ))}
                  </div>
                ) : (
                  <span className="text-gray-400">No comment</span>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
