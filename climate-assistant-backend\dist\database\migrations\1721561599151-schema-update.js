"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1721561599151 = void 0;
class SchemaUpdate1721561599151 {
    constructor() {
        this.name = 'SchemaUpdate1721561599151';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "file_upload" ADD "name" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "file_upload_chunk" DROP COLUMN "embedding"`);
        await queryRunner.query(`ALTER TABLE "file_upload_chunk" ADD "embedding" text NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "file_upload_chunk" DROP COLUMN "embedding"`);
        await queryRunner.query(`ALTER TABLE "file_upload_chunk" ADD "embedding" text NOT NULL`);
        await queryRunner.query(`ALTER TABLE "file_upload" DROP COLUMN "name"`);
    }
}
exports.SchemaUpdate1721561599151 = SchemaUpdate1721561599151;
//# sourceMappingURL=1721561599151-schema-update.js.map