import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { Language } from 'src/project/entities/project.entity';
export declare class NormalDpPromptService {
    private readonly GENERATION_LANGUAGE;
    private readonly turndownService;
    constructor();
    generateDatapointContentGenerationSystemPrompt({ esrsDatapoint, generationLanguage, reportTextGenerationRules, customUserRemark, currentContent, otherDatapoints, reportingYear, generalCompanyProfile, linkedChunks, }: {
        esrsDatapoint: ESRSDatapoint;
        generationLanguage: Language;
        reportTextGenerationRules: string;
        customUserRemark: string;
        currentContent: string;
        otherDatapoints: ESRSDatapoint[];
        reportingYear: string;
        generalCompanyProfile: string;
        linkedChunks: string;
    }): string;
    improvingFormattingPrompt({ esrsDatapoint, generationLanguage, reportTextGenerationRules, customUserRemark, currentContent, generalCompanyProfile, reportingYear, predatapointGenerationChatCompletionResponse, }: {
        esrsDatapoint: ESRSDatapoint;
        generationLanguage: Language;
        reportTextGenerationRules: string;
        customUserRemark: string;
        currentContent: string;
        generalCompanyProfile: string;
        reportingYear: string;
        predatapointGenerationChatCompletionResponse: string;
    }): string;
}
