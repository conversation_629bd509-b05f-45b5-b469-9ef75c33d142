"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1720942648773 = void 0;
class SchemaUpdate1720942648773 {
    constructor() {
        this.name = 'SchemaUpdate1720942648773';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "chat_history" ADD "title" character varying NOT NULL DEFAULT ''`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "chat_history" DROP COLUMN "title"`);
    }
}
exports.SchemaUpdate1720942648773 = SchemaUpdate1720942648773;
//# sourceMappingURL=1720942648773-schema-update.js.map