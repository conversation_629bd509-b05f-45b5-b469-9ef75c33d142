-- Create enums
CREATE TYPE project_type AS ENUM ('CSRD', 'EcoVadis');
CREATE TYPE ecovadis_indicator AS ENUM (
  'POLICIES',
  'ENDORSEMENTS',
  'MEASURES',
  'CERTIFICATIONS',
  'COVERAGE',
  'REPORTING',
  'WATCH_FINDINGS'
);
CREATE TYPE impact_score AS ENUM ('High', 'Medium', 'Low');

-- Update existing project table carefully
-- First add the column as nullable
ALTER TABLE project ADD COLUMN "type" project_type NULL;

-- Update existing records to CSRD
UPDATE project SET "type" = 'CSRD';

-- Then make it not nullable
ALTER TABLE project ALTER COLUMN "type" SET NOT NULL;

-- Create document_version table
CREATE TABLE IF NOT EXISTS document_version (
  "id" uuid PRIMARY KEY NOT NULL DEFAULT public.uuid_generate_v4(),
  "documentId" uuid NOT NULL,
  "ancestor" uuid NOT NULL,
  "version" integer
);

-- Create ecovadis_theme table
CREATE TABLE IF NOT EXISTS ecovadis_theme (
  "id" uuid PRIMARY KEY NOT NULL DEFAULT public.uuid_generate_v4(),
  "title" varchar(255) NOT NULL,
  "description" text,
  "createdAt" timestamp NOT NULL DEFAULT now()
);

-- Create project_ecovadis_theme table
CREATE TABLE IF NOT EXISTS project_ecovadis_theme (
  "id" uuid PRIMARY KEY NOT NULL DEFAULT public.uuid_generate_v4(),
  "themeId" uuid NOT NULL,
  "projectId" uuid NOT NULL,
  "impact" impact_score,
  "issues" varchar(255)[],
  "createdAt" timestamp NOT NULL DEFAULT now()
);

-- Create ecovadis_question table
CREATE TABLE IF NOT EXISTS ecovadis_question (
  "id" uuid PRIMARY KEY NOT NULL DEFAULT public.uuid_generate_v4(),
  "themeId" uuid NOT NULL,
  "questionCode" varchar(50) NOT NULL,
  "indicator" ecovadis_indicator NOT NULL,
  "question" varchar(2000) NOT NULL,
  "questionName" varchar(255) NOT NULL,
  "sort" integer NOT NULL DEFAULT 0,
  "createdAt" timestamp NOT NULL DEFAULT now()
);

-- Create project_ecovadis_question table
CREATE TABLE IF NOT EXISTS project_ecovadis_question (
  "id" uuid PRIMARY KEY NOT NULL DEFAULT public.uuid_generate_v4(),
  "questionId" uuid NOT NULL,
  "projectId" uuid NOT NULL,
  "impact" impact_score,
  "status" varchar(50),
  "createdAt" timestamp NOT NULL DEFAULT now()
);

-- Create project_ecovadis_gaps table
CREATE TABLE IF NOT EXISTS project_ecovadis_gaps (
  "id" uuid PRIMARY KEY NOT NULL DEFAULT public.uuid_generate_v4(),
  "questionId" uuid NOT NULL,
  "projectId" uuid NOT NULL,
  "gaps" text,
  "documents" varchar(255)[],
  "resolved" boolean DEFAULT false,
  "createdAt" timestamp NOT NULL DEFAULT now()
);

-- Create ecovadis_answer_option table
CREATE TABLE IF NOT EXISTS ecovadis_answer_option (
  "id" uuid PRIMARY KEY NOT NULL DEFAULT public.uuid_generate_v4(),
  "questionId" uuid NOT NULL,
  "issueTitle" text NOT NULL,
  "instructions" text,
  "sort" integer NOT NULL DEFAULT 0,
  "createdAt" timestamp NOT NULL DEFAULT now()
);

-- Create project_ecovadis_answer table
CREATE TABLE IF NOT EXISTS project_ecovadis_answer (
  "id" uuid PRIMARY KEY NOT NULL DEFAULT public.uuid_generate_v4(),
  "projectId" uuid NOT NULL,
  "optionId" uuid NOT NULL,
  "response" text,
  "createdAt" timestamp NOT NULL DEFAULT now()
);

-- Create project_ecovadis_linked_document_chunks table
CREATE TABLE IF NOT EXISTS project_ecovadis_linked_document_chunks (
  "id" serial PRIMARY KEY,
  "answerId" uuid NOT NULL,
  "documentChunkId" uuid NOT NULL,
  "comment" text,
  "createdAt" timestamp NOT NULL DEFAULT now()
);

-- Add foreign key constraints
ALTER TABLE document_version 
  ADD CONSTRAINT "FK_document_version_document" 
  FOREIGN KEY ("documentId") REFERENCES document("id") ON DELETE CASCADE,
  ADD CONSTRAINT "FK_document_version_ancestor" 
  FOREIGN KEY ("ancestor") REFERENCES document_version("id") ON DELETE CASCADE;

ALTER TABLE project_ecovadis_theme
  ADD CONSTRAINT "FK_project_ecovadis_theme_theme" 
  FOREIGN KEY ("themeId") REFERENCES ecovadis_theme("id") ON DELETE CASCADE,
  ADD CONSTRAINT "FK_project_ecovadis_theme_project" 
  FOREIGN KEY ("projectId") REFERENCES project("id") ON DELETE CASCADE;

ALTER TABLE ecovadis_question
  ADD CONSTRAINT "FK_question_theme" 
  FOREIGN KEY ("themeId") REFERENCES ecovadis_theme("id") ON DELETE CASCADE;

ALTER TABLE project_ecovadis_question
  ADD CONSTRAINT "FK_project_ecovadis_question_question" 
  FOREIGN KEY ("questionId") REFERENCES ecovadis_question("id") ON DELETE CASCADE,
  ADD CONSTRAINT "FK_project_ecovadis_question_project" 
  FOREIGN KEY ("projectId") REFERENCES project("id") ON DELETE CASCADE;

ALTER TABLE project_ecovadis_gaps
  ADD CONSTRAINT "FK_project_ecovadis_gaps_question" 
  FOREIGN KEY ("questionId") REFERENCES ecovadis_question("id") ON DELETE CASCADE,
  ADD CONSTRAINT "FK_project_ecovadis_gaps_project" 
  FOREIGN KEY ("projectId") REFERENCES project("id") ON DELETE CASCADE;

ALTER TABLE ecovadis_answer_option
  ADD CONSTRAINT "FK_ecovadis_answer_option_question" 
  FOREIGN KEY ("questionId") REFERENCES ecovadis_question("id") ON DELETE CASCADE;

ALTER TABLE project_ecovadis_answer
  ADD CONSTRAINT "FK_ecovadis_answer_project" 
  FOREIGN KEY ("projectId") REFERENCES project("id") ON DELETE CASCADE,
  ADD CONSTRAINT "FK_ecovadis_answer_option" 
  FOREIGN KEY ("optionId") REFERENCES ecovadis_answer_option("id") ON DELETE CASCADE;

ALTER TABLE project_ecovadis_linked_document_chunks
  ADD CONSTRAINT "FK_linked_chunks_answer" 
  FOREIGN KEY ("answerId") REFERENCES project_ecovadis_answer("id") ON DELETE CASCADE,
  ADD CONSTRAINT "FK_linked_chunks_document_chunk" 
  FOREIGN KEY ("documentChunkId") REFERENCES document_chunk("id") ON DELETE CASCADE;

-- Create indexes for better performance
CREATE INDEX idx_document_version_document ON document_version("documentId");
CREATE INDEX idx_ecovadis_question_theme ON ecovadis_question("themeId");
CREATE INDEX idx_project_ecovadis_theme_project ON project_ecovadis_theme("projectId");
CREATE INDEX idx_project_ecovadis_theme_theme ON project_ecovadis_theme("themeId");
CREATE INDEX idx_project_ecovadis_question_project ON project_ecovadis_question("projectId");
CREATE INDEX idx_project_ecovadis_question_question ON project_ecovadis_question("questionId");
CREATE INDEX idx_project_ecovadis_gaps_project ON project_ecovadis_gaps("projectId");
CREATE INDEX idx_project_ecovadis_gaps_question ON project_ecovadis_gaps("questionId");
CREATE INDEX idx_ecovadis_answer_option_question ON ecovadis_answer_option("questionId");
CREATE INDEX idx_project_ecovadis_answer_project ON project_ecovadis_answer("projectId");
CREATE INDEX idx_project_ecovadis_linked_document_chunks_answer ON project_ecovadis_linked_document_chunks("answerId");
CREATE INDEX idx_project_ecovadis_linked_document_chunks_chunk ON project_ecovadis_linked_document_chunks("documentChunkId");