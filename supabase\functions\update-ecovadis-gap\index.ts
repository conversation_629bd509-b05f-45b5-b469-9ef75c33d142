
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { getSupabaseClient } from "../_shared/supabaseClient.ts";
import { corsHeaders } from "../_shared/cors.ts";

interface UpdateGapRequest {
  gapId: string;
  isComplete?: boolean;
  assigneeId?: string | null;
}

serve(async (req) => {
  // Handle CORS
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Get the request body
    const { gapId, isComplete, assigneeId } = await req.json() as UpdateGapRequest;

    // Validate required parameters
    if (!gapId) {
      return new Response(
        JSON.stringify({ error: "Gap ID is required" }),
        { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    // Initialize Supabase client with service role
    const supabase = getSupabaseClient(true);

    // Build the update object based on provided parameters
    const updateData: any = {};
    if (isComplete !== undefined) {
      updateData.resolved = isComplete;
    }
    if (assigneeId !== undefined) {
      updateData.assigneeId = assigneeId;
    }

    // Update the gap in the database
    const { error } = await supabase
      .from('project_ecovadis_gaps')
      .update(updateData)
      .eq('id', gapId);

    if (error) {
      console.error('Error updating gap:', error);
      return new Response(
        JSON.stringify({ error: `Failed to update gap: ${error.message}` }),
        { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    // Return success response
    return new Response(
      JSON.stringify({ success: true }),
      { status: 200, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  } catch (err) {
    console.error('Error processing request:', err);
    return new Response(
      JSON.stringify({ error: `Internal server error: ${err.message}` }),
      { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  }
});
