
import { cn } from "@/lib/utils";
import React, { useRef, useState } from "react";
import { motion } from "framer-motion";
import { Upload } from "lucide-react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";

const mainVariant = {
  initial: {
    x: 0,
    y: 0,
  },
  animate: {
    x: 20,
    y: -20,
    opacity: 0.9,
  },
};

interface FileUploadProps {
  onChange?: (files: File[]) => void;
  acceptedFileTypes?: string[];
  acceptedFileTypesMessage?: string;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onChange,
  acceptedFileTypes,
  acceptedFileTypesMessage = "Only specific file types are allowed",
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (newFiles: File[]) => {
    if (acceptedFileTypes && acceptedFileTypes.length > 0) {
      const validFiles = newFiles.filter(file => {
        const fileExtension = file.name.split('.').pop()?.toLowerCase();
        return fileExtension && acceptedFileTypes.includes(`.${fileExtension}`);
      });
      
      if (validFiles.length !== newFiles.length) {
        toast.error("Invalid file format", {
          description: acceptedFileTypesMessage
        });
        
        if (validFiles.length === 0) {
          return;
        }
        
        if (onChange) {
          onChange(validFiles);
        }
        return;
      }
    }
    
    if (onChange) {
      onChange(newFiles);
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const { getRootProps, isDragActive } = useDropzone({
    multiple: false,
    noClick: true,
    onDrop: handleFileChange,
    onDropRejected: (error) => {
      console.log(error);
      toast.error("Invalid file format", {
        description: acceptedFileTypesMessage
      });
    },
    accept: acceptedFileTypes ? acceptedFileTypes.reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>) : undefined
  });

  const acceptedTypesString = acceptedFileTypes?.join(', ') || 'All files';

  return (
    <div className="w-full" {...getRootProps()}>
      <motion.div
        onClick={handleClick}
        whileHover="animate"
        className="p-10 group/file block rounded-lg cursor-pointer w-full relative overflow-hidden"
      >
        <input
          ref={fileInputRef}
          id="file-upload-handle"
          type="file"
          onChange={(e) => handleFileChange(Array.from(e.target.files || []))}
          className="hidden"
          accept={acceptedFileTypes?.join(',')}
        />
        <div className="absolute inset-0 [mask-image:radial-gradient(ellipse_at_center,white,transparent)]">
          <GridPattern />
        </div>
        <div className="flex flex-col items-center justify-center">
          <p className="relative z-20 font-pangea font-bold text-glacier-darkBlue text-base">
            Upload file
          </p>
          <p className="relative z-20 font-pangea font-normal text-gray-500 text-base mt-2">
            Drag or drop your files here or click to upload
          </p>
          {acceptedFileTypes && acceptedFileTypes.length > 0 && (
            <p className="relative z-20 font-pangea font-normal text-gray-500 text-sm mt-1">
              Accepted formats: {acceptedTypesString}
            </p>
          )}
          <div className="relative w-full mt-10 max-w-xl mx-auto">
            <motion.div
              layoutId="file-upload"
              variants={mainVariant}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 20,
              }}
              className={cn(
                "relative group-hover/file:shadow-md z-40 bg-white dark:bg-neutral-900 flex items-center justify-center h-32 mt-4 w-full max-w-[8rem] mx-auto rounded-md",
                "shadow-sm"
              )}
            >
              {isDragActive ? (
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-glacier-darkBlue flex flex-col items-center"
                >
                  Drop it
                  <Upload className="h-4 w-4 text-glacier-darkBlue dark:text-neutral-400" />
                </motion.p>
              ) : (
                <Upload className="h-4 w-4 text-glacier-darkBlue dark:text-neutral-300" />
              )}
            </motion.div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export function GridPattern() {
  const columns = 41;
  const rows = 11;
  return (
    <div className="flex bg-gray-100 dark:bg-neutral-900 flex-shrink-0 flex-wrap justify-center items-center gap-x-px gap-y-px scale-105">
      {Array.from({ length: rows }).map((_, row) =>
        Array.from({ length: columns }).map((_, col) => {
          const index = row * columns + col;
          return (
            <div
              key={`${col}-${row}`}
              className={`w-10 h-10 flex flex-shrink-0 rounded-[2px] ${
                index % 2 === 0
                  ? "bg-gray-50 dark:bg-neutral-950"
                  : "bg-gray-50 dark:bg-neutral-950 shadow-[0px_0px_1px_3px_rgba(255,255,255,1)_inset] dark:shadow-[0px_0px_1px_3px_rgba(0,0,0,1)_inset]"
              }`}
            />
          );
        })
      )}
    </div>
  );
}
