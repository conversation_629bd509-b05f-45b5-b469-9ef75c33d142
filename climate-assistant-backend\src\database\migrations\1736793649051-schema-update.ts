import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1736793649051 implements MigrationInterface {
  name = 'SchemaUpdate1736793649051';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ADD "conditional" boolean NOT NULL DEFAULT false`,
    );

    await queryRunner.query(
      `UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-1_02';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-1_03';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_01';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_02';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_03';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_05';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_06';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_10';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_11';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_12';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_13';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_14';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_15';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_16';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_17';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_20';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_21';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_22';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_23';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_24';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_25';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_26';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'BP-2_27';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-1_05';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-1_10';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-1_11';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-1_12';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-1_13';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-1_14';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-1_15';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-1_16';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-1_17';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-1_18';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-1_19';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-1_20';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-1_24';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-2_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-2_09';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-2_10';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'SBM-2_11';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'IRO-1_13';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'IRO-2_03';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'IRO-2_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'IRO-2_05';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'IRO-2_06';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'IRO-2_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'IRO-2_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'IRO-2_09';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'IRO-2_10';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'IRO-2_11';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'IRO-2_12';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-1_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-1_09';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-1_10';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-1_11';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-1_16';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-3_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_02';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_03';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_05';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_06';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_09';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_10';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_11';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_12';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_13';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_14';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_15';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_16';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-4_17';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-5_10';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-5_11';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-5_12';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-5_13';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-5_14';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-5_15';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-5_16';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-5_17';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-5_18';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-5_19';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-5_20';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-5_21';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-5_22';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-5_23';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-6_14';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-6_16';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-6_33';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-6_34';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-6_35';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-7_03';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-7_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-7_05';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-7_06';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-7_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-7_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-7_09';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-7_10';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-7_11';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-7_20';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-7_22';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-7_23';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-7_24';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-7_25';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-8_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-8_09';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-9_13';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-9_30';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-9_31';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-9_32';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-9_33';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-9_34';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E1-9_35';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E2-2_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E2-3_06';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E2-3_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E2-3_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E2-3_11';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E2-4_15';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E2-4_16';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E2-4_17';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E2-4_18';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E2-4_19';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E2-4_20';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E3-1_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E3-1_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4.IRO-1_11';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4.IRO-1_12';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4.IRO-1_13';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-3_03';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-3_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-3_05';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-3_06';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-3_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-3_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-3_11';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-4_02';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-4_03';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-4_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-4_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_01';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_02';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_03';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_05';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_06';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_09';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_10';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_11';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_12';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_13';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_14';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_15';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_16';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_17';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_18';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_19';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_20';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_21';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_22';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_23';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_24';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_25';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_26';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E4-5_27';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'E5-4_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1.SBM-3_03';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1.SBM-3_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-2_02';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-2_03';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-2_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-2_05';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-2_06';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-2_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-2_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-2_09';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-2_15';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-3_09';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-3_10';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-3_11';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-4_19';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-6_16';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-7_09';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-7_10';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-9_06';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-10_02';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-11_06';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-11_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-11_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-11_09';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-11_10';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-11_11';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-16_03';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-17_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S1-17_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S2.SBM-3_05';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S2.SBM-3_06';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S2-2_02';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S2-2_03';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S2-2_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S2-2_05';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S2-2_06';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S2-2_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S2-2_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S2-2_09';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S2-3_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S3.SBM-3_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S3.SBM-3_05';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S3-2_02';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S3-2_03';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S3-2_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S3-2_05';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S3-2_06';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S3-2_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S3-2_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S3-2_09';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S3-3_16';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S3-3_17';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S4.SBM-3_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S4.SBM-3_05';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S4-2_02';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S4-2_03';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S4-2_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S4-2_05';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S4-2_06';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S4-2_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S4-2_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'S4-3_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'G1-1_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'G1-1_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'G1-1_09';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'G1-1_12';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'G1-3_03';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'G1-3_04';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'G1-5_01';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'G1-5_07';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'G1-5_08';
        UPDATE "esrs_datapoint" SET "conditional" = true WHERE "datapointId" = 'G1-5_10';`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" DROP COLUMN "conditional"`,
    );
  }
}
