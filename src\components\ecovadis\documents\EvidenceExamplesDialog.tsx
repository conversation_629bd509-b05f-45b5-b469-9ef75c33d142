
import { 
  Dialog,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription
} from "@/components/ui/dialog";
import { EvidenceContent } from "./EvidenceContent";

interface EvidenceExamplesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  evidence: string | null;
}

export const EvidenceExamplesDialog = ({ 
  open, 
  onOpenChange, 
  evidence 
}: EvidenceExamplesDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>
            Examples of documents to attach
          </DialogTitle>
          <DialogDescription>
            The following documents can be used as evidence for this option
          </DialogDescription>
        </DialogHeader>
        {evidence && <EvidenceContent content={evidence} />}
      </DialogContent>
    </Dialog>
  );
};
