import { Repository } from 'typeorm';
import { ChatHistory } from './entities/chat-history.entity';
import { User } from '../users/entities/user.entity';
import { ChatMessage } from './entities/chat.message.entity';
import { HistoryCreationDto } from './entities/history-creation.dto';
import { UsersService } from '../users/users.service';
import { ChatGptService } from '../llm/chat-gpt.service';
import { ChatMessageDto, HistoryUpdateDto } from './entities/chat.message.dto';
import { KnowledgeBaseService } from '../knowledge-base/knowledge-base.service';
import { PerplexityService } from '../util/perplexity.service';
import { InitiativeSuggestionService } from './initiative-suggestion.service';
import { InitiativeDetailService } from './initiative-detail.service';
import { SearchEngineTool } from '../util/search-engine.tool';
export declare class ChatService {
    private chatHistoryRepository;
    private chatMessageRepository;
    private usersService;
    private readonly chatGptService;
    private readonly knowledgeBaseService;
    private readonly perplexityService;
    private readonly initiativeSuggestionService;
    private readonly initiativeDetailService;
    private readonly searchEngineTool;
    constructor(chatHistoryRepository: Repository<ChatHistory>, chatMessageRepository: Repository<ChatMessage>, usersService: UsersService, chatGptService: ChatGptService, knowledgeBaseService: KnowledgeBaseService, perplexityService: PerplexityService, initiativeSuggestionService: InitiativeSuggestionService, initiativeDetailService: InitiativeDetailService, searchEngineTool: SearchEngineTool);
    getChats(userId: string): Promise<ChatHistory[]>;
    getChatHistory(id: string): Promise<{
        messages: ChatMessage[];
        id: string;
        user: User;
        title: string;
        createdAt: Date;
    }>;
    updateChatHistory(id: string, updatedHistory: HistoryUpdateDto): Promise<import("typeorm").UpdateResult>;
    deleteChatHistory(id: string): Promise<import("typeorm").DeleteResult>;
    createEmptyHistory(userId: string, type: HistoryCreationDto['type']): Promise<ChatHistory>;
    addMessageToHistory(historyId: string, messages: ChatMessageDto[]): Promise<void>;
    queryCompanyRelatedVectors({ query, userId, count, threshold, }: {
        query: string;
        userId: User['id'];
        count: number;
        threshold: number;
    }): Promise<{
        content: string;
        similarity: number;
    }[]>;
    queryInternalRelatedVectors({ query, count, threshold, }: {
        query: string;
        count: number;
        threshold: number;
    }): Promise<{
        content: string;
        similarity: number;
    }[]>;
    createMessageStream({ messages, onMessage, userId, }: {
        messages: ChatMessageDto[];
        onMessage: (chunk: string) => void;
        userId: User['id'];
    }): Promise<string>;
    generateHistoryTitle(id: string, question: string, answer: string): Promise<void>;
    storeTitle(id: string, title: string): Promise<import("typeorm").UpdateResult>;
}
