-- First, add the auth_id column to the user table
ALTER TABLE public.user 
ADD COLUMN IF NOT EXISTS auth_id UUID REFERENCES auth.users(id);

-- Create function to handle new auth user creation
-- This will check if the email exists in the user table before creating a mapping
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
DECLARE
    existing_user_id UUID;
BEGIN
    -- Check if the email already exists in the user table
    SELECT id INTO existing_user_id 
    FROM public.user 
    WHERE email = new.email;
    
    IF existing_user_id IS NULL THEN
        -- If no invitation exists (no user record), log and do nothing
        -- Optionally, you could raise an exception here to prevent the auth creation
        RAISE EXCEPTION 'User with email % not found in invitation list', new.email;
    ELSE
        -- Update the existing user with the auth_id
        UPDATE public.user
        SET auth_id = new.id,
            name = COALESCE(new.raw_user_meta_data->>'name', public.user.name)
        WHERE id = existing_user_id;
    END IF;
    
    RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to verify and map new auth users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Create function to sync email updates
CREATE OR REPLACE FUNCTION public.handle_user_update() 
RETURNS TRIGGER AS $$
BEGIN
    -- Only update users that have been mapped
    UPDATE public.user
    SET email = new.email,
        name = COALESCE(new.raw_user_meta_data->>'name', public.user.name)
    WHERE auth_id = new.id;
    RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to keep profile in sync with auth updates
DROP TRIGGER IF EXISTS on_auth_user_updated ON auth.users;
CREATE TRIGGER on_auth_user_updated
    AFTER UPDATE ON auth.users
    FOR EACH ROW EXECUTE PROCEDURE public.handle_user_update();