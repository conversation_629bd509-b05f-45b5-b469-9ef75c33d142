import { USER_ROLE } from "@/constants/workspaceConstants";

export function isCurrentUserAllowedToInviteUser(role: USER_ROLE | undefined) {
  if (
    role &&
    [
      USER_ROLE.SuperAdmin,
      USER_ROLE.WorkspaceAdmin,
      USER_ROLE.AiContributor,
    ].includes(role)
  ) {
    return true;
  }
  return false;
}

export function getRolesAllowedToImport(role: USER_ROLE | undefined) {
  // TODO: AiContributor's acess should be limited
  if (role === USER_ROLE.AiContributor) {
    return [USER_ROLE.WorkspaceAdmin, USER_ROLE.Contributor];
  }
  if (role === USER_ROLE.WorkspaceAdmin) {
    return [USER_ROLE.WorkspaceAdmin, USER_ROLE.Contributor];
  }
  if (role === USER_ROLE.SuperAdmin) return Object.values(USER_ROLE);
}

export function canCreateProject(role: USER_ROLE | undefined) {
  if (
    role &&
    [
      USER_ROLE.SuperAdmin,
      USER_ROLE.WorkspaceAdmin,
      USER_ROLE.AiContributor,
    ].includes(role)
  ) {
    return true;
  }
  return false;
}

export function canUploadSustainabilityIssues(role: USER_ROLE | undefined) {
  if (
    role &&
    [
      USER_ROLE.SuperAdmin,
      USER_ROLE.WorkspaceAdmin,
      USER_ROLE.AiContributor,
    ].includes(role)
  ) {
    return true;
  }
  return false;
}
