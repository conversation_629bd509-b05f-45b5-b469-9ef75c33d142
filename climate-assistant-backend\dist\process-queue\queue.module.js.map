{"version": 3, "file": "queue.module.js", "sourceRoot": "", "sources": ["../../src/process-queue/queue.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,+CAAqD;AACrD,6DAA0D;AAC1D,mDAKyB;AACzB,wCAA8C;AAC9C,iEAA8D;AAC9D,iHAA4G;AAC5G,wDAAqD;AACrD,oFAAgF;AAChF,6EAAyE;AACzE,2DAAwD;AAoCjD,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;CAAG,CAAA;AAArB,gDAAkB;6BAAlB,kBAAkB;IAlC9B,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YAEP,wBAAe,CAAC,UAAU,CAAC;gBACzB,IAAI,EAAE,mBAAY,CAAC,eAAe;gBAClC,OAAO,EAAE,yBAAW;aACrB,CAAC;YACF,wBAAe,CAAC,UAAU,CAAC;gBACzB,IAAI,EAAE,mBAAY,CAAC,cAAc;gBACjC,OAAO,EAAE,yBAAW;aACrB,CAAC;YACF,wBAAe,CAAC,UAAU,CAAC;gBACzB,IAAI,EAAE,mBAAY,CAAC,mBAAmB;gBACtC,OAAO,EAAE,yBAAW;aACrB,CAAC;YACF,wBAAe,CAAC,UAAU,CAAC;gBACzB,IAAI,EAAE,mBAAY,CAAC,eAAe;gBAClC,OAAO,EAAE,yBAAW;aACrB,CAAC;YACF,4BAAY;YACZ,gCAAc;YACd,0BAAW;YACX,8DAA4B;YAC5B,iDAAsB;YACtB,uCAAiB;SAClB;QACD,SAAS,EAAE;YACT,wCAAwB;YACxB,qCAAqB;YACrB,4CAA4B;YAC5B,wCAAwB;SACzB;QACD,OAAO,EAAE,EAAE;KACZ,CAAC;GACW,kBAAkB,CAAG"}