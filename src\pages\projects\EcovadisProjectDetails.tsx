import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { EmptyEcovadisState } from '@/components/projects/EmptyEcovadisState';
import { MainLayout } from '@/components/MainLayout';
import { ProjectSidebar } from '@/components/ecovadis/project-details/ProjectSidebar';
import { ProjectHeader } from '@/components/ecovadis/project-details/ProjectHeader';
import { QuestionDetailView } from '@/components/ecovadis/project-details/QuestionDetailView';
import { toast } from '@/components/ui/use-toast';
import { EcovadisQuestion, GapItem } from '@/types/ecovadis';
import { EcovadisProjectLoadingState } from '@/components/ecovadis/project-details/EcovadisProjectLoadingState';
import { useEcovadisProject } from '@/hooks/useEcovadisProject';
import { useEcovadisQuestionMutations } from '@/hooks/useEcovadisQuestion';
import { useEcovadisExport } from '@/hooks/useEcovadisExport';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useDocuments } from '@/hooks/useDocuments';

const queryClient = new QueryClient();

const EcovadisProjectDetailsWrapper = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <EcovadisProjectDetailsContent />
    </QueryClientProvider>
  );
};

const EcovadisProjectDetailsContent = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const questionIdFromUrl = searchParams.get('questionId');
  const [selectedQuestionId, setSelectedQuestionId] = useState<string>(questionIdFromUrl || '');
  const [expandedTopics, setExpandedTopics] = useState<string[]>([]);
  const [expandedIndicators, setExpandedIndicators] = useState<string[]>([]);
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [updateModalOpen, setUpdateModalOpen] = useState(false);

  const { data: projectData, isLoading, isError, error, refetch } = useEcovadisProject(id);
  const { uploadedFiles: documents, loading: documentsLoading } = useDocuments();
  
  const { updateQuestion, markGapComplete, toggleOption } = useEcovadisQuestionMutations(id);
  const { exportQuestionnaire, isExporting } = useEcovadisExport();

  useEffect(() => {
    if (projectData?.questions) {
      const uniqueTopics = [...new Set(projectData.questions.map(q => q.topic))];
      setExpandedTopics(uniqueTopics);
    }
  }, [projectData]);

  // Effect to handle questionId from URL
  useEffect(() => {
    if (questionIdFromUrl && projectData?.questions) {
      // Find if the question exists in our data
      const questionExists = projectData.questions.find(q => q.questionId === questionIdFromUrl);
      if (questionExists) {
        setSelectedQuestionId(questionIdFromUrl);
        
        // Ensure the topic for this question is expanded
        if (questionExists.topic && !expandedTopics.includes(questionExists.topic)) {
          setExpandedTopics(prev => [...prev, questionExists.topic]);
        }
        
        // Auto-expand the indicator for this question
        if (questionExists.indicator) {
          const indicatorKey = `${questionExists.topic}-${questionExists.indicator}`;
          if (!expandedIndicators.includes(indicatorKey)) {
            setExpandedIndicators(prev => [...prev, indicatorKey]);
          }
        }
      }
    }
  }, [questionIdFromUrl, projectData, expandedTopics, expandedIndicators]);

  const selectedQuestion = selectedQuestionId && projectData?.questions 
    ? projectData.questions.find(q => q.questionId === selectedQuestionId) 
    : null;

  const gaps = selectedQuestion?.gaps || [];

  useEffect(() => {
    if (projectData?.questions && projectData.questions.length > 0 && !selectedQuestionId) {
      setSelectedQuestionId(projectData.questions[0].questionId);
    }
  }, [projectData, selectedQuestionId]);

  const handleToggleTopic = (topic: string) => {
    setExpandedTopics(prev => 
      prev.includes(topic) 
        ? prev.filter(t => t !== topic) 
        : [...prev, topic]
    );
  };

  const handleToggleIndicator = (indicatorKey: string) => {
    setExpandedIndicators(prev => 
      prev.includes(indicatorKey) 
        ? prev.filter(i => i !== indicatorKey) 
        : [...prev, indicatorKey]
    );
  };

  const handleSelectQuestion = (questionId: string) => {
    setSelectedQuestionId(questionId);
    // Update the URL query parameter
    navigate(`/ecovadis-project/${id}?questionId=${questionId}`, { replace: true });
  };


  const handleMarkGapComplete = async (gapId: string, isComplete: boolean) => {
    markGapComplete.mutate({ gapId, isComplete });
  };

  const handleMarkQuestionComplete = async () => {
    if (!selectedQuestion) return;
    
    const currentStatus = selectedQuestion.status || 'pending';
    const newStatus = currentStatus === 'complete' ? 'pending' : 'complete';
    
    updateQuestion.mutate({
      id: selectedQuestion.projectQuestionId,
      status: newStatus
    });
  };

  const handleUpdateQuestion = async (updatedQuestion: EcovadisQuestion) => {
    updateQuestion.mutate({
      id: updatedQuestion.projectQuestionId,
      status: updatedQuestion.status,
      impact: updatedQuestion.impact
      // We don't update estimatedScore from frontend as it comes from the API
    });
  };

  const handleUpdateOptions = async (updatedOptions: any[]) => {
    toast({
      title: "Options updated",
      description: "Your changes have been saved.",
    });
  };

  const handleUploadQuestionnaire = () => {
    toast({
      title: "Questionnaire uploaded",
      description: "Your EcoVadis questionnaire has been uploaded successfully.",
    });
    
    refetch();
  };

  const handleSetQuestions = (newQuestions: EcovadisQuestion[]) => {
    console.log("Updating questions state:", newQuestions.length);
  };

  const handleExportQuestionnaire = () => {
    if (id && projectData?.project?.name) {
      exportQuestionnaire({ projectId: id, projectName: projectData.project.name });
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <EcovadisProjectLoadingState />
      </MainLayout>
    );
  }

  if (isError) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center min-h-screen p-4">
          <h1 className="text-2xl font-bold mb-4">Error Loading Project</h1>
          <p className="text-gray-500 mb-4">{error instanceof Error ? error.message : "Unknown error"}</p>
          <button 
            onClick={() => refetch()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 mr-4"
          >
            Try Again
          </button>
          <button 
            onClick={() => navigate('/projects')}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Return to Projects
          </button>
        </div>
      </MainLayout>
    );
  }

  if (!projectData) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center min-h-screen p-4">
          <h1 className="text-2xl font-bold mb-4">Project Not Found</h1>
          <p className="text-gray-500 mb-4">The project you are looking for does not exist or has been deleted.</p>
          <button 
            onClick={() => navigate('/projects')}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Return to Projects
          </button>
        </div>
      </MainLayout>
    );
  }

  if (!projectData.hasQuestionnaire) {
    return (
      <MainLayout>
        <EmptyEcovadisState 
          onUploadQuestionnaire={handleUploadQuestionnaire} 
          projectId={id || ''}
        />
      </MainLayout>
    );
  }

  const { project, questions } = projectData;
  const totalQuestions = questions.length;
  const completedQuestions = questions.filter(q => q.status === 'complete').length;
  const progressPercentage = totalQuestions > 0 ? Math.round(completedQuestions / totalQuestions * 100) : 0;

  return (
    <MainLayout>
      <div className="flex h-[calc(100vh-4rem)]">
        <ProjectSidebar
          projectId={id || ''}
          projectName={project.name}
          questions={questions}
          expandedTopics={expandedTopics}
          expandedIndicators={expandedIndicators}
          selectedQuestionId={selectedQuestionId}
          progressPercentage={progressPercentage}
          completedQuestions={completedQuestions}
          totalQuestions={totalQuestions}
          onToggleTopic={handleToggleTopic}
          onToggleIndicator={handleToggleIndicator}
          onSelectQuestion={handleSelectQuestion}
        />
        
        <div className="flex-1 overflow-auto">
          <ProjectHeader 
            projectName={project.name}
            projectId={project.id}
            deadline={project.deadline}
            onOpenUpdateModal={() => setUpdateModalOpen(true)}
            onOpenExportModal={handleExportQuestionnaire}
          />
          
          <div className="p-6">
            {selectedQuestion && (
              <QuestionDetailView
                selectedQuestion={selectedQuestion}
                gaps={gaps as any[]}
                handleMarkGapComplete={handleMarkGapComplete}
                handleMarkQuestionComplete={handleMarkQuestionComplete}
                handleUpdateQuestion={handleUpdateQuestion}
                handleUpdateOptions={handleUpdateOptions}
                setQuestions={handleSetQuestions}
                documents={documents || []}
              />
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default EcovadisProjectDetailsWrapper;
