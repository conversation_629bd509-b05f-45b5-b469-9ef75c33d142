import { Button } from './ui/button';
import { toast } from './ui/use-toast';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { deleteDocumentUpload } from '@/api/workspace-settings/workspace-settings.api';
import { useDocumentRefetch } from '@/context/documentsContext';

export function DeleteDocumentDialog({
  id,
  open,
  setOpen,
}: {
  id: string;
  open: boolean;
  setOpen: (open: boolean) => void;
}) {
  const { refetchDocuments } = useDocumentRefetch();
  const handleDeleteFile = async (id: string) => {
    try {
      await deleteDocumentUpload(id);
      toast({
        variant: 'success',
        title: 'Success',
        description: 'File deleted successfully',
      });
      refetchDocuments();
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message,
      });
    }
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Delete Document</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this document?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="destructive" onClick={() => handleDeleteFile(id)}>
            Confirm Delete
          </Button>
          <Button onClick={() => setOpen(false)} variant="secondary">
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
