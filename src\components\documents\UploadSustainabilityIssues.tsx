import { useState } from 'react';
import { UploadIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { UploadSustainabilityIssuesForm } from './UploadSustainabilityIssuesForm';

export function UploadSustainabilityIssuesButton({
  refreshData,
  projectId
}: {
  refreshData: () => void;
  projectId: string;
}) {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <Button
        variant="darkBlue"
        className="rounded-full w-fit"
        onClick={() => setIsOpen(true)}
      >
        <UploadIcon className="h-4 w-4 mr-2" />
        Upload Sustainability Issues
      </Button>
      <DialogContent className="mt-2 bg-white/90 backdrop-blur-2xl max-w-max">
        <DialogHeader>
          <DialogTitle>Upload Sustainability Issues</DialogTitle>
        </DialogHeader>
        <UploadSustainabilityIssuesForm
          callback={() => {
            refreshData();
            setIsOpen(false);
          }}
          projectId={projectId}
        />
      </DialogContent>
    </Dialog>
  );
}
