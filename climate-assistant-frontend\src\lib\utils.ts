import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { NavigateFunction } from 'react-router-dom';
import axios from 'axios';
import { marked } from 'marked';

import { IUser } from '@/types/user';
import { USER_ROLE } from '@/constants/workspaceConstants';
import { DatapointDocumentChunkMap } from '@/types/project';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const setupAxiosInterceptors = (navigate: NavigateFunction) => {
  axios.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response && error.response.status === 401) {
        handle401(navigate, window.location.pathname);
      }
      return Promise.reject(error);
    }
  );
};

export const formatDate = (date: string | Date | null | undefined) => {
  if (!date) return '';
  try {
    return new Date(date).toLocaleDateString();
  } catch {
    return '';
  }
};

export const handle401 = (navigate: NavigateFunction, path: string) => {
  switch (path) {
    case '/dashboard':
      // If the path is `/dashboard`, navigate to `/login`
      navigate('/login', { replace: true });
      break;
    case '/login':
      // No action needed if already on `/login`
      break;
    case '/reset-password':
      break;
    default:
      // For any other path, navigate to `/dashboard`
      navigate('/dashboard', { replace: true });
      break;
  }
};

// TODO: Remove as soon as https://github.com/compulim/react-scroll-to-bottom/issues/140 is resolved
const consoleError = console.error;
const SURPRESSED_ERRORS = [
  'Warning: %s: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead.%s',
];

console.error = function filterErrors(msg, ...args) {
  try {
    if (!SURPRESSED_ERRORS.some((entry) => msg.includes(entry))) {
      consoleError(msg, ...args);
    }
  } catch (e) {
    consoleError(msg, ...args);
  }
};

export const validateSystemPrompt = (prompt: string): any => {
  if (prompt.startsWith('!!{')) {
    try {
      return JSON.parse(prompt.substring(2));
    } catch (error) {
      throw new Error('Invalid process request');
    }
  }
  return null;
};

export function xor(arr: boolean[]): boolean {
  const allTrue = arr.every((value) => value === true);
  const allFalse = arr.every((value) => value === false);
  return !(allTrue || allFalse);
}

export function parseMarkdown(text: string) {
  return marked
    .parse(text)
    .toString()
    .replace(/<ul>/g, '<ul class="ml-6 list-disc">')
    .replace(/<ol>/g, '<ol class="ml-6 list-decimal">')
    .replace(
      /<table>/g,
      '<table class="table-auto border-collapse w-full text-left">'
    )
    .replace(/<thead>/g, '<thead class="bg-slate-200">')
    .replace(/<th>/g, '<th class="px-4 py-2 font-medium text-slate-700">')
    .replace(/<tbody>/g, '<tbody class="bg-white">')
    .replace(/<td>/g, '<td class="border px-4 py-2">');
}

export function permitOverride() {
  const queryParams = new URLSearchParams(window.location.search);
  const unlocked = queryParams.get('unlocked');
  return unlocked === 'true';
}

export function userHasRequiredRole(
  requiredRoles: USER_ROLE[],
  user: IUser | null
) {
  if (user && user.userWorkspaces && user.userWorkspaces.length > 0) {
    return requiredRoles.includes(user.userWorkspaces[0].role);
  } else {
    return false;
  }
}

// Helper function to extract the first page number as an integer
export function getFirstPageNumber(pageString: string) {
  const firstPart = String(pageString).split('-')[0]; // Get the part before '-'
  const pageNumber = parseInt(firstPart, 10); // Parse it as integer
  return isNaN(pageNumber) ? 0 : pageNumber; // Fallback to 0 if parsing fails
}

export function isJson(text: string) {
  try {
    return JSON.parse(text);
  } catch (e) {
    return false;
  }
}

export function getDaysInMonth(year?: number, month?: number): number {
  if (year === undefined || month === undefined) {
    return 31;
  }
  return new Date(year, month, 0).getDate();
}

export function getActiveDocumentChunkLinksData(
  documentLinksData: DatapointDocumentChunkMap[]
) {
  if (documentLinksData && documentLinksData.length > 0) {
    return documentLinksData.filter((documentLink) => documentLink.active);
  }
  return [];
}
