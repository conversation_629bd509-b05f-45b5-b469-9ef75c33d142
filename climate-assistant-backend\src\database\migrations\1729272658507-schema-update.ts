import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1729272658507 implements MigrationInterface {
  name = 'SchemaUpdate1729272658507';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "document_chunk" ALTER COLUMN "matchingsJson" DROP NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "document_chunk" ALTER COLUMN "matchingsJson" SET NOT NULL`,
    );
  }
}
