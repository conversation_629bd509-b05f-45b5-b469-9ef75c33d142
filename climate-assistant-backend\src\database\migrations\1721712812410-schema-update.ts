import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1721712812410 implements MigrationInterface {
  name = 'SchemaUpdate1721712812410';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "knowledge_base_file_upload_chunk" DROP CONSTRAINT "FK_d906b1920691f35fa454d766439"`,
    );
    await queryRunner.query(`ALTER TABLE "knowledge_base_file_upload_chunk"
        ADD CONSTRAINT "FK_d906b1920691f35fa454d766439" FOREIGN KEY ("fileUploadId") REFERENCES "knowledge_base_file_upload" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "knowledge_base_file_upload_chunk" DROP CONSTRAINT "FK_d906b1920691f35fa454d766439"`,
    );
    await queryRunner.query(`ALTER TABLE "knowledge_base_file_upload_chunk"
        ADD CONSTRAINT "FK_d906b1920691f35fa454d766439" FOREIGN KEY ("fileUploadId") REFERENCES "file_upload" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
  }
}
