import axios from 'axios';

import { API_URL } from '@/api/apiConstants';

export const login = async (credentials: {
  email: string;
  password: string;
}) => {
  try {
    const response = await axios.post(`${API_URL}/auth/login`, {
      ...credentials,
    });
    return response.data;
  } catch (error: any) {
    if (error?.response) {
      throw error.response.data.message;
    } else {
      console.error(error); // TODO: sentry
      throw error?.message ?? 'An error occurred';
    }
  }
};

export const logout = async () => {
  const response = await axios.post(`${API_URL}/auth/logout`);
  return response.data;
};

export const fetchUserProfile = async () => {
  const response = await axios.get(`${API_URL}/auth/profile`, {
    withCredentials: true,
  });
  return response.data;
};

export const sendPasswordResetEmailMutation = async (email: string) => {
  try {
    const response = await axios.post(
      `${API_URL}/auth/request-password-reset`,
      { email },
      { withCredentials: true }
    );
    return response.data;
  } catch (error: any) {
    if (error?.response) {
      throw error.response.data.message;
    } else {
      console.error(error);
      throw error?.message ?? 'An error occurred';
    }
  }
};

export const resetPasswordMutation = async ({
  password,
  token,
}: {
  password: string;
  token: string;
}) => {
  return await axios.post(`${API_URL}/auth/password-reset-submit`, {
    password,
    token,
  });
};

export const validatePasswordResetToken = async (token: string) => {
  const response = await axios.post(
    `${API_URL}/auth/validate-password-reset-token`,
    {
      token,
    }
  );
  return response.data;
};
