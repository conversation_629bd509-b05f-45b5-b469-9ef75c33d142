import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1733129759095 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            UPDATE "document"
            SET 
              "year" = 2024,
              "esrsCategory" = ARRAY['ESRS 2', 'ESRS S', 'ESRS G', 'ESRS E']::text[],
              "documentType" = 'Other'
            WHERE "year" IS NULL OR "esrsCategory" IS NULL OR "documentType" IS NULL
          `);
  }

  public async down(): Promise<void> {}
}
