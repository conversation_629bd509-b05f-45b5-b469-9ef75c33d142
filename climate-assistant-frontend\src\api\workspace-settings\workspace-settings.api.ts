import axios from 'axios';

import { API_URL } from '@/api/apiConstants';
import { DocumentSettingsFormData } from '@/validators/file-upload';
import type { DocumentUpload, Project } from '@/types/project';
import { IUserWithStatus } from '@/types/user';
import { Workspace } from '@/types/workspace';
import { Language } from '@/types';

export interface WorkspacePayload {
  name: string;
  createdAt: Date;
  reportTextGenerationRules: string;
  userWorkspaces?: any;
  companies?: Partial<CompanyPayload>[];
  documents?: any;
  projects?: any;
}

export interface CompanyPayload {
  id: string;
  generalCompanyProfile: string;
}

export const fetchDocumentUploads = async (): Promise<DocumentUpload[]> => {
  const response = await axios.get<DocumentUpload[]>(`${API_URL}/documents`);
  return response.data;
};

export const uploadDocumentForPromptContext = async (
  formData: FormData
): Promise<{ message: string }> => {
  const response = await axios.post<{ message: string }>(
    `${API_URL}/documents/context-document-upload`,
    formData
  );
  return response.data;
};

export const uploadDocument = async (
  formData: FormData
): Promise<{ message: string }> => {
  const response = await axios.post<{ message: string }>(
    `${API_URL}/documents`,
    formData
  );
  return response.data;
};

export const updateDocumentSettings = async (
  id: string,
  data: DocumentSettingsFormData
): Promise<{ message: string }> => {
  const response = await axios.put<{ message: string }>(
    `${API_URL}/documents/${id}`,
    data
  );
  return response.data;
};

export const uploadDocumentForPageReview = async (
  formData: FormData
): Promise<{ message: string; store: string[] }> => {
  const response = await axios.post<{ message: string; store: string[] }>(
    `${API_URL}/documents/context-document-page-analysis`,
    formData
  );
  return response.data;
};

export const deleteDocumentUpload = async (
  id: string
): Promise<{ message: string }> => {
  const response = await axios.delete<{ message: string }>(
    `${API_URL}/documents/${id}`
  );
  return response.data;
};

export const downloadUploadedDocument = async (id: string): Promise<void> => {
  const response = await axios.get(`${API_URL}/documents/${id}/download`, {
    responseType: 'blob',
  });

  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement('a');
  link.href = url;

  const contentDisposition = response.headers['content-disposition'];

  const fileName = contentDisposition
    ? contentDisposition.split('filename=')[1].replace(/"/g, '')
    : undefined;

  if (!fileName) {
    throw new Error('File name corrupted');
  }

  link.setAttribute('download', fileName);
  document.body.appendChild(link);
  link.click();

  window.URL.revokeObjectURL(url);
  document.body.removeChild(link);
};

export const fetchAllMembers = async (): Promise<IUserWithStatus[]> => {
  const response = await axios.get<IUserWithStatus[]>(
    `${API_URL}/workspace/users`
  );
  return response.data;
};

export const fetchWorkspaceDetail = async (): Promise<
  Partial<WorkspacePayload>
> => {
  const response = await axios.get<Partial<WorkspacePayload>>(
    `${API_URL}/workspace/`
  );
  return response.data;
};

//Reusing same function for reinvite user, in which case role is optional
export const inviteUsersToWorkspace = async ({
  emails,
  role,
}: {
  emails: string[];
  role: string;
}): Promise<{ success: string; failure: string }> => {
  const response = await axios.post<{ success: string; failure: string }>(
    `${API_URL}/workspace/inviteUsers`,
    { emails, role }
  );
  return response.data;
};

export const updateWorkspaceDetail = async (
  workspaceDetail: Partial<WorkspacePayload>
): Promise<{ message: string }> => {
  const response = await axios.put<{ message: string }>(
    `${API_URL}/workspace/`,
    { ...workspaceDetail }
  );

  return response.data;
};

export const updateCompanyDetail = async (
  companyDetail: Partial<CompanyPayload>
): Promise<{ message: string }> => {
  const response = await axios.put<{ message: string }>(
    `${API_URL}/workspace/company`,
    { ...companyDetail }
  );

  return response.data;
};

export const switchWorkspace = async (
  workspaceId: string
): Promise<{ success: boolean }> => {
  const response = await axios.post<{ success: boolean }>(
    `${API_URL}/auth/switch-workspace`,
    { workspaceId },
    { withCredentials: true }
  );
  return response.data;
};

export const createWorkspace = async (workspaceDetail: {
  name: string;
  email: string;
  password: string;
}): Promise<{
  user: IUserWithStatus;
  workspace: Workspace;
  company: unknown;
}> => {
  const response = await axios.post<{
    user: IUserWithStatus;
    workspace: Workspace;
    company: unknown;
  }>(`${API_URL}/users/create-workspace`, workspaceDetail);
  return response.data;
};

export const createStarterProject = async (projectDetails: {
  workspaceId: string;
  userId: string;
  createProjectRequest: {
    name: string;
    primaryContentLanguage: keyof Language;
    type?: string;
  };
}): Promise<Project> => {
  const response = await axios.post<Project>(
    `${API_URL}/projects/starter`,
    projectDetails
  );
  return response.data;
};

export const fetchAllWorkspaces = async (): Promise<any> => {
  const response = await axios.get<{ success: boolean }>(
    `${API_URL}/workspace/all-workspaces`
  );
  return response.data;
};
