import { MiddlewareConsumer, Module } from '@nestjs/common';
import { UsersModule } from './users/users.module';
import { WorkspaceModule } from './workspace/workspace.module';
import { APP_GUARD } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PostgresConnectionCredentialsOptions } from 'typeorm/driver/postgres/PostgresConnectionCredentialsOptions';
import { ChatModule } from './chat/chat.module';
import {
  createDataSourceWithVectorSupport,
  getEnvFilePath,
  getRedisHost,
} from './env-helper';
import { DataSourceOptions } from 'typeorm';
import { KnowledgeBaseModule } from './knowledge-base/knowledge-base.module';
import { DocumentModule } from './document/document.module';
import { PromptModule } from './prompts/prompts.module';
import { DatapointDocumentChunkModule } from './datapoint-document-chunk/datapoint-document-chunk.module';
import { ProjectModule } from './project/project.module';
import { DatapointRequestModule } from './datapoint/datapoint-request.module';
import { DataRequestModule } from './data-request/data-request.module';
import { LoggerMiddleware } from './middleware/logger.middleware';
import { EmailModule } from './external/email.module';
import { BullModule } from '@nestjs/bull';
import { CronModule } from './cron/cron.module';
import { BullBoardModule } from '@bull-board/nestjs';
import { ExpressAdapter } from '@bull-board/express';
import { ProcessQueueModule } from './process-queue/queue.module';
import { LlmRateLimiterModule } from './llm-rate-limiter/llm-rate-limiter.module';
import { AuthGuard } from './auth/supabase/supabase.auth.guard';
import { SupabaseAuthModule } from './auth/supabase/supabase.module';
import { QueuesModule } from './queues/queues.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: getEnvFilePath(),
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const connectionCredentials: PostgresConnectionCredentialsOptions = {
          url: configService.get<string>('BACKEND_DB_URL'),
        };

        return {
          type: 'postgres',
          ...connectionCredentials,
          entities: ['dist/**/*.entity{.ts,.js}'],
          synchronize: false,
          autoLoadEntities: true,
          migrations: ['database/migrations/**/*{.ts,.js}'],
        };
      },
      dataSourceFactory: async (options: DataSourceOptions) =>
        createDataSourceWithVectorSupport(options),
      inject: [ConfigService],
    }),
    EmailModule,
    ConfigModule.forRoot(),
    BullModule.forRoot({
      redis: {
        host: getRedisHost(),
        port: 6379,
      },
    }),
    // Import queues module for centralized queue management
    QueuesModule,
    BullBoardModule.forRoot({
      route: '/api/queues',
      adapter: ExpressAdapter,
    }),
    SupabaseAuthModule,
    UsersModule,
    WorkspaceModule,
    ChatModule,
    KnowledgeBaseModule,
    DocumentModule,
    PromptModule,
    ProjectModule,
    DataRequestModule,
    LlmRateLimiterModule,
    DatapointRequestModule,
    DatapointDocumentChunkModule,
    CronModule,
    ProcessQueueModule,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
  ],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LoggerMiddleware).forRoutes('*');
  }
}
