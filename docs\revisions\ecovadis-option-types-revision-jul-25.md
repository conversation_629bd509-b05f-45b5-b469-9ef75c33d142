# EcoVadis Option Types Revision: Supporting Free Text Input

## Document Summary
This revision document outlines the implementation of free text support for EcoVadis assessment options. The approach introduces a new `supportsAnswerText` boolean column while leveraging the existing `response` column for storing text values, eliminating the confusion around the current `type` column system.

Note: As each project-level option could have different behaviour, we're storing the information in `project_ecovadis_answer` instead of `ecovadis_answer_option`.

## Current Problem with Type Column

The existing `type` column in `project_ecovadis_answer` creates confusion:

### Issues with Current Approach:
1. **Semantic Ambiguity**: The `type` column was added with assumption that some option will be checkbox and some will be text or text-with-checkbox, which was wrong assumption.
2. **Default Behavior Confusion**: All options are fundamentally checkbox-based, but the type suggests different interaction models
3. **Implementation Complexity**: The enum values (`checkbox`, `text`, `text-with-checkbox`) don't align with actual user interaction patterns

### Current Database Schema:
```sql
Table "project_ecovadis_answer" {
  "id" uuid [pk]
  "projectId" uuid
  "optionId" uuid
  "response" string
  "type" ecovadis_answer_type [DEFAULT 'checkbox']  -- PROBLEMATIC
  "createdAt" timestamp
}
```

## Proposed Solution: `supportsAnswerText` Column

### New Database Schema:
```sql
ALTER TABLE project_ecovadis_answer 
ADD COLUMN "supportsAnswerText" boolean NOT NULL DEFAULT false;
```

### Updated Table Structure:
```sql
Table "project_ecovadis_answer" {
  "id" uuid [pk]
  "projectId" uuid
  "optionId" uuid
  "response" string              -- Stores both selection state and text content
  "supportsAnswerText" boolean     -- Indicates if option supports text input
  "type" ecovadis_answer_type    -- TO BE DEPRECATED
  "createdAt" timestamp
}
```

## Response Column Usage Strategy

The `response` column will handle both selection state and text content:

### Response Value Logic:
- **Checkbox-only options** (`supportsAnswerText = false`):
  - Selected: `response = "true"`
  - Unselected: `response = null`

- **Free text options** (`supportsAnswerText = true`):
  - With text input: `response = "user's actual text"`
  - Unselected: `response = null`

### Detection Logic:
```typescript
// Determine if option is selected
function isOptionSelected(response: string | null): boolean {
  return response !== null && response !== "";
}

// Determine if option has text content
function hasTextContent(response: string | null, supportsAnswerText: boolean): boolean {
  return supportsAnswerText && response !== null && response !== "true";
}

// Get text value for display
function getTextValueForAnswerOption(response: string | null, supportsAnswerText: boolean): string {
  if (!supportsAnswerText || !response || response === "true") {
    return "";
  }
  return response;
}

// Determine selection state for checkbox
function isCheckboxChecked(response: string | null): boolean {
  return response !== null;
}
```

## UI Implementation Changes

### Universal Checkbox Behavior:
**Every option will display as a checkbox** - this is the fundamental interaction model.

### Two UI Patterns:

1. **Standard Checkbox Option** (`supportsAnswerText = false`):
   ```
   ☐ Option title text
   ```

2. **Checkbox with Text Input** (`supportsAnswerText = true`):
   ```
   ☐ Option title text
   ┌─────────────────────────────────────┐
   │ [Text input area when checked]      │
   │                                     │
   └─────────────────────────────────────┘
   ```
## Implementation Plan

### Phase 1: Database Schema Update
1. Add `supportsAnswerText` column to `project_ecovadis_answer`
2. Populate column based on existing `type` values:
   - `type = 'text'` or `type = 'text-with-checkbox'` → `supportsAnswerText = true`
   - `type = 'checkbox'` → `supportsAnswerText = false`

### Phase 2: Backend Updates
1. Update Supabase functions to handle `supportsAnswerText` flag
2. Modify option detection logic during data import
3. Update API responses to include `supportsAnswerText` field

### Phase 3: Frontend Implementation
1. Update UI components to use universal checkbox pattern
2. Implement conditional text input based on `supportsAnswerText`
3. Update response handling logic
4. Update TypeScript interfaces

### Phase 4: Migration and Cleanup
1. Validate all existing data works with new logic
2. Test user flows for both option types
3. Plan deprecation of `type` column
4. Update documentation and training materials

## Benefits of This Approach

1. **Clear Semantics**: `supportsAnswerText` explicitly indicates text input capability
2. **Consistent UI**: All options use the same checkbox interaction model
3. **Flexible Data Storage**: Single `response` field handles both scenarios efficiently
4. **Backward Compatible**: Existing data can be migrated smoothly
5. **Future Proof**: Easy to extend without breaking changes
6. **Developer Friendly**: Clear, understandable logic for frontend implementation

## Migration Strategy

### Data Migration Script:
```sql
-- Update supportsAnswerText based on existing type values
UPDATE project_ecovadis_answer 
SET supportsAnswerText = true 
WHERE type IN ('text', 'text-with-checkbox');

-- Verify migration
SELECT 
  type, 
  supportsAnswerText, 
  COUNT(*) 
FROM project_ecovadis_answer 
GROUP BY type, supportsAnswerText;
```

### Validation Steps:
1. Ensure all records have correct `supportsAnswerText` values
2. Verify response values are interpreted correctly in UI
3. Test text input functionality end-to-end
4. Confirm document attachment workflow remains intact


---

*Document Version: 2.0*  
*Created: July 25, 2025*  
*Author: Ishwar Rimal*
