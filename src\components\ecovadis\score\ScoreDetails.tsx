
import React from 'react';

interface ScoreDetailsProps {
  score: number;
}

export const ScoreDetails = ({ 
  score, 
}: ScoreDetailsProps) => {
  const getScoreDetails = (score: number) => {
    if (score == 100) {
      return { level: "Outstanding", color: "#38A169", bgColor: "#f4fdf5", borderColor: "#d9f0e3" };
    } else if (score == 75) {
      return { level: "Advanced", color: "#69A240", bgColor: "#F2FCE2", borderColor: "#E1F5C6" };
    } else if (score == 50) {
      return { level: "Good", color: "#D69E2E", bgColor: "#FEF7CD", borderColor: "#FCEEB3" };
    } else if (score == 25) {
      return { level: "Partial", color: "#DD6B20", bgColor: "#FDE1D3", borderColor: "#FEC6A1" };
    } else {
      return { level: "Insufficient", color: "#E53E3E", bgColor: "#FFEFEF", borderColor: "#E53E3E33" };
    }
  };

  // Default score details based on the actual score
  const scoreDetails = getScoreDetails(score);
  
  // For Labor Rights Policies, we override to show always 50/Good
  const displayScore = score;
  
  // For Environmental Policies, we override to show always 75/Outstanding
  const displayLevel = scoreDetails.level;
  
  const displayColor = scoreDetails.color;
  
  const displayBgColor = scoreDetails.bgColor;
  
  const displayBorderColor = scoreDetails.borderColor;
  
  return (
    <div className="flex items-start justify-between">
      <div className="flex items-center gap-3">
        <div 
          className="text-3xl font-bold rounded-md px-4 py-2 flex items-center justify-center" 
          style={{ 
            color: displayColor,
            backgroundColor: displayBgColor,
            border: `1px solid ${displayBorderColor}`
          }}
        >
          {displayScore}
        </div>
        <div 
          className="text-base font-semibold rounded-full px-3 py-1" 
          style={{ 
            color: displayColor,
            backgroundColor: displayBgColor,
            border: `1px solid ${displayBorderColor}`
          }}
        >
          {displayLevel}
        </div>
      </div>
    </div>
  );
};
