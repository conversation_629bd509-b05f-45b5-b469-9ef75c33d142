import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import { RegisterWithCompanyDto } from './auth.dto';
import { Token } from 'src/users/entities/token.entity';
export declare class AuthService {
    private usersService;
    private jwtService;
    constructor(usersService: UsersService, jwtService: JwtService);
    login(email: string, password: string): Promise<string>;
    registerWithCompany(registerDto: RegisterWithCompanyDto): Promise<string>;
    resetPassword(password: string, token: string): Promise<string>;
    sendPasswordResetEmail(email: string, origin: string): Promise<void>;
    validateToken(token: string): Promise<Token>;
    switchUserWorkspace(userId: string, workspaceId: string): Promise<string>;
}
