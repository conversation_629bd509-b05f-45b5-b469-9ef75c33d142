import * as React from 'react';

import { parseMarkdown } from '@/lib/markdown-util';

interface MarkdownRendererProps extends React.HTMLAttributes<HTMLDivElement> {
  text: string;
}

const MarkdownRenderer = React.forwardRef<
  HTMLDivElement,
  MarkdownRendererProps
>(({ className, text, ...props }, ref) => (
  <div
    ref={ref}
    className={className + ' markdown-renderer'}
    dangerouslySetInnerHTML={{
      __html: parseMarkdown(text),
    }}
    {...props}
  />
));

MarkdownRenderer.displayName = 'MarkdownRenderer';

export { MarkdownRenderer };
