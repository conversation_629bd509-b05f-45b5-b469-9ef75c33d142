{"name": "climate-assistant-backend", "version": "0.1.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "NODE_ENV=development nest start", "start:dev": "set NODE_ENV=development && nest start --watch", "start:debug": "NODE_ENV=development nest start --debug --watch", "start:prod": "NODE_ENV=production node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "typeorm-ts-node-commonjs", "schema:sync": "npx typeorm-ts-node-commonjs schema:sync", "typeorm:cache": "npx typeorm-ts-node-commonjs cache:clear", "schema:drop": "npx typeorm-ts-node-commonjs -d ./src/database/data-source.ts", "migration:create": "typeorm migration:create src/database/migrations/schema-update", "migration:generate": "npx typeorm-ts-node-commonjs migration:generate src/database/migrations/schema-update -d ./src/database/data-source.ts && prettier --config \".prettierrc\"  \"./src/database/migrations/*.ts\" --write", "migration:show": "npx typeorm-ts-node-commonjs migration:show -d ./src/database/data-source.ts", "migration:run": "npx typeorm-ts-node-commonjs migration:run -d  ./src/database/data-source.ts", "migration:revert": "npx typeorm-ts-node-commonjs migration:revert -d ./src/database/data-source.ts", "migration:run:prod": "NODE_ENV=production npx typeorm-ts-node-commonjs migration:run -d  ./src/database/data-source.ts", "prettier": "prettier --config \".prettierrc\"  \"./**/*.tsx\" \"./**/*.ts\" --write", "generate-entity-interfaces": "typeorm-entity-type-interface -i ./src/**/*.entity.ts -o ../climate-assistant-frontend/src/types/db-entities.ts"}, "dependencies": {"@azure-rest/ai-document-intelligence": "^1.1.0", "@azure/identity": "^4.10.2", "@bull-board/api": "^6.5.4", "@bull-board/express": "^6.5.4", "@bull-board/nestjs": "^6.5.4", "@dqbd/tiktoken": "^1.0.17", "@google/genai": "^1.12.0", "@langchain/textsplitters": "^0.0.3", "@nestjs/bull": "^10.2.3", "@nestjs/bull-shared": "^11.0.3", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^7.4.2", "@nestjs/typeorm": "^10.0.2", "@pinecone-database/pinecone": "^6.1.2", "@supabase/supabase-js": "^2.49.4", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.7", "@types/html-docx-js": "^0.3.4", "@types/marked": "^5.0.2", "@types/pdf-parse": "^1.1.4", "bcrypt": "^5.1.1", "bull": "^4.16.5", "cheerio": "^1.1.2", "chromadb": "^3.0.10", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cohere-ai": "^7.18.0", "cookie-parser": "^1.4.6", "customerio-node": "^4.2.0", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "html-docx-js": "^0.3.1", "joplin-turndown-plugin-gfm": "^1.0.12", "json-2-csv": "^5.5.5", "langchain": "^0.3.2", "llamaindex": "^0.11.25", "marked": "^15.0.2", "openai": "^4.52.7", "pdf-parse": "^1.1.1", "pdf.js-extract": "^0.2.1", "pg": "^8.12.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "throat": "^6.0.2", "turndown": "^7.2.0", "typeorm": "^0.3.20", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bull-board": "^2.0.4", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.16.11", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.3.3", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typeorm-entity-type-interface": "^1.0.5", "typescript": "^5.6.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}