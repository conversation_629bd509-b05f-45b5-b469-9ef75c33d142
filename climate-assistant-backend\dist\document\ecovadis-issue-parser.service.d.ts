import 'dotenv/config';
import { ChatGptService } from 'src/llm/chat-gpt.service';
import { DataSource, Repository } from 'typeorm';
import { EcoVadisAnswerOption, EcoVadisQuestion, EcoVadisSustainabilityIssue, EcoVadisTheme, ProjectEcoVadisAnswer, ProjectEcoVadisTheme } from 'src/ecovadis/entities/ecovadis.entity';
export declare class EcovadisIssueParserService {
    private readonly chatGptService;
    private readonly ecoVadisSustainabilityIssueRepository;
    private readonly ecoVadisThemeRepository;
    private readonly projectEcoVadisThemeRepository;
    private readonly ecoVadisQuestionRepository;
    private readonly ecoVadisAnswerOptionRepository;
    private readonly projectEcoVadisAnswerOptionRepository;
    private readonly dataSource;
    private readonly logger;
    constructor(chatGptService: ChatGptService, ecoVadisSustainabilityIssueRepository: Repository<EcoVadisSustainabilityIssue>, ecoVadisThemeRepository: Repository<EcoVadisTheme>, projectEcoVadisThemeRepository: Repository<ProjectEcoVadisTheme>, ecoVadisQuestionRepository: Repository<EcoVadisQuestion>, ecoVadisAnswerOptionRepository: Repository<EcoVadisAnswerOption>, projectEcoVadisAnswerOptionRepository: Repository<ProjectEcoVadisAnswer>, dataSource: DataSource);
    parseEcovadisSustainabilityIssuePDF(filePath: string, projectId: string): Promise<void>;
    cleanupGermanAnswerOptions(dryRun?: boolean): Promise<{
        totalProcessed: number;
        germanOptionsFound: number;
        deletedCount: number;
        errors: string[];
    }>;
    private processQuestionOptions;
    private identifyGermanOptions;
    private deleteGermanOptions;
}
