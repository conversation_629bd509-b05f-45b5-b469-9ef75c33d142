import { Navigate } from 'react-router-dom';
import { ProtectedRoute } from './ProtectedRoute';
import Login from '@/pages/login';
import NotFound from '@/pages/NotFound';

import { ChatGptExample } from '@/pages/chat-gpt-example/chat-gpt-example';
import { NewChat } from '@/pages/new-chat';
import { AdminSettings } from '@/pages/admin-settings/UserSettings';
import { Documents } from '@/pages/documents';
import { Dashboard } from '@/pages/dashboard';
import { DataRequestDetailPage } from '@/pages/dashboard/DatarequestDetail';
import DocumentChunksPage from '@/pages/documents/DocumentChunks';
import DocumentDetail from '@/pages/documents/DocumentDetail';
import { ChatFlowiseExample } from '@/pages/flowise-example/chat-flowise-example';
import { VectorSearch } from '@/pages/admin-settings/VectorAnalysis';
import Settings from '@/pages/dashboard/settings';
import { MaterialitySettings } from '@/pages/dashboard/MaterialitySettings';
import ResetPassword from '@/pages/reset-password';
import ProjectsPage from '@/pages/projects';
import EcovadisProjectDetails from '@/pages/projects/EcovadisProjectDetails';
import Improvements from '@/pages/improvements';

export interface RouteConfig {
  path?: string;
  index?: boolean;
  element: React.ReactNode;
  handle?: {
    breadcrumb: string;
  };
  children?: RouteConfig[];
}

export const routeConfig = [
  {
    path: '/login',
    element: <Login />,
    handle: { breadcrumb: 'Login' },
  },
  {
    path: '/reset-password',
    element: <ResetPassword />,
    handle: { breadcrumb: 'ResetPassowrd' },
  },
  {
    path: '/',
    element: <ProtectedRoute />,
    children: [
      {
        index: true,
        element: <Navigate to="/projects" />,
      },
      {
        path: 'flowise',
        element: <ChatFlowiseExample />,
        handle: { breadcrumb: 'Flowise' },
      },
      {
        path: 'chats/:id',
        element: <ChatGptExample />,
        handle: { breadcrumb: 'Chat' },
      },
      {
        path: 'admin-settings',
        element: <AdminSettings />,
        handle: { breadcrumb: 'Admin Settings' },
      },
      {
        path: 'vector-search',
        element: <VectorSearch />,
        handle: { breadcrumb: 'Vector Search' },
      },
      {
        path: 'chat',
        element: <NewChat />,
        handle: { breadcrumb: 'New Chat' },
      },
      {
        path: 'documents',
        element: <Documents />,
        handle: { breadcrumb: 'Documents' },
      },
      {
        path: 'documents/chunks/:id',
        element: <DocumentChunksPage />,
        handle: { breadcrumb: 'Document Extracts' },
      },
      {
        path: 'documents/:id',
        element: <DocumentDetail />,
        handle: { breadcrumb: 'Document Details' },
      },
      {
        path: 'dashboard',
        element: <Dashboard />,
        handle: { breadcrumb: 'Dashboard' },
      },
      {
        path: 'projects',
        element: <ProjectsPage />,
        handle: { breadcrumb: 'Projects' },
      },
      {
        path: 'ecovadis-project/:id',
        element: <EcovadisProjectDetails />,
        handle: { breadcrumb: 'EcoVadis Project' },
      },
      {
        path: 'settings',
        element: <Settings />,
        handle: { breadcrumb: 'Settings' },
      },
      {
        path: 'dashboard/materiality-settings',
        element: <MaterialitySettings />,
        handle: { breadcrumb: 'Material Topics' },
      },
      {
        path: 'dashboard/:id',
        element: <DataRequestDetailPage />,
        handle: { breadcrumb: 'Data Request' },
      },
      {
        path: 'improvements',
        element: <Improvements />,
        handle: { breadcrumb: 'All Gaps' },
      },
      {
        path: '*',
        element: <NotFound />,
      },
    ],
  },
];
