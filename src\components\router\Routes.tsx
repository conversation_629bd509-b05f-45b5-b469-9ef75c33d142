import { Navigate } from 'react-router-dom';
import { ProtectedRoute } from './ProtectedRoute';
import Login from '@/pages/login';
import NotFound from '@/pages/NotFound';

import { Documents } from '@/pages/documents';
import DocumentDetail from '@/pages/documents/DocumentDetail';
import ProjectsPage from '@/pages/projects';
import EcovadisProjectDetails from '@/pages/projects/EcovadisProjectDetails';
import Improvements from '@/pages/improvements';

export interface RouteConfig {
  path?: string;
  index?: boolean;
  element: React.ReactNode;
  handle?: {
    breadcrumb: string;
  };
  children?: RouteConfig[];
}

export const routeConfig = [
  {
    path: '/login',
    element: <Login />,
    handle: { breadcrumb: 'Login' },
  },
  {
    path: '/',
    element: <ProtectedRoute />,
    children: [
      {
        index: true,
        element: <Navigate to="/projects" />,
      },
      {
        path: 'documents',
        element: <Documents />,
        handle: { breadcrumb: 'Documents' },
      },
      {
        path: 'documents/:id',
        element: <DocumentDetail />,
        handle: { breadcrumb: 'Document Details' },
      },
      {
        path: 'projects',
        element: <ProjectsPage />,
        handle: { breadcrumb: 'Projects' },
      },
      {
        path: 'ecovadis-project/:id',
        element: <EcovadisProjectDetails />,
        handle: { breadcrumb: 'EcoVadis Project' },
      },
      {
        path: 'improvements',
        element: <Improvements />,
        handle: { breadcrumb: 'All Gaps' },
      },
      {
        path: '*',
        element: <NotFound />,
      },
    ],
  },
];
