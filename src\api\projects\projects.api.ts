
import { supabase } from "@/integrations/supabase/client";
import { API_URL } from "../apiConstants";
import axios from "axios";

export const fetchProjects = async () => {
      try {
        
        // Get the session token for authorization
        const { data: { session } } = await supabase.auth.getSession();

        // Call our edge function to get projects
        const { data, error: functionError } = await supabase.functions.invoke('get-projects', {
          headers: {
            Authorization: `Bearer ${session!.access_token}`
          }
        });

        if (functionError) {
          console.error('Error calling get-projects function:', functionError);
          throw new Error('Failed to fetch projects from server');
        }
        return data.projects;
      } catch (err) {
        throw new Error('An unexpected error occurred');
      }
    };

export const updateProject = async (projectId: string, updates: any) => {
  try {
    // Get the session token for authorization
    const { data: { session } } = await supabase.auth.getSession();

    // Call our edge function to update the project
    const { data, error: functionError } = await supabase.functions.invoke('update-project', {
      headers: {
        Authorization: `Bearer ${session!.access_token}`
      },
      body: { projectId, updates }
    });

    if (functionError) {
      console.error('Error calling update-project function:', functionError);
      throw new Error('Failed to update project');
    }
    
    return data.project;
  } catch (err) {
    console.error('Error updating project:', err);
    throw new Error('An unexpected error occurred when updating the project');
  }
};

/**
 * Uploads a new EcoVadis questionnaire
 */
export const uploadEcovadisQuestionnaire = async (
  projectId: string, 
  file: File
): Promise<void> => {
  try {
    // Create FormData to send file
    const formData = new FormData();
    formData.append('file', file);

    await axios.post<{ message: string }>(
      `${API_URL}/auth/upload-questionnaire/${projectId}`,
      formData
    );

  } catch (err) {
    console.error('Error uploading questionnaire:', err);
    throw new Error('Failed to upload questionnaire');
  }
};
