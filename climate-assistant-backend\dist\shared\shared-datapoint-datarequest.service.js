"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DatapointDataRequestSharedService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatapointDataRequestSharedService = void 0;
const common_1 = require("@nestjs/common");
const datapoint_request_entity_1 = require("../datapoint/entities/datapoint-request.entity");
const datapoint_request_service_1 = require("../datapoint/datapoint-request.service");
const jobs_1 = require("../types/jobs");
const bull_1 = require("@nestjs/bull");
const env_helper_1 = require("../env-helper");
let DatapointDataRequestSharedService = DatapointDataRequestSharedService_1 = class DatapointDataRequestSharedService {
    constructor(datapointRequestService, datapointGenerationQueue, datapointReviewQueue) {
        this.datapointRequestService = datapointRequestService;
        this.datapointGenerationQueue = datapointGenerationQueue;
        this.datapointReviewQueue = datapointReviewQueue;
        this.logger = new common_1.Logger(DatapointDataRequestSharedService_1.name);
    }
    async addDatapointToGenerationQueue({ datapointRequest, userId, workspaceId, useExistingReportTextForReference, }) {
        try {
            await this.datapointRequestService.updateQueueStatus({
                datapointRequestId: datapointRequest.id,
                queueStatus: datapoint_request_entity_1.DatapointQueueStatus.QueuedForGeneration,
            });
            await this.datapointGenerationQueue.add(jobs_1.JobQueue.DatapointGenerate, {
                dataRequestId: datapointRequest.dataRequestId,
                datapointRequestId: datapointRequest.id,
                userId,
                workspaceId,
                useExistingReportTextForReference,
            }, {
                attempts: 5,
                backoff: {
                    type: 'exponential',
                    delay: 5000,
                },
                removeOnComplete: env_helper_1.isDevelopment ? false : true,
            });
        }
        catch (error) {
            this.logger.error(`Error generating datapoint ${datapointRequest.id} for data request ${datapointRequest.dataRequestId}: ${error}`);
            await this.datapointRequestService.update({
                datapointRequestId: datapointRequest.id,
                updateDatapointRequestPayload: datapointRequest,
                userId,
                workspaceId,
            });
        }
    }
    async addDatapointToReviewQueue({ datapointRequest, userId, workspaceId, }) {
        try {
            await this.datapointRequestService.updateQueueStatus({
                datapointRequestId: datapointRequest.id,
                queueStatus: datapoint_request_entity_1.DatapointQueueStatus.QueuedForReview,
            });
            await this.datapointReviewQueue.add(jobs_1.JobQueue.DatapointReview, {
                dataRequestId: datapointRequest.dataRequestId,
                datapointRequestId: datapointRequest.id,
                userId,
                workspaceId,
            }, {
                attempts: 5,
                backoff: {
                    type: 'exponential',
                    delay: 5000,
                },
                removeOnComplete: env_helper_1.isDevelopment ? false : true,
            });
        }
        catch (error) {
            this.logger.error(`Error reviewing datapoint ${datapointRequest.id} for data request ${datapointRequest.dataRequestId}: ${error}`);
            await this.datapointRequestService.update({
                datapointRequestId: datapointRequest.id,
                updateDatapointRequestPayload: datapointRequest,
                userId,
                workspaceId,
            });
        }
    }
};
exports.DatapointDataRequestSharedService = DatapointDataRequestSharedService;
exports.DatapointDataRequestSharedService = DatapointDataRequestSharedService = DatapointDataRequestSharedService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, bull_1.InjectQueue)(jobs_1.JobProcessor.DatapointGeneration)),
    __param(2, (0, bull_1.InjectQueue)(jobs_1.JobProcessor.DatapointReview)),
    __metadata("design:paramtypes", [datapoint_request_service_1.DatapointRequestService, Object, Object])
], DatapointDataRequestSharedService);
//# sourceMappingURL=shared-datapoint-datarequest.service.js.map