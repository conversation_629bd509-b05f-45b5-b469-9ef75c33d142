import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1730288906047 implements MigrationInterface {
  name = 'SchemaUpdate1730288906047';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "comment" RENAME COLUMN "commentable_id" TO "commentableId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment" RENAME COLUMN "commentable_type" TO "commentableType"`,
    );

    await queryRunner.query(
      `ALTER TYPE "comment_commentable_type_enum" RENAME TO "comment_commentabletype_enum"`,
    );

    await queryRunner.query(
      `DROP INDEX IF EXISTS "public"."IDX_b8d38847018497d8a6121b3abb"`,
    );

    await queryRunner.query(
      `CREATE INDEX "IDX_b8d38847018497d8a6121b3abb" ON "comment" ("commentableId", "commentableType")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b8d38847018497d8a6121b3abb"`,
    );

    await queryRunner.query(
      `ALTER TYPE "comment_commentabletype_enum" RENAME TO "comment_commentable_type_enum"`,
    );

    await queryRunner.query(
      `ALTER TABLE "comment" RENAME COLUMN "commentableType" TO "commentable_type"`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment" RENAME COLUMN "commentableId" TO "commentable_id"`,
    );
  }
}
