import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1741845747429 implements MigrationInterface {
  name = 'SchemaUpdate1741845747429';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."project_primarycontentlanguage_enum" RENAME TO "project_primarycontentlanguage_enum_old"`
    );
    await queryRunner.query(
      `CREATE TYPE "public"."project_primarycontentlanguage_enum" AS ENUM('BG', 'HR', 'CS', 'DA', 'NL', 'EN', 'ET', 'FI', 'FR', 'DE', 'EL', 'HU', 'GA', 'IT', 'LV', 'LT', 'MT', 'PL', 'PT', 'RO', 'SK', 'SL', 'ES', 'SV')`
    );
    await queryRunner.query(
      `ALTER TABLE "project" ALTER COLUMN "primaryContentLanguage" TYPE "public"."project_primarycontentlanguage_enum" USING "primaryContentLanguage"::"text"::"public"."project_primarycontentlanguage_enum"`
    );
    await queryRunner.query(
      `DROP TYPE "public"."project_primarycontentlanguage_enum_old"`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."project_primarycontentlanguage_enum_old" AS ENUM('DE', 'EN')`
    );
    await queryRunner.query(
      `ALTER TABLE "project" ALTER COLUMN "primaryContentLanguage" TYPE "public"."project_primarycontentlanguage_enum_old" USING "primaryContentLanguage"::"text"::"public"."project_primarycontentlanguage_enum_old"`
    );
    await queryRunner.query(
      `DROP TYPE "public"."project_primarycontentlanguage_enum"`
    );
    await queryRunner.query(
      `ALTER TYPE "public"."project_primarycontentlanguage_enum_old" RENAME TO "project_primarycontentlanguage_enum"`
    );
  }
}
