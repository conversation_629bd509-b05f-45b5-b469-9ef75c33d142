export interface RerankResult {
    index: number;
    relevance_score: number;
    document: {
        text: string;
    };
}
export declare class CohereService {
    private readonly logger;
    private readonly cohereClient;
    constructor();
    rerankResults(query: string, documents: Array<{
        content: string;
        metadata?: any;
    }>, topK?: number): Promise<Array<{
        content: string;
        relevance_score: number;
        metadata?: any;
        original_index: number;
    }>>;
}
