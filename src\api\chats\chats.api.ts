import axios from 'axios';

import { ChatHistory } from '@/models/chat.models.ts';
import { API_URL } from '@/api/apiConstants';
import { AI_ACTIONS } from '@/types';
import { VectorSearchResponseData } from '@/pages/admin-settings/VectorAnalysis';

export const fetchHistories = async () => {
  const response = await axios.get<ChatHistory[]>(`${API_URL}/chats/`);
  return response.data;
};

export const deleteHistory = async (id: ChatHistory['id']) => {
  const response = await axios.delete<ChatHistory[]>(`${API_URL}/chats/${id}`);
  return response.data;
};

export const fetchHistory = async (id: string) => {
  const response = await axios.get<ChatHistory>(`${API_URL}/chats/${id}`);
  return response.data;
};

export const updateHistory = async (
  id: string,
  changes: Partial<ChatHistory>
) => {
  const response = await axios.put<ChatHistory>(
    `${API_URL}/chats/${id}`,
    changes
  );
  return response.data;
};

export const createEmptyHistory = async (type: AI_ACTIONS) => {
  const response = await axios.post<ChatHistory>(
    `${API_URL}/chats/create-empty-history`,
    { type }
  );
  return response.data;
};

export const queryRelatedVectors = async ({
  query,
  count,
  threshold,
  database,
}: {
  query: string;
  count: number;
  threshold: number;
  database: 'internal' | 'company';
}) => {
  const response = await axios.post<VectorSearchResponseData[]>(
    `${API_URL}/chats/vector-query`,
    {
      query,
      count,
      threshold,
      database,
    }
  );
  return response.data;
};
