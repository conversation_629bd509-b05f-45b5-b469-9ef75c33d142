import { AuthService } from './auth.service';
import { Response } from 'express';
import { UsersService } from '../users/users.service';
import { LoginDto, LoginResponseSuccess, LogoutResponseSuccess, RegisterWithCompanyDto } from './auth.dto';
export declare class AuthController {
    private authService;
    private readonly userService;
    constructor(authService: AuthService, userService: UsersService);
    login(loginDto: LoginDto, res: Response): Promise<Response<LoginResponseSuccess>>;
    register(registerDto: RegisterWithCompanyDto, res: Response): Promise<Response<LoginResponseSuccess>>;
    passwordResetEmail(res: Response, req: any, body: {
        email: string;
    }): Promise<Response>;
    validatePasswordResetToken(req: any, body: {
        token: string;
    }): Promise<import("../users/entities/user.entity").User>;
    passwordSubmit(req: any, res: Response, body: {
        password: string;
        token: string;
    }): Promise<Response<any, Record<string, any>>>;
    logout(res: Response): Promise<Response<LogoutResponseSuccess>>;
    getProfile(req: any): Promise<import("../users/entities/user.entity").User>;
    switchWorkspace(req: any, body: {
        workspaceId: string;
    }, res: Response): Promise<Response<any, Record<string, any>>>;
}
