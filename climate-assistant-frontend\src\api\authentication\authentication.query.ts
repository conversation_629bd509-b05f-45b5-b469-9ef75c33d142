import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';

import { USER_QUERY_KEY } from '../apiConstants';

import {
  fetchUserProfile,
  login,
  logout,
  resetPasswordMutation,
  sendPasswordResetEmailMutation,
} from '@/api/authentication/authentication.api.ts';
import { MixpanelService } from '@/services/mixpanel.service.ts';
import { HotjarService } from '@/services/hotjar.service.ts';
import { IUser } from '@/types/user';

export const useAuthentication = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const { data, isLoading } = useQuery<IUser | null>({
    queryKey: [USER_QUERY_KEY],
    retry: 0,
    queryFn: fetchUserProfile,
    staleTime: 1000 * 60 * 60,
  });

  const loginMutation = useMutation({
    mutationFn: login,
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEY] });
      navigate('/');
    },
  });

  const logoutMutation = useMutation({
    mutationFn: logout,
    onSuccess: async () => {
      await queryClient.setQueryData([USER_QUERY_KEY], () => null);
      navigate('/login');
    },
  });

  const sessionReloadMutation = useMutation({
    mutationFn: fetchUserProfile,
    onSuccess: async () => {
      queryClient.clear();
    },
  });

  const sendPasswordResetEmail = useMutation({
    mutationFn: sendPasswordResetEmailMutation,
  });

  const resetPassword = useMutation({
    mutationFn: resetPasswordMutation,
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEY] });
      navigate('/dashboard');
    },
  });

  useEffect(() => {
    if (data != null) {
      void MixpanelService.identifyUser(data.email);
      void HotjarService.identifyUser(data.email);
    }
    if (data == null) {
      void MixpanelService.reset();
    }
  }, [data]);

  return {
    user: data as IUser | null,
    isLoading,
    isLoginPending: loginMutation.isPending,
    login: loginMutation.mutate,
    logout: logoutMutation.mutate,
    sendPasswordResetEmail: sendPasswordResetEmail.mutate,
    resetPassword: resetPassword.mutate,
    restPasswordLoading: resetPassword.isPending,
    sendPasswordResetEmailLoading: sendPasswordResetEmail.isPending,
    loginErrors: loginMutation.error as string | string[] | null,
    sessionReloadMutation,
  };
};
