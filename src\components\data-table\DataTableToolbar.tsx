'use client';

import { X } from 'lucide-react';
import { Column, Row, Table } from '@tanstack/react-table';
import { useSearchParams, useNavigate } from 'react-router-dom';

import { Button } from '../ui/button';
import { Input } from '../ui/input';

import { DataTableViewOptions } from './DataTableViewOptions';

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  bulkActions?: (selectedRows: number[]) => React.ReactNode[];
  columnActions?: {
    columnName: string;
    actions: (column: Column<TData, unknown>) => React.ReactNode;
  }[];
}

export function DataTableToolbar<TData>({
  table,
  bulkActions,
  columnActions,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0;
  const selectedRows = table
    .getSelectedRowModel()
    .rows.map((row: Row<TData>) => (row.original as { id: number }).id);

  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const handleResetFilters = () => {
    // Reset all column filters
    table.resetColumnFilters();
    // Clone the current search parameters
    const params = new URLSearchParams(searchParams);
    // Identify all columns that have filters
    const filterColumns = table.getAllColumns().map((column) => column.id);
    // Remove each filter parameter
    filterColumns.forEach((columnId) => {
      params.delete(columnId);
    });

    navigate({ search: params.toString() }, { replace: true });
  };

  return (
    <div className="flex items-center justify-between bg-gray-100 p-3 rounded-lg">
      <div className="flex flex-1 items-center space-x-2">
        {columnActions &&
          columnActions.map(({ columnName, actions }, index) => {
            const column = table.getColumn(columnName);
            return column && <div key={index}>{actions(column)}</div>;
          })}

        {isFiltered && (
          <Button
            variant="ghost"
            onClick={handleResetFilters}
            className="h-8 px-2 lg:px-3"
          >
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex flex-1 items-center space-x-2 justify-end">
        <Input
          placeholder="Search ..."
          value={(table.getColumn('name')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('name')?.setFilterValue(event.target.value)
          }
          className="h-10 border-none w-[150px] lg:w-[250px]"
        />
        {bulkActions &&
          bulkActions.length > 0 &&
          selectedRows.length > 1 &&
          bulkActions(selectedRows).map((action, index) => (
            <div key={index}>{action}</div>
          ))}

        <DataTableViewOptions table={table} />
      </div>
    </div>
  );
}
