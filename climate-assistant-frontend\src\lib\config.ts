import { IconName } from '@/components/ui/icons';
import { AI_ACTIONS } from '@/types';

export const climaFeatures: {
  title: string;
  description: string;
  icon: IconName;
  badge?: string;
  active: boolean;
  type: AI_ACTIONS;
}[] = [
  {
    type: AI_ACTIONS.initiativeCreation,
    title: 'Maßnahmen erstellen',
    description: 'Erhalte individuelle Maßnahmen für dein Unternehmen',
    icon: 'HammerIcon',
    active: true,
  },
  {
    type: AI_ACTIONS.regulatoryHelp,
    title: 'Umsetzung der ISO 50001',
    description:
      'Erhalte deinen Leitfaden für eine erfolgreiche Zertifizierung',
    icon: 'ClipboardListIcon',
    active: true,
  },
  {
    type: AI_ACTIONS.csrdCreation,
    title: 'CSRD Reporting',
    description:
      'Erhalte automatisierte Unterstützung bei der Erarbeitung von ESRS Datenpunkten',
    icon: 'ListEnd',
    active: true,
  },
  {
    type: AI_ACTIONS.transitionPlan,
    title: 'Transition Plan erstellen',
    description: 'Maßgeschneiderten Transition Plan entwickeln',
    icon: 'Layers',
    badge: 'coming soon',
    active: false,
  },
];

export const topNavigationItems = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Documents',
    href: '/documents',
  },
  // {
  //   title: 'Chat',
  //   href: '/chats',
  // },
];

export const ESRS_TOPIC_SEGMENTS = [
  'ESRS 2',
  'E1',
  'E2',
  'E3',
  'E4',
  'E5',
  'S1',
  'S2',
  'S3',
  'S4',
  'G1',
];

// NOTE: This sorting object is cloned in the frontend at climate-assistant-backend/src/util/config.ts
export const ESRS_SORT_ORDER: Record<string, number> = {
  'ESRS 2': 0,
  E1: 1,
  E2: 2,
  E3: 3,
  E4: 4,
  E5: 5,
  S1: 6,
  S2: 7,
  S3: 8,
  S4: 9,
  G1: 10,
};

export const GLOBAL_AI_USER_UUID = '08106d33-61e4-477a-9adb-0283ecff0c54';
