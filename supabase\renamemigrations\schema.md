Enum "user_workspace_role_enum" {
  "SUPER_ADMIN"
  "WORKSPACE_ADMIN"
  "AI_CONTRIBUTOR"
  "AI_ONLY_REVIEW"
  "CONTRIBUTOR"
}

Enum "project_type" {
  "CSRD"
  "EcoVadis"
}

Enum "ecovadis_indicator" {
"POLICIES"
"ENDORSEMENTS"
"MEASURES"
"CERTIFICATIONS"
"COVERAGE"
"REPORTING"
"WATCH_FINDINGS"
}

Table "user" {
  "id" uuid [pk, not null, default: `public.uuid_generate_v4()`]
  "email" "character varying(100)" [not null]
  "password" "character varying(100)"
  "name" "character varying(100)"
  "createdAt" timestamp [not null, default: `now()`]
}

Table "user_workspace" {
  "userId" uuid [not null]
  "workspaceId" uuid [not null]
  "joinedAt" timestamp
  "createdAt" timestamp [not null, default: `now()`]
  "role" user_workspace_role_enum

  Indexes {
    (userId, workspaceId) [pk, name: "PK_c395920dbb9ba8840eaa0278bf8"]
  }
}

Table "workspace" {
  "id" uuid [pk, not null, default: `public.uuid_generate_v4()`]
  "name" "character varying" [not null]
  "createdAt" timestamp [not null, default: `now()`]
}


Table "document" {
  "id" uuid [pk, not null, default: `public.uuid_generate_v4()`]
  "workspaceId" uuid [not null]
  "name" "character varying" [not null]
  "path" "character varying" [not null]
  "createdAt" timestamp [not null, default: `now()`]
  "status" document_status_enum [not null, default: `'not_processed'::public.document_status_enum`]
  "documentType" "character varying"
  "esrsCategory" text
  "year" integer
  "month" integer
  "day" integer
  "remarks" text
  "createdBy" uuid
}

Table "document_version" {
  "id" uuid [pk, not null, default: `public.uuid_generate_v4()`]
  "documentId" uuid [not null]
  "ancestor" uuid [not null]
  "version" int
}

Table "document_chunk" {
  "id" uuid [pk, not null, default: `public.uuid_generate_v4()`]
  "documentId" uuid [not null]
  "page" "character varying" [not null]
  "content" text [not null]
  "matchingsJson" text
  "createdAt" timestamp [not null, default: `now()`]
  "metadataJson" json
}

Table "project" {
  "id" uuid [pk, not null, default: `public.uuid_generate_v4()`]
  "workspaceId" uuid [not null]
  "name" "character varying" [not null]
  "type" project_type
  "primaryContentLanguage" project_primarycontentlanguage_enum [not null]
  "metadata" JSON
  "createdBy" uuid [not null]
  "createdAt" timestamp [not null, default: `now()`]
  "reportTextGenerationRules" text [not null, default: `''::text`]
}

Table "ecovadis_theme" {
  "id" uuid [pk, not null, default: `public.uuid_generate_v4()`]
  "title" string
  "description" text
  "createdAt" timestamp [not null, default: `now()`]
}

Enum "impact_score" {
  "High"
  "Medium"
  "Low"
}

Table "project_ecovadis_theme" {
  "id" uuid [pk, not null, default: `public.uuid_generate_v4()`]
  "themeId" uuid
  "projectId" uuid
  "impact" impact_score
  "issues" array
  "createdAt" timestamp [not null, default: `now()`]
}


Table "ecovadis_question" {
  "id" uuid [pk, not null, default: `public.uuid_generate_v4()`]
  "themeId" uuid
  "questionCode" string
  "indicator" ecovadis_indicator
  "questionName" string
  "question" string
  "createdAt" timestamp [not null, default: `now()`]
}

Table "project_ecovadis_question_score" {
  "id" uuid [pk, not null, default: `public.uuid_generate_v4()`]
  "questionId" uuid [not null]
  "score" integer [not null]
  "level" ecovadis_score_level [not null]
  "description" text
  "breakdown" text
  "conclusion" text
  "createdAt" timestamp [not null, default: `now()`]
}

Table "project_ecovadis_question_score_history" {
  "id" uuid [pk, not null, default: `public.uuid_generate_v4()`]
  "scoreId" uuid [not null]
  "score" integer [not null]
  "level" ecovadis_score_level [not null]
  "description" text
  "breakdown" text
  "conclusion" text
  "version" integer [not null]
  "createdAt" timestamp [not null, default: `now()`]
}

Enum "ecovadis_score_level" {
  "Outstanding"
  "Advanced"
  "Good"
  "Partial"
  "Insufficient"
}

Ref: "project_ecovadis_question_score"."questionId" > "project_ecovadis_question"."id" [delete: cascade]
Ref: "project_ecovadis_question_score_history"."scoreId" > "project_ecovadis_question_score"."id" [delete: cascade]

Table "project_ecovadis_question" {
  "id" uuid [pk, not null, default: `public.uuid_generate_v4()`]
  "questionId" uuid
  "projectId" uuid
  "impact" impact_score
  "status" string
  "createdAt" timestamp [not null, default: `now()`]
}

Table "project_ecovadis_gaps" {
  "id" uuid [pk, not null, default: `public.uuid_generate_v4()`]
  "questionId" uuid
  "projectId" uuid
  "gaps" json 
  "documents" string[]
  "resolved" boolean
  "assigneeId" uuid
  "deadline" timestamp
  "createdAt" timestamp [not null, default: `now()`]
}


Table "ecovadis_answer_option" {
  "id" uuid [pk, not null, default: `public.uuid_generate_v4()`]
  "questionId" uuid
  "issueTitle" string
  "instructions" text
  "createdAt" timestamp [not null, default: `now()`]
}

Table "project_ecovadis_answer" {
  "id" uuid [pk, not null, default: `public.uuid_generate_v4()`]
  "projectId" uuid
  "optionId" uuid
  "response" string
  "createdAt" timestamp [not null, default: `now()`]
}

Table "project_ecovadis_linked_document_chunks" {
  "id" Int
  "answerId" uuid
  "documentChunkId" uuid
  "comment" text
  "createdAt" timestamp [not null, default: `now()`]
}

Ref "FK_document_workspace": "workspace"."id" < "document"."workspaceId"

Ref "FK_user_workspace_association": "workspace"."id" < "user_workspace"."workspaceId"

Ref "FK_project_workspace": "workspace"."id" < "project"."workspaceId"

Ref "FK_user_workspace_user": "user"."id" < "user_workspace"."userId"

Ref "FK_document_chunk_document": "document"."id" < "document_chunk"."documentId"

Ref "FK_ecovadis_answer_option_question": "ecovadis_question"."id" < "ecovadis_answer_option"."questionId"
Ref "FK_project_ecovadis_answer_option": "ecovadis_answer_option"."id" < "project_ecovadis_answer"."optionId"

Ref "FK_document_version_document": "document"."id" < "document_version"."documentId"
Ref "FK_document_version_ancestor": "document_version"."id" < "document_version"."ancestor"

Ref "FK_project_ecovadis_theme_theme": "ecovadis_theme"."id" < "project_ecovadis_theme"."themeId"
Ref "FK_project_ecovadis_theme_project": "project"."id" < "project_ecovadis_theme"."projectId"

Ref "FK_project_ecovadis_question_question": "ecovadis_question"."id" < "project_ecovadis_question"."questionId"
Ref "FK_project_ecovadis_question_project": "project"."id" < "project_ecovadis_question"."projectId"

Ref "FK_project_ecovadis_gaps_question": "ecovadis_question"."id" < "project_ecovadis_gaps"."questionId"
Ref "FK_project_ecovadis_gaps_project": "project"."id" < "project_ecovadis_gaps"."projectId"

Ref "FK_document_created_by": "user"."id" < "document"."createdBy"

Ref "FK_project_created_by_user": "user"."id" < "project"."createdBy"
Ref "FK_question_theme": "ecovadis_theme"."id" < "ecovadis_question"."themeId"
Ref "FK_ecovadis_answer_project": "project"."id" < "project_ecovadis_answer"."projectId"
Ref "FK_linked_chunks_answer": "project_ecovadis_answer"."id" < "project_ecovadis_linked_document_chunks"."answerId"
Ref "FK_linked_chunks_document_chunk": "document_chunk"."id" < "project_ecovadis_linked_document_chunks"."documentChunkId"