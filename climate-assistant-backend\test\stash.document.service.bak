
const generatePageReportCategorisingPrompt =
  () => `Given the conditions listed below for various categories, analyze the content of the provided page to determine which categories it pertains to based on the descriptions of each category. If the page relates to one or more categories, return their identifiers (e.g., E1, E2, S1). If the page does not relate to any of the categories, return an empty array.
  
  Category Reference:
  
  Conditions for E1 - Climate Change:
  ${esrsTopics.E1}
  Conditions for E2 - Pollution:
  ${esrsTopics.E2}
  Conditions for E3 - Water and Marine Resources:
  ${esrsTopics.E3}
  Conditions for E4 - Biodiversity and Ecosystems:
  ${esrsTopics.E4}
  Conditions for E5 - Resource Use and Circular Economy:
  ${esrsTopics.E5}
  Conditions for S1 - Own Workforce:
  ${esrsTopics.S1}
  Conditions for S2 - Workers in the Value Chain:
  ${esrsTopics.S2}
  Conditions for S3 - Affected Communities:
  ${esrsTopics.S3}
  Conditions for S4 - Consumers and End-users:
  ${esrsTopics.S4}
  Conditions for G1 - Business Conduct:
  ${esrsTopics.G1}
  
  Task: Analyze the page and determine which of the categories (E1, E2, S1) from the category reference the page refers to. Output the result as a JSON array of the relevant categories.
  
  Example Output:
  {
    "relevantTopics": ["E1", "E2", "S1"]
  }
  
  If the page does not match any categories:
  {
    "relevantTopics": []
  }`;

const generatePageReportSubCategorisingPrompt = (
  topic: string,
) => `Given the conditions listed below for various categories, analyze the content of the provided page to determine which categories it pertains to based on the descriptions of each category. If the page relates to one or more categories, return their identifiers (e.g., ${topic}-1, ${topic}-2). If the page does not relate to any of the categories, return an empty array.
  
  Category Reference:
  
  ${disclosureRequirements[topic]}
  
  Task: Analyze the page and determine which of the categories (${topic}-1, ${topic}-2, ${topic}-3) from the category reference the page refers to. Output the result as a JSON array of the relevant categories.
  
  Example Output:
  {
    "relevantDisclosureRequirements": ["${topic}-1", "${topic}-2"]
  }
  
  If the page does not match any categories:
  {
    "relevantDisclosureRequirements": []
  }`;

const generatePageReportRequirementExtractionPrompt = (
  topic: string,
) => `You are reviewing the ${topic} related requirements as part of the ESRS reporting criterion. You will be provided with a JSON object containing the criteria for ${topic} and the content from one page of a company's CSRD report.
  
  Task:
  Analyze the content of the page and identify any key text that pertains to the sub-criteria of ${topic}. Extract relevant information that aligns with these sub-criteria and output the result as a JSON array of extracted requirements.
  
  If there are no matching criteria, return an empty array.
  
  Example Output:
  {
    "paragraphs": [
      {
        "paragraph": 1,
        "textInParagraph": "<Exact Text from the paragraph>"
        "relevantForDisclosureIDs": ["${topic}_01", "${topic}_02"]
      }
    ]
  }
    
  Note:
  - There may be multiple matching texts, so the output should be an array of objects.
  - It is possible that same text may align with multiple sub-criteria, you should include it in the output for each relevant sub-criterion.
  - Make sure to extract any text that aligns with the sub-criteria for ${topic}. DO NOT ALTER THE TEXT
  - If no text matches the criteria for a specific sub-criterion, do not include it in the output. If none is please skip the sub-criterion.
  `;
