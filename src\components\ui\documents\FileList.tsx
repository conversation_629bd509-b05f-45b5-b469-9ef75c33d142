
import React from 'react';
import { FileText, Trash } from 'lucide-react';

interface FileListProps {
  files: File[];
  onDeleteClick: (index: number) => void;
}

export function FileList({ files, onDeleteClick }: FileListProps) {
  if (files.length === 0) {
    return null;
  }

  return (
    <div className="bg-white border border-gray-100 rounded-lg shadow-sm p-6">
      <h3 className="text-md font-medium text-glacier-darkBlue mb-3">
        Uploaded files ({files.length})
      </h3>
      
      <ul className="space-y-2">
        {files.map((file, index) => (
          <li key={index} className="border border-green-200 bg-green-50 rounded-lg p-3 flex items-center justify-between">
            <div className="flex items-center">
              <FileText className="h-5 w-5 text-glacier-darkBlue mr-3" />
              <div className="flex flex-col">
                <span className="text-sm font-medium text-gray-800 truncate max-w-xs">{file.name}</span>
                <span className="text-xs text-gray-500">{(file.size / (1024 * 1024)).toFixed(2)} MB</span>
              </div>
            </div>
            <button 
              onClick={() => onDeleteClick(index)}
              className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1.5 rounded-full transition-colors"
              aria-label="Delete file"
            >
              <Trash className="h-4 w-4" />
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
}
