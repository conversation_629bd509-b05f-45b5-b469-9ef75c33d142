import { Trash2Icon } from 'lucide-react';
import { DialogDescription } from '@radix-ui/react-dialog';

import { Badge } from '../ui/badge';
import { Button } from '../ui/button';

import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { dateDocumentChunk } from '@/api/documents/documents.api';

export function DatapointLinksDeleteModal({
  documentChunkId,
  mutate,
}: {
  documentChunkId: string;
  mutate: () => void;
}) {
  async function handleDelete() {
    await dateDocumentChunk(documentChunkId);
    mutate();
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Badge
          className="rounded-md font-normal bg-red-600 text-white hover:bg-red-600/90"
          size="sm"
          variant={'secondary'}
          onClick={(e) => e.stopPropagation()}
        >
          <Trash2Icon className="h-3 w-3 mr-1" />
          Delete
        </Badge>
      </DialogTrigger>
      <DialogContent
        className="text-slate-900"
        onClick={(e) => e.stopPropagation()}
      >
        <DialogHeader>
          <DialogTitle>Delete Document Chunk</DialogTitle>
        </DialogHeader>
        <DialogDescription /> {/* required for render */}
        <DialogFooter>
          <Button variant="destructive" onClick={handleDelete}>
            Delete
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
