"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DifyService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DifyService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("axios");
const config_1 = require("@nestjs/config");
const error_middleware_1 = require("../middleware/error.middleware");
let DifyService = DifyService_1 = class DifyService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(DifyService_1.name);
        this.metadataFieldMappings = [
            {
                id: 'eebd5604-b852-4c17-baca-6a4967a70e2e',
                name: 'workspace_id',
                type: 'string',
            },
            {
                id: 'f8f93e58-b965-46d5-979d-d109cb46a7a0',
                name: 'document_chunk_id',
                type: 'string',
            },
            {
                id: '525d210f-a5ca-4038-8329-eef6f0099493',
                name: 'project_id',
                type: 'string',
            },
            {
                id: '99d3166e-29d8-4fe9-9d59-421bc003a514',
                name: 'document_id',
                type: 'string',
            },
            {
                id: '6294bae1-babf-4b25-a823-03340a21e2be',
                name: 'page',
                type: 'string',
            },
        ];
        const DIFY_BASE_URL = 'https://dify-ai.glacier.eco/v1';
        const DIFY_KNOWLEDGE_API_KEY = process.env.DIFY_KNOWLEDGE_API_KEY;
        this.datasetId = 'd46ea0e0-00cb-4972-a5c4-12d760738781';
        this.client = axios_1.default.create({
            baseURL: DIFY_BASE_URL,
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${DIFY_KNOWLEDGE_API_KEY}`,
            },
            timeout: 300000,
        });
    }
    async updateDocumentMetadata(difyDocumentId, metadata) {
        try {
            const payload = this.createMetadataOperationData(difyDocumentId, metadata);
            const response = await this.client.post(`/datasets/${this.datasetId}/documents/metadata`, {
                operation_data: payload,
            });
            return response.data;
        }
        catch (error) {
            const axiosError = error;
            this.logger.error(`Error updating document metadata: ${axiosError.message}`, axiosError.stack);
            throw new error_middleware_1.DifyApiError(axiosError.message, axiosError.response?.status, axiosError.response?.data);
        }
    }
    async createDocumentFromText({ name, text, indexingTechnique = 'high_quality', processMode = 'automatic', }) {
        try {
            const payload = {
                name,
                text,
                indexing_technique: indexingTechnique,
                process_rule: {
                    mode: processMode,
                },
            };
            const response = await this.client.post(`/datasets/${this.datasetId}/document/create-by-text`, payload);
            return response.data;
        }
        catch (error) {
            const axiosError = error;
            this.logger.error(`Error creating document from text: ${axiosError.message}`, axiosError.stack);
            throw new error_middleware_1.DifyApiError(axiosError.message, axiosError.response?.status, axiosError.response?.data);
        }
    }
    createMetadataOperationData(difyDocumentId, metadata) {
        const metadataList = this.metadataFieldMappings
            .filter((mapping) => {
            const value = metadata[mapping.name];
            return value !== undefined && value !== null && value !== '';
        })
            .map((mapping) => {
            return {
                id: mapping.id,
                name: mapping.name,
                type: mapping.type,
                value: metadata[mapping.name],
            };
        });
        if (metadataList.length === 0) {
            return [];
        }
        return [
            {
                document_id: difyDocumentId,
                metadata_list: metadataList,
            },
        ];
    }
};
exports.DifyService = DifyService;
exports.DifyService = DifyService = DifyService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], DifyService);
//# sourceMappingURL=dify.service.js.map