"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var EcovadisIssueParserService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EcovadisIssueParserService = void 0;
const common_1 = require("@nestjs/common");
require("dotenv/config");
const llamaparse_service_1 = require("../llm/llamaparse.service");
const chat_gpt_service_1 = require("../llm/chat-gpt.service");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const ecovadis_entity_1 = require("../ecovadis/entities/ecovadis.entity");
const constants_1 = require("../constants");
let EcovadisIssueParserService = EcovadisIssueParserService_1 = class EcovadisIssueParserService {
    constructor(chatGptService, ecoVadisSustainabilityIssueRepository, ecoVadisThemeRepository, projectEcoVadisThemeRepository, ecoVadisQuestionRepository, ecoVadisAnswerOptionRepository, projectEcoVadisAnswerOptionRepository, dataSource) {
        this.chatGptService = chatGptService;
        this.ecoVadisSustainabilityIssueRepository = ecoVadisSustainabilityIssueRepository;
        this.ecoVadisThemeRepository = ecoVadisThemeRepository;
        this.projectEcoVadisThemeRepository = projectEcoVadisThemeRepository;
        this.ecoVadisQuestionRepository = ecoVadisQuestionRepository;
        this.ecoVadisAnswerOptionRepository = ecoVadisAnswerOptionRepository;
        this.projectEcoVadisAnswerOptionRepository = projectEcoVadisAnswerOptionRepository;
        this.dataSource = dataSource;
        this.logger = new common_1.Logger(EcovadisIssueParserService_1.name);
    }
    async parseEcovadisSustainabilityIssuePDF(filePath, projectId) {
        this.logger.log(`Parsing EcoVadis sustainability issue PDF on projectId: ${projectId}`);
        const pageSeparator = '<PAGE>=================</PAGE>';
        const allDocumentMarkdown = await (0, llamaparse_service_1.parseDocumentWithLlamaparseApi)({
            filePath,
            premiumMode: true,
            pageSeparator,
        });
        this.logger.log(`Received parsed document from Llamaparse API, text length: ${allDocumentMarkdown.text.length}`);
        const pages = allDocumentMarkdown.text.split(pageSeparator);
        const prompt = `You are an AI assistant tasked with extracting structured sustainability risk information from an EcoVadis industry profile document.

Given the following page of text, identify and extract any sustainability issue that appears, along with its metadata, and return it as a JSON object. If not a theme is mentioned, you can mention null.

Use the following structure:

{
  issues: [
    {
      "theme": <title of theme> | null,
      "impact": <impact score / Importance>,
      "issue": "<title of the sustainability issue>",
      "definition": "<short definition provided in the text>",
      "industry_issues": "<summary of the relevant challenges or context described in the 'Industry issues' section>"
    }
  ] 
}

Sensibly pick up the theme name (Ethik, Arbeits- & Menschenrechte, Allgemeines, General, Umwelt, Nachhaltige Beschaffung, Environment, Labor & Human Rights, Ethics or any other) from the text. If present theme usually appears at the beginning of the text before Bedeutung / Impact / Importance. Theme and issue are not the same, and the theme is not always mentioned. If the theme is not mentioned, you can mention null - DO NOT use issue name as theme.

If something is "Non-activated"or "Nicht aktiviert" skip it. Pick only those which contains definition and industry issues.

Extract text as it is from the page, and do not add any additional text or explanation. The output should be a valid JSON object.

### Page Text:
`;
        const issues = [];
        for (let i = 0; i < pages.length; i++) {
            if ((pages[i].includes('Branchenleistung') &&
                pages[i].includes('Verteilung der Gesamtbewertungen')) ||
                (pages[i].includes('Overall score distribution') &&
                    pages[i].includes('Industry Performance'))) {
                this.logger.log(`Reached end of relevant pages at page ${i + 1}, stopping parsing.`);
                break;
            }
            const gptresponse = await this.chatGptService.createCompletionWithAzure(constants_1.LLM_MODELS['gpt-4o'], [
                {
                    role: 'user',
                    content: prompt + pages[i],
                },
            ], true);
            try {
                const parsedResponse = JSON.parse(gptresponse);
                if (parsedResponse && parsedResponse.issues) {
                    issues.push(...parsedResponse.issues);
                }
            }
            catch (error) {
                this.logger.error(`Error parsing GPT response for page ${i + 1}: ${error.message}`);
            }
        }
        const issuesByThemeName = issues.reduce((acc, issue) => {
            const themeName = issue.theme || acc.lastTheme || '';
            acc.lastTheme = themeName;
            if (!acc.groups[themeName]) {
                acc.groups[themeName] = [];
            }
            acc.groups[themeName].push(issue);
            return acc;
        }, { groups: {}, lastTheme: null }).groups;
        this.logger.log(`Grouped issues by theme name, number of themes:`);
        for (const [themeName, themeIssues] of Object.entries(issuesByThemeName)) {
            const issuesArray = themeIssues;
            this.logger.log(`Processing theme: ${themeName}, number of issues: ${issuesArray.length}`);
            let theme = await this.ecoVadisThemeRepository.findOne({
                where: { title: themeName },
            });
            if (!theme) {
                theme = this.ecoVadisThemeRepository.create({
                    title: themeName,
                    description: '',
                });
                await this.ecoVadisThemeRepository.save(theme);
            }
            const issueEntries = [];
            for (const issue of issuesArray) {
                let existingIssue = await this.ecoVadisSustainabilityIssueRepository.findOne({
                    where: { issue: issue.issue },
                });
                if (!existingIssue) {
                    existingIssue = this.ecoVadisSustainabilityIssueRepository.create({
                        issue: issue.issue,
                        definition: issue.definition,
                        industryIssues: issue.industry_issues || '',
                    });
                    await this.ecoVadisSustainabilityIssueRepository.save(existingIssue);
                }
                issueEntries.push({
                    issueId: existingIssue.id,
                    impact: issue.impact,
                });
            }
            let projectTheme = await this.projectEcoVadisThemeRepository.findOne({
                where: { projectId, themeId: theme.id },
            });
            if (!projectTheme) {
                projectTheme = this.projectEcoVadisThemeRepository.create({
                    projectId,
                    themeId: theme.id,
                    impact: ecovadis_entity_1.ImpactScore.Medium,
                    issues: issueEntries,
                });
            }
            else {
                projectTheme.issues = issueEntries;
            }
            await this.projectEcoVadisThemeRepository.save(projectTheme);
        }
    }
    async cleanupGermanAnswerOptions(dryRun = true) {
        const results = {
            totalProcessed: 0,
            germanOptionsFound: 0,
            deletedCount: 0,
            errors: [],
        };
        this.logger.log(`Starting German answer options cleanup (dryRun: ${dryRun})`);
        try {
            const questions = await this.ecoVadisQuestionRepository.find({
                relations: ['answerOptions'],
                order: { createdAt: 'ASC' },
            });
            this.logger.log(`Found ${questions.length} questions to process`);
            const batchSize = 5;
            for (let i = 0; i < questions.length; i += batchSize) {
                const batch = questions.slice(i, i + batchSize);
                for (const question of batch) {
                    try {
                        const result = await this.processQuestionOptions(question, dryRun);
                        results.totalProcessed++;
                        results.germanOptionsFound += result.germanOptionsFound;
                        results.deletedCount += result.deletedCount;
                    }
                    catch (error) {
                        const errorMsg = `Error processing question ${question.id}: ${error.message}`;
                        this.logger.error(errorMsg);
                        results.errors.push(errorMsg);
                    }
                }
            }
        }
        catch (error) {
            const errorMsg = `Fatal error in cleanup process: ${error.message}`;
            this.logger.error(errorMsg);
            results.errors.push(errorMsg);
        }
        this.logger.log(`Cleanup completed. Results:`, results);
        return results;
    }
    async processQuestionOptions(question, dryRun) {
        if (!question.answerOptions || question.answerOptions.length < 2) {
            return { germanOptionsFound: 0, deletedCount: 0 };
        }
        this.logger.debug(`Processing question ${question.questionCode} with ${question.answerOptions.length} options`);
        const identification = await this.identifyGermanOptions(question.answerOptions);
        if (identification.germanOptionIds.length === 0) {
            return { germanOptionsFound: 0, deletedCount: 0 };
        }
        this.logger.log(`Found ${identification.germanOptionIds.length} German options for question ${question.questionCode}`);
        let deletedCount = 0;
        if (!dryRun) {
            deletedCount = await this.deleteGermanOptions(identification);
        }
        else {
            this.logger.log(`[DRY RUN] Would delete ${identification.germanOptionIds.length} German options`);
        }
        return {
            germanOptionsFound: identification.germanOptionIds.length,
            deletedCount,
        };
    }
    async identifyGermanOptions(options) {
        const optionsData = options.map((option) => ({
            id: option.id,
            issueTitle: option.issueTitle,
        }));
        const prompt = `
You are analyzing EcoVadis answer options to identify German text that are mixed with English text.

Answer Options:
${JSON.stringify(optionsData, null, 2)}

Task: Identify German options from the list if there are English options.
Get the exact UUIDs of the German options

Return JSON format:
{
"germanOptionIds": ["id1", "id2"]
}

If no German options are found or if no english options are found, return empty arrays.
`;
        try {
            const gptResponse = await this.chatGptService.createCompletionWithAzure(constants_1.LLM_MODELS['o4-mini'], [
                {
                    role: 'user',
                    content: prompt,
                },
            ], true);
            const parsedResponse = JSON.parse(gptResponse);
            const germanIds = parsedResponse.germanOptionIds || [];
            return {
                questionId: options[0]?.questionId || '',
                germanOptionIds: germanIds,
            };
        }
        catch (error) {
            this.logger.error(`Error in GPT identification: ${error.message}`);
            return {
                questionId: options[0]?.questionId || '',
                germanOptionIds: [],
            };
        }
    }
    async deleteGermanOptions(identification) {
        try {
            const existingAnswers = await this.dataSource.manager.find(ecovadis_entity_1.ProjectEcoVadisAnswer, {
                where: { optionId: (0, typeorm_2.In)(identification.germanOptionIds) },
            });
            this.logger.warn(`Found ${existingAnswers.length} existing answers linked to German options.`);
            const deleteProjectAnswersResult = await this.projectEcoVadisAnswerOptionRepository.delete({
                optionId: (0, typeorm_2.In)(identification.germanOptionIds),
            });
            this.logger.log(`Deleted ${deleteProjectAnswersResult.affected} project answers linked to German options`);
            const deleteResult = await this.ecoVadisAnswerOptionRepository.delete({
                id: (0, typeorm_2.In)(identification.germanOptionIds),
            });
            this.logger.log(`Deleted ${deleteResult.affected} German options with IDs: ${identification.germanOptionIds.join(', ')}`);
            return deleteResult.affected || 0;
        }
        catch (error) {
            this.logger.error(`Error deleting German options: ${error.message}`);
            throw error;
        }
    }
};
exports.EcovadisIssueParserService = EcovadisIssueParserService;
exports.EcovadisIssueParserService = EcovadisIssueParserService = EcovadisIssueParserService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(ecovadis_entity_1.EcoVadisSustainabilityIssue)),
    __param(2, (0, typeorm_1.InjectRepository)(ecovadis_entity_1.EcoVadisTheme)),
    __param(3, (0, typeorm_1.InjectRepository)(ecovadis_entity_1.ProjectEcoVadisTheme)),
    __param(4, (0, typeorm_1.InjectRepository)(ecovadis_entity_1.EcoVadisQuestion)),
    __param(5, (0, typeorm_1.InjectRepository)(ecovadis_entity_1.EcoVadisAnswerOption)),
    __param(6, (0, typeorm_1.InjectRepository)(ecovadis_entity_1.ProjectEcoVadisAnswer)),
    __metadata("design:paramtypes", [chat_gpt_service_1.ChatGptService,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource])
], EcovadisIssueParserService);
//# sourceMappingURL=ecovadis-issue-parser.service.js.map