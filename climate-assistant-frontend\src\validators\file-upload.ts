import * as z from 'zod';

const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
const ACCEPTED_FILE_TYPES = [
  'application/pdf',
  // 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
  // 'application/msword', // doc
  // 'application/vnd.ms-works', // wps
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
  'application/vnd.ms-excel', // xls, xlw
  'text/csv',
  'application/vnd.ms-excel.sheet.macroEnabled.12', // xlsm
  'application/vnd.ms-excel.sheet.binary.macroEnabled.12', // xlsb
  // 'image/jpeg', // jpg, jpeg
  // 'image/png', // png
  // 'application/vnd.openxmlformats-officedocument.presentationml.presentation', // pptx
  // 'application/vnd.ms-powerpoint', // ppt
  // 'application/vnd.ms-powerpoint.presentation.macroEnabled.12', // pptm
  // 'application/xml', // xml
  'text/plain', // txt
] as const;

const fileSchema = z.instanceof(File).superRefine((file, ctx) => {
  if (file.size > MAX_FILE_SIZE) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: `File "${file.name}" exceeds the maximum size of 10MB.`,
    });
  }
  if (!ACCEPTED_FILE_TYPES.includes(file.type as any)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: `File "${file.name}" must be of type .pdf, .txt, .xlsx, .xls or .csv.`,
    });
  }
});

export const documentSettingsSchema = z.object({
  documentType: z.string().min(1, 'Document type is required'),
  esrsCategory: z
    .array(z.string())
    .min(1, 'At least one ESRS category must be selected'),
  year: z
    .number()
    .min(1900, 'Year must be valid')
    .max(new Date().getFullYear(), 'Year cannot be in the future'),
  month: z.number().optional(),
  day: z.number().optional(),
  remarks: z.string().optional(),
  files: z.array(fileSchema).optional(),
});

export const fileUploadSchema = documentSettingsSchema.extend({
  files: z
    .array(fileSchema)
    .length(1, 'Only one file can be uploaded')
    .refine((files) => files.length > 0, 'A file is required'),
});

export type DocumentSettingsFormData = z.infer<typeof documentSettingsSchema>;
export type FileUploadFormData = z.infer<typeof fileUploadSchema>;
