import { DataRequest } from 'src/data-request/entities/data-request.entity';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { DocumentChunk } from 'src/document/entities/document-chunk.entity';
import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { ESRSDisclosureRequirement } from 'src/knowledge-base/entities/esrs-disclosure-requirement.entity';
import { Language } from 'src/project/entities/project.entity';
import { ESRSTopic } from 'src/knowledge-base/entities/esrs-topic.entity';
interface MDRInstructions {
    elementType: string;
    elementIdentification: string;
    specificRequirements: string;
    specialNotes: string;
    fallbackHandling: string;
    responseFormat?: string;
}
export declare class PromptService {
    private readonly GENERATION_LANGUAGE;
    private readonly turndownService;
    constructor();
    getMDRInstructions(datapointId: string): MDRInstructions;
    generateDisclosureRequirementClassificationPrompt1(): string;
    generateDisclosureRequirementClassificationPrompt2(esrsDisclosureRequirements: ESRSDisclosureRequirement[]): string;
    generateDatapointClassificationPrompt1(): string;
    generateDatapointClassificationPrompt2(disclosureRequirement: ESRSDisclosureRequirement): string;
    generateSystemPromptForAllDatapoints(esrsDatapoints: ESRSDatapoint[]): string;
    generateDatapointGapAnalysisSystemPrompt1({ esrsDatapoint, generationLanguage, }: {
        esrsDatapoint: ESRSDatapoint;
        generationLanguage: Language;
    }): string;
    generateDatapointGapAnalysisDatapointSpecificSystemPrompt(esrsDatapoint: ESRSDatapoint, otherDatapoints: ESRSDatapoint[]): string;
    generateDatapointGapAnalysisContentContext(content: string): string;
    generateDatapointGapAnalysisSystemPrompt2({ esrsDatapoint, generationLanguage, reportTextGenerationRules, generalCompanyProfile, reportingYear, }: {
        esrsDatapoint: ESRSDatapoint;
        generationLanguage: Language;
        reportTextGenerationRules: string;
        generalCompanyProfile: string;
        reportingYear: string;
    }): string;
    generateDatapointGapAnalysisSystemO3Prompt({ esrsDatapoint, materialTopics, nonMaterialTopics, otherDatapoints, reportTextGenerationRules, generalCompanyProfile, content, conditional, reportingYear, generationLanguage, }: {
        esrsDatapoint: ESRSDatapoint;
        materialTopics: ESRSTopic[];
        nonMaterialTopics: ESRSTopic[];
        otherDatapoints: ESRSDatapoint[];
        reportTextGenerationRules: string;
        generalCompanyProfile: string;
        content: string;
        conditional: boolean;
        reportingYear: string;
        generationLanguage: Language;
    }): string;
    generateDatapointContentGenerationSystemPrompt1(esrsDatapoint: ESRSDatapoint): string;
    generateDatapointContentGenerationExample(): string;
    generateDatapointContentGenerationSpecificDPSystemPrompt(esrsDatapoint: ESRSDatapoint, otherDatapoints: ESRSDatapoint[]): string;
    createFullEsrsDatapointLawTextContext(esrsDatapoint: ESRSDatapoint): string;
    generateDatapointContentGenerationSystemPrompt2({ esrsDatapoint, generationLanguage, reportTextGenerationRules, generalCompanyProfile, customUserRemark, }: {
        esrsDatapoint: ESRSDatapoint;
        generationLanguage: Language;
        generalCompanyProfile: string;
        reportTextGenerationRules: string;
        customUserRemark: string;
    }): string;
    generateDataRequestGapAnalysisSystemPrompt({ esrsDisclosureRequirement, generationLanguage, }: {
        esrsDisclosureRequirement: ESRSDisclosureRequirement;
        generationLanguage: Language;
    }): string;
    generateDataRequestGapAnalysisContentContext(dataRequest: DataRequest): string;
    generateDataRequestFullLawTextContextForReportedDatapoints(dataRequest: DataRequest): string;
    generateDataRequestGapAnalysisReviewSystemPrompt({ generationLanguage, }: {
        generationLanguage: Language;
    }): string;
    generateDataRequestContextFromDatapoints(dataRequest: DataRequest): string;
    generateDataRequestContentGenerationSystemPrompt1(disclosureRequirement: ESRSDisclosureRequirement): string;
    generateDataRequestContentExampleIntegrated(): string;
    generateDataRequestContentExampleTechnical(): string;
    generateDataRequestContentGenerationSystemPrompt2({ disclosureRequirement, generationLanguage, reportTextGenerationRules, customUserRemark, enableDatapointTags, }: {
        disclosureRequirement: ESRSDisclosureRequirement;
        generationLanguage: Language;
        reportTextGenerationRules: string;
        customUserRemark?: string;
        enableDatapointTags: boolean;
    }): string;
    reduceLinkedDocumentChunkPrompt(datapointRequest: DatapointRequest, documentChunk: DocumentChunk): string;
    validateReportTextGenerationRulesPrompt(language: Language): string;
    formatTopics({ topics, level, material, }: {
        topics: ESRSTopic[];
        level?: number;
        material: boolean;
    }): string[];
    datapointRequestGapAnalysisSystemPromptMaterialTopics({ topics, material, }: {
        topics: ESRSTopic[];
        material: boolean;
    }): string;
    datapointRequestContentGenerationSystemPromptMaterialTopics({ topics, material, }: {
        topics: ESRSTopic[];
        material: boolean;
    }): string;
    datapointRequestContentGenerationSystemPromptRelatedDatapoints(relatedDatapoints: DatapointRequest[]): string;
}
export {};
