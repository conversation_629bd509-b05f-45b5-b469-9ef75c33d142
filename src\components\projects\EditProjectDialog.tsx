
import * as React from "react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import { Project } from "@/types/project";

// Form schema for the project
const formSchema = z.object({
  name: z.string().min(1, { message: "Project name is required" })
});

type ProjectFormValues = z.infer<typeof formSchema>;

interface EditProjectDialogProps {
  project: Project;
  onUpdate: (project: Pick<Project, "name">) => void;
  trigger: React.ReactNode;
}

export function EditProjectDialog({ 
  project,
  onUpdate,
  trigger
}: EditProjectDialogProps) {
  const [open, setOpen] = useState(false);
  
  const form = useForm<ProjectFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: project.name,
    },
  });

  function onSubmit(data: ProjectFormValues) {
    // Make sure we're passing all required fields
    const updatedProject: Pick<Project, "name"> = {
      name: data.name,
    };
    
    // Handle the form submission
    onUpdate(updatedProject);
    
    // Show success notification
    toast.success("Project updated successfully", {
      description: `${data.name} has been updated.`,
    });
    
    // Close the dialog
    setOpen(false);
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-gray-900">Edit Project</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            Update your project details below.
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 pt-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project Name</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter project name" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <DialogFooter className="pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button 
                type="submit"
                variant="darkBlue"
              >
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
