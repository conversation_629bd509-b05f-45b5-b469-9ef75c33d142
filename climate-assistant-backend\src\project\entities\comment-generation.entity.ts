import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  Index,
  CreateDateColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { DatapointRequest } from '../../datapoint/entities/datapoint-request.entity';
import { DataRequest } from '../../data-request/entities/data-request.entity';
import { CommentType } from './comment.entity';

export enum CommentStatus {
  Pending = 'pending',
  Approved = 'approved',
  Rejected = 'rejected',
}

@Entity()
@Index(['commentableId', 'commentableType'])
export class CommentGeneration {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  userId: string;

  @Column('uuid')
  commentableId: string;

  @Column({ type: 'enum', enum: CommentType })
  commentableType: CommentType;

  @Column({ type: 'text' })
  comment: string;

  @Column({ type: 'enum', enum: CommentStatus, default: CommentStatus.Pending })
  status: CommentStatus;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => User, (user) => user.comments)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'uuid', nullable: true })
  evaluatorId: string;

  @JoinColumn({ name: 'evaluatorId' })
  evaluator: User;

  @Column({ type: 'varchar', nullable: true })
  evaluatorComment: string;

  @Column({ type: 'timestamp', nullable: true })
  evaluatedAt: Date;

  @ManyToOne(
    () => DataRequest,
    (dataRequest) => dataRequest.commentGenerations,
    {
      createForeignKeyConstraints: false,
    },
  )
  @JoinColumn({ name: 'commentableId' })
  dataRequest: DataRequest;

  @ManyToOne(
    () => DatapointRequest,
    (datapointRequest) => datapointRequest.commentGenerations,
    { createForeignKeyConstraints: false },
  )
  @JoinColumn({ name: 'commentableId' })
  datapointRequest: DatapointRequest;
}
