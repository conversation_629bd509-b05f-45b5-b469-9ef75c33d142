"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InitiativeSuggestionService = void 0;
const common_1 = require("@nestjs/common");
const chat_gpt_service_1 = require("../llm/chat-gpt.service");
const users_service_1 = require("../users/users.service");
const multi_question_search_engine_service_1 = require("../util/multi-question-search-engine.service");
const knowledge_base_service_1 = require("../knowledge-base/knowledge-base.service");
const constants_1 = require("../constants");
let InitiativeSuggestionService = class InitiativeSuggestionService {
    constructor(chatGptService, usersService, searchEngineTool, knowledgeBaseService) {
        this.chatGptService = chatGptService;
        this.usersService = usersService;
        this.searchEngineTool = searchEngineTool;
        this.knowledgeBaseService = knowledgeBaseService;
        this.gptModel = constants_1.LLM_MODELS['gpt-4o'];
    }
    createInitiativeSuggestionCreator(userId) {
        return {
            type: 'replacing-gpt-tool',
            toolDefinition: {
                type: 'function',
                function: {
                    name: 'initiative-suggestion-creator',
                    description: 'Generiert Maßnahmenvorschläge für das Unternehmen. Das Ergebnis MUSS 1-1 übernommen werden und darf nicht verändert werden!',
                },
            },
            execute: (_, messages) => this.createInitiativeSuggestions(messages, userId),
        };
    }
    async *createInitiativeSuggestions(previousMessages, userId) {
        console.log('[Initiative Suggestion Creator] Called');
        const filteredMessages = previousMessages.filter((m) => m.role !== 'system');
        const context = await this.usersService.getUserPromptContext(userId);
        const keyInformation = await this.extractKeyInformation(context, filteredMessages);
        const fetchKnowledgeBaseResults = await this.getKnowledgebaseAnswers(context, filteredMessages, keyInformation);
        const fetchSearchEngineResults = await this.extractSearchEngineQuestions(context, filteredMessages, keyInformation);
        const [questionAnswerPairs, knowledgeBaseChunks] = await Promise.all([fetchSearchEngineResults, fetchKnowledgeBaseResults]);
        const ideas = await this.generateIdeas(context, questionAnswerPairs, knowledgeBaseChunks, keyInformation);
        console.log('[Ideen]:', ideas);
        const stream = await this.generateInitiativeSuggestionsStream(context, ideas, questionAnswerPairs, knowledgeBaseChunks, keyInformation, filteredMessages);
        yield '\n';
        for await (const part of stream) {
            yield part;
        }
        return;
    }
    async generateInitiativeSuggestionsStream(context, ideas, questionAnswerPairs, knowledgeBaseChunks, keyInformation, previousMessages) {
        return this.chatGptService.createCompletionStream(this.gptModel, [
            {
                role: 'system',
                content: generateInitativesPrompt(context, ideas, questionAnswerPairs, keyInformation, knowledgeBaseChunks, previousMessages),
            },
        ]);
    }
    async generateIdeas(context, questionAnswerPairs, knowledgeBaseChunks, keyInformation) {
        const result = await this.chatGptService.createCompletion(this.gptModel, [
            {
                role: 'system',
                content: generateSuggestionsPrompt(context, questionAnswerPairs, keyInformation, knowledgeBaseChunks),
            },
        ], true);
        const { ideas } = JSON.parse(result);
        return ideas;
    }
    async extractSearchEngineQuestions(context, previousMessages, keyInformation) {
        const messages = [
            {
                role: 'system',
                content: generateQuestionsSearchEnginePrompt(context, previousMessages, keyInformation),
            },
        ];
        const questionString = await this.chatGptService.createCompletion(this.gptModel, messages, true);
        const { questions } = JSON.parse(questionString);
        return await this.searchEngineTool.searchMultipleQuestions({
            questions,
        });
    }
    async getKnowledgebaseAnswers(context, previousMessages, keyInformation) {
        const messages = [
            {
                role: 'system',
                content: generateQuestionsKnowledgeBasePrompt(context, previousMessages, keyInformation),
            },
        ];
        const questionString = await this.chatGptService.createCompletion(this.gptModel, messages, true);
        const { questions } = JSON.parse(questionString);
        console.log('[Knowledge-Base] Received questions:', questions);
        const chunks = await this.knowledgeBaseService.getSimilarChunksForMultipleQuestions(questions, 3);
        return chunks;
    }
    async extractKeyInformation(context, previousMessages) {
        const messages = [
            {
                role: 'system',
                content: extractKeyInformationPrompt(context, previousMessages),
            },
        ];
        const keyInformation = await this.chatGptService.createCompletion(this.gptModel, messages, true);
        console.log(keyInformation);
        try {
            return JSON.parse(keyInformation);
        }
        catch (e) {
            return { location: '', budget: '', timeframe: '', focus_topics: [] };
        }
    }
};
exports.InitiativeSuggestionService = InitiativeSuggestionService;
exports.InitiativeSuggestionService = InitiativeSuggestionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [chat_gpt_service_1.ChatGptService,
        users_service_1.UsersService,
        multi_question_search_engine_service_1.MultiQuestionSearchEngine,
        knowledge_base_service_1.KnowledgeBaseService])
], InitiativeSuggestionService);
const extractKeyInformationPrompt = (context, previousMessages) => `
  Du bist ein Assistent der wichtige Informationen aus einer Konversation extrahiert.
  
  Extrahiere folgende Informationen aus der Konversation und dem Kontext.
   "budget": "Budget des Unternehmens", // Idealerweise aus <Previous_Messages> aber wenn nicht vorhanden aus <Context> oder eine Schätzung
   "timeframe": "Zeitrahmen des Unternehmens", // Idealerweise aus <Previous_Messages> aber wenn nicht vorhanden aus <Context> oder eine Schätzung
   "focus_topics": ["Thema 1", "Thema 2"], // Idealerweise aus <Previous_Messages> aber wenn nicht vorhanden aus <Context> oder eine Schätzung
   "location": "Standort des Unternehmens" // Idealerweise aus <Previous_Messages> aber wenn nicht vorhanden aus <Context> oder eine Schätzung
  
  Deine Antwort MUSS ein VALIDES JSON Objekt sein - es darf nur die oben genannten Informationen enthalten sonst nichts!
  
  <Context>${context}</Context>
  
  <Previous_Messages>${previousMessages.map((m) => `[${m.role}]: ${m.content}\n`)}</Previous_Messages>
`;
const generateQuestionsSearchEnginePrompt = (context, previousMessages, keyInformation) => `
  Du bist erfahrene Beraterin spezialisiert auf Nachhaltigkeitsberatung von großen Unternehmen.
  Du begleitest seit über 10 Jahren Unternehmen und ihre Mitarbeitenden und Teams in der nachhaltigen Transformation und gibst systematische und konkrete Maßnahmen, wie Unternehmen erfolgreich und effizient ihre Nachhaltigkeitsziele erreichen können.

  Ziel: Fragen formulieren die einer Suchmaschine gestellt werden um Informationen für Maßnahmen zur CO2 Reduktion zu erhalten.
  
  Wichtig:
   a. Stelle sicher, dass diese innerhalb des Budgets von "${keyInformation.budget}" und Zeitrahmens von "${keyInformation.timeframe}" des Unternehmens liegen
   b. Stelle sicher, dass diese die Fokusthemen des Unternehmens: "${keyInformation.focus_topics.join(', ')}" am Standort ${keyInformation.location} adressieren 

  Aufgaben:
  1. Überlege dir welche Informationen du benötigst um Maßnahmen für CO2 Reduktion zu liefern.
  2. Checke welche dieser Fragen du bereits mit dem <Context> und <Previous_Messages> unten beantworten kannst
  3. Liefere eine Liste an Fragen du du einer öffentlichen Suchmaschine stellen würdest, um die restlichen Informationen zu erhalten
    a. Deine Antwort MUSS ein VALIDES JSON Array sein. Es darf nur die Fragen enthalten sonst nichts!
 
  <Context>${context}</Context>
  
  <Previous_Messages>${previousMessages.map((m) => `[${m.role}]: ${m.content}\n`)}</Previous_Messages>
`;
const generateQuestionsKnowledgeBasePrompt = (context, previousMessages, keyInformation) => `
  Du bist erfahrene Beraterin spezialisiert auf Nachhaltigkeitsberatung von großen Unternehmen.
  Du begleitest seit über 10 Jahren Unternehmen und ihre Mitarbeitenden und Teams in der nachhaltigen Transformation und gibst systematische und konkrete Maßnahmen, wie Unternehmen erfolgreich und effizient ihre Nachhaltigkeitsziele erreichen können.

  Ziel: Fragen formulieren die der internen Wissensdatenban gestellt werden um Informationen für Maßnahmen zur CO2 Reduktion zu erhalten.
  
  Wichtig:
   a. Stelle sicher, dass diese innerhalb des Budgets von "${keyInformation.budget}" und Zeitrahmens von "${keyInformation.timeframe}" des Unternehmens liegen
   b. Stelle sicher, dass diese die Fokusthemen des Unternehmens: "${keyInformation.focus_topics.join(', ')}" am Standort ${keyInformation.location} adressieren 

  Aufgaben:
  1. Überlege dir welche Informationen du benötigst um Maßnahmen für CO2 Reduktion zu liefern.
  2. Checke welche dieser Fragen du bereits mit dem <Context> und <Previous_Messages> unten beantworten kannst
  3. Liefere eine Liste an Fragen die du der internen Wissensdatenbank stellen würdest, um die restlichen Informationen zu erhalten
    a. a. Deine Antwort MUSS ein VALIDES JSON Array sein. Es darf nur die Fragen enthalten sonst nichts!
 
  <Context>${context}</Context>
  
  <Previous_Messages>${previousMessages.map((m) => `[${m.role}]: ${m.content}\n`)}</Previous_Messages>
`;
const generateSuggestionsPrompt = (context, questionAnswerPairs, keyInformation, knowledgeBaseChunks) => `
  Du bist erfahrene Beraterin spezialisiert auf Nachhaltigkeitsberatung von großen Unternehmen.
  Du begleitest seit über 10 Jahren Unternehmen und ihre Mitarbeitenden und Teams in der nachhaltigen Transformation und gibst systematische und konkrete Maßnahmen, wie Unternehmen erfolgreich und effizient ihre Nachhaltigkeitsziele erreichen können.

  Ziel: Nutze den gegebenen <Context>, die <Search_Engine_Results> und die <Knowledge_Base_Vector_Search_Results> um 25 Ideen für Maßnahmenvorschläge für CO2 Reduktion zu generieren.
  
  Wichtig:
   a. Stelle sicher, dass diese innerhalb des Budgets von "${keyInformation.budget}" und Zeitrahmens von "${keyInformation.timeframe}" des Unternehmens liegen
   b. Stelle sicher, dass diese die Fokusthemen des Unternehmens: "${keyInformation.focus_topics.join(', ')}" am Standort ${keyInformation.location} adressieren 
  
  
  Antworte mit folgendem JSON Format:
  {ideas: ["Idee 1", "Idee 2", "Idee 3"]}
  
  <Context>${context}</Context>
  <Search_Engine_Results>${questionAnswerPairs}</Search_Engine_Results>
  <Knowledge_Base_Vector_Search_Results>${knowledgeBaseChunks.join(',')}</Knowledge_Base_Vector_Search_Results>
  `;
const generateInitativesPrompt = (context, ideas, questionAnswerPairs, keyInformation, knowledgeBaseChunks, previousMessages) => `
  Du bist erfahrene Beraterin spezialisiert auf Nachhaltigkeitsberatung von großen Unternehmen.
  Du begleitest seit über 10 Jahren Unternehmen und ihre Mitarbeitenden und Teams in der nachhaltigen Transformation und gibst systematische und konkrete Maßnahmen, wie Unternehmen erfolgreich und effizient ihre Nachhaltigkeitsziele erreichen können.

  Aufgaben:
  1. Suche dir die 5 besten Maßnahmenvorschläge aus den <Ideen_für_Maßnahmenvorschlaege>
    a. Nutze den gegebenen <Context>, die <Search_Engine_Results>, <Knowledge_Base_Vector_Search_Results> und <Bisheriger_Chatverlauf> für die Entscheidung 
    b. Stelle sicher, dass die gewählten Initiativen innerhalb Budgets von "${keyInformation.budget}" und Zeitrahmens von "${keyInformation.timeframe}" des Unternehmens liegen
    c. Stelle sicher, dass diese die gewählten Initiativen die Fokusthemen des Unternehmens: "${keyInformation.focus_topics.join(', ')}" am Standort ${keyInformation.location} adressiere
  2. Erstelle für jeden Maßnahmenvorschlag eine Beschreibung
    - Folge strikt dem <Format> und den <Rahmenbedingungen_Maßnahmen> um die Vorschläge zu strukturieren.
    - Stelle sicher, dass der Button für jede Initiative verfügbar ist und genau dem Format folgt.
    - Verwende keine Aufzählungen sondern einfach nur Überschriften und Text.
    - Zitiere keine Quellen und füge auch keine Links hinzu
    - Antworte nur mit dem finalen Ergebnis - keine Zwischenantworten
  
  <Rahmenbedingungen_Maßnahmen>
    Um sicherzustellen, dass die Maßnahmen auch auf das Unternehmen maßgeschneidert sind, orientierst du dich an den Unternehmenszielen und -strategien die unten bereitgestellt werden.
    Hier nimmst du den individuellen Input von Unternehmen durch Nachhaltigkeitsberichte und Erfahrung der Nachhaltigkeitsverantwortlichen zur Hand.
    Zudem bereicherst du die Maßnahmen stets mit aktuellen Zahlen, Daten und Fakten und hinterlegst die stets mit Quellen.
  
    Du gibst auf Basis des Inputs 5 mögliche Maßnahmen vor, die Unternehmen bei der Erreichung ihrer Ziele unterstützen.
    In einem ersten Schritt reicht lediglich der Titel der Maßnahme, sowie eine kurze Summary.
    Diese soll max. 200-300 Wörter pro Maßnahme sein und die wichtigsten Infos für eine Entscheidungsgrundlage für oder gegen die Maßnahme liefern.
    
    Stelle sicher, dass Kennzahlen inkludiert sind und wie sich diese auf die Ziele des Unternehmens auswirken.
  </Rahmenbedingungen_Maßnahmen>  
  
  <Format>
  ### 1. Überschrift der Maßnahme
  
  200-300 Wörter zur Maßnahme
  
  <button class="markdown-detail-button" data-initiative="1">Details erläutern</button>
  </Format>
  
  <Context>${context}</Context>
  <Search_Engine_Results>${questionAnswerPairs}</Search_Engine_Results>
  <Knowledge_Base_Vector_Search_Results>${knowledgeBaseChunks.join(',')}</Knowledge_Base_Vector_Search_Results>
  <Ideen_für_Maßnahmenvorschlaege>${ideas.join(',\n')}</Ideen_für_Maßnahmenvorschlaege>
  <Bisheriger_Chatverlauf>${previousMessages.map((m) => `[${m.role}]: ${m.content}\n`)}</Bisheriger_Chatverlauf>
  `;
const generateFeedbackPrompt = (initiativeSuggestions, keyInformation, context) => `
  Du bist erfahrene Beraterin spezialisiert auf Nachhaltigkeitsberatung von großen Unternehmen.
  Du begleitest seit über 10 Jahren Unternehmen und ihre Mitarbeitenden und Teams in der nachhaltigen Transformation und gibst systematische und konkrete Maßnahmen, wie Unternehmen erfolgreich und effizient ihre Nachhaltigkeitsziele erreichen können.

  Dir wird eine Liste an Maßnahmenvorschlägen für CO2 Reduktion präsentiert.
  Du sollst diese Vorschläge überprüfen und Feedback geben, ob diese den Anforderungen des Unternehmens entsprechen.
  
  Aufgabe
  1. Sieh dir jeden Maßnahmenvorschlag einzeln durch
     a. Besonderer Fokus: innerhalb des Budgets von "${keyInformation.budget}" und Zeitrahmens von "${keyInformation.timeframe}" des Unternehmens liegen
     b. Besonderer Fokus: die Fokusthemen des Unternehmens: "${keyInformation.focus_topics.join(', ')}" am Standort ${keyInformation.location} adressieren
  2. Sammle dein Feedback
  3. Antworte mit folgendem JSON Format:
  {
     "feedback": "Feedback zu den Maßnahmenvorschlägen" // dieses Feedback wird genutzt um die Maßnahmen zu verbessern - stelle sicher, dass es actionable ist
     "bewertung" : [1-5] // 1-5 Sterne Bewertung
  }

  Stelle sicher, dass das Format mit dem du antwortest dem JSON Format entspricht und ausschließlich die oben genannten Informationen enthält.

  <Maßnahmen_Vorschläge>${initiativeSuggestions}</Maßnahmen_Vorschläge>
  <Context>${context}</Context>
  `;
//# sourceMappingURL=initiative-suggestion.service.js.map