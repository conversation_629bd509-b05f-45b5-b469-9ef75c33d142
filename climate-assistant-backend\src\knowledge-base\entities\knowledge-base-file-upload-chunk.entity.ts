import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { KnowledgeBaseFileUpload } from './knowledge-base-file-upload.entity';

// important: this entity is excluded from sync as the unsupported vector type leads to drop / recreation of it with every migration
// if you want to change this entity -> make sure to adapt the migration file (embedding column)
// @Entity()
@Entity({ synchronize: false })
export class KnowledgeBaseFileUploadChunk {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => KnowledgeBaseFileUpload, (fileUpload) => fileUpload.chunks)
  @JoinColumn({ name: 'fileUploadId' })
  fileUpload: KnowledgeBaseFileUpload;

  @Column({ type: 'uuid' })
  fileUploadId: string;

  @Column({ type: 'varchar' })
  content: string;

  @Column({
    type: 'vector' as 'array',
    length: 1536,
    nullable: true,
    select: false,
  })
  embedding: number[];
}
