import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { DatapointRequestService } from './datapoint-request.service';
import { DataRequestService } from '../data-request/data-request.service';
import { Reflector } from '@nestjs/core';

@Injectable()
export class DatapointRequestGuard implements CanActivate {
  constructor(
    private readonly dataRequestService: DataRequestService,
    private readonly datapointRequestService: DatapointRequestService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const datapointRequestId = request.params.datapointRequestId;
    const workspaceId = request.user.workspaceId;

    const datapointRequest =
      await this.datapointRequestService.findById(datapointRequestId);

    const dataRequestId = datapointRequest.dataRequestId;

    const dataRequest =
      await this.dataRequestService.findProject(dataRequestId);
    const project = dataRequest.project;

    if (project.workspaceId !== workspaceId) {
      throw new UnauthorizedException(`Project is not from this workspace`);
    }

    request.datapointRequest = datapointRequest;

    const customCheck = this.reflector.get<string>(
      'customCheck',
      context.getHandler(),
    );

    switch (customCheck) {
      case 'generateWithAI':
        return this.datapointRequestService.validateDatapointRequestGenerationRightsOrFail(
          {
            datapointRequest,
            userId: request.user.id,
          },
        );
      default:
        return true;
    }
  }
}
