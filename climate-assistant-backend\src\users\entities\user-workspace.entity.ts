import {
  <PERSON><PERSON><PERSON>,
  Column,
  ManyTo<PERSON>ne,
  PrimaryColumn,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
} from 'typeorm';
import { User } from './user.entity';
import { Workspace } from '../../workspace/entities/workspace.entity';

export enum Role {
  SuperAdmin = 'SUPER_ADMIN',
  WorkspaceAdmin = 'WORKSPACE_ADMIN',
  AiContributor = 'AI_CONTRIBUTOR',
  AiReviewer = 'AI_ONLY_REVIEW',
  Contributor = 'CONTRIBUTOR',
}

@Entity()
export class UserWorkspace {
  @PrimaryColumn('uuid')
  userId: string;

  @PrimaryColumn('uuid')
  workspaceId: string;

  @Column({ type: 'enum', enum: Role, nullable: true })
  role: Role;

  @Column({ type: 'timestamp', nullable: true })
  joinedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => User, (user) => user.userWorkspaces)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Workspace, (workspace) => workspace.userWorkspaces)
  @JoinColumn({ name: 'workspaceId' })
  workspace: Workspace;
}
