import React from 'react';
import MDEditor, {
  ICommand,
  TextState,
  TextAreaTextApi,
  commands,
  PreviewType,
} from '@uiw/react-md-editor';
import {
  BoldIcon,
  ItalicIcon,
  UnderlineIcon,
  StrikethroughIcon,
  ListOrderedIcon,
  ListIcon,
  TableIcon,
  LinkIcon,
} from 'lucide-react';

interface MarkdownEditorProps {
  content: string;
  setContent: (data: string) => void;
  height?: number;
  preview?: PreviewType;
}
export const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  content,
  setContent,
  height = 500,
  preview = 'preview',
}) => {
  const paragraphFormatCommand: ICommand = commands.group(
    [
      commands.title1,
      commands.title2,
      commands.title3,
      commands.title4,
      commands.title5,
      commands.title6,
    ],
    {
      name: 'title',
      groupName: 'title',
      buttonProps: { 'aria-label': 'Insert title' },
    }
  );

  const underlineCommand: ICommand = {
    name: 'underline',
    keyCommand: 'underline',
    buttonProps: { 'aria-label': 'Add underline text' },
    icon: <UnderlineIcon className="h-4 w-4" />,
    execute: (state: TextState, api: TextAreaTextApi) => {
      const modifyText = `<u>${state.selectedText}</u>`;
      api.replaceSelection(modifyText);
    },
  };

  const strikethroughCommand: ICommand = {
    name: 'strikethrough',
    keyCommand: 'strikethrough',
    buttonProps: { 'aria-label': 'Add strikethrough text' },
    icon: <StrikethroughIcon className="h-4 w-4" />,
    execute: (state: TextState, api: TextAreaTextApi) => {
      const modifyText = '~~' + state.selectedText + '~~';
      api.replaceSelection(modifyText);
    },
  };

  const tableCommand: ICommand = {
    name: 'table',
    keyCommand: 'table',
    buttonProps: { 'aria-label': 'Insert table' },
    icon: <TableIcon className="h-4 w-4" />,
    execute: (state: TextState, api: TextAreaTextApi) => {
      console.log('tableCommand', state);
      const tableText = `| Header 1 | Header 2 |
| -------- | -------- |
| Cell 1   | Cell 2   |`;
      api.replaceSelection(tableText);
      console.log(state);
    },
  };

  return (
    <div className="my-4" data-color-mode="light">
      <MDEditor
        value={content}
        onChange={(value) => setContent(value || '')}
        preview={preview}
        height={height}
        commands={[
          paragraphFormatCommand,
          {
            name: 'bold',
            keyCommand: 'bold',
            buttonProps: { 'aria-label': 'Add bold text' },
            icon: <BoldIcon className="h-4 w-4" />,
            execute: (state: TextState, api: TextAreaTextApi) => {
              const modifyText = '**' + state.selectedText + '**';
              api.replaceSelection(modifyText);
            },
          },
          {
            name: 'italic',
            keyCommand: 'italic',
            buttonProps: { 'aria-label': 'Add italic text' },
            icon: <ItalicIcon className="h-4 w-4" />,
            execute: (state: TextState, api: TextAreaTextApi) => {
              const modifyText = '*' + state.selectedText + '*';
              api.replaceSelection(modifyText);
            },
          },
          underlineCommand,
          strikethroughCommand,
          {
            name: 'unorderedList',
            keyCommand: 'unorderedList',
            buttonProps: { 'aria-label': 'Add unordered list' },
            icon: <ListIcon className="h-4 w-4" />,
            execute: (state: TextState, api: TextAreaTextApi) => {
              api.replaceSelection('- ' + state.selectedText);
            },
          },
          {
            name: 'orderedList',
            keyCommand: 'orderedList',
            buttonProps: { 'aria-label': 'Add ordered list' },
            icon: <ListOrderedIcon className="h-4 w-4" />,
            execute: (state: TextState, api: TextAreaTextApi) => {
              api.replaceSelection('1. ' + state.selectedText);
            },
          },
          {
            name: 'link',
            keyCommand: 'link',
            buttonProps: { 'aria-label': 'Add link' },
            icon: <LinkIcon className="h-4 w-4" />,
            execute: (state: TextState, api: TextAreaTextApi) => {
              const modifyText = `[${state.selectedText}](url)`;
              api.replaceSelection(modifyText);
            },
          },
          tableCommand,
        ]}
        extraCommands={[
          commands.codePreview,
          commands.codeEdit,
          commands.fullscreen,
        ]}
      />
    </div>
  );
};
