# **Bull Queue Setup & Monitoring Guide**

This documentation serves as a guide for developers to seamlessly integrate new queues into the project using **Bull** and **BullBoard**. It covers the setup of a new queue, the structure of processors, and how to monitor jobs using BullBoard.

---

## **1. Understanding the Queue Structure**
A queue in **Bull** consists of:
- **Producers**: Services that add jobs to the queue.
- **Processors**: Workers that execute the jobs.
- **BullBoard**: A UI to monitor job processing.

Each queue follows a modular pattern, ensuring separation of concerns.

---

## **2. Setting Up a New Queue**
To integrate a new queue, follow these steps:

### **2.1 Register the Queue in a Module**
Each queue should be registered in a NestJS module using `BullModule.registerQueue()`.

**Example (queue.module.ts)**
```ts
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { BullBoardModule } from '@bull-board/nestjs';
import { BullAdapter } from '@bull-board/api/bullAdapter';
import { ExampleQueueProcessor } from './queue.service';
import { SomeServiceModule } from 'src/some-service/some-service.module';

@Module({
  imports: [
    BullModule.registerQueue(
      { name: 'example-queue' },  // Define queue name
    ),
    BullBoardModule.forFeature({
      name: 'example-queue',
      adapter: BullAdapter,
    }),
    SomeServiceModule,
  ],
  providers: [ExampleQueueProcessor],
})
export class ExampleQueueModule {}
```
📌 **Key Points:**
- The queue is registered with **BullModule**.
- **BullBoardModule** enables UI monitoring.
- The queue processor is added to `providers`.

---

### **2.2 Implementing the Queue Processor**
A **processor** defines how jobs in a queue are handled.

**Example (queue.service.ts)**
```ts
import { Injectable, Logger } from '@nestjs/common';
import { Processor, Process } from '@nestjs/bull';
import { Job } from 'bull';
import { SomeService } from 'src/some-service/some-service.service';

@Injectable()
@Processor('example-queue') // Link processor to the queue
export class ExampleQueueProcessor {
  private readonly logger = new Logger(ExampleQueueProcessor.name);

  constructor(private readonly someService: SomeService) {}

  @Process({ name: 'process-example-task', concurrency: 5 })
  async handleExampleTask(job: Job) {
    const { data } = job;
    this.logger.log(`Processing example task: ${JSON.stringify(data)}`);

    try {
      // Perform some processing
      await this.someService.performTask(data);
      this.logger.log(`Successfully processed job ${job.id}`);
    } catch (error) {
      this.logger.error(`Error processing job ${job.id}`, error);
      throw error;
    }
  }
}
```
📌 **Key Points:**
- `@Processor('example-queue')`: Binds the processor to a queue.
- `@Process()`: Defines a specific task inside the queue.
- `concurrency`: Defines how many jobs can run in parallel.

---

### **2.3 Adding Jobs to the Queue**
A **producer** service is responsible for adding jobs.

**Example (producer.service.ts)**
```ts
import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

@Injectable()
export class ExampleQueueProducer {
  constructor(@InjectQueue('example-queue') private readonly queue: Queue) {}

  async addExampleJob(taskData: any) {
    await this.queue.add(
      'process-example-task',
      taskData,
      {
        jobId: `example-${taskData.id}`,
        attempts: 3, // Retry up to 3 times
        backoff: { type: 'fixed', delay: 3000 }, // 3s retry delay
        removeOnComplete: isDevelopment ? false : true,
      }
    );
  }
}
```
📌 **Key Points:**
- `InjectQueue('example-queue')`: Injects the queue instance.
- `.add()`: Adds a job with retries and backoff settings.

---

## **3. Monitoring with BullBoard**
BullBoard provides a web UI to monitor queues.

### **3.1 Registering BullBoard in App Module**
Ensure **BullBoardModule** is configured globally.

**Example (app.module.ts)**
```ts
import { Module } from '@nestjs/common';
import { BullBoardModule } from '@bull-board/nestjs';
import { ExpressAdapter } from '@bull-board/express';

@Module({
  imports: [
    BullBoardModule.forRoot({
      route: '/api/queues', // Accessible at /api/queues
      adapter: ExpressAdapter,
    }),
  ],
})
export class AppModule {}
```

### **3.2 Accessing BullBoard**
Once the server is running, navigate to:

```
http://localhost:<PORT>/api/queues
```

📌 **Features Available in BullBoard:**
- View job status (`waiting`, `active`, `completed`, `failed`).
- Retry or remove jobs.
- Debug failed jobs.

---

## **4. Queue Best Practices**
- **Define Clear Job Names**: Helps with monitoring and debugging.
- **Use Concurrency Wisely**: Adjust based on workload.
- **Set Retry Policies**: Use `attempts` and `backoff` to handle failures gracefully.
- **Ensure Proper Logging**: Use `Logger` to track job execution.

---

## **5. Summary**
- **Step 1**: Register the queue in a module.
- **Step 2**: Implement the processor to handle jobs.
- **Step 3**: Use a producer service to add jobs.
- **Step 4**: Monitor jobs via BullBoard at `/api/queues`.

This guide provides a structured approach for adding new queues to the project. Follow these steps to integrate and manage job processing efficiently. 🚀