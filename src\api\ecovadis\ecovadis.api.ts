import { supabase } from "@/integrations/supabase/client";
import { EcovadisQuestion, GapItem, WorkspaceGap } from "@/types/ecovadis";
import { API_URL } from "../apiConstants";
import axios from "axios";

export interface EcovadisQuestionnaire {
  id: string;
  project_id: string;
  uploaded_at: string;
  file_path?: string;
  version: number;
  is_current: boolean;
}

export interface EcovadisProject {
  project: {
    id: string;
    name: string;
    type: string;
    description?: string;
    deadline?: string;
    status: string;
    progress?: {
      percentage: number;
      complete: number;
      incomplete: number;
      gaps: number;
    };
  };
  questionnaire?: EcovadisQuestionnaire;
  questions: EcovadisQuestion[];
  hasQuestionnaire: boolean;
}

/**
 * Fetches an EcoVadis project by ID
 */
export const getEcovadisProject = async (projectId: string): Promise<EcovadisProject> => {
  const { data, error } = await supabase.functions.invoke('get-ecovadis-project', {
    body: { projectId }
  });

  if (error) {
    console.error('Error fetching EcoVadis project:', error);
    throw new Error('Failed to fetch EcoVadis project details');
  }

  return data;
};

/**
 * Updates an EcoVadis question
 */
export const updateEcovadisQuestion = async (
  questionId: string, 
  updates: {
    status?: string;
    impact?: string;
    ecovadisScore?: number;
  }
): Promise<void> => {
  const { error } = await supabase.functions.invoke('update-ecovadis-question', {
    body: { questionId, updates }
  });

  if (error) {
    console.error('Error updating EcoVadis question:', error);
    throw new Error('Failed to update question');
  }
};

/**
 * Updates an EcoVadis question option
 */
export const updateEcovadisQuestionOption = async (
  optionId: string,
  projectId: string,
  updates: {
    selected?: boolean;
    evidence_examples?: string;
  }
): Promise<void> => {
  const { error } = await supabase.functions.invoke('update-ecovadis-option', {
    body: { optionId, projectId, updates }
  });

  if (error) {
    console.error('Error updating option:', error);
    throw new Error('Failed to update question option');
  }
};

/**
 * Updates an EcoVadis gap
 */
export const updateEcovadisGap = async (
  gapId: string, 
  isComplete: boolean
): Promise<void> => {
  const { error } = await supabase.functions.invoke('update-ecovadis-gap', {
    body: { gapId, isComplete }
  });

  if (error) {
    console.error('Error updating gap:', error);
    throw new Error('Failed to update gap');
  }
};

/**
 * Assigns a gap to a user
 */
export const assignGapToUser = async (
  gapId: string,
  userId: string | null
): Promise<void> => {
  const { error } = await supabase.functions.invoke('update-ecovadis-gap', {
    body: { gapId, assigneeId: userId }
  });

  if (error) {
    console.error('Error assigning gap:', error);
    throw new Error('Failed to assign gap');
  }
};

/**
 * Deletes an EcoVadis gap
 */
export const deleteEcovadisGap = async (gapId: string): Promise<void> => {
  const { error } = await supabase.functions.invoke('delete-ecovadis-gap', {
    body: { gapId }
  });

  if (error) {
    console.error('Error deleting EcoVadis gap:', error);
    throw new Error('Failed to delete gap');
  }
};

/**
 * Uploads a new EcoVadis questionnaire
 */
export const uploadEcovadisQuestionnaire = async (
  projectId: string, 
  file: File
): Promise<void> => {
  try {
    // Create FormData to send file
    const formData = new FormData();
    formData.append('file', file);
    formData.append('projectId', projectId);

    // Use ecovadis-questionnaire-import edge function
    const { error } = await supabase.functions.invoke('ecovadis-questionnaire-import', {
      body: formData
    });

    if (error) {
      console.error('Error uploading questionnaire:', error);
      throw new Error('Failed to upload questionnaire');
    }
  } catch (err) {
    console.error('Error uploading questionnaire:', err);
    throw new Error('Failed to upload questionnaire');
  }
};

/**
 * Updates an existing EcoVadis questionnaire
 */
export const updateEcovadisQuestionnaire = async (
  projectId: string, 
  file: File
): Promise<void> => {
  try {
    // Create FormData to send file
    const formData = new FormData();
    formData.append('file', file);
    formData.append('projectId', projectId);

    // Use update-ecovadis-questionnaire edge function
    const { error } = await supabase.functions.invoke('update-ecovadis-questionnaire', {
      body: formData
    });

    if (error) {
      console.error('Error updating questionnaire:', error);
      throw new Error('Failed to update questionnaire');
    }
  } catch (err) {
    console.error('Error updating questionnaire:', err);
    throw new Error('Failed to upload questionnaire');
  }
};

/**
 * Exports EcoVadis questionnaire as Excel file
 */
export const exportEcovadisQuestionnaire = async (projectId: string): Promise<Blob> => {
  const { data, error } = await supabase.functions.invoke('export-all-gaps', {
    body: { projectId },
  });

  if (error) {
    console.error('Error exporting EcoVadis questionnaire:', error);
    throw new Error('Failed to export questionnaire');
  }
  
  // Decode the base64 data
  const binaryString = atob(data.data);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  
  return new Blob([bytes], { 
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
  });
};

/**
 * Fetches all gaps in the workspace
 */
export const getWorkspaceGaps = async (questionId: string | null, projectId?: string): Promise<WorkspaceGap[]> => {
  const { data, error } = await supabase.functions.invoke('get-projects-gaps', {
    body: { projectId, questionId }
  });

  if (error) {
    console.error('Error fetching projects gaps:', error);
    throw new Error('Failed to fetch projects gaps');
  }

  return data || [];
};

/**
 * Exports workspace gaps as Excel file
 * @param projectId Optional project ID to filter gaps
 * @returns A promise that resolves to a Blob containing the Excel file
 */
export const exportWorkspaceGaps = async (projectId?: string): Promise<Blob> => {
  const { data, error } = await supabase.functions.invoke('export-gaps', {
    body: { projectId },
  });

  if (error) {
    console.error('Error exporting workspace gaps:', error);
    throw new Error('Failed to export gaps');
  }
  
  // Decode the base64 data to binary
  const binaryString = atob(data.data);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  
  return new Blob([bytes], { 
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
  });
};

/**
 * Requests EcoVadis question gap analysis
 * @param projectId - The ID of the project
 * @param projectQuestionId - The ID of the ecovadis_project_question
 * @returns A promise that resolves to an array of gap items
 */
export const requestEcovadisQuestionGapAnalysis = async (projectQuestionId: string,projectId?: string): Promise<GapItem[]> => {
  const { data, error } = await supabase.functions.invoke('ecovadis-ga-request', {
    body: { projectId, projectQuestionId }
  });

  if (error) {
    console.error('Error requesting EcoVadis question gap analysis:', error);
    throw new Error('Failed to request gap analysis');
  }

  return data;
};

/**
 * Requests EcoVadis question AI answer
 * @param projectId - The ID of the project
 * @param questionId - The ID of the question
 * @returns A promise that resolves to the full AI response
 */
export const requestEcovadisQuestionAiAnswering = async (projectId: string, questionId: string): Promise<any> => {
try {
  const data = await axios.post<{ message: string }>(
      `${API_URL}/auth/ecovadis/answer`,
    { projectId, questionId }
    );

    return data;

} catch (error) {
  console.error('Error requesting EcoVadis question AI answer:', error);
    throw new Error('Failed to request AI answer');
}

};

/**
 * Requests EcoVadis question score assessment
 * @param projectId - The ID of the project
 * @param questionId - The ID of the question
 * @returns A promise that resolves to the score assessment response
 */
export const requestEcovadisScoreAssessment = async (projectId: string, projectQuestionId: string): Promise<any> => {
  const { data, error } = await supabase.functions.invoke('ecovadis-score-assessment', {
    body: { projectId, projectQuestionId }
  });

  if (error) {
    console.error('Error requesting EcoVadis score assessment:', error);
    throw new Error('Failed to request score assessment');
  }

  return data;
};

/**
 * Detaches a document from an answer
 * @param answerId The ID of the answer
 * @param documentId The ID of the document to detach
 * @param chunkIds Optional array of document chunk IDs to detach
 * @returns A promise that resolves when the document is detached
 */
export const detachDocumentFromAnswer = async (
  answerId: string,
  documentId: string,
  chunkIds?: string[]
): Promise<void> => {
  const { error } = await supabase.functions.invoke('ecovadis-document-link', {
    body: { 
      operation: 'detach',
      answerId,
      documentId,
      chunkIds
    }
  });

  if (error) {
    console.error('Error detaching document:', error);
    throw new Error('Failed to detach document from answer');
  }
};

/**
 * Updates a document in an answer
 * @param answerId The ID of the answer
 * @param documentId The ID of the document to update
 * @param updates Object containing the updated pages, comment, and existing chunkIds
 * @returns A promise that resolves when the document is updated
 */
export const updateDocumentInAnswer = async (
  answerId: string,
  documentId: string,
  updates: {
    pages: string;
    comment?: string;
  }
): Promise<void> => {
  const { error } = await supabase.functions.invoke('ecovadis-document-link', {
    body: { 
      operation: 'update',
      answerId,
      documentId,
      pages: updates.pages,
      comment: updates.comment || '',
    }
  });

  if (error) {
    console.error('Error updating document:', error);
    throw new Error('Failed to update document in answer');
  }
};
