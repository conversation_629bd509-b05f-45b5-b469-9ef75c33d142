export interface EcovadisProject {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'completed';
  dueDate: string;
  creationDate: string;
  completionDate: string;
  estimatedScore: number;
  actualScore: number;
  questions: EcovadisQuestion[];
}

export interface EcovadisQuestion {
  id: string;
  name: string;
  code: string;
  topic: string;
  status: 'open' | 'in progress' | 'completed' | 'approved';
  estimatedScore: number;
  actualScore: number;
  options: QuestionOption[];
  gaps: number;
}

export interface QuestionOption {
  id: string;
  text: string;
  helpText: string;
  scoreImpact: number;
  evidenceExamples: string[];
  attachedDocuments: AttachedDocument[];
}

export interface AttachedDocument {
  id: string;
  name: string;
  pages: string;
  comment: string;
}

// Define the gap item structure
export interface GapItem {
  id: string;
  title: string;
  description: string;
  recommendation?: string;
  severity?: 'high' | 'medium' | 'low';
  resolved?: boolean;
  isComplete?: boolean;
  documents?: string[];
  relatedDocument?: string;
  topic?: string;
  projectId?: string;
  projectName?: string;
  assignedTo?: string | null;
  assigneeId?: string;
  questionId?: string;
  questionCode?: string;
  status?: string;
  createdAt?: string;
  type?: string;
  gaps?: {
    Title?: string;
    Description?: string;
    'Document Issue'?: boolean;
  };
}
