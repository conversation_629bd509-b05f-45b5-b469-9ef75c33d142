
import React from 'react';
import { Link } from 'react-router-dom';
import { Home } from 'lucide-react';
import { EcovadisQuestion } from '@/types/ecovadis';
import { TopicSection } from './TopicSection';

interface ProjectSidebarProps {
  projectId: string;
  projectName: string;
  questions: EcovadisQuestion[];
  expandedTopics: string[];
  expandedIndicators: string[];
  selectedQuestionId: string;
  progressPercentage: number;
  completedQuestions: number;
  totalQuestions: number;
  onToggleTopic: (topic: string) => void;
  onToggleIndicator: (indicatorKey: string) => void;
  onSelectQuestion: (questionId: string) => void;
}

export const ProjectSidebar: React.FC<ProjectSidebarProps> = ({
  projectId,
  projectName,
  questions,
  expandedTopics,
  expandedIndicators,
  selectedQuestionId,
  progressPercentage,
  completedQuestions,
  totalQuestions,
  onToggleTopic,
  onToggleIndicator,
  onSelectQuestion
}) => {
  // Theme ordering
  const THEME_ORDER = [
    'General',
    'Environmental', 
    'Labor & Human Rights',
    'Ethics',
    'Sustainable Procurement'
  ];

  // Group questions by topic
  const questionsByTopic: Record<string, EcovadisQuestion[]> = {};
  
  questions.forEach(question => {
    if (!questionsByTopic[question.topic]) {
      questionsByTopic[question.topic] = [];
    }
    questionsByTopic[question.topic].push(question);
  });

  // Sort topics by defined order
  const sortedTopics = Object.keys(questionsByTopic).sort((a, b) => {
    const indexA = THEME_ORDER.indexOf(a);
    const indexB = THEME_ORDER.indexOf(b);
    
    // If both themes are in the order array, sort by their index
    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }
    // If only one is in the order array, prioritize it
    if (indexA !== -1) return -1;
    if (indexB !== -1) return 1;
    // If neither is in the order array, sort alphabetically
    return a.localeCompare(b);
  });

  return (
    <div className="w-80 border-r border-gray-200 h-full overflow-auto flex-shrink-0 bg-gray-50">
      <div className="px-4 py-3 border-b border-gray-200">
        <div className="flex items-center text-sm">
          <Link to="/projects" className="text-glacier-darkBlue hover:text-glacier-blue flex items-center">
            <Home size={14} className="mr-1" />
            Projects
          </Link>
          <span className="mx-2 text-gray-400">/</span>
          <span className="font-medium text-glacier-darkBlue truncate">{projectName}</span>
        </div>
      </div>
      
      <div className="px-4 py-3 border-b border-gray-200">
        <div className="mb-2">
          <span className="text-sm font-medium">Questions ({questions.length})</span>
        </div>
        <div className="mb-2">
          <div className="bg-gray-200 h-2 rounded-full overflow-hidden">
            <div 
              className="bg-glacier-darkBlue h-full rounded-full transition-all duration-500"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>
        <div className="flex justify-between text-xs">
          <span className="text-gray-600">{progressPercentage}% complete</span>
          <span className="text-gray-600">{completedQuestions} of {totalQuestions} approved</span>
        </div>
      </div>
      
      <div className="p-3">
        {sortedTopics.map((topic) => (
          <TopicSection 
            key={topic}
            topic={topic}
            questions={questionsByTopic[topic]}
            expandedTopics={expandedTopics}
            expandedIndicators={expandedIndicators}
            selectedQuestion={selectedQuestionId}
            onToggleTopic={onToggleTopic}
            onToggleIndicator={onToggleIndicator}
            onSelectQuestion={onSelectQuestion}
          />
        ))}
      </div>
    </div>
  );
};
