import { <PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryColumn, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { ESRSTopic } from './esrs-topic.entity';
import { ESRSDatapoint } from '../../datapoint/entities/esrs-datapoint.entity';

@Entity('esrs_topic_datapoint')
export class ESRSTopicDatapoint {
  @PrimaryColumn()
  esrsTopicId: number;

  @PrimaryColumn()
  esrsDatapointId: number;

  @ManyToOne(() => ESRSTopic, (topic) => topic.datapointRelations)
  @JoinColumn({ name: 'esrsTopicId' })
  topic: ESRSTopic;

  @ManyToOne(() => ESRSDatapoint, (dr) => dr.topicRelations)
  @JoinColumn({ name: 'esrsDatapointId' })
  datapoint: ESRSDatapoint;
}
