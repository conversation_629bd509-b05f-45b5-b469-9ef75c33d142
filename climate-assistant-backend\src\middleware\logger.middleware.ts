import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class LoggerMiddleware implements NestMiddleware {
  private logger = new Logger(`HTTP`);
  use(req: Request, res: Response, next: NextFunction) {
    this.logger.log(
      JSON.stringify({
        level: 'info',
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode,
        message: 'HTTP request handled',
      }),
    );
    next();
  }
}
