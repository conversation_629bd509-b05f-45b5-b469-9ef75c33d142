// @ts-ignore
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { authValidator } from "../_shared/authValidator.ts";
import { corsHeaders } from "../_shared/cors.ts";

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }
  
  try {
    
    const { user, error, supabaseClient, response } = await authValidator(req);
    if (error || !supabaseClient) {
      return response;
    }
    
    const workspaceId = user?.user_workspace[0].workspaceId;
        
    // Get all projects for this workspace
    const { data: projects, error: projectsError } = await supabaseClient
      .from("project")
      .select("*")
      .eq("workspaceId", workspaceId);
    
    if (projectsError) {
      return new Response(
        JSON.stringify({ error: "Failed to fetch projects", details: projectsError }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
      );
    }
    
    // Process each project to include progress information
    const enrichedProjects = await Promise.all(projects.map(async (project) => {
      let progress = {
        percentage: 0,
        complete: 0,
        incomplete: 0,
        gaps: 0,
        resolvedGaps: 0
      };
      
      if (project.type?.toLowerCase() === "csrd") {
        // Get CSRD project progress from data_request table
        const { data: dataRequests, error: dataRequestsError } = await supabaseClient
          .from("data_request")
          .select("status")
          .eq("projectId", project.id);
        
        if (!dataRequestsError && dataRequests.length > 0) {
          const totalRequests = dataRequests.length;
          const completedRequests = dataRequests.filter(dr => dr.status === "approved_answer").length;
          
          progress = {
            percentage: Math.round((completedRequests / totalRequests) * 100) || 0,
            complete: completedRequests,
            incomplete: totalRequests - completedRequests,
            gaps: 0, // For CSRD, we don't track gaps specifically,
            resolvedGaps: 0
          };
        } else {
          console.log("No data requests found for CSRD project or error:", dataRequestsError);
        }
      } else if (project.type?.toLowerCase() === "ecovadis") {
        // Get EcoVadis project progress from project_ecovadis_question table
        const { data: ecovadisQuestions, error: ecovadisQuestionsError } = await supabaseClient
          .from("project_ecovadis_question")
          .select("status")
          .eq("projectId", project.id);
        
        if (!ecovadisQuestionsError && ecovadisQuestions && ecovadisQuestions.length > 0) {
          const totalQuestions = ecovadisQuestions.length;
          // Case-insensitive status matching
          const completedQuestions = ecovadisQuestions.filter(q => 
            q.status && q.status.toLowerCase() === "complete"
          ).length;
          
          // For gaps, look specifically at status = 'gap'
          const gapQuestions = ecovadisQuestions.filter(q => 
            q.status && q.status.toLowerCase() === "gap"
          ).length;
          
          // Incomplete is all questions that aren't complete or gaps
          const incompleteQuestions = totalQuestions - completedQuestions - gapQuestions;
          
          progress = {
            percentage: totalQuestions > 0 ? Math.round((completedQuestions / totalQuestions) * 100) : 0,
            complete: completedQuestions,
            incomplete: incompleteQuestions,
            gaps: gapQuestions,
            resolvedGaps: 0
          };
          
          console.log(`Project ${project.id} progress:`, progress);
        } else {
          console.log("No questions found for EcoVadis project or error:", ecovadisQuestionsError);
          
          // If we can't find questions data, check if there are any gaps data
          const { data: ecovadisGaps, error: ecovadisGapsError } = await supabaseClient
            .from("project_ecovadis_gaps")
            .select("resolved")
            .eq("projectId", project.id);
            
          if (!ecovadisGapsError && ecovadisGaps && ecovadisGaps.length > 0) {
            const gapsCount = ecovadisGaps.length;
            const resolvedGaps = ecovadisGaps.filter(g => g.resolved).length;
            
            progress = {
              percentage: 0, // No complete questions yet
              complete: 0,
              incomplete: 0,
              gaps: gapsCount,
              resolvedGaps: resolvedGaps
            };
          } else {
            // If no data at all, provide default values
            progress = {
              percentage: 0,
              complete: 0,
              incomplete: 0,
              gaps: 0,
              resolvedGaps: 0
            };
          }
        }
      }
      
      return {
        id: project.id,
        name: project.name,
        type: project.type,
        progress
      };
    }));
    
    return new Response(
      JSON.stringify({ projects: enrichedProjects }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
    
  } catch (error) {
    console.error("Server error:", error);
    return new Response(
      JSON.stringify({ error: "Internal server error", details: error.message }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
    );
  }
});
