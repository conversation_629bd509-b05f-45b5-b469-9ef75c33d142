"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1721055638014 = void 0;
class SchemaUpdate1721055638014 {
    constructor() {
        this.name = 'SchemaUpdate1721055638014';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "chat_history"
          ALTER COLUMN "title" DROP NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "chat_history"
          ALTER COLUMN "title" SET NOT NULL`);
    }
}
exports.SchemaUpdate1721055638014 = SchemaUpdate1721055638014;
//# sourceMappingURL=1721055638014-schema-update.js.map