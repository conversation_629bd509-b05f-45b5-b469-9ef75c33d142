
import { useState } from 'react';
import { Document, DocumentType } from '@/types/ecovadis';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Calendar, Type, Info } from 'lucide-react';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { formatDocumentType } from '@/utils/documentUtils';

interface DocumentPropertiesTabProps {
  document: Document;
  onSave: (document: Document) => void;
}

const documentTypes: DocumentType[] = [
  'certificate',
  'sustainability_report',
  'policy',
  'supplier_code',
  'collective_agreement',
  'reporting_document',
  'other'
];

export function DocumentPropertiesTab({ document, onSave }: DocumentPropertiesTabProps) {
  const [editedDoc, setEditedDoc] = useState<Document>(document);
  
  const handleSave = () => {
    onSave(editedDoc);
    toast.success("Document details updated", {
      description: "Your changes have been saved successfully."
    });
  };
  
  const handleDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setEditedDoc({
      ...editedDoc,
      publishedDate: event.target.value ? new Date(event.target.value) : undefined
    });
  };
  
  const formattedDate = editedDoc.publishedDate 
    ? format(new Date(editedDoc.publishedDate), 'yyyy-MM-dd')
    : '';
  
  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div className="space-y-2">
              <label className="text-sm font-medium text-glacier-darkBlue flex items-center gap-2">
                <Type className="h-4 w-4" />
                Document Type
              </label>
              <Select 
                value={editedDoc.type as string} 
                onValueChange={(value) => setEditedDoc({...editedDoc, type: value as DocumentType})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select document type" />
                </SelectTrigger>
                <SelectContent>
                  {documentTypes.map(type => (
                    <SelectItem key={type} value={type}>
                      {formatDocumentType(type)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-glacier-darkBlue flex items-center gap-2">
                <Info className="h-4 w-4" />
                Description
              </label>
              <Textarea 
                value={editedDoc.description || ''} 
                onChange={(e) => setEditedDoc({...editedDoc, description: e.target.value})}
                placeholder="Enter a short description or table of contents"
                className="min-h-28"
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-glacier-darkBlue flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Publication Date
              </label>
              <Input 
                type="date"
                value={formattedDate}
                onChange={handleDateChange}
              />
            </div>
            
            <Button onClick={handleSave} className="bg-glacier-darkBlue text-white">
              Save Changes
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
