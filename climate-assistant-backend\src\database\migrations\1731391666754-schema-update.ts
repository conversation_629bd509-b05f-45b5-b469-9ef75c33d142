import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1731391666754 implements MigrationInterface {
  name = 'SchemaUpdate1731391666754';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "document_chunk" ALTER COLUMN "page" TYPE character varying USING "page"::text`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "document_chunk" ALTER COLUMN "page" TYPE integer USING "page"::integer`,
    );
  }
}
