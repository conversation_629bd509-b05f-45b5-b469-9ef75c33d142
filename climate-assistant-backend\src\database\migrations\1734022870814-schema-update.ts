import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1734022870814 implements MigrationInterface {
  name = 'SchemaUpdate1734022870814';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "esrs_topic" ADD "description" text`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "esrs_topic" DROP COLUMN "description"`,
    );
  }
}
