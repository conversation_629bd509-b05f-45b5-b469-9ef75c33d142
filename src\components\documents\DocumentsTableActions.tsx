'use client';

import { ChevronDownIcon } from 'lucide-react';
import { useEffect, useState } from 'react';

import { Button } from '../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { DeleteDocumentDialog } from '../DocumentsDeleteConfirm';
import { downloadUploadedDocument } from '@/api/workspace-settings/workspace-settings.api';
import { toast } from '../ui/use-toast';
import { useAuthentication } from '@/api/authentication/authentication.query';
import { USER_ROLE } from '@/constants/workspaceConstants';
import { userHasRequiredRole } from '@/lib/utils';

export function DocumentsTableActions({ id }: { id: string }) {
  const { user } = useAuthentication();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  useEffect(() => {
    if (!isDeleteDialogOpen) {
      setTimeout(() => (document.body.style.pointerEvents = ''), 500);
    }
  }, [isDeleteDialogOpen]);

  const handleDownloadFile = async (id: string) => {
    try {
      toast({
        title: 'Downloading Document...',
      });
      await downloadUploadedDocument(id);
    } catch (error: any) {
      let errorMessage = 'Failed to download file';
      if (error.response) {
        const { data } = error.response;
        if (data instanceof Blob) {
          try {
            const text = await data.text();
            const jsonData = JSON.parse(text);
            errorMessage = jsonData.message;
          } catch (blobError) {
            console.error('Failed to parse error blob:', blobError);
          }
        }
      }

      toast({
        variant: 'destructive',
        title: 'Error',
        description: errorMessage,
      });
    }
  };

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="flex gap-2 items-center justify-between rounded-full py-0 px-5 text-glacier-bluedark border-slate-300"
            size="xs"
          >
            <span>Action</span>
            <ChevronDownIcon className="w-3 h-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[160px]">
          {userHasRequiredRole(
            [
              USER_ROLE.SuperAdmin,
              USER_ROLE.WorkspaceAdmin,
              USER_ROLE.AiContributor,
            ],
            user
          ) && (
            <DropdownMenuItem onClick={() => handleDownloadFile(id)}>
              <span>Download File</span>
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            onClick={() => setIsDeleteDialogOpen(true)}
            className="hover:bg-red-400"
          >
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <DeleteDocumentDialog
        id={id}
        open={isDeleteDialogOpen}
        setOpen={setIsDeleteDialogOpen}
      />
    </div>
  );
}
