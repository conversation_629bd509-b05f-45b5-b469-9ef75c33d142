import { KnowledgeBaseService } from './knowledge-base.service';
export declare class KnowledgeBaseController {
    private readonly knowledgeBaseService;
    constructor(knowledgeBaseService: KnowledgeBaseService);
    saveKnowledgeBaseFile(req: any, file: Express.Multer.File): Promise<{
        message: string;
    }>;
    getKnowledgebaseFileUploads(): Promise<import("./entities/knowledge-base-file-upload.entity").KnowledgeBaseFileUpload[]>;
    deleteKnowledgebaseFile(req: any, id: string): Promise<{
        success: boolean;
    }>;
    getEsrsDatapoints(esrs: string): Promise<import("../datapoint/entities/esrs-datapoint.entity").ESRSDatapoint[]>;
}
