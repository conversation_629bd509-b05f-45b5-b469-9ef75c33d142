import axios from 'axios';

import { API_URL } from '../apiConstants';

import { DatapointRequestStatus } from '@/types';
import { DatapointRequest, generationStatus } from '@/types/project';
import type { GenerateDatapointWithAIFormData } from '@/components/dashboard/AiGenerateDpConfirmModal';

export const updateDatapointRequestContent = async ({
  datapointRequestId,
  content,
}: {
  datapointRequestId: string;
  content: string;
}): Promise<void> => {
  await axios.put(`${API_URL}/datapoint-request/${datapointRequestId}`, {
    content,
  });
};

export const loadMaterialTopics = async ({
  datapointRequestId,
}: {
  datapointRequestId: string;
}) => {
  const store = await axios.get(
    `${API_URL}/datapoint-request/${datapointRequestId}/material-topics`
  );

  return store.data;
};

export const reviewDatapointContentWithAI = async ({
  datapointRequestId,
}: {
  datapointRequestId: string;
}): Promise<void> => {
  await axios.post(
    `${API_URL}/datapoint-request/${datapointRequestId}/review-with-ai`
  );
};

export const updateDatapointRequestStatus = async ({
  datapointRequestId,
  status,
}: {
  datapointRequestId: string;
  status: DatapointRequestStatus;
}): Promise<DatapointRequest> => {
  const store = await axios.put(
    `${API_URL}/datapoint-request/${datapointRequestId}`,
    {
      status,
    }
  );

  return store.data;
};

export const generateDatapointWithAi = async (
  datapointRequestId: string,
  data: GenerateDatapointWithAIFormData
): Promise<void> => {
  await axios.post(
    `${API_URL}/datapoint-request/${datapointRequestId}/generate-with-ai`,
    data
  );
};

export const fetchDatapointRequestCitations = async ({
  datapointRequestId,
  citationId,
}: {
  datapointRequestId: string;
  citationId: string;
}) => {
  const store = await axios.get(
    `${API_URL}/datapoint-request/${datapointRequestId}/citations?citationId=${citationId}`
  );

  return store.data;
};

export const updateDatapointGenerationStatus = async ({
  datapointGenerationId,
  status,
}: {
  datapointGenerationId: string;
  status: generationStatus;
}): Promise<{
  content?: string;
  status: generationStatus;
  evaluator?: { id: string; name: string; email: string };
  evaluatedAt?: string;
}> => {
  const response = await axios.put(
    `${API_URL}/datapoint-request/generation-status/${datapointGenerationId}`,
    { status }
  );
  return response.data;
};

export const updateDatapointRequestCitations = async ({
  datapointRequestId,
  citationId,
  index,
}: {
  datapointRequestId: string;
  citationId: string;
  index: number;
}) => {
  const store = await axios.put(
    `${API_URL}/datapoint-request/${datapointRequestId}/citations`,
    {
      citationId,
      index,
    }
  );

  return store.data;
};

export const fetchDocumentLinksForDp = async (datapointRequestId: string) => {
  const store = await axios.get(
    `${API_URL}/datapoint-request/${datapointRequestId}/document-links`
  );
  return store.data;
};
