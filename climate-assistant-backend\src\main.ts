import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as cookieParser from 'cookie-parser';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

const lovableAppRegex = /^https:\/\/.*\.(lovable\.app|lovableproject\.com)$/;

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.use(cookieParser());
  app.useGlobalPipes(new ValidationPipe());
  app.enableCors({
    credentials: true,
    // origin: process.env.FRONTEND_URL,
    origin: [
      'https://app.glacier.eco',
      'https://prototype.glacier.eco',
      'https://ecovadis.glacier.eco',
      lovableAppRegex,
      'http://localhost:5173',
      'http://localhost:8080',
    ],
  });

  const config = new DocumentBuilder()
    .setTitle('Glacier API')
    .setDescription('Glacier App API standards')
    .setVersion('1.0')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/swagger', app, document);

  await app.listen(3000);
}

bootstrap();
