import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1739172792667 implements MigrationInterface {
  name = 'SchemaUpdate1739172792667';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."data_request_generation_status_enum" AS ENUM('pending', 'approved', 'rejected')`,
    );
    await queryRunner.query(
      `CREATE TABLE "data_request_generation" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "data" json NOT NULL, "evaluatorId" uuid, "evaluatedAt" TIMESTAMP, "createdAt" TIMESTAMP DEFAULT now(), "status" "public"."data_request_generation_status_enum" NOT NULL DEFAULT 'pending', "dataRequestId" uuid, CONSTRAINT "PK_258425f2fa89c149d23b0577011" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_request_generation" ADD CONSTRAINT "FK_bf8f064dcc74c3cd021b27228c2" FOREIGN KEY ("dataRequestId") REFERENCES "data_request"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "data_request_generation" DROP CONSTRAINT "FK_bf8f064dcc74c3cd021b27228c2"`,
    );
    await queryRunner.query(`DROP TABLE "data_request_generation"`);
    await queryRunner.query(
      `DROP TYPE "public"."data_request_generation_status_enum"`,
    );
  }
}
