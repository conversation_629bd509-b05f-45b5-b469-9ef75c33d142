// supabase/functions/ecovadis-ai-answer

// @ts-ignore
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { authValidator } from "../_shared/authValidator.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { formatScoringFrameworkKey } from "../_config/ecovadis/scoring-framework.ts";
import { DifyClient } from "../_shared/difyRequests.ts";

interface ParsedAttachment {
  optionId: string;
  documentId: string;
  pages: string[];
  comment: string;
  chunkIds: string[];
}

const parseAiAnswerResponse = (response: any): ParsedAttachment[] => {
  console.log('Parsing AI answer response:', response);
  
  if (!response?.llmout || !Array.isArray(response.llmout)) {
    console.error('Invalid AI response format - missing llmout array');
    return [];
  }

  const attachments: ParsedAttachment[] = [];

  for (const item of response.llmout) {
    if (!item?.answer_option?.id || !item?.document_chunks || !Array.isArray(item.document_chunks)) {
      console.warn('Skipping invalid AI answer item:', item);
      continue;
    }

    // Group document chunks by documentId
    const documentGroups: { [documentId: string]: { pages: string[], chunkIds: string[] } } = {};

    for (const chunk of item.document_chunks) {
      if (!chunk.document_chunk_id) continue;

      // Find the corresponding content to get documentId and page
      const content = item.contents?.find(c => c.id === chunk.document_chunk_id);
      if (!content) continue;

      if (!documentGroups[content.documentId]) {
        documentGroups[content.documentId] = { pages: [], chunkIds: [] };
      }

      documentGroups[content.documentId].pages.push(content.page);
      documentGroups[content.documentId].chunkIds.push(chunk.document_chunk_id);
    }

    // Create an attachment for each document
    for (const [documentId, data] of Object.entries(documentGroups)) {
      attachments.push({
        optionId: item.answer_option.id,
        documentId,
        pages: [...new Set(data.pages)], // Remove duplicates
        comment: item.comment || '',
        chunkIds: data.chunkIds
      });
    }
  }

  console.log('Parsed attachments:', attachments);
  return attachments;
};

const saveAiAnswerAttachments = async (
  supabaseClient: any,
  projectId: string, 
  attachments: ParsedAttachment[],
  validOptionIds: string[]
) => {
  const results: any[] = [];

  // Process each attachment
  for (const attachment of attachments) {
    const { optionId, documentId, pages, comment, chunkIds } = attachment;

    try {
      // Validate that the optionId belongs to the current project's question
      if (!validOptionIds.includes(optionId)) {
        results.push({ 
          optionId,
          documentId,
          status: 'error', 
          message: 'Option ID does not belong to the current project question'
        });
        continue;
      }

      // Check if answer already exists for this option
      const { data: existingAnswer, error: answerFetchError } = await supabaseClient
        .from('project_ecovadis_answer')
        .select('id')
        .eq('projectId', projectId)
        .eq('optionId', optionId)
        .maybeSingle();

      if (answerFetchError) {
        throw new Error(`Error fetching existing answer: ${answerFetchError.message}`);
      }

      let answerId;

      if (existingAnswer) {
        // Use existing answer
        answerId = existingAnswer.id;
      } else {
        // Create new answer with response="true"
        const { data: newAnswer, error: createAnswerError } = await supabaseClient
          .from('project_ecovadis_answer')
          .insert({
            projectId,
            optionId,
            response: 'true'
          })
          .select('id')
          .single();

        if (createAnswerError) {
          throw new Error(`Error creating new answer: ${createAnswerError.message}`);
        }

        answerId = newAnswer.id;
      }

      // Check for existing links to avoid duplicates
      const { data: existingLinks, error: existingLinksError } = await supabaseClient
        .from('project_ecovadis_linked_document_chunks')
        .select('documentChunkId')
        .eq('answerId', answerId);

      if (existingLinksError) {
        throw new Error(`Error checking existing links: ${existingLinksError.message}`);
      }

      // Filter out document chunks that are already linked
      const existingChunkIds = existingLinks?.map(link => link.documentChunkId) || [];
      const chunksToLink = chunkIds.filter(chunkId => !existingChunkIds.includes(chunkId));

      if (chunksToLink.length > 0) {
        // Create linked document chunks - only set comment on the first chunk
        const linkedChunksData = chunksToLink.map((chunkId, index) => ({
          answerId,
          documentChunkId: chunkId,
          comment: index === 0 ? comment || null : null,
          attachment_source: 'ai'
        }));

        const { data: linkedChunks, error: linkError } = await supabaseClient
          .from('project_ecovadis_linked_document_chunks')
          .insert(linkedChunksData)
          .select();

        if (linkError) {
          throw new Error(`Error linking document chunks: ${linkError.message}`);
        }

        results.push({ 
          optionId,
          documentId,
          status: 'success', 
          answerId, 
          linkedChunksCount: linkedChunks.length,
          newLinksCreated: true,
          commentAdded: !!comment
        });
      } else {
        results.push({ 
          optionId,
          documentId,
          status: 'success', 
          answerId, 
          linkedChunksCount: 0,
          newLinksCreated: false,
          message: 'All document chunks already linked to this answer'
        });
      }
    } catch (error) {
      results.push({ 
        optionId,
        documentId,
        status: 'error', 
        message: error.message 
      });
    }
  }

  return results;
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }

  try {
    // Get the request body
    const { projectId, questionId } = await req.json();
    if (!projectId || !questionId) {
      return new Response(JSON.stringify({
        error: 'Project ID and Question ID are required'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }

    const { user, error, supabaseClient, response: errResponse } = await authValidator(req);

    if (!user || error || !supabaseClient) {
      return errResponse;
    }
    
    // 1. Fetch project question details
    const { data: projectQuestion, error: projectQuestionError } = await supabaseClient
      .from('project_ecovadis_question')
      .select(`
        id,
        status,
        impact,
        ecovadis_question:questionId (
          id,
          questionCode,
          question,
          questionName,
          indicator,
          themeId,
          type
        )
      `)
      .eq('id', questionId)
      .single();

    if (projectQuestionError) {
      console.error('Error fetching project question:', projectQuestionError);
      return new Response(JSON.stringify({
        error: 'Failed to fetch project question details'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }

    if (!projectQuestion) {
      return new Response(JSON.stringify({
        error: 'Question not found for this project'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 404
      });
    }

    // 2. Fetch theme details and sustainability issues
    const { data: theme, error: themeError } = await supabaseClient
      .from('ecovadis_theme')
      .select(`
        id, 
        title, 
        description,
        project_ecovadis_theme!inner (
          id,
          impact,
          issues
        )
      `)
      .eq('id', projectQuestion.ecovadis_question.themeId)
      .eq('project_ecovadis_theme.projectId', projectId)
      .single();

    if (themeError) {
      console.error('Error fetching theme:', themeError);
    }

    // 3. Fetch question options with their answer data - only include options that have answers in project context
    const { data: options, error: optionsError } = await supabaseClient
      .from('ecovadis_answer_option')
      .select(`
        id, 
        issueTitle, 
        instructions,
        project_ecovadis_answer!inner (
          id,
          response
        )
      `)
      .eq('questionId', projectQuestion.ecovadis_question.id)
      .eq('project_ecovadis_answer.projectId', projectId);

    if (optionsError) {
      console.error('Error fetching options and answers:', optionsError);
    }

    // 5. Get scoring framework for this indicator
    const scoringFrameworkKey = formatScoringFrameworkKey({
      theme: theme ? theme.title : '',
      indicator: projectQuestion.ecovadis_question.indicator
    })

    const scoringFrameworkKeyDriver = scoringFrameworkKey + ':scoring-drivers'

    const [
      { data: scoringFrameworkData },
      { data: scoringFrameworkDriverData }
    ] = await Promise.all([
      supabaseClient
        .from('kv_store')
        .select(`value`)
        .eq('key', scoringFrameworkKey)
        .single(),
      supabaseClient
        .from('kv_store')
        .select(`value`)
        .eq('key', scoringFrameworkKeyDriver)
        .single()
    ]);

    const scoringFramework = scoringFrameworkData?.value;
    const scoringFrameworkDrivers = scoringFrameworkDriverData?.value;

    // 6. Format questionnaire answers
    let questionnaireAnswers = "";

    // Add the main question
    questionnaireAnswers += `Question: ${projectQuestion.ecovadis_question.questionName}: ${projectQuestion.ecovadis_question.question}\n\n`;

    // Add each answer option and its response
    if (options) {
      options.forEach((option, index) => {
        questionnaireAnswers += `ANSWER ${index + 1}:\n\n`;
        questionnaireAnswers += `AnswerId: ${option.id}\n\n`;
        questionnaireAnswers += `Answer: ${option.issueTitle}\n\n`;
        questionnaireAnswers += `Support: ${option.instructions || 'No instructions provided'}\n\n`;
      });
    }

    // 7. Prepare theme criteria
    const themeCriteria = theme ?
      `Theme: ${theme.title}\n\n${theme.description}` :
      'Theme information not available';

    // 8. Prepare indicator criteria
    const indicatorCriteria = `Indicator: ${projectQuestion.ecovadis_question.indicator}\n\n` +
      `This indicator is about your company's actions to support your sustainability policies and commitments.\n\n` +
      `The answer options in each question represent best practices for your company's size and industry. Select options that your company has already implemented and provide the documented proof of your actions.`;

    // 9. Prepare the final payload
    const themeSustainabilityIssue: {
      impact: string;
      issueId: string;
    }[] = theme && theme.project_ecovadis_theme.length > 0 ?
    theme.project_ecovadis_theme.map((itheme) => itheme.issues).flat() : [];

    let sustainabilityIssues = ''

    if (themeSustainabilityIssue && themeSustainabilityIssue.length > 0) {
      const { data: sustainabilityIssuesData, error: sustainabilityIssuesError } = await supabaseClient
      .from('ecovadis_sustainability_issues')
      .select('id, issue, definition, industryIssues')
      .in('id', themeSustainabilityIssue.map((issue) => issue.issueId));

      theme.project_ecovadis_theme.forEach((itheme) => {
        itheme.issues.forEach((issue) => {
          const issueData = sustainabilityIssuesData?.find((data) => data.id === issue.issueId);
          if (issueData) {
            sustainabilityIssues += `
              Issue: ${issueData.issue}
              Impact: ${themeSustainabilityIssue.find((i) => i.issueId === issueData.id)?.impact || 'No impact provided'}
              Definition: ${issueData.definition}
              Industry Issues: ${issueData.industryIssues}
              \n`;
          }
        });
      });
    }
    const payload = {
      projectId: projectId,
      questionId: questionId,
      workspaceId: user.user_workspace[0].workspaceId,
      sustainabilityIssues: sustainabilityIssues,
      themeCriteria: themeCriteria,
      indicatorCriteria: indicatorCriteria,
      scoringFramework: (typeof scoringFramework === 'object' && scoringFramework !== null) ?
      JSON.stringify(scoringFramework) :
      (scoringFramework || "Scoring framework not available for this indicator"),
      scoringFrameworkCoreDrivers: (typeof scoringFrameworkDrivers === 'object' && scoringFrameworkDrivers !== null && scoringFrameworkDrivers["core-drivers"]) ?
      JSON.stringify(scoringFrameworkDrivers["core-drivers"]) :
      ("Scoring framework drivers not available for this indicator"),
      scoringFrameworkExtraDrivers: (typeof scoringFrameworkDrivers === 'object' && scoringFrameworkDrivers !== null && scoringFrameworkDrivers["extra-drivers"]) ?
      JSON.stringify(scoringFrameworkDrivers["extra-drivers"]) :
      ("Scoring framework drivers not available for this indicator"),
      answerOption: questionnaireAnswers.trim()
    } as {
      projectId: string;
      questionId: string;
      workspaceId: string;
      sustainabilityIssues: string;
      themeCriteria: string;
      indicatorCriteria: string;
      scoringFramework: string;
      scoringFrameworkCoreDrivers: string;
      scoringFrameworkExtraDrivers: string;
      answerOption: string;
    };

    // console.log('Payload:', payload);
    

    // 10. Make dify request
    const difyClient = new DifyClient({
      apiKey: 'app-aC3EmKELVhHy5oxFhf71fOqd'
    });
    const result = await difyClient.runWorkflow({
      inputs: payload,
      response_mode: 'blocking',
      user: user.id,
    });

    // Parse the AI response and save attachments
    let saveResults: any = null;
    if (result?.data?.outputs) {
      const attachments = parseAiAnswerResponse(result.data.outputs);
      console.log('Parsed attachments:', attachments);
      
      if (attachments.length > 0) {
        try {
          // Get valid option IDs from the fetched options
          const validOptionIds = options?.map(option => option.id) || [];
          saveResults = await saveAiAnswerAttachments(supabaseClient, projectId, attachments, validOptionIds);
          console.log('Attachments saved successfully:', saveResults);
        } catch (saveError) {
          console.error('Error saving attachments:', saveError);
        }
      }
    }

    return new Response(JSON.stringify({ 
      result: result?.data?.outputs || result,
      payload,
      saveResults
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});
