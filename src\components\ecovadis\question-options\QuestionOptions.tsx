import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { QuestionOption, EcovadisQuestion, AttachedDocument } from "@/types/ecovadis";
import { useParams } from "react-router-dom";
import { toast } from "sonner";
import { AutoAnswerButton } from "../auto-answer/AutoAnswerButton";
import { OptionItem } from "./OptionItem";
import { DocumentDialogManager } from "./DocumentDialogManager";
import { Accordion } from "@/components/ui/accordion";
import { DocumentUpload } from "@/types/project";
import { detachDocumentFromAnswer, updateDocumentInAnswer, updateEcovadisQuestionOption } from "@/api/ecovadis/ecovadis.api";
import { useEcovadisProject } from "@/hooks/useEcovadisProject";
import { useUserRole } from '@/hooks/useUserRole';
import { USER_ROLE } from '@/constants/workspaceConstants';
import { TooltipProvider } from '@/components/ui/tooltip';

interface QuestionOptionsProps {
  options: QuestionOption[];
  onUpdateOptions: (options: QuestionOption[]) => void;
  question?: EcovadisQuestion | null;
  onUpdateQuestion?: (updatedQuestion: EcovadisQuestion) => void;
  onToggleOption?: (optionId: string, selected: boolean) => Promise<void>;
  loadingOptionIds?: string[];
  documents?: DocumentUpload[];
}

export const QuestionOptions = ({
  options,
  onUpdateOptions,
  question,
  onUpdateQuestion,
  onToggleOption,
  loadingOptionIds = [],
  documents = []
}: QuestionOptionsProps) => {
  const { id: projectId } = useParams<{ id: string }>();
  const { invalidateProjectData } = useEcovadisProject(projectId);
  const [expandedOptions, setExpandedOptions] = useState<string[]>([]);
  const [isAnswered, setIsAnswered] = useState(false);
  const [detachingDocIds, setDetachingDocIds] = useState<string[]>([]);
  const [updatingDocIds, setUpdatingDocIds] = useState<string[]>([]);
  
  // Dialog states
  const [dialogState, setDialogState] = useState({
    openDialogId: null as string | null,
    uploadDialogId: null as string | null,
    editDocId: null as string | null,
    searchTerm: "",
    filteredDocuments: documents,
    evidenceDialogOpen: false,
    selectedEvidence: null as string | null,
    isDetachDialogOpen: false,
    selectedDetachDoc: null as { optionId: string; docId: string } | null
  });

  // Update filteredDocuments when documents prop changes
  useEffect(() => {
    setDialogState(prev => ({
      ...prev,
      filteredDocuments: documents
    }));
  }, [documents]);

  useEffect(() => {
    const selectedOptions = options.filter(option => option?.selected).map(option => option.id);
    setExpandedOptions(selectedOptions);
    if (selectedOptions.length > 0) {
      setIsAnswered(true);
    }
  }, [options]);

  const handleToggleOption = async (optionId: string) => {
    const option = options.find(o => o.id === optionId);
    if (!option) return;
    
    const newSelected = !option.selected;
    
    if (onToggleOption) {
      await onToggleOption(optionId, newSelected);
    } else {
      const updatedOptions = options.map(option => {
        if (option?.id === optionId) {
          if (newSelected && !expandedOptions.includes(optionId)) {
            setExpandedOptions([...expandedOptions, optionId]);
          }
          
          return {
            ...option,
            selected: newSelected,
            attachedDocuments: option.attachedDocuments || []
          };
        }
        return option;
      });
      
      onUpdateOptions(updatedOptions);
      
      if (newSelected) {
        toast.success("Option selected");
      } else {
        toast.success("Option deselected");
      }
    }
  };

  const handleAddDocument = async () => {
    invalidateProjectData();
  };

  const handleUpdateDocument = async (optionId: string, updatedDoc: AttachedDocument) => {
    const option = options.find(opt => opt.id === optionId);
    
    if (!option || !option.answerId) {
      toast.error("Could not find answer information for this option");
      return;
    }
    
    setUpdatingDocIds(prev => [...prev, updatedDoc.id]);
    
    try {      
      await updateDocumentInAnswer(option.answerId, updatedDoc.id, {
        pages: updatedDoc.pages || "",
        comment: updatedDoc.comment
      });
      
      const updatedOptions = options.map(opt => {
        if (opt.id === optionId) {
          return {
            ...opt,
            attachedDocuments: (opt.attachedDocuments || []).map(doc => 
              doc.id === updatedDoc.id ? { ...doc, ...updatedDoc } : doc
            )
          };
        }
        return opt;
      });
      
      onUpdateOptions(updatedOptions);
      
      invalidateProjectData();
      
      setDialogState(prev => ({
        ...prev,
        openDialogId: null,
        editDocId: null
      }));
      
      toast.success("Document updated successfully");
    } catch (error) {
      console.error('Error updating document:', error);
      toast.error("Failed to update document. Please try again.");
    } finally {
      setUpdatingDocIds(prev => prev.filter(id => id !== updatedDoc.id));
    }
  };

  const handleRemoveDocument = async (optionId: string, docId: string, chunkIds?: string[]) => {
    const option = options.find(opt => opt.id === optionId);
    
    if (!option || !option.answerId) {
      toast.error("Could not find answer information for this option");
      return;
    }
    
    setDetachingDocIds(prev => [...prev, docId]);
    
    try {
      await detachDocumentFromAnswer(option.answerId, docId, chunkIds);
      
      const updatedOptions = options.map(opt => {
        if (opt.id === optionId) {
          return {
            ...opt,
            attachedDocuments: (opt.attachedDocuments || []).filter(doc => doc.id !== docId)
          };
        }
        return opt;
      });
      
      onUpdateOptions(updatedOptions);
      
      invalidateProjectData();
      
      toast.success("Document detached successfully");
    } catch (error) {
      console.error('Error detaching document:', error);
      toast.error("Failed to detach document. Please try again.");
    } finally {
      setDetachingDocIds(prev => prev.filter(id => id !== docId));
    }
  };

  const handleUpdateResponse = async (optionId: string, response: string) => {
    try {
      const option = options.find(opt => opt.id === optionId);
      
      await updateEcovadisQuestionOption(optionId, projectId || '', {
        evidence_examples: response,
        selected: !!response,
      });
      
      const updatedOptions = options.map(opt => {
        if (opt.id === optionId) {
          return {
            ...opt,
            response: response,
            selected: !!response
          };
        }
        return opt;
      });
      
      onUpdateOptions(updatedOptions);
      
      invalidateProjectData();
    } catch (error) {
      console.error('Error updating response:', error);
      throw error;
    }
  };

  const handleAutoAnswerComplete = () => {
    setIsAnswered(true);
    invalidateProjectData();
  };

  const toggleExpand = (optionId: string) => {
    setExpandedOptions(prev => 
      prev.includes(optionId) 
        ? prev.filter(id => id !== optionId) 
        : [...prev, optionId]
    );
  };

  const openDocumentDialog = (optionId: string, docId: string | null = null) => {
    setDialogState(prev => ({
      ...prev,
      openDialogId: optionId,
      editDocId: docId
    }));
  };

  const openUploadDialog = (optionId: string) => {
    setDialogState(prev => ({
      ...prev,
      uploadDialogId: optionId
    }));
  };

  const openEvidenceDialog = (evidence: string) => {
    setDialogState(prev => ({
      ...prev,
      selectedEvidence: evidence,
      evidenceDialogOpen: true
    }));
  };

  const openDetachDialog = (optionId: string, docId: string) => {
    setDialogState(prev => ({
      ...prev,
      isDetachDialogOpen: true,
      selectedDetachDoc: { optionId, docId }
    }));
  };

  const confirmDetachDocument = () => {
    if (dialogState.selectedDetachDoc) {
      handleRemoveDocument(
        dialogState.selectedDetachDoc.optionId, 
        dialogState.selectedDetachDoc.docId
      );
      
      setDialogState(prev => ({
        ...prev,
        isDetachDialogOpen: false,
        selectedDetachDoc: null
      }));
    }
  };

  const handleSearchChange = (term: string) => {
    setDialogState(prev => ({
      ...prev,
      searchTerm: term,
      filteredDocuments: documents.filter(doc => 
        doc.name.toLowerCase().includes(term.toLowerCase())
      )
    }));
  };

  const closeDialogs = () => {
    setDialogState(prev => ({
      ...prev,
      openDialogId: null,
      uploadDialogId: null,
      editDocId: null,
      evidenceDialogOpen: false,
      isDetachDialogOpen: false
    }));
  };

  return (
    <Card className="border-none shadow-none">
      <CardContent className="space-y-6 px-0 pt-6 py-0">
        {question && onUpdateQuestion && (
          <div className="flex justify-start mb-4">
            <AutoAnswerButton 
              question={question} 
              projectId={projectId || ''}
              onComplete={handleAutoAnswerComplete}
              disabled={false}
            />
          </div>
        )}

        <div className="w-full">
          <Accordion type="multiple" value={expandedOptions} className="w-full">
            {options.map(option => {
              if (!option) return null;
              
              return (
                <OptionItem
                  key={option.id}
                  option={option}
                  expanded={expandedOptions.includes(option.id)}
                  onToggleExpand={toggleExpand}
                  onToggleOption={handleToggleOption}
                  onUpdateResponse={handleUpdateResponse}
                  onOpenDocumentDialog={openDocumentDialog}
                  onOpenUploadDialog={openUploadDialog}
                  onOpenEvidenceDialog={openEvidenceDialog}
                  onDetachDocument={openDetachDialog}
                  onRemoveDocument={handleRemoveDocument}
                  isLoading={loadingOptionIds.includes(option.id)}
                  isAnswered={isAnswered}
                  detachingDocIds={detachingDocIds}
                  updatingDocIds={updatingDocIds}
                />
              );
            })}
          </Accordion>
        </div>
      </CardContent>

        <DocumentDialogManager 
          dialogState={dialogState}
          options={options}
          onClose={closeDialogs}
          onAddDocument={handleAddDocument}
          onConfirmDetach={confirmDetachDocument}
          onSearchChange={handleSearchChange}
        />
    </Card>
  );
};
