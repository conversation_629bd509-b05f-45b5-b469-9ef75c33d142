
import React from 'react';
import { Button } from '@/components/ui/button';
import { Upload, Download } from 'lucide-react';
import { format } from 'date-fns';
import { UploadSustainabilityIssuesButton } from '@/components/documents/UploadSustainabilityIssues';
import { useUserRole } from '@/hooks/useUserRole';
import { canUploadSustainabilityIssues } from '@/utils/userWorkspaceUtils';

interface ProjectHeaderProps {
  projectName: string;
  projectId: string;
  deadline?: Date | string;
  onOpenUpdateModal: () => void;
  onOpenExportModal: () => void;
}

export const ProjectHeader: React.FC<ProjectHeaderProps> = ({
  projectName,
  projectId,
  deadline,
  onOpenUpdateModal,
  onOpenExportModal
}) => {
  const { getUserRole } = useUserRole();
  const userRole = getUserRole();
  const canUpload = canUploadSustainabilityIssues(userRole);

  const formattedDeadline = deadline 
    ? typeof deadline === 'string'
      ? deadline
      : format(deadline, 'MMM d, yyyy')
    : 'No deadline set';

  return (
    <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10">
      <div>
        <h1 className="text-xl font-semibold text-glacier-darkBlue">
          {projectName}
        </h1>
        <div className="flex items-center mt-1">
          {/* <span className="text-sm text-gray-600">Deadline: {formattedDeadline}</span> */}
        </div>
      </div>
      
      <div className="flex gap-3">
        {canUpload && (
          <UploadSustainabilityIssuesButton projectId={projectId} refreshData={() => {}} />
        )}
          
        {/* <Button 
          onClick={onOpenUpdateModal} 
          variant="outline" 
          disabled={true}
          className="flex items-center gap-2"
        >
          <Upload className="h-4 w-4" />
          <span>Update Questionnaire</span>
        </Button> */}

        
        <Button 
          onClick={onOpenExportModal} 
          variant="secondary" 
          className="flex items-center gap-2"
        >
          <Download className="h-4 w-4" />
          <span>Export</span>
        </Button>
      </div>
    </div>
  );
};
