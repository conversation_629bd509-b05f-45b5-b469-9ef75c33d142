import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1731407256579 implements MigrationInterface {
  name = 'SchemaUpdate1731407256579';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "material_esrs_topic" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_chunk" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "document_chunk" DROP COLUMN "createdAt"`,
    );
    await queryRunner.query(
      `ALTER TABLE "material_esrs_topic" DROP COLUMN "createdAt"`,
    );
  }
}
