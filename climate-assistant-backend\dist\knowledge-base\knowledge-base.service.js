"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeBaseService = void 0;
const common_1 = require("@nestjs/common");
const fs = require("fs");
const pdf = require("pdf-parse");
const textsplitters_1 = require("@langchain/textsplitters");
const knowledge_base_file_upload_chunk_entity_1 = require("./entities/knowledge-base-file-upload-chunk.entity");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const knowledge_base_file_upload_entity_1 = require("./entities/knowledge-base-file-upload.entity");
const chat_gpt_service_1 = require("../llm/chat-gpt.service");
const esrs_datapoint_entity_1 = require("../datapoint/entities/esrs-datapoint.entity");
const esrs_disclosure_requirement_entity_1 = require("./entities/esrs-disclosure-requirement.entity");
const esrs_topic_entity_1 = require("./entities/esrs-topic.entity");
let KnowledgeBaseService = class KnowledgeBaseService {
    constructor(knowledgeBaseFileUploadRepository, knowledgeBaseFileUploadChunkRepository, esrsDatapointRepository, esrsDisclosureRequirementRepository, esrsTopicRepository, dataSource, chatGptService) {
        this.knowledgeBaseFileUploadRepository = knowledgeBaseFileUploadRepository;
        this.knowledgeBaseFileUploadChunkRepository = knowledgeBaseFileUploadChunkRepository;
        this.esrsDatapointRepository = esrsDatapointRepository;
        this.esrsDisclosureRequirementRepository = esrsDisclosureRequirementRepository;
        this.esrsTopicRepository = esrsTopicRepository;
        this.dataSource = dataSource;
        this.chatGptService = chatGptService;
    }
    async saveFileWithEmbeddings(originalname, path) {
        const fileExists = await this.knowledgeBaseFileUploadRepository.exists({
            where: { name: originalname },
        });
        if (fileExists) {
            fs.unlinkSync(path);
            throw new common_1.BadRequestException('File already uploaded');
        }
        const fileUpload = await this.knowledgeBaseFileUploadRepository.save({
            path,
            name: originalname,
        });
        const dataBuffer = fs.readFileSync(path);
        const pdfData = await pdf(dataBuffer);
        const splitter = new textsplitters_1.RecursiveCharacterTextSplitter({
            chunkSize: 1000,
            chunkOverlap: 200,
        });
        const chunks = await splitter.splitText(pdfData.text);
        const contentEmbeddings = await this.chatGptService.createContentEmbeddingPairs(chunks);
        await this.knowledgeBaseFileUploadChunkRepository.save(contentEmbeddings.map(({ content, embedding }) => ({
            content,
            embedding,
            fileUploadId: fileUpload.id,
        })));
    }
    async getUploadedFiles() {
        return this.knowledgeBaseFileUploadRepository.find();
    }
    async deleteFile(id) {
        const hasPermission = await this.knowledgeBaseFileUploadRepository.exists({
            where: { id },
        });
        if (!hasPermission) {
            throw new common_1.UnauthorizedException('You are not allowed to delete this file');
        }
        const file = await this.knowledgeBaseFileUploadRepository.findOneOrFail({
            where: { id },
        });
        await this.knowledgeBaseFileUploadChunkRepository.delete({
            fileUploadId: id,
        });
        await this.knowledgeBaseFileUploadRepository.delete({ id: id });
        fs.unlinkSync(file.path);
    }
    async getSimilarChunksWithVectorSearch(question, elements, similarityThreshold = 0.8) {
        const embeddingQuestion = await this.chatGptService.createEmbedding(question);
        const res = await this.dataSource.query(`
          SELECT knowledge_base_file_upload_chunk.content,
                 1 - (knowledge_base_file_upload_chunk.embedding <=> $1) AS similarity,
                 knowledge_base_file_upload_chunk."fileUploadId",
                 knowledge_base_file_upload_chunk.id
          FROM knowledge_base_file_upload_chunk
                   JOIN
               knowledge_base_file_upload
               ON knowledge_base_file_upload_chunk."fileUploadId" = knowledge_base_file_upload.id
          WHERE 1 - (knowledge_base_file_upload_chunk.embedding <=> $1) > $2
          ORDER BY knowledge_base_file_upload_chunk.embedding <=> $1
              LIMIT $3;
      `, ['[' + embeddingQuestion + ']', similarityThreshold, elements]);
        return res;
    }
    async getSimilarChunksForMultipleQuestions(questions, chunksPerQuestion) {
        const chunks = (await Promise.all(questions.map(async (question) => {
            return this.getSimilarChunksWithVectorSearch(question, chunksPerQuestion);
        }))).reduce((acc, val) => acc.concat(val), []);
        return chunks.map((chunk) => chunk.content);
    }
    async getEsrsDatapointsByStandard(esrs) {
        const drs = esrs === 'all'
            ? await this.esrsDisclosureRequirementRepository.find()
            : await this.esrsDisclosureRequirementRepository.find({
                where: { esrs: esrs },
            });
        const datapoints = await this.esrsDatapointRepository.find({
            where: {
                esrsDisclosureRequirementId: (0, typeorm_2.In)(drs.map((dr) => dr.id)),
            },
            order: {
                datapointId: 'ASC',
            },
        });
        return datapoints;
    }
    async getEsrsTopics() {
        return this.esrsTopicRepository.find({
            where: { level: 'topic', id: (0, typeorm_2.Not)(11) },
            relations: ['children.children'],
        });
    }
};
exports.KnowledgeBaseService = KnowledgeBaseService;
exports.KnowledgeBaseService = KnowledgeBaseService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(knowledge_base_file_upload_entity_1.KnowledgeBaseFileUpload)),
    __param(1, (0, typeorm_1.InjectRepository)(knowledge_base_file_upload_chunk_entity_1.KnowledgeBaseFileUploadChunk)),
    __param(2, (0, typeorm_1.InjectRepository)(esrs_datapoint_entity_1.ESRSDatapoint)),
    __param(3, (0, typeorm_1.InjectRepository)(esrs_disclosure_requirement_entity_1.ESRSDisclosureRequirement)),
    __param(4, (0, typeorm_1.InjectRepository)(esrs_topic_entity_1.ESRSTopic)),
    __param(5, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource,
        chat_gpt_service_1.ChatGptService])
], KnowledgeBaseService);
//# sourceMappingURL=knowledge-base.service.js.map