
import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

import { PageLayout } from '@/components/MainLayout';
import { Button } from '@/components/ui/button';
import { ProjectHeader } from '@/components/projects/ProjectHeader';
import { ProjectContent } from '@/components/projects/ProjectContent';
import { Project } from '@/types/project';
import { Skeleton } from '@/components/ui/skeleton';

const PROJECTS_STORAGE_KEY = 'glacier-dashboard-projects';

export default function ProjectDetailPage() {
  const { id } = useParams<{ id: string }>();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    if (id) {
      setLoading(true);
      const savedProjects = localStorage.getItem(PROJECTS_STORAGE_KEY);
      if (savedProjects) {
        try {
          const projects = JSON.parse(savedProjects);
          const foundProject = projects.find((p: Project) => p.id === id);
          setProject(foundProject || null);
        } catch (error) {
          console.error('Error parsing saved projects:', error);
        }
      }
      setLoading(false);
    }
  }, [id]);

  const handleProjectUpdate = (updatedProject: Project) => {
    setProject(updatedProject);
    
    const savedProjects = localStorage.getItem(PROJECTS_STORAGE_KEY);
    if (savedProjects) {
      try {
        const projects = JSON.parse(savedProjects);
        const updatedProjects = projects.map((p: Project) => 
          p.id === updatedProject.id ? updatedProject : p
        );
        localStorage.setItem(PROJECTS_STORAGE_KEY, JSON.stringify(updatedProjects));
      } catch (error) {
        console.error('Error updating project:', error);
      }
    }
  };

  if (loading) {
    return (
      <PageLayout>
        <div className="mb-6">
          <Skeleton className="h-10 w-1/3 mb-3" />
          <Skeleton className="h-6 w-2/3" />
        </div>
        <Skeleton className="h-[600px] w-full" />
      </PageLayout>
    );
  }

  if (!project) {
    return (
      <PageLayout>
        <div className="flex flex-col items-center justify-center min-h-[80vh] text-center p-6">
          <h2 className="text-2xl font-semibold mb-4">Project Not Found</h2>
          <p className="text-gray-600 mb-8">The project you are looking for does not exist or has been deleted.</p>
          <Button 
            onClick={() => navigate('/projects')}
            variant="darkBlue"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Projects
          </Button>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <ProjectHeader project={project} />
      <ProjectContent project={project} onProjectUpdate={handleProjectUpdate} />
    </PageLayout>
  );
}
