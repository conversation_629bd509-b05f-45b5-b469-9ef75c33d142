import { useState, useEffect } from "react";
import { AttachedDocument } from "@/types/ecovadis";
import { useForm } from "react-hook-form";
import { 
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter,
  DialogDescription
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { FileText, Search, ExternalLink, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/components/ui/use-toast";
import { useDocuments } from "@/hooks/useDocuments";

interface DocumentDialogProps {
  open: boolean;
  onClose: () => void;
  optionId: string;
  answerId: string;
  editDocument?: AttachedDocument & { optionId?: string };
  existingDocuments: { id: string; name: string; }[];
  searchTerm: string;
  onSearchChange: (term: string) => void;
}

export const DocumentDialog = ({ 
  open, 
  onClose, 
  optionId, 
  answerId,
  editDocument,
  existingDocuments,
  searchTerm,
  onSearchChange
}: DocumentDialogProps) => {
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(null);
  const { toast } = useToast();
  const { updateDocumentAsync, isUpdating } = useDocuments();
  
  const formSchema = z.object({
      pages: z.string().min(1, "Page number is required"),
      comment: z.string().optional()
    });
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      pages: editDocument?.pages || "",
      comment: editDocument?.comment || ""
    }
  });

  type FormValues = z.infer<typeof formSchema>;
  
  
  const isEdit = !!editDocument;
  
  // Reset form when edit document changes
  useEffect(() => {
    if (editDocument) {
      form.reset({
        pages: editDocument.pages || "",
        comment: editDocument.comment || ""
      });
      setSelectedDocumentId(editDocument.id);
    } else {
      form.reset({
        pages: "",
        comment: ""
      });
      setSelectedDocumentId(null);
    }
  }, [editDocument, form]);

  
  // Filter documents based on search term
  const filteredDocuments = existingDocuments.filter(doc => 
    doc.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const onSubmit = async (data: FormValues) => {
    try {
      if (isEdit && editDocument) {
        // Update existing document
        await updateDocumentAsync({
          answerId,
          documentId: editDocument.id,
          updates: {
            pages: data.pages,
            comment: data.comment
          }
        });
        
        toast({
          description: "Document details updated successfully"
        });
      } else if (selectedDocumentId) {
        // Attach new document
        const selectedDoc = filteredDocuments.find(d => d.id === selectedDocumentId);
        if (selectedDoc) {
          await updateDocumentAsync({
            answerId,
            documentId: selectedDoc.id,
            updates: {
              pages: data.pages,
              comment: data.comment
            }
          });
          
          toast({
            description: "Document attached successfully"
          });
        }
      }
      
      form.reset();
      setSelectedDocumentId(null);
      onClose();
    } catch (error) {
      console.error('Error updating document:', error);
      toast({
        variant: "destructive",
        description: "Failed to update document. Please try again."
      });
    }
  };
  
  // Function to simulate opening a document
  const handleOpenDocument = () => {
    toast({
      description: "Opening document..."
    });
    // In a real implementation, this would open the document in a new tab
  };
  
  return (
    <Dialog open={open} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-[80%] max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="truncate">{isEdit ? "Edit Attached Document" : "Attach Document"}</DialogTitle>
          {!isEdit && (
            <DialogDescription className="break-words">
              Attach an existing document from the library.
            </DialogDescription>
          )}
        </DialogHeader>
        
        <div className="flex-1 overflow-y-auto">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {isEdit ? (
                <div className="space-y-2">
                  <FormLabel>Document name</FormLabel>
                  <div 
                    className="flex items-center gap-2 p-2 border rounded-md cursor-pointer hover:bg-gray-50 min-w-0"
                    onClick={handleOpenDocument}
                  >
                    <FileText className="h-5 w-5 flex-shrink-0 text-glacier-darkBlue" />
                    <span className="flex-1 font-medium truncate" title={editDocument?.name}>{editDocument?.name}</span>
                    <ExternalLink className="h-4 w-4 text-gray-400 flex-shrink-0" />
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="text-sm font-medium">Choose existing Document</div>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
                    <Input
                      type="text"
                      placeholder="Search documents..."
                      className="pl-9"
                      value={searchTerm}
                      onChange={(e) => onSearchChange(e.target.value)}
                    />
                  </div>
                  
                  <div className="max-h-[200px] overflow-y-auto border rounded-md">
                    {filteredDocuments.length > 0 ? (
                      <div className="divide-y">
                        {filteredDocuments.map(doc => (
                          <label 
                            key={doc.id}
                            htmlFor={`option-${doc.id}`}
                            className={cn(
                              "flex items-center gap-2 p-3 cursor-pointer hover:bg-gray-50 min-w-0",
                              selectedDocumentId === doc.id && "bg-glacier-mint/10"
                            )}
                          >
                            <Checkbox 
                                id={`option-${doc.id}`} 
                                checked={selectedDocumentId === doc.id} 
                                onCheckedChange={() => setSelectedDocumentId(doc.id)}
                                className="flex-shrink-0"
                              />
                            <FileText className="h-4 w-4 flex-shrink-0 text-glacier-darkBlue" />
                            <span className="text-sm truncate flex-1 min-w-0" title={doc.name}>{doc.name}</span>
                          </label>
                        ))}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-[160px] text-sm text-gray-500 px-4 text-center">
                        No documents found matching your search
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              <FormField
                control={form.control}
                name="pages"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Relevant pages</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. 10-12, 15" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="comment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Comment (optional)</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Add context about how this document provides evidence" 
                        className="min-h-[100px] resize-none"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter className="sm:justify-end flex-shrink-0 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                >
                  Cancel
                </Button>
                <Button 
                  variant="darkBlue"
                  type="submit"
                  disabled={(!isEdit && !selectedDocumentId) || isUpdating}
                >
                  {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {isEdit ? "Update" : "Add"} document
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
};
