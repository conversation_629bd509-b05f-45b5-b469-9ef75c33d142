import {
  Body,
  Controller,
  Get,
  Post,
  Request,
  UseGuards,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { ApiTags } from '@nestjs/swagger';
import { AuthGuard } from 'src/auth/supabase/supabase.auth.guard';
import { Roles } from 'src/auth/roles.decorator';
import { Role } from './entities/user-workspace.entity';

@ApiTags('users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @UseGuards(AuthGuard)
  @Get('/prompt-context')
  async getUserPromptSettings(@Request() req) {
    const userId = req.user.id;

    const context = await this.usersService.getUserPromptContext(userId);

    return {
      context,
    };
  }

  @UseGuards(AuthGuard)
  @Get('/generated-prompt-context')
  async getGeneratedPromptContext(@Request() req) {
    const userId = req.user.id;
    const context = await this.usersService.getGeneratedPromptContext(userId);

    return {
      context,
    };
  }

  @UseGuards(AuthGuard)
  @Post('/user-prompt-settings')
  async savePromptSettings(@Request() req, @Body() body: { context: string }) {
    const userId = req.user.id;
    const { context } = body;

    await this.usersService.saveUserPromptContext(userId, context);
    return { success: true };
  }

  @UseGuards(AuthGuard)
  @Roles(Role.SuperAdmin)
  @Post('/create-workspace')
  async createUserCompanyWorkspace(
    @Body() body: { email: string; password: string; name: string }
  ) {
    const { email, password, name } = body;

    const create = await this.usersService.createUserWithCompanyAndWorkspace({
      email,
      password,
      companyName: name,
    });
    return create;
  }
}
