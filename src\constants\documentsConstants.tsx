// Define the type for each category
interface ESRSCategory {
  id: string;
  label: string;
  color: string;
}

// Define the type for the ESRS_CATEGORIES object
export type ESRSCategories = {
  [key: string]: ESRSCategory;
};

export const ESRS_CATEGORIES = {
  'ESRS 2': {
    id: 'ESRS 2',
    label: 'ESRS 2',
    color: '#48698B',
    value: 'ESRS 2',
  },
  'ESRS E': { id: 'ESRS E', label: 'E', color: '#5FBC99', value: 'ESRS E' },
  'ESRS S': { id: 'ESRS S', label: 'S', color: '#3381C9', value: 'ESRS S' },
  'ESRS G': { id: 'ESRS G', label: 'G', color: '#CA8A04', value: 'ESRS G' },
};

export type ESRSCategoryIds = keyof typeof ESRS_CATEGORIES;
