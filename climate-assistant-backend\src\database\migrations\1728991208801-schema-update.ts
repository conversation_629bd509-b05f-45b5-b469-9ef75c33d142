import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1728991208801 implements MigrationInterface {
  name = 'SchemaUpdate1728991208801';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "esrs_topic_disclosure_requirement" DROP CONSTRAINT "FK_e208d4f71abc19d8c0a5eaaf267"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e208d4f71abc19d8c0a5eaaf26"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_40a3bc24828423a91684a5ce47"`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_topic_disclosure_requirement" ADD CONSTRAINT "FK_e208d4f71abc19d8c0a5eaaf267" FOREIGN KEY ("esrsTopicId") REFERENCES "esrs_topic"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "esrs_topic_disclosure_requirement" DROP CONSTRAINT "FK_e208d4f71abc19d8c0a5eaaf267"`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_40a3bc24828423a91684a5ce47" ON "esrs_topic_disclosure_requirement" ("esrsDisclosureRequirementId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e208d4f71abc19d8c0a5eaaf26" ON "esrs_topic_disclosure_requirement" ("esrsTopicId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_topic_disclosure_requirement" ADD CONSTRAINT "FK_e208d4f71abc19d8c0a5eaaf267" FOREIGN KEY ("esrsTopicId") REFERENCES "esrs_topic"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }
}
