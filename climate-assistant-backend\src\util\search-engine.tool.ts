import { Injectable } from '@nestjs/common';
import { CustomGptTool } from './chat-gpt.models';
import { PerplexityService } from './perplexity.service';

@Injectable()
export class SearchEngineTool {
  constructor(private readonly perplexityService: PerplexityService) {}

  createSearchEngine(): CustomGptTool<{ question: string }, string> {
    return {
      type: 'async-gpt-tool',
      toolDefinition: {
        type: 'function',
        function: {
          name: 'get-up-to-date-infos-via-search-engine',
          description:
            'Sucht im Web nach der Frage und gibt dir aktuelle Infos - perfekt für Fragen zu Förderungen, Anbietern, Competitors oder aktuellen Entwicklungen.',
          parameters: {
            type: 'object',
            properties: {
              question: {
                type: 'string',
                description: 'Die Frage, die beantwortet werden soll',
              },
            },
            require: ['question'],
          },
        },
      },
      execute: (payload) => this.searchWithSearchEngine(payload.question),
    };
  }

  private async searchWithSearchEngine(question: string) {
    // TODO: Fix this
    // passiert hin und wieder, dass nichts übergeben wird
    if (question === undefined) {
      return '';
    }

    console.log(`[Search Engine] Question: ${question}`);
    const answer = await this.perplexityService.createCompletion(
      'llama-3-sonar-small-32k-online',
      [
        {
          role: 'system',
          content: ` 
          Du bist eine Suchmaschine die Kontextinformationen zu der Frage des Users liefert sodass damit eine gute Antwort für den User generiert werden kann.
          
          Konzentriere dich kurz und bündig möglichst viele Fakten bereitzustellen!
          
          Du antwortest immer auf deutsch - auch wenn dir die Frage auf englisch gestellt wird.
       `,
        },
        { role: 'user', content: question },
      ],
    );

    return answer;
  }
}
