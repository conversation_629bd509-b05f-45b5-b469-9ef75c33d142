import { Injectable, Logger } from '@nestjs/common';
import { CohereClient } from 'cohere-ai';

export interface RerankResult {
  index: number;
  relevance_score: number;
  document: {
    text: string;
  };
}

@Injectable()
export class CohereService {
  private readonly logger = new Logger(CohereService.name);
  private readonly cohereClient: CohereClient;

  constructor() {
    this.cohereClient = new CohereClient({
      token: process.env.COHERE_API_KEY,
    });
  }

  /**
   * Rerank search results using Cohere's rerank-3.5 model
   */
  async rerankResults(
    query: string,
    documents: Array<{ content: string; metadata?: any }>,
    topK: number = 10
  ): Promise<Array<{ content: string; relevance_score: number; metadata?: any; original_index: number }>> {
    if (!process.env.COHERE_API_KEY) {
      this.logger.warn('COHERE_API_KEY not set, skipping reranking');
      return documents.slice(0, topK).map((doc, index) => ({
        ...doc,
        relevance_score: 1.0 - (index * 0.1), // Simple fallback scoring
        original_index: index,
      }));
    }

    try {
      const documentTexts = documents.map(doc => doc.content);
      
      this.logger.log(`Reranking ${documents.length} documents with query: "${query}"`);
      
      const response = await this.cohereClient.rerank({
        model: 'rerank-v3.5',
        query: query,
        documents: documentTexts,
        topN: Math.min(topK, documents.length),
        returnDocuments: false, // We already have the documents
      });

      // Map reranked results back to original documents with metadata
      const rerankedResults = response.results.map(result => ({
        content: documents[result.index].content,
        relevance_score: result.relevanceScore,
        metadata: documents[result.index].metadata,
        original_index: result.index,
      }));

      this.logger.log(`Reranking completed, returned ${rerankedResults.length} results`);
      
      return rerankedResults;
    } catch (error) {
      this.logger.error('Error during reranking:', error);
      
      // Fallback to original order with decreasing scores
      return documents.slice(0, topK).map((doc, index) => ({
        ...doc,
        relevance_score: 1.0 - (index * 0.1),
        original_index: index,
      }));
    }
  }
} 