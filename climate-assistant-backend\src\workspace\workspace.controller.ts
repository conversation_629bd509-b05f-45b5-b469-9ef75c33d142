import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Request,
  UseGuards,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { WorkspaceService } from './workspace.service';
import { AuthGuard } from 'src/auth/supabase/supabase.auth.guard';
import { Role } from 'src/users/entities/user-workspace.entity';
import { Roles } from 'src/auth/roles.decorator';

@ApiTags('workspace')
@UseGuards(AuthGuard)
@Controller('workspace')
export class WorkspaceController {
  constructor(private readonly workspaceService: WorkspaceService) {}

  @Get('/')
  async getWorkspaceDetails(@Request() req) {
    const workspaceId = req.user.workspaceId;
    return await this.workspaceService.findById(workspaceId);
  }

  @Get('/users')
  async getUsersByWorkspace(@Request() req) {
    const workspaceId = req.user.workspaceId;
    return await this.workspaceService.getUsersByWorkspace(workspaceId);
  }

  @Roles(Role.SuperAdmin)
  @Get('/all-workspaces')
  async getAllWorkspaces() {
    return await this.workspaceService.getAllWorkspaces();
  }

  @Roles(Role.SuperAdmin, Role.AiContributor, Role.WorkspaceAdmin)
  @Post('/inviteUsers')
  async addUserToWorkspace(
    @Request() req,
    @Body() body: { emails: string[]; role: Role }
  ): Promise<{ success?: string; failure?: string }> {
    const workspaceId = req.user.workspaceId;
    const { emails, role } = body;
    const canInviteUser = await this.workspaceService.canInviteUser(
      role,
      req.user
    );
    if (!canInviteUser) {
      throw new Error('You are not allowed to invite users for this role');
    }
    const origin = req.headers.origin;
    const failedEmails: string[] = [];
    const inviteAllUsersPromise = emails.map(
      (email) =>
        new Promise(async (resolve, reject) => {
          try {
            const user = await this.workspaceService.inviteUserToWorkspace({
              inviteeEmail: req.user.email,
              origin,
              workspaceId,
              email,
              role,
              shouldSendEmail: origin.endsWith('.glacier.eco'), //Should send email
            });
            resolve(user);
          } catch (e) {
            failedEmails.push(email);
            reject(e);
          }
        })
    );
    await Promise.allSettled(inviteAllUsersPromise);
    if (failedEmails.length === emails.length) {
      return {
        failure: `Failed to invite ${failedEmails.length > 1 ? 'all' : ''} user${failedEmails.length > 1 ? 's' : ''}: ${failedEmails.join(' , ')}`,
      };
    }
    if (failedEmails.length > 0) {
      const invitedUsers = emails.length - failedEmails.length;
      return {
        success: `${invitedUsers} user${invitedUsers > 1 ? 's' : ''} invited successfully`,
        failure: `Failed to invite few users: ${failedEmails.join(' , ')} `,
      };
    }
    return {
      success: `${emails.length > 1 ? 'All' : ''} ${emails.length > 1 ? 'u' : 'U'}ser${emails.length > 1 ? 's' : ''} invited successfully`,
    };
  }

  @Get('/:workspaceId')
  async getWorkspaceById(@Param('workspaceId') workspaceId: string) {
    const workspace = await this.workspaceService.findById(workspaceId);
    return workspace;
  }

  @Roles(Role.AiContributor, Role.SuperAdmin)
  @Put('/')
  async updateWorkspaceById(@Request() req, @Body() body: any) {
    const workspaceId = req.user.workspaceId;
    const updatedWorkspace = await this.workspaceService.updateById(
      workspaceId,
      body
    );

    return updatedWorkspace;
  }

  @Roles(Role.AiContributor, Role.SuperAdmin)
  @Put('/company')
  async updateCompanyDetail(@Request() req, @Body() body: any) {
    const workspaceId = req.user.workspaceId;
    //TODO: replace this with company service
    const updatedWorkspace = await this.workspaceService.updateCompanyDetail(
      workspaceId,
      body
    );

    return updatedWorkspace;
  }
}
