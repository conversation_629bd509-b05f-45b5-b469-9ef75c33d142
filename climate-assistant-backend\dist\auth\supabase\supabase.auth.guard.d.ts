import { CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { SupabaseService } from './supabase.service';
export declare class AuthGuard implements CanActivate {
    private reflector;
    private supabaseService;
    constructor(reflector: Reflector, supabaseService: SupabaseService);
    canActivate(context: ExecutionContext): Promise<boolean>;
    private extractToken;
}
