"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataRequestController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const data_request_service_1 = require("./data-request.service");
const data_request_dto_1 = require("./entities/data-request.dto");
const common_2 = require("@nestjs/common");
const data_request_guard_1 = require("./data-request.guard");
const data_request_entity_1 = require("./entities/data-request.entity");
const roles_decorator_1 = require("../auth/roles.decorator");
const user_workspace_entity_1 = require("../users/entities/user-workspace.entity");
const project_guard_1 = require("../project/project.guard");
const rxjs_1 = require("rxjs");
const sse_util_1 = require("../util/sse.util");
const supabase_auth_guard_1 = require("../auth/supabase/supabase.auth.guard");
let DataRequestController = class DataRequestController {
    constructor(dataRequestService) {
        this.dataRequestService = dataRequestService;
    }
    async listAllDataRequests(projectId) {
        await this.dataRequestService.findAll(projectId);
    }
    async getDataRequest(req, dataRequestId) {
        return await this.dataRequestService.findRelatedData(dataRequestId, req.user.id);
    }
    async updateDataRequest(req, dataRequestId, updateDataRequestPayload) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        const project = await this.dataRequestService.update({
            dataRequestId,
            updateDataRequestPayload,
            userId,
            workspaceId,
        });
        return project;
    }
    async approveDataRequest(req, dataRequestId) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        const updateDataRequestPayload = {
            approvedBy: userId,
            approvedAt: new Date(),
            status: data_request_entity_1.DataRequestStatus.ApprovedAnswer,
        };
        const project = await this.dataRequestService.update({
            dataRequestId,
            updateDataRequestPayload,
            userId,
            workspaceId,
            event: 'data_request_approved',
        });
        return project;
    }
    async reviewContentWithAi(dataRequestId, req) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        return await this.dataRequestService.reviewDataRequestContentWithAI({
            dataRequestId,
            userId,
            workspaceId,
        });
    }
    async generateContentWithAi(dataRequestId, data, req) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        return await this.dataRequestService.generateDataRequestTextContentWithAI({
            dataRequestId,
            userId,
            workspaceId,
            additionalData: data,
        });
    }
    async generateBulkDatapointForDataRequest(dataRequestId, req) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        return await this.dataRequestService.generateAllDatapointForDataRequest({
            dataRequestId,
            userId,
            workspaceId,
        });
    }
    async getGenerations(dataRequestId) {
        return await this.dataRequestService.getGenerations(dataRequestId);
    }
    async reviewBulkDatapointForDataRequest(dataRequestId, req) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        return await this.dataRequestService.reviewAllDatapointForDataRequest({
            dataRequestId,
            userId,
            workspaceId,
        });
    }
    subscribeToDataRequestEvents(dataRequestId, response) {
        const eventStream = this.dataRequestService.events$.pipe((0, rxjs_1.filter)((event) => event.dataRequestId === dataRequestId), (0, rxjs_1.switchMap)(async (event) => {
            if (event.status === 'failed') {
                await this.dataRequestService.setDatapointQueueStatusToNull(event.datapointRequestId);
            }
            const datapoint = await this.dataRequestService.findDatapointById(event.datapointRequestId);
            event.datapointRequest = datapoint;
            return {
                data: JSON.stringify(event),
                type: 'message',
            };
        }));
        return (0, sse_util_1.createSseStream)(response, eventStream, {
            data: { connected: true },
            type: 'connection',
        });
    }
    async updateDatapointGenerationStatus(dataRequestGenerationId, req, payload) {
        const generation = await this.dataRequestService.updateGenerationStatus({
            dataRequestGenerationId,
            status: payload.status,
            userId: req.user.id,
            workspaceId: req.user.workspaceId,
        });
        if (generation) {
            await this.dataRequestService.update({
                dataRequestId: generation.dataRequest.id,
                updateDataRequestPayload: {
                    content: generation.data.content,
                },
                userId: req.user.id,
                workspaceId: req.user.workspaceId,
            });
        }
        return {
            status: payload.status,
            content: generation?.data?.content || null,
        };
    }
};
exports.DataRequestController = DataRequestController;
__decorate([
    (0, common_2.UseGuards)(project_guard_1.ProjectGuard),
    (0, common_1.Get)('list/:projectId'),
    (0, swagger_1.ApiOperation)({ summary: 'List all data requests for a project' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Data requests retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DataRequestController.prototype, "listAllDataRequests", null);
__decorate([
    (0, common_2.UseGuards)(data_request_guard_1.DataRequestGuard),
    (0, common_1.Get)('/:dataRequestId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific data request by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Data request retrieved successfully',
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('dataRequestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], DataRequestController.prototype, "getDataRequest", null);
__decorate([
    (0, common_2.UseGuards)(data_request_guard_1.DataRequestGuard),
    (0, common_1.Put)('/:dataRequestId'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a specific data request by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Data request updated successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('dataRequestId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, data_request_dto_1.UpdateDataRequestPayload]),
    __metadata("design:returntype", Promise)
], DataRequestController.prototype, "updateDataRequest", null);
__decorate([
    (0, common_2.UseGuards)(data_request_guard_1.DataRequestGuard),
    (0, common_1.Put)('/:dataRequestId/approve'),
    (0, swagger_1.ApiOperation)({ summary: 'Approve a specific data request by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Data request approved successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('dataRequestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], DataRequestController.prototype, "approveDataRequest", null);
__decorate([
    (0, common_2.UseGuards)(data_request_guard_1.DataRequestGuard),
    (0, common_1.Post)('/:dataRequestId/review-with-ai'),
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin, user_workspace_entity_1.Role.WorkspaceAdmin, user_workspace_entity_1.Role.AiContributor, user_workspace_entity_1.Role.AiReviewer, user_workspace_entity_1.Role.Contributor),
    (0, swagger_1.ApiOperation)({ summary: 'Review data request content with AI' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Data request content reviewed successfully',
    }),
    __param(0, (0, common_1.Param)('dataRequestId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DataRequestController.prototype, "reviewContentWithAi", null);
__decorate([
    (0, common_2.UseGuards)(data_request_guard_1.DataRequestGuard),
    (0, common_1.SetMetadata)('customCheck', 'generateWithAI'),
    (0, common_1.Post)('/:dataRequestId/generate-with-ai'),
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin, user_workspace_entity_1.Role.WorkspaceAdmin, user_workspace_entity_1.Role.AiContributor, user_workspace_entity_1.Role.Contributor),
    (0, swagger_1.ApiOperation)({ summary: 'Generate data request content with AI' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Data request content generated successfully',
    }),
    __param(0, (0, common_1.Param)('dataRequestId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, data_request_dto_1.GenerateDataRequestReportTextTextPayload, Object]),
    __metadata("design:returntype", Promise)
], DataRequestController.prototype, "generateContentWithAi", null);
__decorate([
    (0, common_2.UseGuards)(data_request_guard_1.DataRequestGuard),
    (0, common_1.Put)('/:dataRequestId/generate-bulk-datapoint'),
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin, user_workspace_entity_1.Role.WorkspaceAdmin, user_workspace_entity_1.Role.AiContributor),
    (0, swagger_1.ApiOperation)({ summary: 'Generate datapoint for data request' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint generated successfully',
    }),
    __param(0, (0, common_1.Param)('dataRequestId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DataRequestController.prototype, "generateBulkDatapointForDataRequest", null);
__decorate([
    (0, common_1.Get)('/:dataRequestId/generations'),
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin),
    (0, swagger_1.ApiOperation)({ summary: 'Get all generations for a data request' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Generations retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('dataRequestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DataRequestController.prototype, "getGenerations", null);
__decorate([
    (0, common_2.UseGuards)(data_request_guard_1.DataRequestGuard),
    (0, common_1.Put)('/:dataRequestId/review-bulk-datapoint'),
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin, user_workspace_entity_1.Role.WorkspaceAdmin, user_workspace_entity_1.Role.AiContributor),
    (0, swagger_1.ApiOperation)({ summary: 'Review datapoint for data request' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint reviewed successfully',
    }),
    __param(0, (0, common_1.Param)('dataRequestId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DataRequestController.prototype, "reviewBulkDatapointForDataRequest", null);
__decorate([
    (0, common_1.Sse)('/events/datapoint/:dataRequestId'),
    __param(0, (0, common_1.Param)('dataRequestId')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", rxjs_1.Observable)
], DataRequestController.prototype, "subscribeToDataRequestEvents", null);
__decorate([
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin),
    (0, common_1.Put)('/generation-status/:dataRequestGenerationId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update generation status by generation ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Generation status updated successfully',
    }),
    __param(0, (0, common_1.Param)('dataRequestGenerationId')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], DataRequestController.prototype, "updateDatapointGenerationStatus", null);
exports.DataRequestController = DataRequestController = __decorate([
    (0, swagger_1.ApiTags)('Data Request'),
    (0, common_2.UseGuards)(supabase_auth_guard_1.AuthGuard),
    (0, common_1.Controller)('data-request'),
    __metadata("design:paramtypes", [data_request_service_1.DataRequestService])
], DataRequestController);
//# sourceMappingURL=data-request.controller.js.map