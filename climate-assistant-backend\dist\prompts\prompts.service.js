"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PromptService = void 0;
const common_1 = require("@nestjs/common");
const TurndownService = require("turndown");
const turndownPluginGfm = require("joplin-turndown-plugin-gfm");
const datapoint_request_entity_1 = require("../datapoint/entities/datapoint-request.entity");
const constants_1 = require("../constants");
const llm_response_util_1 = require("../util/llm-response-util");
let PromptService = class PromptService {
    constructor() {
        this.GENERATION_LANGUAGE = 'German';
        this.turndownService = new TurndownService({
            headingStyle: 'atx',
            bulletListMarker: '*',
        });
        const tables = turndownPluginGfm.tables;
        const strikethrough = turndownPluginGfm.strikethrough;
        this.turndownService.use([tables, strikethrough]);
    }
    getMDRInstructions(datapointId) {
        const id = datapointId.split('.')[1];
        const instructions = {
            elementType: '',
            elementIdentification: '',
            specificRequirements: '',
            specialNotes: '',
            fallbackHandling: '',
            responseFormat: '',
        };
        switch (id) {
            case 'MDR-P_01-06':
                instructions.elementType = 'policy';
                instructions.elementIdentification =
                    'Each policy is represented by a <h2> heading in the HTML content.';
                instructions.specificRequirements = `
          The undertaking shall disclose information about policies adopted to manage material sustainability matters:
          1. **Key Content Description**: A description of the key contents of the policy, including its general objectives and the material impacts, risks, or opportunities the policy addresses, and the process for monitoring.
          2. **Scope Description**: A description of the policy's scope or exclusions, covering activities, value chain (upstream/downstream), geographies, and affected stakeholder groups.
          3. **Senior Accountability**: The most senior level in the organization accountable for implementing the policy.
          4. **Reference to Standards**: A reference, if relevant, to the third-party standards or initiatives the undertaking commits to respecting through the implementation of the policy.
          5. **Stakeholder Consideration**: A description of the consideration given to key stakeholders' interests in setting the policy.
          6. **Policy Accessibility**: Whether and how the undertaking makes the policy available to potentially affected stakeholders and those helping to implement it.
        `;
                instructions.specialNotes =
                    'Focus your gap analysis on each individual policy identified in the content.';
                instructions.fallbackHandling =
                    'If no policy is available and no <h2> tags are present, check if the text "No policy available" is written. If not, include an appropriate message indicating the absence of policies.';
                break;
            case 'MDR-A_01-12':
                instructions.elementType = 'action';
                instructions.elementIdentification =
                    'Each action is represented by a <h2> heading in the HTML content.';
                instructions.specificRequirements = `
          The undertaking shall disclose the key actions taken and/or planned for material sustainability matters:
          1. **List of Key Actions**: The list of key actions taken in the reporting year and planned for the future, their expected outcomes, and how their implementation contributes to policy objectives and targets.
          2. **Scope of Actions**: The scope of the key actions (i.e., coverage in terms of activities, upstream and/or downstream value chain, geographies, and affected stakeholder groups).
          3. **Time Horizons**: The time horizons under which the undertaking intends to complete each key action.
          4. **Remedial Actions**: If applicable, key actions taken to provide for and cooperate in or support the provision of remedy for those harmed by actual material impacts.
          5. **Progress Information**: If applicable, quantitative and qualitative information regarding the progress of actions or action plans disclosed in prior periods.
          
          For action plans requiring significant operational expenditures (Opex) and/or capital expenditures (Capex), additional disclosures about financial resources are required.
        `;
                instructions.specialNotes =
                    'Focus your gap analysis on each individual action identified in the content.';
                instructions.fallbackHandling =
                    'If no action is available and no <h2> tags are present, check if the text "No action available" is written. If not, include an appropriate message indicating the absence of actions.';
                break;
            case 'MDR-T_01-13':
                instructions.elementType = 'target';
                instructions.elementIdentification =
                    'Each target is represented by a <h2> heading in the HTML content.';
                instructions.specificRequirements = `
          The undertaking shall disclose the measurable, outcome-oriented, and time-bound targets on material sustainability matters:
          1. **Relationship to Objectives**: Description of the relationship of the target to the objectives.
          2. **Target Level**: The defined target level to be achieved, including whether it is absolute or relative and its measurement unit.
          3. **Scope**: The scope of the target, including activities, value chain, geographical boundaries.
          4. **Baseline**: The baseline value and base year from which progress is measured.
          5. **Time Period**: The period to which the target applies, including milestones or interim targets.
          6. **Methodologies**: The methodologies and significant assumptions used to define targets.
          7. **Scientific Evidence**: Whether the targets are based on conclusive scientific evidence.
          8. **Stakeholder Involvement**: Whether and how stakeholders have been involved in target setting.
          9. **Target Changes**: Any changes in targets and corresponding metrics or methodologies.
          10. **Performance**: The performance against disclosed targets, including monitoring and review information.
        `;
                instructions.specialNotes =
                    'Focus your gap analysis on each individual target identified in the content.';
                instructions.fallbackHandling =
                    'If no target is available and no <h2> tags are present, check if the text "No target available" is written. If not, include an appropriate message indicating the absence of targets.';
                break;
            case 'MDR-P_07-09':
                instructions.elementType = 'policy';
                instructions.elementIdentification =
                    'Each policy (or lack thereof) is represented by a <h2> heading in the HTML content.';
                instructions.specificRequirements = `
          If the undertaking has not adopted policies concerning the specific sustainability matter:
          1. It shall disclose:
             - The fact that it has not adopted such policies
             - The reasons for not adopting the policies
          2. The undertaking may disclose a timeframe within which it aims to adopt such policies.
        `;
                instructions.specialNotes =
                    'This MDR specifically addresses cases where the company has not adopted policies. Focus your gap analysis on whether the company properly discloses the absence of policies and provides reasons.';
                instructions.fallbackHandling =
                    'If no <h2> headings are present and "No policy available" is not stated, note that the absence of policies should be addressed in your gap analysis.';
                break;
            case 'MDR-A_13-15':
                instructions.elementType = 'action';
                instructions.elementIdentification =
                    'Each action (or lack thereof) is represented by a <h2> heading in the HTML content.';
                instructions.specificRequirements = `
          If the undertaking has not adopted actions concerning the specific sustainability matter:
          1. It shall disclose:
             - The fact that it has not adopted such actions
             - The reasons for not adopting the actions
          2. The undertaking may disclose a timeframe within which it aims to adopt such actions.
        `;
                instructions.specialNotes =
                    'This MDR specifically addresses cases where the company has not adopted actions. Focus your gap analysis on whether the company properly discloses the absence of actions and provides reasons.';
                instructions.fallbackHandling =
                    'If no <h2> headings are present and "No action available" is not stated, note that the absence of actions should be addressed in your gap analysis.';
                break;
            case 'MDR-T_14-19':
                instructions.elementType = 'target';
                instructions.elementIdentification =
                    'Each target (or lack thereof) is represented by a <h2> heading in the HTML content.';
                instructions.specificRequirements = `
          If the undertaking has not set any measurable outcome-oriented targets:
          1. It may disclose:
             - Whether it plans to set such targets in the future and the timeframe for doing so, or
             - The reasons why it does not intend to set such targets.
          2. It shall disclose whether it nevertheless tracks the effectiveness of its policies and actions on material sustainability-related impacts, risks, and opportunities, and if so:
             - Any processes used to track effectiveness
             - The level of ambition to be achieved and any qualitative or quantitative indicators used to measure progress, including the baseline period.
        `;
                instructions.specialNotes =
                    'This MDR specifically addresses cases where the company has not set targets. Focus your gap analysis on whether the company properly discloses the absence of targets, reasons, and alternative tracking methods.';
                instructions.fallbackHandling =
                    'If no <h2> headings are present and "No target available" is not stated, note that the absence of targets should be addressed in your gap analysis.';
                break;
        }
        return instructions;
    }
    generateDisclosureRequirementClassificationPrompt1() {
        return `
    You are a sustainability expert with 20 years of experience in sustainability reporting. 
    You are good in reading existing content from documents and classifying specific chunks of content as relevant for using it as a base information for a CSRD reports datapoint generation. 
    Underneath is a summary of different ESRS Disclosure Requirements. 
    Analyze the provided chunk of content and determine which ESRS Disclosure Requirements it might this content might be relevant for as a source for report writing.
    If the content potentially relates to one or more Disclosure Requirements, return their identifiers. 
    If the content does not relate to any of the Disclosure Requirements, return an empty array.
    --------------
    Here is the text chunk to classify:`;
    }
    generateDisclosureRequirementClassificationPrompt2(esrsDisclosureRequirements) {
        let disclosureRequirementContenxt = ``;
        for (const esrsDisclosureRequirement of esrsDisclosureRequirements) {
            disclosureRequirementContenxt += `
      
      ${esrsDisclosureRequirement.dr}: ${esrsDisclosureRequirement.name}`;
        }
        return `
    -------------- (End of text chunk)
  Here are the disclosure requirements the company has to report upon:
  ${disclosureRequirementContenxt}
  -------------- (End of disclosure requirements)
  Task: Analyze the content of the text chunk before the disclosure requirements and determine for which of the Disclosure Requirements the content of the chunk might be relevant. It will then be handed to the selected disclosure requirements for further processing. If you are very uncertain, err minimally towards rather adding an extra disclosure requirement, in order to avoid missing important links. Output the result as a JSON array of the relevant categories. 
  
  Example Output:
  {
    "disclosureRequirementMatches": [ "E1-1", "E1-2", "E1-5", "E1.GOV3", "E1.SBM-3", "E1.IRO-1"]
  }
  
  If the page does not match any categories:
  {
    "disclosureRequirementMatches": []
  }`;
    }
    generateDatapointClassificationPrompt1() {
        return `
  You are a sustainability expert with 20 years of experience in sustainability reporting. 
  You are good in reading existing content from documents and classifying specific chunks of content as relevant for using it as a base information for a CSRD reports datapoint generation. 
  Underneath is a summary of different ESRS Datapoints. 
  Analyze the provided chunk of content and determine which ESRS Datapoint this content might be relevant for as a source for report writing.
  
  If the content relates to one or more Datapoints, return their identifiers. 
  
  If the content does not relate to any of the Disclosure Requirements, return an empty array.
  Chunk & Datapoints to report upon: --------------`;
    }
    generateDatapointClassificationPrompt2(disclosureRequirement) {
        return ` 
  -------------- (End of text chunk & Datapoints) 
  Task:
  Analyze the content and identify any key text that pertains to the datapoint of ${disclosureRequirement.dr}. 
  Return a JSON array of Datapoints for which the chunk contains relevant information. If you are very uncertain, err minimally towards rather adding an extra datapoint, in order to avoid missing important links. Special case if 2 conditions are met: 1) The chunk is relevant for a Datapoint that contains "MDR-A", "MDR-P", "MDR-T" in its name, i.e. is a minimal disclosure requirement (MDR) about actions (A), policies (P) or targets (T). 2) [The chunk names a target and the data point is MDR-T] (DO NOT do it for MDR-P or MDR-A because its only for the target T) OR [it contains an action AND the datapoint is MDR-A] (ONLY MDR-A DO NOT do this for MDR-T or MDR-P here) OR [the chunk contains a policy P AND the datapoint is MDR-P] (ONLY MDR-P DO NOT do this for MDR-T or MDR-A here). So in other words strictly do not mix them. If both conditions are met, add to the json another key "mdrTitle" with the *name* of the respective policy/action/target from the chunk as value formatted as HTML <h6>Name of the Policy/Action/Target</h6>. If there's not a clear name given, choose a reasonable one. STRICTLY NEVER use the term "Target" in such a description of for an MDR-P datapoint etc, because only *policies* belong there, not targets. NEVER mention the word of the other MDRs in the others' description.

  If there are no matching criteria, return an empty array.
  
  Example Outputs:
  {
    "matchedDatapoints": [
      {
        "matchedId": "${disclosureRequirement.esrs}-1_01",
      },
      {
        "matchedId": "${disclosureRequirement.esrs}-2_02",
      },
      {
        "matchedId": "${disclosureRequirement.esrs}-3_14",
      },
      {
        "matchedId": "${disclosureRequirement.esrs}.MDR-T_01-13",
        "mdrTitle": "<h2>Target on CO2 emission of supply chain</h2>"
      },
      {
        "matchedId": "${disclosureRequirement.esrs}.IRO-1_01",
      },
    ]
  }
    
  NOT-Example:
  {
    "matchedId": "${disclosureRequirement.esrs}.MDR-P_01-06",
    "mdrTitle": "<h2>Target on CO2 emission of supply chain</h2>"
  } --> This is wrong, because it is an MDR-P and the chunk mentions a target not a policy, so it should not be added.

  Note:
  - There may be multiple matching texts, so the output should be an array of objects.
  - It is possible that same text may align with multiple Datapoints, you should include it in the output for each relevant Datapoint.
  - Make sure to extract any text that aligns with the Datapoint for ${disclosureRequirement.dr}. 
  - DO NOT ALTER THE TEXT
  - If no text matches the criteria for a specific Datapoint, do not include it in the output. If none is please skip the Datapoint.
  `;
    }
    generateSystemPromptForAllDatapoints(esrsDatapoints) {
        return esrsDatapoints
            .map((esrsDatapoint) => {
            return `
          ${esrsDatapoint.datapointId}: ${esrsDatapoint.name}
        `;
        })
            .join('');
    }
    generateDatapointGapAnalysisSystemPrompt1({ esrsDatapoint, generationLanguage, }) {
        return `**Task:**

    You are tasked with performing a structured gap analysis based on the **exact requirements for ${esrsDatapoint.datapointId}** for a company’s sustainability report, focusing on the given datapoint "${esrsDatapoint.datapointId} - ${esrsDatapoint.name}". The goal is to evaluate the company's compliance with the requirements of ${esrsDatapoint.datapointId} and identify any missing or insufficiently covered information. The information of the company is stated in the <Context>. The gaps should then be presented in a systematic way, along with clear recommendations for improvement and a responsible party to address each gap.
    
    **Procedure**:
    
    1. **Context Review**: Review the company's existing <context> related to the requirements of ${esrsDatapoint.datapointId} - "${esrsDatapoint.name}". Ensure that all information is evaluated against each specific requirement from **${esrsDatapoint.datapointId}.**
    2. **Structured Analysis**: For each **${esrsDatapoint.datapointId}**, analyze the company's current disclosures and structure the findings under the following headings:
        1. **Gap**: Clearly identify any missing or insufficiently covered information that prevents the company from fully meeting the requirements.
        2. **Recommendation for Action Steps**: Provide specific, actionable steps to address the gap and ensure compliance with the ${esrsDatapoint.datapointId}. Make sure to be as concrete as possible.
        3. **Sample Text**: Include an example of the information that could be added in order to fill the gap. However, you MUST highlight those pieces of information that are not yet available so compliance gaps regarding the requirements are transparent.
    3. **Presentation of Gaps**: Ensure that all gaps are documented in a way that allows the company to prioritize and address them effectively.
    
    **Expected Outcome**:
    
    1. A **structured gap analysis** of the company’s compliance with **${esrsDatapoint.datapointId}**, presented in a consistent format for each paragraph of the standard.
    2. A **comprehensive list of gaps**, with specific recommendations for improvements.
    3. **A sample text** that shall provide the reader with guidance on the expected outcome and required information. This sample text shall be compliant with the requirements and highlight not-yet-available information.`;
    }
    generateDatapointGapAnalysisDatapointSpecificSystemPrompt(esrsDatapoint, otherDatapoints) {
        const otherDatapointsNames = otherDatapoints
            .map((datapoint) => datapoint.name)
            .join(', ');
        return `
    Here are the exact requirements for "${esrsDatapoint.datapointId} – ${esrsDatapoint.name}" based on the paragraphs from the official ESRS and the respective IDs matched to the exact paragraph:

    **Requirements of the Standard - extracted from [ESRS](https://xbrl.efrag.org/e-esrs/esrs-set1-2023.html#7144)**

    Requirements: ${esrsDatapoint.lawText}
    Footnotes: ${esrsDatapoint.footnotes}

    Application Requirements: ${esrsDatapoint.lawTextAR}
    Footnotes for Application Requirements: ${esrsDatapoint.footnotesAR}
    
    Note: Do not claim there is a gap if the information is expected not in this datapoint but in a related one.
    Here's the other Datapoints from the same Disclosure Requirement:
    <other Datapoints>
    ${otherDatapointsNames}
    </other Datapoints>
    `;
    }
    generateDatapointGapAnalysisContentContext(content) {
        content = content.replace(llm_response_util_1.CITATION_CLIENT_REGEX, '$3');
        return `Here is the current Datapoint content as context:
    <Context> ${this.turndownService.turndown(content)} </Context>`;
    }
    generateDatapointGapAnalysisSystemPrompt2({ esrsDatapoint, generationLanguage, reportTextGenerationRules, generalCompanyProfile, reportingYear, }) {
        const currentYear = new Date().getFullYear();
        return `
    **Here is an Example Output with Gaps identified:**
    {
      "gapIdentified": true,
      "gap": "<p>The disclosure about training programs lacks details on the nature, scope, and depth of these programs, that are required by G1-3_06.</p>",
      "actions": [
          "<p>Provide more details about the training programs, including content, duration, and delivery methods.</p>",
          "<p>Highlight specialized training for various roles or departments.</p>",
      ],
      "exampleText": "<p>“The company’s anti-corruption and anti-bribery training programs cover a wide range of topics, including risk assessment and reporting procedures. These programs are delivered through e-learning modules and in-person workshops and are tailored to different roles within the company.”</p>",
      "disclaimer": "<p>Detailed descriptions of the training programs are currently unavailable and need to be provided for compliance.</p>"
    }
    

    **Example Output with no Gaps identified:**
    {
      "gapIdentified": false,
      "text": "Upon reviewing the provided information, no gap has been identified. "Company" has adequately disclosed its transition plan for climate change mitigations. No further action is required for this specific datapoint."
    }
    DO NOT add any longer exaplations here, if there is no gap. This example is an upper limit of how much to write, if gapIdentified is false.

    **Extremely Bad Examples Output claiming unjustified Gaps identified:**
    # 1
    application requirements: "Sectoral pathways have not yet been defined by the public policies for all sectors. Hence, the disclosure under paragraph 16 (a) on the compatibility of the transition plan with the objective of limiting global warming to 1.5°C should be understood as the disclosure of the undertaking’s GHG emissions reduction target. The disclosure under paragraph 16 (a) shall be benchmarked in relation to a pathway to 1.5°C. This benchmark should be based on either a sectoral decarbonisation pathway if available for the undertaking’s sector or an economy-wide scenario bearing in mind its limitations (i.e., it is a simple translation of emission reduction objectives from the state to undertaking level). This AR should be read also in conjunction with AR 26 and AR 27 and the sectoral decarbonisation pathways they refer to."

    {
      "gapIdentified": true,
      "gap": "<p>The current disclosure confirms that XXX' targets are science-based and validated by the SBTi; however, it does not explicitly explain how these targets are benchmarked against a specific 1.5°C scenario (for example, by referencing a sectoral decarbonisation pathway or an economy-wide scenario). This additional benchmark detail is needed to fully demonstrate the targets’ compatibility with limiting global warming to 1.5°C.

</p>",
      "actions": [...],
      "exampleText": "<p>...</p>"
    }
    --> application requirement says "shall be benchmarked", not "must be benchmarked" i.e. it is optional and thus cannot be a gap. Moreover it contains "if available". This is a minor discrepancy not a real gap like data from the wrong year or missing mandatory requirements that are material for the company.

    # 2
    LawText: The undertaking shall disclose: whether and how these policies are implemented through specific procedures to ensure discrimination is prevented, mitigated and acted upon once detected, as well as to advance diversity and inclusion in general.
    {
      "gapIdentified": true,
      "gap": "<p>Die Offenlegung beschreibt zwar die Verfahren zur Meldung und Reaktion bei Diskriminierungsfällen sowie diverse Initiativen zur Förderung von Diversität und Inklusion, geht aber nicht ausreichend darauf ein, wie die Wirksamkeit dieser Verfahren systematisch überwacht und regelmäßig verbessert wird. Konkrete Angaben zu Evaluationsmaßnahmen, festgelegten Leistungsindikatoren oder internen Audits fehlen.</p>",
      "actions": [...],
      "exampleText": "<p>...</p>"
    }
    --> again massively exaggerated. The requirement is about "whether and how" and not about "how systematically" and "regularly" and "concrete evaluations" and "performance indicators" and "internal audits". This is again much too minor discrepancy not a real gap like data from the wrong year or missing mandatory requirements that are material for the company.
    --> again massively exaggerated. The requirement is about "whether and how" and not about "how systematically" and "regularly" and "concrete evaluations" and "performance indicators" and "internal audits". This is again much too minor for it to be considered a gap.

    We are currently in the year ${currentYear} and ${reportingYear ? ' regardless of what the reporing rules below specify or what the reference document mention, we are strictly reporting for the year ' + reportingYear : ' figure out the reporting year from the reporting rules specified below or from the other references'}. Consider this when doing your analysis, since data from 2023 are outdated and do not fulfill the requirements in some cases.

    ${!!reportTextGenerationRules && `**Company Specific report text generation rules**: ${reportTextGenerationRules}`}

    ${!!generalCompanyProfile && `**General Company Profile**: ${generalCompanyProfile} \n\n This is the general company profile. Use this information to inform your generation of the data point. In particular focus on company structure and which year is being reported for.`}
    
    **Notes**:
    
    1. All actual **"${esrsDatapoint.datapointId} - ${esrsDatapoint.name}"** requirements must remain unchanged and be addressed directly. 
      If the phrasing contains the word "shall", it is mandatory and must be strictly checked for compliance. 
      Non-compliance with "shall" requirements is considered a gap. Requirements without either "may" or "shall" should be interpreted based on their context, but only flag gaps if the standard explicitly requires it.
    2. The analysis must focus on identifying gaps and providing actionable recommendations for the company to fully meet the **${esrsDatapoint.datapointId}**.
    3. Each gap must be linked to the relevant department or team responsible for addressing it, or if possible, concrete company documents.
    4. The output structure should be in JSON format and strings can be basic html elements. DO NOT ESCAPE THE HTML AS A STRING. JUST GIVE OUT THE PURE HTML. Do not add any css classes and just give the output as h1, h2, h3, p.
    5. If the output contains Numbers, Year-Numbers, Monetary Numbers, Percentages or similar numeric Data, pack them inside a <span> tag with the following styling: style="color: #eab308"
    6. Phrase it in short, concise, user-friendly way. Avoid jargon or theoretical, formal language. Don't use the literal lawtext language. The gap should be intuitive to understand for person who reads about ESG the first time & does not know what to write about here.
    7. Only identify gaps that are gaps actually missing in the datapoint "${esrsDatapoint.datapointId} - ${esrsDatapoint.name}" and not in the other Datapoints of the same Disclosure Requirement mentioned above.
    8. Beware of the exact requirements that are actually applicable to the company. If the legal requirements require a geographical breakdown if applicable, but the company is not operating in any country other than austria, this is not an applicable gap.
    9. Generally be lenient. Only name gaps where there are actual gaps in a strict sense, and not where the company could have written more, but it is not strictly required by the standard. Avoid overdoing it. In doubt, compare against the examples of what is not a gap.
    ${reportingYear ? `10. The current reporting year is ${reportingYear}.` : ''}

    **Strict Rule for "May" Requirements:**
    - Non-compliance or absence of information related to "may" requirements does **not** constitute a gap.
    - Do not generate any gaps for "may" requirements unless explicitly instructed to do so.
    - Treat all "may" requirements as optional and exclude them from gap analysis by default.
    
    **Language**:
    All the outcomes that you generate are written in ${constants_1.LANGUAGE_MAP[generationLanguage]} language.
    `;
    }
    generateDatapointGapAnalysisSystemO3Prompt({ esrsDatapoint, materialTopics, nonMaterialTopics, otherDatapoints, reportTextGenerationRules, generalCompanyProfile, content, conditional, reportingYear, generationLanguage, }) {
        const materialTopicHierarchyLines = this.formatTopics({
            topics: materialTopics,
            level: 0,
            material: true,
        });
        const nonMaterialTopicHierarchyLines = this.formatTopics({
            topics: nonMaterialTopics,
            level: 0,
            material: false,
        });
        const currentYear = new Date().getFullYear();
        const otherDatapointsNames = otherDatapoints
            .map((datapoint) => datapoint.name)
            .join(', ');
        const mainTopics = materialTopics.map((t) => t.name).join(', ');
        const mdrInstructions = this.getMDRInstructions(esrsDatapoint.datapointId);
        const isMDR = esrsDatapoint.datapointId.includes('MDR');
        const h2Count = (content.match(/<\/h2>/g) || []).length;
        return `**Goal:**
    Perform a structured gap analysis for the datapoint **${esrsDatapoint.datapointId} – ${esrsDatapoint.name}**, identifying any missing or insufficiently covered information in the company’s disclosures. Return a json that contains as first key whether there is a gap or not and if so, provide details and recommendations for closing these gaps and include sample text to guide compliance, ensuring that any information not yet available is clearly highlighted. Write the output in ${constants_1.LANGUAGE_MAP[generationLanguage]}. Be rather lenient though, if the requirements are mostly met, do not claim a gap for minor discrepancies. If there are clear gaps, keep the explanation short and do not go into too much detail.

    **Return a json with exactly this structure & these keys**:
    {
      "gapIdentified": true,
      "gap": "<p>The disclosure about training programs lacks details on the nature, scope, and depth of these programs, that are required by G1-3_06.</p>",
      "actions": [
          "<p>Provide more details about the training programs, including content, duration, and delivery methods.</p>",
          "<p>Highlight specialized training for various roles or departments.</p>",
      ],
      "exampleText": "<p>“The company’s anti-corruption and anti-bribery training programs cover a wide range of topics, including risk assessment and reporting procedures. These programs are delivered through e-learning modules and in-person workshops and are tailored to different roles within the company.”</p>",
      "disclaimer": "<p>Detailed descriptions of the training programs are currently unavailable and need to be provided for compliance.</p>"
    }


**Example with No Gap**:
{
  "gapIdentified": false,
  "text": "After reviewing the disclosures, no gap was found. The company has fully met the disclosure requirements for this datapoint."
}

We are currently in the year ${currentYear} and ${reportingYear
            ? ' regardless of what the reporing rules below specify or what the reference document mention, we are strictly reporting for the year ' +
                reportingYear
            : ' figure out the reporting year from the reporting rules specified below or from the other references'}. Consider this when doing your gap analysis, as e.g. reported data for an old year might be outdated. In such a case it might be a gap, that there is no recent data.

---

**Return Format:**  
1. **Return a json with a Structured Gap Analysis**  
   - Organize findings for each requirement of **${esrsDatapoint.datapointId}** with the following fields:  
     1. **gapIdentified**: Boolean – indicates whether a gap exists (true or false).  
     2. **gap** *(if gapIdentified is true)*: A clear explanation of the missing or insufficiently covered information.  
     3. **actions** *(if gapIdentified is true)*: A list of specific, actionable steps to fill or resolve the identified gap.  
     4. **exampleText** *(if gapIdentified is true)*: A suggested disclosure snippet that meets the requirement, explicitly highlighting any data that is currently unavailable.  
     5. **text** *(if gapIdentified is false)*: A concise confirmation of no gaps and no further action required.  

2. **Recommendation for Action Steps**:  
   - Provide concrete next steps for addressing each gap, referencing which department or individual (role) should be responsible.  

3. **Sample Text**:  
   - Offer an illustrative example for each gap that demonstrates how the required information should appear in the final disclosure.  

4. **Presentation**:  
   - Document any gaps in a way that allows the company to prioritize tasks, clearly distinguishing between required and optional information.  

---

**Warnings:**  
1. **Avoid Incorrect Gap Claims**:  
   - Do **not** flag a gap if the requirement is optional (e.g., “shall be benchmarked if available”).  
   - Do **not** expand scope beyond “whether and how” if the standard doesn’t demand further details.  
2. **Year of Reporting**:  
   - We are currently reporting for **2024** (in 2025) unless specified otherwise above. Avoid referencing 2023 data unless explicitly allowed.  
3. **Accuracy and Relevance**:  
   - Only highlight material topics as per the user’s hierarchy. Non-material topics should be disregarded.  
   - Ensure that any recommended sample text is aligned with the standard’s mandatory disclosure elements.  

---

**Context Dump:**  
Below is all relevant information and constraints to shape your final response:

1. **Objective**:  
   - Provide a structured gap analysis for **${esrsDatapoint.datapointId} – ${esrsDatapoint.name}** by examining the company’s disclosed <Context> information against the exact ESRS requirements.
   ${conditional && 'This is a conditional datapoint. If there is information missing, it is up to the user whether they are ok with a gap, or not. The conditions typically have "if relevant" or similar conditionals in the law-text or application-requirements. Explicitly mention in this gap analysis whether the condition is met or not met.'}

2. **Company Context**:  
   - <Context> ${this.turndownService.turndown(content)} </Context>  

3. **Material Topic(s)**:  
   - **${mainTopics}**  
   - Hierarchical breakdown of topics (material) to consider:  
     
     ${materialTopicHierarchyLines.join('\n')}
       
4. **Non-Material Topics**:  
   - Do **not** include these in the gap analysis:  
     
     ${nonMaterialTopicHierarchyLines.join('\n')}
       

5. **ESRS Reference**:  
   - **${esrsDatapoint.lawText}**  
   - Footnotes: **${esrsDatapoint.footnotes}**  
   - **${esrsDatapoint.lawTextAR}** (Application Requirements)  
   - Footnotes: **${esrsDatapoint.footnotesAR}**  
   - Other Datapoints in the Same Disclosure Requirement:  
     
  Here's the other Datapoints from the same Disclosure Requirement:
  <other Datapoints>
    ${otherDatapointsNames}
  </other Datapoints>

6. **Reporting Guidance**:  
   - “Do not claim a gap” if the requirement or data is intended for another datapoint.  
   - Use caution with optional or conditional language in the standard (e.g., “if available”).  
   - **2025** is the current year; primary data coverage is **2024**.  

7. ${reportingYear ? `The current reporting year is ${reportingYear}.` : ''}


8. **Company-Specific Notes** *(if any)*:  
   ${!!reportTextGenerationRules && `**Company Specific report text generation rules**: ${reportTextGenerationRules}`}  
   ${!!generalCompanyProfile && `**General Company Profile**: ${generalCompanyProfile}\n\nThis profile provides background on company structure and the reporting year. Use it to ensure the analysis is accurate and complete.`}
    All the outcomes that you generate are written in ${constants_1.LANGUAGE_MAP[generationLanguage]} language.

    ${isMDR && h2Count > 2
            ? `
9. **MDR-Specific Instructions**:
   - **Element Type**: ${mdrInstructions.elementType}
   - **Element Identification**: ${mdrInstructions.elementIdentification}
   - **Specific Requirements**:
     ${mdrInstructions.specificRequirements}
   - **Special Notes**: ${mdrInstructions.specialNotes}
   - **Fallback Handling**: ${mdrInstructions.fallbackHandling}
`
            : ''}

  Use all the above details to analyse the content and identify gaps, following the styling precisely in the described json style with the given keys [gapIdentified, gap, actions, exampleText, disclaimer]. Be rather lenient in judgement on whether or not there is a gap, avoid getting stuck on minor details. If the requirements are roughly met, that is fine and not a gap to be reported. Otherwise, do not go into too much detail.`;
    }
    generateDatapointContentGenerationSystemPrompt1(esrsDatapoint) {
        return `**Role:**

    You are a language model that acts like a senior sustainability consultant with more than 20 years of experience in social topics for the industry the company is operating. Your task is to write a comprehensive ESG report to fulfill all the disclosure requirements for the report. Each disclosure requirement needs to include several datapoints. For this reason you first generate texts that contain all information about each datapoint. You extract the relevant information for doing so from the <Context> about the company, which contains many chunks of relevant context from a variety of documents. Those texts will be used in further processing.

    **Task**:
    Your task is to write a text for each **Datapoint** that contains all relevant information according to the legal requirements, potentially important considerations as specified company in the corresponding use case. The results should be formulated in such a way that they can be directly integrated into the company’s sustainability report. For the given Datapoint all relevant requirements must be considered.

    **Procedure:**

    1. **Context Review:** Use the information provided in the the company’s documents to extract the necessary data and meet the requirements of the specific <Datapoint>.
    2. **Integration of Relevant Information**: Ensure that found data from the documents is written accordingly to given requirements and include relevant key figures and datapoints.
    3. **Citing Sources**: Cite the source of information from the document chunk id. If you find multiple sources, prioritize the most recent one. If this is not available, prioritize the document type → Business Report → Sustainability Report → Materiality Analysis → Policy → Strategy → Other. As a third ordering take "Remarks" of the user into account, if provided. Keep each value as a separate citation with different citation index. Do not include multiple values in one citation as comma-separated values.
    citation: {
      [cited-index]: [
        {
          "id": "<Document Chunk ID>", // documentChunk ID
          "value": "[source]", //fixed string
          "active": true, // higest priority
        }
      ]
    }`;
    }
    generateDatapointContentGenerationExample() {
        return `
    -------------- (End of Text snippet context)
    Example text with masked facts: Learn from this one-shot example json with masked facts what the output can look like:
      {
        "datapoint": "<h2>EX-X_XX Approval of XXX by Administrative, Management, and Supervisory Bodies</h2>
    <p>XXX’s transition plan aligns with the company’s sustainability objectives and has been approved and supported by its highest governing bodies. Sustainability is firmly embedded in the corporate strategy and is reinforced through an amendment to Article 6 of the Articles of Association, ensuring that the Executive Board incorporates sustainability aspects into strategic development and implementation. This commitment is further strengthened within the "Strategy 2030," which prioritizes the net-zero goal and the sustainability roadmap as central objectives of the corporate strategy, fully supported by the company’s leadership.</p>
    <p>The continuous monitoring of the transition plan's implementation is ensured through regular reporting to the management board and supervisory board, guaranteeing that transition-related measures are consistently reviewed at the highest levels of governance.</p>"",
        "citation": {
          "dpcite-1": [
              {
                "id": "bd44f396-5d1b-45aa-a60a-e796a9bfe1f3", // documentChunk ID
                "value": "[source]", //fixed string
                "active": true, // higest priority
              },
            ],
          "dpcite-2": [
              {
                "id": "a1b3f547-77ac-4c59-89a0-32455bda52b0",
                "value": "[source]",
                "active": true,
              },
            ]
        }
      }
      --------------
      (End of Example)
      Make sure to wrap text for citing text-"example" inside double quotes "" (do not use single quotes).
    -------------- (End of Example text)`;
    }
    generateDatapointContentGenerationSpecificDPSystemPrompt(esrsDatapoint, otherDatapoints) {
        const otherDatapointsNames = otherDatapoints
            .map((datapoint) => datapoint.name)
            .join(', ');
        let prompt = `**Datapoint to report upon: ${esrsDatapoint.datapointId} - ${esrsDatapoint.name}**

    Here are the exact requirements for **${esrsDatapoint.datapointId}** based on the paragraphs from the official ESRS Document and the respective IDs matched to the exact paragraph. Your Task is to create ${esrsDatapoint.datapointId}.
    
    **Requirements of the Standard - extracted from [ESRS ${esrsDatapoint.esrsDisclosureRequirement.dr}](https://xbrl.efrag.org/e-esrs/esrs-set1-2023.html)**:
    `;
        prompt += this.createFullEsrsDatapointLawTextContext(esrsDatapoint);
        prompt += `\n\n Here is a list of related Datapoints from the same Datapoint-Category ('Disclosure Requirement'). Those are not part of this Datapoint and thus information that belongs to them should not marked as missing if it is not in this datapoint: <other Datapoints> [${otherDatapointsNames}] </other Datapoints>`;
        return prompt;
    }
    createFullEsrsDatapointLawTextContext(esrsDatapoint) {
        let esrsDatapointContenxt = `DP ${esrsDatapoint.datapointId}: ${esrsDatapoint.name}
    Requirements: ${esrsDatapoint.lawText}`;
        if (esrsDatapoint.footnotes) {
            esrsDatapointContenxt += `
      Footnotes: ${esrsDatapoint.footnotes}`;
        }
        if (esrsDatapoint.lawTextAR) {
            esrsDatapointContenxt += `
      Application Requirements: ${esrsDatapoint.lawTextAR}`;
        }
        if (esrsDatapoint.footnotesAR) {
            esrsDatapointContenxt += `
      Footnotes: ${esrsDatapoint.footnotesAR}`;
        }
        return esrsDatapointContenxt;
    }
    generateDatapointContentGenerationSystemPrompt2({ esrsDatapoint, generationLanguage, reportTextGenerationRules, generalCompanyProfile, customUserRemark, }) {
        const cleanCustomUserRemark = customUserRemark
            .replace(/<p>/g, '')
            .replace(/<\/p>/g, '')
            .trim();
        return `
    **Task**:
    Your task is to write a text for each **Datapoint** that contains all relevant information according to the legal requirements, potentially important considerations and  specified company in the corresponding use case. The results should be formulated in such a way that they can be directly integrated into the company’s sustainability report. For the given Datapoint *all* relevant requirements must be considered, be comprehensive there, without adding too much that belongs to adjacent datapoints though. Return a json with these the key 'datapoint'. Utilise headings and subheadings (e.g. h3 for paragraphs)
    **Procedure:**

    1. **Context Review:** Use the information provided in the the company’s documents to extract the necessary data and meet the requirements of the specific <Datapoint>.
    2. **Integration of Relevant Information**: Ensure that found data from the documents is written accordingly to given requirements and include relevant key figures and datapoints.
    3. Cite the information sources. Include information like title of the Document, Page Number and Paragraph Number in this format (Sustainability Report, P. 12, §4). If this is not available cite the first words or sentence of the respective source.

    **Generation Rules**
    ${reportTextGenerationRules !== '' ? `**Content Generation Rules**:\n${reportTextGenerationRules}` : ''}

    Notes:
    1. Ensure that the writing is as complete as possible based on the given information.
    2. Ensure that the output is complete and coherent, meeting all the requirements of the relevant <Datapoint> within the standard.
    3. Avoid identifying gaps or missing information at this stage; focus solely on disclosing the requirements.
    4. DO NOT WRITE ABOUT ANYTHING THAT IS NOT SPECIFICALLY MENTIONED IN <Context>.
    5. If information are not available in the <Context> but are required from the standards, leave it out and DO NOT create Information that are not given.
    6. ONLY fulfill the described requirements and no other requirements, when writing the text.
    7. When fulfilling a requirement, include the ID and Pagraph ( specified before and summarize what it is about in the headline.
    8. The output structure should be in html. DO NOT ESCAPE THE HTML AS A STRING. 
      JUST GIVE OUT THE PURE HTML. Do not add any css classes and just give the output as h1, h2, h3, p
    9. Cite sources. For each piece of information that is taken from a chunk, cite the chunk. [dpcite-<index>|color-#eab308|text-"[source]"]. text-"[source]" is a fixed value and it should be inside double quotes "" (do not use single quotes) and it should only say source inside square braces. Response JSON needs to contain a citation object with the structure as shown in the example. You shall provide a citation only if <Context></Context> contains any sources to cite.
    10. ${!!generalCompanyProfile && `**General Company Profile**: ${generalCompanyProfile}\n\nThis is the general company profile. Use this information to inform your generation of the data point. In particular focus on company structure and which year is being reported for.`}

    **Language**:
    All the outcomes that you generate are written in ${constants_1.LANGUAGE_MAP[generationLanguage]} language.

    ${cleanCustomUserRemark !== '' && cleanCustomUserRemark.length > 2 ? `**Additional User Remark (High Priority - CONSIDER THIS WHEN GENERATING THE DATAPOINT)**:\n${cleanCustomUserRemark}` : ''}
    `;
    }
    generateDataRequestGapAnalysisSystemPrompt({ esrsDisclosureRequirement, generationLanguage, }) {
        return `
    You are tasked with performing a structured gap analysis based on the **exact requirements for ${esrsDisclosureRequirement.dr}** for a company’s sustainability report, focusing on the given <Disclosure Requirement>. The goal is to evaluate the company's compliance with the requirements of ${esrsDisclosureRequirement.dr} and identify any missing or insufficiently covered information. The Information of the Company is stated in the <Context>. The gaps should then be presented in a systematic way, along with clear recommendations for improvement and a responsible party to address each gap.

**Procedure**:

1. **Context Review**:
    
    Review the company's existing <context> related to the requirements of <Disclosure Requirement>. Ensure that all information is evaluated against each specific requirement from **<Disclosure Requirement>**.
    
2. **Structured Analysis**:
    
    For each **<Datapoint Requirement>**, analyze the company's current disclosures and structure the findings under the following headings:
    
    - **Gap**: Clearly identify any missing or insufficiently covered information that prevents the company from fully meeting the requirements.
    - **Recommendation for Action Steps**: Provide specific, actionable steps to address the gap and ensure compliance with the <Disclosure Requirement>. Make sure to be as concrete as possible.
    - **Sample Text**: Include an example of the information that could be added to fill the gap. **Highlight information that is not yet available**, ensuring compliance gaps in regard to the requirements are transparent. Include a disclaimer in brackets if the information is not yet available.
3. **Presentation of Gaps in a List**:
    
    Ensure that all gaps are documented in a way that allows the company to prioritize and address them effectively.
    

**Expected Outcome**:

A **structured gap analysis** of the company’s compliance with **<Disclosure Requirement>**, presented in a consistent format for each paragraph of the standard.

Each Gap needs to include:

- **A headline** stating the datapoint that has a gap and the exact datapoint title: E.g.: G1-3_06: Information about nature, scope, and depth of anti-corruption or anti-bribery training programs offered or required
- **An Analysis of the Gap** based on the missing information per Datapoint Requirements.
- **A comprehensive list of recommendations**, with specific guidance for improvements.
- **A sample text**: This text should guide the reader on the expected outcome and required information, comply with requirements, and highlight information that is not yet available.

**Example Outcome:**

{
  "gapIdentified": true,
  "datapointGaps": [
    {
      "datapoint": "G1-3_06: Information about nature, scope, and depth of anti-corruption or anti-bribery training programs offered or required",
      "gap": "<p>The disclosure offers some information about training programs but lacks specific details on the nature, scope, and depth of these programs, as required by G1-3_06.</p>",
      "actions": [
        "<p>Provide a comprehensive overview of the training programs, including content, duration, and delivery methods.</p>",
        "<p>Highlight specialized training for various roles or departments.</p>"
      ],
      "exampleText": "<p>“The company’s anti-corruption and anti-bribery training programs cover a wide range of topics, including risk assessment and reporting procedures. These programs are delivered through e-learning modules and in-person workshops and are tailored to different roles within the company.” (Disclaimer: Detailed descriptions of the training programs are currently unavailable and need to be provided for compliance.)</p>"
    },
    {
      "datapoint": "S2-4_02: Description of whether and how action to provide or enable remedy in relation to an actual material impact",
      "gap": "<p>The disclosure lacks information on whether and how the company has taken steps to provide or enable remedies for actual material impacts on workers in the value chain.</p>",
      "actions": [
        "<p>Add specific examples of remediation measures taken or planned, such as compensation or support programs for affected workers.</p>",
        "<p>Describe processes to ensure that these remedies are accessible and effective.</p>"
      ],
      "exampleText": "<p>“The company has established a compensation fund for workers affected by workplace incidents, offering financial assistance and rehabilitation services.” (Disclaimer: Information on the number of compensated workers and the effectiveness of these measures is currently unavailable.)</p>"
    }
  ]
}


**Notes**:

1. All **<Disclosure Requirement>** requirements must remain unchanged and be addressed directly.
2. The analysis must focus on identifying gaps and providing actionable recommendations for the company to fully meet the **<Disclosure Requirement>**.
3. Each gap shall include a sample text that is concrete enough to give the needed guidance.
4. If you do not identify a GAP, then state this explicitly, as it is possible the information is sufficient.
5. The output structure should be in JSON format and strings can be basic html elements. DO NOT ESCAPE THE HTML AS A STRING. JUST GIVE OUT THE PURE HTML. Do not add any css classes and just give the output as h1, h2, h3, p.
6. If the output contains Numbers, Year-Numbers, Monetary Numbers, Percentages or similar numeric Data, pack them inside a <span> tag with the following styling: style="color: #eab308"

**Language**:
All the outcomes that you generate are written in ${constants_1.LANGUAGE_MAP[generationLanguage]} language, except for the "datapoint". This should be displayed in the exact same wording as specified under Disclosure Requirements and in English.


**Required Information**:

- **Company Context (<Context>)**: Comprehensive information provided by the company related to each **<Disclosure Requirement>**.
- **Disclosure Requirement Details**: Specific criteria that each gap should be checked against to ensure accurate alignment.
    
    `;
    }
    generateDataRequestGapAnalysisContentContext(dataRequest) {
        return `<Context> ${this.turndownService.turndown(dataRequest.content)} </Context>`;
    }
    generateDataRequestFullLawTextContextForReportedDatapoints(dataRequest) {
        let disclosureRequirementContenxt = `<Disclosure Requirement>
    Here are the information for the Disclosure Requirement based on the paragraphs from the official ESRS Document and the respective Datapoints matched to the exact paragraph. Your Task is to create the reporting text based on the Datapoints for the listed Disclosure Requirement:
    
    `;
        const esrsDisclosureRequirement = dataRequest.disclosureRequirement;
        disclosureRequirementContenxt += ` ${esrsDisclosureRequirement.dr}: ${esrsDisclosureRequirement.name}`;
        for (const datapointRequest of dataRequest.datapointRequests) {
            if (datapointRequest.status !== datapoint_request_entity_1.DatapointRequestStatus.NotAnswered) {
                disclosureRequirementContenxt +=
                    this.createFullEsrsDatapointLawTextContext(datapointRequest.esrsDatapoint) + `\n\n`;
                if (datapointRequest.esrsDatapoint.conditional) {
                    disclosureRequirementContenxt += `\n\nThis is a conditional datapoint". The user can decide if he/she is ok with that, or not. 
    The Condition mainly have "if relevant" or another condition stated in the law-text or application-requirements.\n\n`;
                }
            }
        }
        disclosureRequirementContenxt += `</Disclosure Requirement>`;
        return disclosureRequirementContenxt;
    }
    generateDataRequestGapAnalysisReviewSystemPrompt({ generationLanguage, }) {
        return `You are tasked with creating a feedback loop to **validate gaps** identified in a structured gap analysis of the company’s sustainability report. The purpose of this loop is to **challenge each gap** against the information provided by the company, ensuring that only those gaps where information is genuinely missing based on the requirements remain.

    **Procedure**:
    
    1. **Initial Gap Validation**:
        
        For each identified gap, compare it directly against the given company information (<Context>) for the specified **<Disclosure Requirement>**. Your objective is to determine whether the gap is legitimate by verifying that the requirement is not met with the current information.
        
    2. **Challenge Each Gap**:
        
        Review each gap critically. If any portion of the requirement appears to be addressed in the context provided, remove or modify the gap to reflect only the unmet requirements. This process is to avoid overstatement of gaps by focusing solely on genuinely missing information.
        
    3. **Refinement of Gaps**:
        
        For gaps that remain after this challenge, ensure that they are refined and precise. Only list elements that are truly missing or insufficiently detailed, as compared to the exact needs of the **<Disclosure Requirement>**.
        
    4. **Feedback Documentation**:
        
        Present each gap with the rationale for its inclusion after the review. Each gap should now be documented with clear justification on why it remains a gap, backed by specific requirements from the standard that the company context does not meet.
        
    
    **Expected Outcome**:
    
    A **validated gap analysis** where only true gaps are documented, meaning that each gap is:
    
    - Justified by an absence of information or insufficient detail in relation to the exact requirements.
    - Presented with concise, clear recommendations that address only unmet aspects of the <Disclosure Requirement>.
    - Supported by a specific example from the requirements showing where current information does not align.
    
    **Notes**:
    
    1. Ensure no gap is included if the information is partially or fully addressed within the provided company context.
    2. If gaps are revised or removed during this process, document the reasoning behind each decision.
    3. The process should focus on eliminating any assumptions about missing information and instead rely on a strict comparison to the given requirements.
    4. Any remaining gaps should be concrete and actionable, with minimal or no overlap of information already provided by the company.
    
    **Language**:
    All the outcomes that you generate are written in ${constants_1.LANGUAGE_MAP[generationLanguage]} language.

    **Required Information**:
    
    - **Company Context (<Context>)**: Comprehensive information provided by the company related to each **<Disclosure Requirement>**.
    - **Disclosure Requirement Details**: Specific criteria that each gap should be checked against to ensure accurate alignment.
    - The already found Gaps (<GAPS>): A list of gaps that were already identified.`;
    }
    generateDataRequestContextFromDatapoints(dataRequest) {
        let datapointContentContext = `<Context>`;
        for (const datapointRequest of dataRequest.datapointRequests) {
            if (datapointRequest.status === datapoint_request_entity_1.DatapointRequestStatus.CompleteData) {
                datapointContentContext += `
        ${datapointRequest.esrsDatapoint.datapointId}: ${datapointRequest.esrsDatapoint.name}
        `;
                datapointContentContext +=
                    this.turndownService.turndown(datapointRequest.content) + `\n\n`;
            }
        }
        return (datapointContentContext +
            `
    </Context>
    `);
    }
    generateDataRequestContentGenerationSystemPrompt1(disclosureRequirement) {
        return `    
    **Task:**

    You help with creating the final text for the ESG report of a EU corporation. Your task is to create the text for a specific Disclosure Requirement (DR) based on the different Data Points and their requirements the DR consists of. The results should be formulated in such a way that they can be directly integrated into the company’s sustainability report. For the given Disclosure Requirement all relevant requirements must be considered.
    
    **Disclosure Requirement**
    ${disclosureRequirement.dr} ${disclosureRequirement.name}
    
    **Procedure:**

    1. **Context Review**: Review all the different Datapoints which are the essence of the Disclosre Requirement. Extract the relevant information to meet the requirements of the specific Disclosre Requirement.
    2. **Integration of relevant information:** Ensure that all documents are evaluated against each other to avoid redundancies or contradicting information.
    
    **Data:**
    Below the system will supply with the neccessary data:
    1. Law Texts of all Datapoints that must be included
    2. Context of all the Datapoints
    --------------
    3. Example text for one-shot-learning
    --------------
    `;
    }
    generateDataRequestContentExampleIntegrated() {
        return `
    -------------- (End of Law texts & Data content)
    --------------
    Example text with masked facts for one-shot-learning:
    <h2>Policies for Managing Significant Impacts, Risks, and Opportunities Related to XXX (SX.XXX to SX-XXX)</h2>
    <p>The XXX Group has developed comprehensive policies to identify, manage, and continuously improve the significant impacts, risks, and opportunities related to its workforce. These policies not only promote ethical behavior and adherence to international standards but also actively contribute to creating a safe, inclusive, and sustainable working environment.</p>
    <h3>Ethical Behavior and Compliance (SX.XXX, SX-XXX)</h3>
    <p>The Code of Conduct forms the foundation for ethical behavior, compliance with laws, and the protection of human rights at XXX. It applies worldwide to all employees and subsidiaries and is designed to minimize risks such as discrimination, corruption, and other ethical concerns. Additionally, the Code of Conduct supports a respectful, inclusive working environment and will be backed by mandatory training from 2024 (SX.XXX, SX.XXX, S1.XXX, SX.XXX).</p>
    <h3>Alignment with International Standards (SX.XXX, SX-XXX)</h3>
    <p>TAKKT aligns itself with globally...</p>...

    -------------- (End of Example text)`;
    }
    generateDataRequestContentExampleTechnical() {
        return `
    -------------- (End of Law texts & Data content)
    Example text with masked facts for one-shot-learning:
    --------------
    <h2>Policies for Managing Significant Impacts, Risks, and Opportunities Related to Own Workforce (SX.XXX to SX-XXX)</h2>
    <h3>S1.XXX – General Objectives and Impacts</h3>
    <p>XXX is committed, through its Code of Conduct and Occupational Health and Safety Policy, to promoting ethical behavior, compliance with laws, and respect for human rights. These policies mitigate risks such as discrimination and ethical misconduct while maximizing positive impacts on the workforce by establishing clear guidelines for a respectful working environment.</p>
    <h3>S1.XXX – Scope and Exclusions</h3>
    <p>XXX Group’s policies, including the Code of Conduct, apply globally and encompass all subsidiaries, employees, and contractors. This ensures uniform implementation of global standards across all areas of the company.</p>
    <h3>S1.XXX-XXX – Responsibility</h3>
    <p>The Executive Board and Compliance Team are responsible...</p>
    -------------- (End of Example text)`;
    }
    generateDataRequestContentGenerationSystemPrompt2({ disclosureRequirement, generationLanguage, reportTextGenerationRules, customUserRemark, enableDatapointTags, }) {
        return `
    **Task:**
    Creating now the final text that reconciles all of the above data point texts into on coherent report text for the Disclosure Requirement ${disclosureRequirement.name} based on the legal requirements. The results should be formulated in such a way that they can be directly integrated into the company’s sustainability report.

    **Procedure:**

    1. **Context Review**: Review all the different Datapoints which are the essence of the Disclosre Requirement. Extract the relevant information to meet the requirements of the specific Disclosre Requirement.
    2. **Integration of relevant information:** Ensure that all documents are evaluated against each other to avoid redundancies or contradicting information.

    **Generation Rules**
    ${reportTextGenerationRules !== '' ? `**Report Text Generation Rules**:\n${reportTextGenerationRules}` : ''}

    Notes:
    1. Ensure that the writing is as complete as possible based on the given information.
    2. Ensure that the output is complete and coherent, meeting all the requirements of the relevant Disclosure Requirement within the standard.
    3. Ensure that no information is excluded only ensure that redundant information are prevented.
    4. DO NOT WRITE ABOUT ANYTHING THAT IS NOT SPECIFICALLY MENTIONED IN <Context>.
    5. The output structure should be in html. DO NOT ESCAPE THE HTML AS A STRING. JUST GIVE OUT THE PURE HTML. Do not add any css classes and just give the output as h1, h2, h3, p.
    6. If the output contains Numbers, Year-Numbers, Monetary Numbers, Percentages or similar numeric Data, pack them inside a <span> tag with the following styling: style="color: #eab308"
    7. ${enableDatapointTags ? 'Include the tags of the datapoints in the text as separate sub-headings.' : 'Do not include the tags of the datapoints in the text.'}
    ${customUserRemark && customUserRemark.length > 2 ? `**Additional User Remark (High Priority - CONSIDER THIS WHEN GENERATING THE REPORT TEXT)**:\n${customUserRemark}` : ''}

    **Language**:
    All the outcomes that you generate are written in ${constants_1.LANGUAGE_MAP[generationLanguage]} language`;
    }
    reduceLinkedDocumentChunkPrompt(datapointRequest, documentChunk) {
        return ` 
    **Task:**
    Your task is to assist with collecting & extracting data for a specfic datapoint in our company's ESG report. The EU's corporate sustainability reporting directive describes a range of topics, disclosure requirements and specific datapoints we have to report upon. A later language model will write the texts for each datapoint. For this we need to preprocess the data to feed into it. The information we need is contained in reports, policy documents etc. Since they are too long to fit in a single language model, we have to extract the relevant data & summarise at least some of them. For this, the systems translates documents into markdown and chunks them up into shorter chunks. I then provide pairs of chunks and datapoints I have to report upon to you, a language model. Your task is to extract all information from the chunk of a document that is relevant for our reporting for the respective datapoint and return it as json or alternatively return a notice for keeping the whole chunk.
    
    Text Chunk:
    --------------
    ${documentChunk.content.replace(llm_response_util_1.MERGED_CELL_REGEX, '')}
    --------------
    (End of Chunk)

    You are working on datapoint ${datapointRequest.esrsDatapoint.datapointId}: ${datapointRequest.esrsDatapoint.name} from the disclosure requirement ${datapointRequest.esrsDatapoint.esrsDisclosureRequirement.name}
        
    Legal requirements of the datapoint: 
    ${datapointRequest.esrsDatapoint.lawText}
    ---
    Legal requirements in more detail:
    ${datapointRequest.esrsDatapoint.lawTextAR}

    **Procedure:**
    Now generate a short version of the chunk in json format that contains all pieces of concrete information where there is even the slightest chance that they might be useful for reporting on the respective datapoint. **STRICTLY ONLY WRITE INFORMATION THAT IS ACTUALLY IN THE CHUNK** and keep the phrasing as it is initially to avoid slight deviations in meaning. Write comprehensively without missing any detail. Yet always write clear, simple, short sentences, inclduing sources and relevant metadata. Stick to minimalist phrasing without being verbose, your output is only going to be processed by another language model, therefore focus on it being easy and unambiguous to process. Also include sources as far as provided. Provide all information about sources including but not limited to [document name, source, page or headings & subheadings] if and only if they are provided. Since the input comes from markdown versions of the documents, "## Reduction strategy & measuresIn 2023, the Group-wide emissions" implies that "Reduction strategy & measures" is a heading: Notice the "##" and the lack of a space between "measures" and "In", indicating a new line wihtin the heading started.

    Example response:
    --------------
    {"keep_chunk_as_is": "false", "key_information":"Extracted Text for E1-X_XX Disclosure of Decarbonisation Levers and Key Actions at XXX XXX (Requirements of ESRS E1-1_03):\n 
    - _Sustainable Mobility_: According to Document {Source Name} heading > subheading on page 37: 'XXX XXX already has around XY e-vehicles in use...conversion of the vehicle fleet to CO2-neutral drive types is being evaluated on an ongoing basis.'.\n
    - _Transparent Compensation_: For the remaining approximately XY t of XY in the year XY which resulted from airport operations, offsetting was carried out via XXX to compensate for these CO2 emissions.
    ..."}
    --------------
    (End Example)

    Sometimes most of the chunk is relevant context. If more than a third of the chunk is relevant context for reporting on the datapoint as is, don't try to compress it. Instead you can forward the whole chunk as is by setting in the json the key "keep_chunk_as_is" to "true".
    E.g. {"keep_chunk_as_is": "true", "key_information":""}

    Notes:
    1. Keep the original phrasings. Do not try to rephrase it to make it sound more fluent. Only add to the phrasing if important context from elsewhere in the chunk enriches important parts of the meaning.
    2. Be concrete. We need concrete plans, policies, numbers, claims, promises.
    3. Make sure not skip any context that might shift the meaning of the text. Be very extensive & comprehensive. Be as complete and coherent as possible given the information. 
    4. If sources are given, provide them.
    5. MAKE SURE TO NOT ADD OR IMPLY ANY INFORMATION THAT IS NOT IN THE TEXT.
    6. All the outcomes that you generate are written in ${this.GENERATION_LANGUAGE}

    Return a json with all information of the chunk that is relevant to the datapoint ${datapointRequest.esrsDatapoint.name}:
`;
    }
    validateReportTextGenerationRulesPrompt(language) {
        return `Please analyze the content submitted in the "Report Text Generation Rules" field to determine if it contains completely nonsensical, or potentially harmful content. When evaluating, please consider the following:
    
  Please provide your analysis in ${constants_1.LANGUAGE_MAP[language]} language.
  Be rather lenient on this, if its not actively harmful or confusing, it should be considered relevant. These notes will be used in context, where they might make sense later.
  
  Response should be in JSON format: 
  {
    "status": 200 | 403,
    "reason": "reason from above 3, translated to ${constants_1.LANGUAGE_MAP[language]} language"
  }`;
    }
    formatTopics({ topics, level, material, }) {
        const indent = '  '.repeat(level);
        let lines = [];
        for (const t of topics) {
            lines.push(`${indent}- ${t.name}`);
            if (t.description && material) {
                lines.push(`${indent}  ${t.description}`);
            }
            if (t.children && t.children.length > 0) {
                lines = lines.concat(this.formatTopics({ topics: t.children, level: level + 1, material }));
            }
        }
        return lines;
    }
    datapointRequestGapAnalysisSystemPromptMaterialTopics({ topics, material, }) {
        const topicHierarchyLines = this.formatTopics({
            topics,
            level: 0,
            material,
        });
        const mainTopics = topics.map((t) => t.name).join(', ');
        if (material) {
            return `
  **Material Topic and Sub-Topics:**
  The Datapoint relates to the Material Topic(s): ${mainTopics}
  Below is the hierarchical breakdown of the Material Topic(s) and their Sub-Topics (including any Sub-Sub-Topics), which have been assessed as **material** for the company:

  ${topicHierarchyLines.join('\n')}
  
  Important Note: Gap analysis should only consider these Topics and its Sub-Topics that are actually material (as provided above) to the company.
  `;
        }
        else {
            return `
    **Non-Material Topics and Sub-Topics:**
    The Datapoint relates to the following Topic(s), which have been assessed as **non-material** for the company:

    ${topicHierarchyLines.join('\n')}

  Important Note: Gap analysis should NOT consider these Topics and its Sub-Topics that are non-material to the company.
  `;
        }
    }
    datapointRequestContentGenerationSystemPromptMaterialTopics({ topics, material, }) {
        const topicHierarchyLines = this.formatTopics({
            topics,
            level: 0,
            material,
        });
        const mainTopics = topics.map((t) => t.name).join(', ');
        if (material) {
            return `
  **Material Topic and Sub-Topics:**
  The Datapoint relates to the Material Topic(s): ${mainTopics}
  Below is the hierarchical breakdown of the Material Topic(s) and their Sub-Topics (including any Sub-Sub-Topics) that are directly linked to the Datapoint:

  ${topicHierarchyLines.join('\n')}
  
  **Important Note:**
  - Only report on the Sub-Topics that are actually linked to the Datapoint and material (as provided above).
  - The writing style should be suitable for direct integration into a sustainability report.
  - Ensure coherence, clarity, and factual accuracy.
  `;
        }
        else {
            return `
    **Non-Material Topics and Sub-Topics:**
    The Datapoint relates to the following Topic(s), which have been assessed as **non-material** for the company:

    ${topicHierarchyLines.join('\n')}

    **Important Note:**
    - The following Topics and Sub-Topics are included for reference but have been identified as not material to the company’s sustainability priorities or reporting requirements.
    - Do not write about those non-material aspect of the legal requirements, since non-material means they do not have to be reported upon.
    - The writing style should be suitable for direct integration into a sustainability report.
    - Ensure coherence, clarity, and factual accuracy.
  `;
        }
    }
    datapointRequestContentGenerationSystemPromptRelatedDatapoints(relatedDatapoints) {
        return `Here are **other** but related Datapoints that belong to the same disclore requirements. They already have been reported upon: 

        ${relatedDatapoints
            .map((dp) => `**${dp.esrsDatapoint.datapointId} - ${dp.esrsDatapoint.name}:**
            ${this.turndownService.turndown(dp.content.replace(llm_response_util_1.CITATION_CLIENT_REGEX, '$3'))}`)
            .join('\n\n')}
       
       Use this information to understand what _not_ to report on, if it does not directly belong to the datapoint at hand but to one of those. Make sure to be consistent with those related datapoints and mention discrepancies if they conflict with other data from the <Context>."`;
    }
};
exports.PromptService = PromptService;
exports.PromptService = PromptService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], PromptService);
//# sourceMappingURL=prompts.service.js.map