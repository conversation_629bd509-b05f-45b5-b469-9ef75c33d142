
import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

export const ProjectsLoadingState: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div className="space-y-2">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-72" />
        </div>
        <Skeleton className="h-10 w-40 rounded-md" />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <div key={i} className="border rounded-lg p-6 shadow-sm animate-pulse">
            <div className="space-y-4">
              <Skeleton className="h-6 w-36" />
              <Skeleton className="h-4 w-24" />
              <div className="flex justify-between items-end mt-4">
                <div className="space-y-2">
                  <Skeleton className="h-3 w-20" />
                  <Skeleton className="h-2 w-32" />
                </div>
                <Skeleton className="h-10 w-10 rounded-full" />
              </div>
              <div className="mt-4">
                <Skeleton className="h-2.5 w-full rounded-full" />
                <div className="flex justify-between text-xs mt-1">
                  <Skeleton className="h-3 w-10" />
                  <Skeleton className="h-3 w-8" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
