import axios from 'axios';
import { API_URL } from '@/api/apiConstants';
import { DocumentData } from '@/types/document';
import { supabase } from '@/integrations/supabase/client';

export interface LinkedDocumentQuestion {
  id: string;
  text: string;
  projectId?: string;
  projectName?: string;
  questionCode?: string;
  pages?: string;
  comment?: string;
  issueTitle?: string;
}

export interface DocumentDetailsResponse extends DocumentData {
  chunks: any[];
  linkedQuestions?: LinkedDocumentQuestion[];
}

export const fetchDocumentData = async (id: string): Promise<DocumentData> => {
  const response = await axios.get<DocumentData>(`${API_URL}/documents/${id}`);
  return response.data;
};

export const startDocumentExtraction = async (
  id: string
): Promise<{ message: string }> => {
  const response = await axios.post<{ message: string }>(
    `${API_URL}/documents/${id}/extract-chunks`,
    {
      premiumMode: true,
    }
  );
  return response.data;
};

export const startDocumentChunkDatapointLinking = async (
  id: string,
  testMode: boolean
): Promise<{}> => {
  const response = await axios.post<{ message: string }>(
    `${API_URL}/documents/${id}/link-to-datapoints`,
    {
      testing: testMode,
    }
  );
  return response.data;
};

export const updateDatapointLinkToDocumentChunk = async (
  documentChunkId: string,
  data: {
    datapointRequestId: string;
    linked: boolean;
  }[]
) => {
  await axios.post(
    `${API_URL}/documents/chunk/${documentChunkId}/link-datapoints`,
    data
  );
};

export const dateDocumentChunk = async (id: string) => {
  await axios.delete(`${API_URL}/documents/chunk/${id}`);
};

/**
 * Fetches document details from a Supabase Edge Function.
 * Data transformation logic happens here, only SQL fetch happens server-side.
 */
export const fetchDocumentDetails = async (id: string): Promise<DocumentDetailsResponse> => {
  const { data: {data}, error } = await supabase.functions.invoke('get-document-details', {
    body: { id }
  });

  if (error) {
    console.error('Error fetching EcoVadis project:', error);
    throw new Error('Failed to fetch EcoVadis project details');
  }
  // Data transformation logic (keep as previously)
  const transformedData: DocumentDetailsResponse = {
    ...data,
    linkedQuestions: data.linkedQuestions?.flatMap((chunk: any) =>
      chunk.project_ecovadis_linked_document_chunks?.map((link: any) => ({
        id: link.id,
        text: link.answer.option.question.question,
        issueTitle: link.answer.option.issueTitle,
        questionCode: link.answer.option.question.questionCode,
        projectId: link.answer.project.id,
        projectName: link.answer.project.name,
        pages: chunk.page,
        comment: link.comment
      })) ?? []
    ) ?? []
  };
  return transformedData;
};
