import { FileIcon } from 'lucide-react';

import { Button } from '../ui/button';
import { toast } from '../ui/use-toast';

import { downloadDataRequestReport } from '@/api/project-settings/project-settings.api';
import { Project } from '@/types/project';

export const ExportReportTextButton = ({ project }: { project: Project }) => {
  async function handleClick() {
    try {
      const docxblob = await downloadDataRequestReport(project.id);
      const url = window.URL.createObjectURL(
        new Blob([docxblob], {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        })
      );
      const link = document.createElement('a');

      link.href = url;
      link.setAttribute('download', `${project.name} Reporttext.docx`);
      document.body.appendChild(link);
      link.click();

      // Clean up
      link.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: 'Download Started',
        variant: 'success',
      });
    } catch (error: any) {
      toast({
        title: 'Error exporting report',
        description:
          error.response.status === 404
            ? 'No approved reporttexts found for this project'
            : error.message,
        variant: 'destructive',
      });
    }
  }

  return (
    <Button onClick={handleClick} variant="outline">
      <FileIcon className="w-4 h-4 mr-2" />
      Export Report
    </Button>
  );
};
