import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1740059058666 implements MigrationInterface {
  name = 'SchemaUpdate1740059058666';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."datapoint_generation_status_enum" RENAME TO "datapoint_generation_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."datapoint_generation_status_enum" AS ENUM('pending', 'approved', 'rejected', 'minorChanges')`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_generation" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_generation" ALTER COLUMN "status" TYPE "public"."datapoint_generation_status_enum" USING "status"::"text"::"public"."datapoint_generation_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_generation" ALTER COLUMN "status" SET DEFAULT 'pending'`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."datapoint_generation_status_enum_old"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."datapoint_generation_status_enum_old" AS ENUM('pending', 'approved', 'rejected')`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_generation" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_generation" ALTER COLUMN "status" TYPE "public"."datapoint_generation_status_enum_old" USING "status"::"text"::"public"."datapoint_generation_status_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_generation" ALTER COLUMN "status" SET DEFAULT 'pending'`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."datapoint_generation_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."datapoint_generation_status_enum_old" RENAME TO "datapoint_generation_status_enum"`,
    );
    }
}
