import { Queue } from 'bull';
import { Repository } from 'typeorm';
import { Document } from '../document/entities/document.entity';
export declare class CronService {
    private readonly chunkExtractionQueue;
    private readonly chunkLinkingQueue;
    private readonly documentRepository;
    constructor(chunkExtractionQueue: Queue, chunkLinkingQueue: Queue, documentRepository: Repository<Document>);
    private readonly logger;
    checkUnprocessedDocuments(): Promise<void>;
}
