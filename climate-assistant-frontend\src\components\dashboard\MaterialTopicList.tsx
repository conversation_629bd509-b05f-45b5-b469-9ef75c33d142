export const MATERIAL_TOPIC_ORDER = ['topic', 'sub-topic', 'sub-sub-topic'];
export type MaterialTopicsType = {
  id: string;
  level: string;
  name: string;
  children: MaterialTopicsType[];
};

export const MaterialTopicList = ({ topic }: { topic: MaterialTopicsType }) => {
  return (
    <div>
      Material <span className="font-bold">{topic.level}</span>: {topic.name}
      {topic.children?.map((child) => (
        <MaterialTopicList key={child.id} topic={child} />
      ))}
    </div>
  );
};
