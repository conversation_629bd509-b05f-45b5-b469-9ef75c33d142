
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Copy } from 'lucide-react';
import { LinkedDocument } from '@/types/ecovadis';
import { toast } from 'sonner';
import { MarkdownRenderer } from '@/components/ui/markdown-renderer';

interface GapFixModalContentProps {
  relatedDocument?: LinkedDocument[];
  recommendedActions: string;
  impact: string;
  exampleText: string;
  description: string;
  onComplete: () => void;
  onClose: () => void;
  gapTitle?: string;
}

export const GapFixModalContent: React.FC<GapFixModalContentProps> = ({ 
  relatedDocument, 
  recommendedActions,
  impact,
  exampleText,
  description,
  onComplete, 
  onClose,
  gapTitle = ""
}: GapFixModalContentProps) => {
  // State for file uploads
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);

  const getRecommendedActions = () => {
    return recommendedActions.split('\n').map((action, index) => {
      return action.trim()
    }
    );
  };


  const copyTemplateToClipboard = () => {
    navigator.clipboard.writeText(exampleText)
      .then(() => {
        toast.success("Template copied to clipboard!", {
          description: "You can now paste it in your document"
        });
      })
      .catch((err) => {
        console.error("Failed to copy text: ", err);
        toast.error("Failed to copy template", {
          description: "Please try again or copy manually"
        });
      });
  };

  const recommendedSteps = getRecommendedActions();
  
  return (
    <div className="space-y-6">
      {gapTitle && (
      <div className="space-y-2">
        <h2 className="text-xl font-semibold text-glacier-darkBlue">
          {gapTitle}
        </h2>
      </div>
      )}

      <div className="space-y-2">
        <h3 className="text-base font-semibold">
          Gap description:
        </h3>
        <p className="text-gray-700">
          <MarkdownRenderer text={description} />
        </p>
      </div>
      
      {/* Recommended Actions */}
      <div className="space-y-3">
        <h3 className="text-base font-semibold">
          Recommended Actions:
        </h3>
        <div className="space-y-2">
          <ol className="list-decimal pl-5 space-y-2">
            {recommendedSteps.map((action, index) => (
              <li key={index} className="flex items-start">
              <div className="h-5 w-5 flex-shrink-0 rounded-full bg-glacier-mint flex items-center justify-center text-xs text-white font-medium mr-3 mt-0.5">
                {index + 1}
              </div>
              <div>
                <p className="text-sm text-glacier-darkBlue font-medium">
                  <MarkdownRenderer text={action} />
                </p>
              </div>
              </li>
            ))}
          </ol>
        </div>
      </div>
      
      {/* Template */}
      <div className="space-y-2">
        <h3 className="text-base font-semibold">
          Use the AI-generated template as a starting point.
        </h3>
        <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-blue-600 font-medium">Example Text Template</span>
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-8 text-gray-500"
              onClick={copyTemplateToClipboard}
            >
              <Copy className="h-4 w-4 mr-1" />
              <span className="text-xs">Copy</span>
            </Button>
          </div>
          <pre className="text-sm bg-white p-4 rounded border border-gray-100 font-mono whitespace-pre-wrap text-gray-700">
            {/* {exampleText} */}
          <MarkdownRenderer text={exampleText} />
          </pre>
        </div>
      </div>
      
      {/* Actions */}
      <div className="flex justify-start gap-3 pt-4">
        <Button 
          variant="darkBlue"
        >
          Mark as complete
        </Button>
        
        <Button 
          variant="outline"
          onClick={onClose}
        >
          Close
        </Button>
      </div>
    </div>
  );
};
