// @ts-ignore
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { authValidator } from "../_shared/authValidator.ts";
import { getSupabaseClient } from "../_shared/supabaseClient.ts";
// @ts-ignore
import * as XLSX from "https://cdn.jsdelivr.net/npm/xlsx@0.18.5/+esm";

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get the request body
    const { projectId } = await req.json();

    const { user, error, supabaseClient, response: errResponse } = await authValidator(req);
    
    if (!user || error || !supabaseClient) {
      return errResponse;
    }

    const projectIds: string[] = projectId ? [projectId] : [];

    // If no specific project ID provided, fetch all projects for the user
    if (!projectId) {
      const { data: projectsData, error: projectsError } = await supabaseClient
        .from('project')
        .select('id')
        .eq('workspaceId', user.user_workspace[0].workspaceId)
        .eq('type', 'EcoVadis');

      if (projectsError) {
        console.error('Error fetching projects:', projectsError);
        return new Response(JSON.stringify({
          error: 'Failed to fetch projects'
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 500
        });
      }

      projectIds.push(...(projectsData || []).map(project => project.id));
    }
    
    // Fetch all gaps for the specified projects
    const { data: gapsData, error: gapsError } = await supabaseClient
      .from('project_ecovadis_gaps')
      .select(`
        *,
        ecovadis_question (
          id,
          questionCode,
          indicator,
          question,
          questionName,
          ecovadis_theme (
            title
          )
        ),
        project (
          id,
          name
        )
      `)
      .in('projectId', projectIds)
      .order('createdAt', { ascending: true });
      
    if (gapsError) {
      console.error('Error fetching gaps:', gapsError);
      return new Response(JSON.stringify({
        error: 'Failed to fetch project gaps'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }
    
    // Collect all document IDs from gaps
    const allDocumentIds: string[] = [];
    (gapsData || []).forEach(gap => {
      if (gap.documents && Array.isArray(gap.documents)) {
        allDocumentIds.push(...gap.documents);
      }
    });
    
    // Get unique document IDs
    const uniqueDocumentIds = [...new Set(allDocumentIds)].filter(Boolean);
    
    // Fetch document names for all gap documents
    let documentsMap = {};
    if (uniqueDocumentIds.length > 0) {
      const { data: documentsData, error: documentsError } = await supabaseClient
        .from('document')
        .select('id, name')
        .in('id', uniqueDocumentIds);
      
      if (!documentsError && documentsData) {
        documentsMap = documentsData.reduce((map, doc) => ({ ...map, [doc.id]: doc.name }), {});
      }
    }
    
    // Process gaps for Excel export
    const excelData = (gapsData || []).map(gap => {
      // Parse gaps object if it's a string
      const gapsObject = typeof gap.gaps === 'string' ? JSON.parse(gap.gaps) : gap.gaps || {};
      
      // Map document IDs to names
      const documentNames = Array.isArray(gap.documents) 
        ? gap.documents.map(docId => documentsMap[docId] || 'Unknown Document').join(', ')
        : '';
      
      // Create row for Excel
      return {
        'Question Code': gap.ecovadis_question?.questionCode || gap.questionCode || '',
        'Question Title': gap.ecovadis_question?.question || gap.question || '',
        'Question Name': gap.ecovadis_question?.questionName || '',
        'Project': gap.project?.name || gap.projectName || '',
        'Topic': gap.ecovadis_question?.ecovadis_theme?.title || gapsObject.Topic || '',
        'Document Name': documentNames,
        'Gap Summary': gapsObject.Title || '',
        'Gap Details': gapsObject.Description || '',
        'Recommendations': gapsObject["Recommended Actions"] || '',
        'Sample Text': gapsObject["Sample Text"] || '',
        'Status': gap.resolved ? 'Complete' : 'Incomplete',
        'Created At': gap.createdAt ? new Date(gap.createdAt).toLocaleDateString() : ''
      };
    });
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);
    
    // Set column widths for better readability
    const columnWidths = [
      { wch: 15 }, // Question Code
      { wch: 40 }, // Question Title
      { wch: 20 }, // Project
      { wch: 20 }, // Topic
      { wch: 30 }, // Document Name
      { wch: 30 }, // Gap Summary
      { wch: 50 }, // Gap Details
      { wch: 50 }, // Recommendations
      { wch: 30 }, // Sample Text
      { wch: 10 }, // Status
      { wch: 15 }  // Created At
    ];
    worksheet['!cols'] = columnWidths;
    
    // Add the worksheet to the workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, "Gaps");
    
    // Generate the Excel file
    const excelBuffer = XLSX.write(workbook, { type: "base64", bookType: "xlsx" });
    
    // Build project name part of the filename
    let projectName = "";
    if (projectId && gapsData && gapsData.length > 0 && gapsData[0].project) {
      projectName = gapsData[0].project.name.replace(/[^a-zA-Z0-9]/g, "_") + "_";
    }
    
    // Current date for the filename
    const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const filename = `${projectName}EcovadisGaps_${date}.xlsx`;
    
    // Return the Excel file
    return new Response(JSON.stringify({
      data: excelBuffer,
      filename
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
    
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});
