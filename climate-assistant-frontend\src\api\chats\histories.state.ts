import { useQuery } from '@tanstack/react-query';
import { useNavigate, useParams } from 'react-router-dom';

import { CHAT_HISTORIES_QUERY_KEY } from '../apiConstants';

import { ChatHistory } from '@/models/chat.models.ts';
import { deleteHistory, fetchHistories } from '@/api/chats/chats.api.ts';
import {
  MixpanelEvents,
  MixpanelService,
} from '@/services/mixpanel.service.ts';

export const useHistories = () => {
  const activeUrlHistory = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { data, refetch } = useQuery<ChatHistory[]>({
    queryKey: [CHAT_HISTORIES_QUERY_KEY],
    queryFn: fetchHistories,
  });

  const removeHistory = async (id: ChatHistory['id']) => {
    await deleteHistory(id);
    MixpanelService.track(MixpanelEvents.historyDeleted());
    const { data } = await refetch();
    const histories = data as ChatHistory[];
    if (!histories.some((history) => history.id === activeUrlHistory)) {
      navigate('/');
    }
  };

  return {
    histories: data as ChatHistory[] | null,
    refetchHistories: refetch,
    removeHistory,
  };
};
