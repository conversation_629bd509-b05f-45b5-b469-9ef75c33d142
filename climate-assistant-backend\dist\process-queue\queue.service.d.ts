import { Job, Queue } from 'bull';
import { DocumentService } from 'src/document/document.service';
import { DatapointDocumentChunkService } from 'src/datapoint-document-chunk/datapoint-document-chunk.service';
import { UsersService } from 'src/users/users.service';
import { DatapointRequestService } from 'src/datapoint/datapoint-request.service';
import { DataRequestService } from 'src/data-request/data-request.service';
export declare class DatapointGenerationProcessor {
    private readonly datapointRequestService;
    private readonly dataRequestService;
    private readonly logger;
    constructor(datapointRequestService: DatapointRequestService, dataRequestService: DataRequestService);
    generateDatapoint(job: Job): Promise<void>;
}
export declare class DatapointReviewProcessor {
    private readonly datapointRequestService;
    private readonly dataRequestService;
    private readonly logger;
    constructor(datapointRequestService: DatapointRequestService, dataRequestService: DataRequestService);
    reviewDatapoint(job: Job): Promise<void>;
}
export declare class ChunkExtractionProcessor {
    private readonly documentService;
    private readonly chunkLinkingQueue;
    private readonly logger;
    constructor(documentService: DocumentService, chunkLinkingQueue: Queue);
    handleChunkExtraction(job: Job): Promise<void>;
}
export declare class ChunkLinkingProcessor {
    private readonly documentService;
    private readonly datapointDocumentChunkService;
    private readonly userService;
    private readonly logger;
    constructor(documentService: DocumentService, datapointDocumentChunkService: DatapointDocumentChunkService, userService: UsersService);
    handleChunkLinking(job: Job): Promise<void>;
}
