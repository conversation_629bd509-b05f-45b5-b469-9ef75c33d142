export declare function isTextPresentInHTML(htmlString: string): boolean;
export declare function chunkArray<T>(array: T[], size: number): T[][];
export declare function isRequestForDataGenerationType(requestId: string): boolean;
export declare function getGenerationIdFromRequestId(requestId: string): string;
export declare function parsePageRanges(pageRanges: string): number[];
export declare function addPageRangeBuffer(pages: number[], maxPage?: number): number[];
export declare function validatePageNumbersAgainstDocument(supabase: any, documentId: string, pageNumbers: number[]): Promise<number[]>;
