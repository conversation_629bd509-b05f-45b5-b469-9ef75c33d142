
import { useState } from "react";
import { Target, Loader2 } from "lucide-react";
import { GapItem } from "@/components/ecovadis/GapItem";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useUserRole } from "@/hooks/useUserRole";
import { USER_ROLE } from '@/constants/workspaceConstants';
import { 
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from "@/components/ui/tooltip";
import { useWorkspaceGaps } from "@/hooks/useWorkspaceGaps";
import { useQuestionGapAnalysis } from "@/hooks/useQuestionGapAnalysis";
import { useWorkspaceSettings } from "@/api/workspace-settings/useWorkspaceSettings";
import { useAuth } from "@/context/AuthContext";
import { useMemo } from "react";

interface GapAnalysisSectionProps {
  questionId?: string;
  projectQuestionId?: string;
}

export const GapAnalysisSection = ({
  questionId,
  projectQuestionId
}: GapAnalysisSectionProps) => {
  const [gapFilter, setGapFilter] = useState<"all" | "incomplete" | "complete">("all");
  const { hasRole } = useUserRole();
  const isSuperAdmin = hasRole([USER_ROLE.SuperAdmin]);
  const { gaps, isLoading, refetchGaps, handleMarkGapComplete, handleDeleteGap, handleAssignGap } = useWorkspaceGaps(questionId);
  const { handleGapAnalysis, isLoading: isGapAnalysisRunning } = useQuestionGapAnalysis(projectQuestionId, refetchGaps);
  const { workspaceUsers } = useWorkspaceSettings();
  const { user } = useAuth();

  // Prepare users for MemberSelector with current user at the top
  const users = useMemo(() => {
    if (!workspaceUsers) return [];
    
    const mappedUsers = workspaceUsers.map(user => ({
      id: user.id,
      name: user.name || user.email
    }));
    
    // Put current user at the top if they exist in workspace users
    if (user?.id) {
      const currentUserIndex = mappedUsers.findIndex(u => u.id === user.id);
      if (currentUserIndex > 0) {
        const currentUser = mappedUsers.splice(currentUserIndex, 1)[0];
        mappedUsers.unshift(currentUser);
      }
    }
    
    return mappedUsers;
  }, [workspaceUsers, user?.id]);

  const onGapAnalysis = async () => {
    await handleGapAnalysis();
    refetchGaps();
  }

  const filteredGaps = gaps.filter(gap => {
    if (gapFilter === "all") return true;
    if (gapFilter === "complete") return gap.resolved === true;
    return gap.resolved !== true; // incomplete filter
  });

  if (isLoading) {
    return (
      <div className="mt-4 p-4 bg-glacier-mint/10 border border-glacier-mint/30 rounded-lg">
        <div className="flex items-center gap-3">
          <Loader2 className="h-5 w-5 animate-spin text-glacier-darkBlue" />
          <span className="text-glacier-darkBlue">Loading gaps...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-4">
      <div className="flex flex-wrap justify-between items-center mb-4 gap-3">
        {(!filteredGaps.length) ? (
          <div className="flex w-full">
            <Tooltip delayDuration={300}>
              <TooltipTrigger asChild>
                <div>
                  <Button 
                    onClick={isSuperAdmin ? onGapAnalysis : undefined} 
                    disabled={isGapAnalysisRunning || !isSuperAdmin} 
                    className="bg-glacier-darkBlue hover:bg-glacier-darkBlue/90 text-white flex items-center gap-1"
                  >
                    {isGapAnalysisRunning ? (
                      <>
                        <Loader2 className="h-3.5 w-3.5 animate-spin" />
                        <span>Running Analysis...</span>
                      </>
                    ) : (
                      <>
                        <Target className="h-3.5 w-3.5" />
                        <span>AI Gap Analysis</span>
                      </>
                    )}
                  </Button>
                </div>
              </TooltipTrigger>
              {!isSuperAdmin && (
                <TooltipContent side="bottom">
                  <p>Only Super Admins can run AI gap analysis</p>
                </TooltipContent>
              )}
            </Tooltip>
          </div>
        ) : (
          <div className="flex items-center space-x-3 w-full">
            <Tooltip delayDuration={300}>
              <TooltipTrigger asChild>
                <div>
                  <Button 
                    onClick={isSuperAdmin ? onGapAnalysis : undefined} 
                    disabled={isGapAnalysisRunning || !isSuperAdmin} 
                    className="bg-glacier-darkBlue hover:bg-glacier-darkBlue/90 text-white flex items-center gap-1"
                  >
                    <Target className="h-3.5 w-3.5" />
                    <span>AI Gap Analysis</span>
                  </Button>
                </div>
              </TooltipTrigger>
              {!isSuperAdmin && (
                <TooltipContent side="bottom">
                  <p>Only Super Admins can run AI gap analysis</p>
                </TooltipContent>
              )}
            </Tooltip>
            
            <Select value={gapFilter} onValueChange={(value) => setGapFilter(value as "all" | "incomplete" | "complete")}>
              <SelectTrigger className="w-40 bg-white border border-gray-200 rounded-full h-10">
                <SelectValue placeholder="All Gaps" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Gaps</SelectItem>
                <SelectItem value="incomplete">Incomplete</SelectItem>
                <SelectItem value="complete">Complete</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
      </div>
      
      {isGapAnalysisRunning && (
        <div className="p-4 bg-glacier-mint/10 border border-glacier-mint/30 rounded-lg mb-4">
          <div className="flex items-center gap-3">
            <Loader2 className="h-5 w-5 text-glacier-darkBlue animate-spin" />
            <span className="text-glacier-darkBlue">AI is analyzing gaps for this question...</span>
          </div>
        </div>
      )}
      <>
        {filteredGaps.length > 0 ? (
          <div className="space-y-4">
            {filteredGaps.map(gap => (
              <GapItem 
                  key={gap.id} 
                  gap={gap} 
                  onMarkAsComplete={handleMarkGapComplete} 
                  onDeleteGap={handleDeleteGap}
                  onAssignUser={handleAssignGap}
                  users={users}
                />
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center p-10 text-center border border-dashed border-gray-200 rounded-lg">
              {gaps.length > 0 ? (
                <div className="text-gray-500 mb-3">
                  {gapFilter === "complete" 
                    ? "No completed gaps yet." 
                    : gapFilter === "incomplete" 
                      ? "No incomplete gaps left. Good job! 🎉" 
                      : "No gaps to display with the current filter."
                  }
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <div className="text-gray-500">
                    Run an AI gap analysis to identify potential improvements
                  </div>
                </div>
              )}
            </div>
          )}
        </>
    </div>
  );
};
