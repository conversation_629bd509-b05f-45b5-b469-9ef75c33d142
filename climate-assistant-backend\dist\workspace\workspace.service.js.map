{"version": 3, "file": "workspace.service.js", "sourceRoot": "", "sources": ["../../src/workspace/workspace.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAqC;AACrC,kEAAwD;AACxD,mFAA8E;AAC9E,+DAAqD;AACrD,iEAAkE;AAClE,6DAAyD;AACzD,8EAG2C;AAC3C,8DAAoD;AAG7C,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAC3B,YAEmB,mBAA0C,EAE1C,iBAAsC,EAEtC,uBAAkD,EAE3D,eAAkC,EAEzB,cAAgC,EAEhC,wBAAoD,EAC7D,YAA0B;QAXjB,wBAAmB,GAAnB,mBAAmB,CAAuB;QAE1C,sBAAiB,GAAjB,iBAAiB,CAAqB;QAEtC,4BAAuB,GAAvB,uBAAuB,CAA2B;QAE3D,oBAAe,GAAf,eAAe,CAAmB;QAEzB,mBAAc,GAAd,cAAc,CAAkB;QAEhC,6BAAwB,GAAxB,wBAAwB,CAA4B;QAC7D,iBAAY,GAAZ,YAAY,CAAc;IACjC,CAAC;IAEJ,KAAK,CAAC,QAAQ,CAAC,EAAmB;QAChC,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;YACtC,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,SAAS,EAAE,CAAC,WAAW,CAAC;SACzB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,EAAU,EAAE,OAA2B;QACtD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAEnE,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QAGD,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAGlC,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,WAAmB;QAC3C,OAAO,IAAI,CAAC,uBAAuB;aAChC,IAAI,CAAC;YACJ,KAAK,EAAE,EAAE,WAAW,EAAE;YACtB,SAAS,EAAE,CAAC,MAAM,EAAE,aAAa,CAAC;SACnC,CAAC;aACD,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE,CACvB,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;YACxB,IAAI,MAAM,GAAG,UAAU,CAAC;YACxB,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,MAAM,GAAG,QAAQ,CAAC;YACpB,CAAC;iBAAM,CAAC;gBACN,MAAM,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,wBAAS,CAAC,eAAe,EAAE,CAAC;oBACtD,MAAM,GAAG,SAAS,CAAC;gBACrB,CAAC;YACH,CAAC;YACD,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;YACxB,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;YACtB,OAAO;gBACL,GAAG,EAAE,CAAC,IAAI;gBACV,IAAI,EAAE,EAAE,CAAC,IAAI;gBACb,MAAM;aACP,CAAC;QACJ,CAAC,CAAC,CACH,CAAC;IACN,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,EACvB,WAAW,EACX,MAAM,EACN,IAAI,GAKL;QACC,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;YACxD,WAAW;YACX,MAAM;YACN,IAAI;YACJ,QAAQ,EAAE,IAAI,IAAI,EAAE;SACrB,CAAC,CAAC;QACH,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC1D,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,EACrB,WAAW,EACX,MAAM,GAIP;QACC,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC;YAC1C,KAAK,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;SAC/B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,EAC5B,WAAW,EACX,MAAM,GAIP;QACC,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC;IACrE,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,EAC1B,YAAY,EACZ,MAAM,EACN,WAAW,EACX,KAAK,EACL,IAAI,EACJ,eAAe,GAAG,IAAI,GAQvB;QACC,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YAC3C,KAAK,EAAE,EAAE,KAAK,EAAE;YAChB,SAAS,EAAE,CAAC,gBAAgB,CAAC;SAC9B,CAAC,CAAC;QACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE;SAC/B,CAAC,CAAC;QACH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YAC7C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK,6CAA6C,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;gBACxD,WAAW;gBACX,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,IAAI;aACL,CAAC,CAAC;YACH,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;QAExC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;YAC9C,KAAK,EAAE,WAAW;YAClB,IAAI;YACJ,IAAI,EAAE,wBAAS,CAAC,eAAe;YAC/B,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;SAC1D,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEvC,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;gBACjC,KAAK,EAAE,WAAW;gBAClB,YAAY;gBACZ,KAAK;gBACL,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,EACrB,WAAW,EACX,MAAM,GAIP;QACC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC;QAC3E,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YACvE,OAAO,aAAa,CAAC;QACvB,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,EACvB,WAAW,EACX,KAAK,EACL,GAAG,EACH,WAAW,GAMZ;QACC,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC;YACnD,WAAW;YACX,KAAK;YACL,GAAG;YACH,WAAW;SACZ,CAAC,CAAC;QACH,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,WAAiB,EAAE,IAAU;QAC/C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC;YAC/D,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;SAC3B,CAAC,CAAC;QACH,MAAM,eAAe,GAAG,aAAa,EAAE,IAAI,CAAC;QAC5C,IAAI,eAAe,KAAK,4BAAI,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QACrD,IACE,eAAe,KAAK,4BAAI,CAAC,aAAa;YACtC,CAAC,4BAAI,CAAC,cAAc,EAAE,4BAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,EAC7D,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IACE,eAAe,KAAK,4BAAI,CAAC,cAAc;YACvC,CAAC,4BAAI,CAAC,cAAc,EAAE,4BAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,EAC7D,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,YAAY,CAAC,IAAU;QACrB,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YAInC,KAAK,EAAE;gBACL,IAAI,EAAE,KAAK;aACZ;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,+BAA+B,CAAC,WAAmB;QACvD,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YACpC,KAAK,EAAE,EAAE,WAAW,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,mBAAmB,CACvB,WAAmB,EACnB,aAAsB;QAEtB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YACnD,KAAK,EAAE,EAAE,WAAW,EAAE;SACvB,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,sCAAsC,WAAW,EAAE,CAAC,CAAC;QACvE,CAAC;QACD,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,GAAG,aAAa,EAAE,CAAC,CAAC;IAC3E,CAAC;CACF,CAAA;AAlQY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,4BAAS,CAAC,CAAA;IAE3B,WAAA,IAAA,0BAAgB,EAAC,wBAAO,CAAC,CAAA;IAEzB,WAAA,IAAA,0BAAgB,EAAC,qCAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,oBAAK,CAAC,CAAA;IAEvB,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;IAEtB,WAAA,IAAA,0BAAgB,EAAC,uCAAc,CAAC,CAAA;qCATK,oBAAU;QAEZ,oBAAU;QAEJ,oBAAU;QAE3B,oBAAU;QAEF,oBAAU;QAEA,oBAAU;QAC/B,4BAAY;GAdzB,gBAAgB,CAkQ5B"}