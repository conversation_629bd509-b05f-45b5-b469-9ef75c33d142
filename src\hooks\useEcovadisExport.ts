import { useMutation } from '@tanstack/react-query';
import { exportEcovadisQuestionnaire } from '@/api/ecovadis/ecovadis.api';
import { toast } from '@/components/ui/use-toast';

export function useEcovadisExport() {
  const exportMutation = useMutation({
    mutationFn: async ({ projectId, projectName }: { projectId: string; projectName?: string }) => {
      try {
        const blob = await exportEcovadisQuestionnaire(projectId);
        
        // Create a safe filename
        const dateString = new Date().toISOString().split('T')[0];
        const safeName = (projectName || 'ecovadis-questionnaire')
          .replace(/[^a-zA-Z0-9]/g, '_')
          .substring(0, 30);
        const filename = `EcoVadis_Questionnaire_${safeName}_${dateString}.xlsx`;
        
        // Create download link and trigger download
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        
        // Clean up
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        return { success: true };
      } catch (error) {
        console.error('Export error:', error);
        throw error;
      }
    },
    onSuccess: () => {
      toast({
        title: "Export Successful",
        description: "Your EcoVadis questionnaire was exported successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export questionnaire",
        variant: "destructive"
      });
    }
  });

  return {
    exportQuestionnaire: exportMutation.mutate,
    isExporting: exportMutation.isPending
  };
}
