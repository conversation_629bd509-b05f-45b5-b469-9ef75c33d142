import 'dotenv/config';
import type { DocumentChunkGenerated } from 'src/types';
import { LlmRateLimiterService } from 'src/llm-rate-limiter/llm-rate-limiter.service';
export declare class DocumentParserService {
    private readonly llmRateLimitService;
    constructor(llmRateLimitService: LlmRateLimiterService);
    private tokenizer;
    private readonly logger;
    private countTokens;
    private parseMarkdownTable;
    parseDocumentToMarkdown(path: string, premiumMode?: boolean): Promise<DocumentChunkGenerated[]>;
    private processCurrentChunk;
    private enhanceHtmlWithStyles;
    private analyzeHtmlTables;
    private joinLinesWithHtmlTables;
    parsePageBasedPDFToMarkdown(filePath: string, premiumMode?: boolean): Promise<DocumentChunkGenerated[]>;
    parsePDFDocumentToMarkdown(filePath: string, premiumMode?: boolean): Promise<DocumentChunkGenerated[]>;
    private createMarkdownTable;
    private escapeCell;
    private splitTableByTokenCount;
    private splitRowsRecursively;
    private detectHeaderRows;
    detectHeaderRowsWithLLM(tableData: any[][]): Promise<number>;
    private parseSpreadsheetToMarkdown;
    private normalizeSheetData;
    private extractRegionData;
    private isLikelyDate;
    private findEmptyRowsAndColsAdaptive;
    private identifyTableRegionsEnhanced;
    private applyHierarchicalClustering;
    private detectTablesInSheet;
    private detectTablesProgressively;
    private analyzeSheetStructure;
    private handleContinuousTable;
    private analyzeColumnStructure;
    private enforceColumnConsistency;
    private enforceGlobalConsistency;
    private regionsToTables;
    private calculateHorizontalOverlap;
    private calculateHeaderLikelihood;
    private calculateDataRowLikelihood;
    private calculateRowPatternSimilarity;
    private getCellDataType;
    private analyzeColumnTypeConsistency;
    private calculateDataDensity;
    private assessTableConfidenceAdaptive;
    private calculateOptimalHeaderCount;
    private detectHiddenRowsAndColumns;
    private normalizeSheetDataWithHiddenCells;
    private detectNativeTables;
    private detectTablesFromFormatting;
    private parseExcelReference;
    private extractNativeTable;
    private enhancedUnmergeCells;
    private postProcessMergedCells;
    private adjustTableForHiddenCells;
    private deduplicateTables;
}
