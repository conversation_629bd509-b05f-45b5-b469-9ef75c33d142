import { toast } from './ui/use-toast';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useDataRequestContext } from '@/context/dataRequestContext';

interface GenerateBulkDatapointsDialogProps {
  dataRequestId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onGenerate: () => Promise<void>;
  datapointCount: number;
  isGenerating: boolean;
  setIsGenerating: React.Dispatch<React.SetStateAction<boolean>>;
}

export function GenerateBulkDatapointsDialog({
  dataRequestId,
  open,
  onOpenChange,
  onGenerate,
  datapointCount,
  isGenerating,
  setIsGenerating,
}: GenerateBulkDatapointsDialogProps) {
  const { setupEventSource, closeEventSource } = useDataRequestContext();

  async function handleGenerateXDatapoint() {
    onOpenChange(false);
    try {
      setIsGenerating(true);
      await setupEventSource(dataRequestId);
      await onGenerate();
      toast({
        title: 'Datapoint queued for generation',
        description: `${datapointCount} Datapoints queued for Generation`,
        variant: 'default',
      });
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to generate datapoints',
        variant: 'destructive',
      });
      closeEventSource();
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Generate {datapointCount} Datapoints</DialogTitle>
          <DialogDescription>
            Generate multiple datapoints automatically. Caution: This can not be
            stopped, once started, so ensure, that all relevant documents are
            already uploaded. While a datapoint is waiting for generation, you
            cannot trigger another generation or review.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleGenerateXDatapoint} disabled={isGenerating}>
            Generate {datapointCount} Datapoints
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
