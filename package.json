{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:clean": "rm -rf dist && npm run build", "build:dev": "vite build --mode development", "preview": "vite preview", "postinstall": "npm rebuild @rollup/rollup-linux-x64-gnu || true"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/supabase-js": "^2.49.4", "@tanstack/eslint-plugin-query": "^5.68.0", "@tanstack/react-query": "^5.69.0", "@tanstack/react-query-devtools": "^5.69.0", "@tanstack/react-table": "^8.21.2", "@tiptap/core": "^2.11.5", "@tiptap/extension-bullet-list": "^2.11.5", "@tiptap/extension-character-count": "^2.11.5", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-heading": "^2.11.5", "@tiptap/extension-highlight": "^2.11.5", "@tiptap/extension-horizontal-rule": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-ordered-list": "^2.11.5", "@tiptap/extension-paragraph": "^2.11.5", "@tiptap/extension-subscript": "^2.11.5", "@tiptap/extension-superscript": "^2.11.5", "@tiptap/extension-table": "^2.11.5", "@tiptap/extension-table-cell": "^2.11.5", "@tiptap/extension-table-header": "^2.11.5", "@tiptap/extension-table-row": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/extension-typography": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/canvas-confetti": "^1.9.0", "@types/mixpanel-browser": "^2.49.1", "@types/react-scroll-to-bottom": "^4.2.5", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@uiw/react-md-editor": "^4.0.5", "@vitejs/plugin-react": "^4.3.4", "axios": "^1.8.4", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.5", "framer-motion": "^12.6.3", "input-otp": "^1.2.4", "js-cookie": "^3.0.5", "lucide-react": "^0.407.0", "markdown-to-jsx": "^7.7.4", "marked": "^14.1.4", "mermaid": "^11.6.0", "mixpanel-browser": "^2.53.0", "next-themes": "^0.3.0", "openai": "^4.89.0", "path-to-regexp": "^8.2.0", "prettier": "^3.5.3", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-hotjar": "^6.3.1", "react-markdown": "^10.1.0", "react-multi-email": "^1.0.25", "react-quill-new": "^3.4.6", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.30.0", "react-scroll-to-bottom": "^4.2.0", "react-textarea-autosize": "^8.5.8", "recharts": "^2.12.7", "socket.io-client": "^4.8.1", "sonner": "^1.5.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^20.17.27", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.21", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.2", "typescript-eslint": "^8.0.1", "vite": "^5.4.15"}}