{"version": "0.1.01", "devDependencies": {"husky": "^9.1.6", "prettier": "^3.3.3"}, "scripts": {"prepare": "husky", "prettier": "prettier './climate-assistant-backend/**/*.ts' --write && prettier './climate-assistant-frontend/**/*.{ts,tsx}' --write", "docker-up": "docker compose up -d backend frontend && docker compose restart nginx", "docker-build": "docker compose up -d --build backend frontend && docker compose restart nginx", "into-server": "ssh $(cat .env.local | grep SERVER_ADDRESS | cut -d '=' -f2)", "build:frontend": "cd climate-assistant-frontend && npm run build", "build:backend": "cd climate-assistant-backend && npm run build", "build": "npm run build:frontend && npm run build:backend"}, "dependencies": {"@azure-rest/ai-document-intelligence": "^1.1.0", "customerio-node": "^4.2.0", "react-multi-email": "^1.0.25"}}