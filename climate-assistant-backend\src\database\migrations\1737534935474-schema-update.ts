import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1737534935474 implements MigrationInterface {
  name = 'SchemaUpdate1737534935474';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_generation" ADD "evaluatorId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_generation" ADD "evaluatedAt" TIMESTAMP`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_generation" DROP COLUMN "evaluatedAt"`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_generation" DROP COLUMN "evaluatorId"`,
    );
  }
}
