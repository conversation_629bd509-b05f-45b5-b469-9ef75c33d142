import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1738173452082 implements MigrationInterface {
  name = 'SchemaUpdate1738173452082';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" DROP COLUMN "publicAccess"`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_request" DROP COLUMN "publicAccess"`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ADD "publicAccess" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_disclosure_requirement" ADD "publicAccess" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "esrs_disclosure_requirement" DROP COLUMN "publicAccess"`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" DROP COLUMN "publicAccess"`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_request" ADD "publicAccess" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" ADD "publicAccess" boolean NOT NULL DEFAULT false`,
    );
  }
}
