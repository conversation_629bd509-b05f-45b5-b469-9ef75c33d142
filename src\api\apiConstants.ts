const isDevelopment = process.env.NODE_ENV === 'development';
export const API_URL = 'http://localhost:3000';
// export const API_URL = 'https://prototypeapi.glacier.eco/api';

// All the keys used in the query
export const USER_QUERY_KEY = 'user';
export const CHAT_HISTORIES_QUERY_KEY = 'histories';

// Axios defaults for credentials
import axios from 'axios';
axios.defaults.withCredentials = true;

export const PROJECTS_QUERY_KEY = 'projects';
