
import { useState, useEffect } from 'react';
import { Progress } from '@/components/ui/progress';
import { Check, X, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface FileUploadProgressProps {
  file: File;
  onComplete: () => void;
  onCancel: () => void;
  skipErrorSimulation?: boolean;
}

export function FileUploadProgress({ file, onComplete, onCancel, skipErrorSimulation = false }: FileUploadProgressProps) {
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState<'uploading' | 'processing' | 'success'>('uploading');
  
  useEffect(() => {
    // Simulate file upload progress
    const uploadTimer = setInterval(() => {
      setProgress(prevProgress => {
        if (prevProgress >= 100) {
          clearInterval(uploadTimer);
          setStatus('processing');
          return 100;
        }
        
        // Randomly speed up or slow down to simulate real upload
        const increment = Math.random() * 15;
        return Math.min(prevProgress + increment, 100);
      });
    }, 300);
    
    return () => clearInterval(uploadTimer);
  }, []);
  
  // When upload completes, simulate processing for 2 seconds
  useEffect(() => {
    if (status === 'processing') {
      const processingTimer = setTimeout(() => {
        setStatus('success');
        onComplete();
      }, 2000);
      
      return () => clearTimeout(processingTimer);
    }
  }, [status, onComplete]);
  
  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <Check className="h-4 w-4 text-green-500" />;
      case 'processing':
        return <Clock className="h-4 w-4 text-amber-500" />;
      default:
        return null;
    }
  };
  
  const getStatusText = () => {
    switch (status) {
      case 'uploading':
        return `Uploading... ${Math.round(progress)}%`;
      case 'processing':
        return 'Processing document...';
      case 'success':
        return 'Upload complete';
    }
  };
  
  return (
    <div className="py-2">
      <div className="flex items-center justify-between mb-1.5">
        <div className="flex items-center gap-2">
          {getStatusIcon()}
          <span className="text-sm font-medium truncate max-w-[200px]" title={file.name}>
            {file.name}
          </span>
          <span className="text-xs text-gray-500">
            {(file.size / 1024 / 1024).toFixed(2)} MB
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          {status !== 'success' && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={onCancel}
            >
              <X className="h-3.5 w-3.5" />
            </Button>
          )}
        </div>
      </div>
      
      <Progress 
        value={status === 'processing' ? 100 : progress} 
        className={`h-1.5 ${
          status === 'processing' ? 'bg-amber-100' : 
          'bg-gray-100'
        }`}
        color={
          status === 'processing' ? 'bg-amber-500' : 
          status === 'success' ? 'bg-green-500' : 
          undefined
        }
      />
      
      <p className={`text-xs mt-1 ${
        status === 'processing' ? 'text-amber-500' : 
        status === 'success' ? 'text-green-500' : 
        'text-gray-500'
      }`}>
        {getStatusText()}
      </p>
      
      {status === 'processing' && (
        <p className="text-xs text-gray-500 mt-0.5">
          Document analysis may take up to 1 hour for large files
        </p>
      )}
    </div>
  );
}
