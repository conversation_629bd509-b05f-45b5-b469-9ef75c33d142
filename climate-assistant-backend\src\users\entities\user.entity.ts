import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  OneToOne,
  CreateDateColumn,
} from 'typeorm';
import { UserWorkspace } from './user-workspace.entity';
import { Token } from './token.entity';
import { ChatHistory } from '../../chat/entities/chat-history.entity';
import { UserPromptContext } from './user-prompt-context.entity';
import { DataRequest } from '../../data-request/entities/data-request.entity';
import { Comment } from '../../project/entities/comment.entity';
import { Document } from '../../document/entities/document.entity';
import { DatapointGeneration } from '../../datapoint/entities/datapoint-generation.entity';

@Entity()
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  name: string;

  @Column({ type: 'varchar', length: 100 })
  email: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  auth_id: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  password: string;

  @CreateDateColumn()
  createdAt: Date;

  @OneToMany(() => UserWorkspace, (userWorkspace) => userWorkspace.user)
  userWorkspaces: UserWorkspace[];

  @OneToMany(() => Token, (token) => token.user)
  tokens: Token[];

  @OneToMany(() => ChatHistory, (chatHistory) => chatHistory.user)
  chatHistories: ChatHistory[];

  @OneToOne(
    () => UserPromptContext,
    (userPromptContext) => userPromptContext.user
  )
  userPromptContext: UserPromptContext;

  @OneToMany(() => DataRequest, (dataRequest) => dataRequest.responsiblePerson)
  dataRequests: DataRequest[];

  @OneToMany(() => Comment, (comment) => comment.user)
  comments: Comment[];

  @OneToMany(
    () => DatapointGeneration,
    (datapointGeneration) => datapointGeneration.evaluator
  )
  datapointGenerations: DatapointGeneration[];

  @OneToMany(() => Document, (document) => document.creator)
  documents: Document[];
}
