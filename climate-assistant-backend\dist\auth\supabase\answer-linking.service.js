"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EnhancedEcoVadisAnswerAgentService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedEcoVadisAnswerAgentService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const supabase_js_1 = require("@supabase/supabase-js");
const openai_1 = require("openai");
const genai_1 = require("@google/genai");
const constants_1 = require("../../constants");
const pinecone_1 = require("@pinecone-database/pinecone");
const chat_gpt_service_1 = require("../../llm/chat-gpt.service");
const cohere_service_1 = require("../../llm/cohere.service");
const common_util_1 = require("../../util/common-util");
let EnhancedEcoVadisAnswerAgentService = EnhancedEcoVadisAnswerAgentService_1 = class EnhancedEcoVadisAnswerAgentService {
    constructor(configService, chatGptService, cohereService) {
        this.configService = configService;
        this.chatGptService = chatGptService;
        this.cohereService = cohereService;
        this.logger = new common_1.Logger(EnhancedEcoVadisAnswerAgentService_1.name);
        this.openAiResponseSchema = {
            type: 'object',
            description: 'Single JSON response containing evidence-backed answers for EcoVadis questions',
            properties: {
                answers: {
                    type: 'array',
                    description: 'List of answer options, each with evidence document chunks and per-link comments. Must contain at least one answer if evidence exists.',
                    minItems: 0,
                    maxItems: 20,
                    items: {
                        type: 'object',
                        properties: {
                            answer_option: {
                                type: 'object',
                                description: 'Identifier and text of the answer option',
                                properties: {
                                    id: {
                                        type: 'string',
                                        description: 'Identifier UUID of the answer option',
                                        pattern: '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$',
                                    },
                                    answer_name: {
                                        type: 'string',
                                        description: 'Text of the answer option',
                                        minLength: 1,
                                        maxLength: 200,
                                    },
                                },
                                required: ['id', 'answer_name'],
                                additionalProperties: false,
                            },
                            document_chunks: {
                                type: 'array',
                                description: 'List of document chunks containing the evidence for this option with a brief comment (empty if none).',
                                minItems: 0,
                                maxItems: 10,
                                items: {
                                    type: 'object',
                                    properties: {
                                        document_chunk_id: {
                                            type: 'string',
                                            description: 'Identifier UUID of the document chunk',
                                            pattern: '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$',
                                        },
                                        comment: {
                                            type: 'string',
                                            description: 'Clarifying comment or explanation about why this specific chunk is evidence. Must properly escape quotes using backslash (\\") and avoid control characters.',
                                            minLength: 1,
                                            maxLength: 1000,
                                        },
                                    },
                                    required: ['document_chunk_id', 'comment'],
                                    additionalProperties: false,
                                },
                            },
                        },
                        required: ['answer_option', 'document_chunks'],
                        additionalProperties: false,
                    },
                },
            },
            required: ['answers'],
            additionalProperties: false,
        };
        this.tools = [
            {
                type: 'function',
                function: {
                    name: 'search_vectorstore',
                    description: "Search the Pinecone vector knowledge base for relevant information about sustainability practices, ESG requirements, and EcoVadis criteria within the user's workspace. The results are going to be reranked by the cohere reranker before you see them.",
                    parameters: {
                        type: 'object',
                        properties: {
                            queries: {
                                type: 'array',
                                items: { type: 'string' },
                                description: 'Array of search queries to find relevant information in the knowledge base. You can provide 1-5 queries to cover different aspects of the question. Results will be deduplicated and reranked.',
                                minItems: 1,
                                maxItems: 5,
                            },
                        },
                        required: ['queries'],
                    },
                },
            },
            {
                type: 'function',
                function: {
                    name: 'get_document_pages',
                    description: 'Retrieve all chunk texts for the given document and specific page numbers. Use this when you know the document ID and want to fetch specific pages.',
                    parameters: {
                        type: 'object',
                        properties: {
                            documentId: {
                                type: 'string',
                                description: 'Document UUID to fetch pages from',
                            },
                            pages: {
                                type: 'array',
                                items: { type: 'integer' },
                                description: 'Array of page numbers to fetch (e.g., [1, 2, 3])',
                            },
                            topKPerPage: {
                                type: 'integer',
                                description: 'Optional limit of chunks per page (default: 5)',
                            },
                        },
                        required: ['documentId', 'pages'],
                    },
                },
            },
        ];
        const azureResource = (process.env.AZURE_RESOURCE || '').replace(/\/$/, '');
        const azureApiKey = process.env.AZURE_OPENAI_API_KEY;
        if (!azureResource || !azureApiKey) {
            throw new Error('Azure OpenAI is not configured. Please set AZURE_RESOURCE and AZURE_OPENAI_API_KEY');
        }
        this.openAiClient = new openai_1.default({
            apiKey: azureApiKey,
            baseURL: process.env.HELICONE_AUTH_API_KEY
                ? 'https://oai.helicone.ai/openai/v1/'
                : `${azureResource}/openai/v1/`,
            defaultQuery: { 'api-version': 'preview' },
            defaultHeaders: {
                'api-key': azureApiKey,
                ...(process.env.HELICONE_AUTH_API_KEY && {
                    'Helicone-Auth': `Bearer ${process.env.HELICONE_AUTH_API_KEY}`,
                    'Helicone-OpenAI-API-Base': azureResource,
                }),
            },
        });
        if (!process.env.GEMINI_API_KEY) {
            throw new Error('GEMINI_API_KEY missing');
        }
        if (!process.env.HELICONE_AUTH_API_KEY) {
            this.logger.warn('HELICONE_AUTH_API_KEY not found, Helicone logging will be disabled');
        }
        this.geminiClient = new genai_1.GoogleGenAI({
            apiKey: process.env.GEMINI_API_KEY,
            ...(process.env.HELICONE_AUTH_API_KEY && {
                httpOptions: {
                    baseUrl: 'https://gateway.helicone.ai',
                    headers: {
                        'Helicone-Auth': `Bearer ${process.env.HELICONE_AUTH_API_KEY}`,
                        'Helicone-Target-URL': 'https://generativelanguage.googleapis.com',
                    },
                },
            }),
        });
        this.supabase = (0, supabase_js_1.createClient)(this.configService.get('SUPABASE_APP_URL'), this.configService.get('SUPABASE_SERVICE_KEY'));
        this.initializePinecone();
    }
    async initializePinecone() {
        try {
            this.pineconeClient = new pinecone_1.Pinecone({
                apiKey: process.env.PINECONE_API_KEY,
            });
            this.logger.log('Pinecone client initialized successfully');
        }
        catch (error) {
            this.logger.error('Failed to initialize Pinecone client:', error);
        }
    }
    generateSystemPrompt(question, themeCriteria, sustainabilityIssues, indicatorCriteria, scoringFramework, answerOptions) {
        return `You are an EcoVadis sustainability reporting assistant that helps answer ecovadis questions to fill out the documentation. Your task is to answer Ecovadis questions for a company using documents uploaded by the reporting company. The goal here is to find the most relevant information to answer the question, not just any. In order to answer questions, use the two tools provided to search the Pinecone vector database deeply over a couple of iterations and retrieve specific pages from documents in the end to find details. First task is to find relevant data to Ecovadis questions for the company in their Pinecone vector database, finding as many relevant documents as possible over a few tool call iterations. Then you looks for specific pages from documents that you found might have more relevant information. Finally return the final answer as a json adding as much evidence as reasonably possible to every single answer option.


    1. Use your search_vectorstore tool to research the knowledge base and find relevant evidence. Use usually at least one query per answer option. Run the tool at least 3 consecutive iterations after having retrieved and considered the results of a previous iterations to get a broad overview of available documents but at most 6-8 iterations. Often you will not find the most relevant pieces of information in the first tool call, so you need to iterate over the vector db several times.  Search Strategy:
    - Use declarative search terms (not questions) that match embedded text
    - Search for at least one query per answer option
    - Iterate 3+ times to ensure comprehensive coverage
    - More evidence links are better than fewer
    2. Use your get_document_pages tool to retrieve specific pages from documents when you know the document ID from previous similarity searches to see more pages you have not gotten access to via the vector db yet. Use this where applicable, but don't overdo it.
    3. Iterate over multiple searches to gather comprehensive information. This means not just multiple queries per tool call but also multiple iterations after having seen the results of the previous tool call. Generally speaking, more links are better.
    4. Answers are primarily links to document pages where the evidence is found. Generate accurate, validated links to the most important pages, sorted by relevance with proper citations. For every link you provide, include a short comment, but never include document IDs as they are not useful for users. Make sure to not hallucinate anything in the comments.
    5. Provide confidence levels based on the quality and quantity of evidence found
    6. Respond to all answer options in the answer options set if possible. Better strong evidence, but better weaker evidence than no evidence at all.
    
    Each question in Ecovadis has multiple answer options, each of which requires as an answer a set of evidence documents that contain the information on the company's actions/policies etc. Crucially, the linked document pages themselves are the main answer. A question can have multiple answer options, so you can identify and map to each of them one or several document chunks to respective answer options. All relevant documents the company has are indexed in a Pinecone vector database. Reason first how to phrase the sentence or paragraph effectively that you send to the vector database i.e. that the sentence or paragraph should be similar to the embedded text rather than a question and be broad enough to cover all potential answer options/topics. You should iterate over the db to get a good overview of available documents, but limit the number of iterations to max ~6 (i.e. at least 2-3 max 6 iterations of one or more queries at once). Cohere reranker will rerank the results before you see them. Besides the similarity search, you can also use the get_document_pages tool to retrieve specific pages from documents when you know the document ID from previous similarity searches. Write a json containing the required fields of the answer i.e. a list of document chunks (each chunk representing a page from a document) where the evidence is. Only do so if there are documents that contain correct answers, otherwise return that there are no answers provided. In the array of answers you only need to provide ids of answers which have valid evidence linkable to them.

    When you have finished gathering all necessary information through tool calls, return your final response as a JSON object following the defined schema.

    Below is the issue that we need to provide evidence for, the criteria and the scoring framework. Use those to contemplate which output to create.

    Guidelines:
    - DO NOT RETURN THE JSON OBJECT DUPLICATED. It sometimes happens that you return the same output double. Only return the json once, obviously.
    - Always search the knowledge base before answering. Search longer and deeper to find more relevant content. Empirically speaking, you usually miss a lot of relevant content, so try harder and err towards too much content rather than too little. More links are better.
    - Use search_vectorstore for similarity-based searches when you need to find relevant content
    - Use get_document_pages when you have identified specific documents and want to look at particular pages
    - Use multiple search queries with different keywords
    - search until you either have a comprehensive answer or are confident that there is no more relevant information to be found
    - Cite your sources by referencing the evidence found
    - Indicate your confidence level: high (strong evidence), medium (some evidence), or low (limited evidence)
    - Be concise but comprehensive in your responses
    - Focus on actionable insights and practical guidance. Provide a comment for every link you include.
    - Order the document chunks in the response json by relevance to the question starting with the most relevant one

    **Question**
    ${question}

    **Theme**
    ${themeCriteria || 'Not provided'}

    **Sustainability Issue**
    ${sustainabilityIssues || 'Not provided'}

    **Indicator Criteria**
    ${indicatorCriteria || 'Not provided'}

    **Scoring Framework**
    ${scoringFramework || 'Not provided'}

    **Answer Options** (optional, if we have them)
    ${answerOptions || 'Not provided'}

    Return your final answer as a JSON object with the specified schema. Ensure all quotes in comments are escaped with backslash (\\") and generate no duplicate responses.`;
    }
    async answerEcoVadisQuestion({ projectId, questionId, }) {
        const useOpenAI = process.env.USE_OPENAI === 'true' || !process.env.GEMINI_API_KEY;
        if (useOpenAI) {
            this.logger.log(`EcoVadis answer generation started: Azure OpenAI (project: ${projectId}, question: ${questionId})`);
            return this.answerEcoVadisQuestionWithOpenAI({ projectId, questionId });
        }
        else {
            this.logger.log(`EcoVadis answer generation started: Gemini (project: ${projectId}, question: ${questionId})`);
            return this.answerEcoVadisQuestionWithGemini({ projectId, questionId });
        }
    }
    async chatWithModel(client, model, messages, tools) {
        const isGemini = model.includes('gemini');
        if (isGemini) {
            const contents = messages.map(message => {
                let geminiRole;
                if (message.role === 'assistant') {
                    geminiRole = 'model';
                }
                else {
                    geminiRole = 'user';
                }
                return {
                    role: geminiRole,
                    parts: [{ text: String(message.content || '') }]
                };
            });
            const result = await client.models.generateContent({
                model,
                contents,
                config: {
                    tools: [{
                            functionDeclarations: [
                                {
                                    name: 'search_vectorstore',
                                    description: "Search the Pinecone vector knowledge base for relevant information about sustainability practices, ESG requirements, and EcoVadis criteria within the user's workspace",
                                    parameters: {
                                        type: genai_1.Type.OBJECT,
                                        properties: {
                                            queries: {
                                                type: genai_1.Type.ARRAY,
                                                items: { type: genai_1.Type.STRING },
                                                description: 'Array of search queries to find relevant information in the knowledge base. You can provide 1-5 queries to cover different aspects of the question. Results will be deduplicated and reranked.',
                                            },
                                        },
                                        required: ['queries'],
                                    },
                                },
                                {
                                    name: 'get_document_pages',
                                    description: 'Retrieve all chunk texts for the given document and specific page numbers. Use this when you know the document ID and want to fetch specific pages.',
                                    parameters: {
                                        type: genai_1.Type.OBJECT,
                                        properties: {
                                            documentId: {
                                                type: genai_1.Type.STRING,
                                                description: 'Document UUID to fetch pages from',
                                            },
                                            pages: {
                                                type: genai_1.Type.ARRAY,
                                                items: { type: genai_1.Type.NUMBER },
                                                description: 'Array of page numbers to fetch (e.g., [1, 2, 3])',
                                            },
                                            topKPerPage: {
                                                type: genai_1.Type.NUMBER,
                                                description: 'Optional limit of chunks per page (default: 5)',
                                            },
                                        },
                                        required: ['documentId', 'pages'],
                                    },
                                }
                            ]
                        }],
                    toolConfig: {
                        functionCallingConfig: {
                            mode: genai_1.FunctionCallingConfigMode.AUTO
                        }
                    },
                    temperature: 0
                }
            });
            const functionCalls = result.functionCalls ??
                result.candidates?.flatMap(c => c.content.parts
                    .filter(p => 'functionCall' in p)
                    .map(p => p.functionCall));
            if (functionCalls && functionCalls.length > 0) {
                const toolCalls = functionCalls.map((fc, index) => ({
                    id: `call_${index}`,
                    type: 'function',
                    function: {
                        name: fc.name,
                        arguments: JSON.stringify(fc.args)
                    }
                }));
                return {
                    role: 'assistant',
                    content: result.text || null,
                    tool_calls: toolCalls,
                };
            }
            return {
                role: 'assistant',
                content: result.text,
                tool_calls: undefined,
            };
        }
        else {
            try {
                this.logger.log('Azure OpenAI request: function calling phase');
                const completion = await client.chat.completions.create({
                    model: 'gpt-5-stefan',
                    messages,
                    tools,
                    tool_choice: 'auto',
                    verbosity: 'low',
                });
                this.logger.log('Azure OpenAI success: function calling phase');
                const message = completion.choices[0]?.message;
                if (!message) {
                    throw new Error('No message returned from OpenAI');
                }
                return {
                    role: 'assistant',
                    content: message.content,
                    tool_calls: message.tool_calls,
                };
            }
            catch (error) {
                this.logger.error(`Azure OpenAI failed: function calling phase - ${error.message}`);
                throw error;
            }
        }
    }
    async chatWithModelFinalResponse(client, model, messages) {
        const isGemini = model.includes('gemini');
        if (isGemini) {
            const contents = messages.map(message => {
                let geminiRole;
                if (message.role === 'assistant') {
                    geminiRole = 'model';
                }
                else {
                    geminiRole = 'user';
                }
                return {
                    role: geminiRole,
                    parts: [{ text: String(message.content || '') }]
                };
            });
            const result = await client.models.generateContent({
                model,
                contents,
                config: {
                    responseMimeType: 'application/json',
                    responseSchema: this.openAiResponseSchema,
                    temperature: 0
                }
            });
            return {
                role: 'assistant',
                content: result.text,
                tool_calls: undefined,
            };
        }
        else {
            try {
                this.logger.log('Azure OpenAI request: JSON response phase');
                const completion = await client.chat.completions.create({
                    model: 'gpt-5-stefan',
                    messages,
                    response_format: {
                        type: 'json_schema',
                        json_schema: {
                            name: 'ecovadis_answer_schema',
                            schema: this.openAiResponseSchema,
                            strict: true,
                        },
                    },
                    verbosity: 'low',
                });
                this.logger.log('Azure OpenAI success: JSON response phase');
                const message = completion.choices[0]?.message;
                if (!message) {
                    throw new Error('No message returned from OpenAI');
                }
                return {
                    role: 'assistant',
                    content: message.content,
                    tool_calls: undefined,
                };
            }
            catch (error) {
                this.logger.error(`Azure OpenAI failed: JSON response phase - ${error.message}`);
                throw error;
            }
        }
    }
    convertMessagesToGeminiPrompt(messages) {
        return messages
            .map((message) => {
            if (message.role === 'system') {
                return `System: ${message.content}`;
            }
            else if (message.role === 'user') {
                return `User: ${message.content}`;
            }
            else if (message.role === 'assistant') {
                return `Assistant: ${message.content}`;
            }
            else if (message.role === 'tool') {
                return `Tool Response: ${message.content}`;
            }
            return '';
        })
            .filter(Boolean)
            .join('\n\n');
    }
    async handleToolCallsGeneric(client, model, messages, toolCalls, workspaceId, projectId, options, seenChunks) {
        for (const toolCall of toolCalls) {
            if (toolCall.function.name === 'search_vectorstore') {
                const args = JSON.parse(toolCall.function.arguments);
                const searchResult = await this.searchVectorStoreMultiple(args.queries, args.limit, workspaceId, seenChunks);
                messages.push({
                    role: 'tool',
                    tool_call_id: toolCall.id,
                    content: JSON.stringify(searchResult),
                });
            }
            else if (toolCall.function.name === 'get_document_pages') {
                const args = JSON.parse(toolCall.function.arguments);
                const documentPages = await this.getDocumentPages(args.documentId, args.pages, args.topKPerPage, workspaceId);
                messages.push({
                    role: 'tool',
                    tool_call_id: toolCall.id,
                    content: JSON.stringify(documentPages),
                });
            }
        }
        const finalMessage = await this.chatWithModelFinalResponse(client, model, messages);
        messages.push(finalMessage);
        if (finalMessage.tool_calls && finalMessage.tool_calls.length > 0) {
            return await this.handleToolCallsGeneric(client, model, messages, finalMessage.tool_calls, workspaceId, projectId, options, seenChunks);
        }
        const validOptionIds = options?.map((option) => option.id) || [];
        const result = await this.parseAgentResponse(finalMessage.content || '', messages, projectId || '', validOptionIds, workspaceId || '');
        this.logger.log(`EcoVadis tool-based answer generation completed (project: ${projectId})`);
        return result;
    }
    async answerEcoVadisQuestionGeneric({ projectId, questionId, client, model, vendorName, }) {
        this.logger.log(`Answering EcoVadis question with ${vendorName} for project ${projectId}, question ${questionId}`);
        try {
            const workspaceId = await this.getWorkspaceIdFromProject(projectId);
            this.logger.log(`Processing question for workspace: ${workspaceId}`);
            const { projectQuestion, theme, options, scoringFramework, scoringFrameworkDrivers, sustainabilityIssues, } = await this.fetchEcoVadisData(projectId, questionId);
            const { question, themeCriteria, indicatorCriteria, answerOptions, formattedScoringFramework, } = this.formatDataForPrompt(projectQuestion, theme, options, scoringFramework, scoringFrameworkDrivers, sustainabilityIssues);
            const messages = [
                {
                    role: 'system',
                    content: this.generateSystemPrompt(question, themeCriteria, sustainabilityIssues, indicatorCriteria, formattedScoringFramework, answerOptions),
                },
                { role: 'user', content: question },
            ];
            const isGemini = model.includes('gemini');
            const message = await this.chatWithModel(client, model, messages, this.tools);
            messages.push(message);
            if (message.tool_calls && message.tool_calls.length > 0) {
                const seenChunks = {
                    chunkIds: new Set(),
                    chunkTexts: new Set(),
                };
                return await this.handleToolCallsGeneric(client, model, messages, message.tool_calls, workspaceId, projectId, options, seenChunks);
            }
            const validOptionIds = options.map((option) => option.id);
            const result = await this.parseAgentResponse(message.content || '', messages, projectId, validOptionIds, workspaceId);
            this.logger.log(`EcoVadis answer generation completed: ${vendorName} (project: ${projectId})`);
            return result;
        }
        catch (error) {
            this.logger.error(`EcoVadis answer generation failed: ${vendorName} (project: ${projectId}) - ${error.message}`);
            throw new Error(`Failed to answer EcoVadis question with ${vendorName}: ${error.message}`);
        }
    }
    async answerEcoVadisQuestionWithOpenAI({ projectId, questionId, }) {
        this.logger.log(`EcoVadis answer generation started: Azure OpenAI (project: ${projectId}, question: ${questionId})`);
        try {
            const workspaceId = await this.getWorkspaceIdFromProject(projectId);
            const { projectQuestion, theme, options, scoringFramework, scoringFrameworkDrivers, sustainabilityIssues, } = await this.fetchEcoVadisData(projectId, questionId);
            const { question, themeCriteria, indicatorCriteria, answerOptions, formattedScoringFramework, } = this.formatDataForPrompt(projectQuestion, theme, options, scoringFramework, scoringFrameworkDrivers, sustainabilityIssues);
            const messages = [
                {
                    role: 'system',
                    content: this.generateSystemPrompt(question, themeCriteria, sustainabilityIssues, indicatorCriteria, formattedScoringFramework, answerOptions),
                },
                { role: 'user', content: question },
            ];
            const seenChunks = {
                chunkIds: new Set(),
                chunkTexts: new Set(),
            };
            const maxIterations = 8;
            let iterations = 0;
            while (iterations < maxIterations) {
                iterations++;
                const response = await this.openAiClient.chat.completions.create({
                    model: 'gpt-5-stefan',
                    messages,
                    tools: this.tools,
                    tool_choice: 'auto',
                    verbosity: 'low',
                });
                const message = response.choices[0]?.message;
                if (!message) {
                    throw new Error('No message returned from OpenAI');
                }
                messages.push(message);
                const toolCalls = message.tool_calls || [];
                if (toolCalls.length === 0) {
                    break;
                }
                for (const toolCall of toolCalls) {
                    const args = JSON.parse(toolCall.function.arguments || '{}');
                    let toolOutput;
                    if (toolCall.function.name === 'search_vectorstore') {
                        toolOutput = await this.searchVectorStoreMultiple(args.queries, args.limit || 10, workspaceId, seenChunks);
                    }
                    else if (toolCall.function.name === 'get_document_pages') {
                        toolOutput = await this.getDocumentPages(args.documentId, args.pages, args.topKPerPage || 5, workspaceId);
                    }
                    messages.push({
                        role: 'tool',
                        tool_call_id: toolCall.id,
                        content: JSON.stringify(toolOutput),
                    });
                }
            }
            if (iterations >= maxIterations) {
                this.logger.warn('Max iterations reached, proceeding to final response');
            }
            const finalResponse = await this.openAiClient.chat.completions.create({
                model: 'gpt-5-stefan',
                messages,
                response_format: {
                    type: 'json_schema',
                    json_schema: {
                        name: 'ecovadis_answer',
                        schema: this.openAiResponseSchema,
                        strict: true,
                    },
                },
                verbosity: 'low',
            });
            const finalMessage = finalResponse.choices[0]?.message;
            if (!finalMessage || !finalMessage.content) {
                throw new Error('No final response from model');
            }
            const parsed = JSON.parse(finalMessage.content);
            const validOptionIds = options.map(o => o.id);
            await this.saveAiAnswerResults(projectId, finalMessage.content, validOptionIds, workspaceId);
            return {
                answer: parsed,
                evidence_sources: [],
                confidence_level: 'high',
                conversationHistory: messages,
            };
        }
        catch (error) {
            this.logger.error(`Failed to answer EcoVadis question: ${error.message}`);
            throw error;
        }
    }
    async answerEcoVadisQuestionWithGemini({ projectId, questionId, }) {
        const client = this.geminiClient;
        const model = constants_1.LLM_MODELS['gemini-2.5-pro'];
        const vendorName = 'Gemini 2.5 Pro';
        return this.answerEcoVadisQuestionGeneric({
            projectId,
            questionId,
            client,
            model,
            vendorName,
        });
    }
    async searchVectorStoreMultiple(queries, limit = 10, workspaceId, seenChunks) {
        if (!queries || queries.length === 0) {
            this.logger.warn('No queries provided to searchVectorStoreMultiple');
            return [];
        }
        if (!workspaceId) {
            throw new Error('workspaceId is required for workspace isolation');
        }
        const limitedQueries = queries.slice(0, 5);
        this.logger.log(`Hybrid search with ${limitedQueries.length} queries: ${limitedQueries.join(', ')}`);
        const vectorSearchTasks = limitedQueries.map((query, index) => this.searchVectorStore(query, 50, workspaceId).then(results => results.map(result => ({
            ...result,
            metadata: {
                ...result.metadata,
                source_query_index: index,
                source_query: query,
            }
        }))));
        const keywordSearchTask = this.searchKeywords(limitedQueries, 20, workspaceId).then(results => results.map(result => ({
            ...result,
            metadata: {
                ...result.metadata,
                source_query_index: -1,
                source_query: 'keyword_search',
            }
        })));
        const [vectorResults, keywordResults] = await Promise.all([
            Promise.all(vectorSearchTasks).then((results) => results.flat()),
            keywordSearchTask,
        ]);
        const allResults = [...vectorResults, ...keywordResults];
        this.logger.log(`Hybrid search yielded ${vectorResults.length} vector + ${keywordResults.length} keyword = ${allResults.length} total results`);
        const deduplicatedResults = this.deduplicateSearchResults(allResults, seenChunks);
        if (deduplicatedResults.length === 0) {
            this.logger.warn('No results after deduplication');
            return [];
        }
        return await this.performBalancedPerQueryReranking(deduplicatedResults, limitedQueries, limit);
    }
    async performBalancedPerQueryReranking(deduplicatedResults, queries, totalLimit) {
        const resultsByQuery = new Map();
        queries.forEach((_, index) => {
            resultsByQuery.set(index, []);
        });
        resultsByQuery.set(-1, []);
        deduplicatedResults.forEach(result => {
            const queryIndex = result.metadata.source_query_index ?? -1;
            const group = resultsByQuery.get(queryIndex) || [];
            group.push(result);
            resultsByQuery.set(queryIndex, group);
        });
        const activeGroups = Array.from(resultsByQuery.entries()).filter(([_, results]) => results.length > 0);
        const numActiveGroups = activeGroups.length;
        if (numActiveGroups === 0) {
            this.logger.warn('No active query groups found');
            return [];
        }
        const baseAllocationPerGroup = Math.floor(totalLimit / numActiveGroups);
        const remainingSlots = totalLimit % numActiveGroups;
        this.logger.log(`Performing balanced reranking: ${numActiveGroups} active groups, ${baseAllocationPerGroup} base allocation per group, ${remainingSlots} remaining slots`);
        const rerankedGroups = [];
        for (let i = 0; i < activeGroups.length; i++) {
            const [queryIndex, groupResults] = activeGroups[i];
            const groupAllocation = baseAllocationPerGroup + (i < remainingSlots ? 1 : 0);
            if (groupResults.length === 0)
                continue;
            let rerankedGroupResults;
            if (queryIndex === -1) {
                rerankedGroupResults = groupResults
                    .sort((a, b) => b.relevance_score - a.relevance_score)
                    .slice(0, groupAllocation)
                    .map(result => ({
                    ...result,
                    metadata: {
                        ...result.metadata,
                        reranked: false,
                        allocation_group: 'keyword_search',
                        group_allocation: groupAllocation,
                    }
                }));
            }
            else {
                const query = queries[queryIndex];
                try {
                    const reranked = await this.cohereService.rerankResults(query, groupResults.map(r => ({
                        content: r.content,
                        metadata: r.metadata,
                    })), groupAllocation);
                    rerankedGroupResults = reranked.map(result => ({
                        content: result.content,
                        relevance_score: result.relevance_score,
                        metadata: {
                            ...result.metadata,
                            reranked: true,
                            rerank_query: query,
                            allocation_group: `query_${queryIndex}`,
                            group_allocation: groupAllocation,
                            original_index: result.original_index,
                        }
                    }));
                }
                catch (error) {
                    this.logger.warn(`Failed to rerank group ${queryIndex} with query "${query}": ${error.message}`);
                    rerankedGroupResults = groupResults
                        .sort((a, b) => b.relevance_score - a.relevance_score)
                        .slice(0, groupAllocation)
                        .map(result => ({
                        ...result,
                        metadata: {
                            ...result.metadata,
                            reranked: false,
                            rerank_error: error.message,
                            allocation_group: `query_${queryIndex}_fallback`,
                            group_allocation: groupAllocation,
                        }
                    }));
                }
            }
            rerankedGroups.push(...rerankedGroupResults);
            this.logger.log(`Group ${queryIndex} (${queryIndex === -1 ? 'keyword' : `query: "${queries[queryIndex]}"`}): ${groupResults.length} → ${rerankedGroupResults.length} results`);
        }
        const finalResults = rerankedGroups
            .sort((a, b) => b.relevance_score - a.relevance_score)
            .slice(0, totalLimit);
        this.logger.log(`Balanced per-query reranking complete: ${deduplicatedResults.length} → ${finalResults.length} results`);
        return finalResults;
    }
    deduplicateSearchResults(results, seenChunks) {
        const deduplicatedResults = [];
        const currentIterationChunkIds = new Set();
        const currentIterationChunkTexts = new Set();
        for (const result of results) {
            const chunkId = result.metadata?.chunk_id;
            const contentHash = this.hashContent(result.content);
            if (seenChunks?.chunkIds.has(chunkId)) {
                continue;
            }
            if (seenChunks?.chunkTexts.has(contentHash)) {
                continue;
            }
            if (currentIterationChunkIds.has(chunkId) ||
                currentIterationChunkTexts.has(contentHash)) {
                continue;
            }
            if (chunkId) {
                currentIterationChunkIds.add(chunkId);
                seenChunks?.chunkIds.add(chunkId);
            }
            currentIterationChunkTexts.add(contentHash);
            seenChunks?.chunkTexts.add(contentHash);
            deduplicatedResults.push(result);
        }
        this.logger.log(`Deduplication: ${results.length} -> ${deduplicatedResults.length} results`);
        return deduplicatedResults;
    }
    hashContent(content) {
        const normalized = content.trim().replace(/\s+/g, ' ').toLowerCase();
        return normalized.substring(0, 200);
    }
    async searchKeywords(queries, limit = 20, workspaceId) {
        if (!queries || queries.length === 0) {
            return [];
        }
        if (!workspaceId) {
            throw new Error('workspaceId is required for workspace isolation');
        }
        try {
            const allKeywords = queries
                .flatMap(query => query.toLowerCase()
                .replace(/[^\w\s]/g, ' ')
                .split(/\s+/)
                .filter(word => word.length > 3))
                .filter((word, index, arr) => arr.indexOf(word) === index);
            if (allKeywords.length === 0) {
                return [];
            }
            this.logger.log(`Keyword search with terms: ${allKeywords.slice(0, 5).join(', ')}${allKeywords.length > 5 ? '...' : ''}`);
            let query = this.supabase.from('document_chunk').select(`
          id,
          content,
          page,
          documentId,
          document:documentId (
            name,
            workspaceId,
            year,
            documentType
          )
        `);
            query = query.eq('document.workspaceId', workspaceId);
            const searchTerms = allKeywords.slice(0, 10).join(' | ');
            query = query.textSearch('content', searchTerms);
            query = query.limit(limit);
            const { data: chunks, error } = await query;
            if (error) {
                this.logger.error('Keyword search error:', error);
                return [];
            }
            if (!chunks || chunks.length === 0) {
                this.logger.log('No keyword search results found');
                return [];
            }
            const results = chunks.map((chunk) => {
                const content = chunk.content || '';
                const contentLower = content.toLowerCase();
                const matches = allKeywords.filter((keyword) => contentLower.includes(keyword)).length;
                const relevanceScore = Math.min(0.8, matches / allKeywords.length);
                const document = Array.isArray(chunk.document)
                    ? chunk.document[0]
                    : chunk.document;
                return {
                    content,
                    relevance_score: relevanceScore,
                    metadata: {
                        source: 'keyword_search',
                        title: document?.name || 'Unknown Document',
                        chunk_id: chunk.id,
                        document_id: chunk.documentId,
                        page: chunk.page,
                        workspace_id: document?.workspaceId,
                        year: document?.year,
                        document_type: document?.documentType,
                        keyword_matches: matches,
                        total_keywords: allKeywords.length,
                        search_type: 'keyword',
                        timestamp: new Date().toISOString(),
                    },
                };
            });
            this.logger.log(`Keyword search returned ${results.length} results`);
            return results;
        }
        catch (error) {
            this.logger.error('Error in keyword search:', error);
            return [];
        }
    }
    async searchVectorStore(query, limit = 7, workspaceId) {
        this.logger.log(`Searching Pinecone knowledge base for: ${query} (workspace: ${workspaceId})`);
        if (!workspaceId) {
            throw new Error('workspaceId is required for workspace isolation');
        }
        if (!this.pineconeClient) {
            this.logger.error('Pinecone client not initialized');
            throw new Error('Pinecone client not initialized. Please check PINECONE_API_KEY environment variable.');
        }
        try {
            const indexName = process.env.PINECONE_INDEX_NAME;
            const indexHost = process.env.PINECONE_INDEX_HOST;
            const namespace = process.env.PINECONE_NAMESPACE;
            if (!indexHost) {
                throw new Error('PINECONE_INDEX_HOST environment variable is required');
            }
            this.logger.log('Generating embedding for search query...');
            const queryVector = await this.chatGptService.createEmbedding(query);
            this.logger.log(`Generated embedding with ${queryVector.length} dimensions`);
            const index = this.pineconeClient.index(indexName, indexHost);
            const namespaceIndex = index.namespace(namespace);
            const queryParams = {
                vector: queryVector,
                topK: limit,
                includeValues: false,
                includeMetadata: true,
                filter: {
                    workspace_id: { $eq: workspaceId },
                },
            };
            const searchResult = await namespaceIndex.query(queryParams);
            this.logger.log(`Pinecone returned ${searchResult.matches.length} results for query: ${query}${workspaceId ? ` in workspace ${workspaceId}` : ''}`);
            const results = searchResult.matches.map((match) => ({
                content: String(match.metadata?.chunk_text || 'No content available'),
                relevance_score: match.score || 0.0,
                metadata: {
                    source: 'pinecone_knowledge_base',
                    title: match.metadata?.title,
                    document_source: match.metadata?.source,
                    chunk_id: match.metadata?.chunk_id || match.id,
                    document_id: match.metadata?.document_id,
                    page: match.metadata?.page,
                    workspace_id: match.metadata?.workspace_id,
                    year: match.metadata?.year,
                    sub_chunk_index: match.metadata?.sub_chunk_index,
                    token_count: match.metadata?.token_count,
                    score: match.score,
                    query: query,
                    timestamp: new Date().toISOString(),
                },
            }));
            return results;
        }
        catch (error) {
            this.logger.error('Error searching Pinecone knowledge base:', error);
            this.logger.warn('Falling back to placeholder results due to Pinecone error');
            return [
                {
                    content: `Error accessing Pinecone knowledge base for query: ${query}. Error: ${error.message}`,
                    relevance_score: 0.1,
                    metadata: {
                        error: true,
                        fallback: true,
                        error_message: error.message,
                        timestamp: new Date().toISOString(),
                    },
                },
            ];
        }
    }
    async getDocumentPages(documentId, pages, topKPerPage = 5, workspaceId) {
        this.logger.log(`Retrieving document pages for document ${documentId}, pages: ${pages.join(', ')} (workspace: ${workspaceId})`);
        if (!workspaceId) {
            throw new Error('workspaceId is required for workspace isolation');
        }
        try {
            let query = this.supabase
                .from('document_chunk')
                .select(`
          id,
          documentId,
          page,
          content,
          metadataJson,
          createdAt,
          document:documentId (
            id,
            name,
            workspaceId,
            year,
            documentType
          )
        `)
                .eq('documentId', documentId)
                .in('page', pages.map((p) => p.toString()));
            query = query.eq('document.workspaceId', workspaceId);
            query = query
                .order('page', { ascending: true })
                .limit(pages.length * topKPerPage);
            const { data: documentChunks, error } = await query;
            if (error) {
                throw new Error(`Database query failed: ${error.message}`);
            }
            if (!documentChunks || documentChunks.length === 0) {
                this.logger.warn(`No document chunks found for document ${documentId}, pages: ${pages.join(', ')}`);
                return [];
            }
            this.logger.log(`Database returned ${documentChunks.length} chunks for document ${documentId}, pages: ${pages.join(', ')}`);
            const results = documentChunks.map((chunk) => {
                const document = Array.isArray(chunk.document)
                    ? chunk.document[0]
                    : chunk.document;
                return {
                    chunkId: chunk.id,
                    page: parseInt(chunk.page) || 1,
                    text: chunk.content || 'No content available',
                    score: 1.0,
                    metadata: {
                        source: 'database_document_pages',
                        title: document?.name || 'Unknown Document',
                        document_source: document?.name,
                        chunk_id: chunk.id,
                        document_id: chunk.documentId,
                        page: chunk.page,
                        workspace_id: document?.workspaceId,
                        year: document?.year,
                        document_type: document?.documentType,
                        metadata_json: chunk.metadataJson,
                        created_at: chunk.createdAt,
                        query_type: 'document_pages',
                        requested_pages: pages,
                        timestamp: new Date().toISOString(),
                    },
                };
            });
            results.sort((a, b) => a.page - b.page);
            return results;
        }
        catch (error) {
            this.logger.error('Error retrieving document pages from database:', error);
            return [
                {
                    chunkId: 'error',
                    page: 0,
                    text: `Error retrieving document pages for document ${documentId}. Error: ${error.message}`,
                    score: 0.0,
                    metadata: {
                        error: true,
                        fallback: true,
                        error_message: error.message,
                        document_id: documentId,
                        requested_pages: pages,
                        timestamp: new Date().toISOString(),
                    },
                },
            ];
        }
    }
    async parseAgentResponse(content, conversationHistory, projectId, validOptionIds, workspaceId) {
        try {
            let cleanedContent = content.trim();
            if (cleanedContent.startsWith('```json') &&
                cleanedContent.endsWith('```')) {
                cleanedContent = cleanedContent.slice(7, -3).trim();
            }
            else if (cleanedContent.startsWith('```') &&
                cleanedContent.endsWith('```')) {
                cleanedContent = cleanedContent.slice(3, -3).trim();
            }
            const jsonMatch = cleanedContent.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                cleanedContent = jsonMatch[0];
            }
            this.logger.log('Attempting to parse cleaned content:', cleanedContent.substring(0, 200) + '...');
            const parsed = JSON.parse(cleanedContent);
            if (!parsed.answers || !Array.isArray(parsed.answers)) {
                throw new Error('Parsed response missing required "answers" array');
            }
            let savedResults = false;
            for (const answer of parsed.answers) {
                if (answer.document_chunks && answer.document_chunks.length > 0) {
                    await this.saveAiAnswerResults(projectId, JSON.stringify(parsed), validOptionIds, workspaceId);
                    savedResults = true;
                    break;
                }
            }
            this.logger.log(`Successfully parsed response with ${parsed.answers.length} answers${savedResults ? ' (saved to database)' : ''}`);
            return {
                answer: parsed,
                evidence_sources: [],
                confidence_level: 'high',
                conversationHistory,
            };
        }
        catch (error) {
            this.logger.error('Failed to parse agent response as JSON:', error);
            this.logger.error('Original content length:', content.length);
            this.logger.error('Content preview:', content.substring(0, 500));
            return {
                answer: {
                    answers: [],
                },
                evidence_sources: [],
                confidence_level: 'low',
                conversationHistory,
            };
        }
    }
    async getWorkspaceIdFromProject(projectId) {
        const { data: project, error } = await this.supabase
            .from('project')
            .select('workspaceId')
            .eq('id', projectId)
            .single();
        if (error || !project) {
            throw new Error(`Failed to fetch workspace for project ${projectId}: ${error?.message}`);
        }
        return project.workspaceId;
    }
    async fetchEcoVadisData(projectId, questionId) {
        const { data: projectQuestionRaw, error: projectQuestionError } = await this.supabase
            .from('project_ecovadis_question')
            .select(`
        id,
        status,
        impact,
        ecovadis_question:questionId (
          id,
          questionCode,
          question,
          questionName,
          indicator,
          themeId,
          type
        )
      `)
            .eq('id', questionId)
            .single();
        if (projectQuestionError || !projectQuestionRaw) {
            throw new Error('Failed to fetch project question details');
        }
        const projectQuestion = {
            id: projectQuestionRaw.id,
            status: projectQuestionRaw.status,
            impact: projectQuestionRaw.impact,
            ecovadis_question: Array.isArray(projectQuestionRaw.ecovadis_question)
                ? projectQuestionRaw.ecovadis_question[0]
                : projectQuestionRaw.ecovadis_question,
        };
        const { data: theme, error: themeError } = await this.supabase
            .from('ecovadis_theme')
            .select(`
        id,
        title, 
        description,
        project_ecovadis_theme!inner (
          id,
          impact,
          issues
        )
      `)
            .eq('id', projectQuestion.ecovadis_question.themeId)
            .eq('project_ecovadis_theme.projectId', projectId)
            .single();
        const { data: options, error: optionsError } = await this.supabase
            .from('ecovadis_answer_option')
            .select(`
        id, 
        issueTitle, 
        instructions,
        project_ecovadis_answer!inner (
          id,
          response
        )
      `)
            .eq('questionId', projectQuestion.ecovadis_question.id)
            .eq('project_ecovadis_answer.projectId', projectId);
        const scoringFrameworkKey = this.formatScoringFrameworkKey({
            theme: theme ? theme.title : '',
            indicator: projectQuestion.ecovadis_question.indicator,
        });
        const scoringFrameworkKeyDriver = scoringFrameworkKey + ':scoring-drivers';
        const [{ data: scoringFrameworkData }, { data: scoringFrameworkDriverData },] = await Promise.all([
            this.supabase
                .from('kv_store')
                .select(`value`)
                .eq('key', scoringFrameworkKey)
                .single(),
            this.supabase
                .from('kv_store')
                .select(`value`)
                .eq('key', scoringFrameworkKeyDriver)
                .single(),
        ]);
        let sustainabilityIssues = '';
        if (theme?.project_ecovadis_theme?.length > 0) {
            const themeSustainabilityIssue = theme.project_ecovadis_theme
                .map((itheme) => itheme.issues)
                .flat();
            if (themeSustainabilityIssue.length > 0) {
                const { data: sustainabilityIssuesData } = await this.supabase
                    .from('ecovadis_sustainability_issues')
                    .select('id, issue, definition, industryIssues')
                    .in('id', themeSustainabilityIssue.map((issue) => issue.issueId));
                theme.project_ecovadis_theme.forEach((itheme) => {
                    itheme.issues.forEach((issue) => {
                        const issueData = sustainabilityIssuesData?.find((data) => data.id === issue.issueId);
                        if (issueData) {
                            sustainabilityIssues += `
Issue: ${issueData.issue}
Impact: ${themeSustainabilityIssue.find((i) => i.issueId === issueData.id)?.impact || 'No impact provided'}
Definition: ${issueData.definition}
Industry Issues: ${issueData.industryIssues}
\n`;
                        }
                    });
                });
            }
        }
        return {
            projectQuestion: projectQuestion,
            theme: theme,
            options: options,
            scoringFramework: scoringFrameworkData?.value,
            scoringFrameworkDrivers: scoringFrameworkDriverData?.value,
            sustainabilityIssues,
        };
    }
    formatDataForPrompt(projectQuestion, theme, options, scoringFramework, scoringFrameworkDrivers, sustainabilityIssues) {
        let questionnaireAnswers = '';
        questionnaireAnswers += `Question: ${projectQuestion.ecovadis_question.questionName}: ${projectQuestion.ecovadis_question.question}\n\n`;
        if (options) {
            options.forEach((option, index) => {
                questionnaireAnswers += `ANSWER ${index + 1}:\n\n`;
                questionnaireAnswers += `AnswerId: ${option.id}\n\n`;
                questionnaireAnswers += `Answer: ${option.issueTitle}\n\n`;
                questionnaireAnswers += `Support: ${option.instructions || 'No instructions provided'}\n\n`;
            });
        }
        const themeCriteria = theme
            ? `Theme: ${theme.title}\n\n${theme.description}`
            : 'Theme information not available';
        const indicatorCriteria = `Indicator: ${projectQuestion.ecovadis_question.indicator}\n\n` +
            `This indicator is about your company's actions to support your sustainability policies and commitments.\n\n` +
            `The answer options in each question represent best practices for your company's size and industry. Select options that your company has already implemented and provide the documented proof of your actions.`;
        const formattedScoringFramework = typeof scoringFramework === 'object' && scoringFramework !== null
            ? JSON.stringify(scoringFramework)
            : scoringFramework ||
                'Scoring framework not available for this indicator';
        return {
            question: projectQuestion.ecovadis_question.question,
            themeCriteria,
            indicatorCriteria,
            answerOptions: questionnaireAnswers.trim(),
            formattedScoringFramework,
        };
    }
    async saveAiAnswerResults(projectId, aiResponse, validOptionIds, workspaceId) {
        try {
            const parsed = JSON.parse(aiResponse);
            const attachments = await this.parseAiAnswerResponse(parsed, workspaceId);
            if (attachments.length > 0) {
                return await this.saveAiAnswerAttachments(projectId, attachments, validOptionIds);
            }
            return [];
        }
        catch (error) {
            this.logger.error('Error saving AI answer results:', error);
            return [];
        }
    }
    async parseAiAnswerResponse(response, workspaceId) {
        this.logger.log('Parsing AI answer response');
        if (!response?.answers || !Array.isArray(response.answers)) {
            this.logger.error('Invalid AI response format - missing answers array');
            return [];
        }
        const attachments = [];
        for (const item of response.answers) {
            if (!item?.answer_option?.id ||
                !item?.document_chunks ||
                !Array.isArray(item.document_chunks)) {
                this.logger.warn('Skipping invalid AI answer item:', item);
                continue;
            }
            const chunkIds = item.document_chunks
                .map((chunk) => chunk.document_chunk_id)
                .filter((id) => id);
            if (chunkIds.length === 0) {
                this.logger.warn('No valid chunk IDs found for answer option:', item.answer_option.id);
                continue;
            }
            const { data: documentChunks, error } = await this.supabase
                .from('document_chunk')
                .select(`
          id, 
          documentId, 
          page,
          document:documentId (
            workspaceId
          )
        `)
                .in('id', chunkIds);
            if (error) {
                this.logger.error('Error fetching document chunks:', error);
                continue;
            }
            if (!documentChunks || documentChunks.length === 0) {
                this.logger.warn('No document chunks found for chunk IDs:', chunkIds);
                continue;
            }
            const validDocumentChunks = documentChunks.filter((chunk) => {
                const document = Array.isArray(chunk.document) ? chunk.document[0] : chunk.document;
                const chunkWorkspaceId = document?.workspaceId;
                if (chunkWorkspaceId !== workspaceId) {
                    this.logger.error(`Security violation: Chunk ${chunk.id} belongs to workspace ${chunkWorkspaceId}, but expected ${workspaceId}`);
                    return false;
                }
                return true;
            });
            if (validDocumentChunks.length === 0) {
                this.logger.warn('No valid document chunks found after workspace filtering for chunk IDs:', chunkIds);
                continue;
            }
            if (validDocumentChunks.length < documentChunks.length) {
                this.logger.warn(`Filtered out ${documentChunks.length - validDocumentChunks.length} chunks from other workspaces`);
            }
            const documentGroups = {};
            for (const chunk of item.document_chunks) {
                const chunkId = chunk.document_chunk_id;
                if (!chunkId)
                    continue;
                const documentChunk = validDocumentChunks.find((dc) => dc.id === chunkId);
                if (!documentChunk) {
                    this.logger.warn('Document chunk not found in database:', chunkId);
                    continue;
                }
                const documentId = documentChunk.documentId;
                const pageNumber = documentChunk.page?.toString() || '1';
                const link = {
                    chunkId,
                    page: pageNumber,
                    comment: chunk.comment || '',
                };
                if (!documentGroups[documentId]) {
                    documentGroups[documentId] = [];
                }
                documentGroups[documentId].push(link);
            }
            for (const [documentId, data] of Object.entries(documentGroups)) {
                const uniquePages = [...new Set(data.pages)];
                const pageNumbers = uniquePages
                    .map((page) => parseInt(page, 10))
                    .filter((page) => !isNaN(page));
                const maxPage = Math.max(...validDocumentChunks
                    .filter((chunk) => chunk.documentId === documentId)
                    .map((chunk) => parseInt(chunk.page?.toString() || '1', 10))
                    .filter((page) => !isNaN(page)));
                const bufferedPageNumbers = (0, common_util_1.addPageRangeBufferWithLogging)(pageNumbers, maxPage, `AI auto answer - document: ${documentId}
        );

        // Convert back to strings for storage
        const bufferedPages = bufferedPageNumbers.map((page) =>
          page.toString())

        attachments.push({
          optionId: item.answer_option.id,
          documentId,
          pages: bufferedPages,
          comment: item.comment || '',
          chunkIds: data.chunkIds,
        });
      }

      // Create an attachment for each document with its per-link comments
      for (const [documentId, links] of Object.entries(documentGroups)) {
        // Deduplicate links by chunkId while preserving first comment
        const seen = new Set<string>();
        const dedupedLinks = links.filter((l) => {
          if (seen.has(l.chunkId)) return false;
          seen.add(l.chunkId);
          return true;
        });

        if (dedupedLinks.length === 0) continue;

        attachments.push({
          optionId: item.answer_option.id,
          documentId,
          links: dedupedLinks,
        });
      }
    }

    this.logger.log(`, Parsed, attachments, $, { attachments, : .length } `);
    return attachments;
  }

  /**
   * Save AI answer attachments to database
   */
  private async saveAiAnswerAttachments(
    projectId: string,
    attachments: ParsedAttachment[],
    validOptionIds: string[]
  ): Promise<any[]> {
    const results: any[] = [];

    for (const attachment of attachments) {
      const { optionId, documentId, links } = attachment;

      try {
        // Validate that the optionId belongs to the current project's question
        if (!validOptionIds.includes(optionId)) {
          results.push({
            optionId,
            documentId,
            status: 'error',
            message:
              'Option ID does not belong to the current project question',
          });
          continue;
        }

        // Check if answer already exists for this option
        const { data: existingAnswer, error: answerFetchError } =
          await this.supabase
            .from('project_ecovadis_answer')
            .select('id')
            .eq('projectId', projectId)
            .eq('optionId', optionId)
            .maybeSingle();

        if (answerFetchError) {
          throw new Error(
            `, Error, fetching, existing, answer, $, { answerFetchError, : .message } `
          );
        }

        let answerId;

        if (existingAnswer) {
          answerId = existingAnswer.id;
        } else {
          // Create new answer with response="true"
          const { data: newAnswer, error: createAnswerError } =
            await this.supabase
              .from('project_ecovadis_answer')
              .insert({
                projectId,
                optionId,
                response: 'true',
              })
              .select('id')
              .single();

          if (createAnswerError) {
            throw new Error(
              `, Error, creating, new answer, $, { createAnswerError, : .message } `
            );
          }

          answerId = newAnswer.id;
        }

        // Check for existing links to avoid duplicates
        const { data: existingLinks, error: existingLinksError } =
          await this.supabase
            .from('project_ecovadis_linked_document_chunks')
            .select('documentChunkId')
            .eq('answerId', answerId);

        if (existingLinksError) {
          throw new Error(
            `, Error, checking, existing, links, $, { existingLinksError, : .message } `
          );
        }

        // Filter out document chunks that are already linked
        const existingChunkIds =
          existingLinks?.map((link) => link.documentChunkId) || [];
        const linksToInsert = links.filter(
          (l) => !existingChunkIds.includes(l.chunkId)
        );

        if (linksToInsert.length > 0) {
          // Create linked document chunks with per-link comments
          const linkedChunksData = linksToInsert.map((link) => ({
            answerId,
            documentChunkId: link.chunkId,
            comment: link.comment || null,
            attachment_source: 'ai',
          }));

          const { data: linkedChunks, error: linkError } = await this.supabase
            .from('project_ecovadis_linked_document_chunks')
            .insert(linkedChunksData)
            .select();

          if (linkError) {
            throw new Error(
              `, Error, linking, document, chunks, $, { linkError, : .message } `
            );
          }

          results.push({
            optionId,
            documentId,
            status: 'success',
            answerId,
            linkedChunksCount: linkedChunks.length,
            newLinksCreated: true,
          });
        } else {
          results.push({
            optionId,
            documentId,
            status: 'success',
            answerId,
            linkedChunksCount: 0,
            newLinksCreated: false,
            message: 'All document chunks already linked to this answer',
          });
        }
      } catch (error) {
        results.push({
          optionId,
          documentId,
          status: 'error',
          message: error.message,
        });
      }
    }

    return results;
  }

  /**
   * Format scoring framework key (helper method)
   */
  private formatScoringFrameworkKey({
    theme,
    indicator,
  }: {
    theme: string;
    indicator: string;
  }): string {
    // Convert theme and indicator to lowercase and replace spaces/special chars with dashes
    const formattedTheme = theme
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9\-]/g, '');
    const formattedIndicator = indicator
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9\-]/g, '');

    return `, $, { formattedTheme }, $, { formattedIndicator } `;
  }
}
                );
            }
        }
    }
};
exports.EnhancedEcoVadisAnswerAgentService = EnhancedEcoVadisAnswerAgentService;
exports.EnhancedEcoVadisAnswerAgentService = EnhancedEcoVadisAnswerAgentService = EnhancedEcoVadisAnswerAgentService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        chat_gpt_service_1.ChatGptService,
        cohere_service_1.CohereService])
], EnhancedEcoVadisAnswerAgentService);
//# sourceMappingURL=answer-linking.service.js.map