"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EnhancedEcoVadisAnswerAgentService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedEcoVadisAnswerAgentService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const supabase_js_1 = require("@supabase/supabase-js");
const openai_1 = require("openai");
const genai_1 = require("@google/genai");
const constants_1 = require("../../constants");
const pinecone_1 = require("@pinecone-database/pinecone");
const chat_gpt_service_1 = require("../../llm/chat-gpt.service");
const cohere_service_1 = require("../../llm/cohere.service");
let EnhancedEcoVadisAnswerAgentService = EnhancedEcoVadisAnswerAgentService_1 = class EnhancedEcoVadisAnswerAgentService {
    constructor(configService, chatGptService, cohereService) {
        this.configService = configService;
        this.chatGptService = chatGptService;
        this.cohereService = cohereService;
        this.logger = new common_1.Logger(EnhancedEcoVadisAnswerAgentService_1.name);
        this.openAiResponseSchema = {
            type: 'object',
            properties: {
                answers: {
                    type: 'array',
                    description: 'List of answer options, each with evidence documents chunks',
                    items: {
                        type: 'object',
                        properties: {
                            answer_option: {
                                type: 'object',
                                description: 'Identifier and text of the answer option',
                                properties: {
                                    id: {
                                        type: 'string',
                                        description: 'Identifier UUID of the answer option',
                                    },
                                    answer_name: {
                                        type: 'string',
                                        description: 'Text of the answer option',
                                    },
                                },
                                required: ['id', 'answer_name'],
                                additionalProperties: false,
                            },
                            document_chunks: {
                                type: 'array',
                                description: 'List of document chunks containing the evidence for this option (empty if none)',
                                items: {
                                    type: 'object',
                                    properties: {
                                        document_chunk_id: {
                                            type: 'string',
                                            description: 'identifier UUID of the document',
                                        },
                                    },
                                    required: ['document_chunk_id'],
                                    additionalProperties: false,
                                },
                            },
                            comment: {
                                type: 'string',
                                description: 'Optional clarifying comment or explanation about the evidence',
                            },
                        },
                        required: ['answer_option', 'document_chunks'],
                        additionalProperties: false,
                    },
                },
            },
            required: ['answers'],
            additionalProperties: false,
        };
        this.openAiJsonSchema = {
            name: 'ecovadis_answer_schema',
            description: 'Schema for providing Ecovadis question valid answers with evidence references for multiple answer options',
            strict: true,
            schema: this.openAiResponseSchema,
        };
        this.tools = [
            {
                type: 'function',
                function: {
                    name: 'search_vectorstore',
                    description: "Search the Pinecone vector knowledge base for relevant information about sustainability practices, ESG requirements, and EcoVadis criteria within the user's workspace",
                    parameters: {
                        type: 'object',
                        properties: {
                            queries: {
                                type: 'array',
                                items: { type: 'string' },
                                description: 'Array of search queries to find relevant information in the knowledge base. You can provide 1-5 queries to cover different aspects of the question. Results will be deduplicated and reranked.',
                                minItems: 1,
                                maxItems: 5,
                            },
                        },
                        required: ['queries'],
                    },
                },
            },
            {
                type: 'function',
                function: {
                    name: 'get_document_pages',
                    description: 'Retrieve all chunk texts for the given document and specific page numbers. Use this when you know the document ID and want to fetch specific pages.',
                    parameters: {
                        type: 'object',
                        properties: {
                            documentId: {
                                type: 'string',
                                description: 'Document UUID to fetch pages from',
                            },
                            pages: {
                                type: 'array',
                                items: { type: 'integer' },
                                description: 'Array of page numbers to fetch (e.g., [1, 2, 3])',
                            },
                            topKPerPage: {
                                type: 'integer',
                                description: 'Optional limit of chunks per page (default: 5)',
                            },
                        },
                        required: ['documentId', 'pages'],
                    },
                },
            },
        ];
        this.openAiClient = new openai_1.default({
            apiKey: process.env.OPENAI_API_KEY,
            baseURL: 'https://oai.hconeai.com/v1',
            defaultHeaders: {
                'Helicone-Auth': `Bearer ${process.env.HELICONE_AUTH_API_KEY}`,
                'Helicone-RateLimit-Policy': '3000;w=3600;u=requests;s=user',
            },
        });
        if (!process.env.GEMINI_API_KEY) {
            throw new Error('GEMINI_API_KEY missing');
        }
        if (!process.env.HELICONE_AUTH_API_KEY) {
            this.logger.warn('HELICONE_AUTH_API_KEY not found, Helicone logging will be disabled');
        }
        this.geminiClient = new genai_1.GoogleGenAI({
            apiKey: process.env.GEMINI_API_KEY,
            ...(process.env.HELICONE_AUTH_API_KEY && {
                httpOptions: {
                    baseUrl: 'https://gateway.helicone.ai',
                    headers: {
                        'Helicone-Auth': `Bearer ${process.env.HELICONE_AUTH_API_KEY}`,
                        'Helicone-Target-URL': 'https://generativelanguage.googleapis.com',
                    },
                },
            }),
        });
        this.supabase = (0, supabase_js_1.createClient)(this.configService.get('SUPABASE_APP_URL'), this.configService.get('SUPABASE_SERVICE_KEY'));
        this.initializePinecone();
    }
    async initializePinecone() {
        try {
            this.pineconeClient = new pinecone_1.Pinecone({
                apiKey: process.env.PINECONE_API_KEY,
            });
            this.logger.log('Pinecone client initialized successfully');
        }
        catch (error) {
            this.logger.error('Failed to initialize Pinecone client:', error);
        }
    }
    generateSystemPrompt(question, themeCriteria, sustainabilityIssues, indicatorCriteria, scoringFramework, answerOptions) {
        return `You are an EcoVadis sustainability reporting assistant that helps answer ecovadis questions to fill out the documentation. Your task is to answer Ecovadis questions for a company based on documents uploaded by the reporting company. In order to answer questions, use the two tools provided to search the Pinecone vector database and retrieve specific pages from documents. First task is to find relevant data to Ecovadis questions for the company in their Pinecone vector database. Then you return the final answer as a json.

    1. Use your search_vectorstore tool to research the knowledge base and find relevant evidence
    2. Use your get_document_pages tool to retrieve specific pages from documents when you know the document ID from previous similarity searches
    3. Iterate over multiple searches if needed to gather comprehensive information
    4. Craft accurate, evidence-based answers with proper citations
    5. Provide confidence levels based on the quality and quantity of evidence found
    
    Each question in Ecovadis has multiple answer options, each of which requires as an answer an (set of) evidence document(s), that contain the information on the company's actions/policies etc. A question can have multiple answers, so you can identify and map different document chunks to respective answers along with valid comments, if in effect the context suggests that the answer is true for the company. The linked document pages themselves are the answer, optionally with a comment in english language. All relevant documents the company has, are indexed in a Pinecone vector database. Contemplate how to phrase the sentence or paragraph effectively that you send to the vector database i.e. that the sentence or paragraph should be similar to the embedded text rather than a question and be broad enough to cover all potential answer options/topics. You can iterate over the db, but limit the number of iterations to 6 or less. Besides the similarity search, you can also use the get_document_pages tool to retrieve specific pages from documents when you know the document ID from previous similarity searches. Write a json containing the two required fields of the answer i.e. a list of document chunks (each chunk representing a page from a document) where the evidence is, as well as an optional comment. Only do so if there are documents that contain correct answers, otherwise return that there are no answers provided. In the array of answers you only need to provide ids of answers which has a valid evidence linkable to it.

    IMPORTANT: When you have finished gathering all necessary information through tool calls, you MUST return your final response in this EXACT JSON structure (no deviations allowed):

    {
      "answers": [
        {
          "answer_option": {
            "id": "uuid-of-answer-option",
            "answer_name": "Name of the answer option"
          },
          "document_chunks": [
            {
              "document_chunk_id": "uuid-of-document-chunk"
            }
          ],
          "comment": "Optional explanation"
        }
      ]
    }

    This JSON schema is strictly enforced:
    - "answers" is required array
    - Each answer requires "answer_option" object with "id" and "answer_name" strings
    - Each answer requires "document_chunks" array with "document_chunk_id" strings
    - "comment" is optional string

    Below is the issue that we need to provide evidence for, the criteria and the scoring framework. Use those to contemplate which output to create.

    Guidelines:
    - Always search the knowledge base before answering. Search longer and deeper to find more relevant content. Empirically speaking, you usually miss a lot of relevant content, so try harder and err towards too much content rather than too little.
    - Use search_vectorstore for similarity-based searches when you need to find relevant content
    - Use get_document_pages when you have identified specific documents and want to look at particular pages
    - Use multiple search queries with different keywords if the first search is insufficient
    - search until you either have a comprehensive answer or are confident that there is no more relevant information to be found
    - Cite your sources by referencing the evidence found
    - Indicate your confidence level: high (strong evidence), medium (some evidence), or low (limited evidence)
    - Be concise but comprehensive in your responses
    - Focus on actionable insights and practical guidance

    **Question**
    ${question}

    **Theme**
    ${themeCriteria || 'Not provided'}

    **Sustainability Issue**
    ${sustainabilityIssues || 'Not provided'}

    **Indicator Criteria**
    ${indicatorCriteria || 'Not provided'}

    **Scoring Framework**
    ${scoringFramework || 'Not provided'}

    **Answer Options** (optional, if we have them)
    ${answerOptions || 'Not provided'}

    Return your final answer in JSON format with the specified schema.`;
    }
    async answerEcoVadisQuestion({ projectId, questionId, useGemini = false, }) {
        const useOpenAI = process.env.USE_OPENAI === 'true' || !process.env.GEMINI_API_KEY;
        if (useOpenAI) {
            this.logger.log('Using OpenAI GPT-4o for EcoVadis question answering');
            return this.answerEcoVadisQuestionWithOpenAI({ projectId, questionId });
        }
        else {
            this.logger.log('Using Gemini 2.5 Pro for EcoVadis question answering (default)');
            return this.answerEcoVadisQuestionWithGemini({ projectId, questionId });
        }
    }
    async chatWithModel(client, model, messages, tools) {
        const isGemini = model.includes('gemini');
        if (isGemini) {
            const contents = messages.map(message => {
                let geminiRole;
                if (message.role === 'assistant') {
                    geminiRole = 'model';
                }
                else {
                    geminiRole = 'user';
                }
                return {
                    role: geminiRole,
                    parts: [{ text: String(message.content || '') }]
                };
            });
            const result = await client.models.generateContent({
                model,
                contents,
                config: {
                    tools: [{
                            functionDeclarations: [
                                {
                                    name: 'search_vectorstore',
                                    description: "Search the Pinecone vector knowledge base for relevant information about sustainability practices, ESG requirements, and EcoVadis criteria within the user's workspace",
                                    parameters: {
                                        type: genai_1.Type.OBJECT,
                                        properties: {
                                            queries: {
                                                type: genai_1.Type.ARRAY,
                                                items: { type: genai_1.Type.STRING },
                                                description: 'Array of search queries to find relevant information in the knowledge base. You can provide 1-5 queries to cover different aspects of the question. Results will be deduplicated and reranked.',
                                            },
                                        },
                                        required: ['queries'],
                                    },
                                },
                                {
                                    name: 'get_document_pages',
                                    description: 'Retrieve all chunk texts for the given document and specific page numbers. Use this when you know the document ID and want to fetch specific pages.',
                                    parameters: {
                                        type: genai_1.Type.OBJECT,
                                        properties: {
                                            documentId: {
                                                type: genai_1.Type.STRING,
                                                description: 'Document UUID to fetch pages from',
                                            },
                                            pages: {
                                                type: genai_1.Type.ARRAY,
                                                items: { type: genai_1.Type.NUMBER },
                                                description: 'Array of page numbers to fetch (e.g., [1, 2, 3])',
                                            },
                                            topKPerPage: {
                                                type: genai_1.Type.NUMBER,
                                                description: 'Optional limit of chunks per page (default: 5)',
                                            },
                                        },
                                        required: ['documentId', 'pages'],
                                    },
                                }
                            ]
                        }],
                    toolConfig: {
                        functionCallingConfig: {
                            mode: genai_1.FunctionCallingConfigMode.AUTO
                        }
                    },
                    temperature: 0
                }
            });
            const functionCalls = result.functionCalls ??
                result.candidates?.flatMap(c => c.content.parts
                    .filter(p => 'functionCall' in p)
                    .map(p => p.functionCall));
            if (functionCalls && functionCalls.length > 0) {
                const toolCalls = functionCalls.map((fc, index) => ({
                    id: `call_${index}`,
                    type: 'function',
                    function: {
                        name: fc.name,
                        arguments: JSON.stringify(fc.args)
                    }
                }));
                return {
                    role: 'assistant',
                    content: result.text || null,
                    tool_calls: toolCalls,
                };
            }
            return {
                role: 'assistant',
                content: result.text,
                tool_calls: undefined,
            };
        }
        else {
            const response = await client.chat.completions.create({
                model,
                messages,
                tools,
                max_tokens: 2000,
                response_format: {
                    type: 'json_schema',
                    json_schema: this.openAiJsonSchema,
                },
            });
            return response.choices[0].message;
        }
    }
    async chatWithModelFinalResponse(client, model, messages) {
        const isGemini = model.includes('gemini');
        if (isGemini) {
            const contents = messages.map(message => {
                let geminiRole;
                if (message.role === 'assistant') {
                    geminiRole = 'model';
                }
                else {
                    geminiRole = 'user';
                }
                return {
                    role: geminiRole,
                    parts: [{ text: String(message.content || '') }]
                };
            });
            const result = await client.models.generateContent({
                model,
                contents,
                config: {
                    responseMimeType: 'application/json',
                    responseSchema: this.openAiResponseSchema,
                    temperature: 0
                }
            });
            return {
                role: 'assistant',
                content: result.text,
                tool_calls: undefined,
            };
        }
        else {
            const response = await client.chat.completions.create({
                model,
                messages,
                max_tokens: 2000,
                response_format: {
                    type: 'json_schema',
                    json_schema: this.openAiJsonSchema,
                },
            });
            return response.choices[0].message;
        }
    }
    convertMessagesToGeminiPrompt(messages) {
        return messages
            .map((message) => {
            if (message.role === 'system') {
                return `System: ${message.content}`;
            }
            else if (message.role === 'user') {
                return `User: ${message.content}`;
            }
            else if (message.role === 'assistant') {
                return `Assistant: ${message.content}`;
            }
            else if (message.role === 'tool') {
                return `Tool Response: ${message.content}`;
            }
            return '';
        })
            .filter(Boolean)
            .join('\n\n');
    }
    async handleToolCallsGeneric(client, model, messages, toolCalls, workspaceId, projectId, options, seenChunks) {
        for (const toolCall of toolCalls) {
            if (toolCall.function.name === 'search_vectorstore') {
                const args = JSON.parse(toolCall.function.arguments);
                const searchResult = await this.searchVectorStoreMultiple(args.queries, args.limit, workspaceId, seenChunks);
                messages.push({
                    role: 'tool',
                    tool_call_id: toolCall.id,
                    content: JSON.stringify(searchResult),
                });
            }
            else if (toolCall.function.name === 'get_document_pages') {
                const args = JSON.parse(toolCall.function.arguments);
                const documentPages = await this.getDocumentPages(args.documentId, args.pages, args.topKPerPage, workspaceId);
                messages.push({
                    role: 'tool',
                    tool_call_id: toolCall.id,
                    content: JSON.stringify(documentPages),
                });
            }
        }
        const finalMessage = await this.chatWithModelFinalResponse(client, model, messages);
        messages.push(finalMessage);
        if (finalMessage.tool_calls && finalMessage.tool_calls.length > 0) {
            return await this.handleToolCallsGeneric(client, model, messages, finalMessage.tool_calls, workspaceId, projectId, options, seenChunks);
        }
        const validOptionIds = options?.map((option) => option.id) || [];
        return await this.parseAgentResponse(finalMessage.content || '', messages, projectId || '', validOptionIds);
    }
    async answerEcoVadisQuestionGeneric({ projectId, questionId, client, model, vendorName, }) {
        this.logger.log(`Answering EcoVadis question with ${vendorName} for project ${projectId}, question ${questionId}`);
        try {
            const workspaceId = await this.getWorkspaceIdFromProject(projectId);
            this.logger.log(`Processing question for workspace: ${workspaceId}`);
            const { projectQuestion, theme, options, scoringFramework, scoringFrameworkDrivers, sustainabilityIssues, } = await this.fetchEcoVadisData(projectId, questionId);
            const { question, themeCriteria, indicatorCriteria, answerOptions, formattedScoringFramework, } = this.formatDataForPrompt(projectQuestion, theme, options, scoringFramework, scoringFrameworkDrivers, sustainabilityIssues);
            const messages = [
                {
                    role: 'system',
                    content: this.generateSystemPrompt(question, themeCriteria, sustainabilityIssues, indicatorCriteria, formattedScoringFramework, answerOptions),
                },
                { role: 'user', content: question },
            ];
            const isGemini = model.includes('gemini');
            const message = await this.chatWithModel(client, model, messages, this.tools);
            messages.push(message);
            if (message.tool_calls && message.tool_calls.length > 0) {
                const seenChunks = {
                    chunkIds: new Set(),
                    chunkTexts: new Set(),
                };
                return await this.handleToolCallsGeneric(client, model, messages, message.tool_calls, workspaceId, projectId, options, seenChunks);
            }
            const validOptionIds = options.map((option) => option.id);
            return await this.parseAgentResponse(message.content || '', messages, projectId, validOptionIds);
        }
        catch (error) {
            this.logger.error(`Error in answerEcoVadisQuestionGeneric (${vendorName}):`, error);
            throw new Error(`Failed to answer EcoVadis question with ${vendorName}: ${error.message}`);
        }
    }
    async answerEcoVadisQuestionWithOpenAI({ projectId, questionId, }) {
        const client = this.openAiClient;
        const model = constants_1.LLM_MODELS['gpt-4o'];
        const vendorName = 'OpenAI GPT-4o';
        return this.answerEcoVadisQuestionGeneric({
            projectId,
            questionId,
            client,
            model,
            vendorName,
        });
    }
    async answerEcoVadisQuestionWithGemini({ projectId, questionId, }) {
        const client = this.geminiClient;
        const model = constants_1.LLM_MODELS['gemini-2.5-pro'];
        const vendorName = 'Gemini 2.5 Pro';
        return this.answerEcoVadisQuestionGeneric({
            projectId,
            questionId,
            client,
            model,
            vendorName,
        });
    }
    async searchVectorStoreMultiple(queries, limit = 10, workspaceId, seenChunks) {
        if (!queries || queries.length === 0) {
            this.logger.warn('No queries provided to searchVectorStoreMultiple');
            return [];
        }
        const limitedQueries = queries.slice(0, 5);
        this.logger.log(`Hybrid search with ${limitedQueries.length} queries: ${limitedQueries.join(', ')}`);
        const vectorSearchTasks = limitedQueries.map(query => this.searchVectorStore(query, 50, workspaceId));
        const keywordSearchTask = this.searchKeywords(limitedQueries, 20, workspaceId);
        const [vectorResults, keywordResults] = await Promise.all([
            Promise.all(vectorSearchTasks).then((results) => results.flat()),
            keywordSearchTask,
        ]);
        const allResults = [...vectorResults, ...keywordResults];
        this.logger.log(`Hybrid search yielded ${vectorResults.length} vector + ${keywordResults.length} keyword = ${allResults.length} total results`);
        const flatResults = allResults;
        const deduplicatedResults = this.deduplicateSearchResults(flatResults, seenChunks);
        if (deduplicatedResults.length === 0) {
            this.logger.warn('No results after deduplication');
            return [];
        }
        const mainQuery = limitedQueries[0];
        const rerankedResults = await this.cohereService.rerankResults(mainQuery, deduplicatedResults.map((r) => ({
            content: r.content,
            metadata: r.metadata,
        })), limit);
        return rerankedResults.map((result) => ({
            content: result.content,
            relevance_score: result.relevance_score,
            metadata: {
                ...result.metadata,
                reranked: true,
                original_index: result.original_index,
                main_query: mainQuery,
            },
        }));
    }
    deduplicateSearchResults(results, seenChunks) {
        const deduplicatedResults = [];
        const currentIterationChunkIds = new Set();
        const currentIterationChunkTexts = new Set();
        for (const result of results) {
            const chunkId = result.metadata?.chunk_id;
            const contentHash = this.hashContent(result.content);
            if (seenChunks?.chunkIds.has(chunkId)) {
                continue;
            }
            if (seenChunks?.chunkTexts.has(contentHash)) {
                continue;
            }
            if (currentIterationChunkIds.has(chunkId) ||
                currentIterationChunkTexts.has(contentHash)) {
                continue;
            }
            if (chunkId) {
                currentIterationChunkIds.add(chunkId);
                seenChunks?.chunkIds.add(chunkId);
            }
            currentIterationChunkTexts.add(contentHash);
            seenChunks?.chunkTexts.add(contentHash);
            deduplicatedResults.push(result);
        }
        this.logger.log(`Deduplication: ${results.length} -> ${deduplicatedResults.length} results`);
        return deduplicatedResults;
    }
    hashContent(content) {
        const normalized = content.trim().replace(/\s+/g, ' ').toLowerCase();
        return normalized.substring(0, 200);
    }
    async searchKeywords(queries, limit = 20, workspaceId) {
        if (!queries || queries.length === 0) {
            return [];
        }
        try {
            const allKeywords = queries
                .flatMap(query => query.toLowerCase()
                .replace(/[^\w\s]/g, ' ')
                .split(/\s+/)
                .filter(word => word.length > 3))
                .filter((word, index, arr) => arr.indexOf(word) === index);
            if (allKeywords.length === 0) {
                return [];
            }
            this.logger.log(`Keyword search with terms: ${allKeywords.slice(0, 5).join(', ')}${allKeywords.length > 5 ? '...' : ''}`);
            let query = this.supabase.from('document_chunk').select(`
          id,
          content,
          page,
          documentId,
          document:documentId (
            name,
            workspaceId,
            year,
            documentType
          )
        `);
            if (workspaceId) {
                query = query.eq('document.workspaceId', workspaceId);
            }
            const searchTerms = allKeywords.slice(0, 10).join(' | ');
            query = query.textSearch('content', searchTerms);
            query = query.limit(limit);
            const { data: chunks, error } = await query;
            if (error) {
                this.logger.error('Keyword search error:', error);
                return [];
            }
            if (!chunks || chunks.length === 0) {
                this.logger.log('No keyword search results found');
                return [];
            }
            const results = chunks.map((chunk) => {
                const content = chunk.content || '';
                const contentLower = content.toLowerCase();
                const matches = allKeywords.filter((keyword) => contentLower.includes(keyword)).length;
                const relevanceScore = Math.min(0.8, matches / allKeywords.length);
                const document = Array.isArray(chunk.document)
                    ? chunk.document[0]
                    : chunk.document;
                return {
                    content,
                    relevance_score: relevanceScore,
                    metadata: {
                        source: 'keyword_search',
                        title: document?.name || 'Unknown Document',
                        chunk_id: chunk.id,
                        document_id: chunk.documentId,
                        page: chunk.page,
                        workspace_id: document?.workspaceId,
                        year: document?.year,
                        document_type: document?.documentType,
                        keyword_matches: matches,
                        total_keywords: allKeywords.length,
                        search_type: 'keyword',
                        timestamp: new Date().toISOString(),
                    },
                };
            });
            this.logger.log(`Keyword search returned ${results.length} results`);
            return results;
        }
        catch (error) {
            this.logger.error('Error in keyword search:', error);
            return [];
        }
    }
    async searchVectorStore(query, limit = 7, workspaceId) {
        this.logger.log(`Searching Pinecone knowledge base for: ${query}${workspaceId ? ` (workspace: ${workspaceId})` : ''}`);
        if (!this.pineconeClient) {
            this.logger.error('Pinecone client not initialized');
            throw new Error('Pinecone client not initialized. Please check PINECONE_API_KEY environment variable.');
        }
        try {
            const indexName = process.env.PINECONE_INDEX_NAME;
            const indexHost = process.env.PINECONE_INDEX_HOST;
            const namespace = process.env.PINECONE_NAMESPACE;
            if (!indexHost) {
                throw new Error('PINECONE_INDEX_HOST environment variable is required');
            }
            this.logger.log('Generating embedding for search query...');
            const queryVector = await this.chatGptService.createEmbedding(query);
            this.logger.log(`Generated embedding with ${queryVector.length} dimensions`);
            const index = this.pineconeClient.index(indexName, indexHost);
            const namespaceIndex = index.namespace(namespace);
            const queryParams = {
                vector: queryVector,
                topK: limit,
                includeValues: false,
                includeMetadata: true,
                filter: {
                    workspace_id: { $eq: workspaceId },
                },
            };
            const searchResult = await namespaceIndex.query(queryParams);
            this.logger.log(`Pinecone returned ${searchResult.matches.length} results for query: ${query}${workspaceId ? ` in workspace ${workspaceId}` : ''}`);
            const results = searchResult.matches.map((match) => ({
                content: String(match.metadata?.chunk_text || 'No content available'),
                relevance_score: match.score || 0.0,
                metadata: {
                    source: 'pinecone_knowledge_base',
                    title: match.metadata?.title,
                    document_source: match.metadata?.source,
                    chunk_id: match.metadata?.chunk_id || match.id,
                    document_id: match.metadata?.document_id,
                    page: match.metadata?.page,
                    workspace_id: match.metadata?.workspace_id,
                    year: match.metadata?.year,
                    sub_chunk_index: match.metadata?.sub_chunk_index,
                    token_count: match.metadata?.token_count,
                    score: match.score,
                    query: query,
                    timestamp: new Date().toISOString(),
                },
            }));
            return results;
        }
        catch (error) {
            this.logger.error('Error searching Pinecone knowledge base:', error);
            this.logger.warn('Falling back to placeholder results due to Pinecone error');
            return [
                {
                    content: `Error accessing Pinecone knowledge base for query: ${query}. Error: ${error.message}`,
                    relevance_score: 0.1,
                    metadata: {
                        error: true,
                        fallback: true,
                        error_message: error.message,
                        timestamp: new Date().toISOString(),
                    },
                },
            ];
        }
    }
    async getDocumentPages(documentId, pages, topKPerPage = 5, workspaceId) {
        this.logger.log(`Retrieving document pages for document ${documentId}, pages: ${pages.join(', ')}${workspaceId ? ` (workspace: ${workspaceId})` : ''}`);
        try {
            let query = this.supabase
                .from('document_chunk')
                .select(`
          id,
          documentId,
          page,
          content,
          metadataJson,
          createdAt,
          document:documentId (
            id,
            name,
            workspaceId,
            year,
            documentType
          )
        `)
                .eq('documentId', documentId)
                .in('page', pages.map((p) => p.toString()));
            if (workspaceId) {
                query = query.eq('document.workspaceId', workspaceId);
            }
            query = query
                .order('page', { ascending: true })
                .limit(pages.length * topKPerPage);
            const { data: documentChunks, error } = await query;
            if (error) {
                throw new Error(`Database query failed: ${error.message}`);
            }
            if (!documentChunks || documentChunks.length === 0) {
                this.logger.warn(`No document chunks found for document ${documentId}, pages: ${pages.join(', ')}`);
                return [];
            }
            this.logger.log(`Database returned ${documentChunks.length} chunks for document ${documentId}, pages: ${pages.join(', ')}`);
            const results = documentChunks.map((chunk) => {
                const document = Array.isArray(chunk.document)
                    ? chunk.document[0]
                    : chunk.document;
                return {
                    chunkId: chunk.id,
                    page: parseInt(chunk.page) || 1,
                    text: chunk.content || 'No content available',
                    score: 1.0,
                    metadata: {
                        source: 'database_document_pages',
                        title: document?.name || 'Unknown Document',
                        document_source: document?.name,
                        chunk_id: chunk.id,
                        document_id: chunk.documentId,
                        page: chunk.page,
                        workspace_id: document?.workspaceId,
                        year: document?.year,
                        document_type: document?.documentType,
                        metadata_json: chunk.metadataJson,
                        created_at: chunk.createdAt,
                        query_type: 'document_pages',
                        requested_pages: pages,
                        timestamp: new Date().toISOString(),
                    },
                };
            });
            results.sort((a, b) => a.page - b.page);
            return results;
        }
        catch (error) {
            this.logger.error('Error retrieving document pages from database:', error);
            return [
                {
                    chunkId: 'error',
                    page: 0,
                    text: `Error retrieving document pages for document ${documentId}. Error: ${error.message}`,
                    score: 0.0,
                    metadata: {
                        error: true,
                        fallback: true,
                        error_message: error.message,
                        document_id: documentId,
                        requested_pages: pages,
                        timestamp: new Date().toISOString(),
                    },
                },
            ];
        }
    }
    async parseAgentResponse(content, conversationHistory, projectId, validOptionIds) {
        try {
            let cleanedContent = content.trim();
            if (cleanedContent.startsWith('```json') &&
                cleanedContent.endsWith('```')) {
                cleanedContent = cleanedContent.slice(7, -3).trim();
            }
            else if (cleanedContent.startsWith('```') &&
                cleanedContent.endsWith('```')) {
                cleanedContent = cleanedContent.slice(3, -3).trim();
            }
            const jsonMatch = cleanedContent.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                cleanedContent = jsonMatch[0];
            }
            this.logger.log('Attempting to parse cleaned content:', cleanedContent.substring(0, 200) + '...');
            const parsed = JSON.parse(cleanedContent);
            if (!parsed.answers || !Array.isArray(parsed.answers)) {
                throw new Error('Parsed response missing required "answers" array');
            }
            let savedResults = false;
            for (const answer of parsed.answers) {
                if (answer.document_chunks && answer.document_chunks.length > 0) {
                    await this.saveAiAnswerResults(projectId, JSON.stringify(parsed), validOptionIds);
                    savedResults = true;
                    break;
                }
            }
            this.logger.log(`Successfully parsed response with ${parsed.answers.length} answers${savedResults ? ' (saved to database)' : ''}`);
            return {
                answer: parsed,
                evidence_sources: [],
                confidence_level: 'high',
                conversationHistory,
            };
        }
        catch (error) {
            this.logger.error('Failed to parse agent response as JSON:', error);
            this.logger.error('Original content length:', content.length);
            this.logger.error('Content preview:', content.substring(0, 500));
            return {
                answer: {
                    answers: [],
                },
                evidence_sources: [],
                confidence_level: 'low',
                conversationHistory,
            };
        }
    }
    async getWorkspaceIdFromProject(projectId) {
        const { data: project, error } = await this.supabase
            .from('project')
            .select('workspaceId')
            .eq('id', projectId)
            .single();
        if (error || !project) {
            throw new Error(`Failed to fetch workspace for project ${projectId}: ${error?.message}`);
        }
        return project.workspaceId;
    }
    async fetchEcoVadisData(projectId, questionId) {
        const { data: projectQuestionRaw, error: projectQuestionError } = await this.supabase
            .from('project_ecovadis_question')
            .select(`
        id,
        status,
        impact,
        ecovadis_question:questionId (
          id,
          questionCode,
          question,
          questionName,
          indicator,
          themeId,
          type
        )
      `)
            .eq('id', questionId)
            .single();
        if (projectQuestionError || !projectQuestionRaw) {
            throw new Error('Failed to fetch project question details');
        }
        const projectQuestion = {
            id: projectQuestionRaw.id,
            status: projectQuestionRaw.status,
            impact: projectQuestionRaw.impact,
            ecovadis_question: Array.isArray(projectQuestionRaw.ecovadis_question)
                ? projectQuestionRaw.ecovadis_question[0]
                : projectQuestionRaw.ecovadis_question,
        };
        const { data: theme, error: themeError } = await this.supabase
            .from('ecovadis_theme')
            .select(`
        id, 
        title, 
        description,
        project_ecovadis_theme!inner (
          id,
          impact,
          issues
        )
      `)
            .eq('id', projectQuestion.ecovadis_question.themeId)
            .eq('project_ecovadis_theme.projectId', projectId)
            .single();
        const { data: options, error: optionsError } = await this.supabase
            .from('ecovadis_answer_option')
            .select(`
        id, 
        issueTitle, 
        instructions,
        project_ecovadis_answer!inner (
          id,
          response
        )
      `)
            .eq('questionId', projectQuestion.ecovadis_question.id)
            .eq('project_ecovadis_answer.projectId', projectId);
        const scoringFrameworkKey = this.formatScoringFrameworkKey({
            theme: theme ? theme.title : '',
            indicator: projectQuestion.ecovadis_question.indicator,
        });
        const scoringFrameworkKeyDriver = scoringFrameworkKey + ':scoring-drivers';
        const [{ data: scoringFrameworkData }, { data: scoringFrameworkDriverData },] = await Promise.all([
            this.supabase
                .from('kv_store')
                .select(`value`)
                .eq('key', scoringFrameworkKey)
                .single(),
            this.supabase
                .from('kv_store')
                .select(`value`)
                .eq('key', scoringFrameworkKeyDriver)
                .single(),
        ]);
        let sustainabilityIssues = '';
        if (theme?.project_ecovadis_theme?.length > 0) {
            const themeSustainabilityIssue = theme.project_ecovadis_theme
                .map((itheme) => itheme.issues)
                .flat();
            if (themeSustainabilityIssue.length > 0) {
                const { data: sustainabilityIssuesData } = await this.supabase
                    .from('ecovadis_sustainability_issues')
                    .select('id, issue, definition, industryIssues')
                    .in('id', themeSustainabilityIssue.map((issue) => issue.issueId));
                theme.project_ecovadis_theme.forEach((itheme) => {
                    itheme.issues.forEach((issue) => {
                        const issueData = sustainabilityIssuesData?.find((data) => data.id === issue.issueId);
                        if (issueData) {
                            sustainabilityIssues += `
Issue: ${issueData.issue}
Impact: ${themeSustainabilityIssue.find((i) => i.issueId === issueData.id)?.impact || 'No impact provided'}
Definition: ${issueData.definition}
Industry Issues: ${issueData.industryIssues}
\n`;
                        }
                    });
                });
            }
        }
        return {
            projectQuestion: projectQuestion,
            theme: theme,
            options: options,
            scoringFramework: scoringFrameworkData?.value,
            scoringFrameworkDrivers: scoringFrameworkDriverData?.value,
            sustainabilityIssues,
        };
    }
    formatDataForPrompt(projectQuestion, theme, options, scoringFramework, scoringFrameworkDrivers, sustainabilityIssues) {
        let questionnaireAnswers = '';
        questionnaireAnswers += `Question: ${projectQuestion.ecovadis_question.questionName}: ${projectQuestion.ecovadis_question.question}\n\n`;
        if (options) {
            options.forEach((option, index) => {
                questionnaireAnswers += `ANSWER ${index + 1}:\n\n`;
                questionnaireAnswers += `AnswerId: ${option.id}\n\n`;
                questionnaireAnswers += `Answer: ${option.issueTitle}\n\n`;
                questionnaireAnswers += `Support: ${option.instructions || 'No instructions provided'}\n\n`;
            });
        }
        const themeCriteria = theme
            ? `Theme: ${theme.title}\n\n${theme.description}`
            : 'Theme information not available';
        const indicatorCriteria = `Indicator: ${projectQuestion.ecovadis_question.indicator}\n\n` +
            `This indicator is about your company's actions to support your sustainability policies and commitments.\n\n` +
            `The answer options in each question represent best practices for your company's size and industry. Select options that your company has already implemented and provide the documented proof of your actions.`;
        const formattedScoringFramework = typeof scoringFramework === 'object' && scoringFramework !== null
            ? JSON.stringify(scoringFramework)
            : scoringFramework ||
                'Scoring framework not available for this indicator';
        return {
            question: projectQuestion.ecovadis_question.question,
            themeCriteria,
            indicatorCriteria,
            answerOptions: questionnaireAnswers.trim(),
            formattedScoringFramework,
        };
    }
    async saveAiAnswerResults(projectId, aiResponse, validOptionIds) {
        try {
            const parsed = JSON.parse(aiResponse);
            const attachments = await this.parseAiAnswerResponse(parsed);
            if (attachments.length > 0) {
                return await this.saveAiAnswerAttachments(projectId, attachments, validOptionIds);
            }
            return [];
        }
        catch (error) {
            this.logger.error('Error saving AI answer results:', error);
            return [];
        }
    }
    async parseAiAnswerResponse(response) {
        this.logger.log('Parsing AI answer response');
        if (!response?.answers || !Array.isArray(response.answers)) {
            this.logger.error('Invalid AI response format - missing answers array');
            return [];
        }
        const attachments = [];
        for (const item of response.answers) {
            if (!item?.answer_option?.id ||
                !item?.document_chunks ||
                !Array.isArray(item.document_chunks)) {
                this.logger.warn('Skipping invalid AI answer item:', item);
                continue;
            }
            const chunkIds = item.document_chunks
                .map((chunk) => chunk.document_chunk_id)
                .filter((id) => id);
            if (chunkIds.length === 0) {
                this.logger.warn('No valid chunk IDs found for answer option:', item.answer_option.id);
                continue;
            }
            const { data: documentChunks, error } = await this.supabase
                .from('document_chunk')
                .select('id, documentId, page')
                .in('id', chunkIds);
            if (error) {
                this.logger.error('Error fetching document chunks:', error);
                continue;
            }
            if (!documentChunks || documentChunks.length === 0) {
                this.logger.warn('No document chunks found for chunk IDs:', chunkIds);
                continue;
            }
            const documentGroups = {};
            for (const chunk of item.document_chunks) {
                if (!chunk.document_chunk_id)
                    continue;
                const documentChunk = documentChunks.find((dc) => dc.id === chunk.document_chunk_id);
                if (!documentChunk) {
                    this.logger.warn('Document chunk not found in database:', chunk.document_chunk_id);
                    continue;
                }
                const documentId = documentChunk.documentId;
                if (!documentGroups[documentId]) {
                    documentGroups[documentId] = { pages: [], chunkIds: [] };
                }
                const pageNumber = documentChunk.page?.toString() || '1';
                documentGroups[documentId].pages.push(pageNumber);
                documentGroups[documentId].chunkIds.push(chunk.document_chunk_id);
            }
            for (const [documentId, data] of Object.entries(documentGroups)) {
                attachments.push({
                    optionId: item.answer_option.id,
                    documentId,
                    pages: [...new Set(data.pages)],
                    comment: item.comment || '',
                    chunkIds: data.chunkIds,
                });
            }
        }
        this.logger.log(`Parsed attachments: ${attachments.length}`);
        return attachments;
    }
    async saveAiAnswerAttachments(projectId, attachments, validOptionIds) {
        const results = [];
        for (const attachment of attachments) {
            const { optionId, documentId, pages, comment, chunkIds } = attachment;
            try {
                if (!validOptionIds.includes(optionId)) {
                    results.push({
                        optionId,
                        documentId,
                        status: 'error',
                        message: 'Option ID does not belong to the current project question',
                    });
                    continue;
                }
                const { data: existingAnswer, error: answerFetchError } = await this.supabase
                    .from('project_ecovadis_answer')
                    .select('id')
                    .eq('projectId', projectId)
                    .eq('optionId', optionId)
                    .maybeSingle();
                if (answerFetchError) {
                    throw new Error(`Error fetching existing answer: ${answerFetchError.message}`);
                }
                let answerId;
                if (existingAnswer) {
                    answerId = existingAnswer.id;
                }
                else {
                    const { data: newAnswer, error: createAnswerError } = await this.supabase
                        .from('project_ecovadis_answer')
                        .insert({
                        projectId,
                        optionId,
                        response: 'true',
                    })
                        .select('id')
                        .single();
                    if (createAnswerError) {
                        throw new Error(`Error creating new answer: ${createAnswerError.message}`);
                    }
                    answerId = newAnswer.id;
                }
                const { data: existingLinks, error: existingLinksError } = await this.supabase
                    .from('project_ecovadis_linked_document_chunks')
                    .select('documentChunkId')
                    .eq('answerId', answerId);
                if (existingLinksError) {
                    throw new Error(`Error checking existing links: ${existingLinksError.message}`);
                }
                const existingChunkIds = existingLinks?.map((link) => link.documentChunkId) || [];
                const chunksToLink = chunkIds.filter((chunkId) => !existingChunkIds.includes(chunkId));
                if (chunksToLink.length > 0) {
                    const linkedChunksData = chunksToLink.map((chunkId, index) => ({
                        answerId,
                        documentChunkId: chunkId,
                        comment: index === 0 ? comment || null : null,
                        attachment_source: 'ai',
                    }));
                    const { data: linkedChunks, error: linkError } = await this.supabase
                        .from('project_ecovadis_linked_document_chunks')
                        .insert(linkedChunksData)
                        .select();
                    if (linkError) {
                        throw new Error(`Error linking document chunks: ${linkError.message}`);
                    }
                    results.push({
                        optionId,
                        documentId,
                        status: 'success',
                        answerId,
                        linkedChunksCount: linkedChunks.length,
                        newLinksCreated: true,
                        commentAdded: !!comment,
                    });
                }
                else {
                    results.push({
                        optionId,
                        documentId,
                        status: 'success',
                        answerId,
                        linkedChunksCount: 0,
                        newLinksCreated: false,
                        message: 'All document chunks already linked to this answer',
                    });
                }
            }
            catch (error) {
                results.push({
                    optionId,
                    documentId,
                    status: 'error',
                    message: error.message,
                });
            }
        }
        return results;
    }
    formatScoringFrameworkKey({ theme, indicator, }) {
        const formattedTheme = theme
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/[^a-z0-9\-]/g, '');
        const formattedIndicator = indicator
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/[^a-z0-9\-]/g, '');
        return `${formattedTheme}:${formattedIndicator}`;
    }
};
exports.EnhancedEcoVadisAnswerAgentService = EnhancedEcoVadisAnswerAgentService;
exports.EnhancedEcoVadisAnswerAgentService = EnhancedEcoVadisAnswerAgentService = EnhancedEcoVadisAnswerAgentService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        chat_gpt_service_1.ChatGptService,
        cohere_service_1.CohereService])
], EnhancedEcoVadisAnswerAgentService);
//# sourceMappingURL=answer-linking.service.js.map