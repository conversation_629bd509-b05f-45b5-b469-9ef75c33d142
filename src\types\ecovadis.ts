
export interface EcovadisTheme {
  id: string;
  title: string;
  description?: string;
}

export interface LinkedDocument {
  id: string;
  name: string;
  documentId?: string;
  page?: string;
  comment?: string;
}

export type QuestionStatus = 'pending' | 'complete'

export enum ECOVADIS_INDICATORS {
    'POLICIES',
    'ENDORSEMENTS',
    'MEASURES',
    'CERTIFICATIONS',
    'COVERAGE',
    'REPORTING',
    'WATCH_FINDINGS'
}

export interface EcovadisQuestion {
  // Backend fields from project_ecovadis_question
  projectQuestionId: string;
  status?: QuestionStatus;
  impact?: 'high' | 'medium' | 'low' | 'none' | 'N/A' | string;
  // Remove estimatedScore from question object - it's now fetched separately
  
  // Fields from ecovadis_question
  questionId: string;
  themeId: string;
  questionCode: string;
  question: string;
  questionName?: string;
  indicator: ECOVADIS_INDICATORS;
  
  // Related data
  options?: any[];
  gaps?: any[];
  
  // UI compatibility fields
  name?: string;
  text?: string;
  code?: string;
  topic?: QuestionTopic | string;
  
  // Added field for theme
  ecovadis_theme?: {
    title: string;
    id?: string;
  };
}

// GapItem type with all possible fields to handle both backend and UI needs
export type GapItem = {
  id: string;
  questionId?: string;
  projectId?: string;
  gaps?: {
    Title?: string;
    Description?: string;
    "Sample Text"?: string;
    "Related Document"?: {
      id: string;
      name: string;
    }[];
    "Recommended Actions"?: string;
    "Impact/Effort Evaluation"?: string;
    "Document Issue"?: boolean;
    [key: string]: any; // Allow other properties
  };
  documents?: {
    id: string;
    name: string;
  }[];
  resolved?: boolean;
  createdAt?: string;
  assigneeId?: string | null;
  deadline?: string | null;
  type?: 'evidence_gap' | 'invalid_document';
  
  // For UI compatibility with existing interface
  title?: string;
  description?: string;
  severity?: 'high' | 'medium' | 'low';
  isComplete?: boolean;
  relatedDocument?: LinkedDocument[];
  questionCode?: string;
  projectName?: string;
  topic?: string;
  assignedTo?: string | null;
  status?: string;
  
  // Add the ecovadis_question field with ecovadis_theme
  ecovadis_question?: {
    id: string;
    indicator: string;
    questionCode: string;
    ecovadis_theme?: {
      title: string;
      id?: string;
    };
  };
};

export type QuestionOption = {
  id: string;
  text: string;            // Maps to issutTitle in the database
  evidenceExamples: string; // Maps to instructions in the database
  selected: boolean;
  response?: string;        // Stores both selection state and text content
  attachedDocuments: AttachedDocument[];
  answerId: string;
  supportsAnswerText: boolean; // Indicates if option supports text input
};

export const getTextValueForAnswerOption = (response: string | null, supportsAnswerText: boolean): string => {
  if (!supportsAnswerText || !response || response === "true") {
    return "";
  }
  return response;
};

export const isCheckboxChecked = (response: string | null): boolean => {
  return response !== null;
};

export enum AttachmentSource{
  MANUAL = 'manual',
  AI = 'ai',
  IMPORT = 'import',
  SYSTEM = 'system'
}

export type AttachedDocument = {
  id: string;
  name: string;
  pages?: string;
  comment?: string;
  chunkId?: string;
  createdBy?: string;
  createdByName?: string;
  attachmentSource?: AttachmentSource
};

export type DocumentContent = any;
export type LinkedQuestion = any;
export type Document = any;
export type DocumentType = 'certificate' | 'sustainability_report' | 'policy' | 'supplier_code' | 'collective_agreement' | 'reporting_document' | 'other' | string;
export type DocumentStatus = 'complete' | 'processing' | 'failed' | 'draft' | 'invalid' | string;
export type QuestionScore = any;
export type QuestionTopic = '🌍 General' | '🌱 Environment' | '👥 Labor & Human Rights' | '🔷 Ethics' | '💼 Sustainable Procurement' | string;
export type QuestionImpact = any;

export type WorkspaceGap = {
  id: string;
  projectId: string;
  projectName: string;
  workspaceId: string;
  questionId: string;
  deadline: string | null;
  assigneeId: string | null;
  resolved: boolean | null;
  question: string;
  questionCode: string;
  questionName: string | null;
  createdAt: string;
  gaps: {
    Title: string;
    Description: string;
    "Sample Text"?: string;
    "Related Document"?: {
      id: string;
      name: string;
    }[]
    "Recommended Actions"?: string;
    "Impact/Effort Evaluation"?: string;
    "Document Issue"?: boolean;
    [key: string]: any; // Allow other properties
  };
  documents: {
    id: string;
    name: string;
  }[]
  status: QuestionStatus;
  type?: 'evidence_gap' | 'invalid_document';
  ecovadis_question?: {
    id: string;
    indicator: string;
    questionCode: string;
    ecovadis_theme?: {
      title: string;
      id?: string;
    };
  };
};

// Add a GroupedQuestion type
export interface GroupedQuestion {
  id?: string;
  name?: string;
  questions?: any[];
  [key: string]: any; // Allow other properties
}
