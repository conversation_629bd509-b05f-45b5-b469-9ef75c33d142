// @ts-expect-error TODO look into this later
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { corsHeaders } from '../_shared/cors.ts';
import { simpleSupabaseClient } from '../_shared/authValidator.ts';

serve(async (req) => {
    
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
  
  try {
      // Parse request body
      const requestBody = await req.json();
    
    // Extract chunk IDs - handle both direct array and {chunks: [...]} formats
    const chunkIds = Array.isArray(requestBody) ? requestBody : requestBody.chunks;
    console.log('Received chunk IDs:', chunkIds);

    // Validate input
    if (!Array.isArray(chunkIds) || chunkIds.length === 0) {
      return new Response(
        JSON.stringify({ error: 'Invalid input. Expected a non-empty array of document chunk UUIDs.' }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Look up document chunks by their IDs
    const { data: documentChunks, error: fetchError } = await simpleSupabaseClient
      .from('document_chunk')
      .select('*')
      .in('id', chunkIds);

    if (fetchError) {
      return new Response(
        JSON.stringify({ error: 'Error fetching document chunks', details: fetchError.message }),
        { 
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Prepare the response
    const foundIds = documentChunks.map(chunk => chunk.id);
    const missingIds = chunkIds.filter(id => !foundIds.includes(id));

    return new Response(
      JSON.stringify({
        success: true,
        summary: {
          totalRequested: chunkIds.length,
          found: documentChunks.length,
          missing: missingIds.length
        },
        chunks: documentChunks,
        missingIds: missingIds.length > 0 ? missingIds : undefined
      }),
      { 
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  } catch (error) {
    console.log(error);
    
    return new Response(
      JSON.stringify({ error: 'Unexpected error', details: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});