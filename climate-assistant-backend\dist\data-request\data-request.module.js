"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataRequestModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const data_request_service_1 = require("./data-request.service");
const workspace_entity_1 = require("../workspace/entities/workspace.entity");
const data_request_guard_1 = require("./data-request.guard");
const data_request_controller_1 = require("./data-request.controller");
const data_request_entity_1 = require("./entities/data-request.entity");
const datapoint_request_entity_1 = require("../datapoint/entities/datapoint-request.entity");
const prompts_module_1 = require("../prompts/prompts.module");
const project_module_1 = require("../project/project.module");
const users_module_1 = require("../users/users.module");
const workspace_module_1 = require("../workspace/workspace.module");
const datapoint_request_module_1 = require("../datapoint/datapoint-request.module");
const nestjs_1 = require("@bull-board/nestjs");
const jobs_1 = require("../types/jobs");
const bullAdapter_1 = require("@bull-board/api/bullAdapter");
const bull_1 = require("@nestjs/bull");
const datarequest_generation_entity_1 = require("./entities/datarequest-generation.entity");
const shared_datapoint_datarequest_service_1 = require("../shared/shared-datapoint-datarequest.service");
const llm_rate_limiter_module_1 = require("../llm-rate-limiter/llm-rate-limiter.module");
const supabase_module_1 = require("../auth/supabase/supabase.module");
let DataRequestModule = class DataRequestModule {
};
exports.DataRequestModule = DataRequestModule;
exports.DataRequestModule = DataRequestModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                data_request_entity_1.DataRequest,
                datapoint_request_entity_1.DatapointRequest,
                workspace_entity_1.Workspace,
                datarequest_generation_entity_1.DataRequestGeneration,
            ]),
            supabase_module_1.SupabaseAuthModule,
            prompts_module_1.PromptModule,
            project_module_1.ProjectModule,
            users_module_1.UsersModule,
            workspace_module_1.WorkspaceModule,
            llm_rate_limiter_module_1.LlmRateLimiterModule,
            bull_1.BullModule.registerQueue({ name: jobs_1.JobProcessor.DatapointGeneration }),
            bull_1.BullModule.registerQueue({ name: jobs_1.JobProcessor.DatapointReview }),
            nestjs_1.BullBoardModule.forFeature({
                name: jobs_1.JobProcessor.DatapointGeneration,
                adapter: bullAdapter_1.BullAdapter,
            }),
            nestjs_1.BullBoardModule.forFeature({
                name: jobs_1.JobProcessor.DatapointReview,
                adapter: bullAdapter_1.BullAdapter,
            }),
            (0, common_1.forwardRef)(() => datapoint_request_module_1.DatapointRequestModule),
        ],
        providers: [
            data_request_service_1.DataRequestService,
            shared_datapoint_datarequest_service_1.DatapointDataRequestSharedService,
            data_request_guard_1.DataRequestGuard,
        ],
        exports: [data_request_service_1.DataRequestService],
        controllers: [data_request_controller_1.DataRequestController],
    })
], DataRequestModule);
//# sourceMappingURL=data-request.module.js.map