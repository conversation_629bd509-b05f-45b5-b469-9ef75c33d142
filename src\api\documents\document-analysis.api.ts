import { supabase } from "@/integrations/supabase/client";

export interface DocumentAnalysisRequest {
  documentId: string;
}

export interface DocumentAnalysisResponse {
  result: any; // The JSON response from Dify
  document: {
    id: string;
    name: string;
    year: string;
  };
  workspace: {
    id: string;
    company_name: string;
  };
}

export const analyzeDocument = async (documentId: string): Promise<DocumentAnalysisResponse> => {
  try {
    // Get the session token for authorization
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      throw new Error('No active session found');
    }

    // Call the document-analysis Supabase function
    const { data, error: functionError } = await supabase.functions.invoke('document-analysis', {
      headers: {
        Authorization: `Bearer ${session.access_token}`
      },
      body: { documentId }
    });

    if (functionError) {
      console.error('Error calling document-analysis function:', functionError);
      throw new Error('Failed to analyze document');
    }

    return data;
  } catch (err) {
    console.error('Error analyzing document:', err);
    throw new Error('An unexpected error occurred while analyzing the document');
  }
};
