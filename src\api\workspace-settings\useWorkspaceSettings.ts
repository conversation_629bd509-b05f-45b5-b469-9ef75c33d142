import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import {
  fetchAllMembers,
  fetchWorkspaceDetail,
  inviteUsersToWorkspace,
  updateWorkspaceDetail,
  updateCompanyDetail,
  WorkspacePayload,
  CompanyPayload,
} from './workspace-settings.api';

import { IUserWithStatus } from '@/types/user';

export const WORKSPACE_USER_KEY = 'workspace-users';
export const FETCH_WORKSPACE_DETAIL = 'fetch-workspace';

export const useWorkspaceSettings = () => {
  const queryClient = useQueryClient();

  // Fetch workspace users
  const { data: workspaceUsers, isLoading: isWorkspaceUsersLoading } = useQuery<
    IUserWithStatus[] | null
  >({
    queryKey: [WORKSPACE_USER_KEY],
    queryFn: fetchAllMembers,
  });

  // Invite users mutation
  const { mutateAsync: inviteUsers, isPending: isInviteUsersInProgress } =
    useMutation({
      mutationFn: inviteUsersToWorkspace,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: [WORKSPACE_USER_KEY] });
      },
    });

  // Update workspace details mutation
  const {
    mutateAsync: updateWorkspace,
    isPending: isUpdateWorkspaceInProgress,
  } = useMutation({
    mutationFn: ({
      workspacePayload,
    }: {
      workspacePayload: Partial<WorkspacePayload>;
    }) => updateWorkspaceDetail(workspacePayload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [FETCH_WORKSPACE_DETAIL] });
    },
  });

  // Update company detail mutation
  const { mutateAsync: updateCompany, isPending: isUpdateCompanyInProgress } =
    useMutation({
      mutationFn: ({
        companyPayload,
      }: {
        companyPayload: Partial<CompanyPayload>;
      }) => updateCompanyDetail(companyPayload),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: [FETCH_WORKSPACE_DETAIL] });
      },
    });

  const { data: workspaceDetail } = useQuery<Partial<WorkspacePayload>>({
    queryKey: [FETCH_WORKSPACE_DETAIL],
    queryFn: fetchWorkspaceDetail,
  });

  return {
    workspaceUsers,
    isWorkspaceUsersLoading,
    inviteUsers,
    isInviteUsersInProgress,
    updateWorkspace,
    isUpdateWorkspaceInProgress,
    updateCompany,
    isUpdateCompanyInProgress,
    workspaceDetail,
  };
};
