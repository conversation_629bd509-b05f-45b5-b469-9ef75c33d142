
import { LoaderCircle } from 'lucide-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useState } from 'react';
import * as z from 'zod';

import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form.tsx';
import { Input } from '@/components/ui/input.tsx';
import { Button } from '@/components/ui/button.tsx';
import { useAuthentication } from '@/api/authentication/authentication.query.ts';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ILoginFormProps {
  switchToLogin: () => void;
  switchToEmailSent: () => void;
}

const formSchema = z.object({
  email: z.string().email({ message: 'Please provide a valid email address' }),
});

const ForgotPassword = ({
  switchToLogin,
  switchToEmailSent,
}: ILoginFormProps) => {
  const { sendPasswordResetEmail, sendPasswordResetEmailLoading } =
    useAuthentication();
  const [error, setError] = useState<string | null>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setError(null);
    try {
      console.log("Submitting password reset for:", values.email);
      await sendPasswordResetEmail(values.email);
      switchToEmailSent();
    } catch (err: any) {
      console.error("Password reset error:", err);
      setError(err.message || "Failed to send password reset email. Please try again.");
    }
  }

  return (
    <div className={`flex flex-col w-full max-w-[380px]`}>
      <div className={`font-semibold text-3xl text-center mb-8`}>
        Forgot Password?
        <p className="text-xl text-gray-400">
          Enter your email address for password reset
        </p>
      </div>
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="Email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="text-center">
            <Button
              type="submit"
              disabled={sendPasswordResetEmailLoading}
              variant="darkBlue"
              className="mr-3 mt-3 w-full rounded-3xl"
              style={{ backgroundColor: '#143560' }}
            >
              {sendPasswordResetEmailLoading && (
                <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
              )}
              Send password reset link
            </Button>
            <Button
              variant="link"
              onClick={(e) => {
                e.preventDefault();
                switchToLogin();
              }}
              className="text-sm"
              style={{ textAlign: 'center' }}
            >
              Back to Log in
            </Button>
          </div>
        </form>
      </Form>
      <div className="text-center mt-4 text-xs">
        If you have any questions, feel free to reach out to us via{' '}
        <a href="mailto:<EMAIL>"><EMAIL></a>. We will
        review your reply within one business day.
      </div>
    </div>
  );
};

export default ForgotPassword;
