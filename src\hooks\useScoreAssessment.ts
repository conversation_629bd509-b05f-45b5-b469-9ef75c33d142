import { requestEcovadisScoreAssessment } from '@/api/ecovadis/ecovadis.api';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';

export const useScoreAssessment = (projectId: string, projectQuestionId?: string) => {
  const queryClient = useQueryClient();

  // Query to fetch existing score data
  const { data: scoreData, isLoading: isLoadingScore } = useQuery({
    queryKey: ['questionScore', projectQuestionId],
    queryFn: async () => {
      if (!projectQuestionId) return null;
      
      const { data, error } = await supabase
        .from('project_ecovadis_question_score')
        .select('*')
        .eq('questionId', projectQuestionId)
        .single();
      
      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        throw error;
      }
      
      // Process breakdown if it exists
      if (data?.breakdown) {
        try {
          const parsedBreakdown = JSON.parse(data.breakdown);
          data.breakdown = Object.values(parsedBreakdown).join('\n\n');
        } catch (error) {
          console.error('Error processing score breakdown:', error);
        }
      }
      
      return data;
    },
    enabled: !!projectQuestionId,
  });

  // Use React Query's useMutation for score assessment
  const scoreAssessmentMutation = useMutation({
    mutationFn: ({ projectQuestionId }: { projectQuestionId: string }) => {
      // Call the score assessment API
      return requestEcovadisScoreAssessment(projectId, projectQuestionId);
    },
    onSuccess: (data) => {
      // Invalidate queries to refresh the score data
      queryClient.invalidateQueries({ queryKey: ['questionScore', projectQuestionId] });
      
      // Show success toast notification
      toast({
        title: "Score Assessment Complete",
        description: "Successfully assessed the estimated score for this question.",
        variant: "success",
      });
    },
    onError: (error) => {
      console.error("Score assessment failed:", error);
      
      // Show error toast notification
      toast({
        title: "Score Assessment Failed",
        description: "There was an error assessing the score. Please try again.",
        variant: "destructive",
      });
    }
  });

  const handleScoreAssessment = async (projectQuestionId: string) => {
    scoreAssessmentMutation.mutate({ projectQuestionId });
  };

  return {
    handleScoreAssessment,
    isLoading: scoreAssessmentMutation.isPending,
    scoreData,
    isLoadingScore
  };
};