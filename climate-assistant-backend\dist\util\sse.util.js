"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSseStream = createSseStream;
const rxjs_1 = require("rxjs");
function createSseStream(response, eventStream, initialEvent) {
    response.setHeader('Content-Type', 'text/event-stream');
    response.setHeader('Cache-Control', 'no-cache');
    response.setHeader('Connection', 'keep-alive');
    return new rxjs_1.Observable((subscriber) => {
        if (initialEvent) {
            subscriber.next(initialEvent);
        }
        const subscription = eventStream.subscribe({
            next: (event) => {
                subscriber.next(event);
            },
            error: (error) => {
                subscriber.error(error);
            },
            complete: () => {
                subscriber.complete();
            },
        });
        return () => subscription.unsubscribe();
    });
}
//# sourceMappingURL=sse.util.js.map