import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import throat from 'throat';
import { ChatCompletionMessageParam } from 'openai/resources/chat';
import { LLM_MODELS } from '../../constants';
import { LlmRateLimiterService } from '../../llm-rate-limiter/llm-rate-limiter.service';
import { DocumentStatus } from 'src/document/entities/document.entity';

interface EcoVadisChunkMatches {
  chunkContent: string;
  themeMatches: string[];
  questionMatches: string[];
  indicatorMatches: string[];
}

interface EcoVadisQuestionMatch {
  questionCode: string;
  relevanceScore: number;
}

interface EcoVadisLinkResponse {
  inputTokensUsed: number;
  outputTokensUsed: number;
  costForDocument: number;
  totalLinksCreated: number;
  timeToGenerate: string;
  numberOfChunks: number;
  chunks: EcoVadisChunkMatches[];
}

interface ProjectQuestionData {
  id: string;
  questionId: string;
  ecovadis_question: {
    id: string;
    questionCode: string;
    question: string;
    questionName: string;
    indicator: string;
    themeId: string;
    ecovadis_theme: {
      id: string;
      title: string;
      description: string;
    };
    ecovadis_answer_option: Array<{
      id: string;
      issueTitle: string;
      instructions: string;
    }>;
  };
}

@Injectable()
export class EcoVadisQuestionChunkLinkingService {
  private supabase: SupabaseClient;
  private readonly logger = new Logger(
    EcoVadisQuestionChunkLinkingService.name
  );
  private readonly limit = throat(4);

  constructor(
    private configService: ConfigService,
    private readonly llmRateLimitService: LlmRateLimiterService
  ) {
    this.supabase = createClient(
      this.configService.get<string>('SUPABASE_APP_URL'),
      this.configService.get<string>('SUPABASE_SERVICE_KEY')
    );
  }

  getClient(): SupabaseClient {
    return this.supabase;
  }

  /**
   * Links document chunks to EcoVadis questions using LLM-based classification
   * Quality optimization strategies:
   * 1. Hierarchical Classification: Theme -> Question classification reduces false positives
   * 2. Relevance Scoring: Each match includes a confidence score for ranking
   * 3. Content Quality Filtering: Skip chunks that are too short or meaningless
   * 4. Batch Processing: Process chunks in batches to optimize performance
   * 5. Error Recovery: Continue processing even if individual chunks fail
   * 6. Deduplication: Prevent duplicate links from being created
   * 7. Evidence Extraction: Store relevant text portions for audit trails
   */
  async linkDocumentChunksToEcoVadisQuestions(
    documentId: string,
    projectId: string,
    userId: string,
    maxNumberOfChunks?: number
  ): Promise<EcoVadisLinkResponse> {
    this.logger.log(
      `Start linking EcoVadis Questions to Chunks for DocumentId ${documentId}, ProjectId ${projectId}`
    );

    const response: EcoVadisLinkResponse = {
      inputTokensUsed: 0,
      outputTokensUsed: 0,
      costForDocument: 0,
      totalLinksCreated: 0,
      timeToGenerate: '0s',
      numberOfChunks: 0,
      chunks: [],
    };

    const startTime = Date.now();
    const createdAtBatch = new Date();

    try {
      await this.updateDocumentStatus(documentId, DocumentStatus.LinkingData);

      const documentChunks = await this.getDocumentChunks(
        documentId,
        maxNumberOfChunks
      );
      this.logger.log(
        `${documentId}: Found ${documentChunks.length} Document Chunks`
      );
      response.numberOfChunks = documentChunks.length;

      if (documentChunks.length === 0) {
        await this.updateDocumentStatus(
          documentId,
          DocumentStatus.LinkingDataFinished
        );
        return response;
      }

      const projectQuestions = await this.getProjectQuestions(projectId);
      this.logger.log(
        `${documentId}: Found ${projectQuestions.length} Project Questions`
      );

      if (projectQuestions.length === 0) {
        this.logger.warn(
          `${documentId}: No project questions found for project ${projectId}`
        );
        await this.updateDocumentStatus(
          documentId,
          DocumentStatus.LinkingDataFinished
        );
        return response;
      }

      const BATCH_SIZE = 10;
      const chunksToProcess = documentChunks.slice(
        0,
        maxNumberOfChunks || documentChunks.length
      );

      for (let i = 0; i < chunksToProcess.length; i += BATCH_SIZE) {
        const batch = chunksToProcess.slice(i, i + BATCH_SIZE);

        const promises = batch.map((chunk) => {
          return this.limit(async () => {
            return await this.processChunkForEcoVadisLinking(
              chunk,
              documentId,
              projectQuestions,
              userId,
              createdAtBatch,
              response,
              projectId
            );
          });
        });

        await Promise.all(promises);
      }

      this.logger.log(
        `TOTAL TOKEN USAGE FOR ECOVADIS LINKING ${documentId}: inputTokens ${response.inputTokensUsed}, outputTokens: ${response.outputTokensUsed}, cost: ${response.costForDocument}`
      );

      const endTime = Date.now();
      response.timeToGenerate = `${(endTime - startTime) / 1000}s`;

      await this.updateDocumentStatus(
        documentId,
        DocumentStatus.LinkingDataFinished
      );
      this.logger.log(`EcoVadis Question Linking Finished ${documentId}`);

      return response;
    } catch (error) {
      this.logger.error(
        `Error linking EcoVadis questions for document ${documentId}:`,
        error
      );

      const documentExists = await this.checkDocumentExists(documentId);
      if (documentExists) {
        // await this.updateDocumentStatus(documentId, 'ErrorProcessing');
      }

      throw error;
    }
  }

  private async processChunkForEcoVadisLinking(
    chunk: any,
    documentId: string,
    projectQuestions: ProjectQuestionData[],
    userId: string,
    createdAtBatch: Date,
    response: EcoVadisLinkResponse,
    projectId: string
  ): Promise<void> {
    this.logger.debug(
      `${documentId}: Linking chunk ${chunk.id}, Length: ${chunk.content.length}`
    );

    const chunkContent = chunk.content;
    const contentQuality = this.calculateContentQuality(chunkContent);
    this.logger.debug(
      `${documentId}: Chunk ${chunk.id} quality score: ${contentQuality.toFixed(2)}`
    );

    this.logger.debug(
      `${documentId}: ${chunk.id} Start EcoVadis Theme Classification`
    );

    const themeClassificationPrompt = this.generateThemeClassificationPrompt(
      chunkContent,
      projectQuestions
    );

    let themeClassificationResponse;
    try {
      themeClassificationResponse =
        await this.llmRateLimitService.handleRequest({
          model: LLM_MODELS['o4-mini'],
          messages: themeClassificationPrompt,
          json: true,
          temperature: 0,
        });
    } catch (llmError) {
      this.logger.error(
        `${documentId}: Chunk ${chunk.id} - LLM request failed for theme classification:`,
        llmError instanceof Error ? llmError.stack : llmError
      );
      return;
    }

    if (themeClassificationResponse.status === 400) {
      this.logger.error(
        `${documentId}: Chunk: ${chunk.id} Theme Classification failed with status 400`
      );
      console.log(themeClassificationResponse.response);
      return;
    }

    if (!themeClassificationResponse.response) {
      this.logger.error(
        `${documentId}: Chunk ${chunk.id} - Empty response from LLM for theme classification`
      );
      return;
    }

    response.inputTokensUsed +=
      themeClassificationResponse.token.prompt_tokens || 0;
    response.outputTokensUsed +=
      themeClassificationResponse.token.completion_tokens || 0;
    response.costForDocument +=
      themeClassificationResponse.token.total_cost || 0;

    const matchedThemes: string[] =
      themeClassificationResponse.response.themeMatches?.map((theme: string) =>
        theme.trim()
      ) || [];

    const chunkMatches: EcoVadisChunkMatches = {
      chunkContent: chunkContent,
      themeMatches: matchedThemes,
      questionMatches: [],
      indicatorMatches: [],
    };

    if (matchedThemes && matchedThemes.length > 0) {
      this.logger.debug(
        `${documentId}: Chunk: ${chunk.id} Themes Matched: ${matchedThemes.join(', ')}`
      );

      const themeRelevantQuestions = projectQuestions.filter((pq) =>
        matchedThemes.includes(pq.ecovadis_question.ecovadis_theme.id)
      );

      if (themeRelevantQuestions.length > 0) {
        await this.processChunkForQuestionLinking({
          chunk,
          chunkContent,
          documentId,
          themeRelevantQuestions,
          userId,
          createdAtBatch,
          response,
          chunkMatches,
          projectId,
          matchedThemes,
        });
      }

      await this.updateChunkMatches(chunk.id, chunkMatches);
      response.chunks.push(chunkMatches);
    }
  }

  private async processChunkForQuestionLinking({
    chunk,
    chunkContent,
    documentId,
    themeRelevantQuestions,
    userId,
    createdAtBatch,
    response,
    chunkMatches,
    projectId,
    matchedThemes,
  }: {
    chunk: any;
    chunkContent: string;
    documentId: string;
    themeRelevantQuestions: ProjectQuestionData[];
    userId: string;
    createdAtBatch: Date;
    response: EcoVadisLinkResponse;
    chunkMatches: EcoVadisChunkMatches;
    projectId: string;
    matchedThemes: string[];
  }): Promise<void> {
    this.logger.debug(
      `${documentId}: Chunk: ${chunk.id} Start Question Classification with ${themeRelevantQuestions.length} questions`
    );

    const questionClassificationPrompt =
      this.enhanceQuestionClassificationWithContext({
        chunkContent,
        matchedThemes,
        themeRelevantQuestions,
      });

    let questionClassificationResponse;
    try {
      questionClassificationResponse =
        await this.llmRateLimitService.handleRequest({
          model: LLM_MODELS['o4-mini'],
          messages: questionClassificationPrompt,
          json: true,
          temperature: 0,
        });
    } catch (llmError) {
      this.logger.error(
        `${documentId}: Chunk ${chunk.id} - LLM request failed for question classification:`,
        llmError instanceof Error ? llmError.stack : llmError
      );
      return;
    }

    if (questionClassificationResponse.status === 400) {
      this.logger.error(
        `${documentId}: Chunk: ${chunk.id} Question Classification failed with status 400`
      );
      return;
    }

    if (!questionClassificationResponse.response) {
      this.logger.error(
        `${documentId}: Chunk ${chunk.id} - Empty response from LLM for question classification`
      );
      return;
    }

    response.inputTokensUsed +=
      questionClassificationResponse.token.prompt_tokens || 0;
    response.outputTokensUsed +=
      questionClassificationResponse.token.completion_tokens || 0;
    response.costForDocument +=
      questionClassificationResponse.token.total_cost || 0;

    const questionMatches: EcoVadisQuestionMatch[] =
      questionClassificationResponse.response.matchedQuestions || [];

    this.logger.debug(
      `${documentId}: Chunk: ${chunk.id} Questions matched: ${questionMatches.length}`
    );

    if (questionMatches && questionMatches.length > 0) {
      const validatedMatches = this.validateAndEnrichMatches(questionMatches);
      const linksToCreate: {
        projectQuestionId: string;
        documentChunkId: string;
        relevanceScore: number;
        createdBy: string;
      }[] = [];

      for (const questionMatch of validatedMatches) {
        const matchingProjectQuestion = themeRelevantQuestions.find(
          (pq) =>
            pq.ecovadis_question.questionCode === questionMatch.questionCode
        );

        if (matchingProjectQuestion) {
          linksToCreate.push({
            projectQuestionId: matchingProjectQuestion.id,
            documentChunkId: chunk.id,
            relevanceScore: questionMatch.relevanceScore || 0.5,
            createdBy: userId,
          });

          chunkMatches.questionMatches.push(questionMatch.questionCode);

          this.logger.debug(
            `${documentId}: Chunk: ${chunk.id} Question matched: ${questionMatch.questionCode} (score: ${questionMatch.relevanceScore})`
          );
        } else {
          this.logger.warn(
            `${documentId}: Chunk: ${chunk.id} Question ${questionMatch.questionCode} not found in project questions`
          );
        }
      }

      if (linksToCreate.length > 0) {
        await this.createQuestionChunkLinks(linksToCreate);
        response.totalLinksCreated += linksToCreate.length;
      }
    }
  }

  private generateThemeClassificationPrompt(
    chunkContent: string,
    projectQuestions: ProjectQuestionData[]
  ): ChatCompletionMessageParam[] {
    const themes = Array.from(
      new Set(
        projectQuestions.map((pq) => ({
          id: pq.ecovadis_question.ecovadis_theme.id,
          title: pq.ecovadis_question.ecovadis_theme.title,
          description: pq.ecovadis_question.ecovadis_theme.description,
        }))
      )
    );

    const themesContext = themes
      .map(
        (theme) => `Theme ID: ${theme.id}\n${theme.title}: ${theme.description}`
      )
      .join('\n');

    return [
      {
        role: 'system',
        content: `You are an expert EcoVadis documentation classifier. Your job is to read one page of a document and decide which EcoVadis questionnaire themes that page could reasonably help answer.

## Inputs
- themesContext: a list of available EcoVadis themes (IDs and titles). Use the Theme **ID** values exactly as provided.
  ${themesContext}

- chunkContent: the full text of a single document page (possibly noisy OCR).
  "${chunkContent}"

## Output (strict)
Return **only** a JSON object with this shape:
{
  "themeMatches": ["<Theme ID>", "..."]
}

- Use **Theme IDs** (not titles).
- Preserve the order that themes appear in 'themesContext'.
- If nothing qualifies, return: { "themeMatches": [] }
- Do not include explanations or any other text.

## Decision principles (think through silently; output only JSON)
Be **conservative but thorough**: include a theme if the page content could **reasonably contribute** to answering EcoVadis questions for that theme. Base your decision **only** on the provided page—do not infer from company type or external knowledge.

### What “reasonably contribute” means
A page helps a theme if it contains at least one of:
- **Policies/commitments/charters/codes**
- **Procedures/processes/controls**, roles & responsibilities, governance
- **Objectives/targets/KPIs/metrics**, action plans, results
- **Risk assessments/audits/due diligence**, training, communications
- **Certifications/standards** (e.g., ISO references) linked to a theme

### Theme cue map (non-exhaustive cues; use as signals, not rules)
- **Environment**: ISO 14001/50001, energy, GHG/emissions, climate, waste, recycling, water, effluents, chemicals, biodiversity, noise, spills, environmental compliance, life-cycle, eco-design.
- **Labor & Human Rights**: ISO 45001, health & safety, PPE, incidents/LTIFR, working hours/wages, freedom of association, DEI/non-discrimination, harassment, child/forced labor, grievance/whistleblowing for HR issues, training, human rights due diligence.
- **Ethics**: anti-corruption/bribery, gifts/hospitality, conflicts of interest, fraud, antitrust/competition, sanctions, data privacy/IT security (if framed as business ethics/compliance), code of ethics, ethics hotline (non-HR scope), disciplinary measures.
- **Sustainable Procurement**: supplier code of conduct, supplier screening/risk mapping, questionnaires, audits, corrective action plans, conflict minerals, supply-chain due diligence (ESG expectations for suppliers), procurement policy, supplier training/engagement.

### Negative/neutral content (usually **do not** match)
Covers, tables of contents, page numbers/footers, generic marketing, contact info, image-only pages without meaningful captions, legal boilerplate (copyright, trademarks), empty/garbled OCR.

### Ambiguity handling
- If a signal clearly maps to a theme → include that theme.
- If signals are weak/ambiguous and not tied to a theme → exclude.
- If the page mixes themes → include all applicable themes (minimal set).
- If the page references a policy or procedure by name (even without details) and it obviously aligns with a theme → include that theme.

## Quality checks before output
- Use **only** theme IDs from 'themesContext'.
- No duplicates; keep original 'themesContext' order.
- If 'chunkContent' is empty, image-only with no text, or purely boilerplate → return an empty array.

## Examples (for clarity)
Example when matches exist:
{
  "themeMatches": ["Environment", "Sustainable Procurement"]
}

Example when no matches:
{
  "themeMatches": []
}
`,
      },
    ];
  }

  // Helper methods
  private async getDocumentChunks(
    documentId: string,
    maxChunks?: number
  ): Promise<any[]> {
    let query = this.supabase
      .from('document_chunk')
      .select('*')
      .eq('documentId', documentId)
      .order('page', { ascending: true });

    if (maxChunks) {
      query = query.limit(maxChunks);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to get document chunks: ${error.message}`);
    }

    return data || [];
  }

  private async getProjectQuestions(
    projectId: string
  ): Promise<ProjectQuestionData[]> {
    const { data, error } = await this.supabase
      .from('project_ecovadis_question')
      .select(
        `
        id,
        questionId,
        ecovadis_question!inner (
          id,
          questionCode,
          question,
          questionName,
          indicator,
          themeId,
          ecovadis_theme!inner (
            id,
            title,
            description
          ),
          ecovadis_answer_option (
            id,
            issueTitle,
            instructions
          )
        )
      `
      )
      .eq('projectId', projectId);

    if (error) {
      throw new Error(`Failed to get project questions: ${error.message}`);
    }

    return (data as any) || [];
  }

  private async updateDocumentStatus(
    documentId: string,
    status: string
  ): Promise<void> {
    const { error } = await this.supabase
      .from('document')
      .update({ status })
      .eq('id', documentId);

    if (error) {
      this.logger.error(`Failed to update document status: ${error.message}`);
    }
  }

  private async checkDocumentExists(documentId: string): Promise<boolean> {
    const { data, error } = await this.supabase
      .from('document')
      .select('id')
      .eq('id', documentId)
      .single();

    return !error && !!data;
  }

  private async updateChunkMatches(
    chunkId: string,
    matches: EcoVadisChunkMatches
  ): Promise<void> {
    const { error } = await this.supabase
      .from('document_chunk')
      .update({ matchingsJson: JSON.stringify(matches) })
      .eq('id', chunkId);

    if (error) {
      this.logger.error(`Failed to update chunk matches: ${error.message}`);
    }
  }

  private async createQuestionChunkLinks(
    links: {
      projectQuestionId: string;
      documentChunkId: string;
      relevanceScore: number;
      createdBy: string;
    }[]
  ): Promise<void> {
    const { error } = await this.supabase
      .from('project_ecovadis_question_linked_document_chunk')
      .upsert(links, {
        onConflict: 'projectQuestionId, documentChunkId',
        ignoreDuplicates: false,
      });

    if (error) {
      this.logger.error(
        `Failed to create question chunk links: ${error.message}, links: ${JSON.stringify(links)}`
      );
      throw error;
    }
  }

  // Quality optimization methods
  private isChunkMeaningful(content: string): boolean {
    if (content.length < 100) return false;

    const meaningfulText = content.replace(/[^a-zA-Z\s]/g, '').trim();
    if (meaningfulText.length < content.length * 0.3) return false;

    const words = meaningfulText.toLowerCase().split(/\s+/);
    const uniqueWords = new Set(words);
    if (uniqueWords.size < words.length * 0.5) return false;

    return true;
  }

  private calculateContentQuality(content: string): number {
    let score = 0.5;

    const length = content.length;
    if (length >= 500 && length <= 2000) score += 0.2;
    else if (length >= 200 && length < 500) score += 0.1;

    const sustainabilityKeywords = [
      'sustainability',
      'environmental',
      'social',
      'governance',
      'ESG',
      'carbon',
      'emissions',
      'energy',
      'waste',
      'water',
      'biodiversity',
      'human rights',
      'labor',
      'ethics',
      'compliance',
      'policy',
      'target',
      'goal',
      'KPI',
      'metric',
      'assessment',
      'audit',
    ];

    const lowerContent = content.toLowerCase();
    const foundKeywords = sustainabilityKeywords.filter((keyword) =>
      lowerContent.includes(keyword)
    ).length;

    score += Math.min(foundKeywords * 0.05, 0.3);

    return Math.min(score, 1.0);
  }

  private enhanceQuestionClassificationWithContext({
    chunkContent,
    matchedThemes,
    themeRelevantQuestions,
  }: {
    chunkContent: string;
    matchedThemes: string[];
    themeRelevantQuestions: ProjectQuestionData[];
  }): ChatCompletionMessageParam[] {
    const questionsContext = themeRelevantQuestions
      .map((pq) => {
        const answerOptionsText =
          pq.ecovadis_question.ecovadis_answer_option
            ?.map(
              (option) => `    - ${option.issueTitle}: ${option.instructions}`
            )
            .join('\n') || '    No specific answer options available';

        return `Question Code: ${pq.ecovadis_question.questionCode}
      Theme: ${pq.ecovadis_question.ecovadis_theme.title}
      Question: "${pq.ecovadis_question.question}"
      Name: ${pq.ecovadis_question.questionName}
      Indicator: ${pq.ecovadis_question.indicator}
      Answer Options:
${answerOptionsText}`;
      })
      .join('\n\n');

    return [
      {
        role: 'system',
        content: `You are an expert EcoVadis question matcher. Your job is to read one page of content and decide which EcoVadis **questions** (from already-matched themes) this page could help answer, and to assign a calibrated relevance score for each included question.

## Inputs
- matchedThemes: a comma-separated list of EcoVadis themes that this page was already matched to. Use for context only.
  ${matchedThemes.join(', ')}

- chunkContent: the full text of a single document page (may include noisy OCR).
  ${chunkContent}

- questionsContext: the full set of EcoVadis questions for the matched themes (includes question codes, wording, and specific answer options with instructions). Only consider these questions.
  ${questionsContext}

## Task
Identify which questions the page could help answer. Be **precise and conservative** — include a question only if the content provides **meaningful value** for answering it. Pay special attention to the answer options and their instructions, as they provide detailed guidance on what evidence is needed for each question.

## Output (strict)
Return **only** a JSON object with this shape:
{
  "matchedQuestions": [
    {
      "questionCode": "<Question Code>",
      "relevanceScore": 0.85
    }
  ]
}

- Use **questionCode** exactly as provided in questionsContext.
- **relevanceScore** is a float in **[0.0, 1.0]** (use up to two decimals).
- Keep the **order of questions** as they appear in questionsContext.
- No duplicates.
- If no question meets the threshold (≥ 0.30), return:
{
  "matchedQuestions": []
}
- Do **not** include explanations, quotes, evidence spans, or any extra fields in the output.

## Scoring rubric (apply consistently)
- **Direct evidence (0.80–1.00):** The page directly answers the question (e.g., policy/commitment text, named procedure/role, KPI/target/result, certification clearly tied to the question).
- **Supporting evidence (0.60–0.79):** The page provides substantial context that strongly supports answering the question but isn’t a direct answer (e.g., mentions implementation steps without full detail, partial KPI context).
- **Indirect relevance (0.30–0.59):** Related concepts are mentioned but are incomplete, generic, or peripheral.
- **Minimal/none (<0.30):** Exclude from output.

## What counts as evidence (signals; use as guidance, not rigid rules)
Count it as relevant if the page includes at least one of:
- **Policies/commitments/codes/charters** (by name or verbatim).
- **Procedures/processes/controls**; roles & responsibilities; governance structures.
- **Objectives/targets/KPIs/metrics**; action plans; results/achievements.
- **Risk assessments/audits/due diligence**; training; communications/awareness.
- **Certifications/standards** (e.g., ISO) clearly linked to the question’s theme/scope.

## Guardrails & exclusions
- Base decisions **only** on this page (chunkContent). Do not use external knowledge or assume content from other pages.
- Ignore generic marketing, cover pages, tables of contents, contact info, image-only pages without meaningful captions, legal boilerplate (copyright/trademarks), page numbers/footers, or garbled OCR with no substantive meaning.
- If chunkContent is empty, image-only without text, or purely boilerplate → return { "matchedQuestions": [] }.

## Internal reasoning (silent)
Think through: map question scope and answer option requirements → scan page for concrete evidence that aligns with answer options → calibrate score via rubric → keep only items ≥ 0.30. Do **not** output your reasoning or text extracts; output **only** the JSON object.

## Examples
Example with matches:
{
  "matchedQuestions": [
    { "questionCode": "Q1.1", "relevanceScore": 0.88 },
    { "questionCode": "Q2.3", "relevanceScore": 0.62 }
  ]
}

Example with no matches:
{
  "matchedQuestions": []
}`,
      },
    ];
  }

  private validateAndEnrichMatches(
    matches: EcoVadisQuestionMatch[]
  ): EcoVadisQuestionMatch[] {
    return matches
      .filter((match) => match.relevanceScore >= 0.3)
      .map((match) => ({
        ...match,
        relevanceScore: Math.max(0, Math.min(1, match.relevanceScore)),
      }))
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, 10);
  }
}
