
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.7'

// Define CORS headers for browser requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Create a Supabase client using the environment variables
const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Handle OPTIONS requests for CORS preflight
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders,
      status: 204,
    })
  }

  try {
    // Parse the request body
    const { questionId, updates } = await req.json()
    
    // Validate input parameters
    if (!questionId) {
      return new Response(
        JSON.stringify({ error: 'Question ID is required' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Normalize status to lowercase if it exists
    if (updates.status) {
      updates.status = updates.status.toLowerCase()
    }

    console.log(`Updating EcoVadis question ${questionId} with:`, updates)

    // Update the project_ecovadis_question record in the database
    const { data, error } = await supabase
      .from('project_ecovadis_question')
      .update(updates)
      .eq('id', questionId)
      .select()

    if (error) {
      console.error('Error updating question:', error)
      return new Response(
        JSON.stringify({ error: `Failed to update question: ${error.message}` }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }

    // Return the updated question data
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Question updated successfully',
        data 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )
  } catch (error) {
    console.error('Unexpected error processing request:', error)
    return new Response(
      JSON.stringify({ error: `Server error: ${error.message}` }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
