"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDataSourceWithVectorSupport = exports.lovableAppRegex = exports.getRedisHost = exports.getDBHost = exports.getEnvFilePath = exports.isProduction = exports.isDevelopment = exports.NODE_ENV = void 0;
const typeorm_1 = require("typeorm");
exports.NODE_ENV = process.env.NODE_ENV;
exports.isDevelopment = exports.NODE_ENV === 'development';
exports.isProduction = exports.NODE_ENV === 'production';
const getEnvFilePath = () => {
    return exports.isProduction ? undefined : '../.env';
};
exports.getEnvFilePath = getEnvFilePath;
const getDBHost = () => {
    return exports.isProduction ? process.env.BACKEND_DB_HOST : 'localhost';
};
exports.getDBHost = getDBHost;
const getRedisHost = () => {
    return exports.isProduction ? process.env.REDIS_HOST : 'localhost';
};
exports.getRedisHost = getRedisHost;
exports.lovableAppRegex = /^https:\/\/.*\.(lovable\.app|lovableproject\.com)$/;
const createDataSourceWithVectorSupport = (options) => {
    const dataSource = new typeorm_1.DataSource(options);
    dataSource.driver.supportedDataTypes.push('vector');
    dataSource.driver.withLengthColumnTypes.push('vector');
    return dataSource;
};
exports.createDataSourceWithVectorSupport = createDataSourceWithVectorSupport;
//# sourceMappingURL=env-helper.js.map