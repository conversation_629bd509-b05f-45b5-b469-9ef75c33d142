"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EcoVadisSustainabilityIssue = exports.ProjectEcoVadisLinkedDocumentChunks = exports.ProjectEcoVadisAnswer = exports.EcoVadisAnswerOption = exports.ProjectEcoVadisGaps = exports.ProjectEcoVadisQuestion = exports.ProjectEcoVadisQuestionScoreHistory = exports.ProjectEcoVadisQuestionScore = exports.EcoVadisQuestion = exports.ProjectEcoVadisTheme = exports.EcoVadisTheme = exports.EcoVadisScoreLevel = exports.ImpactScore = exports.EcoVadisIndicator = exports.ProjectType = void 0;
const document_chunk_entity_1 = require("../../document/entities/document-chunk.entity");
const project_entity_1 = require("../../project/entities/project.entity");
const user_entity_1 = require("../../users/entities/user.entity");
const typeorm_1 = require("typeorm");
var ProjectType;
(function (ProjectType) {
    ProjectType["CSRD"] = "CSRD";
    ProjectType["EcoVadis"] = "EcoVadis";
})(ProjectType || (exports.ProjectType = ProjectType = {}));
var EcoVadisIndicator;
(function (EcoVadisIndicator) {
    EcoVadisIndicator["POLICIES"] = "POLICIES";
    EcoVadisIndicator["ENDORSEMENTS"] = "ENDORSEMENTS";
    EcoVadisIndicator["MEASURES"] = "MEASURES";
    EcoVadisIndicator["CERTIFICATIONS"] = "CERTIFICATIONS";
    EcoVadisIndicator["COVERAGE"] = "COVERAGE";
    EcoVadisIndicator["REPORTING"] = "REPORTING";
    EcoVadisIndicator["WATCH_FINDINGS"] = "WATCH_FINDINGS";
})(EcoVadisIndicator || (exports.EcoVadisIndicator = EcoVadisIndicator = {}));
var ImpactScore;
(function (ImpactScore) {
    ImpactScore["High"] = "High";
    ImpactScore["Medium"] = "Medium";
    ImpactScore["Low"] = "Low";
})(ImpactScore || (exports.ImpactScore = ImpactScore = {}));
var EcoVadisScoreLevel;
(function (EcoVadisScoreLevel) {
    EcoVadisScoreLevel["Outstanding"] = "Outstanding";
    EcoVadisScoreLevel["Advanced"] = "Advanced";
    EcoVadisScoreLevel["Good"] = "Good";
    EcoVadisScoreLevel["Partial"] = "Partial";
    EcoVadisScoreLevel["Insufficient"] = "Insufficient";
})(EcoVadisScoreLevel || (exports.EcoVadisScoreLevel = EcoVadisScoreLevel = {}));
let EcoVadisTheme = class EcoVadisTheme {
};
exports.EcoVadisTheme = EcoVadisTheme;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], EcoVadisTheme.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], EcoVadisTheme.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], EcoVadisTheme.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], EcoVadisTheme.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => EcoVadisQuestion, (question) => question.theme),
    __metadata("design:type", Array)
], EcoVadisTheme.prototype, "questions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => ProjectEcoVadisTheme, (projectTheme) => projectTheme.theme),
    __metadata("design:type", Array)
], EcoVadisTheme.prototype, "projectThemes", void 0);
exports.EcoVadisTheme = EcoVadisTheme = __decorate([
    (0, typeorm_1.Entity)('ecovadis_theme')
], EcoVadisTheme);
let ProjectEcoVadisTheme = class ProjectEcoVadisTheme {
};
exports.ProjectEcoVadisTheme = ProjectEcoVadisTheme;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ProjectEcoVadisTheme.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisTheme.prototype, "themeId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisTheme.prototype, "projectId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ImpactScore,
    }),
    __metadata("design:type", String)
], ProjectEcoVadisTheme.prototype, "impact", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', { nullable: true, default: '[]' }),
    __metadata("design:type", Array)
], ProjectEcoVadisTheme.prototype, "issues", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProjectEcoVadisTheme.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => EcoVadisTheme, (theme) => theme.projectThemes),
    (0, typeorm_1.JoinColumn)({ name: 'themeId' }),
    __metadata("design:type", EcoVadisTheme)
], ProjectEcoVadisTheme.prototype, "theme", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => project_entity_1.Project),
    (0, typeorm_1.JoinColumn)({ name: 'projectId' }),
    __metadata("design:type", project_entity_1.Project)
], ProjectEcoVadisTheme.prototype, "project", void 0);
exports.ProjectEcoVadisTheme = ProjectEcoVadisTheme = __decorate([
    (0, typeorm_1.Entity)('project_ecovadis_theme')
], ProjectEcoVadisTheme);
let EcoVadisQuestion = class EcoVadisQuestion {
};
exports.EcoVadisQuestion = EcoVadisQuestion;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], EcoVadisQuestion.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], EcoVadisQuestion.prototype, "themeId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], EcoVadisQuestion.prototype, "questionCode", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: EcoVadisIndicator,
    }),
    __metadata("design:type", String)
], EcoVadisQuestion.prototype, "indicator", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], EcoVadisQuestion.prototype, "questionName", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], EcoVadisQuestion.prototype, "question", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], EcoVadisQuestion.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => EcoVadisTheme, (theme) => theme.questions),
    (0, typeorm_1.JoinColumn)({ name: 'themeId' }),
    __metadata("design:type", EcoVadisTheme)
], EcoVadisQuestion.prototype, "theme", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => EcoVadisAnswerOption, (option) => option.question),
    __metadata("design:type", Array)
], EcoVadisQuestion.prototype, "answerOptions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => ProjectEcoVadisQuestion, (projectQuestion) => projectQuestion.question),
    __metadata("design:type", Array)
], EcoVadisQuestion.prototype, "projectQuestions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => ProjectEcoVadisGaps, (gap) => gap.question),
    __metadata("design:type", Array)
], EcoVadisQuestion.prototype, "gaps", void 0);
exports.EcoVadisQuestion = EcoVadisQuestion = __decorate([
    (0, typeorm_1.Entity)('ecovadis_question')
], EcoVadisQuestion);
let ProjectEcoVadisQuestionScore = class ProjectEcoVadisQuestionScore {
};
exports.ProjectEcoVadisQuestionScore = ProjectEcoVadisQuestionScore;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ProjectEcoVadisQuestionScore.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProjectEcoVadisQuestionScore.prototype, "questionId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], ProjectEcoVadisQuestionScore.prototype, "score", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: EcoVadisScoreLevel,
    }),
    __metadata("design:type", String)
], ProjectEcoVadisQuestionScore.prototype, "level", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisQuestionScore.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisQuestionScore.prototype, "breakdown", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisQuestionScore.prototype, "conclusion", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProjectEcoVadisQuestionScore.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => EcoVadisQuestion),
    (0, typeorm_1.JoinColumn)({ name: 'questionId' }),
    __metadata("design:type", EcoVadisQuestion)
], ProjectEcoVadisQuestionScore.prototype, "question", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => ProjectEcoVadisQuestionScoreHistory, (history) => history.score),
    __metadata("design:type", Array)
], ProjectEcoVadisQuestionScore.prototype, "history", void 0);
exports.ProjectEcoVadisQuestionScore = ProjectEcoVadisQuestionScore = __decorate([
    (0, typeorm_1.Entity)('project_ecovadis_question_score')
], ProjectEcoVadisQuestionScore);
let ProjectEcoVadisQuestionScoreHistory = class ProjectEcoVadisQuestionScoreHistory {
};
exports.ProjectEcoVadisQuestionScoreHistory = ProjectEcoVadisQuestionScoreHistory;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ProjectEcoVadisQuestionScoreHistory.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProjectEcoVadisQuestionScoreHistory.prototype, "scoreId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], ProjectEcoVadisQuestionScoreHistory.prototype, "score", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: EcoVadisScoreLevel,
    }),
    __metadata("design:type", String)
], ProjectEcoVadisQuestionScoreHistory.prototype, "level", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisQuestionScoreHistory.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisQuestionScoreHistory.prototype, "breakdown", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisQuestionScoreHistory.prototype, "conclusion", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], ProjectEcoVadisQuestionScoreHistory.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProjectEcoVadisQuestionScoreHistory.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ProjectEcoVadisQuestionScore, (score) => score.history),
    (0, typeorm_1.JoinColumn)({ name: 'scoreId' }),
    __metadata("design:type", ProjectEcoVadisQuestionScore)
], ProjectEcoVadisQuestionScoreHistory.prototype, "currentScore", void 0);
exports.ProjectEcoVadisQuestionScoreHistory = ProjectEcoVadisQuestionScoreHistory = __decorate([
    (0, typeorm_1.Entity)('project_ecovadis_question_score_history')
], ProjectEcoVadisQuestionScoreHistory);
let ProjectEcoVadisQuestion = class ProjectEcoVadisQuestion {
};
exports.ProjectEcoVadisQuestion = ProjectEcoVadisQuestion;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ProjectEcoVadisQuestion.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisQuestion.prototype, "questionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisQuestion.prototype, "projectId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ImpactScore,
    }),
    __metadata("design:type", String)
], ProjectEcoVadisQuestion.prototype, "impact", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProjectEcoVadisQuestion.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProjectEcoVadisQuestion.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => EcoVadisQuestion, (question) => question.projectQuestions),
    (0, typeorm_1.JoinColumn)({ name: 'questionId' }),
    __metadata("design:type", EcoVadisQuestion)
], ProjectEcoVadisQuestion.prototype, "question", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => project_entity_1.Project),
    (0, typeorm_1.JoinColumn)({ name: 'projectId' }),
    __metadata("design:type", project_entity_1.Project)
], ProjectEcoVadisQuestion.prototype, "project", void 0);
exports.ProjectEcoVadisQuestion = ProjectEcoVadisQuestion = __decorate([
    (0, typeorm_1.Entity)('project_ecovadis_question')
], ProjectEcoVadisQuestion);
let ProjectEcoVadisGaps = class ProjectEcoVadisGaps {
};
exports.ProjectEcoVadisGaps = ProjectEcoVadisGaps;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ProjectEcoVadisGaps.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisGaps.prototype, "questionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisGaps.prototype, "projectId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], ProjectEcoVadisGaps.prototype, "gaps", void 0);
__decorate([
    (0, typeorm_1.Column)('simple-array', { nullable: true }),
    __metadata("design:type", Array)
], ProjectEcoVadisGaps.prototype, "documents", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Boolean)
], ProjectEcoVadisGaps.prototype, "resolved", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisGaps.prototype, "assigneeId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ProjectEcoVadisGaps.prototype, "deadline", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProjectEcoVadisGaps.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => EcoVadisQuestion, (question) => question.gaps),
    (0, typeorm_1.JoinColumn)({ name: 'questionId' }),
    __metadata("design:type", EcoVadisQuestion)
], ProjectEcoVadisGaps.prototype, "question", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => project_entity_1.Project),
    (0, typeorm_1.JoinColumn)({ name: 'projectId' }),
    __metadata("design:type", project_entity_1.Project)
], ProjectEcoVadisGaps.prototype, "project", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'assigneeId' }),
    __metadata("design:type", user_entity_1.User)
], ProjectEcoVadisGaps.prototype, "assignee", void 0);
exports.ProjectEcoVadisGaps = ProjectEcoVadisGaps = __decorate([
    (0, typeorm_1.Entity)('project_ecovadis_gaps')
], ProjectEcoVadisGaps);
let EcoVadisAnswerOption = class EcoVadisAnswerOption {
};
exports.EcoVadisAnswerOption = EcoVadisAnswerOption;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], EcoVadisAnswerOption.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], EcoVadisAnswerOption.prototype, "questionId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], EcoVadisAnswerOption.prototype, "issueTitle", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], EcoVadisAnswerOption.prototype, "instructions", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], EcoVadisAnswerOption.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => EcoVadisQuestion, (question) => question.answerOptions),
    (0, typeorm_1.JoinColumn)({ name: 'questionId' }),
    __metadata("design:type", EcoVadisQuestion)
], EcoVadisAnswerOption.prototype, "question", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => ProjectEcoVadisAnswer, (answer) => answer.option),
    __metadata("design:type", Array)
], EcoVadisAnswerOption.prototype, "answers", void 0);
exports.EcoVadisAnswerOption = EcoVadisAnswerOption = __decorate([
    (0, typeorm_1.Entity)('ecovadis_answer_option')
], EcoVadisAnswerOption);
let ProjectEcoVadisAnswer = class ProjectEcoVadisAnswer {
};
exports.ProjectEcoVadisAnswer = ProjectEcoVadisAnswer;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ProjectEcoVadisAnswer.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisAnswer.prototype, "projectId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisAnswer.prototype, "optionId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProjectEcoVadisAnswer.prototype, "response", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProjectEcoVadisAnswer.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => project_entity_1.Project),
    (0, typeorm_1.JoinColumn)({ name: 'projectId' }),
    __metadata("design:type", project_entity_1.Project)
], ProjectEcoVadisAnswer.prototype, "project", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => EcoVadisAnswerOption, (option) => option.answers),
    (0, typeorm_1.JoinColumn)({ name: 'optionId' }),
    __metadata("design:type", EcoVadisAnswerOption)
], ProjectEcoVadisAnswer.prototype, "option", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => ProjectEcoVadisLinkedDocumentChunks, (linkedChunks) => linkedChunks.answer),
    __metadata("design:type", Array)
], ProjectEcoVadisAnswer.prototype, "linkedChunks", void 0);
exports.ProjectEcoVadisAnswer = ProjectEcoVadisAnswer = __decorate([
    (0, typeorm_1.Entity)('project_ecovadis_answer')
], ProjectEcoVadisAnswer);
let ProjectEcoVadisLinkedDocumentChunks = class ProjectEcoVadisLinkedDocumentChunks {
};
exports.ProjectEcoVadisLinkedDocumentChunks = ProjectEcoVadisLinkedDocumentChunks;
__decorate([
    (0, typeorm_1.PrimaryColumn)(),
    __metadata("design:type", Number)
], ProjectEcoVadisLinkedDocumentChunks.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisLinkedDocumentChunks.prototype, "answerId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisLinkedDocumentChunks.prototype, "documentChunkId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProjectEcoVadisLinkedDocumentChunks.prototype, "comment", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProjectEcoVadisLinkedDocumentChunks.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ProjectEcoVadisAnswer, (answer) => answer.linkedChunks),
    (0, typeorm_1.JoinColumn)({ name: 'answerId' }),
    __metadata("design:type", ProjectEcoVadisAnswer)
], ProjectEcoVadisLinkedDocumentChunks.prototype, "answer", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => document_chunk_entity_1.DocumentChunk),
    (0, typeorm_1.JoinColumn)({ name: 'documentChunkId' }),
    __metadata("design:type", document_chunk_entity_1.DocumentChunk)
], ProjectEcoVadisLinkedDocumentChunks.prototype, "documentChunk", void 0);
exports.ProjectEcoVadisLinkedDocumentChunks = ProjectEcoVadisLinkedDocumentChunks = __decorate([
    (0, typeorm_1.Entity)('project_ecovadis_linked_document_chunks')
], ProjectEcoVadisLinkedDocumentChunks);
let EcoVadisSustainabilityIssue = class EcoVadisSustainabilityIssue {
};
exports.EcoVadisSustainabilityIssue = EcoVadisSustainabilityIssue;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], EcoVadisSustainabilityIssue.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], EcoVadisSustainabilityIssue.prototype, "issue", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], EcoVadisSustainabilityIssue.prototype, "definition", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], EcoVadisSustainabilityIssue.prototype, "industryIssues", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], EcoVadisSustainabilityIssue.prototype, "createdAt", void 0);
exports.EcoVadisSustainabilityIssue = EcoVadisSustainabilityIssue = __decorate([
    (0, typeorm_1.Entity)('ecovadis_sustainability_issues')
], EcoVadisSustainabilityIssue);
//# sourceMappingURL=ecovadis.entity.js.map