# Backend Architecture Documentation

## Overview

The backend architecture follows a **modular design pattern**, built with the **NestJS framework**, which allows for scalability, maintainability, and separation of concerns. This architecture is structured into **modules, services, controllers, and entities**, ensuring that different parts of the system remain isolated while still being able to interact effectively.

The backend primarily interacts with a **PostgreSQL database**, using **TypeORM** as the Object-Relational Mapping (ORM) tool. This enables seamless database operations with TypeScript-based entity management and relations.

## Architectural Components

The backend is composed of the following core components:

### 1. **Modules**
Each major domain of the backend is encapsulated in a dedicated **module**, which organizes related services, controllers, and entities. Some of the core modules include:

- **Authentication Module**: Handles user authentication and authorization (JWT-based).
- **User Management Module**: Manages user-related operations, including profiles and permissions.
- **Workspace Module**: Manages different workspaces, their members, and related operations.
- **Chat Module**: Supports chat-based interactions and message history.
- **Document Management Module**: Manages document storage, retrieval, and chunk processing.
- **Knowledge Base Module**: Handles retrieval and processing of knowledge base data.
- **Project & Data Request Modules**: Facilitate project and data request management.
- **Email & Notification Module**: Manages email notifications and user communication.
- **Background Processing Module**: Handles scheduled tasks and queues for long-running operations.

Each module defines its own **controllers, services, and entities**, ensuring clear separation and maintainability.

---

### 2. **Controllers**
Controllers handle incoming HTTP requests and define API endpoints. Each module typically has its own **controller**, which processes client requests and invokes the corresponding services.

**Key responsibilities:**
- Defining **RESTful endpoints** using NestJS decorators like `@Controller()`, `@Get()`, `@Post()`, etc.
- Validating and **handling incoming requests**.
- Delegating business logic to the respective **services**.
- Formatting and returning appropriate **HTTP responses**.

For example:
```typescript
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get(':id')
  async getUser(@Param('id') id: string) {
    return this.usersService.findById(id);
  }
}
```

---

### 3. **Services**
Services contain the **business logic** of the application. They interact with the database, handle computations, and process data before returning responses to controllers.

**Key responsibilities:**
- Implement **business rules** and logic.
- **Interact with the database** via TypeORM repositories.
- **Process data** before returning results to controllers.
- Handle **authentication, authorization, and background processing**.

Each module has its respective service, such as:
- **AuthService** for authentication-related logic.
- **UsersService** for user-related operations.
- **WorkspaceService** for managing workspace membership and actions.
- **DocumentService** for file handling and chunk extraction.
- **ChatService** for message storage and retrieval.
- **BackgroundProcessService** for queue-based operations.

Example of a basic service function:
```typescript
@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async findById(id: string): Promise<User | undefined> {
    return this.userRepository.findOne({ where: { id } });
  }
}
```

---

### 4. **Database Layer**
The backend uses **TypeORM** as the ORM for interacting with a **PostgreSQL database**. The database layer consists of **entities**, which map directly to database tables.

#### **Entities**
Entities define the structure of data in the database and its relationships with other entities. They are represented as TypeScript classes decorated with TypeORM decorators.

Example of a **User entity**:
```typescript
@Entity()
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  @ManyToOne(() => Workspace, (workspace) => workspace.users)
  workspace: Workspace;
}
```

#### **Database Connection**
The database connection is configured using the `TypeOrmModule` inside the `AppModule`, specifying connection details such as host, username, and password.

```typescript
TypeOrmModule.forRoot({
  type: 'postgres',
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT, 10),
  username: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  entities: [User, Workspace, Document],
  synchronize: true, // Auto-migrate in dev environment
});
```

---

### 5. **Authentication & Authorization**
The backend implements **JWT-based authentication**, managed by the **AuthService**. 

Key aspects:
- Users authenticate via email & password.
- A JWT token is issued upon successful login.
- **Guards & decorators** enforce authentication at the route level.

Example of a **JWT Guard** for protecting routes:
```typescript
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {}
```

Protected route:
```typescript
@UseGuards(JwtAuthGuard)
@Get('profile')
async getProfile(@Request() req) {
  return req.user;
}
```

---

### 6. **Routing & API Structure**
NestJS uses **decorators** for defining API routes within controllers. Each module defines its own set of routes, structured under logical controllers.

Example **API structure**:
```
/auth/login          - POST (User authentication)
/users/:id           - GET  (Fetch user profile)
/workspaces/:id      - GET  (Retrieve workspace details)
/documents/upload    - POST (Upload a document)
/chat/history        - GET  (Retrieve chat history)
```

---

### 7. **Background Processing**
Long-running tasks (such as document processing, email notifications, and data analysis) are handled asynchronously using **Queues (BullMQ)** and **Cron jobs**.

**Queue-based processing:**
- Some actions, such as document processing, are offloaded to a **Redis-backed queue**.
- **Workers** process queued jobs in the background.
- Helps improve **API responsiveness**.

For more details on how the queue system is implemented, refer to the [Process Queue Documentation](./process-queue.md).

---

### 8. **Environment Configuration**
All environment variables are managed using a `.env` file. 

Example `.env` file:
```
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=secret
JWT_SECRET=supersecretkey
```

Environment variables are loaded using `ConfigModule`:
```typescript
ConfigModule.forRoot({
  isGlobal: true,
});
```

---

## Getting Started with Development

### **1. Navigate into Repository**
```bash
cd climate-assistant-backend
```

### **2. Install Dependencies**
```bash
npm install
```

### **3. Configure Environment Variables**
Create a `.env` file with the necessary database and authentication configurations.

### **4. Run the Application**
Start the NestJS backend:
```bash
npm run start:dev
```

### **5. Database Migration (if needed)**
```bash
npm run migration:run
```

### **6. Running Tests**
```bash
npm run test
```

---

## Summary

This backend architecture follows **modular principles**, leveraging **NestJS, TypeORM, and PostgreSQL** for an efficient and scalable system. With a clear separation of **controllers, services, and entities**, developers can easily extend and maintain the system. **Authentication, database interactions, background processing, and API routing** are all structured to ensure seamless development and deployment.

For further contributions:
- Follow NestJS best practices.
- Maintain modularity for easy scalability.
- Utilize TypeORM for efficient database interactions.
- Implement background tasks using queues where necessary.

🚀 **Happy Coding!**