import { Injectable, Logger } from '@nestjs/common';
import { Processor, Process, InjectQueue } from '@nestjs/bull';
import { Queue, Job } from 'bull';
import OpenAI from 'openai';
import { ChatGptService } from '../llm/chat-gpt.service';
import { LLM_MODELS } from 'src/constants';
import { TimeoutError } from 'src/middleware/error.middleware';
import { JobQueue, JobProcessor } from 'src/types/jobs';

const seed = 123;

interface HandleRequestProps {
  tokens?: number;
  model: LLM_MODELS;
  messages: OpenAI.Chat.ChatCompletionMessageParam[];
  temperature: number;
  json: boolean;
}

interface ModelLimits {
  maxTokensPerMinute: number;
  maxRequestsPerMinute: number;
}

interface TokenUsage {
  tokensUsed: number;
  requestsUsed: number;
}

@Injectable()
@Processor(JobProcessor.LlmRequest)
export class LlmRateLimiterService {
  private readonly logger = new Logger(LlmRateLimiterService.name);
  private lastResetTime = Date.now();
  private modelUsage: Record<LLM_MODELS, TokenUsage> = {
    [LLM_MODELS.o1]: {
      tokensUsed: 0,
      requestsUsed: 0,
    },
    [LLM_MODELS.o3]: {
      tokensUsed: 0,
      requestsUsed: 0,
    },
    [LLM_MODELS['gpt-4o']]: {
      tokensUsed: 0,
      requestsUsed: 0,
    },
    [LLM_MODELS['o3-mini']]: {
      tokensUsed: 0,
      requestsUsed: 0,
    },
    [LLM_MODELS['o4-mini']]: {
      tokensUsed: 0,
      requestsUsed: 0,
    },
    [LLM_MODELS['o1-preview']]: {
      tokensUsed: 0,
      requestsUsed: 0,
    },
    [LLM_MODELS['gpt-4o-mini']]: {
      tokensUsed: 0,
      requestsUsed: 0,
    },
    [LLM_MODELS['deepseek-r1']]: {
      tokensUsed: 0,
      requestsUsed: 0,
    },
    // Gemini models via OpenAI compatibility layer
    [LLM_MODELS['gemini-2.5-pro']]: {
      tokensUsed: 0,
      requestsUsed: 0,
    },
    [LLM_MODELS['gemini-2.0-flash']]: {
      tokensUsed: 0,
      requestsUsed: 0,
    },
    [LLM_MODELS['gemini-2.5-flash']]: {
      tokensUsed: 0,
      requestsUsed: 0,
    },
  };

  private readonly modelLimits: Record<LLM_MODELS, ModelLimits> = {
    [LLM_MODELS.o1]: {
      maxTokensPerMinute: 164000,
      maxRequestsPerMinute: 274,
    },
    [LLM_MODELS['gpt-4o']]: {
      maxTokensPerMinute: 445000,
      maxRequestsPerMinute: 267,
    },
    [LLM_MODELS['o3-mini']]: {
      maxTokensPerMinute: 5000000,
      maxRequestsPerMinute: 500,
    },
    [LLM_MODELS['o4-mini']]: {
      maxTokensPerMinute: 5000000,
      maxRequestsPerMinute: 500,
    },
    [LLM_MODELS['o1-preview']]: {
      maxTokensPerMinute: 90000,
      maxRequestsPerMinute: 700,
    },
    [LLM_MODELS['gpt-4o-mini']]: {
      maxTokensPerMinute: 90000,
      maxRequestsPerMinute: 700,
    },
    [LLM_MODELS['deepseek-r1']]: {
      maxTokensPerMinute: 400000,
      maxRequestsPerMinute: 700,
    },
    [LLM_MODELS['o3']]: {
      maxTokensPerMinute: 400000,
      maxRequestsPerMinute: 700,
    },
    // Gemini models via OpenAI compatibility layer - Free tier limits
    [LLM_MODELS['gemini-2.5-pro']]: {
      maxTokensPerMinute: 20000, // Conservative estimate based on 300K tokens/day
      maxRequestsPerMinute: 60, // Google's free tier limit
    },
    [LLM_MODELS['gemini-2.0-flash']]: {
      maxTokensPerMinute: 20000,
      maxRequestsPerMinute: 60,
    },
    [LLM_MODELS['gemini-2.5-flash']]: {
      maxTokensPerMinute: 20000,
      maxRequestsPerMinute: 60,
    },
  };

  constructor(
    @InjectQueue(JobQueue.LlmRequest) private readonly llmQueue: Queue,
    private readonly chatGptService: ChatGptService
  ) {}

  @Process({ name: JobProcessor.LlmRequest, concurrency: 10 })
  async processLlmRequest(job: Job) {
    try {
      const request = JSON.parse(job.data.payload);
      return await this.processJob(request);
    } catch (error) {
      throw error;
    }
  }

  private exceedsTokenLimit(tokens: number, model: LLM_MODELS): boolean {
    const limits = this.modelLimits[model];
    return tokens > limits.maxTokensPerMinute;
  }

  async processJob(request) {
    this.resetTokensIfNeeded();

    if (!this.canMakeRequest(request.tokens, request.model)) {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      return await this.processJob(request);
    }
    return await this.makeRequest(request);
  }

  private resetTokensIfNeeded(): void {
    const currentTime = Date.now();
    if (currentTime - this.lastResetTime >= 80 * 1000) {
      //Make it to 80 seconds, anything more than
      this.resetTokens();
      this.lastResetTime = currentTime;
    }
  }

  private resetTokens(): void {
    Object.keys(this.modelUsage).forEach((model) => {
      this.modelUsage[model as LLM_MODELS] = {
        tokensUsed: 0,
        requestsUsed: 0,
      };
    });
    this.logger.log('Tokens reset complete');
  }

  async handleRequest(
    request: HandleRequestProps,
    priority: number = 1
  ): Promise<any> {
    const tokens = this.chatGptService.calculateTokens(request.messages);

    if (this.exceedsTokenLimit(tokens, request.model)) {
      throw new Error(
        `Request exceeds maximum token limit of ${this.modelLimits[request.model].maxTokensPerMinute} for model ${request.model}`
      );
    }
    const payload = JSON.stringify({ tokens, ...request });
    const job = await this.llmQueue.add(
      JobQueue.LlmRequest,
      {
        payload,
      },
      {
        priority,
      }
    );
    try {
      const resp = await job.finished();
      return resp;
    } catch (error) {
      this.logger.error('Error processing job:', error);
      throw new Error(`Job failed: ${error.message}`);
    }
  }

  private canMakeRequest(tokens: number, model: LLM_MODELS): boolean {
    const usage = this.modelUsage[model];
    const limits = this.modelLimits[model];
    return (
      usage.tokensUsed + tokens <= limits.maxTokensPerMinute &&
      usage.requestsUsed < limits.maxRequestsPerMinute
    );
  }

  private updateTokenUsage(tokens: number, model: LLM_MODELS): void {
    this.modelUsage[model].tokensUsed += tokens;
    this.modelUsage[model].requestsUsed++;
    this.logger.log(
      `Model ${model} - Updated tokens used: ${this.modelUsage[model].tokensUsed}, ` +
        `Requests used: ${this.modelUsage[model].requestsUsed}`
    );
  }

  private async makeRequest({
    tokens,
    model,
    messages,
    temperature,
    json,
  }: HandleRequestProps): Promise<any> {
    const titleParams: OpenAI.Chat.ChatCompletionCreateParams = {
      model,
      messages: messages as OpenAI.Chat.ChatCompletionMessageParam[],
      seed,
      temperature,
      response_format: json
        ? {
            type: 'json_object',
          }
        : undefined,
    };

    // We allow one retry if we hit a rate-limit error (429) with a valid wait time <= 30
    // starting attempt at 1 for better readability - first attempt is not a retry
    let attempts = 1;
    const maxAttempts = 5;

    while (attempts < maxAttempts) {
      // Decide which model to use this round:
      //  - First two attempts keep 'o1'
      //  - From the 3rd attempt onwards, switch to '4o'
      let currentModel = model;
      if (model === LLM_MODELS.o1 && attempts > 2) {
        currentModel = LLM_MODELS['gpt-4o'];
      }

      // Adjust OpenAI client & params based on the current model
      let openai;
      if (currentModel === LLM_MODELS.o1) {
        delete titleParams.temperature;
        titleParams.reasoning_effort = 'medium';
        // If Azure OpenAI client fails in first attempt, switch to OpenAI client
        if (attempts > 1) {
          openai = this.chatGptService.openAiClient;
        } else {
          openai = this.chatGptService.azureo1Client;
        }
        // elseif for o3-mini-2025-01-31
      } else if (currentModel === LLM_MODELS['o3-mini-2025-01-31']) {
        delete titleParams.temperature;
        titleParams.reasoning_effort = 'medium';
      } else if (currentModel === LLM_MODELS['o3-mini-2025-01-31']) {
        delete titleParams.temperature;
        titleParams.reasoning_effort = 'medium';
        openai = this.chatGptService.openAiClient;
      } else if (currentModel === LLM_MODELS['o3']) {
        delete titleParams.temperature;
        titleParams.reasoning_effort = 'medium';
        openai = this.chatGptService.openAiClient;
      } else if (currentModel === LLM_MODELS['o4-mini']) {
        delete titleParams.temperature;
        titleParams.reasoning_effort = 'medium';
        openai = this.chatGptService.azureo4miniClient;
      } else {
        // Restore the temperature
        titleParams.temperature = temperature;
        // Remove reasoning_effort if previously set
        delete (titleParams as any).reasoning_effort;
        openai = this.chatGptService.azureOpenAiClient;
      }

      titleParams.model = currentModel;

      if (!this.canMakeRequest(tokens, currentModel)) {
        continue;
      }
      this.updateTokenUsage(tokens, model);

      try {
        let response = '';
        let promptTokens = 0;
        let completionTokens = 0;

        if (titleParams.model === 'deepseek-r1') {
          response = await this.chatGptService.callDeepSeek(messages);
        } else {
          const result: OpenAI.Chat.ChatCompletion = await Promise.race([
            openai.chat.completions.create(titleParams),
            new Promise<OpenAI.Chat.ChatCompletion>((_, reject) =>
              setTimeout(
                () =>
                  reject(new TimeoutError('Request timed out after 3 minutes')),
                3 * 60 * 1000 // 3 minutes
              )
            ),
          ]);

          response = result.choices[0].message.content ?? '';
          promptTokens = result.usage?.prompt_tokens ?? 0;
          completionTokens = result.usage?.completion_tokens ?? 0;
        }

        this.updateTokenUsage(completionTokens, model);

        return {
          response: json ? JSON.parse(response) : response,
          status: 200,
          token: {
            prompt_tokens: promptTokens,
            completion_tokens: completionTokens,
            total_cost: this.chatGptService.calculateCost({
              model,
              inputTokens: promptTokens,
              outputTokens: completionTokens,
            }),
          },
        };
      } catch (error: any) {
        attempts++;

        if (error instanceof TimeoutError) {
          this.logger.log(
            `Timeout Error: ${error.message}. Retrying attempt ${attempts}/${maxAttempts}.`
          );

          if (attempts > 2) {
            this.logger.log(
              `Switching to model '4o' after ${attempts} timeout failures.`
            );
          }
          continue; // Retry
        }

        const httpStatus = error?.status || error?.response?.status;

        const isInvalidChatRequest = httpStatus === 400;
        if (isInvalidChatRequest) {
          return {
            response:
              'The request was either filtered due to the context triggering our content management policy or because the context was longer than the allowed context length.',
            status: 400,
            token: {
              prompt_tokens: 0,
              completion_tokens: 0,
              total_cost: 0,
            },
          };
          // this.executeCallback(cb, respData, args);
        }

        const isRateLimit = httpStatus === 429;

        const retryAfterHeader =
          error?.headers?.['retry-after'] ||
          error?.response?.headers?.['retry-after'];
        const waitTime = parseInt(retryAfterHeader ?? '60', 10);

        this.logger.log(
          `Error: ${error.message} - Status: ${httpStatus} - Retry-After: ${waitTime}s`
        );

        // If it's a rate limit error with a valid waitTime and we haven't exceeded maxAttempts:
        //  1) If the waitTime is <= 240, we wait and then retry
        //  2) If the waitTime > 240 or we've already retried once, throw the error
        if (isRateLimit && attempts < maxAttempts) {
          if (waitTime > 240) {
            throw new Error(
              `Rate limit hit, but wait time (${waitTime}s) exceeds 240 seconds. Aborting.`
            );
          }
          // Wait for the indicated or default waitTime
          const safeWait = Math.max(waitTime, 30) + 5; // e.g. wait at least 30 second + 5 seconds buffer
          await new Promise((resolve) => setTimeout(resolve, safeWait * 1000));
        } else {
          // For any other error, or if we've already retried once, rethrow it
          throw error;
        }
      }
    }

    // If we got here, we've exhausted retries
    throw new Error('Max retry attempts reached. Aborting.');
  }
}
