auth_enabled: false

server:
  http_listen_port: 3100

common:
  path_prefix: /loki
  storage:
    filesystem:
      chunks_directory: /loki/chunks
      rules_directory: /loki/rules
  ring:
    kvstore:
      store: inmemory
    replication_factor: 1

ingester:
  lifecycler:
    ring:
      replication_factor: 1
      kvstore:
        store: inmemory

schema_config:
  configs:
    - from: 2020-10-24
      store: tsdb
      object_store: filesystem
      schema: v13
      index:
        prefix: index_
        period: 24h

compactor:
  working_directory: /loki/compactor
  compaction_interval: 10m

limits_config:
  ingestion_rate_mb: 10
  ingestion_burst_size_mb: 20
  allow_structured_metadata: false
  reject_old_samples: false
  max_global_streams_per_user: 5000
  reject_old_samples_max_age: 168h
