import { <PERSON>umn, <PERSON><PERSON><PERSON>, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { KnowledgeBaseFileUploadChunk } from './knowledge-base-file-upload-chunk.entity';

@Entity()
export class KnowledgeBaseFileUpload {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  path: string;

  @OneToMany(() => KnowledgeBaseFileUploadChunk, (chunk) => chunk.fileUpload)
  chunks: KnowledgeBaseFileUploadChunk[];
}
