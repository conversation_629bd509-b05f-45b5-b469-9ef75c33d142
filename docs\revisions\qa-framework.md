# Revision Journal: EcoVadis GAP Approval/Disapproval Feature

## 1. Overview
We want to introduce a feature that allows Super-Admins to approve, disapprove or regenerate individual AI-generated GAPs. This will help us monitor quality and collect structured feedback to improve future generations.

### Description
- Add three buttons: "Approve", "Disapprove", and "Regenerate with feedback" in the GAP view, visible to Super-Admins only.
- When a Super-Admin selects **Disapprove**, a modal dialog must prompt for a mandatory free-text justification.
- When a Super-Admin selects **Regenerate with feedback**, a modal dialog must prompt for a mandatory free-text justification.
- Save the rejection reason, reviewer identity, and timestamp alongside the GAP record for internal evaluation and potential model fine-tuning.

## 2. Key Requirements

### UI Changes (Super-Admin only)
-   Approve button
-   Disapprove button
-   Regenerate with feedback button
-   Rejection justification modal on Disapprove or Regenerate.

### Database Changes
A new table is required to store the generated content and its review status. Let's call it `project_ecovadis_gap_generation`.

**`project_ecovadis_gap_generation` table schema:**
-   `id` (PK, UUID)
-   `projectId` (FK to `project`)
-   `questionId` (FK to `ecovadis_question`)
-   `generatedContent` (JSONB or TEXT) - The AI-generated GAP.
-   `documents` (ARRAY of UUIDs, FK to `document`)
-   `status` (ENUM: `pending_review`, `approved`, `rejected`, `regenerating`) - Default `pending_review`.
-   `feedback` (TEXT, nullable) - Reason for rejection or feedback for regeneration.
-   `reviewedBy` (FK to `users`)
-   `reviewedAt` (TIMESTAMPTZ)
-   `createdAt` (TIMESTAMPTZ, default now())

The existing `project_ecovadis_gaps` will be the "actual answer table".

### Business Logic
1.  **AI Generation**:
    -   When a Super Admin performs AI generate on a GAP, it creates a new record in the `project_ecovadis_gap_generation` table with `status = 'pending_review'`.
    - When a non Super Admin performs AI generate on a GAP, existing flow should be maintained. The gaps should be stored in the gaps table.

2.  **Super-Admin View**:
    -   Super-Admins see a list of GAPs from the `project_ecovadis_gap_generation` table that are pending review.
    -   They have options to "Approve", "Disapprove", or "Regenerate with feedback".
    - Super-Amdins also see the gaps those were approved and are in the gaps table.

3.  **Approval Workflow**:
    -   **Approve**:
        -   The `status` in `project_ecovadis_gap_generation` is updated to `'approved'`.
        -   The `reviewedBy` and `reviewedAt` fields are populated.
        -   The `generatedContent` is **copied** into the `project_ecovadis_gaps` table, making it visible to all users.
    -   **Disapprove**:
        -   The `status` in `project_ecovadis_gap_generation` is updated to `'rejected'`.
        -   The `feedback` field is populated with the mandatory justification.
        -   The `reviewedBy` and `reviewedAt` fields are populated.
        -   The content is NOT copied to `project_ecovadis_gaps`.
    -   **Regenerate with feedback**:
        -   The `status` in `project_ecovadis_gap_generation` is updated to `'regenerating'`.
        -   The `feedback` field is populated with the mandatory justification.
        -   This triggers the AI generation flow again.
        -   The prompt for the AI will be the original prompt appended with the new feedback.
        -   The new AI output will create another new record in `project_ecovadis_gap_generation` for review.

## 3. Expected Outcome
- Super-Admins can approve, disapprove, or trigger regeneration of GAPs.
- Disapprovals and regenerations require written comments.
- Only approved GAPs are visible to non-Super-Admin users.
- A clear history of reviews and feedback is maintained.
- The feedback loop helps improve the quality of AI-generated content over time.

**Date:** 2025-07-22
**Author:** Ishwar Rimal