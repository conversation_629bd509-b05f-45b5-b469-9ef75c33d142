import { Injectable, Logger } from '@nestjs/common';
import { CustomerIoService } from './customerio.service';
import { User } from '../users/entities/user.entity';

const EMAIL_TEMPLATE_ID = {
  RESET_PASSWORD: '3',
  INVITE_USER: '4',
};

@Injectable()
export class EmailService {
  constructor(private readonly customerIoService: CustomerIoService) {}

  private readonly logger = new Logger(EmailService.name);

  async sendPasswordReset({
    email,
    userName,
    resetToken,
    origin,
  }: {
    email: string;
    userName: string;
    resetToken: string;
    origin: string;
  }): Promise<void> {
    this.logger.log(`Sending password reset email to: ${email}`);
    this.logger.log(`Reset token: ${resetToken}`);

    //These are email variables that will be used in the email template
    const custom_data = {
      userName,
      password_reset_email: `${origin}/reset-password?token=${resetToken}`,
    };

    await this.customerIoService.sendEmailWithTemplate({
      templateId: EMAIL_TEMPLATE_ID.RESET_PASSWORD,
      to: email,
      subject: 'Glacier Password Reset Link',
      custom_data,
    });

    this.logger.log('Password reset email sent successfully.');
  }

  async inviteUser({
    token,
    invitingUser,
    email,
    origin,
  }: {
    token: string;
    invitingUser: User;
    email: string;
    origin: string;
  }): Promise<void> {
    this.logger.log(`Sending invite user email to: ${email}`);

    const custom_data = {
      workspaceUserName: invitingUser.name || invitingUser.email,
      workspaceUserEmail: invitingUser.email,
      inviteUserLink: `${origin}/reset-password?token=${token}`,
    };

    await this.customerIoService.sendEmailWithTemplate({
      templateId: EMAIL_TEMPLATE_ID.INVITE_USER,
      to: email,
      subject: `${invitingUser.name || invitingUser.email} has invited you to join the Workspace in Glacier`,
      custom_data,
    });

    this.logger.log('Email sent successfully.');
  }
}
