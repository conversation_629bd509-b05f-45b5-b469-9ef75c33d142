import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1733416177418 implements MigrationInterface {
  name = 'SchemaUpdate1733416177418';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" ADD "customUserRemark" text NOT NULL DEFAULT ''`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" DROP COLUMN "customUserRemark"`,
    );
  }
}
