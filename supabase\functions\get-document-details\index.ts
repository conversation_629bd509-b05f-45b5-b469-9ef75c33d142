// @ts-expect-error TODO look into this later
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { authValidator } from "../_shared/authValidator.ts";
import { corsHeaders } from "../_shared/cors.ts";

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { id } = await req.json();

    const { user, error, supabaseClient, response: errResponse } = await authValidator(req);
    if (!user || error || !supabaseClient) {
      return errResponse;
    }

    // Fetch the raw row with its chunks and linked questions as in the original SQL
    const { data, error: queryErr } = await supabaseClient
      .from('document')
      .select(`
        *,
        chunks:document_chunk(*),
        linkedQuestions:document_chunk(
          id,
          page,
          content,
          project_ecovadis_linked_document_chunks(
            id,
            comment,
            answer:project_ecovadis_answer(
              id,
              project:project(
                id,
                name
              ),
              option:ecovadis_answer_option(
                id,
                issueTitle,
                question:ecovadis_question(
                  questionCode,
                  question
                )
              )
            )
          )
        )
      `)
      .eq('id', id)
      .maybeSingle();

    if (queryErr) {
      return new Response(JSON.stringify({ error: queryErr.message }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      });
    }

    return new Response(JSON.stringify({ data }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  } catch (e) {
    console.error("[get-document-details]", e);
    return new Response(JSON.stringify({ error: String(e) }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
