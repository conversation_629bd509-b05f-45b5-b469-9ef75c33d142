"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DatapointRequestService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatapointRequestService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const datapoint_request_entity_1 = require("./entities/datapoint-request.entity");
const prompts_service_1 = require("../prompts/prompts.service");
const project_service_1 = require("../project/project.service");
const comment_entity_1 = require("../project/entities/comment.entity");
const llm_response_util_1 = require("../util/llm-response-util");
const users_service_1 = require("../users/users.service");
const workspace_service_1 = require("../workspace/workspace.service");
const datapoint_document_chunk_entity_1 = require("../datapoint-document-chunk/entities/datapoint-document-chunk.entity");
const mdr_prompts_service_1 = require("../prompts/mdr-prompts.service");
const numerics_prompts_service_1 = require("../prompts/numerics-prompts.service");
const datapoint_generation_prompts_service_1 = require("../prompts/datapoint-generation-prompts.service");
const table_prompts_service_1 = require("../prompts/table-prompts.service");
const esrs_datapoint_entity_1 = require("./entities/esrs-datapoint.entity");
const document_entity_1 = require("../document/entities/document.entity");
const common_util_1 = require("../util/common-util");
const user_workspace_entity_1 = require("../users/entities/user-workspace.entity");
const datapoint_generation_entity_1 = require("./entities/datapoint-generation.entity");
const esrs_topic_datapoint_entity_1 = require("../knowledge-base/entities/esrs-topic-datapoint.entity");
const constants_1 = require("../constants");
const llm_rate_limiter_service_1 = require("../llm-rate-limiter/llm-rate-limiter.service");
let DatapointRequestService = DatapointRequestService_1 = class DatapointRequestService {
    constructor(datapointRequestRepository, datapointDocumentChunkMapRepository, esrsDatapointRepository, esrsTopicDatapointRepository, datapointGenerationRepository, documentRepository, promptService, mdrPromptService, NormalDpPromptService, tablePromptService, datapointPromptService, projectService, userService, workspaceService, llmRateLimiterService) {
        this.datapointRequestRepository = datapointRequestRepository;
        this.datapointDocumentChunkMapRepository = datapointDocumentChunkMapRepository;
        this.esrsDatapointRepository = esrsDatapointRepository;
        this.esrsTopicDatapointRepository = esrsTopicDatapointRepository;
        this.datapointGenerationRepository = datapointGenerationRepository;
        this.documentRepository = documentRepository;
        this.promptService = promptService;
        this.mdrPromptService = mdrPromptService;
        this.NormalDpPromptService = NormalDpPromptService;
        this.tablePromptService = tablePromptService;
        this.datapointPromptService = datapointPromptService;
        this.projectService = projectService;
        this.userService = userService;
        this.workspaceService = workspaceService;
        this.llmRateLimiterService = llmRateLimiterService;
        this.logger = new common_1.Logger(DatapointRequestService_1.name);
        this.MAX_DATAPOINT_GENERATION_DOCUMENT_CHUNKS_LENGHT = 300000;
        this.THRESHOLD_DOCUMENT_CHUNK_LENGTH_UNTIL_REDUCING = 3000;
        this.MAX_CHATCONTENT_MESSAGE_LENGTH = 300000;
    }
    isNumericDataPoint(dataType) {
        if (!dataType) {
            return false;
        }
        const numericDataTypes = [
            'monetary', 'gyear', 'date', 'ghgemissions', 'energy', 'intensity', 'integer', 'decimal', 'volume', 'area', 'percent', 'mass'
        ].map((type) => type.toLowerCase());
        const dataTypeList = dataType.split('/');
        return dataTypeList.some((type) => numericDataTypes.includes(type.toLowerCase()));
    }
    async findById(datapointRequestId) {
        const datapointRequest = await this.datapointRequestRepository.findOne({
            where: { id: datapointRequestId },
            relations: {
                esrsDatapoint: true,
            },
        });
        if (!datapointRequest) {
            throw new common_1.NotFoundException(`Datapoint with ID ${datapointRequest} not found`);
        }
        return datapointRequest;
    }
    async findDatapointWithGenerationsById(datapointRequestId) {
        const relations = {
            esrsDatapoint: true,
            datapointGenerations: true,
            commentGenerations: true,
            comments: {
                user: true,
            },
            datapointDocumentChunkMap: {
                documentChunk: {
                    document: true,
                },
            },
        };
        return await this.datapointRequestRepository.findOne({
            where: { id: datapointRequestId },
            relations,
            order: {
                esrsDatapointId: 'ASC',
                comments: {
                    createdAt: 'ASC',
                },
            },
        });
    }
    async findDatapointGenerationWithId(datapointGenerationId) {
        return await this.datapointGenerationRepository.findOne({
            where: { id: datapointGenerationId },
            relations: ['datapointRequest', 'evaluator'],
        });
    }
    async findAllDataPointRequests(dataRequestId, userId) {
        const relations = {
            esrsDatapoint: true,
            comments: {
                user: true,
            },
        };
        const isSuperAdmin = await this.userService.userHasRequiredRole(userId, [
            user_workspace_entity_1.Role.SuperAdmin,
        ]);
        const isAiContributor = await this.userService.userHasRequiredRole(userId, [
            user_workspace_entity_1.Role.AiContributor,
        ]);
        if (isSuperAdmin) {
            relations['datapointGenerations'] = {
                evaluator: true,
            };
        }
        if (isSuperAdmin || isAiContributor) {
            relations['commentGenerations'] = true;
        }
        const datapoints = await this.datapointRequestRepository.find({
            where: { dataRequestId },
            relations,
            order: {
                esrsDatapointId: 'ASC',
                comments: {
                    createdAt: 'ASC',
                },
            },
        });
        const datapointsWithDocumentCount = [];
        for (const datapoint of datapoints) {
            try {
                const count = await this.datapointDocumentChunkMapRepository.count({
                    where: {
                        datapointRequestId: datapoint.id,
                        active: true,
                        documentChunk: {
                            document: {
                                id: (0, typeorm_2.Not)((0, typeorm_2.IsNull)()),
                            },
                        },
                    },
                    relations: ['documentChunk.document'],
                });
                datapointsWithDocumentCount.push({
                    ...datapoint,
                    documentChunkCount: count,
                });
            }
            catch (e) {
                this.logger.error('Error while counting document chunks', e);
            }
        }
        return datapointsWithDocumentCount;
    }
    async loadMaterialTopics(datapointRequestId) {
        const datapointRequest = await this.findData(datapointRequestId);
        const { id } = datapointRequest.esrsDatapoint;
        const projectId = datapointRequest.dataRequest.projectId;
        const esrsTopicDatapoint = await this.esrsTopicDatapointRepository.find({
            where: { esrsDatapointId: id },
            relations: ['topic'],
        });
        const validMaterialTopics = await this.generateHierarchicalListOfTopics({
            topicRelations: esrsTopicDatapoint,
            projectId,
            material: true,
        });
        return validMaterialTopics;
    }
    async findData(datapointRequestId) {
        const datapointRequest = await this.datapointRequestRepository.findOne({
            where: { id: datapointRequestId },
            relations: [
                'esrsDatapoint',
                'dataRequest',
                'comments',
                'datapointDocumentChunkMap.documentChunk.document',
            ],
        });
        if (!datapointRequest) {
            throw new common_1.NotFoundException(`Datapoint with ID ${datapointRequest} not found`);
        }
        return datapointRequest;
    }
    async update({ datapointRequestId, updateDatapointRequestPayload, userId, workspaceId, event = 'datapoint_request_updated', }) {
        const datapointRequest = await this.findData(datapointRequestId);
        if (!datapointRequest) {
            throw new common_1.NotFoundException(`Datapoint Request not found`);
        }
        const definedPayload = Object.fromEntries(Object.entries(updateDatapointRequestPayload).filter(([_, value]) => value !== undefined));
        const dockedPayload = {
            ...datapointRequest,
            ...definedPayload,
        };
        const evaluatedStatus = this.datapointRequestStatusProcessor(dockedPayload);
        definedPayload.status = evaluatedStatus;
        await this.datapointRequestRepository.update(datapointRequestId, definedPayload);
        const datapoint = await this.findById(datapointRequestId);
        if (userId && workspaceId) {
            await this.workspaceService.storeActionHistory({
                event: event,
                ref: datapointRequestId,
                workspaceId: workspaceId,
                versionData: {
                    event: event,
                    doneBy: userId,
                    data: datapoint,
                },
            });
        }
        return datapoint;
    }
    async generateHierarchicalListOfTopics({ topicRelations, projectId, material, }) {
        const projectEsrsTopics = await this.projectService.getProjectEsrsTopics({
            projectId,
            esrsTopicIds: topicRelations.map((tr) => tr.topic.id),
        });
        const filteredTopics = projectEsrsTopics.filter((mt) => Boolean(mt.active) === material);
        const filteredTopicIds = new Set(filteredTopics.map((mt) => mt.esrsTopicId));
        const topicsById = new Map();
        for (const tr of topicRelations) {
            if (filteredTopicIds.has(tr.topic.id)) {
                topicsById.set(tr.topic.id, { ...tr.topic, children: [] });
            }
        }
        for (const [id, topic] of topicsById.entries()) {
            if (topic.parentId && !topicsById.has(topic.parentId)) {
                topic.parentId = null;
            }
        }
        for (const [id, topic] of topicsById.entries()) {
            if (topic.parentId && topicsById.has(topic.parentId)) {
                topicsById.get(topic.parentId).children.push(topic);
            }
        }
        const hierarchicalTopics = [...topicsById.values()].filter((t) => !t.parentId);
        return hierarchicalTopics;
    }
    async reviewDatapointContentWithAI({ datapointRequestId, userId, workspaceId, }) {
        const isAIEvaluator = await this.userService.userHasRequiredRole(userId, [
            user_workspace_entity_1.Role.SuperAdmin,
        ]);
        const datapointRequest = await this.datapointRequestRepository.findOne({
            where: {
                id: datapointRequestId,
                status: (0, typeorm_2.Not)(datapoint_request_entity_1.DatapointRequestStatus.NotAnswered),
            },
            relations: {
                dataRequest: { project: true },
                esrsDatapoint: {
                    topicRelations: {
                        topic: true,
                    },
                },
                comments: true,
            },
        });
        const { generalCompanyProfile } = await this.workspaceService.getCompanyDetailFromWorkspaceId(workspaceId);
        this.logger.log(`Start Gap Analysis for datapointRequest ${datapointRequest.id}`);
        const otherDatapoints = await this.esrsDatapointRepository.find({
            where: {
                esrsDisclosureRequirementId: datapointRequest.esrsDatapoint.esrsDisclosureRequirementId,
                id: (0, typeorm_2.Not)(datapointRequest.esrsDatapoint.id),
            },
        });
        let datapointRequestGapAnalysisChatCompletion = [];
        const esrsDatapoint = datapointRequest.esrsDatapoint;
        const h2Count = (datapointRequest.content.match(/<\/h2>/g) || []).length;
        if (esrsDatapoint.datapointId.includes('MDR') && h2Count > 0) {
            this.logger.log(`MDR Datapoint identified: ${esrsDatapoint.datapointId} with ${h2Count} policies`);
            datapointRequestGapAnalysisChatCompletion = [
                {
                    role: 'system',
                    content: this.mdrPromptService.indentifyMDRandGenerateGapAnalysisSystemPrompt({
                        esrsDatapoint,
                        language: datapointRequest.dataRequest.project.primaryContentLanguage,
                    }),
                },
                {
                    role: 'user',
                    content: this.promptService.generateDatapointGapAnalysisContentContext(datapointRequest.content),
                },
            ];
        }
        else {
            datapointRequestGapAnalysisChatCompletion = [
                {
                    role: 'system',
                    content: this.promptService.generateDatapointGapAnalysisSystemPrompt1({
                        esrsDatapoint,
                        generationLanguage: datapointRequest.dataRequest.project.primaryContentLanguage,
                    }),
                },
                {
                    role: 'system',
                    content: this.promptService.generateDatapointGapAnalysisDatapointSpecificSystemPrompt(esrsDatapoint, otherDatapoints),
                },
                {
                    role: 'user',
                    content: this.promptService.generateDatapointGapAnalysisContentContext(datapointRequest.content),
                },
            ];
        }
        if (esrsDatapoint.conditional) {
            datapointRequestGapAnalysisChatCompletion.push({
                role: 'system',
                content: `This is a conditional datapoint. If there is information missing, it is up to the user whether they are ok with a gap, or not. The conditions typically have "if relevant" or similar conditionals in the law-text or application-requirements. Explicitly mention in this gap analysis whether the condition is met or not met.`,
            });
        }
        const topicRelations = datapointRequest.esrsDatapoint.topicRelations;
        const materialTopicsInHierarchy = await this.generateHierarchicalListOfTopics({
            topicRelations,
            projectId: datapointRequest.dataRequest.project.id,
            material: true,
        });
        if (materialTopicsInHierarchy.length > 0) {
            datapointRequestGapAnalysisChatCompletion.push({
                role: 'system',
                content: this.promptService.datapointRequestGapAnalysisSystemPromptMaterialTopics({ topics: materialTopicsInHierarchy, material: true }),
            });
            const nonMaterialTopicsInHierarchy = await this.generateHierarchicalListOfTopics({
                topicRelations,
                projectId: datapointRequest.dataRequest.project.id,
                material: false,
            });
            if (nonMaterialTopicsInHierarchy.length > 0) {
                datapointRequestGapAnalysisChatCompletion.push({
                    role: 'system',
                    content: this.promptService.datapointRequestGapAnalysisSystemPromptMaterialTopics({ topics: nonMaterialTopicsInHierarchy, material: false }),
                });
            }
        }
        datapointRequestGapAnalysisChatCompletion.push({
            role: 'system',
            content: this.promptService.generateDatapointGapAnalysisSystemPrompt2({
                esrsDatapoint,
                generationLanguage: datapointRequest.dataRequest.project.primaryContentLanguage,
                reportTextGenerationRules: datapointRequest.dataRequest.project.reportTextGenerationRules,
                generalCompanyProfile,
                reportingYear: datapointRequest.dataRequest.project.reportingYear,
            }),
        });
        const gapAnalysisCompletionResponse = await this.llmRateLimiterService.handleRequest({
            model: process.env.GA_MODEL,
            messages: datapointRequestGapAnalysisChatCompletion,
            json: true,
            temperature: 0.3,
        });
        if (gapAnalysisCompletionResponse.status === 400) {
            throw new Error(gapAnalysisCompletionResponse.response);
        }
        this.logger.log(`Gap Analysis Tokens: ${JSON.stringify(gapAnalysisCompletionResponse.token)}`);
        const gapAnalysis = gapAnalysisCompletionResponse.response;
        const globalAIUser = await this.userService.findGlobalGlacierAIUser();
        const comments = [];
        let gapHtml = '';
        if (gapAnalysis.gapIdentified &&
            gapAnalysis.gaps &&
            gapAnalysis.gaps.length > 0) {
            for (const gap of gapAnalysis.gaps) {
                gapHtml =
                    (gap.title ? "<h3> " + gap.title + "</h3>" : "") +
                        "<p><strong>Gap Identified:</strong> " + gap.gap + "</p>" +
                        "<p><strong>Recommended Actions:</strong></p>" +
                        (gap.actions ?
                            "<ul>" +
                                gap.actions.map(function (action) {
                                    return "<li>" + action + "</li>";
                                }).join('') +
                                "</ul>" :
                            "") +
                        "<p><strong>Example Text:</strong>" + gap.exampleText + "</p>" +
                        (gap.disclaimer ? "<p><strong>Disclaimer:</strong> " + gap.disclaimer + "</p>" : "");
                const comment = await this.projectService.addComment({
                    commentableId: datapointRequestId,
                    commentableType: comment_entity_1.CommentType.DatapointRequest,
                    userId: globalAIUser.id,
                    comment: gapHtml,
                    workspaceId,
                    evaluationLot: isAIEvaluator,
                });
                comments.push(comment);
            }
        }
        else if (gapAnalysis.gapIdentified) {
            gapHtml =
                (gapAnalysis.title ? "<h3> " + gapAnalysis.title + "</h3>" : "") +
                    "<p><strong>Gap Identified:</strong> " + gapAnalysis.gap + "</p>" +
                    "<p><strong>Recommended Actions:</strong></p>" +
                    (gapAnalysis.actions ?
                        "<ul>" +
                            gapAnalysis.actions.map(function (action) {
                                return "<li>" + action + "</li>";
                            }).join('') +
                            "</ul>" :
                        "") +
                    "<p><strong>Example Text:</strong>" + gapAnalysis.exampleText + "</p>" +
                    (gapAnalysis.disclaimer ? "<p><strong>Disclaimer:</strong> " + gapAnalysis.disclaimer + "</p>" : "");
            const comment = await this.projectService.addComment({
                commentableId: datapointRequestId,
                commentableType: comment_entity_1.CommentType.DatapointRequest,
                userId: globalAIUser.id,
                comment: gapHtml,
                workspaceId,
                evaluationLot: isAIEvaluator,
            });
            comments.push(comment);
        }
        else {
            gapHtml =
                "<p><strong>No Gap Identified:</strong></p>" +
                    "<p>" + gapAnalysis.text + "</p>";
            const comment = await this.projectService.addComment({
                commentableId: datapointRequestId,
                commentableType: comment_entity_1.CommentType.DatapointRequest,
                userId: globalAIUser.id,
                comment: gapHtml,
                workspaceId,
                evaluationLot: isAIEvaluator,
            });
            comments.push(comment);
        }
        if (datapointRequest.queueStatus === datapoint_request_entity_1.DatapointQueueStatus.QueuedForReview) {
            await this.updateQueueStatus({
                datapointRequestId,
                queueStatus: null,
            });
        }
        return comments;
    }
    async validateDatapointRequestGenerationRightsOrFail({ datapointRequest, userId, }) {
        if (!datapointRequest.esrsDatapoint.publicAccess) {
            await this.userService.userHasRequiredRoleOrFail({
                userOrId: userId,
                role: [user_workspace_entity_1.Role.SuperAdmin, user_workspace_entity_1.Role.AiContributor],
                message: 'Datapoint generation is not enabled for this request.',
            });
        }
        return true;
    }
    async generateDatapointContentWithAI({ datapointRequestId, userId, workspaceId, useExistingReportTextForReference, }) {
        const datapointRequest = await this.datapointRequestRepository.findOne({
            where: { id: datapointRequestId },
            relations: {
                dataRequest: { project: true },
                esrsDatapoint: {
                    esrsDisclosureRequirement: true,
                    topicRelations: {
                        topic: true,
                    },
                },
                datapointDocumentChunkMap: {
                    documentChunk: { document: true },
                },
            },
        });
        const { generalCompanyProfile } = await this.workspaceService.getCompanyDetailFromWorkspaceId(workspaceId);
        const relatedDatapoints = await this.datapointRequestRepository.find({
            where: {
                dataRequestId: datapointRequest.dataRequestId,
                id: (0, typeorm_2.Not)(datapointRequest.id),
                content: (0, typeorm_2.Not)(''),
            },
            relations: ['esrsDatapoint'],
        });
        this.logger.log(`Start Generating datapointRequest ${datapointRequest.id} with AI`);
        const otherDatapoints = await this.esrsDatapointRepository.find({
            where: {
                esrsDisclosureRequirementId: datapointRequest.esrsDatapoint.esrsDisclosureRequirementId,
                id: (0, typeorm_2.Not)(datapointRequest.esrsDatapoint.id),
            },
        });
        const { context: datapointGenerationContextFromLinkedDocumentChunks, documentChunksIndex, } = await this.generateDatapointGenerationContextFromLinkedDocumentChunks(datapointRequest);
        let datapointGenerationChatCompletion = [];
        const topicRelations = datapointRequest.esrsDatapoint.topicRelations;
        const materialTopicsInHierarchy = await this.generateHierarchicalListOfTopics({
            topicRelations,
            projectId: datapointRequest.dataRequest.project.id,
            material: true,
        });
        const nonMaterialTopicsInHierarchy = await this.generateHierarchicalListOfTopics({
            topicRelations,
            projectId: datapointRequest.dataRequest.project.id,
            material: false,
        });
        const esrsDatapoint = datapointRequest.esrsDatapoint;
        const h2Count = (datapointRequest.content.match(/<\/h2>/g) || []).length;
        const isNumeric = this.isNumericDataPoint(esrsDatapoint.dataType);
        const isTabular = esrsDatapoint.dataType?.includes('table');
        if (esrsDatapoint.datapointId.includes('MDR') && h2Count > 0) {
            this.logger.log(`MDR Datapoint identified: ${esrsDatapoint.datapointId} with ${h2Count} policies`);
            datapointGenerationChatCompletion = [
                {
                    role: 'system',
                    content: this.mdrPromptService.indentifyMDRContentGenerationMainPrompt({
                        esrsDatapoint,
                        datapointGenerationContextFromLinkedDocumentChunks,
                        language: datapointRequest.dataRequest.project.primaryContentLanguage,
                        reportTextGenerationRules: datapointRequest.dataRequest.project.reportTextGenerationRules,
                        generalCompanyProfile,
                        reportingYear: datapointRequest.dataRequest.project.reportingYear,
                        customUserRemark: datapointRequest.customUserRemark,
                    }),
                },
            ];
        }
        else if (isTabular) {
            this.logger.log(`Datapoint identified: ${esrsDatapoint.datapointId}${isTabular ? ' (Tabular)' : isNumeric ? ' (Numeric)' : 'Other'}`);
            const exampleOutput = esrsDatapoint?.exampleOutput?.trim() !== ''
                ? esrsDatapoint.exampleOutput
                : 'no example provided, infer from context';
            datapointGenerationChatCompletion = [
                {
                    role: 'system',
                    content: this.tablePromptService.generateTableSystemPrompt({
                        esrsDatapoints: [esrsDatapoint],
                        generationLanguage: datapointRequest.dataRequest.project.primaryContentLanguage,
                        reportTextGenerationRules: datapointRequest.dataRequest.project.reportTextGenerationRules,
                        customUserRemark: datapointRequest.customUserRemark,
                        currentContent: datapointRequest.content,
                        linkedChunks: datapointGenerationContextFromLinkedDocumentChunks,
                        otherDatapoints,
                        reportingYear: datapointRequest.dataRequest.project.reportingYear,
                        generalCompanyProfile,
                    }),
                },
            ];
        }
        else if (isNumeric) {
            this.logger.log(`Datapoint identified: ${esrsDatapoint.datapointId}${isTabular ? ' (Tabular)' : isNumeric ? ' (Numeric)' : 'Other'}`);
            datapointGenerationChatCompletion = [
                {
                    role: 'system',
                    content: this.datapointPromptService.generateNumericDatapointContentGenerationSystemPrompt({
                        esrsDatapoint: esrsDatapoint,
                        generationLanguage: datapointRequest.dataRequest.project.primaryContentLanguage,
                        reportTextGenerationRules: datapointRequest.dataRequest.project
                            .reportTextGenerationRules,
                        customUserRemark: datapointRequest.customUserRemark,
                        currentContent: datapointRequest.content,
                        generalCompanyProfile,
                        otherDatapoints: otherDatapoints,
                        reportingYear: datapointRequest.dataRequest.project.reportingYear,
                        linkedChunks: datapointGenerationContextFromLinkedDocumentChunks,
                    }),
                },
            ];
        }
        else {
            datapointGenerationChatCompletion = [
                {
                    role: 'system',
                    content: this.NormalDpPromptService.generateDatapointContentGenerationSystemPrompt({
                        esrsDatapoint: esrsDatapoint,
                        generationLanguage: datapointRequest.dataRequest.project.primaryContentLanguage,
                        reportTextGenerationRules: datapointRequest.dataRequest.project
                            .reportTextGenerationRules,
                        customUserRemark: datapointRequest.customUserRemark,
                        currentContent: datapointRequest.content,
                        otherDatapoints: otherDatapoints,
                        reportingYear: datapointRequest.dataRequest.project.reportingYear,
                        generalCompanyProfile,
                        linkedChunks: datapointGenerationContextFromLinkedDocumentChunks,
                    }),
                },
            ];
        }
        if (materialTopicsInHierarchy.length > 0) {
            datapointGenerationChatCompletion.push({
                role: 'system',
                content: this.promptService.datapointRequestContentGenerationSystemPromptMaterialTopics({ topics: materialTopicsInHierarchy, material: true }),
            });
            if (nonMaterialTopicsInHierarchy.length > 0) {
                datapointGenerationChatCompletion.push({
                    role: 'system',
                    content: this.promptService.datapointRequestContentGenerationSystemPromptMaterialTopics({ topics: nonMaterialTopicsInHierarchy, material: false }),
                });
            }
        }
        if (useExistingReportTextForReference) {
            datapointGenerationChatCompletion.push({
                role: 'system',
                content: `Following is the existing report text that was previously generated, user wishes to extend upon this:\n ${datapointRequest.content}`,
            });
        }
        if (esrsDatapoint.conditional) {
            datapointGenerationChatCompletion.push({
                role: 'system',
                content: `This is a conditional datapoint. This means that information described in the law text might not be neccessary to disclose, if the conditions aren't met. The conditions typically have "if relevant" or similar conditionals in the phrasing of law-text or application-requirements. It is up to the user to decide whether they are ok with a gap, or not.  Thus for apparent gaps here, explicitly mention in this‚ gap analysis whether the condition is met or not met.`,
            });
        }
        if (relatedDatapoints.length > 0) {
            datapointGenerationChatCompletion.push({
                role: 'system',
                content: this.promptService.datapointRequestContentGenerationSystemPromptRelatedDatapoints(relatedDatapoints),
            });
        }
        const predatapointGenerationChatCompletionResponse = await this.llmRateLimiterService.handleRequest({
            model: constants_1.LLM_MODELS.o3,
            messages: datapointGenerationChatCompletion,
            json: true,
            temperature: 0,
        });
        if (predatapointGenerationChatCompletionResponse.status === 400) {
            throw new Error(predatapointGenerationChatCompletionResponse.response);
        }
        let datapointGenerationChatCompletionResponse;
        if (!esrsDatapoint.datapointId.includes('MDR')) {
            datapointGenerationChatCompletionResponse =
                predatapointGenerationChatCompletionResponse;
        }
        else {
            let improvingFormattingPrompt = [];
            improvingFormattingPrompt = [
                {
                    role: 'system',
                    content: this.NormalDpPromptService.improvingFormattingPrompt({
                        esrsDatapoint: esrsDatapoint,
                        generationLanguage: datapointRequest.dataRequest.project.primaryContentLanguage,
                        reportTextGenerationRules: datapointRequest.dataRequest.project.reportTextGenerationRules,
                        customUserRemark: datapointRequest.customUserRemark,
                        generalCompanyProfile,
                        currentContent: datapointRequest.content,
                        reportingYear: datapointRequest.dataRequest.project.reportingYear,
                        predatapointGenerationChatCompletionResponse: predatapointGenerationChatCompletionResponse.response['datapoint'],
                    }),
                },
            ];
            datapointGenerationChatCompletionResponse =
                await this.llmRateLimiterService.handleRequest({
                    model: constants_1.LLM_MODELS['o3'],
                    messages: improvingFormattingPrompt,
                    json: true,
                    temperature: 0,
                });
        }
        if (datapointGenerationChatCompletionResponse.status === 400) {
            throw new Error(datapointGenerationChatCompletionResponse.response);
        }
        if (datapointGenerationChatCompletionResponse.response['datapoint'].startsWith('<br>')) {
            datapointGenerationChatCompletionResponse.response['datapoint'] =
                datapointGenerationChatCompletionResponse.response['datapoint'].slice(4);
        }
        let { reportText: generatedContent, citation: generatedCitation } = (0, llm_response_util_1.extractCitationsFromReportTextGeneration)(datapointGenerationChatCompletionResponse.response['datapoint'], documentChunksIndex);
        if (datapointGenerationChatCompletionResponse.response['citation']) {
            const citation = datapointGenerationChatCompletionResponse.response['citation'];
            generatedCitation = { ...generatedCitation, ...citation };
        }
        generatedContent = (0, llm_response_util_1.trimHtmlPreAndPostfix)(generatedContent);
        datapointRequest.content = generatedContent;
        let datapointMetadata;
        if (datapointRequest.metadata) {
            datapointMetadata = JSON.parse(datapointRequest.metadata);
            datapointMetadata.citation = generatedCitation;
            datapointMetadata = JSON.stringify(datapointMetadata);
        }
        else {
            datapointMetadata = JSON.stringify({
                citation: generatedCitation,
            });
        }
        const isAIEvaluator = await this.userService.userHasRequiredRole(userId, [
            user_workspace_entity_1.Role.SuperAdmin,
        ]);
        const generatedDataResponse = {
            content: generatedContent,
            metadata: datapointMetadata,
            id: datapointRequestId,
        };
        if (isAIEvaluator) {
            const dataGeneration = await this.datapointGenerationRepository.create({
                data: { content: generatedContent, metadata: datapointMetadata },
                datapointRequest: datapointRequest,
                evaluatorId: userId,
            });
            await this.datapointGenerationRepository.save(dataGeneration);
            generatedDataResponse.id = dataGeneration.id;
        }
        else {
            datapointRequest.metadata = datapointMetadata;
            datapointRequest.content = generatedContent;
            await this.datapointRequestRepository.save(datapointRequest);
        }
        await this.updateQueueStatus({
            datapointRequestId,
            queueStatus: null,
        });
        this.logger.log(`Finished Generating datapointRequest ${datapointRequest.id} with AI`);
        const globalAIUser = await this.userService.findGlobalGlacierAIUser();
        await this.workspaceService.storeActionHistory({
            event: 'datapoint_request_ai_generated',
            ref: datapointRequestId,
            workspaceId: workspaceId,
            versionData: {
                event: 'datapoint_request_ai_generated',
                doneBy: globalAIUser.id,
                issuedBy: userId,
                data: datapointRequest,
            },
        });
    }
    async loadDatapointCitations(datapointRequestId, citationId) {
        let citations;
        if ((0, common_util_1.isRequestForDataGenerationType)(datapointRequestId)) {
            const generationData = await this.findDatapointGenerationWithId((0, common_util_1.getGenerationIdFromRequestId)(datapointRequestId));
            const metadata = JSON.parse(generationData.data.metadata);
            citations = metadata ? (metadata.citation?.[citationId] ?? []) : [];
        }
        else {
            const datapointRequest = await this.datapointRequestRepository.findOne({
                where: { id: datapointRequestId },
            });
            citations = datapointRequest.metadata
                ? (JSON.parse(datapointRequest.metadata).citation?.[citationId] ?? [])
                : [];
        }
        const documentChunks = await this.datapointDocumentChunkMapRepository.find({
            where: {
                documentChunkId: (0, typeorm_2.In)(citations.map((citation) => citation.id)),
            },
            relations: ['documentChunk.document'],
        });
        return citations.map((citation) => {
            const documentChunkMaps = documentChunks.find((dc) => dc.documentChunk.id === citation.id);
            return {
                ...citation,
                ...documentChunkMaps.documentChunk,
            };
        });
    }
    async updateContentAndReplaceCitation({ datapointRequestId, citationId, index, workspaceId, userId, }) {
        const datapointRequest = await this.datapointRequestRepository.findOne({
            where: { id: datapointRequestId },
        });
        if (!datapointRequest || !datapointRequest.metadata) {
            throw new Error(`Datapoint request with ID ${datapointRequestId} not found`);
        }
        const metadata = JSON.parse(datapointRequest.metadata);
        metadata.citation = metadata.citation || {};
        const citations = metadata.citation[citationId] || [];
        const currentCitationIndex = citations.findIndex((citation) => citation.active);
        if (!citations[currentCitationIndex]) {
            throw new Error(`No active citation found for citationId ${citationId}`);
        }
        const newCitationIndex = index;
        if (!citations[newCitationIndex]) {
            throw new Error(`Citation index ${newCitationIndex} is invalid for citationId ${citationId}`);
        }
        const replacementValue = citations[newCitationIndex].value;
        const activeCitationValue = citations[currentCitationIndex].value;
        const regexMarkdown = new RegExp(`"${activeCitationValue}"`, 'g');
        const regexHtml = new RegExp(`>${activeCitationValue}<`, 'g');
        datapointRequest.content = datapointRequest.content
            .replace(regexMarkdown, `"${replacementValue}"`)
            .replace(regexHtml, `>${replacementValue}<`);
        citations[currentCitationIndex].active = false;
        citations[newCitationIndex].active = true;
        metadata.citation[citationId] = citations;
        datapointRequest.metadata = JSON.stringify(metadata);
        await this.datapointRequestRepository.save(datapointRequest);
        await this.workspaceService.storeActionHistory({
            event: 'datapoint_request_citation_replaced',
            ref: datapointRequestId,
            workspaceId: workspaceId,
            versionData: {
                event: 'datapoint_request_citation_replaced',
                doneBy: userId,
                data: datapointRequest,
            },
        });
        return datapointRequest;
    }
    async generateDatapointGenerationContextFromLinkedDocumentChunks(datapointRequest) {
        const documentTypePriority = {
            'Business Report': 1,
            'Sustainability Report': 2,
            'Materiality Analysis': 3,
            Policy: 4,
            Strategy: 5,
            Other: 6,
        };
        this.logger.log(`Number of linked DocumentChunks: ${datapointRequest.datapointDocumentChunkMap.filter((map) => map.active).length}`);
        const documentChunks = datapointRequest.datapointDocumentChunkMap
            .filter((map) => map.active)
            .map((datapointDocumentChunk) => {
            return {
                ...datapointDocumentChunk.documentChunk,
                key_information: datapointDocumentChunk.key_information,
                documentTitle: datapointDocumentChunk.documentChunk.document.name,
                year: datapointDocumentChunk.documentChunk.document.year,
                month: datapointDocumentChunk.documentChunk.document.month,
                day: datapointDocumentChunk.documentChunk.document.day,
                documentType: datapointDocumentChunk.documentChunk.document.documentType,
                remarks: datapointDocumentChunk.documentChunk.document.remarks,
            };
        })
            .sort((a, b) => {
            const dateA = new Date(a.year || 0, (a.month || 1) - 1, a.day || 1);
            const dateB = new Date(b.year || 0, (b.month || 1) - 1, b.day || 1);
            if (dateB.getTime() !== dateA.getTime()) {
                return dateB.getTime() - dateA.getTime();
            }
            const typePriorityA = documentTypePriority[a.documentType] || 999;
            const typePriorityB = documentTypePriority[b.documentType] || 999;
            if (typePriorityA !== typePriorityB) {
                return typePriorityA - typePriorityB;
            }
            const hasRemarkA = a.remarks ? 1 : 0;
            const hasRemarkB = b.remarks ? 1 : 0;
            return hasRemarkB - hasRemarkA;
        });
        const totalDocumentChunkLength = documentChunks.reduce((acc, documentChunk) => acc + documentChunk.content.length, 0);
        const numberOfLinkedDocumentChunks = documentChunks.length;
        this.logger.log(`${datapointRequest.id}: Total DocumentChunkLength ${totalDocumentChunkLength}, Number of LinkedChunks: ${numberOfLinkedDocumentChunks}`);
        const generateMetadataSection = async (documentChunk) => {
            let metadata = '';
            if (documentChunk.metadataJson) {
                metadata += `<Metadata>\nPart 1: ${documentChunk.metadataJson} \nPart 2:`;
            }
            else {
                metadata += `<Metadata>\nNo Base Metadata`;
            }
            if (documentChunk.documentId) {
                const document = await this.documentRepository.findOne({
                    where: { id: documentChunk.documentId },
                });
                if (document) {
                    const documentMetadata = [
                        document.documentType && `Document Type: ${document.documentType}`,
                        document.esrsCategory &&
                            `ESRS Category: ${document.esrsCategory.join(', ')}`,
                        document.year && `Year: ${document.year}`,
                        document.month && `Month: ${document.month}`,
                        document.day && `Day: ${document.day}`,
                        document.remarks && `Remarks: ${document.remarks}`,
                    ].filter(Boolean);
                    if (documentMetadata.length > 0) {
                        metadata += `\n\n${documentMetadata.join('\n')}`;
                    }
                }
            }
            metadata += '\n</Metadata>\n\n';
            return metadata;
        };
        const documentChunkSplitter = '\n\n';
        let generatedContext = '<Context>\n\n';
        if (totalDocumentChunkLength >
            this.MAX_DATAPOINT_GENERATION_DOCUMENT_CHUNKS_LENGHT) {
            const processedChunks = await Promise.all(documentChunks.map(async (documentChunk, index) => {
                const metadata = await generateMetadataSection(documentChunk);
                const chunkContent = `Document Chunk ID: chunk-${index + 1} \n Chunk Source:\n${metadata}\n`;
                if (documentChunk.key_information &&
                    documentChunk.key_information !== 'false') {
                    return chunkContent + documentChunk.key_information;
                }
                else if (documentChunk.content.length >
                    this.THRESHOLD_DOCUMENT_CHUNK_LENGTH_UNTIL_REDUCING) {
                    return (chunkContent +
                        (await this.reduceLinkedDocumentChunkContextToRelevantInformationOnly(datapointRequest, documentChunk)));
                }
                else {
                    return chunkContent + documentChunk.content;
                }
            }));
            generatedContext += processedChunks.join(documentChunkSplitter);
        }
        else {
            generatedContext += await Promise.all(documentChunks.map(async (documentChunk, index) => {
                const metadata = await generateMetadataSection(documentChunk);
                return `
          Document Chunk ID: chunk-${index + 1}
          Document: ${documentChunk.documentTitle} (${documentChunk.documentType}) - ${documentChunk.year}-${documentChunk.month}-${documentChunk.day}
          Page: ${documentChunk.page}:
          Metadata: ${metadata}
          --------------------------------------
          ${documentChunk.content}
          --------------------------------------`;
            })).then((chunks) => chunks.join(documentChunkSplitter));
        }
        if (generatedContext.length > this.MAX_CHATCONTENT_MESSAGE_LENGTH) {
            this.logger.log(`${datapointRequest.id}: Generated context exceeds maximum chat content message length. Shortening context.`);
            generatedContext = generatedContext.slice(0, this.MAX_CHATCONTENT_MESSAGE_LENGTH);
        }
        generatedContext + '\n</Context>';
        return {
            context: generatedContext,
            documentChunksIndex: documentChunks.map((dc) => dc.id),
        };
    }
    async reduceLinkedDocumentChunkContextToRelevantInformationOnly(datapointRequest, documentChunk) {
        this.logger.log(`${datapointRequest.id}: Reduce content of linked document Chunk ${documentChunk.id}, Length of document chunk: ${documentChunk.content.length}`);
        const extractedChunkInformation = [
            {
                role: 'user',
                content: this.promptService.reduceLinkedDocumentChunkPrompt(datapointRequest, documentChunk),
            },
        ];
        const reductionResponse = await this.llmRateLimiterService.handleRequest({
            model: constants_1.LLM_MODELS['gpt-4o'],
            messages: extractedChunkInformation,
            json: true,
            temperature: 0.2,
        });
        if (reductionResponse.status === 400) {
            throw new Error(reductionResponse.response);
        }
        const result = reductionResponse.response;
        await this.datapointDocumentChunkMapRepository.update({
            documentChunkId: documentChunk.id,
            datapointRequestId: datapointRequest.id,
        }, {
            key_information: result.keep_chunk_as_is === 'false'
                ? 'false'
                : result.key_information,
        });
        return result.keep_chunk_as_is === 'false'
            ? result.key_information
            : documentChunk.content;
    }
    datapointRequestStatusProcessor(datapointRequest) {
        const { content, datapointDocumentChunkMap: datapointDocumentChunkMaps, comments, } = datapointRequest;
        if (datapointRequest.status === datapoint_request_entity_1.DatapointRequestStatus.NotAnswered) {
            return datapoint_request_entity_1.DatapointRequestStatus.NotAnswered;
        }
        if (!(0, common_util_1.isTextPresentInHTML)(content)) {
            return datapoint_request_entity_1.DatapointRequestStatus.NoData;
        }
        const hasUnresolvedComments = comments.some((comment) => !comment.resolved);
        if (hasUnresolvedComments && content) {
            return datapoint_request_entity_1.DatapointRequestStatus.IncompleteData;
        }
        const noUnresolvedComments = comments.every((comment) => comment.resolved);
        if (content && noUnresolvedComments) {
            return datapoint_request_entity_1.DatapointRequestStatus.CompleteData;
        }
        return datapoint_request_entity_1.DatapointRequestStatus.NoData;
    }
    async updateContentWithMDRText(datapointRequestId, mdrText) {
        const datapointRequest = await this.findById(datapointRequestId);
        if (!datapointRequest) {
            throw new common_1.NotFoundException(`Datapoint Request not found`);
        }
        datapointRequest.content += `\n\n${mdrText}`;
        await this.datapointRequestRepository.save(datapointRequest);
    }
    async approveDatapointGeneration({ datapointRequestId }) {
        const dataGeneration = await this.datapointGenerationRepository.findOne({
            where: {
                datapointRequest: { id: datapointRequestId },
                status: datapoint_generation_entity_1.datapointGenerationStatus.Pending,
            },
        });
        const datapointRequest = await this.datapointRequestRepository.findOne({
            where: { id: datapointRequestId },
        });
        datapointRequest.content = dataGeneration.data.content;
        datapointRequest.metadata = dataGeneration.data.metadata;
        await this.datapointGenerationRepository.update(datapointRequestId, {
            status: datapoint_generation_entity_1.datapointGenerationStatus.Approved,
        });
        return datapointRequest;
    }
    async updateGenerationStatus({ datapointGenerationId, status, userId, workspaceId, }) {
        try {
            let responseData = { status };
            await this.datapointGenerationRepository.update({
                id: datapointGenerationId,
            }, { status, evaluatorId: userId, evaluatedAt: new Date() });
            const generation = await this.datapointGenerationRepository.findOne({
                where: { id: datapointGenerationId },
                relations: ['datapointRequest', 'evaluator'],
            });
            responseData.evaluator = generation.evaluator;
            responseData.evaluatedAt = generation.evaluatedAt;
            const existingDatapoint = await this.datapointRequestRepository.findOne({
                where: { id: generation.datapointRequest.id },
            });
            if (status === datapoint_generation_entity_1.datapointGenerationStatus.Approved ||
                status === datapoint_generation_entity_1.datapointGenerationStatus.MinorChanges) {
                existingDatapoint.content = generation.data.content;
                existingDatapoint.metadata = generation.data.metadata;
                existingDatapoint.status = datapoint_request_entity_1.DatapointRequestStatus.CompleteData;
                await this.update({
                    datapointRequestId: existingDatapoint.id,
                    updateDatapointRequestPayload: existingDatapoint,
                    userId,
                    workspaceId,
                });
                responseData = { ...responseData, content: generation.data.content };
            }
            return responseData;
        }
        catch (err) {
            console.log(err);
            throw new Error(`Error while updating generation status: ${err.message}`);
        }
    }
    async findDatapointRequestsExcludingStatus({ dataRequestId, status, }) {
        return await this.datapointRequestRepository.find({
            where: {
                dataRequestId,
                status: (0, typeorm_2.Not)((0, typeorm_2.In)(status)),
            },
        });
    }
    async findDatapointRequestsByStatus({ dataRequestId, status, }) {
        return await this.datapointRequestRepository.find({
            where: {
                dataRequestId,
                status: (0, typeorm_2.In)(status),
            },
            relations: {
                datapointDocumentChunkMap: {
                    documentChunk: true,
                },
                datapointGenerations: true,
            },
        });
    }
    async updateStatus({ id, status, }) {
        await this.datapointRequestRepository.update({ id }, { status });
    }
    async updateQueueStatus({ datapointRequestId, queueStatus, }) {
        await this.datapointRequestRepository.update({ id: datapointRequestId }, { queueStatus });
    }
    async loadDocumentLinks(datapointRequestId) {
        const documentLinks = await this.datapointDocumentChunkMapRepository.find({
            where: { datapointRequestId, active: true },
            relations: ['documentChunk', 'documentChunk.document'],
        });
        return documentLinks;
    }
};
exports.DatapointRequestService = DatapointRequestService;
exports.DatapointRequestService = DatapointRequestService = DatapointRequestService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(datapoint_request_entity_1.DatapointRequest)),
    __param(1, (0, typeorm_1.InjectRepository)(datapoint_document_chunk_entity_1.DatapointDocumentChunk)),
    __param(2, (0, typeorm_1.InjectRepository)(esrs_datapoint_entity_1.ESRSDatapoint)),
    __param(3, (0, typeorm_1.InjectRepository)(esrs_topic_datapoint_entity_1.ESRSTopicDatapoint)),
    __param(4, (0, typeorm_1.InjectRepository)(datapoint_generation_entity_1.DatapointGeneration)),
    __param(5, (0, typeorm_1.InjectRepository)(document_entity_1.Document)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        prompts_service_1.PromptService,
        mdr_prompts_service_1.MDRPromptService,
        datapoint_generation_prompts_service_1.NormalDpPromptService,
        table_prompts_service_1.TablePromptService,
        numerics_prompts_service_1.NumericsPromptService,
        project_service_1.ProjectService,
        users_service_1.UsersService,
        workspace_service_1.WorkspaceService,
        llm_rate_limiter_service_1.LlmRateLimiterService])
], DatapointRequestService);
//# sourceMappingURL=datapoint-request.service.js.map