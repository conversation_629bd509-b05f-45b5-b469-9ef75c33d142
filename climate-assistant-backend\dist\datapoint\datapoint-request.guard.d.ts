import { CanActivate, ExecutionContext } from '@nestjs/common';
import { DatapointRequestService } from './datapoint-request.service';
import { DataRequestService } from '../data-request/data-request.service';
import { Reflector } from '@nestjs/core';
export declare class DatapointRequestGuard implements CanActivate {
    private readonly dataRequestService;
    private readonly datapointRequestService;
    private readonly reflector;
    constructor(dataRequestService: DataRequestService, datapointRequestService: DatapointRequestService, reflector: Reflector);
    canActivate(context: ExecutionContext): Promise<boolean>;
}
