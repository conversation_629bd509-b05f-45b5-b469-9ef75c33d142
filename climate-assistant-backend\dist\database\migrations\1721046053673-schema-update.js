"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1721046053673 = void 0;
class SchemaUpdate1721046053673 {
    constructor() {
        this.name = 'SchemaUpdate1721046053673';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "chat_message" DROP CONSTRAINT "FK_4c772df41e9f3e87b644158fea7"`);
        await queryRunner.query(`ALTER TABLE "chat_history" DROP CONSTRAINT "FK_6bac64204c7b416f465e17957ed"`);
        await queryRunner.query(`ALTER TABLE "chat_history"
        ALTER COLUMN "userId" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "chat_message"
        ADD CONSTRAINT "FK_4c772df41e9f3e87b644158fea7" FOREIGN KEY ("chatHistoryId") REFERENCES "chat_history" ("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "chat_history"
        ADD CONSTRAINT "FK_6bac64204c7b416f465e17957ed" FOREIGN KEY ("userId") REFERENCES "user" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "chat_history" DROP CONSTRAINT "FK_6bac64204c7b416f465e17957ed"`);
        await queryRunner.query(`ALTER TABLE "chat_message" DROP CONSTRAINT "FK_4c772df41e9f3e87b644158fea7"`);
        await queryRunner.query(`ALTER TABLE "chat_history"
        ALTER COLUMN "userId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "chat_history"
        ADD CONSTRAINT "FK_6bac64204c7b416f465e17957ed" FOREIGN KEY ("userId") REFERENCES "user" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "chat_message"
        ADD CONSTRAINT "FK_4c772df41e9f3e87b644158fea7" FOREIGN KEY ("chatHistoryId") REFERENCES "chat_history" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
}
exports.SchemaUpdate1721046053673 = SchemaUpdate1721046053673;
//# sourceMappingURL=1721046053673-schema-update.js.map