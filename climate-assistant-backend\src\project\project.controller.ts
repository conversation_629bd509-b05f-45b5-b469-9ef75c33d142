import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
  Res,
} from '@nestjs/common';
import { ProjectService } from './project.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  CreateProjectRequest,
  UpdateProjectRequest,
} from './entities/project.dto';
import { CommentType } from './entities/comment.entity';
import { ProjectGuard } from './project.guard';
import { AuthGuard } from 'src/auth/supabase/supabase.auth.guard';
import { Response } from 'express';
import { ESRSTopicLevel } from 'src/knowledge-base/entities/esrs-topic.entity';
import { CommentStatus } from './entities/comment-generation.entity';

@ApiTags('projects')
@UseGuards(AuthGuard)
@Controller('projects')
export class ProjectController {
  constructor(private readonly projectsService: ProjectService) {}

  @Get()
  @ApiOperation({ summary: 'List all projects' })
  @ApiResponse({ status: 200, description: 'Projects retrieved successfully' })
  async listAllProjects(@Request() req) {
    const workspace = req.user.workspaceId;
    const projects = await this.projectsService.findAll(workspace);
    return projects;
  }

  @Post()
  @HttpCode(201)
  @ApiOperation({ summary: 'Create a new project' })
  @ApiResponse({ status: 201, description: 'Project created successfully' })
  async createProject(
    @Request() req,
    @Body() createProjectRequest: CreateProjectRequest
  ) {
    const workspaceId = req.user.workspaceId;
    const userId = req.user.id;
    const project = await this.projectsService.create({
      workspaceId,
      userId,
      createProjectRequest,
    });
    return project;
  }

  @Post('/starter')
  @HttpCode(201)
  @ApiOperation({ summary: 'Create a new starter project' })
  @ApiResponse({ status: 201, description: 'Project created successfully' })
  async createBaseStarterProject(
    @Body()
    {
      workspaceId,
      userId,
      createProjectRequest,
    }: {
      workspaceId: string;
      userId: string;
      createProjectRequest: CreateProjectRequest;
    }
  ) {
    const project = await this.projectsService.create({
      workspaceId,
      userId,
      createProjectRequest,
    });
    return project;
  }

  @Get('/esrs-datapoints')
  @ApiOperation({ summary: 'List ESRS datapoints' })
  @ApiResponse({
    status: 200,
    description: 'ESRS datapoints retrieved successfully',
  })
  async listEsrsDatapoints(@Request() req, @Query('esrs') esrs: string) {
    const workspaceId = req.user.workspaceId;
    const esrsDatapoints =
      await this.projectsService.findAssignedESRSDatapoints({
        esrs,
        workspaceId,
      });
    return esrsDatapoints;
  }

  @UseGuards(ProjectGuard)
  @Get('/:projectId')
  @ApiOperation({ summary: 'Get project by ID' })
  @ApiResponse({ status: 200, description: 'Project retrieved successfully' })
  async getProjectById(@Param('projectId') projectId: string) {
    return await this.projectsService.findData(projectId);
  }

  @UseGuards(ProjectGuard)
  @Put('/:projectId')
  @ApiOperation({ summary: 'Update project by ID' })
  @ApiResponse({ status: 200, description: 'Project updated successfully' })
  async updateProjectById(
    @Request() req,
    @Param('projectId') projectId: string,
    @Body() updateProjectRequest: UpdateProjectRequest
  ) {
    const workspace = req.user.workspaceId;
    const userId = req.user.id;
    const project = await this.projectsService.update({
      projectId,
      workspaceId: workspace,
      createdBy: userId,
      updateProjectRequest,
    });
    return project;
  }

  @UseGuards(ProjectGuard)
  @Delete('/:projectId')
  @HttpCode(204)
  @ApiOperation({ summary: 'Delete project by ID' })
  @ApiResponse({ status: 204, description: 'Project deleted successfully' })
  async deleteProjectById(
    @Request() req,
    @Param('projectId') projectId: string
  ) {
    const workspaceId = req.user.workspaceId;
    const userId = req.user.id;
    await this.projectsService.delete({ projectId, workspaceId, userId });
    return { statusCode: 204, message: 'Project deleted successfully' };
  }

  @UseGuards(ProjectGuard)
  @Post('/:projectId/comment/create')
  @HttpCode(201)
  @ApiOperation({ summary: 'Create a comment on a data request' })
  @ApiResponse({ status: 201, description: 'Comment created successfully' })
  async commentDataRequest(
    @Request() req,
    @Body()
    data: {
      comment: string;
      commentableType: CommentType;
      commentableId: string;
    }
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const { commentableId, commentableType } = data;
    const addComment = await this.projectsService.addComment({
      commentableId,
      commentableType,
      userId,
      workspaceId,
      comment: data.comment,
    });

    return addComment;
  }

  @UseGuards(ProjectGuard)
  @Put('/:projectId/comment/:commentId')
  @ApiOperation({ summary: 'Update a comment on a data request' })
  @ApiResponse({ status: 200, description: 'Comment updated successfully' })
  async updateCommentDataRequest(
    @Request() req,
    @Param('commentId') commentId: string,
    @Body() data: { comment: string }
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const addComment = await this.projectsService.updateComment({
      commentId,
      userId,
      workspaceId,
      comment: data.comment,
    });

    return addComment;
  }

  @UseGuards(ProjectGuard)
  @Put('/:projectId/comment/:commentId/status')
  @ApiOperation({ summary: 'Update a AI comment review status' })
  @ApiResponse({
    status: 200,
    description: 'Comment review updated successfully',
  })
  async updateCommentGenerationStatus(
    @Request() req,
    @Param('commentId') commentId: string,
    @Body() data: { status: CommentStatus; evaluatorComment: string }
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const addComment = await this.projectsService.updateCommentGenerationStatus(
      {
        commentId,
        userId,
        workspaceId,
        data,
      }
    );

    return addComment;
  }

  @UseGuards(ProjectGuard)
  @Put('/:projectId/comment/:commentId/resolve')
  @ApiOperation({ summary: 'Resolve a comment on a data request' })
  @ApiResponse({ status: 200, description: 'Comment resolved successfully' })
  async resolveCommentDataRequest(
    @Param('commentId') commentId: string,
    @Body() data: { resolve: boolean }
  ) {
    const resolve = await this.projectsService.resolveComment({
      commentId,
      resolution: data.resolve,
    });

    return resolve;
  }

  @UseGuards(ProjectGuard)
  @Delete('/:projectId/comment/:commentId')
  @HttpCode(204)
  @ApiOperation({ summary: 'Delete a comment on a data request' })
  @ApiResponse({ status: 204, description: 'Comment deleted successfully' })
  async deleteCommentDataRequest(
    @Request() req,
    @Param('commentId') commentId: string
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    await this.projectsService.deleteComment({
      commentId,
      userId,
      workspaceId,
    });
    return { statusCode: 204, message: 'Comment deleted successfully' };
  }

  @UseGuards(ProjectGuard)
  @Get('/:projectId/export-reporttext')
  @ApiOperation({ summary: 'Generate DOCX report text for a project' })
  @ApiResponse({
    status: 200,
    description: 'DOCX report text generated successfully',
  })
  async generateDocx(
    @Param('projectId') projectId: string,
    @Res() res: Response
  ) {
    const project = await this.projectsService.findById(projectId);
    const docxBuffer = await this.projectsService.generateDocx(projectId);

    res.setHeader(
      'Content-Disposition',
      `attachment; filename="${project.name} Reporttext.docx"`
    );
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document; charset=UTF-8'
    );
    res.end(docxBuffer);
  }

  @UseGuards(ProjectGuard)
  @Get('/:projectId/export-gaps')
  @ApiOperation({ summary: 'Generate XLSX datapoint gaps for a project' })
  @ApiResponse({
    status: 200,
    description: ' XLSX datapoint gaps generated successfully',
  })
  async generateXlsx(
    @Param('projectId') projectId: string,
    @Res() res: Response
  ) {
    const project = await this.projectsService.findById(projectId);
    const docxBuffer = await this.projectsService.generateXlsx(projectId);

    res.setHeader(
      'Content-Disposition',
      `attachment; filename="${project.name} Gaps.xlsx"`
    );
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    res.end(docxBuffer);
  }

  @UseGuards(ProjectGuard)
  @Get('/:projectId/materiality')
  @ApiOperation({ summary: 'Get materiality status for a project' })
  @ApiResponse({
    status: 200,
    description: 'Materiality status retrieved successfully',
  })
  async findMaterialityStatus(@Param('projectId') projectId: string) {
    const data = await this.projectsService.findMaterialityStatus(projectId);
    return data;
  }

  @UseGuards(ProjectGuard)
  @Put('/:projectId/materiality')
  @ApiOperation({ summary: 'Update materiality status for a project' })
  @ApiResponse({
    status: 200,
    description: 'Materiality status updated successfully',
  })
  async updateMaterialityStatus(
    @Param('projectId') projectId: string,
    @Body()
    body: {
      materialTopics: {
        esrsTopicId: number;
        level: ESRSTopicLevel;
        active: boolean;
      }[];
    }
  ) {
    const { materialTopics } = body;
    const updatedMaterialStatus =
      await this.projectsService.updateMaterialityStatus(
        projectId,
        materialTopics
      );
    return { success: true, ...updatedMaterialStatus };
  }
}
