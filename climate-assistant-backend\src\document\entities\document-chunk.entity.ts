import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
  CreateDateColumn,
} from 'typeorm';
import { Document } from './document.entity';
import { DatapointDocumentChunk } from '../../datapoint-document-chunk/entities/datapoint-document-chunk.entity';

@Entity()
export class DocumentChunk {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  documentId: string;

  @Column({ type: 'varchar' })
  page: string;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'json', nullable: true })
  metadataJson: string;

  @Column({ type: 'text', nullable: true })
  matchingsJson: string;

  @ManyToOne(() => Document, (document) => document.chunks)
  @JoinColumn({ name: 'documentId' })
  document: Document;

  @OneToMany(
    () => DatapointDocumentChunk,
    (datapointDocumentChunkMap) => datapointDocumentChunkMap.documentChunk,
  )
  datapointDocumentChunkMap: DatapointDocumentChunk[];

  @CreateDateColumn()
  createdAt: Date;
}
