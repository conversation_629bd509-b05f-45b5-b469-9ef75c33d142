import { FunctionComponent, useState } from 'react';

import LoginForm from './LoginForm';
import ForgotPassword from './ForgotPassword';
import EmailSent from './EmailSent';
import LeftSection from './LeftSection';

enum LOGIN_STATE {
  LOGIN,
  REGISTER,
  FORGOT_PASSWORD,
  EMAIL_SENT,
}

const Login: FunctionComponent = () => {
  const [loginState, setLoginState] = useState<LOGIN_STATE>(LOGIN_STATE.LOGIN);

  return (
    <div className={`flex flex-row w-full h-screen`}>
      <LeftSection />
      <div className={`flex flex-1 flex-col justify-center items-center`}>
        {loginState === LOGIN_STATE.LOGIN && (
          <LoginForm
            switchToForgotPassword={() => {
              setLoginState(LOGIN_STATE.FORGOT_PASSWORD);
            }}
          />
        )}
        {loginState === LOGIN_STATE.FORGOT_PASSWORD && (
          <ForgotPassword
            switchToLogin={() => {
              setLoginState(LOGIN_STATE.LOGIN);
            }}
            switchToEmailSent={() => {
              setLoginState(LOGIN_STATE.EMAIL_SENT);
            }}
          />
        )}
        {loginState === LOGIN_STATE.EMAIL_SENT && (
          <EmailSent
            switchToLogin={() => {
              setLoginState(LOGIN_STATE.LOGIN);
            }}
          />
        )}
      </div>
    </div>
  );
};

export default Login;
