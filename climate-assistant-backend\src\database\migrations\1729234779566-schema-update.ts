import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1729234779566 implements MigrationInterface {
  name = 'SchemaUpdate1729234779566';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ADD "esrsDisclosureRequirementId" integer`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ADD CONSTRAINT "FK_809f5e1cde50fe0978efc545ead" FOREIGN KEY ("esrsDisclosureRequirementId") REFERENCES "esrs_disclosure_requirement"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" DROP CONSTRAINT "FK_809f5e1cde50fe0978efc545ead"`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" DROP COLUMN "esrsDisclosureRequirementId"`,
    );
  }
}
