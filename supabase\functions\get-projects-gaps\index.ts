
// @ts-ignore
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { authValidator } from "../_shared/authValidator.ts";

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }

  try {
    // Get the request body
    const { projectId, questionId } = await req.json();

    const { user, error, supabaseClient, response: errResponse } = await authValidator(req);
    
    if (!user || error || !supabaseClient) {
      return errResponse;
    }

    const projectIds: string[] = projectId ? [projectId] : [];
    let projectNamesMap: Record<string, string> = {};

    if (!projectId) {
      // fetch all projects for the user
      const { data: projectsData, error: projectsError } = await supabaseClient
        .from('project')
        .select('id, name')
        .eq('workspaceId', user.user_workspace[0].workspaceId)
        .eq('type', 'EcoVadis')

      if (projectsError) {
        console.error('Error fetching projects:', projectsError);
        return new Response(JSON.stringify({
          error: 'Failed to fetch projects'
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 500
        });
      }

      projectIds.push(...(projectsData || []).map(project => project.id));
      // Create project names mapping
      projectNamesMap = (projectsData || []).reduce((map, project) => ({
        ...map,
        [project.id]: project.name
      }), {});
    } else {
      // If specific projectId is provided, fetch its name
      const { data: projectData, error: projectError } = await supabaseClient
        .from('project')
        .select('id, name')
        .eq('id', projectId)
        .single();
      
      if (projectError) {
        console.error('Error fetching project:', projectError);
      } else if (projectData) {
        projectNamesMap[projectData.id] = projectData.name;
      }
    }
    
    // Fetch all gaps for the specified project with theme data
    const query = supabaseClient
      .from('project_ecovadis_gaps')
      .select(`
        *,
        ecovadis_question (
          id,
          questionCode,
          indicator,
          ecovadis_theme (
            id,
            title
          )
        )
      `)
      .in('projectId', projectIds)
      .order('createdAt', { ascending: true });

    if(questionId) {
      query.eq('questionId', questionId);
    }

    const { data: gapsData, error: gapsError } = await query;
      
    if (gapsError) {
      console.error('Error fetching gaps:', gapsError);
      return new Response(JSON.stringify({
        error: 'Failed to fetch project gaps'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }
    
    // Collect all document IDs from gaps
    const allDocumentIds: string[] = [];
    (gapsData || []).forEach(gap => {
      if (gap.documents && Array.isArray(gap.documents)) {
        allDocumentIds.push(...gap.documents);
      }
    });
    
    // Get unique document IDs
    const uniqueDocumentIds = [...new Set(allDocumentIds)].filter(Boolean);
    
    // Fetch document names for all gap documents
    let documentsMap = {};
    if (uniqueDocumentIds.length > 0) {
      const { data: documentsData, error: documentsError } = await supabaseClient
        .from('document')
        .select('id, name')
        .in('id', uniqueDocumentIds);
      
      if (documentsError) {
        console.error('Error fetching documents:', documentsError);
      } else {
        documentsMap = (documentsData || []).reduce((map, doc) => ({ ...map, [doc.id]: doc.name }), {});
      }
    }
    
    // Process gaps to ensure proper format and include document information
    const processedGaps = (gapsData || []).map(gap => ({
      ...gap,
      // Add project name to each gap
      projectName: projectNamesMap[gap.projectId] || 'Unknown Project',
      // Ensure gaps field is properly parsed if it's a string
      gaps: typeof gap.gaps === 'string' ? JSON.parse(gap.gaps) : gap.gaps,
      // Map document IDs to document objects with name
      documents: Array.isArray(gap.documents) 
        ? gap.documents.map(docId => ({
            id: docId,
            name: documentsMap[docId] || 'Unknown Document',
          }))
        : []
    }));

    return new Response(JSON.stringify(processedGaps), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});
