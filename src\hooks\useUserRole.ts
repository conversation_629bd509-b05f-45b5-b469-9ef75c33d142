
import { useAuth } from '@/context/AuthContext';
import { useAuthentication } from '@/api/authentication/authentication.query';
import { USER_ROLE } from '@/constants/workspaceConstants';

export function useUserRole() {
  const auth = useAuth();
  const { user: apiUser } = useAuthentication();
  
  // Function to check if the user has any of the specified roles
  const hasRole = (roles: USER_ROLE[]) => {
    // First try with apiUser (from React Query)
    if (apiUser?.user_workspace?.length) {
      return roles.includes(apiUser.user_workspace[0].role);
    }
    
    // Then try with auth context user
    if (auth.user?.app_metadata?.workspaces && auth.currentWorkspace) {
      const workspaceRole = auth.user.app_metadata.workspaces[auth.currentWorkspace];
      return roles.includes(workspaceRole as USER_ROLE);
    }
    
    // Direct role property
    if (apiUser?.role) {
      return roles.includes(apiUser.role);
    }
    
    return false;
  };
  
  // Shortcut functions for common role checks
  const isAdmin = () => hasRole([USER_ROLE.SuperAdmin, USER_ROLE.WorkspaceAdmin]);
  const isContributor = () => hasRole([USER_ROLE.Contributor]);
  const isAIContributor = () => hasRole([USER_ROLE.AiContributor]);
  const isAIReviewer = () => hasRole([USER_ROLE.AiReviewer]);
  
  // Get the current user's role
  const getUserRole = (): USER_ROLE | undefined => {
    if (apiUser?.user_workspace?.length) {
      return apiUser.user_workspace[0].role;
    }
    
    if (auth.user?.app_metadata?.workspaces && auth.currentWorkspace) {
      return auth.user.app_metadata.workspaces[auth.currentWorkspace] as USER_ROLE;
    }
    
    return apiUser?.role;
  };
  
  return {
    hasRole,
    isAdmin,
    isContributor,
    isAIContributor,
    isAIReviewer,
    getUserRole
  };
}
