import { Injectable, Logger } from '@nestjs/common';
import { encoding_for_model, TiktokenModel } from '@dqbd/tiktoken';
import { AzureOpenAI } from 'openai';
import OpenAI from 'openai';
import { Stream } from 'openai/streaming';
import {
  ChatCompletionMessageParam,
  ChatCompletionMessageToolCall,
} from 'openai/resources';
import { CustomGptTool } from '../util/chat-gpt.models';
import { LLM_MODELS } from 'src/constants';
import { TimeoutError } from 'src/middleware/error.middleware';
import axios from 'axios';

const seed = 123;
const default_temperature = 0;

@Injectable()
export class ChatGptService {
  private readonly logger = new Logger(ChatGptService.name);
  public openAiClient: OpenAI;
  public azureOpenAiClient: AzureOpenAI;
  public azureo1Client: AzureOpenAI;
  public azureo4miniClient: AzureOpenAI;

  constructor() {
    // Regular OpenAI client
    this.openAiClient = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      baseURL: 'https://oai.hconeai.com/v1',
      defaultHeaders: {
        'Helicone-Auth': `Bearer ${process.env.HELICONE_AUTH_API_KEY}`,
        'Helicone-RateLimit-Policy': '5000;w=3600;u=requests;s=user', //"5000;w=3600;u=cents;s=user" 50 USD per hour
      },
    });

    // Azure OpenAI client
    this.azureOpenAiClient = new AzureOpenAI({
      apiKey: process.env.AZURE_OPENAI_API_KEY,
      apiVersion: '2025-01-01-preview',
      baseURL: `https://oai.helicone.ai/openai/deployments/gpt-4o-2`,
      defaultHeaders: {
        'Helicone-Auth': `Bearer ${process.env.HELICONE_AUTH_API_KEY}`,
        'Helicone-OpenAI-API-Base': `${process.env.AZURE_RESOURCE}`,
        'api-key': process.env.AZURE_OPENAI_API_KEY,
        'Helicone-RateLimit-Policy': '3000;w=3600;u=requests;s=user', //"5000;w=3600;u=cents;s=user" 50 USD per hour
      },
    });

    // Azure OpenAI o4mini client
    this.azureo4miniClient = new AzureOpenAI({
      apiKey:
        '5cHVNxg0WiR4JPZFeVP3cbl4WX25uBuTcQPlmQ3c0D6NVnHjUM5BJQQJ99AJACfhMk5XJ3w3AAABACOGKAva',
      apiVersion: '2024-12-01-preview',
      baseURL: `https://oai.helicone.ai/openai/deployments/o4-mini`,
      defaultHeaders: {
        'Helicone-Auth': `Bearer ${process.env.HELICONE_AUTH_API_KEY}`,
        'Helicone-OpenAI-API-Base': `https://simon-m2vo32nd-swedencentral.openai.azure.com/`, // should be `${process.env.AZURE_RESOURCE}` as soon as germany is supported,
        'api-key':
          '5cHVNxg0WiR4JPZFeVP3cbl4WX25uBuTcQPlmQ3c0D6NVnHjUM5BJQQJ99AJACfhMk5XJ3w3AAABACOGKAva',
        'Helicone-RateLimit-Policy': '3000;w=3600;u=requests;s=user', //"5000;w=3600;u=cents;s=user" 50 USD per hour
      },
    });
    // Azure OpenAI o1 client
    this.azureo1Client = new AzureOpenAI({
      apiKey:
        '5cHVNxg0WiR4JPZFeVP3cbl4WX25uBuTcQPlmQ3c0D6NVnHjUM5BJQQJ99AJACfhMk5XJ3w3AAABACOGKAva',
      apiVersion: '2024-12-01-preview',
      baseURL: `https://oai.helicone.ai/openai/deployments/o1`,
      defaultHeaders: {
        'Helicone-Auth': `Bearer ${process.env.HELICONE_AUTH_API_KEY}`,
        'Helicone-OpenAI-API-Base': `https://simon-m2vo32nd-swedencentral.openai.azure.com/`, // should be `${process.env.AZURE_RESOURCE}` as soon as germany is supported,
        'api-key':
          '5cHVNxg0WiR4JPZFeVP3cbl4WX25uBuTcQPlmQ3c0D6NVnHjUM5BJQQJ99AJACfhMk5XJ3w3AAABACOGKAva',
        'Helicone-RateLimit-Policy': '3000;w=3600;u=requests;s=user', //"5000;w=3600;u=cents;s=user" 50 USD per hour
      },
    });
  }

  //async openAIClient() {
  //  //return this.azureOpenAiClient;
  //  return this.openAiClient;
  //}
  //async azureOpenAIClient() {
  //  return this.azureOpenAiClient;
  //return this.openAiClient;
  //}

  async callDeepSeek(messages: ChatCompletionMessageParam[]): Promise<string> {
    const params = {
      model: 'deepseek-r1',
      messages: messages,
    };

    const response = await axios.post(
      'https://stefa-m7vs3xre-eastus2.services.ai.azure.com/models/chat/completions',
      params,
      {
        headers: {
          'Content-Type': 'application/json',
          'api-key':
            '1l5KKJdVvVfAC5Gt5e1wjRkrHKQWDT49M1zoraMkVVkSUmQV1wURJQQJ99BCACHYHv6XJ3w3AAAAACOG4OqS',
        },
        timeout: 6 * 60 * 1000,
      }
    );

    let content = response.data.choices[0].message.content ?? '';
    const endOfThink = content.indexOf('</think>');
    if (endOfThink !== -1) {
      content = content.substring(endOfThink + '</think>'.length).trim();
    }
    return content;
  }

  async createCompletion(
    model: LLM_MODELS,
    messages: ChatCompletionMessageParam[],
    json = false
  ): Promise<string> {
    const titleParams: OpenAI.Chat.ChatCompletionCreateParams = {
      model,
      messages: messages as OpenAI.Chat.ChatCompletionMessageParam[],
      seed,
      temperature: default_temperature,
      response_format: json
        ? {
            type: 'json_object',
          }
        : undefined,
    };
    const openai = this.openAiClient;
    const result: OpenAI.Chat.ChatCompletion =
      await openai.chat.completions.create(titleParams);

    return result.choices[0].message.content ?? '';
  }

  async createCompletionWithAzure(
    model: LLM_MODELS,
    messages: ChatCompletionMessageParam[],
    json = false
  ): Promise<string> {
    const titleParams: OpenAI.Chat.ChatCompletionCreateParams = {
      model,
      messages: messages as OpenAI.Chat.ChatCompletionMessageParam[],
      seed,
      temperature: default_temperature,
      response_format: json
        ? {
            type: 'json_object',
          }
        : undefined,
    };
    const openai = this.azureOpenAiClient;
    const result: OpenAI.Chat.ChatCompletion =
      await openai.chat.completions.create(titleParams);

    return result.choices[0].message.content ?? '';
  }

  calculateTokens(
    messages: OpenAI.Chat.ChatCompletionMessageParam[],
    model = 'gpt-4o' as TiktokenModel
  ): number {
    const encoder = encoding_for_model(model); // Use the appropriate model name
    let totalTokens = 0;

    for (const message of messages) {
      // Count tokens in each part of the message
      if (typeof message.content === 'string') {
        totalTokens += encoder.encode(message.content).length;
      }
    }

    encoder.free(); // Free resources
    return totalTokens;
  }

  async createCompletionWithToken(
    model: LLM_MODELS,
    messages: ChatCompletionMessageParam[],
    json = false,
    temperature = default_temperature
  ): Promise<{
    response: any;
    status: number;
    token: {
      prompt_tokens: number;
      completion_tokens: number;
      total_cost: number;
    };
  }> {
    const titleParams: OpenAI.Chat.ChatCompletionCreateParams = {
      model,
      messages: messages as OpenAI.Chat.ChatCompletionMessageParam[],
      seed,
      temperature: temperature,
      response_format: json
        ? {
            type: 'json_object',
          }
        : undefined,
    };

    // We allow one retry if we hit a rate-limit error (429) with a valid wait time <= 30
    // starting attempt at 1 for better readability - first attempt is not a retry
    let attempts = 1;
    const maxAttempts = 5;

    while (attempts < maxAttempts) {
      // Decide which model to use this round:
      //  - First two attempts keep 'o1'
      //  - From the 3rd attempt onwards, switch to '4o'
      let currentModel = model;
      if (model === LLM_MODELS.o1 && attempts > 2) {
        currentModel = LLM_MODELS['gpt-4o'];
      }

      // Adjust OpenAI client & params based on the current model
      let openai;
      if (currentModel === LLM_MODELS.o1) {
        delete titleParams.temperature;
        titleParams.reasoning_effort = 'medium';
        // If Azure OpenAI client fails in first attempt, switch to OpenAI client
        if (attempts > 1) {
          openai = this.openAiClient;
        } else {
          openai = this.azureo1Client;
        }
        // elseif for o3-mini-2025-01-31
      } else if (currentModel === LLM_MODELS['o3-mini-2025-01-31']) {
        delete titleParams.temperature;
        titleParams.reasoning_effort = 'medium';
        openai = this.openAiClient;
      } else if (currentModel === LLM_MODELS['o3']) {
        delete titleParams.temperature;
        titleParams.reasoning_effort = 'medium';
        openai = this.openAiClient;
      } else if (currentModel === LLM_MODELS['o4-mini']) {
        delete titleParams.temperature;
        titleParams.reasoning_effort = 'medium';
        openai = this.azureo4miniClient;
      } else {
        // Restore the temperature
        titleParams.temperature = temperature;
        // Remove reasoning_effort if previously set
        delete (titleParams as any).reasoning_effort;
        openai = this.azureOpenAiClient;
      }

      titleParams.model = currentModel;

      try {
        let response = '';
        let promptTokens = 0;
        let completionTokens = 0;

        if (titleParams.model === 'deepseek-r1') {
          response = await this.callDeepSeek(messages);
        } else {
          const result: OpenAI.Chat.ChatCompletion = await Promise.race([
            openai.chat.completions.create(titleParams),
            new Promise<OpenAI.Chat.ChatCompletion>((_, reject) =>
              setTimeout(
                () =>
                  reject(new TimeoutError('Request timed out after 3 minutes')),
                3 * 60 * 1000 // 3 minutes
              )
            ),
          ]);

          response = result.choices[0].message.content ?? '';
          promptTokens = result.usage?.prompt_tokens ?? 0;
          completionTokens = result.usage?.completion_tokens ?? 0;
        }

        return {
          response: json ? JSON.parse(response) : response,
          status: 200,
          token: {
            prompt_tokens: promptTokens,
            completion_tokens: completionTokens,
            total_cost: this.calculateCost({
              model,
              inputTokens: promptTokens,
              outputTokens: completionTokens,
            }),
          },
        };
      } catch (error: any) {
        attempts++;

        if (error instanceof TimeoutError) {
          this.logger.log(
            `Timeout Error: ${error.message}. Retrying attempt ${attempts}/${maxAttempts}.`
          );

          if (attempts > 2) {
            this.logger.log(
              `Switching to model '4o' after ${attempts} timeout failures.`
            );
          }
          continue; // Retry
        }

        const httpStatus = error?.status || error?.response?.status;

        const isInvalidChatRequest = httpStatus === 400;
        if (isInvalidChatRequest) {
          return {
            response:
              'The request was either filtered due to the context triggering our content management policy or because the context was longer than the allowed context length.',
            status: 400,
            token: {
              prompt_tokens: 0,
              completion_tokens: 0,
              total_cost: 0,
            },
          };
        }

        const isRateLimit = httpStatus === 429;

        const retryAfterHeader =
          error?.headers?.['retry-after'] ||
          error?.response?.headers?.['retry-after'];
        const waitTime = parseInt(retryAfterHeader ?? '60', 10);

        this.logger.log(
          `Error: ${error.message} - Status: ${httpStatus} - Retry-After: ${waitTime}s`
        );

        // If it's a rate limit error with a valid waitTime and we haven't exceeded maxAttempts:
        //  1) If the waitTime is <= 240, we wait and then retry
        //  2) If the waitTime > 240 or we've already retried once, throw the error
        if (isRateLimit && attempts < maxAttempts) {
          if (waitTime > 240) {
            throw new Error(
              `Rate limit hit, but wait time (${waitTime}s) exceeds 240 seconds. Aborting.`
            );
          }
          // Wait for the indicated or default waitTime
          const safeWait = Math.max(waitTime, 30) + 5; // e.g. wait at least 30 second + 5 seconds buffer
          await new Promise((resolve) => setTimeout(resolve, safeWait * 1000));
        } else {
          // For any other error, or if we've already retried once, rethrow it
          throw error;
        }
      }
    }

    // If we got here, we've exhausted retries
    throw new Error('Max retry attempts reached. Aborting.');
  }

  calculateCost({
    model,
    inputTokens,
    outputTokens,
  }: {
    model: LLM_MODELS;
    inputTokens: number;
    outputTokens: number;
  }) {
    const inputCostPerMillion =
      model === LLM_MODELS['gpt-4o']
        ? 2.5
        : model === LLM_MODELS.o1 || model === LLM_MODELS.o3
          ? 10
          : 0.15;
    const outputCostPerMillion =
      model === LLM_MODELS['gpt-4o'] ? 15 : model === LLM_MODELS.o1 ? 60 : 0.6;

    // Calculate costs based on input and output tokens
    const inputCost = (inputTokens / 1_000_000) * inputCostPerMillion;
    const outputCost = (outputTokens / 1_000_000) * outputCostPerMillion;

    // Total cost
    const totalCost = inputCost + outputCost;
    console.log('Total cost:', totalCost);
    return totalCost;
  }

  async *createCompletionStream(
    model: LLM_MODELS,
    messages: ChatCompletionMessageParam[]
  ): AsyncIterableIterator<string> {
    const params: OpenAI.Chat.ChatCompletionCreateParams = {
      model,
      messages: messages as OpenAI.Chat.ChatCompletionMessageParam[],
      stream: true,
      seed,
      temperature: default_temperature,
      top_p: 0,
    };

    const openai = this.openAiClient;
    const stream: Stream<OpenAI.Chat.ChatCompletionChunk> =
      await openai.chat.completions.create(params);

    for await (const part of stream) {
      yield part.choices[0].delta.content ?? '';
    }

    return;
  }

  async createCompletionWithFunctions(
    model: LLM_MODELS,
    previousMessages: ChatCompletionMessageParam[],
    customTools: CustomGptTool<any, any>[],
    temperature = default_temperature
  ): Promise<string> {
    const messages = [
      ...previousMessages,
    ] as OpenAI.Chat.ChatCompletionMessageParam[];

    const tools = customTools.map((tool) => tool.toolDefinition);

    const params: OpenAI.Chat.ChatCompletionCreateParams = {
      model,
      messages,
      tools,
      temperature,
    };

    const openai = await this.openAiClient;
    const chat: OpenAI.Chat.ChatCompletion =
      await openai.chat.completions.create(params);

    const result = chat.choices[0];
    messages.push(result.message);

    const wantsToUseTools = result.finish_reason == 'tool_calls';
    if (wantsToUseTools) {
      const newMessages = await this.runCustomTools(
        result.message.tool_calls,
        customTools,
        previousMessages
      );
      messages.push(...newMessages);

      const response = await openai.chat.completions.create({
        model,
        messages,
      });

      const nextResult = response.choices[0];
      messages.push(nextResult.message);
    }

    const lastMessage = messages[messages.length - 1]?.content as string;

    return lastMessage;
  }

  // TODO: this function must be refactored! :)
  async *createCompletionWithFunctionsStream(
    model: LLM_MODELS,
    previousMessages: Array<{
      role: 'user' | 'assistant' | 'system' | 'function';
      content: string;
    }>,
    customTools: CustomGptTool<any, any>[],
    temperature = default_temperature
  ): AsyncIterableIterator<string> {
    const messages = [
      ...previousMessages,
    ] as OpenAI.Chat.ChatCompletionMessageParam[];

    const tools = customTools.map((tool) => tool.toolDefinition);

    const params: OpenAI.Chat.ChatCompletionCreateParams = {
      model,
      messages,
      temperature,
      tools,
      stream: true,
    };

    const openai = this.openAiClient;
    const stream: Stream<OpenAI.Chat.ChatCompletionChunk> =
      await openai.chat.completions.create(params);

    let tool_calls: ChatCompletionMessageToolCall[] | undefined = undefined;

    for await (const part of stream) {
      if (part.choices[0].delta.tool_calls !== undefined) {
        // first message of the stream contains the tool calls
        if (tool_calls === undefined) {
          tool_calls = [
            ...part.choices[0].delta.tool_calls,
          ] as ChatCompletionMessageToolCall[];
        } else {
          // consecutive messages contain the tool call args
          part.choices[0].delta.tool_calls.map((call) => {
            if (tool_calls[call.index] !== undefined) {
              tool_calls[call.index].function.arguments +=
                call.function.arguments;
            }
          });
        }
      } else if (tool_calls === undefined) {
        yield part.choices[0].delta.content ?? '';
      }
    }

    if (tool_calls === undefined) {
      return;
    }

    const toolsToBeCalled = tool_calls.map((toolCall) =>
      customTools.find(
        (t) => t.toolDefinition.function.name === toolCall.function.name
      )
    );

    // replacing tools cancel the tool call and answer directly
    if (toolsToBeCalled.some((t) => t.type === 'replacing-gpt-tool')) {
      const replacingTool = toolsToBeCalled.find(
        (t) => t.type === 'replacing-gpt-tool'
      );
      const stringArgs = tool_calls.find(
        (call) =>
          call.function.name === replacingTool.toolDefinition.function.name
      )?.function.arguments;
      const args = JSON.parse(stringArgs);

      const stream = replacingTool.execute(args, messages);

      for await (const part of stream) {
        yield part;
      }
    } else {
      // other tool calls put their answer to the message stack and gpt answers at the end
      messages.push({ role: 'assistant', content: null, tool_calls });

      const newMessages = await this.runCustomTools(
        tool_calls,
        customTools,
        messages
      );
      messages.push(...newMessages);

      const finalStream: Stream<OpenAI.Chat.ChatCompletionChunk> =
        await openai.chat.completions.create(params);

      for await (const part of finalStream) {
        yield part.choices[0].delta.content ?? '';
      }
    }
  }

  private async runCustomTools(
    tool_calls: ChatCompletionMessageToolCall[],
    customTools: CustomGptTool<any, any>[],
    previousMessages: ChatCompletionMessageParam[]
  ): Promise<ChatCompletionMessageParam[]> {
    const results = tool_calls.map(async (toolCall) => {
      const tool_call_id = toolCall.id;
      const name = toolCall.function.name;
      const args = JSON.parse(toolCall.function.arguments);

      const tool = customTools.find(
        (t) => t.toolDefinition.function.name === name
      );
      if (tool === undefined) {
        console.error(
          `Tool with name: "${name}" not found - available tools: ${customTools.map((t) => t.toolDefinition.function.name).join(', ')}`
        );
        return {
          role: 'tool',
          tool_call_id,
          content: 'Tool not found',
        };
      }

      let content = '';
      try {
        const getContent = tool.execute(args, previousMessages);
        if (tool.type === 'async-gpt-tool') {
          content = await getContent;
        }
      } catch (e) {
        content = 'Tool execution failed';
      }

      return {
        role: 'tool',
        tool_call_id,
        content,
      };
    });

    const messages = await Promise.all(results);

    return messages as ChatCompletionMessageParam[];
  }

  async createEmbedding(text: string): Promise<number[]> {
    // Use Azure OpenAI client with text-embedding-3-large model
    try {
      const openai = new AzureOpenAI({
        apiKey: process.env.AZURE_OPENAI_API_KEY,
        apiVersion: '2024-12-01-preview',
        endpoint: process.env.AZURE_RESOURCE,
        deployment: 'text-embedding-3-large',
      });
      const embeddingResp = await openai.embeddings.create({
        model: 'text-embedding-3-large',
        input: text,
      });
      return embeddingResp.data[0].embedding;
    } catch (error) {
      this.logger.error('Error creating embedding:', error);
      throw new Error('Failed to create embedding');
    }
  }

  async createContentEmbeddingPairs(
    chunks: string[]
  ): Promise<{ content: string; embedding: string }[]> {
    return await Promise.all(
      chunks.map(async (content) => {
        const embedding = await this.createEmbedding(content);
        const formattedEmbedding = `[${embedding.join(',')}]`;
        return { embedding: formattedEmbedding, content };
      })
    );
  }
}
