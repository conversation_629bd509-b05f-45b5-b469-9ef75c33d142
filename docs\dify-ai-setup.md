# **Setting up Dify on Ubuntu 24.04 LTS**

## **Introduction**

Dify is an open-source AI application development platform that allows you to build, deploy, and manage AI-powered chatbots and automation tools with ease. Whether you're a developer looking for a self-hosted AI solution or a business aiming for greater control over your AI applications, Dify provides a flexible and scalable environment. In this guide, we'll walk you through setting up Dify on Ubuntu 24.04 LTS, ensuring a secure and efficient deployment with Docker, Nginx, and SSL encryption. By the end of this tutorial, you'll have a fully functional Dify server that can be accessed from anywhere, ready to power your AI-driven applications.

Original guide by <PERSON> at [https://jeremypedersen.com/posts/2024-07-16-dify-server-setup/](https://jeremypedersen.com/posts/2024-07-16-dify-server-setup/).

---

## **1. Initial Setup**

Start by updating and cleaning up your system:

```bash
sudo apt-get update
sudo apt-get upgrade
sudo apt-get autoremove
sudo apt-get autoclean
```

Then, reboot:

```bash
sudo reboot
```

---

## **2. Install Docker**

Dify requires `docker` and its built-in `docker compose` command. Follow the [official Docker installation guide](https://docs.docker.com/engine/install/ubuntu/) or use the steps below.

### **Remove any old Docker versions**

```bash
for pkg in docker.io docker-doc docker-compose docker-compose-v2 podman-docker containerd runc; do sudo apt-get remove $pkg; done
```

### **Set up the official Docker repository**

```bash
sudo apt-get update
sudo apt-get install ca-certificates curl
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc

echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
  $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

sudo apt-get update
```

### **Install Docker**

```bash
sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
```

### **Verify Docker Installation**

```bash
sudo docker run hello-world
```

If you don't want to use `sudo` for Docker commands, add your user to the Docker group:

```bash
sudo groupadd docker
sudo usermod -aG docker $USER
newgrp docker
```

Then, test running a container without `sudo`:

```bash
docker run hello-world
```

---

## **3. Install Nginx and Set Up SSL**

To access Dify securely via a web browser, set up an Nginx proxy with an SSL certificate from [Let’s Encrypt](https://letsencrypt.org/).

### **Install Nginx**

```bash
sudo apt-get install nginx
```

Visit your server’s public IP in a browser. You should see the default Nginx welcome page.

### **Install Certbot for SSL Certificates**

```bash
sudo apt install certbot python3-certbot-nginx
```

Before proceeding, ensure your domain (e.g., `www.mysite.com`) is pointing to your server's public IP. Then, request an SSL certificate:

```bash
sudo certbot --nginx
```

After completing the prompts, visiting `https://www.mysite.com` should now be SSL-secured.

---

## **4. Install Dify and Reconfigure Nginx**

### **Clone the Dify Repository**

```bash
git clone https://github.com/langgenius/dify.git
```

Navigate to the Docker directory and configure Dify:

```bash
cd dify/docker
cp .env.example .env
```

### **Modify the Dify Nginx Ports**

Since Dify uses its own Nginx server, change its default ports to avoid conflicts. Open the `.env` file and update these lines:

```bash
EXPOSE_NGINX_PORT=8080
EXPOSE_NGINX_SSL_PORT=8443
```

### **Configure Nginx to Forward Traffic**

Backup the default Nginx configuration:

```bash
cd /etc/nginx/sites-available
sudo cp default default.bkp
```

Edit the Nginx configuration and replace the `location / {}` block with:

```nginx
location / {
    proxy_pass http://localhost:8080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_redirect off;
    proxy_buffering off;
    proxy_request_buffering off;
    client_max_body_size 0;
}
```

### **Validate and Restart Nginx**

Test the configuration:

```bash
sudo nginx -t
```

If successful, restart Nginx:

```bash
sudo systemctl restart nginx
```

Since Dify isn’t running yet, visiting your site will result in a "bad gateway" error, which will be resolved once Dify starts.

---

## **5. Get Dify Running**

Navigate to the Dify Docker directory:

```bash
cd dify/docker
docker compose up -d
```

Once the containers are running, visit your site, and you should see the Dify setup page.

### **Complete Setup**

- Choose an admin username and password.
- Log into Dify’s **Studio** to start creating AI chatbots.

---

## **6. Updating Dify**

To update Dify in the future:

```bash
cd dify/docker
docker compose down
git pull origin main
docker compose pull
docker compose up -d
```

---

**Congratulations!** 🎉 You now have a fully functional Dify setup running on Ubuntu 24.04 LTS, secured with Nginx and SSL.
