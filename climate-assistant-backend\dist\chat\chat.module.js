"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatModule = void 0;
const common_1 = require("@nestjs/common");
const chat_service_1 = require("./chat.service");
const chat_controller_1 = require("./chat.controller");
const typeorm_1 = require("@nestjs/typeorm");
const chat_history_entity_1 = require("./entities/chat-history.entity");
const chat_message_entity_1 = require("./entities/chat.message.entity");
const users_module_1 = require("../users/users.module");
const chat_gpt_service_1 = require("../llm/chat-gpt.service");
const knowledge_base_module_1 = require("../knowledge-base/knowledge-base.module");
const perplexity_service_1 = require("../util/perplexity.service");
const initiative_suggestion_service_1 = require("./initiative-suggestion.service");
const multi_question_search_engine_service_1 = require("../util/multi-question-search-engine.service");
const initiative_detail_service_1 = require("./initiative-detail.service");
const csrd_reporting_service_1 = require("./csrd-reporting.service");
const search_engine_tool_1 = require("../util/search-engine.tool");
const supabase_module_1 = require("../auth/supabase/supabase.module");
let ChatModule = class ChatModule {
};
exports.ChatModule = ChatModule;
exports.ChatModule = ChatModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([chat_history_entity_1.ChatHistory, chat_message_entity_1.ChatMessage]),
            supabase_module_1.SupabaseAuthModule,
            users_module_1.UsersModule,
            knowledge_base_module_1.KnowledgeBaseModule,
        ],
        providers: [
            chat_service_1.ChatService,
            chat_gpt_service_1.ChatGptService,
            perplexity_service_1.PerplexityService,
            initiative_suggestion_service_1.InitiativeSuggestionService,
            multi_question_search_engine_service_1.MultiQuestionSearchEngine,
            initiative_detail_service_1.InitiativeDetailService,
            csrd_reporting_service_1.CsrdReportingService,
            search_engine_tool_1.SearchEngineTool,
        ],
        controllers: [chat_controller_1.ChatController],
    })
], ChatModule);
//# sourceMappingURL=chat.module.js.map