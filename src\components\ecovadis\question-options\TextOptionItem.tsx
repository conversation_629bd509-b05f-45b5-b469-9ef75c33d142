import { useState } from "react";
import { AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { QuestionOption } from "@/types/ecovadis";
import { toast } from "sonner";

interface TextOptionItemProps {
  option: QuestionOption;
  expanded: boolean;
  onToggleExpand: (optionId: string) => void;
  onUpdateResponse: (optionId: string, response: string) => Promise<void>;
  isLoading?: boolean;
}

export const TextOptionItem = ({
  option,
  expanded,
  onToggleExpand,
  onUpdateResponse,
  isLoading = false
}: TextOptionItemProps) => {
  const [response, setResponse] = useState(option.response || "");
  const [isSaving, setIsSaving] = useState(false);

  const handleSaveResponse = async () => {
    if (!response.trim()) {
      toast.error("Please enter a response");
      return;
    }

    setIsSaving(true);
    try {
      await onUpdateResponse(option.id, response);
      toast.success("Response saved successfully");
    } catch (error) {
      console.error("Error saving response:", error);
      toast.error("Failed to save response");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <AccordionItem 
      value={option.id} 
      className="rounded-md p-4 space-y-2 bg-gray-50/50 mb-4 border-none"
    >
      <div className="flex items-start gap-3">
        <div className="space-y-2 flex-1">
          <div className="flex items-center justify-between">
            <AccordionTrigger 
              onClick={(e) => {
                e.preventDefault();
                onToggleExpand(option.id);
              }}
              className="p-0 hover:no-underline"
            >
              <label 
                className="font-medium text-glacier-darkBlue cursor-pointer text-left"
              >
                {option.text}
              </label>
            </AccordionTrigger>
          </div>
          
          <AccordionContent>
            {option.evidenceExamples && option.evidenceExamples !== "N/A" && (
              <div className="flex items-center justify-between mb-3">
                <div className="text-sm text-glacier-darkBlue">
                  Instructions: {option.evidenceExamples}
                </div>
              </div>
            )}
            
            <div className="space-y-3">
              <Textarea
                value={response}
                onChange={(e) => setResponse(e.target.value)}
                placeholder="Enter your response here..."
                className="min-h-[120px]"
                disabled={isLoading || isSaving}
              />
              
              <div className="flex justify-end">
                <Button
                  onClick={handleSaveResponse}
                  disabled={!response.trim() || isLoading || isSaving}
                  className="bg-glacier-darkBlue hover:bg-glacier-darkBlue/90"
                >
                  {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Save Response
                </Button>
              </div>
            </div>
          </AccordionContent>
        </div>
      </div>
    </AccordionItem>
  );
};