# ORM Documentation

This document provides detailed information about the Object-Relational Mapping (ORM) used in the backend of the Climate Assistant project. The ORM used in this project is TypeORM, which is a powerful and flexible ORM for TypeScript and JavaScript (ES7, ES6, ES5).

## Overview

TypeORM is used to interact with the PostgreSQL database in the Climate Assistant project. It allows developers to define and manage database entities and their relationships using TypeScript classes and decorators. TypeORM provides a high-level abstraction over SQL, making it easier to work with the database.

## Database Connection Configuration

The database connection is configured in the `TypeOrmModule` within the `AppModule`. The configuration options are specified in the `createDataSourceWithVectorSupport` function, which is defined in the `env-helper.ts` file. Here is an example of the database connection configuration:

```typescript
import { createDataSourceWithVectorSupport, getDBHost } from '../env-helper';

export default createDataSourceWithVectorSupport({
  type: 'postgres',
  host: getDBHost(),
  port: +process.env.BACKEND_DB_PORT,
  username: process.env.BACKEND_DB_USER,
  password: process.env.BACKEND_DB_PASSWORD,
  database: process.env.BACKEND_DB_NAME,
  synchronize: false,
  dropSchema: false,
  logging: false,
  logger: 'file',
  entities: ['src/**/*.entity{.ts,.js}'],
  migrations: ['src/database/migrations/**/*.ts'],
  migrationsTableName: 'migration_table',
});
```

## Entities and Decorators

Entities in TypeORM are defined as TypeScript classes, and decorators are used to specify the mapping between the class and the database table. Here is an example of an entity definition:

```typescript
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn } from 'typeorm';
import { User } from './user.entity';

@Entity()
export class Document {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column()
  path: string;

  @Column()
  documentType: string;

  @Column('simple-array')
  esrsCategory: string[];

  @Column()
  year: number;

  @Column({ nullable: true })
  month: number;

  @Column({ nullable: true })
  day: number;

  @Column()
  remarks: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'createdBy' })
  creator: User;
}
```

In this example, the `Document` class is mapped to a database table named `document`. The `@Entity()` decorator is used to mark the class as an entity, and the `@PrimaryGeneratedColumn()`, `@Column()`, and `@ManyToOne()` decorators are used to define the columns and relationships.

## Relationships

TypeORM supports various types of relationships between entities, including one-to-one, one-to-many, and many-to-many relationships. Here is an example of a one-to-many relationship:

```typescript
import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { Document } from './document.entity';

@Entity()
export class Workspace {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @OneToMany(() => Document, document => document.workspace)
  documents: Document[];
}
```

In this example, the `Workspace` entity has a one-to-many relationship with the `Document` entity. The `@OneToMany()` decorator is used to define the relationship, and the `documents` property is an array of `Document` entities.

## Migrations

TypeORM provides a powerful migration system that allows developers to manage database schema changes over time. Migrations are defined as TypeScript classes and can be run using the TypeORM CLI. Here is an example of a migration:

```typescript
import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1720764073594 implements MigrationInterface {
  name = 'SchemaUpdate1720764073594';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE "user"
                                 (
                                     "id"       uuid                   NOT NULL DEFAULT uuid_generate_v4(),
                                     "email"    character varying(100) NOT NULL,
                                     "password" character varying(100) NOT NULL,
                                     CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id")
                                 )`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "user"`);
  }
}
```

In this example, the `SchemaUpdate1720764073594` migration creates a new table named `user` with columns for `id`, `email`, and `password`. The `up` method defines the changes to be applied, and the `down` method defines how to revert the changes.

## Examples of TypeORM Usage

Here are some examples of how TypeORM is used in the Climate Assistant project:

### Creating a New Document

```typescript
import { Document } from './document.entity';
import { getRepository } from 'typeorm';

const documentRepository = getRepository(Document);

const newDocument = documentRepository.create({
  name: 'Example Document',
  path: '/path/to/document',
  documentType: 'Report',
  esrsCategory: ['Category1', 'Category2'],
  year: 2023,
  month: 5,
  day: 15,
  remarks: 'This is an example document.',
  createdBy: 'user-id',
});

await documentRepository.save(newDocument);
```

### Finding a Document by ID

```typescript
import { Document } from './document.entity';
import { getRepository } from 'typeorm';

const documentRepository = getRepository(Document);

const document = await documentRepository.findOne({ where: { id: 'document-id' } });

if (document) {
  console.log('Document found:', document);
} else {
  console.log('Document not found');
}
```

### Updating a Document

```typescript
import { Document } from './document.entity';
import { getRepository } from 'typeorm';

const documentRepository = getRepository(Document);

const document = await documentRepository.findOne({ where: { id: 'document-id' } });

if (document) {
  document.name = 'Updated Document Name';
  await documentRepository.save(document);
  console.log('Document updated:', document);
} else {
  console.log('Document not found');
}
```

### Deleting a Document

```typescript
import { Document } from './document.entity';
import { getRepository } from 'typeorm';

const documentRepository = getRepository(Document);

const document = await documentRepository.findOne({ where: { id: 'document-id' } });

if (document) {
  await documentRepository.remove(document);
  console.log('Document deleted');
} else {
  console.log('Document not found');
}
```

These examples demonstrate how to create, find, update, and delete entities using TypeORM in the Climate Assistant project.
