import { FunctionComponent } from 'react';
import { createSearchParams, useNavigate } from 'react-router-dom';

import { useHistories } from '@/api/chats/histories.state.ts';
import {
  MixpanelEvents,
  MixpanelService,
} from '@/services/mixpanel.service.ts';
import { createEmptyHistory } from '@/api/chats/chats.api.ts';
import { cn } from '@/lib/utils';
import { AI_ACTIONS } from '@/types';
import { climaFeatures } from '@/lib/config';
import IconRenderer, { IconName } from '@/components/ui/icons';
import { ChatLayout } from '@/components/ChatLayout';

export const NewChat: FunctionComponent = () => {
  const { refetchHistories } = useHistories();
  const navigate = useNavigate();

  const createChat = async (type: AI_ACTIONS) => {
    const history = await createEmptyHistory(type);

    let initialMessage: string | undefined;
    if (type === 'initiativeCreation') {
      // initialMessage =
      //   'Schlag mir 5 Maßnahmen vor die ich in meinem Unternehmen umsetzen könnte.';
      initialMessage = undefined; // nothing for now
    } else if (type === 'regulatoryHelp') {
      initialMessage = undefined;
    }

    navigate({
      pathname: `/chats/${history.id}`,
      search: initialMessage
        ? createSearchParams({
            initialMessage,
          }).toString()
        : undefined,
    });
    void refetchHistories();
    MixpanelService.track(MixpanelEvents.newChatStarted(type));
  };

  return (
    <ChatLayout>
      <div className={`flex flex-col items-center flex-1 justify-center pb-60`}>
        <div className={`text-3xl font-semibold text-center mb-16`}>
          Wie können wir dich unterstützen?
        </div>
        <div className={`grid grid-cols-2 gap-12`}>
          {climaFeatures.map((feature, index) => (
            <NewChatTile
              key={index}
              title={feature.title}
              description={feature.description}
              active={feature.active}
              onClick={() => createChat(feature.type)}
              icon={feature.icon}
              badge={feature.badge}
            />
          ))}
        </div>
      </div>
    </ChatLayout>
  );
};

const NewChatTile: FunctionComponent<{
  title: string;
  description: string;
  icon: IconName;
  active: boolean;
  onClick: () => void;
  badge?: string;
}> = ({ title, description, active, onClick, icon, badge }) => {
  return (
    <div
      onClick={() => (active ? onClick() : {})}
      className={cn(
        `flex flex-row items-center gap-6 relative drop-shadow rounded-xl p-4 max-w-[400px]`,
        {
          'cursor-pointer bg-white hover:bg-gray-50 transition-all': active,
        }
      )}
    >
      {!active && (
        <div className="absolute inset-0 bg-[#E6E6E6] opacity-[0.44] rounded-lg"></div>
      )}
      <div>
        <IconRenderer
          iconName={icon}
          className={`text-glacier-greenmid h-7 w-7`}
        />
      </div>
      {badge && (
        <span
          className={`text-slate-900 border border-slate-900 text-xs font-medium me-2 px-2.5 py-0.5 rounded-full absolute top-2 right-1 scale-90 font-pantea-text tracking-tight`}
        >
          {badge}
        </span>
      )}
      <div>
        <div className={`font-regular`}>{title}</div>
        <div className={` text-gray-500`}>{description}</div>
      </div>
    </div>
  );
};
