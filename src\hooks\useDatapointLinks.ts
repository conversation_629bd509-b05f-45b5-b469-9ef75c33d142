import { useEffect, useState } from 'react';

import { fetchProjectEsrsDatapoints } from '@/api/project-settings/project-settings.api';
import { updateDatapointLinkToDocumentChunk } from '@/api/documents/documents.api';
import { toast } from '@/components/ui/use-toast';
import { LinkedESRSDatapoint } from '@/components/documents/DatapointLinksTable';
import { DatapointRequestLinkage } from '@/types/document';

export function useDatapointLinks({
  documentChunkId,
  datapointRequests,
  mutate,
}: {
  documentChunkId: string;
  datapointRequests: DatapointRequestLinkage[];
  mutate: () => void;
}) {
  const [esrs, setEsrs] = useState('all');
  const [filter, setFilter] = useState('all');
  const [loading, setLoading] = useState(false);
  const [esrsDatapoints, setEsrsDatapoints] = useState<LinkedESRSDatapoint[]>(
    []
  );
  const [esrsDatapointsFiltered, setEsrsDatapointsFiltered] = useState<
    LinkedESRSDatapoint[]
  >([]);
  const [updateSet, setUpdateSet] = useState<
    {
      datapointRequestId: string;
      linked: boolean;
    }[]
  >([]);

  // function handleUpdateSet(id: LinkedESRSDatapoint['id'], linked: boolean) {
  //   const datapointRequestId = esrsDatapoints.find(
  //     (datapoint) => datapoint.id === id,
  //   )?.datapointRequestId;

  //   if (!datapointRequestId) return;

  //   setUpdateSet((prev) => {
  //     const index = prev.findIndex(
  //       (update) => update.datapointRequestId === datapointRequestId,
  //     );

  //     if (index === -1) {
  //       return [...prev, { datapointRequestId, linked }];
  //     }

  //     prev[index].linked = linked;
  //     return prev;
  //   });

  //   setEsrsDatapoints((prev) =>
  //     prev.map((datapoint) => {
  //       if (datapoint.id === id) {
  //         return { ...datapoint, linked };
  //       }
  //       return datapoint;
  //     }),
  //   );
  // }

  async function handleUpdateSubmit(
    id: LinkedESRSDatapoint['id'],
    linked: boolean
  ) {
    setLoading(true);
    try {
      const datapointRequestId = esrsDatapoints.find(
        (datapoint) => datapoint.id === id
      )?.datapointRequestId;

      if (!datapointRequestId) return;

      await updateDatapointLinkToDocumentChunk(documentChunkId, [
        { datapointRequestId, linked },
      ]);
      toast({
        title: 'Datapoints linked successfully',
      });

      setEsrsDatapoints((prev) =>
        prev.map((datapoint) => {
          if (datapoint.id === id) {
            return { ...datapoint, linked };
          }
          return datapoint;
        })
      );
    } catch (error) {
      toast({
        title: 'An error occurred',
        variant: 'destructive',
      });
      mutate();
    }
    setLoading(false);
  }

  async function loadUserFileUploads() {
    setLoading(true);
    setUpdateSet([]);
    const fetchDatapoints = await fetchProjectEsrsDatapoints(esrs);
    const linkedDatapoints = fetchDatapoints.map((datapoint) => {
      return {
        ...datapoint,
        linked: datapointRequests.some(
          (request) =>
            request.esrsDatapointId === datapoint.id && request.linked
        ),
      };
    });

    setEsrsDatapoints(linkedDatapoints);
    setLoading(false);
  }

  useEffect(() => {
    if (filter === 'all') {
      setEsrsDatapointsFiltered(esrsDatapoints);
    } else if (filter === 'Linked') {
      setEsrsDatapointsFiltered(
        esrsDatapoints.filter((datapoint) => datapoint.linked)
      );
    } else {
      setEsrsDatapointsFiltered(
        esrsDatapoints.filter((datapoint) => !datapoint.linked)
      );
    }
  }, [esrsDatapoints, filter]);

  useEffect(() => {
    loadUserFileUploads();
  }, [esrs]);

  return {
    esrs,
    setEsrs,
    filter,
    setFilter,
    loading,
    esrsDatapoints,
    esrsDatapointsFiltered,
    updateSet,
    // handleUpdateSet,
    handleUpdateSubmit,
  };
}
