import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useDataRequestContext } from '@/context/dataRequestContext';
import { toast } from './ui/use-toast';

interface ReviewDatapointsDialogProps {
  dataRequestId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onReview: () => Promise<void>;
  datapointCount: number;
  isReviewing: boolean;
  setIsReviewing: React.Dispatch<React.SetStateAction<boolean>>;
}

export function ReviewDatapointsDialog({
  dataRequestId,
  open,
  onOpenChange,
  onReview,
  datapointCount,
  isReviewing,
  setIsReviewing,
}: ReviewDatapointsDialogProps) {
  const { setupEventSource, closeEventSource } = useDataRequestContext();

  async function handleReviewXDatapoint() {
    onOpenChange(false);
    try {
      setIsReviewing(true);
      await setupEventSource(dataRequestId);
      await onReview();
      toast({
        title: 'Datapoint queued for review',
        description: `${datapointCount} Datapoints queued for Review`,
        variant: 'default',
      });
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to review datapoints',
        variant: 'destructive',
      });

      closeEventSource();
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Review {datapointCount} Datapoints</DialogTitle>
          <DialogDescription>
            Review multiple datapoints automatically. Caution: This can not be
            stopped, once started, so ensure, that all relevant documents are
            already uploaded. While a datapoint is waiting for generation, you
            cannot trigger another generation or review.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleReviewXDatapoint} disabled={isReviewing}>
            Review {datapointCount} Datapoints
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
