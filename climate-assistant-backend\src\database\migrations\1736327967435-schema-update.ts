import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1736327967435 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        UPDATE "esrs_topic" SET "description" = 'Climate Change (ESRS E1) refers to the undertaking''s impacts on the climate system and its strategies to address climate-related risks and opportunities. This includes impacts, risks, and opportunities related to both the undertaking''s effects on climate through greenhouse gas emissions and the effects of climate change on the undertaking''s business model and operations. The scope encompasses greenhouse gas emissions across all categories, including direct emissions (Scope 1) from owned or controlled sources, indirect emissions from purchased energy (Scope 2), and value chain emissions (Scope 3) from upstream and downstream activities. Particular attention is given to seven specific greenhouse gases: carbon dioxide (CO2), methane (CH4), nitrous oxide (N2O), hydrofluorocarbons (HFCs), perfluorocarbons (PFCs), sulphur hexafluoride (SF6), and nitrogen trifluoride (NF3). The definition extends to climate change mitigation through actions and strategies to reduce greenhouse gas emissions, including transition plans toward a low-carbon economy, energy efficiency and renewable energy initiatives, and carbon offsetting and removal activities. It also covers climate change adaptation through measures to adapt to actual and expected climate change impacts, including resilience strategies for operations and value chain, and the assessment and management of physical climate risks. The scope encompasses all climate-related matters that are or can be materially impacted by the undertaking''s operations, products, services, and business relationships throughout its value chain, including both current impacts and future commitments for climate action.' WHERE id = 1;
        UPDATE "esrs_topic" SET "description" = 'Pollution (ESRS E2) refers to the undertaking''s impacts on pollution of air, water, and soil, and its approaches to prevent, control, and remediate pollution. This includes the generation, management, and release of pollutants through the undertaking''s activities, encompassing air pollutants and their specific loads, emissions to water and respective specific loads, pollution to soil and respective specific loads, and substances of concern and substances of very high concern. The scope includes the undertaking''s impacts on living organisms and food resources through various forms of pollution, including consideration of microplastics that have been generated or used during production processes or that are procured. The definition covers pollution prevention and control measures, including those subject to specific regulations such as the Industrial Emissions Directive and Best Available Techniques Reference Documents. Consideration is given to local contexts such as air quality indices, degree of urbanisation, and areas at water risk. The scope encompasses both direct pollution from the undertaking''s own operations and indirect pollution through its value chain relationships, requiring assessment of pollution-related impacts, risks, and opportunities. This includes setting and tracking targets related to pollution prevention and control, with consideration of ecological thresholds such as biosphere integrity, stratospheric ozone depletion, atmospheric aerosol loading, soil depletion, and ocean acidification.' WHERE id = 2;
        UPDATE "esrs_topic" SET "description" = 'Water and Marine Resources (ESRS E3) refers to the undertaking''s interactions with and impacts on water and marine ecosystems. This includes water consumption and management, particularly in areas identified as being at water risk, encompassing water withdrawals, water discharges, and water recycling and storage activities. The definition extends to the responsible management of marine resources, including the nature and quantity of marine resources-related commodities such as gravels, deep-sea minerals, and seafood used by the undertaking. The scope covers impacts on water quality and availability, including pollution of water bodies and marine resources through emissions, microplastics, and other pollutants. It addresses both freshwater and marine environments, considering the undertaking''s role in water management, consumption, and impact across its operational footprint. The definition includes consideration of water-related risks and opportunities, including impacts on local water availability, marine ecosystem health, and sustainable use of aquatic resources. It encompasses both direct impacts from the undertaking''s operations and indirect impacts through its value chain relationships, requiring assessment of water and marine resource-related impacts, risks, and opportunities throughout the water cycle and marine resource utilization.' WHERE id = 3;
        UPDATE "esrs_topic" SET "description" = 'Biodiversity and Ecosystems (ESRS E4) refers to the undertaking''s interactions with and impacts on living organisms, their genetic diversity, and the ecosystems they form part of. This includes the assessment and management of direct impact drivers of biodiversity loss, such as land use change and habitat modification, direct exploitation of organisms and resources, pollution, introduction or spread of invasive alien species, and contribution to climate change. The scope encompasses impacts on species population size and global extinction risk, as well as the undertaking''s dependencies on ecosystem services including provisioning services, regulating services, and cultural services. The definition covers both terrestrial and aquatic ecosystems, considering impacts on habitat integrity, species diversity, and ecosystem functioning. It addresses the undertaking''s role in biodiversity conservation and restoration, including measures to protect threatened species and maintain ecosystem health. The scope includes both direct impacts from the undertaking''s operations and indirect impacts through its value chain relationships, requiring assessment of biodiversity-related impacts, risks, and opportunities across all operational areas and throughout the value chain. Particular attention is given to impacts on protected areas, priority ecosystems, and critical habitats.' WHERE id = 4;
        UPDATE "esrs_topic" SET "description" = 'Resource Use and Circular Economy (ESRS E5) refers to the undertaking''s approach to resource consumption, waste management, and transition toward circular business models. This includes resource inflows encompassing the consumption of raw materials, particularly non-renewable resources, use of intermediate products and processed materials in production processes, and water consumption, excluding energy resources. The definition covers resource outflows related to products and services, including finished products and their material composition, by-products and secondary materials generated during production processes, packaging materials, and materials released through service provision. The scope addresses the undertaking''s transition from linear to circular resource use patterns, including practices that prevent waste generation and promote resource efficiency, reuse, and recycling. It encompasses the undertaking''s strategies for sustainable resource management, including design for circularity, product lifecycle impacts, and waste reduction initiatives. The definition applies to all material flows within the undertaking''s direct operations and throughout its value chain, requiring assessment of resource-related impacts, risks, and opportunities in the context of circular economy principles.' WHERE id = 5;
        UPDATE "esrs_topic" SET "description" = 'Own Workforce (ESRS S1) refers to workers who are in an employment relationship with the undertaking and encompasses both employees and non-employees who are either individual contractors supplying labour to the undertaking or people provided by undertakings primarily engaged in employment activities (NACE Code N78). This includes working conditions and worker rights such as working time arrangements, adequate wages, and equal treatment and opportunities for all workers. The definition covers health and safety matters, including measures to ensure workplace safety and prevent occupational accidents and diseases. It encompasses privacy rights, access to adequate housing, and freedom of association including the existence of work councils. The scope includes training and skills development, diversity initiatives, and protection against violence and harassment in the workplace. The definition extends to all social matters affecting the undertaking''s own workforce, requiring assessment of workforce-related impacts, risks, and opportunities within the undertaking''s direct employment relationships. Particular attention is given to vulnerable groups within the workforce and the protection of fundamental worker rights.' WHERE id = 6;
        UPDATE "esrs_topic" SET "description" = 'Workers in the Value Chain (ESRS S2) refers to individuals performing work in the undertaking''s upstream and downstream value chain, regardless of the existence or nature of any contractual relationship with the undertaking, excluding those covered under ''Own Workforce''. This includes working conditions such as working time arrangements and adequate wages throughout the value chain. The definition covers equal treatment and opportunities, including measures against violence and harassment in the workplace for value chain workers. It encompasses other work-related rights including access to adequate housing, water and sanitation facilities, and privacy protections for workers in supplier operations and other business relationships. The scope addresses the undertaking''s responsibility to assess and address impacts on workers'' rights and working conditions throughout its value chain, including both direct suppliers and broader business relationships. The definition applies to all workers who may be affected by the undertaking''s operations, products, services, and business relationships, requiring assessment of worker-related impacts, risks, and opportunities across the entire value chain. Particular attention is given to vulnerable workers and high-risk areas within the value chain.' WHERE id = 7;
        UPDATE "esrs_topic" SET "description" = 'Affected Communities (ESRS S3) refers to groups of people living in areas impacted by the undertaking''s operations and activities. This includes impacts on communities'' economic, social, and cultural rights, encompassing access to adequate housing, food, water and sanitation, as well as land-related and security-related impacts. The definition covers communities'' civil and political rights including freedom of expression, freedom of assembly, and impacts on human rights defenders who advocate for community interests. It addresses the specific rights and needs of indigenous peoples, including their rights to self-determination, cultural rights, and protection of traditional lands and resources. The scope encompasses both direct impacts from the undertaking''s operations and indirect effects through its business relationships, requiring assessment of community-related impacts, risks, and opportunities. The definition applies to all communities that may be affected by the undertaking''s activities, with particular attention to vulnerable groups and communities facing heightened risks of adverse impacts. It includes consideration of both immediate and long-term effects on community well-being, livelihoods, and cultural heritage.' WHERE id = 8;
        UPDATE "esrs_topic" SET "description" = 'Consumers and End-users (ESRS S4) refers to individuals who use or consume the undertaking''s products or services. This includes information-related impacts encompassing privacy protections, freedom of expression, and access to quality information about products and services. The definition covers personal safety considerations including security of person and protection of children when using or consuming the undertaking''s products or services. It addresses social inclusion aspects including non-discrimination in access to products and services, and the implementation of responsible marketing practices. The scope encompasses all consumer and end-user related matters that are or can be materially impacted by the undertaking''s operations, products, services, and business relationships, requiring assessment of consumer-related impacts, risks, and opportunities. The definition applies to both direct impacts through product and service delivery and indirect effects through business relationships, with particular attention to vulnerable consumers and end-users who may require enhanced protection or consideration in product design and service delivery.' WHERE id = 9;
        UPDATE "esrs_topic" SET "description" = 'Business Conduct (ESRS G1) refers to the undertaking''s strategy, approach, processes, procedures, and performance in respect of business ethics and conduct matters. This includes business ethics and corporate culture, encompassing anti-corruption and anti-bribery measures, protection of whistleblowers, and animal welfare considerations. The definition covers the management of relationships with suppliers, including payment practices with particular attention to preventing late payments to small and medium-sized undertakings, and the consideration of social and environmental criteria in supplier selection. It addresses the undertaking''s political engagement and lobbying activities, including activities and commitments related to exerting its political influence. The scope encompasses governance aspects such as corporate culture development, business conduct policies, and mechanisms for identifying and reporting concerns about unlawful behavior. The definition includes the prevention and detection of corruption and bribery, including training programs and investigation procedures, as well as confirmed incidents requiring remediation. Particular attention is given to the management of relationships with suppliers and the establishment of fair payment practices throughout the value chain.' WHERE id = 10;
        UPDATE "esrs_topic" SET "description" = 'General Disclosures refers to the foundational information about the undertaking''s sustainability-related practices, management approach, and governance structure that provides context for understanding its specific sustainability reporting. This includes governance aspects covering the role of administrative, management and supervisory bodies in sustainability matters, including their expertise and oversight responsibilities. The definition encompasses the undertaking''s overall strategy for addressing sustainability impacts, risks, and opportunities, including its assessment processes and integration into business strategy. It covers the undertaking''s general approach to impact, risk and opportunity management, including processes to identify, assess, and prioritize material sustainability matters. The scope includes cross-cutting elements that apply across different sustainability topics, such as policies, targets, and action plans. The definition applies to disclosures that provide overarching context for understanding the undertaking''s sustainability performance and approach, requiring reporting alongside specific topical disclosures in areas such as environment, social matters, and governance.' WHERE id = 11;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        UPDATE "esrs_topic" SET "description" = '' WHERE id = 1;
        UPDATE "esrs_topic" SET "description" = '' WHERE id = 2;
        UPDATE "esrs_topic" SET "description" = '' WHERE id = 3;
        UPDATE "esrs_topic" SET "description" = '' WHERE id = 4;
        UPDATE "esrs_topic" SET "description" = '' WHERE id = 5;
        UPDATE "esrs_topic" SET "description" = '' WHERE id = 6;
        UPDATE "esrs_topic" SET "description" = '' WHERE id = 7;
        UPDATE "esrs_topic" SET "description" = '' WHERE id = 8;
        UPDATE "esrs_topic" SET "description" = '' WHERE id = 9;
        UPDATE "esrs_topic" SET "description" = '' WHERE id = 10;
        UPDATE "esrs_topic" SET "description" = '' WHERE id = 11;
    `);
  }
}
