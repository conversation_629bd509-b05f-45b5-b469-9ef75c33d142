import { CanActivate, ExecutionContext } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Reflector } from '@nestjs/core';
import { UserWorkspace } from 'src/users/entities/user-workspace.entity';
import { Repository } from 'typeorm';
export declare class AuthGuard implements CanActivate {
    private jwtService;
    private reflector;
    private userWorkspaceRepository;
    constructor(jwtService: JwtService, reflector: Reflector, userWorkspaceRepository: Repository<UserWorkspace>);
    canActivate(context: ExecutionContext): Promise<boolean>;
    private extractFromCookies;
}
