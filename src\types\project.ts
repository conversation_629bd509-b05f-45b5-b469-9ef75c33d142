
import * as z from 'zod';

import { DocumentChunkData, Workspace } from './workspace';
import { IUser } from './user';

import {
  DatapointRequestStatus,
  DataRequestStatus,
  ESRSDatapoint,
  ESRSDisclosureRequirement,
  Language,
} from '.';

export interface Project {
  id: string;
  workspaceId: string;
  name: string;
  primaryContentLanguage: Language;
  reportTextGenerationRules: string;
  reportingYear: string;
  createdBy: string;
  createdAt: Date;
  type: string; // Make type required
  
  // Additional properties for UI compatibility
  progress?: {
    percentage: number;
    complete: number;
    incomplete: number;
    gaps: number;
  };
  deadline?: Date;
  description?: string;
}

export enum CommentType {
  DatapointRequest = 'datapoint_request',
  DataRequest = 'data_request',
}

export enum CommentStatus {
  Pending = 'pending',
  Approved = 'approved',
  Rejected = 'rejected',
}

export enum generationStatus {
  approved = 'approved',
  rejected = 'rejected',
  pending = 'pending',
  minorChanges = 'minorChanges',
}

export interface Comment {
  id: string;
  userId: string;
  commentableId: string;
  commentableType: CommentType;
  comment: string;
  resolved: boolean;
  createdAt: Date;
}

export interface CommentData extends Comment {
  user: IUser;
}

export interface CommentGenerationData extends Comment {
  status: CommentStatus;
  evaluatorComment?: string;
  // evaluator: IUser;
}

export interface ProjectData extends Project {
  workspace: Workspace;
  creator: IUser;
  materialTopics: MaterialESRSTopic[];
  dataRequests: DataRequestData[];
}

export interface CreateProjectRequest {
  name: string;
  primaryContentLanguage: Language;
}

export const projectSettingUpdateSchema = z.object({
  name: z.string().min(3).max(100),
  reportTextGenerationRules: z.string().max(10000).optional(),
  primaryContentLanguage: z.enum(
    Object.keys(Language) as [string, ...string[]]
  ),
  reportingYear: z.string().max(20).min(2).optional(),
  type: z.string().optional(),
});

export type UpdateProjectRequest = z.infer<typeof projectSettingUpdateSchema>;

export interface MaterialESRSTopic {
  esrsTopicId: number;
  projectId: string;
  active: boolean;
  createdAt: Date;
}

export interface DatapointRequest {
  id: string;
  dataRequestId: string;
  esrsDatapointId: number;
  content: string;
  customUserRemark: string;
  status: DatapointRequestStatus;
  queueStatus: QUEUE_STATUS | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface DatapointDocumentChunkMap {
  datapointRequestId: string;
  documentChunkId: string;
  createdAt: Date;
  active: boolean;
}

export interface DatapointDocumentChunkMapData
  extends DatapointDocumentChunkMap {
  documentChunk: DocumentChunkData;
}

//TODO move this to dataRequets specific types
export interface IDataGenerations {
  id: string;
  status: generationStatus;
  data: { content: string; metadata?: string };
  createdAt: string;
  evaluator?: IUser;
  evaluatedAt?: string;
}

export interface DatapointRequestData extends DatapointRequest {
  datapointDocumentChunkMap: DatapointDocumentChunkMapData[];
  documentChunkCount?: number;
  esrsDatapoint: ESRSDatapoint;
  comments: CommentData[];
  commentGenerations: CommentGenerationData[];
  datapointGenerations: IDataGenerations[];
}

export interface DataRequest {
  id: string;
  dataRequestTypeId: number;
  dataRequestType: string;
  status: DataRequestStatus;
  queueStatus: QUEUE_STATUS | null;
  content: string;
  customUserRemark?: string;
  projectId: string;
  dueDate: Date | null;
  responsiblePersonId: string | null;
  approvedBy: string | null;
  approvedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  responsiblePerson: IUser | null;
}

export interface DataRequestData extends DataRequest {
  disclosureRequirement: ESRSDisclosureRequirement;
  responsiblePerson: IUser | null;
  approver: IUser | null;
  datapointRequests: DatapointRequestData[];
  comments: CommentData[];
  commentGenerations: CommentGenerationData[];
}

export enum TopicLevel {
  Topic = 'topic',
  SubTopic = 'sub-topic',
  SubSubTopic = 'sub-sub-topic',
}

export enum QUEUE_STATUS {
  QueuedForGeneration = 'queued_for_generation',
  QueuedForReview = 'queued_for_review',
}

export interface EsrsTopic {
  id: number;
  name: string;
  level: TopicLevel;
  children?: EsrsTopic[];
}

export interface DocumentUpload {
  id: string;
  workspaceId: string;
  name: string;
  path: string;
  createdAt: string;
  status: string;
  documentType: string;
  esrsCategory?: string[];
  year: number;
  month: number;
  day?: null;
  remarks: string;
  createdBy: string;
  creator: Pick<IUser, 'id' | 'name'>;
}
