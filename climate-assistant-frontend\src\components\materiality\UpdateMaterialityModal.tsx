import { useState } from 'react';
import { LoaderCircle, SaveIcon } from 'lucide-react';

import { Button } from '@/components/ui/button.tsx';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';

export function UpdateMaterilaityButton({
  saveData,
  isSaving,
}: {
  saveData: () => Promise<void>;
  isSaving: boolean;
}) {
  const [isOpen, setIsOpen] = useState(false);
  async function handleSaveData() {
    await saveData();
    setIsOpen(false);
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="default"
          disabled={isSaving}
          className="rounded-full w-fit flex gap-2"
        >
          {isSaving ? (
            <LoaderCircle className="h-4 w-4 animate-spin" />
          ) : (
            <SaveIcon className="h-4 w-4" />
          )}
          Save and Apply Material Topics
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[625px]">
        <DialogHeader>
          <DialogTitle>Save and Apply Material Topics</DialogTitle>
          <DialogDescription className="pt-3">
            The reported status is automatically changed. By clicking on "Save
            Material Topics" I consent, that I will check through all of the
            Disclosure Requirements and datapoints myself and correct the
            status, if the automatic suggestion was not right.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            onClick={handleSaveData}
            disabled={isSaving}
            className="rounded-full w-fit flex gap-2"
          >
            {isSaving ? (
              <LoaderCircle className="h-4 w-4 animate-spin" />
            ) : (
              <SaveIcon className="h-4 w-4" />
            )}
            {isSaving ? 'Saving, please wait...' : 'Save Material Topics'}
          </Button>
          <Button onClick={() => setIsOpen(false)} variant="secondary">
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
