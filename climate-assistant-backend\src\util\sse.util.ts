import { Observable, Subscription } from 'rxjs';
import { Response } from 'express';
export function createSseStream<T>(
  response: Response,
  eventStream: Observable<T>,
  initialEvent: MessageEvent,
): Observable<MessageEvent> {
  response.setHeader('Content-Type', 'text/event-stream');
  response.setHeader('Cache-Control', 'no-cache');
  response.setHeader('Connection', 'keep-alive');

  return new Observable<MessageEvent>((subscriber) => {
    if (initialEvent) {
      subscriber.next(initialEvent);
    }

    const subscription: Subscription = eventStream.subscribe({
      next: (event) => {
        subscriber.next(event as MessageEvent);
      },
      error: (error) => {
        subscriber.error(error);
      },
      complete: () => {
        subscriber.complete();
      },
    });

    return () => subscription.unsubscribe();
  });
}
