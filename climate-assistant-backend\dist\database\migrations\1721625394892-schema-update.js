"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1721625394892 = void 0;
class SchemaUpdate1721625394892 {
    constructor() {
        this.name = 'SchemaUpdate1721625394892';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "user"
          ADD "companyName" character varying NOT NULL DEFAULT ''`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "companyName"`);
    }
}
exports.SchemaUpdate1721625394892 = SchemaUpdate1721625394892;
//# sourceMappingURL=1721625394892-schema-update.js.map