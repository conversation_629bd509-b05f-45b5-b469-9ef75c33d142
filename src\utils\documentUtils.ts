
import { DocumentType, LinkedQuestion } from '@/types/ecovadis';

export const formatDocumentType = (type: DocumentType): string => {
  switch (type) {
    case 'policy':
      return 'Policy';
    case 'certificate':
      return 'Certificate';
    case 'sustainability_report':
      return 'Sustainability Report';
    case 'supplier_code':
      return 'Supplier Code';
    case 'collective_agreement':
      return 'Collective Agreement';
    case 'reporting_document':
      return 'Reporting Document';
    default:
      return 'Other';
  }
};

// Mock data for linked questions
export const getLinkedQuestionMockData = (questionId: string) => {
  return {
    optionText: "Yes, with supporting documentation",
    comment: "See relevant pages for details on our sustainability policy implementation."
  };
};
