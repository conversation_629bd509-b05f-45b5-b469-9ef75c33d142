import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';

import LeftSection from '../login/LeftSection';

import ResetPasswordForm from './ResetPasswordForm';
import InvalidToken from './InvalidToken';

import { validatePasswordResetToken } from '@/api/authentication/authentication.api';
import { IUser } from '@/types/user';

const ResetPassword = () => {
  const [userEmail, setUserEmail] = useState<string>('');
  const [token, setToken] = useState<string | null>(null);
  const [invalidToken, setInvalidToken] = useState<boolean>(false);
  const [searchParams] = useSearchParams();

  useEffect(() => {
    const token = searchParams.get('token');
    if (token) {
      validatePasswordResetToken(token)
        .then((user: IUser) => {
          setUserEmail(user.email);
          setInvalidToken(false);
          setToken(token);
        })
        .catch(() => {
          setInvalidToken(true);
        });
    } else {
      setInvalidToken(true);
    }
  }, [searchParams]);

  return (
    <div className={`flex flex-row w-full h-screen`}>
      <LeftSection />
      <div className={`flex flex-1 flex-col justify-center items-center`}>
        {invalidToken ? (
          <InvalidToken />
        ) : token ? (
          <ResetPasswordForm userEmail={userEmail} token={token} />
        ) : (
          <div>Loading...</div>
        )}
      </div>
    </div>
  );
};

export default ResetPassword;
