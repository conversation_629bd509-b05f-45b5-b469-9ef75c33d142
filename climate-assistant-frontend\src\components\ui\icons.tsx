import {
  ClipboardListIcon,
  HammerIcon,
  Layers,
  ListEnd,
  BadgeX,
  LucideProps,
} from 'lucide-react';

const iconMap = {
  ClipboardListIcon: ClipboardListIcon,
  HammerIcon: HammerIcon,
  Layers: Layers,
  ListEnd: ListEnd,
  BadgeX: BadgeX,
  HammerSparcle: ({ ...props }: LucideProps) =>
    // prettier-ignore
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" stroke="currentColor" xmlns="http://www.w3.org/2000/svg" { ...props }>
    <g clipPath="url(#clip0_1175_3775)">
    <path d="M9.99921 8L4.33255 13.6667C3.77921 14.22 2.88588 14.22 2.33255 13.6667C2.20112 13.5354 2.09686 13.3795 2.02572 13.2079C1.95458 13.0363 1.91797 12.8524 1.91797 12.6667C1.91797 12.4809 1.95458 12.297 2.02572 12.1254C2.09686 11.9538 2.20112 11.7979 2.33255 11.6667L7.99921 6" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M11.7598 9.99993L14.6664 7.09326" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M13.94 7.80006L13.1067 6.96672C12.7067 6.56672 12.4867 6.03339 12.4867 5.46672V4.89339L10.6733 3.06672C10.3291 2.72047 9.91984 2.44563 9.46908 2.258C9.01831 2.07037 8.53492 1.97365 8.04667 1.97339H6L6.61333 2.52006C7.04897 2.90632 7.3978 3.38052 7.63681 3.91142C7.87583 4.44231 7.99961 5.01784 8 5.60006V6.64006L9.33333 7.97339H10.98L12.4867 9.24672" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M2 5.33325V7.99992" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10 12V14.6667" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M2.66602 1.33325V2.66659" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3.33268 6.66675H0.666016" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M11.3327 13.3333H8.66602" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3.33333 2H2" strokeLinecap="round" strokeLinejoin="round"/>
    </g>
    <defs>
    <clipPath id="clip0_1175_3775">
    <rect width="16" height="16" fill="white"/>
    </clipPath>
    </defs>
  </svg>,
  ColumnAdd: ({ ...props }: LucideProps) =>
    // prettier-ignore
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" xmlns="http://www.w3.org/2000/svg" { ...props }>
      <path d="M9 3H5C4.46957 3 3.96086 3.21071 3.58579 3.58579C3.21071 3.96086 3 4.46957 3 5V9V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H9M9 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V9V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H9M9 3C9 6.51472 9 8.48528 9 12C9 15.5147 9 17.4853 9 21" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12 12H18" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M15 9L15 15" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
  ColumnDelete: ({ ...props }: LucideProps) =>
    // prettier-ignore
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" xmlns="http://www.w3.org/2000/svg" { ...props }>
      <path d="M9 3H5C4.46957 3 3.96086 3.21071 3.58579 3.58579C3.21071 3.96086 3 4.46957 3 5V9V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H9M9 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V9V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H9M9 3C9 6.51472 9 8.48528 9 12C9 15.5147 9 17.4853 9 21" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12 12H18" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
  RowAdd: ({ ...props }: LucideProps) =>
    // prettier-ignore
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" xmlns="http://www.w3.org/2000/svg" { ...props }>
      <path d="M9 3H5C4.46957 3 3.96086 3.21071 3.58579 3.58579C3.21071 3.96086 3 4.46957 3 5V9M9 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V9M9 21H19C19.5304 21 20.0391 20.7893 20.4142 20.4142C20.7893 20.0391 21 19.5304 21 19V9M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V9" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M5 3V2V3ZM3 5H2H3ZM3 9H2V10H3V9ZM19 3V2V3ZM21 9V10H22V9H21ZM9 2H5V4H9V2ZM5 2C4.20435 2 3.44129 2.31607 2.87868 2.87868L4.29289 4.29289C4.48043 4.10536 4.73478 4 5 4V2ZM2.87868 2.87868C2.31607 3.44129 2 4.20435 2 5H4C4 4.73478 4.10536 4.48043 4.29289 4.29289L2.87868 2.87868ZM9 4H19V2H9V4ZM19 4C19.2652 4 19.5196 4.10536 19.7071 4.29289L21.1213 2.87868C20.5587 2.31607 19.7957 2 19 2V4ZM19.7071 4.29289C19.8946 4.48043 20 4.73478 20 5H22C22 4.20435 21.6839 3.44129 21.1213 2.87868L19.7071 4.29289ZM20 5V9H22V5H20ZM2 5V7H4V5H2ZM2 7V9H4V7H2ZM3 10H21V8H3V10Z"/>
      <path d="M9 15H15" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12 12L12 18" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
  RowDelete: ({ ...props }: LucideProps) =>
    // prettier-ignore
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" xmlns="http://www.w3.org/2000/svg" { ...props }>
      <path d="M9 3H5C4.46957 3 3.96086 3.21071 3.58579 3.58579C3.21071 3.96086 3 4.46957 3 5V9M9 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V9M9 21H19C19.5304 21 20.0391 20.7893 20.4142 20.4142C20.7893 20.0391 21 19.5304 21 19V9M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V9" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M5 3V2V3ZM3 5H2H3ZM3 9H2V10H3V9ZM19 3V2V3ZM21 9V10H22V9H21ZM9 2H5V4H9V2ZM5 2C4.20435 2 3.44129 2.31607 2.87868 2.87868L4.29289 4.29289C4.48043 4.10536 4.73478 4 5 4V2ZM2.87868 2.87868C2.31607 3.44129 2 4.20435 2 5H4C4 4.73478 4.10536 4.48043 4.29289 4.29289L2.87868 2.87868ZM9 4H19V2H9V4ZM19 4C19.2652 4 19.5196 4.10536 19.7071 4.29289L21.1213 2.87868C20.5587 2.31607 19.7957 2 19 2V4ZM19.7071 4.29289C19.8946 4.48043 20 4.73478 20 5H22C22 4.20435 21.6839 3.44129 21.1213 2.87868L19.7071 4.29289ZM20 5V9H22V5H20ZM2 5V7H4V5H2ZM2 7V9H4V7H2ZM3 10H21V8H3V10Z"/>
      <path d="M9 15H15" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>,
};

export type IconName = keyof typeof iconMap;

interface IconRendererProps {
  iconName: IconName;
  className?: string;
  style?: React.CSSProperties;
}

const IconRenderer: React.FC<IconRendererProps> = ({
  iconName,
  className,
  style,
}) => {
  const IconComponent = iconMap[iconName];

  if (!IconComponent) {
    return <BadgeX className={className} style={style} />;
  }

  return <IconComponent className={className} style={style} />;
};

export default IconRenderer;
