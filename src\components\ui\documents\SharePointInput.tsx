
import React, { useState } from 'react';
import { Link as LinkIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';

interface SharePointInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: (e: React.FormEvent) => void;
}

export function SharePointInput({ value, onChange, onSubmit }: SharePointInputProps) {
  const [isConnecting, setIsConnecting] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!value.trim()) return;
    
    setIsConnecting(true);
    setProgress(0);
    
    // Simulate progress
    const intervalId = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + Math.random() * 15;
        if (newProgress >= 100) {
          clearInterval(intervalId);
          setTimeout(() => {
            setIsConnecting(false);
            onSubmit(e);
            // Removed duplicate toast message here - the parent component will handle it
          }, 500);
          return 100;
        }
        return newProgress;
      });
    }, 300);
  };

  return (
    <div className="rounded-lg border border-gray-200 p-4">
      <h3 className="text-md font-medium text-glacier-darkBlue mb-3">
        Import from shared folder
      </h3>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <LinkIcon className="h-4 w-4 text-gray-400" />
          </div>
          <Input
            type="text"
            placeholder="Paste SharePoint or Google Drive link"
            value={value}
            onChange={onChange}
            className="pl-10"
            disabled={isConnecting}
          />
        </div>
        
        {isConnecting ? (
          <div className="space-y-2">
            <Progress value={progress} className="h-2" />
            <p className="text-sm text-gray-500">
              Connecting to shared folder... {Math.round(progress)}%
            </p>
          </div>
        ) : (
          <Button 
            type="submit" 
            className="bg-glacier-darkBlue hover:bg-glacier-darkBlue/90 w-full"
            disabled={!value.trim() || isConnecting}
          >
            Connect to Folder
          </Button>
        )}
      </form>
    </div>
  );
}
