import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { LoaderCircle, SaveIcon } from 'lucide-react';
import { FunctionComponent, useEffect, useState } from 'react';

import { cn, userHasRequiredRole } from '@/lib/utils';
import { buttonVariants } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Language } from '@/types';
import { Textarea } from '@/components/ui/textarea';
import { useDashboard } from '@/hooks/useDashboard';
import { updateProject } from '@/api/project-settings/project-settings.api';
import {
  projectSettingUpdateSchema,
  type UpdateProjectRequest,
} from '@/types/project';
import { USER_ROLE } from '@/constants/workspaceConstants';
import { useAuthentication } from '@/api/authentication/authentication.query';

const ProjectSettings: FunctionComponent = () => {
  const { project } = useDashboard();

  const {
    setValue,
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<UpdateProjectRequest>({
    defaultValues: {},
    resolver: zodResolver(projectSettingUpdateSchema),
  });

  useEffect(() => {
    if (project) {
      setValue('name', project.name);
      setValue('primaryContentLanguage', project.primaryContentLanguage);
      setValue('reportTextGenerationRules', project.reportTextGenerationRules);
      setValue('reportingYear', project.reportingYear);
    }
  }, [project]);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { user } = useAuthentication();

  async function onSubmit(data: UpdateProjectRequest) {
    setIsLoading(true);

    try {
      await updateProject(project!.id, data);

      toast({
        title: 'Project settings updated successfully.',
        variant: 'success',
      });
    } catch (error: any) {
      setIsLoading(false);
      return toast({
        title: error.response.statusText || 'Something went wrong.',
        description: error.response.data.message || error.message,
        variant: 'destructive',
      });
    }

    setIsLoading(false);
  }

  return (
    <div className="max-w-3xl">
      {project && (
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid gap-12">
            <div className="grid gap-5">
              <Label className="text-2xl font-semibold" htmlFor="name">
                Project Name
              </Label>
              <Input
                id="name"
                placeholder="Your Name"
                type="text"
                autoCapitalize="none"
                autoComplete="name"
                autoCorrect="off"
                disabled={isLoading}
                {...register('name')}
              />
              {errors?.name && (
                <p className="px-1 text-xs text-red-600">
                  {errors.name.message}
                </p>
              )}
            </div>

            {userHasRequiredRole(
              [
                USER_ROLE.SuperAdmin,
                USER_ROLE.WorkspaceAdmin,
                USER_ROLE.AiContributor,
              ],
              user
            ) && (
              <div className="grid gap-5">
                <Label
                  className="text-2xl font-semibold"
                  htmlFor="primaryContentLanguage"
                >
                  Primary Content Language
                </Label>
                <Select
                  defaultValue={project.primaryContentLanguage}
                  onValueChange={(value: Language) =>
                    setValue('primaryContentLanguage', value)
                  }
                >
                  <SelectTrigger className="w-48">
                    <SelectValue
                      id="primaryContentLanguage"
                      placeholder="Select language"
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(Language).map(([key, value]) => (
                      <SelectItem key={key} value={key}>
                        <div className="flex gap-4">
                          <span>{value}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {errors?.primaryContentLanguage && (
                  <p className="px-1 text-xs text-red-600">
                    {errors.primaryContentLanguage.message}
                  </p>
                )}
              </div>
            )}

            <div className="grid gap-5">
              <Label className="text-2xl font-semibold" htmlFor="reportingYear">
                Reporting Year
              </Label>
              <Textarea
                id="reportingYear"
                placeholder="Enter the reporting year"
                disabled={isLoading}
                {...register('reportingYear')}
              />
              {errors?.reportingYear && (
                <p className="px-1 text-xs text-red-600">
                  {errors.reportingYear.message}
                </p>
              )}
            </div>

            <div className="grid gap-5">
              <Label
                className="text-2xl font-semibold"
                htmlFor="reportTextGenerationRules"
              >
                Report Text Generation Rules
              </Label>

              <Textarea
                id="reportTextGenerationRules"
                placeholder={`Always write "The TAKKT AG" instead of "We", or "The company".\nUse the following company-specific terminology: "products" instead of "goods".`}
                autoCapitalize="none"
                autoCorrect="off"
                rows={10}
                disabled={
                  isLoading ||
                  !userHasRequiredRole(
                    [
                      USER_ROLE.SuperAdmin,
                      USER_ROLE.WorkspaceAdmin,
                      USER_ROLE.AiContributor,
                    ],
                    user
                  )
                }
                {...register('reportTextGenerationRules')}
              />

              {errors?.reportTextGenerationRules && (
                <p className="px-1 text-xs text-red-600">
                  {errors.reportTextGenerationRules.message}
                </p>
              )}
            </div>

            <div className="flex gap-2">
              <button
                className={cn(
                  buttonVariants({ variant: 'outline' }),
                  'flex gap-2'
                )}
                disabled={isLoading}
              >
                {isLoading && <LoaderCircle className="h-4 w-4 animate-spin" />}
                <SaveIcon className="h-4 w-4" />
                Save Project Settings
              </button>
              {/* <button
                className={cn(buttonVariants(), 'flex gap-2')}
                disabled={isLoading}
                >
                {isLoading && (
                    <LoaderCircle className="h-4 w-4 animate-spin" />
                )}
                <Wand2Icon className="h-4 w-4" />
                Save and Apply Rules Automatically
                </button> */}
            </div>
          </div>
        </form>
      )}
    </div>
  );
};

export default ProjectSettings;
