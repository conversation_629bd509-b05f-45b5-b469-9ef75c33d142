import { User } from 'src/users/entities/user.entity';
import {
  DataRequest,
  DataRequestStatus,
} from 'src/data-request/entities/data-request.entity';
import { ESRSDisclosureRequirement } from 'src/knowledge-base/entities/esrs-disclosure-requirement.entity';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { Comment } from 'src/project/entities/comment.entity';
import { DatapointDocumentChunk } from 'src/datapoint-document-chunk/entities/datapoint-document-chunk.entity';
import { DocumentChunk } from 'src/document/entities/document-chunk.entity';
import { Document } from 'src/document/entities/document.entity';

export class DatapointRequestData extends DatapointRequest {
  esrsDatapoint: ESRSDatapoint;
  comments: (Comment & { user: User })[];
  datapointDocumentChunkMap: (DatapointDocumentChunk & {
    documentChunk: DocumentChunk & { document: Document };
  })[];
  documentChunkCount?: number;
}

export class DataRequestData extends DataRequest {
  responsiblePerson: User;
  approver: User;
  disclosureRequirement: ESRSDisclosureRequirement;
  datapointRequests: DatapointRequestData[];
}

export class UpdateDataRequestPayload {
  content?: string;
  dueDate?: Date;
  responsiblePersonId?: string;
  approvedBy?: string;
  approvedAt?: Date;
  status?: DataRequestStatus;
}

export class GenerateDataRequestReportTextTextPayload {
  additionalReportTextGenerationRules: string;
  enableDatapointTags: boolean;
  useExistingReportText: boolean;
}
