import { Injectable } from '@nestjs/common';
import { APIClient, SendEmailRequest } from 'customerio-node';

@Injectable()
export class CustomerIoService {
  private readonly client: APIClient;

  constructor() {
    this.client = new APIClient(process.env.CUSTOMER_IO_TRANSACTIONAL_API_KEY);
  }

  async sendEmailWithTemplate({
    templateId,
    to,
    subject,
    custom_data,
  }: {
    templateId: string;
    to: string;
    subject: string;
    custom_data: any;
  }): Promise<any> {
    const request = new SendEmailRequest({
      transactional_message_id: templateId,
      identifiers: {
        id: to,
      },
      from: '<EMAIL>',
      to,
      subject,
      message_data: { ...custom_data },
    });

    try {
      return await this.client.sendEmail(request);
    } catch (error) {
      console.error('Error sending email:', error.statusCode, error.message);
      throw new Error('Failed to send email');
    }
  }
}
