"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const project_service_1 = require("./project.service");
const project_entity_1 = require("./entities/project.entity");
const project_controller_1 = require("./project.controller");
const workspace_entity_1 = require("../workspace/entities/workspace.entity");
const comment_entity_1 = require("./entities/comment.entity");
const project_guard_1 = require("./project.guard");
const data_request_entity_1 = require("../data-request/entities/data-request.entity");
const knowledge_base_module_1 = require("../knowledge-base/knowledge-base.module");
const user_entity_1 = require("../users/entities/user.entity");
const workspace_module_1 = require("../workspace/workspace.module");
const llm_module_1 = require("../llm/llm.module");
const prompts_module_1 = require("../prompts/prompts.module");
const material_esrs_topic_entity_1 = require("./entities/material-esrs-topic.entity");
const datapoint_request_entity_1 = require("../datapoint/entities/datapoint-request.entity");
const esrs_topic_datapoint_entity_1 = require("../knowledge-base/entities/esrs-topic-datapoint.entity");
const project_dp_service_1 = require("./project-dp.service");
const comment_generation_entity_1 = require("./entities/comment-generation.entity");
const llm_rate_limiter_module_1 = require("../llm-rate-limiter/llm-rate-limiter.module");
const supabase_module_1 = require("../auth/supabase/supabase.module");
let ProjectModule = class ProjectModule {
};
exports.ProjectModule = ProjectModule;
exports.ProjectModule = ProjectModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                project_entity_1.Project,
                comment_entity_1.Comment,
                comment_generation_entity_1.CommentGeneration,
                workspace_entity_1.Workspace,
                data_request_entity_1.DataRequest,
                user_entity_1.User,
                material_esrs_topic_entity_1.MaterialESRSTopic,
                datapoint_request_entity_1.DatapointRequest,
                esrs_topic_datapoint_entity_1.ESRSTopicDatapoint,
            ]),
            supabase_module_1.SupabaseAuthModule,
            knowledge_base_module_1.KnowledgeBaseModule,
            llm_module_1.LlmModule,
            llm_rate_limiter_module_1.LlmRateLimiterModule,
            prompts_module_1.PromptModule,
            workspace_module_1.WorkspaceModule,
        ],
        providers: [project_service_1.ProjectService, project_dp_service_1.ProjectDatapointRequestService, project_guard_1.ProjectGuard],
        exports: [project_service_1.ProjectService],
        controllers: [project_controller_1.ProjectController],
    })
], ProjectModule);
//# sourceMappingURL=project.module.js.map