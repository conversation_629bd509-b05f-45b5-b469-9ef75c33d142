
import React from 'react';
import { ChevronDown, ChevronRight, <PERSON><PERSON><PERSON>cle2, <PERSON>ert<PERSON><PERSON>cle, CircleX, CircleSlash } from 'lucide-react';
import { ECOVADIS_INDICATORS, EcovadisQuestion } from '@/types/ecovadis';
import { <PERSON><PERSON><PERSON>, Toolt<PERSON>Trigger, Toolt<PERSON><PERSON>ontent, TooltipProvider } from '@/components/ui/tooltip';
import { QUESTION_STATUS } from '@/constants/projectConstants';

interface IndicatorSectionProps {
  indicator: ECOVADIS_INDICATORS | string;
  questions: EcovadisQuestion[];
  isExpanded: boolean;
  selectedQuestion: string;
  onToggle: () => void;
  onSelectQuestion: (questionId: string) => void;
}

const getIndicatorDisplayName = (indicator: ECOVADIS_INDICATORS | string): string => {
  switch (indicator) {
    case ECOVADIS_INDICATORS.POLICIES:
      return 'Policies';
    case ECOVADIS_INDICATORS.ENDORSEMENTS:
      return 'Endorsements';
    case ECOVADIS_INDICATORS.MEASURES:
      return 'Measures';
    case ECOVADIS_INDICATORS.CERTIFICATIONS:
      return 'Certifications';
    case ECOVADIS_INDICATORS.COVERAGE:
      return 'Coverage';
    case ECOVADIS_INDICATORS.REPORTING:
      return 'Reporting';
    case ECOVADIS_INDICATORS.WATCH_FINDINGS:
      return '360° Watch Findings';
    default:
      return indicator;
  }
};

const getStatusIcon = (status: string) => {
  switch(status) {
    case QUESTION_STATUS.COMPLETE:
      return <CheckCircle2 className="h-4 w-4 text-green-500" />;
    case QUESTION_STATUS.PENDING:
      return <AlertCircle className="h-4 w-4 text-amber-500" />;
    default:
      return <CircleX className="h-4 w-4 text-gray-400" />;
  }
};

export const IndicatorSection: React.FC<IndicatorSectionProps> = ({
  indicator,
  questions,
  isExpanded,
  selectedQuestion,
  onToggle,
  onSelectQuestion
}) => {
  const displayName = getIndicatorDisplayName(indicator);
  const completedCount = questions.filter(q => q.status === QUESTION_STATUS.COMPLETE).length;

  return (
    <div className="mb-2">
      <button
        className="flex items-center justify-between w-full py-1.5 px-2 text-xs font-medium text-gray-600 hover:bg-gray-100 rounded"
        onClick={onToggle}
      >
        <span className="flex items-center">
          {isExpanded ? 
            <ChevronDown className="h-3 w-3 mr-1 text-gray-500" /> : 
            <ChevronRight className="h-3 w-3 mr-1 text-gray-500" />
          }
          <span>{displayName}</span>
        </span>
        <span className="text-xs text-gray-500">
          {completedCount}/{questions.length}
        </span>
      </button>
      
      {isExpanded && (
        <div className="ml-4 mt-1 space-y-1">
          {questions.map(question => (
            <button
              key={question.projectQuestionId}
              className={`flex items-center justify-between w-full py-1.5 px-2 text-xs rounded transition-colors ${
                selectedQuestion === question.projectQuestionId
                  ? 'bg-glacier-mint/10 text-glacier-darkBlue font-medium'
                  : 'text-gray-500 hover:bg-gray-50'
              }`}
              onClick={() => question.questionId && onSelectQuestion(question.questionId)}
            >
              <span className="text-left truncate">{question.name}</span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex-shrink-0">
                      {question.status && getStatusIcon(question.status)}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>{question.status}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
