import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { JobProcessor } from 'src/types/jobs';

@Module({
  imports: [
    BullModule.registerQueue(
      { name: JobProcessor.ChunkExtraction },
      { name: JobProcessor.ChunkDpLinking },
      { name: JobProcessor.DatapointGeneration },
      { name: JobProcessor.DatapointReview }
    ),
  ],
  exports: [BullModule],
})
export class QueuesModule {}
