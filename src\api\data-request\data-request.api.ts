import axios from 'axios';

import { API_URL } from '../apiConstants';

import { DataRequestPayload } from '@/types';
import {
  CommentData,
  DataRequestData,
  generationStatus,
  IDataGenerations,
} from '@/types/project';
import { GenerateReportTextWithAIFormData } from '@/components/dashboard/AiGenerateDrConfirmModal';

export const updateDataRequestPayload = async (
  dataRequestId: string,
  payload: DataRequestPayload
): Promise<void> => {
  await axios.put(`${API_URL}/data-request/${dataRequestId}`, {
    ...payload,
  });
};

export const updateDataRequestDueDate = async (
  dataRequestId: string,
  dueDate: Date
): Promise<void> => {
  const dateStamp = dueDate.toLocaleDateString('en-CA', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
  await updateDataRequestPayload(dataRequestId, {
    dueDate: dateStamp,
  });
};

export const generateDatapointForDataRequest = async ({
  dataRequestId,
}: {
  dataRequestId: string;
}): Promise<void> => {
  await axios.put(
    `${API_URL}/data-request/${dataRequestId}/generate-bulk-datapoint`
  );
};

export const reviewDatapointForDataRequest = async ({
  dataRequestId,
}: {
  dataRequestId: string;
}): Promise<void> => {
  await axios.put(
    `${API_URL}/data-request/${dataRequestId}/review-bulk-datapoint`
  );
};

export const subscribeToDataRequestEvents = async ({
  dataRequestId,
}: {
  dataRequestId: string;
}) => {
  const url = `${API_URL}/data-request/events/datapoint/${dataRequestId}`;
  const eventSource = new EventSource(url);
  return eventSource;
};

export async function updateDRGenerationStatus(
  generationId: string,
  status: generationStatus
): Promise<{ content?: string; status: generationStatus }> {
  const response = await axios.put(
    `${API_URL}/data-request/generation-status/${generationId}`,
    {
      status,
    }
  );
  return response.data;
}

export async function fetchDataRequestGenerations(
  id: string
): Promise<IDataGenerations[]> {
  const response = await axios.get(`${API_URL}/data-request/${id}/generations`);
  return response.data;
}

export const fetchDataRequestById = async (
  dataRequestId: string
): Promise<DataRequestData> => {
  const response = await axios.get<DataRequestData>(
    `${API_URL}/data-request/${dataRequestId}`
  );
  return response.data;
};

export const approveDataRequest = async (
  dataRequestId: string
): Promise<void> => {
  await axios.put(`${API_URL}/data-request/${dataRequestId}/approve`);
};

export const generateDataRequestWithAi = async (
  dataRequestId: string,
  data: GenerateReportTextWithAIFormData
): Promise<{ content: string; id: string }> => {
  const response = await axios.post(
    `${API_URL}/data-request/${dataRequestId}/generate-with-ai`,
    data
  );
  return response.data;
};

export const reviewDataRequestContentWithAI = async (
  dataRequestId: string
): Promise<CommentData[]> => {
  const gapAnalysisResponse = await axios.post(
    `${API_URL}/data-request/${dataRequestId}/review-with-ai`
  );
  if (gapAnalysisResponse && gapAnalysisResponse.data) {
    return gapAnalysisResponse.data;
  } else {
    throw 'Error while Reviewing with AI';
  }
};
