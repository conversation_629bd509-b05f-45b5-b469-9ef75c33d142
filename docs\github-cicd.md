# CI/CD Pipeline with GitHub Actions for Server Deployment

This readme demonstrates how to set up a CI/CD pipeline using GitHub Actions to deploy code to a remote server whenever changes are pushed or merged to the `staging` branch.

## Workflow Overview

The workflow performs the following steps:
1. Triggers on a `push` to the `staging` branch.
2. Checks out the repository code.
3. Establishes an SSH connection to the remote server using a secure private key stored in GitHub Secrets.
4. Pulls the latest code on the server.
5. Restarts the application using `docker compose` (or other specified deployment commands).

---

## Workflow File

The workflow is defined in `.github/workflows/deploy-staging.yml`:

---

## Setup Instructions

### Step 1: Generate an SSH Key Pair

Run the following command on your local machine to generate an SSH key pair:

```
ssh-keygen -t rsa -b 4096 -C "github-actions" -f github-actions-key
```

This will create:
- `github-actions-key`: Private key (keep this secure).
- `github-actions-key.pub`: Public key.

### Step 2: Configure the Remote Server

1. Append the public key to the `authorized_keys` file of the deployment user on the server:

   ```
   cat github-actions-key.pub >> ~/.ssh/authorized_keys
   ```

2. Secure the file:

   ```
   chmod 600 ~/.ssh/authorized_keys
   ```

3. Restart the SSH service if necessary:

   ```
   sudo service ssh restart
   ```

### Step 3: Add the Private Key to GitHub Secrets

1. Navigate to **Settings** > **Secrets and variables** > **Actions** in your GitHub repository.
2. Click **New repository secret**.
3. Name the secret `SSH_PRIVATE_KEY`.
4. Paste the private key content (`github-actions-key`) into the secret value field.
5. Save the secret.

### Step 4: Verify Docker and Deployment Setup on the Server

Ensure your server:
- Has `docker` and `docker compose` installed.
- The application is properly configured to restart using `docker compose`.

---

## How It Works

1. When a change is pushed to the `staging` branch:
   - GitHub Actions checks out the code.
   - Establishes a secure SSH connection to the remote server.
   - Pulls the latest code from the repository.
   - Restarts the application using `docker compose`.

2. The process is fully automated, ensuring seamless deployment with minimal manual intervention.

---

## Customization

- **Branch Triggers**: Update the `branches` field in the workflow to trigger on different branches.
- **Deployment Commands**: Modify the commands under `Deploy to Server` for non-Dockerized or custom setups.
- **Multi-Environment Support**: Extend the workflow to deploy to staging and production servers using separate jobs.

---

## Troubleshooting

1. **SSH Issues**: Verify the private key in GitHub Secrets matches the public key on the server.
2. **Deployment Fails**: Check the workflow logs in the **Actions** tab for detailed error messages.
3. **Docker Issues**: Ensure `docker` and `docker compose` are installed and functional on the server.