"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const users_module_1 = require("./users/users.module");
const workspace_module_1 = require("./workspace/workspace.module");
const core_1 = require("@nestjs/core");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const chat_module_1 = require("./chat/chat.module");
const env_helper_1 = require("./env-helper");
const knowledge_base_module_1 = require("./knowledge-base/knowledge-base.module");
const document_module_1 = require("./document/document.module");
const prompts_module_1 = require("./prompts/prompts.module");
const datapoint_document_chunk_module_1 = require("./datapoint-document-chunk/datapoint-document-chunk.module");
const project_module_1 = require("./project/project.module");
const datapoint_request_module_1 = require("./datapoint/datapoint-request.module");
const data_request_module_1 = require("./data-request/data-request.module");
const logger_middleware_1 = require("./middleware/logger.middleware");
const email_module_1 = require("./external/email.module");
const bull_1 = require("@nestjs/bull");
const cron_module_1 = require("./cron/cron.module");
const nestjs_1 = require("@bull-board/nestjs");
const express_1 = require("@bull-board/express");
const queue_module_1 = require("./process-queue/queue.module");
const llm_rate_limiter_module_1 = require("./llm-rate-limiter/llm-rate-limiter.module");
const supabase_auth_guard_1 = require("./auth/supabase/supabase.auth.guard");
const supabase_module_1 = require("./auth/supabase/supabase.module");
const queues_module_1 = require("./queues/queues.module");
let AppModule = class AppModule {
    configure(consumer) {
        consumer.apply(logger_middleware_1.LoggerMiddleware).forRoutes('*');
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: (0, env_helper_1.getEnvFilePath)(),
            }),
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => {
                    const connectionCredentials = {
                        url: configService.get('BACKEND_DB_URL'),
                    };
                    return {
                        type: 'postgres',
                        ...connectionCredentials,
                        entities: ['dist/**/*.entity{.ts,.js}'],
                        synchronize: false,
                        autoLoadEntities: true,
                        migrations: ['database/migrations/**/*{.ts,.js}'],
                    };
                },
                dataSourceFactory: async (options) => (0, env_helper_1.createDataSourceWithVectorSupport)(options),
                inject: [config_1.ConfigService],
            }),
            email_module_1.EmailModule,
            config_1.ConfigModule.forRoot(),
            bull_1.BullModule.forRoot({
                redis: {
                    host: (0, env_helper_1.getRedisHost)(),
                    port: 6379,
                },
            }),
            queues_module_1.QueuesModule,
            nestjs_1.BullBoardModule.forRoot({
                route: '/api/queues',
                adapter: express_1.ExpressAdapter,
            }),
            supabase_module_1.SupabaseAuthModule,
            users_module_1.UsersModule,
            workspace_module_1.WorkspaceModule,
            chat_module_1.ChatModule,
            knowledge_base_module_1.KnowledgeBaseModule,
            document_module_1.DocumentModule,
            prompts_module_1.PromptModule,
            project_module_1.ProjectModule,
            data_request_module_1.DataRequestModule,
            llm_rate_limiter_module_1.LlmRateLimiterModule,
            datapoint_request_module_1.DatapointRequestModule,
            datapoint_document_chunk_module_1.DatapointDocumentChunkModule,
            cron_module_1.CronModule,
            queue_module_1.ProcessQueueModule,
        ],
        providers: [
            {
                provide: core_1.APP_GUARD,
                useClass: supabase_auth_guard_1.AuthGuard,
            },
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map