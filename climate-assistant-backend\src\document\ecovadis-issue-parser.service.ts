import { Injectable, Logger } from '@nestjs/common';
import 'dotenv/config';
import { parseDocumentWithLlamaparseApi } from 'src/llm/llamaparse.service';
import { ChatGptService } from 'src/llm/chat-gpt.service';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, Repository } from 'typeorm';
import {
  EcoVadisAnswerOption,
  EcoVadisQuestion,
  EcoVadisSustainabilityIssue,
  EcoVadisTheme,
  ImpactScore,
  ProjectEcoVadisAnswer,
  ProjectEcoVadisTheme,
} from 'src/ecovadis/entities/ecovadis.entity';
import { LLM_MODELS } from 'src/constants';

interface GermanOptionIdentification {
  questionId: string;
  germanOptionIds: string[];
}

@Injectable()
export class EcovadisIssueParserService {
  private readonly logger = new Logger(EcovadisIssueParserService.name);
  constructor(
    private readonly chatGptService: ChatGptService,
    @InjectRepository(EcoVadisSustainabilityIssue)
    private readonly ecoVadisSustainabilityIssueRepository: Repository<EcoVadisSustainabilityIssue>,
    @InjectRepository(EcoVadisTheme)
    private readonly ecoVadisThemeRepository: Repository<EcoVadisTheme>,
    @InjectRepository(ProjectEcoVadisTheme)
    private readonly projectEcoVadisThemeRepository: Repository<ProjectEcoVadisTheme>,
    @InjectRepository(EcoVadisQuestion)
    private readonly ecoVadisQuestionRepository: Repository<EcoVadisQuestion>,
    @InjectRepository(EcoVadisAnswerOption)
    private readonly ecoVadisAnswerOptionRepository: Repository<EcoVadisAnswerOption>,
    @InjectRepository(ProjectEcoVadisAnswer)
    private readonly projectEcoVadisAnswerOptionRepository: Repository<ProjectEcoVadisAnswer>,
    private readonly dataSource: DataSource
  ) {}

  async parseEcovadisSustainabilityIssuePDF(
    filePath: string,
    projectId: string
  ) {
    this.logger.log(
      `Parsing EcoVadis sustainability issue PDF on projectId: ${projectId}`
    );

    const pageSeparator = '<PAGE>=================</PAGE>';
    const allDocumentMarkdown = await parseDocumentWithLlamaparseApi({
      filePath,
      premiumMode: true,
      pageSeparator,
    });
    this.logger.log(
      `Received parsed document from Llamaparse API, text length: ${allDocumentMarkdown.text.length}`
    );

    // split the text into pages
    const pages = allDocumentMarkdown.text.split(pageSeparator);

    const prompt = `You are an AI assistant tasked with extracting structured sustainability risk information from an EcoVadis industry profile document.

Given the following page of text, identify and extract any sustainability issue that appears, along with its metadata, and return it as a JSON object. If not a theme is mentioned, you can mention null.

Use the following structure:

{
  issues: [
    {
      "theme": <title of theme> | null,
      "impact": <impact score / Importance>,
      "issue": "<title of the sustainability issue>",
      "definition": "<short definition provided in the text>",
      "industry_issues": "<summary of the relevant challenges or context described in the 'Industry issues' section>"
    }
  ] 
}

Sensibly pick up the theme name (Ethik, Arbeits- & Menschenrechte, Allgemeines, General, Umwelt, Nachhaltige Beschaffung, Environment, Labor & Human Rights, Ethics or any other) from the text. If present theme usually appears at the beginning of the text before Bedeutung / Impact / Importance. Theme and issue are not the same, and the theme is not always mentioned. If the theme is not mentioned, you can mention null - DO NOT use issue name as theme.

If something is "Non-activated"or "Nicht aktiviert" skip it. Pick only those which contains definition and industry issues.

Extract text as it is from the page, and do not add any additional text or explanation. The output should be a valid JSON object.

### Page Text:
`;

    const issues: any[] = [];

    for (let i = 0; i < pages.length; i++) {
      // if page contains Branchenleistung || Industry Performance end the loop
      if (
        (pages[i].includes('Branchenleistung') &&
          pages[i].includes('Verteilung der Gesamtbewertungen')) ||
        (pages[i].includes('Overall score distribution') &&
          pages[i].includes('Industry Performance'))
      ) {
        this.logger.log(
          `Reached end of relevant pages at page ${i + 1}, stopping parsing.`
        );
        break;
      }

      const gptresponse = await this.chatGptService.createCompletionWithAzure(
        LLM_MODELS['gpt-4o'],
        [
          {
            role: 'user',
            content: prompt + pages[i],
          },
        ],
        true
      );

      // console.log(pages[i]);

      // this.logger.log(
      //   `Received GPT response for page ${i + 1}: ${gptresponse}`
      // );

      try {
        const parsedResponse = JSON.parse(gptresponse);
        if (parsedResponse && parsedResponse.issues) {
          issues.push(...parsedResponse.issues);
        }
      } catch (error) {
        this.logger.error(
          `Error parsing GPT response for page ${i + 1}: ${error.message}`
        );
      }
    }

    // Step 1: Use reduce to group issues by theme name
    const issuesByThemeName = issues.reduce(
      (acc, issue) => {
        const themeName = issue.theme || acc.lastTheme || '';
        acc.lastTheme = themeName; // Track the current theme for issues without theme

        if (!acc.groups[themeName]) {
          acc.groups[themeName] = [];
        }

        acc.groups[themeName].push(issue);
        return acc;
      },
      { groups: {}, lastTheme: null }
    ).groups;

    this.logger.log(
      `Grouped issues by theme name, number of themes:`
      // issuesByThemeName.
    );

    // Step 2: Process each theme group
    for (const [themeName, themeIssues] of Object.entries(issuesByThemeName)) {
      const issuesArray = themeIssues as {
        theme: string | null;
        impact: string;
        issue: string;
        definition: string;
        industry_issues: string;
      }[];

      this.logger.log(
        `Processing theme: ${themeName}, number of issues: ${issuesArray.length}`
      );
      // Find or create theme
      let theme = await this.ecoVadisThemeRepository.findOne({
        where: { title: themeName },
      });

      if (!theme) {
        theme = this.ecoVadisThemeRepository.create({
          title: themeName,
          description: '',
        });
        await this.ecoVadisThemeRepository.save(theme);
      }

      // Process all issues for this theme and collect them
      const issueEntries: {
        issueId: string;
        impact: ImpactScore;
      }[] = [];

      for (const issue of issuesArray) {
        // Find or create sustainability issue
        let existingIssue =
          await this.ecoVadisSustainabilityIssueRepository.findOne({
            where: { issue: issue.issue },
          });

        if (!existingIssue) {
          existingIssue = this.ecoVadisSustainabilityIssueRepository.create({
            issue: issue.issue,
            definition: issue.definition,
            industryIssues: issue.industry_issues || '',
          });
          await this.ecoVadisSustainabilityIssueRepository.save(existingIssue);
        }

        issueEntries.push({
          issueId: existingIssue.id,
          impact: issue.impact as ImpactScore,
        });
      }

      // Find or create project theme
      let projectTheme = await this.projectEcoVadisThemeRepository.findOne({
        where: { projectId, themeId: theme.id },
      });

      if (!projectTheme) {
        // Create new project theme with all issues for this theme
        projectTheme = this.projectEcoVadisThemeRepository.create({
          projectId,
          themeId: theme.id,
          impact: ImpactScore.Medium,
          issues: issueEntries,
        });
      } else {
        projectTheme.issues = issueEntries;
      }

      await this.projectEcoVadisThemeRepository.save(projectTheme);
    }
  }

  /**
   * Main function to identify and delete German answer options with English counterparts
   */
  async cleanupGermanAnswerOptions(dryRun: boolean = true): Promise<{
    totalProcessed: number;
    germanOptionsFound: number;
    deletedCount: number;
    errors: string[];
  }> {
    const results = {
      totalProcessed: 0,
      germanOptionsFound: 0,
      deletedCount: 0,
      errors: [],
    };

    this.logger.log(
      `Starting German answer options cleanup (dryRun: ${dryRun})`
    );

    try {
      // Get all questions with their answer options
      const questions = await this.ecoVadisQuestionRepository.find({
        relations: ['answerOptions'],
        order: { createdAt: 'ASC' },
      });

      this.logger.log(`Found ${questions.length} questions to process`);

      // Process questions in batches to avoid overwhelming the API
      const batchSize = 5;
      for (let i = 0; i < questions.length; i += batchSize) {
        const batch = questions.slice(i, i + batchSize);

        for (const question of batch) {
          try {
            const result = await this.processQuestionOptions(question, dryRun);
            results.totalProcessed++;
            results.germanOptionsFound += result.germanOptionsFound;
            results.deletedCount += result.deletedCount;
          } catch (error) {
            const errorMsg = `Error processing question ${question.id}: ${error.message}`;
            this.logger.error(errorMsg);
            results.errors.push(errorMsg);
          }
        }
      }
    } catch (error) {
      const errorMsg = `Fatal error in cleanup process: ${error.message}`;
      this.logger.error(errorMsg);
      results.errors.push(errorMsg);
    }

    this.logger.log(`Cleanup completed. Results:`, results);
    return results;
  }

  /**
   * Process answer options for a single question
   */
  private async processQuestionOptions(
    question: EcoVadisQuestion,
    dryRun: boolean
  ): Promise<{ germanOptionsFound: number; deletedCount: number }> {
    if (!question.answerOptions || question.answerOptions.length < 2) {
      return { germanOptionsFound: 0, deletedCount: 0 };
    }

    this.logger.debug(
      `Processing question ${question.questionCode} with ${question.answerOptions.length} options`
    );

    // Identify German options using ChatGPT
    const identification = await this.identifyGermanOptions(
      question.answerOptions
    );

    if (identification.germanOptionIds.length === 0) {
      return { germanOptionsFound: 0, deletedCount: 0 };
    }

    this.logger.log(
      `Found ${identification.germanOptionIds.length} German options for question ${question.questionCode}`
    );

    // Delete German options if not dry run
    let deletedCount = 0;
    if (!dryRun) {
      deletedCount = await this.deleteGermanOptions(identification);
    } else {
      this.logger.log(
        `[DRY RUN] Would delete ${identification.germanOptionIds.length} German options`
      );
    }

    return {
      germanOptionsFound: identification.germanOptionIds.length,
      deletedCount,
    };
  }

  /**
   * Use ChatGPT to identify German answer options and their English counterparts
   */
  private async identifyGermanOptions(
    options: EcoVadisAnswerOption[]
  ): Promise<GermanOptionIdentification> {
    const optionsData = options.map((option) => ({
      id: option.id,
      issueTitle: option.issueTitle,
    }));

    const prompt = `
You are analyzing EcoVadis answer options to identify German text that are mixed with English text.

Answer Options:
${JSON.stringify(optionsData, null, 2)}

Task: Identify German options from the list if there are English options.
Get the exact UUIDs of the German options

Return JSON format:
{
"germanOptionIds": ["id1", "id2"]
}

If no German options are found or if no english options are found, return empty arrays.
`;

    try {
      const gptResponse = await this.chatGptService.createCompletionWithAzure(
        LLM_MODELS['o4-mini'],
        [
          {
            role: 'user',
            content: prompt,
          },
        ],
        true
      );

      const parsedResponse = JSON.parse(gptResponse);
      const germanIds = parsedResponse.germanOptionIds || [];

      return {
        questionId: options[0]?.questionId || '',
        germanOptionIds: germanIds,
      };
    } catch (error) {
      this.logger.error(`Error in GPT identification: ${error.message}`);
      return {
        questionId: options[0]?.questionId || '',
        germanOptionIds: [],
      };
    }
  }

  /**
   * Delete German answer options within a transaction
   */
  private async deleteGermanOptions(
    identification: GermanOptionIdentification
  ): Promise<number> {
    try {
      // Check for existing answers linked to these options
      const existingAnswers = await this.dataSource.manager.find(
        ProjectEcoVadisAnswer,
        {
          where: { optionId: In(identification.germanOptionIds) },
        }
      );

      this.logger.warn(
        `Found ${existingAnswers.length} existing answers linked to German options.`
      );

      const deleteProjectAnswersResult =
        await this.projectEcoVadisAnswerOptionRepository.delete({
          optionId: In(identification.germanOptionIds),
        });

      this.logger.log(
        `Deleted ${deleteProjectAnswersResult.affected} project answers linked to German options`
      );

      // Delete German options directly
      const deleteResult = await this.ecoVadisAnswerOptionRepository.delete({
        id: In(identification.germanOptionIds),
      });

      this.logger.log(
        `Deleted ${deleteResult.affected} German options with IDs: ${identification.germanOptionIds.join(
          ', '
        )}`
      );

      return deleteResult.affected || 0;
    } catch (error) {
      this.logger.error(`Error deleting German options: ${error.message}`);
      throw error;
    }
  }
}
