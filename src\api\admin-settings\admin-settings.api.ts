import axios from 'axios';

import { API_URL } from '@/api/apiConstants';
import type { DocumentUpload } from '@/types/project';

export const fetchKnowledgebaseFiles = async (): Promise<DocumentUpload[]> => {
  const response = await axios.get<DocumentUpload[]>(
    `${API_URL}/knowledge-base/file-uploads`
  );
  return response.data;
};

export const uploadKnowledgeBaseFile = async (
  formData: FormData
): Promise<{ message: string }> => {
  const response = await axios.post<{ message: string }>(
    `${API_URL}/knowledge-base/file-upload`,
    formData
  );
  return response.data;
};

export const deleteKnowledgeBaseFile = async (
  id: string
): Promise<{ message: string }> => {
  const response = await axios.delete<{ message: string }>(
    `${API_URL}/knowledge-base/file-uploads/${id}`
  );
  return response.data;
};
