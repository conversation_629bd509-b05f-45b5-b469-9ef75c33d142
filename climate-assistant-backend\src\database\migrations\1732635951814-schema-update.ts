import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1732635951814 implements MigrationInterface {
  name = 'SchemaUpdate1732635951814';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "material_esrs_topic" ADD "active" boolean NOT NULL DEFAULT true`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "material_esrs_topic" DROP COLUMN "active"`,
    );
  }
}
