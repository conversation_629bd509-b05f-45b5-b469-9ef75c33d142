import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
  OnModuleInit,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import * as fs from 'fs';
import type { DocumentChunkGenerated } from 'src/types';
import { DocumentChunk } from 'src/document/entities/document-chunk.entity';
import {
  Document,
  DocumentStatus,
} from 'src/document/entities/document.entity';
import { Workspace } from '../workspace/entities/workspace.entity';
import { DocumentParserService } from './document-parser.service';
import { DatapointDocumentChunk } from 'src/datapoint-document-chunk/entities/datapoint-document-chunk.entity';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { User } from 'src/users/entities/user.entity';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { JobProcessor, JobQueue } from 'src/types/jobs';
import { isDevelopment } from 'src/env-helper';
import { ChatGptService } from 'src/llm/chat-gpt.service';
import { parsePageRanges } from 'src/util/common-util';
import { ProjectEcovadisLinkedDocumentChunks } from '../ecovadis/entities/project-ecovadis-linked-document-chunks.entity';
import { Pinecone } from '@pinecone-database/pinecone';
import {
  RecursiveCharacterTextSplitter,
  TokenTextSplitter,
} from '@langchain/textsplitters';
import { encoding_for_model } from '@dqbd/tiktoken';

@Injectable()
export class DocumentService implements OnModuleInit {
  constructor(
    @InjectRepository(DocumentChunk)
    private readonly documentChunkRepository: Repository<DocumentChunk>,
    @InjectRepository(DatapointDocumentChunk)
    private readonly datapointDocumentChunkRepository: Repository<DatapointDocumentChunk>,
    @InjectRepository(DatapointRequest)
    private readonly datapointRepository: Repository<DatapointRequest>,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
    private readonly documentParserService: DocumentParserService,
    private readonly workspaceService: WorkspaceService,
    private readonly chatGptService: ChatGptService,
    @InjectQueue(JobProcessor.ChunkExtraction)
    private readonly chunkExtractionQueue: Queue,
    @InjectRepository(ProjectEcovadisLinkedDocumentChunks)
    private readonly projectEcovadisLinkedDocumentChunksRepository: Repository<ProjectEcovadisLinkedDocumentChunks>
  ) {}

  private readonly logger = new Logger(DocumentService.name);
  private pineconeClient: Pinecone;
  private tokenizer = encoding_for_model('gpt-3.5-turbo'); // Compatible tokenizer for counting

  async onModuleInit() {
    try {
      this.pineconeClient = new Pinecone({
        apiKey: process.env.PINECONE_API_KEY!,
      });
      this.logger.log('Pinecone client initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Pinecone client:', error);
    }
  }

  async getWorkspaceDocumentUploads(workspaceId: Workspace['id']) {
    const documents = await this.documentRepository
      .createQueryBuilder('document')
      .leftJoinAndSelect('document.creator', 'creator')
      .leftJoinAndSelect('document.chunks', 'chunks')
      .leftJoinAndSelect(
        'chunks.datapointDocumentChunkMap',
        'datapointDocumentChunkMap'
      )
      .select([
        'document.id',
        'document.name',
        'document.createdAt',
        'document.status',
        'document.documentType',
        'document.esrsCategory',
        'document.year',
        'document.month',
        'document.day',
        'document.remarks',
        'document.createdBy',
        'creator.id',
        'creator.name',
        'chunks.id',
        'datapointDocumentChunkMap.id',
        'datapointDocumentChunkMap.active',
        'datapointDocumentChunkMap.datapointRequestId',
      ])
      .where('document.workspaceId = :workspaceId', { workspaceId })
      .orderBy('document.createdAt', 'DESC')
      .getMany();
    const result = [];

    for (const document of documents) {
      const datapointsCount = this.getUniqueDatapointCount(document.chunks);

      result.push({
        ...document,
        chunks: undefined,
        creator: {
          id: document.creator?.id,
          name: document.creator?.name,
        },
        datapointsCount: datapointsCount,
      });
    }

    return result;
  }

  //TODO: Where do we place helper functions?
  getUniqueDatapointCount(documentchunks: DocumentChunk[]) {
    const uniqueIds = new Set();
    // count of  datapointDocumentChunkMap
    return documentchunks
      .map((chunk) => {
        return chunk.datapointDocumentChunkMap.filter((map) => {
          if (map.active && !uniqueIds.has(map.datapointRequestId)) {
            uniqueIds.add(map.datapointRequestId);
            return true;
          }
          return false;
        }).length;
      })
      .reduce((acc, val) => acc + val, 0);
  }

  async findDocumentById(id: Document['id']) {
    const document = await this.documentRepository.findOne({
      where: { id },
    });

    return document;
  }

  async getDocumentData(id: Document['id']) {
    const documentData = await this.documentRepository.findOne({
      where: { id },
      relations: [
        'creator',
        'chunks.datapointDocumentChunkMap.datapointRequest.dataRequest',
      ],
      select: {
        creator: {
          id: true,
          name: true,
        },
      },
    });

    if (!documentData) {
      throw new NotFoundException(`Document not found`);
    }

    const datapointsCount = this.getUniqueDatapointCount(documentData.chunks);
    return {
      ...documentData,
      creator: {
        id: documentData.creator?.id,
        name: documentData.creator?.name,
      },
      datapointsCount: datapointsCount,
    };
  }

  async deleteDocument(
    id: Document['id'],
    workspaceId: Workspace['id']
  ): Promise<void> {
    const hasPermission = await this.documentRepository.exists({
      where: { id, workspaceId },
    });
    if (!hasPermission) {
      throw new UnauthorizedException(
        'You are not allowed to delete this Document'
      );
    }
    const document = await this.documentRepository.findOneOrFail({
      where: { id, workspaceId },
    });
    const documentChunks = await this.documentChunkRepository.find({
      where: { documentId: id },
      select: ['id'],
    });
    const documentChunkIds = documentChunks.map((chunk) => chunk.id);

    // TODO: Delete document from Pinecone first

    await this.datapointDocumentChunkRepository.delete({
      documentChunkId: In(documentChunkIds),
    });
    await this.documentChunkRepository.delete({ documentId: id });
    await this.documentRepository.delete({ id, workspaceId });
    if (fs.existsSync(document.path)) {
      fs.unlinkSync(document.path);
    }
  }

  async saveDocument({
    originalname,
    path,
    workspaceId,
    userId,
    documentType,
    esrsCategory,
    year,
    month,
    day,
    remarks,
    pageNumbers,
    answerId,
    premiumParse,
  }: {
    originalname: string;
    path: string;
    workspaceId: Workspace['id'];
    userId: User['id'];
    documentType: string;
    esrsCategory: string[];
    year: number;
    month: number | null;
    day: number | null;
    remarks: string;
    pageNumbers?: string;
    answerId?: string;
    premiumParse?: boolean;
  }) {
    const documentExists = await this.documentRepository.exists({
      where: { workspaceId, name: originalname },
    });

    if (documentExists) {
      throw new BadRequestException('Document with name already exists');
    }

    const upload = await this.documentRepository.save({
      workspaceId,
      createdBy: userId,
      path,
      name: originalname,
      documentType,
      esrsCategory,
      year,
      month,
      day,
      remarks,
    });

    await this.workspaceService.storeActionHistory({
      event: 'new_document_uploaded',
      ref: upload.id,
      workspaceId: workspaceId,
      versionData: {
        event: 'new_document_uploaded',
        doneBy: userId,
        data: upload,
      },
    });

    try {
      await this.chunkExtractionQueue.add(
        JobQueue.ChunkExtract,
        {
          documentId: upload.id,
          pageNumbers: pageNumbers,
          answerId: answerId,
          comment: remarks,
          premiumParse: premiumParse ?? true,
        },
        {
          jobId: `chunkExtraction-${upload.id}`,
          attempts: 5,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
          removeOnComplete: isDevelopment ? false : true,
        }
      );
      this.logger.log(
        `Added job to queue for document chunk extraction: ${upload.id}`
      );
      this.documentRepository.update(upload.id, {
        status: DocumentStatus.QueuedForExtraction,
      });
    } catch (error) {
      this.logger.error(`Error adding job to queue:`, error);
    }
    return upload;

    //This is not awaited and done in the background
    //this.extractDocumentChunks(document.id, true);
  }

  async updateDocumentSettings({
    id,
    workspaceId,
    userId,
    documentType,
    esrsCategory,
    year,
    month,
    day,
    remarks,
  }: {
    id: Document['id'];
    workspaceId: Workspace['id'];
    userId: User['id'];
    documentType: string;
    esrsCategory: string[];
    year: number;
    month: number | null;
    day: number | null;
    remarks: string;
  }) {
    const document = await this.documentRepository.findOneOrFail({
      where: { id },
    });

    document.documentType = documentType;
    document.esrsCategory = esrsCategory;
    document.year = year;
    document.month = month;
    document.day = day;
    document.remarks = remarks;

    const update = await this.documentRepository.save(document);

    await this.workspaceService.storeActionHistory({
      event: 'document_settings_updated',
      ref: update.id,
      workspaceId: workspaceId,
      versionData: {
        event: 'document_settings_updated',
        doneBy: userId,
        data: update,
      },
    });
  }

  async extractDocumentChunks(id: Document['id'], premiumMode?: boolean) {
    this.logger.log(`Extracting document chunks for document: ${id}`);
    const document: Document = await this.documentRepository.findOneBy({
      id: id,
    });

    if (!document) {
      this.logger.error(`Document not found: ${id}`);
      return;
    }

    document.status = DocumentStatus.ExtractingData;
    this.documentRepository.save(document);
    const chunks: DocumentChunkGenerated[] =
      await this.documentParserService.parseDocumentToMarkdown(
        document.path,
        premiumMode
      );

    for (const chunkContent of chunks) {
      const cleanChunk = chunkContent.text.replace(/\n/g, '').trim();
      if (cleanChunk !== '') {
        const chunk = await this.documentChunkRepository.save({
          documentId: id,
          page: chunkContent.metadata.pageNumber,
          content: chunkContent.text,
          metadataJson: JSON.stringify(chunkContent.metadata),
        });
        // console.log(savedChunk);
        await this.indexChunkToPinecone({ document, chunk });
      }
    }
    document.status = DocumentStatus.LinkingDataFinished;
    this.documentRepository.save(document);
  }

  /**
   * Count tokens in text using tiktoken
   */
  private countTokens(text: string): number {
    return this.tokenizer.encode(text).length;
  }

  /**
   * Split text into smaller chunks for better vector search performance using LangChain
   * Optimized for Azure OpenAI text-embedding-3-large model (max 8,191 tokens)
   * Uses TokenTextSplitter to prevent infinite loops and ensure accurate token-based splitting
   */
  private async splitTextIntoChunks(
    text: string,
    maxTokens: number = 500,
    overlap: number = 15,
    maxRetries: number = 3
  ): Promise<string[]> {
    // First check if the entire text is within token limits
    const totalTokens = this.countTokens(text);
    if (totalTokens <= maxTokens) {
      return [text];
    }

    try {
      // Use TokenTextSplitter for accurate token-based splitting
      const tokenSplitter = new TokenTextSplitter({
        chunkSize: maxTokens,
        chunkOverlap: overlap,
        encodingName: 'gpt2', // Compatible with OpenAI models
      });

      const chunks = await tokenSplitter.splitText(text);

      // Filter out very small chunks
      const validatedChunks = chunks.filter(
        (chunk) => chunk.trim().length > 50
      );

      this.logger.log(
        `Successfully split text (${totalTokens} tokens) into ${validatedChunks.length} chunks using TokenTextSplitter`
      );

      return validatedChunks;
    } catch (error) {
      this.logger.warn(
        `TokenTextSplitter failed, falling back to RecursiveCharacterTextSplitter: ${error.message}`
      );

      // Fallback to character-based splitting with protection against infinite recursion
      return this.fallbackCharacterBasedSplitting(
        text,
        maxTokens,
        overlap,
        maxRetries
      );
    }
  }

  /**
   * Fallback splitting method using RecursiveCharacterTextSplitter with recursion protection
   */
  private async fallbackCharacterBasedSplitting(
    text: string,
    maxTokens: number,
    overlap: number,
    maxRetries: number
  ): Promise<string[]> {
    const totalTokens = this.countTokens(text);

    if (maxRetries <= 0) {
      this.logger.error(
        'Maximum retries reached for text splitting. Truncating text.'
      );
      // As a last resort, truncate the text to fit within token limits
      // For tiktoken, we need to work with character-based truncation since token decoding can be complex
      const ratio = maxTokens / totalTokens;
      const truncatedLength = Math.floor(text.length * ratio * 0.9); // Use 90% to be safe
      const truncatedText = text.substring(0, truncatedLength);
      return [truncatedText];
    }

    // Use character-based estimation for initial chunking
    const estimatedChunkSize = Math.floor(
      (text.length * maxTokens) / totalTokens
    );

    // Ensure minimum chunk size to prevent infinite recursion
    const minChunkSize = Math.max(100, Math.floor(text.length / 10));
    const actualChunkSize = Math.max(estimatedChunkSize, minChunkSize);

    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: actualChunkSize,
      chunkOverlap: overlap,
      separators: ['\n\n', '\n', '. ', ' ', ''],
    });

    let chunks = await textSplitter.splitText(text);

    // Validate and handle oversized chunks with recursion protection
    const validatedChunks: string[] = [];
    for (const chunk of chunks) {
      const chunkTokens = this.countTokens(chunk);

      if (chunkTokens <= maxTokens) {
        // Chunk is within limits, keep it
        if (chunk.trim().length > 50) {
          validatedChunks.push(chunk);
        }
      } else {
        // Chunk is too large, try one more split with reduced retry count
        this.logger.warn(
          `Chunk with ${chunkTokens} tokens exceeds limit (retry ${4 - maxRetries}/3)`
        );

        const subChunks = await this.fallbackCharacterBasedSplitting(
          chunk,
          maxTokens,
          Math.floor(overlap / 2),
          maxRetries - 1
        );
        validatedChunks.push(...subChunks);
      }
    }

    return validatedChunks;
  }

  /**
   * Index document chunk to Pinecone vector database with dense embeddings
   * Optimized for Azure OpenAI text-embedding-3-large (max 8,191 tokens, 3,072 dimensions)
   */
  async indexChunkToPinecone({
    document,
    chunk,
  }: {
    document: Document;
    chunk: DocumentChunk;
  }) {
    if (!this.pineconeClient) {
      this.logger.error('Pinecone client not initialized');
      return;
    }

    try {
      // Get index configuration from environment variables
      const indexName = process.env.PINECONE_INDEX_NAME;
      const indexHost = process.env.PINECONE_INDEX_HOST;
      const namespace = process.env.PINECONE_NAMESPACE;

      if (!indexHost) {
        throw new Error('PINECONE_INDEX_HOST environment variable is required');
      }

      // Get the index and namespace
      const index = this.pineconeClient.index(indexName, indexHost);
      const namespaceIndex = index.namespace(namespace);

      // Split the chunk content into smaller pieces respecting token limits
      const textChunks = await this.splitTextIntoChunks(chunk.content);

      this.logger.log(
        `Split chunk ${chunk.id} into ${textChunks.length} sub-chunks for embedding`
      );

      // Create embeddings for each text chunk with error handling
      const records = await Promise.all(
        textChunks.map(async (textChunk, index) => {
          try {
            // Log token count for monitoring
            const tokenCount = this.countTokens(textChunk);
            this.logger.debug(
              `Creating embedding for sub-chunk ${index} with ${tokenCount} tokens`
            );

            // Validate token count before sending to API
            if (tokenCount > 8191) {
              throw new Error(
                `Text chunk exceeds Azure OpenAI token limit: ${tokenCount} > 8191`
              );
            }

            // Generate embedding using Azure OpenAI
            const embedding =
              await this.chatGptService.createEmbedding(textChunk);

            // Validate embedding dimensions (should be 3,072 for text-embedding-3-large)
            if (embedding.length !== 3072) {
              this.logger.warn(
                `Unexpected embedding dimensions: ${embedding.length}, expected 3072`
              );
            }

            return {
              id: `${chunk.id}-${index}`,
              values: embedding,
              metadata: {
                chunk_text: textChunk,
                chunk_id: chunk.id,
                document_id: document.id,
                page: chunk.page.toString(),
                workspace_id: document.workspaceId,
                year: document.year?.toString() || '',
                sub_chunk_index:
                  index.toString() + '/' + textChunks.length.toString(),
                token_count: tokenCount.toString(),
              },
            };
          } catch (embeddingError) {
            this.logger.error(
              `Failed to create embedding for sub-chunk ${index}:`,
              embeddingError
            );
            throw embeddingError;
          }
        })
      );

      // Upsert records to Pinecone
      await namespaceIndex.upsert(records);

      this.logger.log(
        `Successfully indexed ${records.length} sub-chunks for document chunk ${chunk.id} (${document.name}, page ${chunk.page})`
      );
    } catch (error) {
      this.logger.error(`Error indexing chunk ${chunk.id} to Pinecone:`, error);
      throw error;
    }
  }

  /**
   * Delete all vectors for a document from Pinecone vector database
   * This method deletes all vectors associated with a document ID, regardless of chunk subdivision
   */
  async deleteDocumentFromPinecone(documentId: Document['id']) {
    if (!this.pineconeClient) {
      this.logger.error('Pinecone client not initialized');
      return;
    }

    try {
      // Get index configuration from environment variables
      const indexName = process.env.PINECONE_INDEX_NAME || 'climate-documents';
      const indexHost = process.env.PINECONE_INDEX_HOST;
      const namespace = process.env.PINECONE_NAMESPACE || 'default';

      if (!indexHost) {
        throw new Error('PINECONE_INDEX_HOST environment variable is required');
      }

      // Get the index directly
      const index = this.pineconeClient.index(indexName, indexHost);

      // Delete all vectors with matching document_id metadata
      await index.deleteMany({
        filter: { document_id: documentId },
        namespace: namespace,
      });

      this.logger.log(
        `Successfully deleted all vectors for document ${documentId} from Pinecone`
      );
    } catch (error) {
      this.logger.error(
        `Error deleting document ${documentId} from Pinecone:`,
        error
      );
      throw error;
    }
  }

  /**
   * Reindex a single document into Pinecone
   * This method will:
   * 1. Delete all existing vectors for the document from Pinecone
   * 2. Re-create embeddings for all document chunks
   * 3. Re-upload the vectors to Pinecone
   *
   * @param documentId - The ID of the document to reindex
   * @throws NotFoundException if document is not found
   * @throws Error if Pinecone operations fail
   */
  async reindexDocument(documentId: Document['id']) {
    this.logger.log(`Starting reindex process for document: ${documentId}`);

    const document = await this.documentRepository.findOne({
      where: { id: documentId },
    });

    if (!document) {
      throw new NotFoundException(`Document not found`);
    }

    try {
      // TODO: First, delete all existing vectors for this document from Pinecone
      // this.logger.log(
      //   `Deleting existing vectors for document ${documentId} from Pinecone`
      // );
      // await this.deleteDocumentFromPinecone(documentId);

      // Get all chunks for this document
      const chunks = await this.documentChunkRepository.find({
        where: { documentId },
        order: { page: 'ASC' },
      });

      this.logger.log(
        `Found ${chunks.length} chunks to reindex for document ${documentId}`
      );

      // Reindex each chunk
      for (const chunk of chunks) {
        this.logger.log(`Reindexing chunk: ${chunk.id} (page ${chunk.page})`);
        await this.indexChunkToPinecone({ document, chunk });
      }

      this.logger.log(
        `Successfully completed reindex for document ${documentId}`
      );
    } catch (error) {
      this.logger.error(
        `Error during reindex of document ${documentId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Reindex multiple documents or all documents in a workspace
   * This method provides bulk reindexing capabilities for administrative operations
   *
   * @param options - Configuration object with either documentIds or workspaceId
   * @param options.documentIds - Array of specific document IDs to reindex
   * @param options.workspaceId - Workspace ID to reindex all documents within
   * @returns Results object containing successful and failed reindex operations
   * @throws BadRequestException if neither documentIds nor workspaceId is provided
   */
  async bulkReindexDocuments(options: {
    documentIds?: Document['id'][];
    workspaceId?: Workspace['id'];
  }) {
    const { documentIds, workspaceId } = options;

    let documentsToReindex: Document[];

    if (documentIds && documentIds.length > 0) {
      // Reindex specific documents
      documentsToReindex = await this.documentRepository.find({
        where: { id: In(documentIds) },
        select: ['id', 'name'],
      });
      this.logger.log(
        `Starting bulk reindex for ${documentsToReindex.length} specific documents`
      );
    } else if (workspaceId) {
      // Reindex all documents in a workspace
      documentsToReindex = await this.documentRepository.find({
        where: { workspaceId },
        select: ['id', 'name'],
      });
      this.logger.log(
        `Starting bulk reindex for all ${documentsToReindex.length} documents in workspace ${workspaceId}`
      );
    } else {
      throw new BadRequestException(
        'Either documentIds or workspaceId must be provided'
      );
    }

    const results = {
      success: [] as string[],
      failed: [] as { id: string; error: string }[],
    };

    for (const document of documentsToReindex) {
      try {
        await this.reindexDocument(document.id);
        results.success.push(document.id);
        this.logger.log(
          `Successfully reindexed document: ${document.name} (${document.id})`
        );
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error';
        results.failed.push({ id: document.id, error: errorMessage });
        this.logger.error(
          `Failed to reindex document: ${document.name} (${document.id})`,
          error
        );
      }
    }

    this.logger.log(
      `Bulk reindex completed. Success: ${results.success.length}, Failed: ${results.failed.length}`
    );

    return results;
  }

  async findDocumentChunkById(id: DocumentChunk['id']) {
    const documentChunk = await this.documentChunkRepository.findOne({
      where: { id },
      relations: ['document'],
    });

    return documentChunk;
  }

  async deleteDocumentChunk({
    id,
    workspaceId,
    userId,
  }: {
    id: DocumentChunk['id'];
    workspaceId: Workspace['id'];
    userId: string;
  }) {
    const documentChunk = await this.documentChunkRepository.findOne({
      where: { id },
    });

    await this.workspaceService.storeActionHistory({
      event: 'document_chunk_deleted',
      ref: documentChunk.documentId,
      workspaceId: workspaceId,
      versionData: {
        event: 'document_chunk_deleted',
        doneBy: userId,
        data: documentChunk,
      },
    });

    await this.documentChunkRepository.delete({ id });
  }

  async getDocumentChunk(id: DocumentChunk['id']) {
    const documentChunk = await this.documentChunkRepository.findOne({
      where: { id },
      relations: ['datapointDocumentChunkMap.datapointRequest'],
    });

    if (!documentChunk) {
      throw new NotFoundException(`Document Chunk not found`);
    }

    return documentChunk;
  }

  async createDocumentChunkMap({
    documentChunkId,
    datapointRequestId,
    userId,
  }: {
    documentChunkId: DocumentChunk['id'];
    datapointRequestId: DatapointRequest['id'];
    userId: string;
  }) {
    const documentChunk = await this.documentChunkRepository.findOne({
      where: { id: documentChunkId },
    });

    const datapointRequest = await this.datapointRepository.findOne({
      where: { id: datapointRequestId },
    });

    if (!datapointRequest || !documentChunk) {
      throw new NotFoundException(`Linking components not found`);
    }

    await this.datapointDocumentChunkRepository.save({
      documentChunk,
      datapointRequest,
      createdBy: userId,
    });
  }

  async updateDocumentChunkMap({
    documentChunkId,
    datapointRequestId,
    userId,
    status,
  }: {
    documentChunkId: DocumentChunk['id'];
    datapointRequestId: DatapointRequest['id'];
    userId: string;
    status: boolean;
  }) {
    const documentChunk = await this.datapointDocumentChunkRepository.findOne({
      where: {
        documentChunkId,
        datapointRequestId,
      },
    });

    if (!documentChunk) {
      if (status === false) {
        throw new NotFoundException(`Linking components not found`);
      } else {
        await this.createDocumentChunkMap({
          documentChunkId,
          datapointRequestId,
          userId,
        });
      }
    } else {
      await this.datapointDocumentChunkRepository.update(
        {
          id: documentChunk.id,
        },
        {
          active: status,
          modifiedBy: userId,
        }
      );
    }
  }

  async bulkUpdateDocumentChunkMap({
    documentChunkId,
    userId,
    data,
  }: {
    documentChunkId: DocumentChunk['id'];
    userId: string;
    data: {
      datapointRequestId: DatapointRequest['id'];
      linked: boolean;
    }[];
  }) {
    for (const { datapointRequestId, linked } of data) {
      try {
        await this.updateDocumentChunkMap({
          documentChunkId,
          datapointRequestId,
          userId,
          status: linked,
        });
      } catch (error) {
        console.log(error);
      }
    }
  }

  async updateDocumentStatus(
    documentId: Document['id'],
    { status }: { status: DocumentStatus }
  ) {
    await this.documentRepository.update(documentId, { status });
  }

  async findDocumentChunkByPage(documentId: string, pageNumber: number) {
    const chunks = await this.documentChunkRepository.find({
      where: {
        documentId,
      },
    });

    // Find chunks where the pageNumber falls within the page range
    return chunks.find((chunk) => {
      const chunkPage = chunk.page;

      // If chunkPage contains range (e.g. "1-3" or "1-3,5,7-9")
      if (
        typeof chunkPage === 'string' &&
        (chunkPage.includes('-') || chunkPage.includes(','))
      ) {
        const pageNumbers = parsePageRanges(chunkPage);
        return pageNumbers.includes(pageNumber);
      }
      return Number(chunkPage) === pageNumber;
    });
  }

  async saveLinkedDocumentChunk({
    documentChunkId,
    answerId,
    comment = '',
  }: {
    documentChunkId: string;
    answerId: string;
    comment: string;
  }) {
    return this.projectEcovadisLinkedDocumentChunksRepository.save({
      documentChunkId,
      answerId,
      comment,
    });
  }
}
