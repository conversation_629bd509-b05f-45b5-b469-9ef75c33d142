CREATE TYPE attachment_source_enum AS ENUM ('manual', 'ai', 'import', 'system');

ALTER TABLE public.project_ecovadis_linked_document_chunks
ADD COLUMN created_by UUI<PERSON>,
ADD COLUMN attachment_source attachment_source_enum NOT NULL DEFAULT 'manual';

-- Add single foreign key constraint
ALTER TABLE public.project_ecovadis_linked_document_chunks
ADD CONSTRAINT "project_ecovadis_linked_document_chunks_created_by_fkey"
FOREIGN KEY (created_by)
REFERENCES public.user(id);

-- Create index
CREATE INDEX IF NOT EXISTS "IDX_linked_chunks_created_by"
ON public.project_ecovadis_linked_document_chunks(created_by);

UPDATE project_ecovadis_linked_document_chunks SET attachment_source = 'system';

-- ALTER TABLE public.project_ecovadis_linked_document_chunks
--   DROP COLUMN created_by,
--   DROP COLUMN attachment_source;

-- DROP TYPE attachment_source_enum;