"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectDatapointRequestService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const datapoint_request_entity_1 = require("../datapoint/entities/datapoint-request.entity");
const data_request_entity_1 = require("../data-request/entities/data-request.entity");
const common_util_1 = require("../util/common-util");
let ProjectDatapointRequestService = class ProjectDatapointRequestService {
    constructor(datapointRequestRepository) {
        this.datapointRequestRepository = datapointRequestRepository;
    }
    async findDatapointRequestCollectiveData(datapointRequestId) {
        const datapointRequest = await this.datapointRequestRepository
            .createQueryBuilder('dpRequest')
            .select(['dpRequest.id', 'dpRequest.content'])
            .leftJoin('dpRequest.esrsDatapoint', 'esrsDatapoint')
            .addSelect(['esrsDatapoint.id'])
            .leftJoin('dpRequest.dataRequest', 'dataRequest')
            .addSelect(['dataRequest.id'])
            .leftJoin('dpRequest.comments', 'comments')
            .addSelect(['comments.id', 'comments.resolved'])
            .leftJoin('dpRequest.datapointDocumentChunkMap', 'datapointDocumentChunkMap')
            .addSelect(['datapointDocumentChunkMap.id'])
            .leftJoin('datapointDocumentChunkMap.documentChunk', 'documentChunk')
            .addSelect(['documentChunk.id'])
            .leftJoin('documentChunk.document', 'document')
            .addSelect(['document.id'])
            .where('dpRequest.id = :id', { id: datapointRequestId })
            .getOne();
        if (!datapointRequest) {
            throw new common_1.NotFoundException(`Datapoint with ID ${datapointRequest} not found`);
        }
        return datapointRequest;
    }
    async datapointRequestStatusProcessor(datapointRequestId, optionalDatapoint) {
        if (optionalDatapoint) {
            return datapoint_request_entity_1.DatapointRequestStatus.NotAnswered;
        }
        const datapointRequest = await this.findDatapointRequestCollectiveData(datapointRequestId);
        if (!datapointRequest) {
            throw new common_1.NotFoundException(`Datapoint Request not found`);
        }
        const { content, datapointDocumentChunkMap: datapointDocumentChunkMaps, comments, } = datapointRequest;
        if (!(0, common_util_1.isTextPresentInHTML)(content)) {
            return datapoint_request_entity_1.DatapointRequestStatus.NoData;
        }
        const hasUnresolvedComments = comments.some((comment) => !comment.resolved);
        if (hasUnresolvedComments && content) {
            return datapoint_request_entity_1.DatapointRequestStatus.IncompleteData;
        }
        const noUnresolvedComments = comments.every((comment) => comment.resolved);
        if (content && noUnresolvedComments) {
            return datapoint_request_entity_1.DatapointRequestStatus.CompleteData;
        }
        return datapoint_request_entity_1.DatapointRequestStatus.NoData;
    }
    dataRequestStatusProcessor({ status, content, approvedBy, datapointRequests, }) {
        if (status === data_request_entity_1.DataRequestStatus.ApprovedAnswer) {
            return data_request_entity_1.DataRequestStatus.ApprovedAnswer;
        }
        const validateDPForNotReported = datapointRequests.every((datapoint) => datapoint.status === datapoint_request_entity_1.DatapointRequestStatus.NotAnswered);
        if (validateDPForNotReported) {
            return data_request_entity_1.DataRequestStatus.NotAnswered;
        }
        datapointRequests.filter((datapoint) => {
            return (datapoint.status !== datapoint_request_entity_1.DatapointRequestStatus.NotAnswered &&
                !datapoint.content &&
                !(datapoint.datapointDocumentChunkMap &&
                    datapoint.datapointDocumentChunkMap.length));
        });
        if (datapointRequests.length === 0) {
            return data_request_entity_1.DataRequestStatus.NoData;
        }
        const validateDPForIncompleteData = datapointRequests.some((datapoint) => datapoint.status !== datapoint_request_entity_1.DatapointRequestStatus.CompleteData &&
            datapoint.status !== datapoint_request_entity_1.DatapointRequestStatus.NotAnswered);
        if (validateDPForIncompleteData) {
            return data_request_entity_1.DataRequestStatus.IncompleteData;
        }
        const validateDPForCompleteData = datapointRequests.every((datapoint) => datapoint.status === datapoint_request_entity_1.DatapointRequestStatus.CompleteData);
        if (validateDPForCompleteData &&
            (!content || content.trim() === '' || content.trim() === '<p></p>')) {
            return data_request_entity_1.DataRequestStatus.CompleteData;
        }
        if (approvedBy === null) {
            return data_request_entity_1.DataRequestStatus.Draft;
        }
        return data_request_entity_1.DataRequestStatus.ApprovedAnswer;
    }
};
exports.ProjectDatapointRequestService = ProjectDatapointRequestService;
exports.ProjectDatapointRequestService = ProjectDatapointRequestService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(datapoint_request_entity_1.DatapointRequest)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ProjectDatapointRequestService);
//# sourceMappingURL=project-dp.service.js.map