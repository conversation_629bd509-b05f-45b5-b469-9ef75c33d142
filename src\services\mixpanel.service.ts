import mixpanel from 'mixpanel-browser';

export type AnalyticsEvent = { id: string; payload?: any };

const MIXPANEL_TOKEN = import.meta.env.VITE_MIXPANEL_TOKEN;

export const MixpanelEvents = {
  login: () => ({ id: 'login' }),
  newChatStarted: (type: string) => ({
    id: 'new_chat_created',
    payload: { type },
  }),
  messageSent: () => ({
    id: 'message_sent',
  }),
  historyDeleted: () => ({
    id: 'history_deleted',
  }),
  historySelected: () => ({
    id: 'history_selected',
  }),
  sidebarToggled: () => ({
    id: 'sidebar_toggled',
  }),
  textCopied: () => ({
    id: 'text_copied',
  }),
  textLiked: (text: string) => ({
    id: 'text_liked',
    payload: { text },
  }),
  textDisliked: (text: string) => ({
    id: 'text_disliked',
    payload: { text },
  }),
};

let isMixpanelInitialized = false;

export const MixpanelService = {
  init: () => {
    mixpanel.init(MIXPANEL_TOKEN);
    isMixpanelInitialized = true;
  },

  identifyUser: async (email: string) => {
    if (!isMixpanelInitialized) {
      return;
    }
    mixpanel.identify(email);

    await new Promise<void>((resolve, reject) => {
      mixpanel.people.set(
        {
          $email: email,
        },
        (status) => (status === 1 ? resolve() : reject())
      );
    }).catch((error: object) => {
      console.error('Failed to identify user:', error);
      // Sentry.captureException({
      //   info: 'Mixpanel identification failed',
      //   error,
      // });
    });

    MixpanelService.track(MixpanelEvents.login());
  },

  track: (event: AnalyticsEvent) => {
    if (!isMixpanelInitialized) {
      return;
    }
    mixpanel.track(event.id, { ...event?.payload });
  },

  reset: () => {
    try {
      mixpanel.reset();
    } catch (_e) {}
  },
};
