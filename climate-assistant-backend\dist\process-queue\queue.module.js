"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessQueueModule = void 0;
const common_1 = require("@nestjs/common");
const nestjs_1 = require("@bull-board/nestjs");
const bullAdapter_1 = require("@bull-board/api/bullAdapter");
const queue_service_1 = require("./queue.service");
const jobs_1 = require("../types/jobs");
const document_module_1 = require("../document/document.module");
const datapoint_document_chunk_module_1 = require("../datapoint-document-chunk/datapoint-document-chunk.module");
const users_module_1 = require("../users/users.module");
const datapoint_request_module_1 = require("../datapoint/datapoint-request.module");
const data_request_module_1 = require("../data-request/data-request.module");
const queues_module_1 = require("../queues/queues.module");
let ProcessQueueModule = class ProcessQueueModule {
};
exports.ProcessQueueModule = ProcessQueueModule;
exports.ProcessQueueModule = ProcessQueueModule = __decorate([
    (0, common_1.Module)({
        imports: [
            nestjs_1.BullBoardModule.forFeature({
                name: jobs_1.JobProcessor.ChunkExtraction,
                adapter: bullAdapter_1.BullAdapter,
            }),
            nestjs_1.BullBoardModule.forFeature({
                name: jobs_1.JobProcessor.ChunkDpLinking,
                adapter: bullAdapter_1.BullAdapter,
            }),
            nestjs_1.BullBoardModule.forFeature({
                name: jobs_1.JobProcessor.DatapointGeneration,
                adapter: bullAdapter_1.BullAdapter,
            }),
            nestjs_1.BullBoardModule.forFeature({
                name: jobs_1.JobProcessor.DatapointReview,
                adapter: bullAdapter_1.BullAdapter,
            }),
            queues_module_1.QueuesModule,
            document_module_1.DocumentModule,
            users_module_1.UsersModule,
            datapoint_document_chunk_module_1.DatapointDocumentChunkModule,
            datapoint_request_module_1.DatapointRequestModule,
            data_request_module_1.DataRequestModule,
        ],
        providers: [
            queue_service_1.ChunkExtractionProcessor,
            queue_service_1.ChunkLinkingProcessor,
            queue_service_1.DatapointGenerationProcessor,
            queue_service_1.DatapointReviewProcessor,
        ],
        exports: [],
    })
], ProcessQueueModule);
//# sourceMappingURL=queue.module.js.map