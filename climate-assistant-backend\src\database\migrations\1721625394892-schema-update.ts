import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1721625394892 implements MigrationInterface {
  name = 'SchemaUpdate1721625394892';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user"
          ADD "companyName" character varying NOT NULL DEFAULT ''`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "companyName"`);
  }
}
