import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, In, Repository, IsNull } from 'typeorm';
import {
  DatapointRequestData,
  DatapointRequestWithDocumentCount,
} from './entities/datapoint-request.dto';
import {
  DatapointQueueStatus,
  DatapointRequest,
  DatapointRequestStatus,
} from 'src/datapoint/entities/datapoint-request.entity';
import { ChatCompletionMessageParam } from 'openai/resources';
import { PromptService } from 'src/prompts/prompts.service';
import { ProjectService } from 'src/project/project.service';
import { Comment, CommentType } from 'src/project/entities/comment.entity';
import { DocumentChunk } from 'src/document/entities/document-chunk.entity';
import {
  extractCitationsFromReportTextGeneration,
  trimHtmlPreAndPostfix,
} from 'src/util/llm-response-util';
import { UsersService } from 'src/users/users.service';
import { User } from 'src/users/entities/user.entity';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { DatapointDocumentChunk } from 'src/datapoint-document-chunk/entities/datapoint-document-chunk.entity';
import { MDRPromptService } from 'src/prompts/mdr-prompts.service';
import { NumericsPromptService } from 'src/prompts/numerics-prompts.service';
import { NormalDpPromptService } from 'src/prompts/datapoint-generation-prompts.service';
import { TablePromptService } from 'src/prompts/table-prompts.service';
import { ESRSDatapoint } from './entities/esrs-datapoint.entity';
import { Document } from 'src/document/entities/document.entity';
import { ESRSTopic } from 'src/knowledge-base/entities/esrs-topic.entity';
import {
  getGenerationIdFromRequestId,
  isRequestForDataGenerationType,
  isTextPresentInHTML,
} from 'src/util/common-util';
import { Role } from 'src/users/entities/user-workspace.entity';
import {
  DatapointGeneration,
  datapointGenerationStatus,
} from './entities/datapoint-generation.entity';
import { ESRSTopicDatapoint } from 'src/knowledge-base/entities/esrs-topic-datapoint.entity';
import { LLM_MODELS } from 'src/constants';
import { LlmRateLimiterService } from 'src/llm-rate-limiter/llm-rate-limiter.service';

@Injectable()
export class DatapointRequestService {
  constructor(
    @InjectRepository(DatapointRequest)
    private readonly datapointRequestRepository: Repository<DatapointRequest>,
    @InjectRepository(DatapointDocumentChunk)
    private readonly datapointDocumentChunkMapRepository: Repository<DatapointDocumentChunk>,
    @InjectRepository(ESRSDatapoint)
    private readonly esrsDatapointRepository: Repository<ESRSDatapoint>,
    @InjectRepository(ESRSTopicDatapoint)
    private readonly esrsTopicDatapointRepository: Repository<ESRSTopicDatapoint>,
    @InjectRepository(DatapointGeneration)
    private readonly datapointGenerationRepository: Repository<DatapointGeneration>,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
    private readonly promptService: PromptService,
    private readonly mdrPromptService: MDRPromptService,
    private readonly NormalDpPromptService: NormalDpPromptService,
    private readonly tablePromptService: TablePromptService,
    private readonly datapointPromptService: NumericsPromptService,
    private readonly projectService: ProjectService,
    private readonly userService: UsersService,
    private readonly workspaceService: WorkspaceService,
    private readonly llmRateLimiterService: LlmRateLimiterService
  ) {}

  private readonly logger = new Logger(DatapointRequestService.name);
  private readonly MAX_DATAPOINT_GENERATION_DOCUMENT_CHUNKS_LENGHT = 300000; // Characters. This is NOT TOKENS
  private readonly THRESHOLD_DOCUMENT_CHUNK_LENGTH_UNTIL_REDUCING = 3000; // Characters. This is NOT TOKENS
  private readonly MAX_CHATCONTENT_MESSAGE_LENGTH = 300000; // Characters. This is NOT TOKENS
  private isNumericDataPoint(dataType?: string) {
    if (!dataType) {
      return false;
    }
    // prettier-ignore
    const numericDataTypes = [
      'monetary', 'gyear', 'date', 'ghgemissions', 'energy', 'intensity', 'integer', 'decimal', 'volume', 'area', 'percent', 'mass'
    ].map((type) => type.toLowerCase());
    const dataTypeList = dataType.split('/');
    return dataTypeList.some((type) =>
      numericDataTypes.includes(type.toLowerCase())
    );
  }

  async findById(datapointRequestId: string) {
    const datapointRequest = await this.datapointRequestRepository.findOne({
      where: { id: datapointRequestId },
      relations: {
        esrsDatapoint: true,
      },
    });
    if (!datapointRequest) {
      throw new NotFoundException(
        `Datapoint with ID ${datapointRequest} not found`
      );
    }

    return datapointRequest;
  }

  async findDatapointWithGenerationsById(datapointRequestId: string) {
    const relations = {
      esrsDatapoint: true,
      datapointGenerations: true,
      commentGenerations: true,
      comments: {
        user: true,
      },
      datapointDocumentChunkMap: {
        documentChunk: {
          document: true,
        },
      },
    };
    return await this.datapointRequestRepository.findOne({
      where: { id: datapointRequestId },
      relations,
      order: {
        esrsDatapointId: 'ASC',
        comments: {
          createdAt: 'ASC',
        },
      },
    });
  }

  async findDatapointGenerationWithId(datapointGenerationId: string) {
    return await this.datapointGenerationRepository.findOne({
      where: { id: datapointGenerationId },
      relations: ['datapointRequest', 'evaluator'],
    });
  }

  async findAllDataPointRequests(
    dataRequestId: string,
    userId: string
  ): Promise<DatapointRequestWithDocumentCount[]> {
    const relations = {
      esrsDatapoint: true,
      comments: {
        user: true,
      },
    };
    const isSuperAdmin = await this.userService.userHasRequiredRole(userId, [
      Role.SuperAdmin,
    ]);
    const isAiContributor = await this.userService.userHasRequiredRole(userId, [
      Role.AiContributor,
    ]);
    if (isSuperAdmin) {
      relations['datapointGenerations'] = {
        evaluator: true,
      };
    }
    if (isSuperAdmin || isAiContributor) {
      relations['commentGenerations'] = true;
    }
    const datapoints = await this.datapointRequestRepository.find({
      where: { dataRequestId },
      relations,
      order: {
        esrsDatapointId: 'ASC',
        comments: {
          createdAt: 'ASC',
        },
      },
    });

    const datapointsWithDocumentCount: DatapointRequestWithDocumentCount[] = [];
    for (const datapoint of datapoints) {
      try {
        const count = await this.datapointDocumentChunkMapRepository.count({
          where: {
            datapointRequestId: datapoint.id,
            active: true,
            documentChunk: {
              document: {
                id: Not(IsNull()),
              },
            },
          },
          relations: ['documentChunk.document'],
        });

        datapointsWithDocumentCount.push({
          ...datapoint,
          documentChunkCount: count,
        });
      } catch (e) {
        this.logger.error('Error while counting document chunks', e);
      }
    }
    return datapointsWithDocumentCount;
  }

  async loadMaterialTopics(datapointRequestId: string): Promise<any> {
    const datapointRequest = await this.findData(datapointRequestId);
    const { id } = datapointRequest.esrsDatapoint;
    const projectId = datapointRequest.dataRequest.projectId;
    const esrsTopicDatapoint: ESRSTopicDatapoint[] =
      await this.esrsTopicDatapointRepository.find({
        where: { esrsDatapointId: id },
        relations: ['topic'],
      });
    const validMaterialTopics = await this.generateHierarchicalListOfTopics({
      topicRelations: esrsTopicDatapoint,
      projectId,
      material: true,
    });
    return validMaterialTopics;
  }

  async findData(datapointRequestId: string): Promise<DatapointRequestData> {
    const datapointRequest = await this.datapointRequestRepository.findOne({
      where: { id: datapointRequestId },
      relations: [
        'esrsDatapoint',
        'dataRequest',
        'comments',
        'datapointDocumentChunkMap.documentChunk.document',
      ],
    });

    if (!datapointRequest) {
      throw new NotFoundException(
        `Datapoint with ID ${datapointRequest} not found`
      );
    }

    return datapointRequest;
  }

  async update({
    datapointRequestId,
    updateDatapointRequestPayload,
    userId,
    workspaceId,
    event = 'datapoint_request_updated',
  }: {
    datapointRequestId: string;
    updateDatapointRequestPayload: Partial<DatapointRequest>;
    userId?: string;
    workspaceId?: string;
    event?: string;
  }): Promise<DatapointRequest> {
    const datapointRequest = await this.findData(datapointRequestId);
    if (!datapointRequest) {
      throw new NotFoundException(`Datapoint Request not found`);
    }

    const definedPayload = Object.fromEntries(
      Object.entries(updateDatapointRequestPayload).filter(
        ([_, value]) => value !== undefined
      )
    );

    const dockedPayload = {
      ...datapointRequest,
      ...definedPayload,
    };

    const evaluatedStatus = this.datapointRequestStatusProcessor(dockedPayload);
    definedPayload.status = evaluatedStatus;

    await this.datapointRequestRepository.update(
      datapointRequestId,
      definedPayload
    );

    const datapoint = await this.findById(datapointRequestId);

    if (userId && workspaceId) {
      await this.workspaceService.storeActionHistory({
        event: event,
        ref: datapointRequestId,
        workspaceId: workspaceId,
        versionData: {
          event: event,
          doneBy: userId,
          data: datapoint,
        },
      });
    }
    return datapoint;
  }

  async generateHierarchicalListOfTopics({
    topicRelations,
    projectId,
    material,
  }: {
    topicRelations: ESRSDatapoint['topicRelations'];
    projectId: string;
    material: boolean;
  }) {
    const projectEsrsTopics = await this.projectService.getProjectEsrsTopics({
      projectId,
      esrsTopicIds: topicRelations.map((tr) => tr.topic.id),
    });

    // 1. Filter to get only the "matching" topics
    const filteredTopics = projectEsrsTopics.filter(
      (mt) => Boolean(mt.active) === material
    );
    const filteredTopicIds = new Set(
      filteredTopics.map((mt) => mt.esrsTopicId)
    );

    // 2. Build a map of ESRSTopic => only if it is in filteredTopicIds
    const topicsById = new Map<number, ESRSTopic>();
    for (const tr of topicRelations) {
      if (filteredTopicIds.has(tr.topic.id)) {
        topicsById.set(tr.topic.id, { ...tr.topic, children: [] });
      }
    }

    // 3. If the parent is missing forcibly nullify the parent so it can become top-level
    // In this case, anscestors are not included in the list
    for (const [id, topic] of topicsById.entries()) {
      if (topic.parentId && !topicsById.has(topic.parentId)) {
        topic.parentId = null;
      }
    }

    // 4. Build the children arrays
    for (const [id, topic] of topicsById.entries()) {
      if (topic.parentId && topicsById.has(topic.parentId)) {
        topicsById.get(topic.parentId).children.push(topic);
      }
    }

    // 5. Pick only roots for the return array
    const hierarchicalTopics = [...topicsById.values()].filter(
      (t) => !t.parentId
    );

    return hierarchicalTopics;
  }

  async reviewDatapointContentWithAI({
    datapointRequestId,
    userId,
    workspaceId,
  }: {
    datapointRequestId: string;
    userId: string;
    workspaceId: string;
  }): Promise<Comment[]> {
    const isAIEvaluator = await this.userService.userHasRequiredRole(userId, [
      Role.SuperAdmin,
    ]);

    const datapointRequest = await this.datapointRequestRepository.findOne({
      where: {
        id: datapointRequestId,
        status: Not(DatapointRequestStatus.NotAnswered),
      },
      relations: {
        dataRequest: { project: true },
        esrsDatapoint: {
          topicRelations: {
            topic: true,
          },
        },
        comments: true,
      },
    });

    const { generalCompanyProfile } =
      await this.workspaceService.getCompanyDetailFromWorkspaceId(workspaceId);

    // Build prompt
    this.logger.log(
      `Start Gap Analysis for datapointRequest ${datapointRequest.id}`
    );

    const otherDatapoints = await this.esrsDatapointRepository.find({
      where: {
        esrsDisclosureRequirementId:
          datapointRequest.esrsDatapoint.esrsDisclosureRequirementId,
        id: Not(datapointRequest.esrsDatapoint.id),
      },
    });

    let datapointRequestGapAnalysisChatCompletion: ChatCompletionMessageParam[] =
      [];
    const esrsDatapoint = datapointRequest.esrsDatapoint;
    const h2Count = (datapointRequest.content.match(/<\/h2>/g) || []).length;
    if (esrsDatapoint.datapointId.includes('MDR') && h2Count > 0) {
      this.logger.log(
        `MDR Datapoint identified: ${esrsDatapoint.datapointId} with ${h2Count} policies`
      );
      datapointRequestGapAnalysisChatCompletion = [
        {
          role: 'system',
          content:
            this.mdrPromptService.indentifyMDRandGenerateGapAnalysisSystemPrompt(
              {
                esrsDatapoint,
                language:
                  datapointRequest.dataRequest.project.primaryContentLanguage,
              }
            ),
        },
        {
          role: 'user',
          content:
            this.promptService.generateDatapointGapAnalysisContentContext(
              datapointRequest.content
            ),
        },
      ];
    } else {
      datapointRequestGapAnalysisChatCompletion = [
        {
          role: 'system',
          content: this.promptService.generateDatapointGapAnalysisSystemPrompt1(
            {
              esrsDatapoint,
              generationLanguage:
                datapointRequest.dataRequest.project.primaryContentLanguage,
            }
          ),
        },
        // TODO: Attach all the gaps that were already analysed and are still not resolved, so they are not added multiple times
        {
          role: 'system',
          content:
            this.promptService.generateDatapointGapAnalysisDatapointSpecificSystemPrompt(
              esrsDatapoint,
              otherDatapoints
            ),
        },
        {
          role: 'user',
          content:
            this.promptService.generateDatapointGapAnalysisContentContext(
              datapointRequest.content
            ),
        },
      ];
    }

    if (esrsDatapoint.conditional) {
      datapointRequestGapAnalysisChatCompletion.push({
        role: 'system',
        content: `This is a conditional datapoint. If there is information missing, it is up to the user whether they are ok with a gap, or not. The conditions typically have "if relevant" or similar conditionals in the law-text or application-requirements. Explicitly mention in this gap analysis whether the condition is met or not met.`,
      });
    }

    const topicRelations = datapointRequest.esrsDatapoint.topicRelations;
    const materialTopicsInHierarchy =
      await this.generateHierarchicalListOfTopics({
        topicRelations,
        projectId: datapointRequest.dataRequest.project.id,
        material: true,
      });

    if (materialTopicsInHierarchy.length > 0) {
      datapointRequestGapAnalysisChatCompletion.push({
        role: 'system',
        content:
          this.promptService.datapointRequestGapAnalysisSystemPromptMaterialTopics(
            { topics: materialTopicsInHierarchy, material: true }
          ),
      });

      const nonMaterialTopicsInHierarchy =
        await this.generateHierarchicalListOfTopics({
          topicRelations,
          projectId: datapointRequest.dataRequest.project.id,
          material: false,
        });

      if (nonMaterialTopicsInHierarchy.length > 0) {
        datapointRequestGapAnalysisChatCompletion.push({
          role: 'system',
          content:
            this.promptService.datapointRequestGapAnalysisSystemPromptMaterialTopics(
              { topics: nonMaterialTopicsInHierarchy, material: false }
            ),
        });
      }
    }

    datapointRequestGapAnalysisChatCompletion.push({
      role: 'system',
      content: this.promptService.generateDatapointGapAnalysisSystemPrompt2({
        esrsDatapoint,
        generationLanguage:
          datapointRequest.dataRequest.project.primaryContentLanguage,
        reportTextGenerationRules:
          datapointRequest.dataRequest.project.reportTextGenerationRules,
        generalCompanyProfile,
        reportingYear: datapointRequest.dataRequest.project.reportingYear,
      }),
    });

    const gapAnalysisCompletionResponse =
      await this.llmRateLimiterService.handleRequest({
        model: process.env.GA_MODEL as LLM_MODELS,
        messages: datapointRequestGapAnalysisChatCompletion,
        json: true,
        temperature: 0.3,
      });

    if (gapAnalysisCompletionResponse.status === 400) {
      throw new Error(gapAnalysisCompletionResponse.response);
    }

    // Save comment
    this.logger.log(
      `Gap Analysis Tokens: ${JSON.stringify(gapAnalysisCompletionResponse.token)}`
    );

    type GapInformation = {
      gap?: string;
      actions?: string[];
      exampleText?: string;
      text?: string;
      disclaimer?: string;
      title?: string;
    };

    const gapAnalysis: GapInformation & {
      gapIdentified: boolean;
      gaps?: GapInformation[];
    } = gapAnalysisCompletionResponse.response;

    //replace ```html if the response escaped the html
    // if (gapAnalysis) {
    //   gapAnalysis = gapAnalysis.replace(`\`\`\`html`, '');
    //   gapAnalysis = gapAnalysis.replace(`\`\`\``, '');
    // }

    const globalAIUser: User = await this.userService.findGlobalGlacierAIUser();

    const comments: Comment[] = [];
    let gapHtml = '';

    if (
      gapAnalysis.gapIdentified &&
      gapAnalysis.gaps &&
      gapAnalysis.gaps.length > 0
    ) {
      for (const gap of gapAnalysis.gaps) {
        // prettier-ignore
        gapHtml =
          (gap.title ? "<h3> " + gap.title + "</h3>" : "") +
          "<p><strong>Gap Identified:</strong> " + gap.gap + "</p>" +
          "<p><strong>Recommended Actions:</strong></p>" +
          (gap.actions ?
            "<ul>" +
            gap.actions.map(function (action) {
              return "<li>" + action + "</li>";
            }).join('') +
            "</ul>" :
            "") +
          "<p><strong>Example Text:</strong>" + gap.exampleText + "</p>" +
          (gap.disclaimer ? "<p><strong>Disclaimer:</strong> " + gap.disclaimer + "</p>" : "");

        const comment = await this.projectService.addComment({
          commentableId: datapointRequestId,
          commentableType: CommentType.DatapointRequest,
          userId: globalAIUser.id,
          comment: gapHtml,
          workspaceId,
          evaluationLot: isAIEvaluator,
        });

        comments.push(comment);
      }
      // TODO: need to validate and combine both if conditions to one
    } else if (gapAnalysis.gapIdentified) {
      // prettier-ignore
      gapHtml =
        (gapAnalysis.title ? "<h3> " + gapAnalysis.title + "</h3>" : "") +
        "<p><strong>Gap Identified:</strong> " + gapAnalysis.gap + "</p>" +
        "<p><strong>Recommended Actions:</strong></p>" +
        (gapAnalysis.actions ?
          "<ul>" +
          gapAnalysis.actions.map(function (action) {
            return "<li>" + action + "</li>";
          }).join('') +
          "</ul>" :
          "") +
        "<p><strong>Example Text:</strong>" + gapAnalysis.exampleText + "</p>" +
        (gapAnalysis.disclaimer ? "<p><strong>Disclaimer:</strong> " + gapAnalysis.disclaimer + "</p>" : "");

      const comment = await this.projectService.addComment({
        commentableId: datapointRequestId,
        commentableType: CommentType.DatapointRequest,
        userId: globalAIUser.id,
        comment: gapHtml,
        workspaceId,
        evaluationLot: isAIEvaluator,
      });

      comments.push(comment);
    } else {
      // prettier-ignore
      gapHtml =
        "<p><strong>No Gap Identified:</strong></p>" +
        "<p>" + gapAnalysis.text + "</p>";

      const comment = await this.projectService.addComment({
        commentableId: datapointRequestId,
        commentableType: CommentType.DatapointRequest,
        userId: globalAIUser.id,
        comment: gapHtml,
        workspaceId,
        evaluationLot: isAIEvaluator,
      });

      comments.push(comment);
    }
    if (datapointRequest.queueStatus === DatapointQueueStatus.QueuedForReview) {
      await this.updateQueueStatus({
        datapointRequestId,
        queueStatus: null,
      });
    }
    return comments;
  }

  async validateDatapointRequestGenerationRightsOrFail({
    datapointRequest,
    userId,
  }: {
    datapointRequest: DatapointRequest;
    userId: string;
  }): Promise<boolean> {
    if (!datapointRequest.esrsDatapoint.publicAccess) {
      await this.userService.userHasRequiredRoleOrFail({
        userOrId: userId,
        role: [Role.SuperAdmin, Role.AiContributor],
        message: 'Datapoint generation is not enabled for this request.',
      });
    }

    return true;
  }

  async generateDatapointContentWithAI({
    datapointRequestId,
    userId,
    workspaceId,
    useExistingReportTextForReference,
  }: {
    datapointRequestId: string;
    userId: string;
    workspaceId: string;
    useExistingReportTextForReference: boolean;
  }): Promise<void> {
    const datapointRequest = await this.datapointRequestRepository.findOne({
      where: { id: datapointRequestId },
      relations: {
        dataRequest: { project: true },
        esrsDatapoint: {
          esrsDisclosureRequirement: true,
          topicRelations: {
            topic: true,
          },
        },
        datapointDocumentChunkMap: {
          documentChunk: { document: true },
        },
      },
    });
    const { generalCompanyProfile } =
      await this.workspaceService.getCompanyDetailFromWorkspaceId(workspaceId);
    const relatedDatapoints = await this.datapointRequestRepository.find({
      where: {
        dataRequestId: datapointRequest.dataRequestId,
        id: Not(datapointRequest.id),
        content: Not(''),
      },
      relations: ['esrsDatapoint'],
    });

    this.logger.log(
      `Start Generating datapointRequest ${datapointRequest.id} with AI`
    );

    const otherDatapoints = await this.esrsDatapointRepository.find({
      where: {
        esrsDisclosureRequirementId:
          datapointRequest.esrsDatapoint.esrsDisclosureRequirementId,
        id: Not(datapointRequest.esrsDatapoint.id),
      },
    });

    //Generate Context from DocumentLinks
    const {
      context: datapointGenerationContextFromLinkedDocumentChunks,
      documentChunksIndex,
    } =
      await this.generateDatapointGenerationContextFromLinkedDocumentChunks(
        datapointRequest
      );
    //Generate Prompt based on Requirements
    let datapointGenerationChatCompletion: ChatCompletionMessageParam[] = [];

    const topicRelations = datapointRequest.esrsDatapoint.topicRelations;
    const materialTopicsInHierarchy =
      await this.generateHierarchicalListOfTopics({
        topicRelations,
        projectId: datapointRequest.dataRequest.project.id,
        material: true,
      });

    const nonMaterialTopicsInHierarchy =
      await this.generateHierarchicalListOfTopics({
        topicRelations,
        projectId: datapointRequest.dataRequest.project.id,
        material: false,
      });

    const esrsDatapoint = datapointRequest.esrsDatapoint;
    const h2Count = (datapointRequest.content.match(/<\/h2>/g) || []).length;
    const isNumeric = this.isNumericDataPoint(esrsDatapoint.dataType);
    const isTabular = esrsDatapoint.dataType?.includes('table');

    if (esrsDatapoint.datapointId.includes('MDR') && h2Count > 0) {
      this.logger.log(
        `MDR Datapoint identified: ${esrsDatapoint.datapointId} with ${h2Count} policies`
      );

      datapointGenerationChatCompletion = [
        {
          role: 'system',
          content:
            this.mdrPromptService.indentifyMDRContentGenerationMainPrompt({
              // instead of separate roles, all in one prompt. Shouldn't make a big difference
              esrsDatapoint,
              datapointGenerationContextFromLinkedDocumentChunks,
              language:
                datapointRequest.dataRequest.project.primaryContentLanguage,
              reportTextGenerationRules:
                datapointRequest.dataRequest.project.reportTextGenerationRules,
              generalCompanyProfile,
              reportingYear: datapointRequest.dataRequest.project.reportingYear,
              customUserRemark: datapointRequest.customUserRemark,
            }),
        },
        //{
        //  role: 'system',
        //  content: this.mdrPromptService.generateCitationPrompt(),
        //},
      ];
    } else if (isTabular) {
      this.logger.log(
        `Datapoint identified: ${esrsDatapoint.datapointId}${
          isTabular ? ' (Tabular)' : isNumeric ? ' (Numeric)' : 'Other'
        }`
      );
      // has this been in use so far?
      const exampleOutput: string =
        esrsDatapoint?.exampleOutput?.trim() !== ''
          ? esrsDatapoint.exampleOutput
          : 'no example provided, infer from context';

      datapointGenerationChatCompletion = [
        {
          role: 'system',
          content: this.tablePromptService.generateTableSystemPrompt({
            esrsDatapoints: [esrsDatapoint],
            generationLanguage:
              datapointRequest.dataRequest.project.primaryContentLanguage,
            reportTextGenerationRules:
              datapointRequest.dataRequest.project.reportTextGenerationRules,
            customUserRemark: datapointRequest.customUserRemark,
            currentContent: datapointRequest.content,
            linkedChunks: datapointGenerationContextFromLinkedDocumentChunks,
            otherDatapoints,
            reportingYear: datapointRequest.dataRequest.project.reportingYear,
            generalCompanyProfile,
          }),
        },
      ];
    } else if (isNumeric) {
      this.logger.log(
        `Datapoint identified: ${esrsDatapoint.datapointId}${
          isTabular ? ' (Tabular)' : isNumeric ? ' (Numeric)' : 'Other'
        }`
      );
      datapointGenerationChatCompletion = [
        {
          role: 'system',
          content:
            this.datapointPromptService.generateNumericDatapointContentGenerationSystemPrompt(
              {
                esrsDatapoint: esrsDatapoint,
                generationLanguage:
                  datapointRequest.dataRequest.project.primaryContentLanguage,
                reportTextGenerationRules:
                  datapointRequest.dataRequest.project
                    .reportTextGenerationRules,
                customUserRemark: datapointRequest.customUserRemark,
                currentContent: datapointRequest.content,
                generalCompanyProfile,
                otherDatapoints: otherDatapoints,
                reportingYear:
                  datapointRequest.dataRequest.project.reportingYear,
                linkedChunks:
                  datapointGenerationContextFromLinkedDocumentChunks,
              }
            ),
        },
      ];
    } else {
      // no MDR, not numeric
      datapointGenerationChatCompletion = [
        {
          role: 'system',
          content:
            this.NormalDpPromptService.generateDatapointContentGenerationSystemPrompt(
              {
                esrsDatapoint: esrsDatapoint,
                generationLanguage:
                  datapointRequest.dataRequest.project.primaryContentLanguage,
                reportTextGenerationRules:
                  datapointRequest.dataRequest.project
                    .reportTextGenerationRules,
                customUserRemark: datapointRequest.customUserRemark,
                currentContent: datapointRequest.content,
                otherDatapoints: otherDatapoints,
                reportingYear:
                  datapointRequest.dataRequest.project.reportingYear,
                generalCompanyProfile,
                linkedChunks:
                  datapointGenerationContextFromLinkedDocumentChunks,
              }
            ),
        },
      ];
    }

    if (materialTopicsInHierarchy.length > 0) {
      datapointGenerationChatCompletion.push({
        role: 'system',
        content:
          this.promptService.datapointRequestContentGenerationSystemPromptMaterialTopics(
            { topics: materialTopicsInHierarchy, material: true }
          ),
      });

      if (nonMaterialTopicsInHierarchy.length > 0) {
        datapointGenerationChatCompletion.push({
          role: 'system',
          content:
            this.promptService.datapointRequestContentGenerationSystemPromptMaterialTopics(
              { topics: nonMaterialTopicsInHierarchy, material: false }
            ),
        });
      }
    }

    if (useExistingReportTextForReference) {
      datapointGenerationChatCompletion.push({
        role: 'system',
        content: `Following is the existing report text that was previously generated, user wishes to extend upon this:\n ${datapointRequest.content}`,
      });
    }

    if (esrsDatapoint.conditional) {
      datapointGenerationChatCompletion.push({
        role: 'system',
        content: `This is a conditional datapoint. This means that information described in the law text might not be neccessary to disclose, if the conditions aren't met. The conditions typically have "if relevant" or similar conditionals in the phrasing of law-text or application-requirements. It is up to the user to decide whether they are ok with a gap, or not.  Thus for apparent gaps here, explicitly mention in this‚ gap analysis whether the condition is met or not met.`,
      });
    }

    if (relatedDatapoints.length > 0) {
      datapointGenerationChatCompletion.push({
        role: 'system',
        content:
          this.promptService.datapointRequestContentGenerationSystemPromptRelatedDatapoints(
            relatedDatapoints
          ),
      });
    }

    const predatapointGenerationChatCompletionResponse =
      await this.llmRateLimiterService.handleRequest({
        model: LLM_MODELS.o3,
        messages: datapointGenerationChatCompletion,
        json: true, //json true and do corresponding handling for 1) desc 2) gap analysis 3) desc 2
        temperature: 0,
      });
    if (predatapointGenerationChatCompletionResponse.status === 400) {
      throw new Error(predatapointGenerationChatCompletionResponse.response);
    }

    let datapointGenerationChatCompletionResponse;

    // if not esrsDatapoint.datapointId.includes('MDR')
    if (!esrsDatapoint.datapointId.includes('MDR')) {
      datapointGenerationChatCompletionResponse =
        predatapointGenerationChatCompletionResponse;
    } else {
      let improvingFormattingPrompt: ChatCompletionMessageParam[] = [];
      improvingFormattingPrompt = [
        {
          role: 'system',
          content: this.NormalDpPromptService.improvingFormattingPrompt({
            esrsDatapoint: esrsDatapoint,
            generationLanguage:
              datapointRequest.dataRequest.project.primaryContentLanguage,
            reportTextGenerationRules:
              datapointRequest.dataRequest.project.reportTextGenerationRules,
            customUserRemark: datapointRequest.customUserRemark,
            generalCompanyProfile,
            currentContent: datapointRequest.content,
            reportingYear: datapointRequest.dataRequest.project.reportingYear,
            predatapointGenerationChatCompletionResponse:
              predatapointGenerationChatCompletionResponse.response[
                'datapoint'
              ],
          }),
        },
      ];

      datapointGenerationChatCompletionResponse =
        await this.llmRateLimiterService.handleRequest({
          model: LLM_MODELS['o3'],
          messages: improvingFormattingPrompt,
          json: true,
          temperature: 0,
        });
    }

    if (datapointGenerationChatCompletionResponse.status === 400) {
      throw new Error(datapointGenerationChatCompletionResponse.response);
    }

    if (
      datapointGenerationChatCompletionResponse.response[
        'datapoint'
      ].startsWith('<br>')
    ) {
      datapointGenerationChatCompletionResponse.response['datapoint'] =
        datapointGenerationChatCompletionResponse.response['datapoint'].slice(
          4
        );
    }

    let { reportText: generatedContent, citation: generatedCitation } =
      extractCitationsFromReportTextGeneration(
        datapointGenerationChatCompletionResponse.response['datapoint'],
        documentChunksIndex
      );

    if (datapointGenerationChatCompletionResponse.response['citation']) {
      const citation =
        datapointGenerationChatCompletionResponse.response['citation'];
      generatedCitation = { ...generatedCitation, ...citation };
    }

    generatedContent = trimHtmlPreAndPostfix(generatedContent);

    datapointRequest.content = generatedContent;
    let datapointMetadata;
    if (datapointRequest.metadata) {
      datapointMetadata = JSON.parse(datapointRequest.metadata);
      datapointMetadata.citation = generatedCitation;
      datapointMetadata = JSON.stringify(datapointMetadata);
    } else {
      datapointMetadata = JSON.stringify({
        citation: generatedCitation,
      });
    }
    const isAIEvaluator = await this.userService.userHasRequiredRole(userId, [
      Role.SuperAdmin,
    ]);

    //Since we use the same function for both superadmin and non-superadmin users, we need to create these optional proerties
    const generatedDataResponse: {
      id: string;
      content: string;
      metadata: string;
    } = {
      content: generatedContent,
      metadata: datapointMetadata,
      id: datapointRequestId,
    };
    if (isAIEvaluator) {
      const dataGeneration = await this.datapointGenerationRepository.create({
        data: { content: generatedContent, metadata: datapointMetadata },
        datapointRequest: datapointRequest,
        evaluatorId: userId,
      });
      await this.datapointGenerationRepository.save(dataGeneration);
      generatedDataResponse.id = dataGeneration.id;
    } else {
      datapointRequest.metadata = datapointMetadata;
      datapointRequest.content = generatedContent;
      await this.datapointRequestRepository.save(datapointRequest);
    }

    //TODO: This should be passed as a success callback to the queue service
    await this.updateQueueStatus({
      datapointRequestId,
      queueStatus: null,
    });

    // comment this out if you don't find the preGapAnalysis useful
    //if (datapointGenerationChatCompletionResponse.response['preGapAnalysis']) {
    //  const globalAIUser: User =
    //    await this.userService.findGlobalGlacierAIUser();

    //  await this.projectService.addComment({
    //    commentableId: datapointRequestId,
    //    commentableType: CommentType.DatapointRequest,
    //    userId: globalAIUser.id,
    //    comment:
    //      datapointGenerationChatCompletionResponse.response['preGapAnalysis'],
    //    workspaceId,
    //  });
    //}

    this.logger.log(
      `Finished Generating datapointRequest ${datapointRequest.id} with AI`
    );

    const globalAIUser: User = await this.userService.findGlobalGlacierAIUser();

    await this.workspaceService.storeActionHistory({
      event: 'datapoint_request_ai_generated',
      ref: datapointRequestId,
      workspaceId: workspaceId,
      versionData: {
        event: 'datapoint_request_ai_generated',
        doneBy: globalAIUser.id,
        issuedBy: userId,
        data: datapointRequest,
      },
    });
  }

  async loadDatapointCitations(datapointRequestId: string, citationId: string) {
    let citations: any;
    if (isRequestForDataGenerationType(datapointRequestId)) {
      const generationData = await this.findDatapointGenerationWithId(
        getGenerationIdFromRequestId(datapointRequestId)
      );
      const metadata = JSON.parse(generationData.data.metadata);
      citations = metadata ? (metadata.citation?.[citationId] ?? []) : [];
    } else {
      const datapointRequest = await this.datapointRequestRepository.findOne({
        where: { id: datapointRequestId },
      });
      citations = datapointRequest.metadata
        ? (JSON.parse(datapointRequest.metadata).citation?.[citationId] ?? [])
        : [];
    }

    const documentChunks = await this.datapointDocumentChunkMapRepository.find({
      where: {
        documentChunkId: In(citations.map((citation) => citation.id)),
      },
      relations: ['documentChunk.document'],
    });

    return citations.map((citation) => {
      const documentChunkMaps = documentChunks.find(
        (dc) => dc.documentChunk.id === citation.id
      );
      return {
        ...citation,
        ...documentChunkMaps.documentChunk,
      };
    });
  }

  async updateContentAndReplaceCitation({
    datapointRequestId,
    citationId,
    index,
    workspaceId,
    userId,
  }: {
    datapointRequestId: string;
    citationId: string;
    index: number;
    workspaceId: string;
    userId: string;
  }) {
    const datapointRequest = await this.datapointRequestRepository.findOne({
      where: { id: datapointRequestId },
    });

    if (!datapointRequest || !datapointRequest.metadata) {
      throw new Error(
        `Datapoint request with ID ${datapointRequestId} not found`
      );
    }

    const metadata = JSON.parse(datapointRequest.metadata);
    metadata.citation = metadata.citation || {};
    const citations = metadata.citation[citationId] || [];

    const currentCitationIndex = citations.findIndex(
      (citation) => citation.active
    );
    if (!citations[currentCitationIndex]) {
      throw new Error(`No active citation found for citationId ${citationId}`);
    }

    const newCitationIndex = index;
    if (!citations[newCitationIndex]) {
      throw new Error(
        `Citation index ${newCitationIndex} is invalid for citationId ${citationId}`
      );
    }

    const replacementValue = citations[newCitationIndex].value;
    const activeCitationValue = citations[currentCitationIndex].value;

    const regexMarkdown = new RegExp(`"${activeCitationValue}"`, 'g');
    const regexHtml = new RegExp(`>${activeCitationValue}<`, 'g');
    datapointRequest.content = datapointRequest.content
      .replace(regexMarkdown, `"${replacementValue}"`)
      .replace(regexHtml, `>${replacementValue}<`);

    citations[currentCitationIndex].active = false;
    citations[newCitationIndex].active = true;
    metadata.citation[citationId] = citations;
    datapointRequest.metadata = JSON.stringify(metadata);

    await this.datapointRequestRepository.save(datapointRequest);

    await this.workspaceService.storeActionHistory({
      event: 'datapoint_request_citation_replaced',
      ref: datapointRequestId,
      workspaceId: workspaceId,
      versionData: {
        event: 'datapoint_request_citation_replaced',
        doneBy: userId,
        data: datapointRequest,
      },
    });

    return datapointRequest;
  }

  async generateDatapointGenerationContextFromLinkedDocumentChunks(
    datapointRequest: DatapointRequest
  ): Promise<{
    context: string;
    documentChunksIndex: string[];
  }> {
    const documentTypePriority: { [key: string]: number } = {
      'Business Report': 1,
      'Sustainability Report': 2,
      'Materiality Analysis': 3,
      Policy: 4,
      Strategy: 5,
      Other: 6,
    };

    this.logger.log(
      `Number of linked DocumentChunks: ${datapointRequest.datapointDocumentChunkMap.filter((map) => map.active).length}`
    );

    //Get all linked Document Original Chunks (shortening of them happens in the call)
    const documentChunks = datapointRequest.datapointDocumentChunkMap
      .filter((map) => map.active)
      .map((datapointDocumentChunk) => {
        return {
          ...datapointDocumentChunk.documentChunk,
          key_information: datapointDocumentChunk.key_information,
          documentTitle: datapointDocumentChunk.documentChunk.document.name,
          year: datapointDocumentChunk.documentChunk.document.year,
          month: datapointDocumentChunk.documentChunk.document.month,
          day: datapointDocumentChunk.documentChunk.document.day,
          documentType:
            datapointDocumentChunk.documentChunk.document.documentType,
          remarks: datapointDocumentChunk.documentChunk.document.remarks,
        };
      })
      .sort((a, b) => {
        // Compare by date (most recent first)
        const dateA = new Date(a.year || 0, (a.month || 1) - 1, a.day || 1);
        const dateB = new Date(b.year || 0, (b.month || 1) - 1, b.day || 1);

        if (dateB.getTime() !== dateA.getTime()) {
          return dateB.getTime() - dateA.getTime(); // Most recent first
        }

        // Compare by document type priority
        const typePriorityA = documentTypePriority[a.documentType] || 999;
        const typePriorityB = documentTypePriority[b.documentType] || 999;

        if (typePriorityA !== typePriorityB) {
          return typePriorityA - typePriorityB; // Lower priority value comes first
        }

        // Check for user remarks
        const hasRemarkA = a.remarks ? 1 : 0;
        const hasRemarkB = b.remarks ? 1 : 0;

        return hasRemarkB - hasRemarkA; // Chunks with remarks come first
      });

    const totalDocumentChunkLength = documentChunks.reduce(
      (acc, documentChunk) => acc + documentChunk.content.length,
      0
    );
    const numberOfLinkedDocumentChunks = documentChunks.length;

    this.logger.log(
      `${datapointRequest.id}: Total DocumentChunkLength ${totalDocumentChunkLength}, Number of LinkedChunks: ${numberOfLinkedDocumentChunks}`
    );

    const generateMetadataSection = async (
      documentChunk: DocumentChunk
    ): Promise<string> => {
      let metadata = '';

      if (documentChunk.metadataJson) {
        metadata += `<Metadata>\nPart 1: ${documentChunk.metadataJson} \nPart 2:`;
      } else {
        metadata += `<Metadata>\nNo Base Metadata`;
      }

      // Fetch the document using documentId
      if (documentChunk.documentId) {
        const document = await this.documentRepository.findOne({
          where: { id: documentChunk.documentId },
        });

        if (document) {
          const documentMetadata = [
            document.documentType && `Document Type: ${document.documentType}`,
            document.esrsCategory &&
              `ESRS Category: ${document.esrsCategory.join(', ')}`,
            document.year && `Year: ${document.year}`,
            document.month && `Month: ${document.month}`,
            document.day && `Day: ${document.day}`,
            document.remarks && `Remarks: ${document.remarks}`,
          ].filter(Boolean);

          if (documentMetadata.length > 0) {
            metadata += `\n\n${documentMetadata.join('\n')}`;
          }
        }
      }
      metadata += '\n</Metadata>\n\n';
      return metadata;
    };

    const documentChunkSplitter = '\n\n';
    let generatedContext = '<Context>\n\n';

    if (
      totalDocumentChunkLength >
      this.MAX_DATAPOINT_GENERATION_DOCUMENT_CHUNKS_LENGHT
    ) {
      const processedChunks = await Promise.all(
        documentChunks.map(async (documentChunk, index) => {
          const metadata = await generateMetadataSection(documentChunk);
          const chunkContent = `Document Chunk ID: chunk-${index + 1} \n Chunk Source:\n${metadata}\n`;

          if (
            documentChunk.key_information &&
            documentChunk.key_information !== 'false'
          ) {
            return chunkContent + documentChunk.key_information;
          } else if (
            documentChunk.content.length >
            this.THRESHOLD_DOCUMENT_CHUNK_LENGTH_UNTIL_REDUCING
          ) {
            return (
              chunkContent +
              (await this.reduceLinkedDocumentChunkContextToRelevantInformationOnly(
                datapointRequest,
                documentChunk
              ))
            );
          } else {
            return chunkContent + documentChunk.content;
          }
        })
      );

      generatedContext += processedChunks.join(documentChunkSplitter);
    } else {
      generatedContext += await Promise.all(
        documentChunks.map(async (documentChunk, index) => {
          const metadata = await generateMetadataSection(documentChunk);
          return `
          Document Chunk ID: chunk-${index + 1}
          Document: ${documentChunk.documentTitle} (${documentChunk.documentType}) - ${documentChunk.year}-${documentChunk.month}-${documentChunk.day}
          Page: ${documentChunk.page}:
          Metadata: ${metadata}
          --------------------------------------
          ${documentChunk.content}
          --------------------------------------`;
        })
      ).then((chunks) => chunks.join(documentChunkSplitter));
    }

    if (generatedContext.length > this.MAX_CHATCONTENT_MESSAGE_LENGTH) {
      this.logger.log(
        `${datapointRequest.id}: Generated context exceeds maximum chat content message length. Shortening context.`
      );
      generatedContext = generatedContext.slice(
        0,
        this.MAX_CHATCONTENT_MESSAGE_LENGTH
      );
    }

    generatedContext + '\n</Context>';

    return {
      context: generatedContext,
      documentChunksIndex: documentChunks.map((dc) => dc.id),
    };
  }

  async reduceLinkedDocumentChunkContextToRelevantInformationOnly(
    datapointRequest: DatapointRequest,
    documentChunk: DocumentChunk
  ) {
    this.logger.log(
      `${datapointRequest.id}: Reduce content of linked document Chunk ${documentChunk.id}, Length of document chunk: ${documentChunk.content.length}`
    );

    // TODO GENERATE PROMPT FOR REDUCTION OF DOCUMENT CHUNKS: https://www.notion.so/glaciereco/Prompt-CSRD-Detailed-f614fbfa9152451bb1671ebc847fd706?pvs=4#009887a74a1b4534afdc2a5f78553ed7

    const extractedChunkInformation: ChatCompletionMessageParam[] = [
      {
        role: 'user',
        content: this.promptService.reduceLinkedDocumentChunkPrompt(
          datapointRequest,
          documentChunk
        ),
      },
    ];

    type ReductionResponse = {
      keep_chunk_as_is: string;
      key_information: string;
    };

    const reductionResponse = await this.llmRateLimiterService.handleRequest({
      model: LLM_MODELS['gpt-4o'],
      messages: extractedChunkInformation,
      json: true,
      temperature: 0.2,
    });

    if (reductionResponse.status === 400) {
      throw new Error(reductionResponse.response);
    }

    const result = reductionResponse.response as ReductionResponse;

    // store the key information in the document chunk
    await this.datapointDocumentChunkMapRepository.update(
      {
        documentChunkId: documentChunk.id,
        datapointRequestId: datapointRequest.id,
      },
      {
        key_information:
          result.keep_chunk_as_is === 'false'
            ? 'false'
            : result.key_information,
      }
    );

    return result.keep_chunk_as_is === 'false'
      ? result.key_information
      : documentChunk.content;
  }

  datapointRequestStatusProcessor(
    datapointRequest: DatapointRequestData
  ): DatapointRequestStatus {
    const {
      content,
      datapointDocumentChunkMap: datapointDocumentChunkMaps,
      comments,
    } = datapointRequest;

    // "Not reported"
    // DP: If the Datapoint is marked as "Not Reported" → If the DR is turned to "Not reported", it is automatically turned off for all the Datapoints as well.
    if (datapointRequest.status === DatapointRequestStatus.NotAnswered) {
      return DatapointRequestStatus.NotAnswered;
    }

    // "No Data"
    // DP: Will be displayed red if there is no content and no document-chunks linked for the datapoint
    if (!isTextPresentInHTML(content)) {
      return DatapointRequestStatus.NoData;
    }

    // "Incomplete Data"
    // DP: Will be displayed if there is at least one unresolved comment and content is available
    const hasUnresolvedComments = comments.some((comment) => !comment.resolved);
    if (hasUnresolvedComments && content) {
      return DatapointRequestStatus.IncompleteData;
    }

    // "Complete Data"
    // DP: Will be displayed if there is saved content and no unresolved comments
    const noUnresolvedComments = comments.every((comment) => comment.resolved);
    if (content && noUnresolvedComments) {
      return DatapointRequestStatus.CompleteData;
    }

    // Default to No Data if no other conditions are met
    return DatapointRequestStatus.NoData;
  }

  async updateContentWithMDRText(
    datapointRequestId: string,
    mdrText: string
  ): Promise<void> {
    const datapointRequest = await this.findById(datapointRequestId);
    if (!datapointRequest) {
      throw new NotFoundException(`Datapoint Request not found`);
    }

    datapointRequest.content += `\n\n${mdrText}`;
    await this.datapointRequestRepository.save(datapointRequest);
  }

  async approveDatapointGeneration({ datapointRequestId }) {
    const dataGeneration = await this.datapointGenerationRepository.findOne({
      where: {
        datapointRequest: { id: datapointRequestId },
        status: datapointGenerationStatus.Pending,
      },
    });

    const datapointRequest = await this.datapointRequestRepository.findOne({
      where: { id: datapointRequestId },
    });

    datapointRequest.content = dataGeneration.data.content;
    datapointRequest.metadata = dataGeneration.data.metadata;

    await this.datapointGenerationRepository.update(datapointRequestId, {
      status: datapointGenerationStatus.Approved,
    });

    return datapointRequest;
  }

  async updateGenerationStatus({
    datapointGenerationId,
    status,
    userId,
    workspaceId,
  }: {
    datapointGenerationId: string;
    status: datapointGenerationStatus;
    userId: string;
    workspaceId: string;
  }) {
    try {
      let responseData: {
        content?: string;
        status: datapointGenerationStatus;
        evaluator?: User;
        evaluatedAt?: Date;
      } = { status }; // Same status goes back to the client
      await this.datapointGenerationRepository.update(
        {
          id: datapointGenerationId,
        },
        { status, evaluatorId: userId, evaluatedAt: new Date() }
      );
      const generation = await this.datapointGenerationRepository.findOne({
        where: { id: datapointGenerationId },
        relations: ['datapointRequest', 'evaluator'],
      });

      // Include evaluator information in response
      responseData.evaluator = generation.evaluator;
      responseData.evaluatedAt = generation.evaluatedAt;

      const existingDatapoint = await this.datapointRequestRepository.findOne({
        where: { id: generation.datapointRequest.id },
      });
      if (
        status === datapointGenerationStatus.Approved ||
        status === datapointGenerationStatus.MinorChanges
      ) {
        existingDatapoint.content = generation.data.content;
        existingDatapoint.metadata = generation.data.metadata;
        existingDatapoint.status = DatapointRequestStatus.CompleteData;
        await this.update({
          datapointRequestId: existingDatapoint.id,
          updateDatapointRequestPayload: existingDatapoint,
          userId,
          workspaceId,
        });
        responseData = { ...responseData, content: generation.data.content };
      }
      return responseData;
    } catch (err) {
      console.log(err);
      throw new Error(`Error while updating generation status: ${err.message}`);
    }
  }

  async findDatapointRequestsExcludingStatus({
    dataRequestId,
    status,
  }: {
    dataRequestId: string;
    status: DatapointRequestStatus[];
  }): Promise<DatapointRequest[]> {
    return await this.datapointRequestRepository.find({
      where: {
        dataRequestId,
        status: Not(In(status)),
      },
    });
  }

  async findDatapointRequestsByStatus({
    dataRequestId,
    status,
  }: {
    dataRequestId: string;
    status: DatapointRequestStatus[];
  }): Promise<DatapointRequest[]> {
    return await this.datapointRequestRepository.find({
      where: {
        dataRequestId,
        status: In(status),
      },
      relations: {
        datapointDocumentChunkMap: {
          documentChunk: true,
        },
        datapointGenerations: true,
      },
    });
  }

  async updateStatus({
    id,
    status,
  }: {
    id: string;
    status: DatapointRequestStatus;
  }) {
    await this.datapointRequestRepository.update({ id }, { status });
  }

  async updateQueueStatus({
    datapointRequestId,
    queueStatus,
  }: {
    datapointRequestId: string;
    queueStatus: DatapointQueueStatus | null;
  }) {
    await this.datapointRequestRepository.update(
      { id: datapointRequestId },
      { queueStatus }
    );
  }

  async loadDocumentLinks(datapointRequestId: string) {
    const documentLinks = await this.datapointDocumentChunkMapRepository.find({
      where: { datapointRequestId, active: true },
      relations: ['documentChunk', 'documentChunk.document'],
    });
    return documentLinks;
  }
}
