server:
  http_listen_port: 9080

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: container_logs
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        refresh_interval: 5s
    relabel_configs:
      - source_labels: ['__meta_docker_container_name']
        target_label: 'container'
      - source_labels: ['__meta_docker_container_log_stream']
        target_label: 'stream'
    pipeline_stages:
      - docker: {}
      - match:
          selector: '{container=~".*backend.*"}'
          stages:
            - regex:
                expression: '(?P<timestamp>\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z)\s+(?P<level>[A-Z]+)\s+(?P<context>\[[^\]]+\])\s+(?P<message>.*)'
            - labels:
                level:
                timestamp:
                context:
      - match:
          selector: '{container="redis"}'
          stages:
            - regex:
                expression: '(?P<timestamp>\d+:\d+:\d+.\d+)\s+(?P<level>[\.\*\#\-])\s+(?P<message>.*)'
            - labels:
                level:
                timestamp:
