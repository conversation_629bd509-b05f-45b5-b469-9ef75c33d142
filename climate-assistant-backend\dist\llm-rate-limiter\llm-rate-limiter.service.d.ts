import { Queue, Job } from 'bull';
import OpenAI from 'openai';
import { ChatGptService } from '../llm/chat-gpt.service';
import { LLM_MODELS } from 'src/constants';
interface HandleRequestProps {
    tokens?: number;
    model: LLM_MODELS;
    messages: OpenAI.Chat.ChatCompletionMessageParam[];
    temperature: number;
    json: boolean;
}
export declare class LlmRateLimiterService {
    private readonly llmQueue;
    private readonly chatGptService;
    private readonly logger;
    private lastResetTime;
    private modelUsage;
    private readonly modelLimits;
    constructor(llmQueue: Queue, chatGptService: ChatGptService);
    processLlmRequest(job: Job): Promise<any>;
    private exceedsTokenLimit;
    processJob(request: any): any;
    private resetTokensIfNeeded;
    private resetTokens;
    handleRequest(request: HandleRequestProps, priority?: number): Promise<any>;
    private canMakeRequest;
    private updateTokenUsage;
    private makeRequest;
}
export {};
