import { useMemo } from 'react';
import { CopyIcon } from 'lucide-react';
// import * as Accordion from '@radix-ui/react-accordion';

import { TipTapEditor } from '../ui/tiptap/tiptap-editor';
import { Button } from '../ui/button';
import {
  Accordion,
  AccordionItem,
  AccordionContent,
  AccordionTrigger,
} from '../ui/accordion';

import { generationStatus, IDataGenerations } from '@/types/project';

type GeneratedContentPropsType = {
  generationContents: IDataGenerations[];
  handleApproveOrReject: (id: string, status: generationStatus) => void;
};

export const GeneratedContent = ({
  generationContents,
  handleApproveOrReject,
}: GeneratedContentPropsType) => {
  const nonApprovedGenerationsCount = generationContents.filter(
    ({ status }) => status === generationStatus.pending
  ).length;

  const sortedGeneratedContent = useMemo(
    () =>
      generationContents.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      ),
    [generationContents]
  );

  return (
    <Accordion
      type="single"
      collapsible
      className="bg-slate-200 p-2 rounded-sm mt-4"
    >
      <AccordionItem value="generations">
        <AccordionTrigger className="text-left w-full">
          <h2 className="text-xl font-bold underline underline-offset-2">
            {nonApprovedGenerationsCount} / {generationContents.length}{' '}
            Generations to Review
          </h2>
        </AccordionTrigger>
        <AccordionContent>
          {sortedGeneratedContent.map(
            ({ id, status, data, createdAt, evaluator, evaluatedAt }: IDataGenerations) => {
              const isApproved = status === generationStatus.approved;
              const isMinorChanges = status === generationStatus.minorChanges;
              const isRejected = status === generationStatus.rejected;
              return (
                <div key={id} className="mt-8">
                  <h3 className="underline">
                    Generation ID: {id}
                    <CopyIcon
                      onClick={() => navigator.clipboard.writeText(id)}
                      className="h-5 w-5 inline ml-1 cursor-pointer"
                    />
                  </h3>
                  <p>
                    Generation Status:{' '}
                    <span
                      className={`font-bold uppercase ${isApproved || isMinorChanges ? 'text-glacier-greendark' : isRejected ? 'text-red-500' : ''}`}
                    >
                      {status}
                    </span>
                  </p>
                  <p>
                    Generation date:{' '}
                    <span className="font-bold">
                      {new Date(createdAt).toDateString()}
                    </span>
                  </p>
                  {evaluator && (
                    <p>
                      Author: <span className="font-bold">{evaluator.name}</span>
                    </p>
                  )}
                  {evaluatedAt && (
                    <p>
                      Evaluation date:{' '}
                      <span className="font-bold">
                        {new Date(evaluatedAt).toDateString()}
                      </span>
                    </p>
                  )}
                  <p>Generation Text:</p>
                  <TipTapEditor
                    refId={`${id}-generation`}
                    content={data.content}
                    setContent={() => { }}
                    isEditable={false}
                  />
                  <div className="mt-4 flex space-x-2">
                    <Button
                      variant="forest"
                      disabled={isApproved}
                      onClick={() =>
                        handleApproveOrReject(id, generationStatus.approved)
                      }
                    >
                      Approve
                    </Button>
                    <Button
                      variant="secondary"
                      disabled={isMinorChanges}
                      onClick={() =>
                        handleApproveOrReject(id, generationStatus.minorChanges)
                      }
                    >
                      Minor Changes
                    </Button>
                    <Button
                      variant="destructive"
                      disabled={isRejected}
                      onClick={() =>
                        handleApproveOrReject(id, generationStatus.rejected)
                      }
                    >
                      Reject
                    </Button>
                  </div>
                </div>
              );
            }
          )}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};
