// entities/datapoint_document_chunk.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  DeleteDateColumn,
  Column,
  Unique,
} from 'typeorm';
import { DatapointRequest } from '../../datapoint/entities/datapoint-request.entity';
import { DocumentChunk } from '../../document/entities/document-chunk.entity';
import { User } from '../../users/entities/user.entity';

@Entity()
@Unique(['documentChunkId', 'datapointRequestId'])
export class DatapointDocumentChunk {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('uuid')
  documentChunkId: string;

  @ManyToOne(() => DocumentChunk)
  @JoinColumn({ name: 'documentChunkId' })
  documentChunk: DocumentChunk;

  @Column('uuid', { nullable: true })
  datapointRequestId: string;

  @ManyToOne(() => DatapointRequest)
  @JoinColumn({ name: 'datapointRequestId' })
  datapointRequest: DatapointRequest;

  @Column({ type: 'text', nullable: true })
  key_information: string;

  @Column('uuid')
  createdBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'createdBy' })
  creator: User;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column('uuid', { nullable: true })
  modifiedBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'modifiedBy' })
  modifier: User;

  @Column({ default: true })
  active: boolean;
}
