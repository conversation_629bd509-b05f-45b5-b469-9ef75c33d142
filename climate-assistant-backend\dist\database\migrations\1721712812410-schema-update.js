"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1721712812410 = void 0;
class SchemaUpdate1721712812410 {
    constructor() {
        this.name = 'SchemaUpdate1721712812410';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "knowledge_base_file_upload_chunk" DROP CONSTRAINT "FK_d906b1920691f35fa454d766439"`);
        await queryRunner.query(`ALTER TABLE "knowledge_base_file_upload_chunk"
        ADD CONSTRAINT "FK_d906b1920691f35fa454d766439" FOREIGN KEY ("fileUploadId") REFERENCES "knowledge_base_file_upload" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "knowledge_base_file_upload_chunk" DROP CONSTRAINT "FK_d906b1920691f35fa454d766439"`);
        await queryRunner.query(`ALTER TABLE "knowledge_base_file_upload_chunk"
        ADD CONSTRAINT "FK_d906b1920691f35fa454d766439" FOREIGN KEY ("fileUploadId") REFERENCES "file_upload" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
}
exports.SchemaUpdate1721712812410 = SchemaUpdate1721712812410;
//# sourceMappingURL=1721712812410-schema-update.js.map