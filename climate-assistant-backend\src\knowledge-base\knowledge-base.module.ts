import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { KnowledgeBaseController } from './knowledge-base.controller';
import { KnowledgeBaseService } from './knowledge-base.service';
import { KnowledgeBaseFileUpload } from './entities/knowledge-base-file-upload.entity';
import { KnowledgeBaseFileUploadChunk } from './entities/knowledge-base-file-upload-chunk.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ChatGptService } from '../llm/chat-gpt.service';
import { ESRSDatapoint } from '../datapoint/entities/esrs-datapoint.entity';
import { ESRSDisclosureRequirement } from './entities/esrs-disclosure-requirement.entity';
import { ESRSTopic } from './entities/esrs-topic.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      KnowledgeBaseFileUpload,
      KnowledgeBaseFileUploadChunk,
      ESRSTopic,
      ESRSDatapoint,
      ESRSDisclosureRequirement,
    ]),
  ],
  controllers: [KnowledgeBaseController],
  providers: [KnowledgeBaseService, ChatGptService],
  exports: [KnowledgeBaseService],
})
export class KnowledgeBaseModule {}
