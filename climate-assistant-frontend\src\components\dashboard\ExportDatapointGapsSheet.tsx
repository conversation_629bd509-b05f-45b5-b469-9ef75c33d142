import { FileIcon } from 'lucide-react';

import { Button } from '../ui/button';
import { toast } from '../ui/use-toast';

import { downloadProjectDatapointGaps } from '@/api/project-settings/project-settings.api';
import { Project } from '@/types/project';

export const ExportDatapointGapsSheet = ({ project }: { project: Project }) => {
  async function handleClick() {
    try {
      const docxblob = await downloadProjectDatapointGaps(project.id);
      const url = window.URL.createObjectURL(
        new Blob([docxblob], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        })
      );
      const link = document.createElement('a');

      link.href = url;
      link.setAttribute('download', `${project.name} Gaps.xlsx`);
      document.body.appendChild(link);
      link.click();

      // Clean up
      link.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: 'Download Started',
        variant: 'success',
      });
    } catch (error: any) {
      toast({
        title: 'Error exporting report',
        description:
          error.response.status === 404
            ? 'No approved reporttexts found for this project'
            : error.message,
        variant: 'destructive',
      });
    }
  }

  return (
    <Button onClick={handleClick} variant="outline">
      <FileIcon className="w-4 h-4 mr-2" />
      Download Gaps Excel
    </Button>
  );
};
