import { DocumentStatus } from '@/types/document';

export function DocumentStatusLabel({
  status,
}: {
  status: {
    value: DocumentStatus;
    label: string;
    icon: any;
    color: string; // Darker gray for "Not Reported"
  };
}) {
  return (
    <div
      className="flex items-center rounded-md py-1 px-3 w-fit text-xs whitespace-nowrap font-normal"
      style={{
        backgroundColor: `${status.color}1A`,
        color: status.color,
      }}
    >
      {status.icon && (
        <status.icon className="mr-2 h-4 w-4 text-muted-foreground" />
      )}
      <span className="text-inline-block">{status.label}</span>
    </div>
  );
}
