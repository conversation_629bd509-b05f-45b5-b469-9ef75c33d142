import { Injectable } from '@nestjs/common';
import { CustomGptTool } from '../util/chat-gpt.models';
import { ChatMessageDto } from './entities/chat.message.dto';
import { UsersService } from '../users/users.service';
import { ChatGptService } from '../llm/chat-gpt.service';
import { MultiQuestionSearchEngine } from '../util/multi-question-search-engine.service';
import { SearchEngineTool } from '../util/search-engine.tool';
import { LLM_MODELS } from 'src/constants';

const MAX_PERPLEXITY_QUESTIONS = 2;

@Injectable()
export class InitiativeDetailService {
  readonly gptModel = LLM_MODELS['gpt-4o'];

  constructor(
    private readonly usersService: UsersService,
    private readonly chatGptService: ChatGptService,
    private readonly multiQuestionSearchEngine: MultiQuestionSearchEngine,
    private readonly searchEngine: SearchEngineTool,
  ) {}

  createInitiativeDetailTool(userId: string): CustomGptTool<void, string> {
    return {
      type: 'replacing-gpt-tool',
      toolDefinition: {
        type: 'function',
        function: {
          name: 'initiative-detail-creator',
          description:
            'Nimmt Maßnahmenvorschläge an und gibt eine detaillierte Beschreibung der Maßnahme zurück.',
        },
      },
      execute: (_, messages) =>
        this.createDetailledInitiative(messages as ChatMessageDto[], userId),
    };
  }

  async *createDetailledInitiative(
    previousMessages: ChatMessageDto[],
    userId: string,
  ): AsyncIterableIterator<string> {
    console.log('[Initiative Detail Creator] Called');

    const filteredMessages = previousMessages.filter(
      (m) => m.role !== 'system',
    );
    const context = await this.usersService.getUserPromptContext(userId);

    const initiativeDescription =
      await this.extractInitiativeInformation(filteredMessages);

    const fetchInitiativeSummary = this.generateShortSummary(
      initiativeDescription,
      context,
      previousMessages,
    );

    const fetchRelevantNumbers = this.generateSummaryRelevantNumbers(
      initiativeDescription,
      context,
      previousMessages,
    );

    const fetchExecutionPlan = this.generateExecutionPlan(
      initiativeDescription,
      context,
      previousMessages,
    );

    const fetchGrantList = this.generateGrantList(
      initiativeDescription,
      context,
      previousMessages,
    );

    const fetchPartners = this.generatePartnerList(
      initiativeDescription,
      context,
      previousMessages,
    );

    const [
      initiativeShortSummary,
      relevantNumbersSummary,
      executionPlan,
      grantList,
      partnersList,
    ] = await Promise.all([
      fetchInitiativeSummary,
      fetchRelevantNumbers,
      fetchExecutionPlan,
      fetchGrantList,
      fetchPartners,
    ]);

    const stream = await this.writeFullSummary(
      initiativeDescription,
      context,
      previousMessages,
      initiativeShortSummary,
      relevantNumbersSummary,
      executionPlan,
      grantList,
      partnersList,
    );

    for await (const part of stream) {
      yield part;
    }

    return;
  }

  async writeFullSummary(
    initiativeDescription: string,
    context: string,
    previousMessages: ChatMessageDto[],
    initiativeShortSummary: string,
    relevantNumbersSummary: string,
    executionPlan: string,
    grantList: string,
    partnersList: string,
  ) {
    const stream = await this.chatGptService.createCompletionStream(
      LLM_MODELS['gpt-4o'], // TODO: needs to be 4 otherwise it'll be cut off
      [
        {
          role: 'system',
          content: fullSummaryPrompt(
            initiativeDescription,
            context,
            previousMessages,
            initiativeShortSummary,
            relevantNumbersSummary,
            executionPlan,
            grantList,
            partnersList,
          ),
        },
      ],
    );

    return stream;
  }

  generateShortSummary(
    initiativeDescription: string,
    context: string,
    previousMessages: ChatMessageDto[],
  ) {
    // const tools = [this.multiQuestionSearchEngine.toolDefinition];
    const tools = [this.searchEngine.createSearchEngine()];
    const initiativeShortSummary =
      this.chatGptService.createCompletionWithFunctions(
        this.gptModel,
        [
          {
            role: 'system',
            content: shortSummaryPrompt(
              initiativeDescription,
              context,
              previousMessages,
            ),
          },
        ],
        tools,
      );

    return initiativeShortSummary;
  }

  generateSummaryRelevantNumbers(
    initiativeDescription: string,
    context: string,
    previousMessages: ChatMessageDto[],
  ) {
    // const tools = [this.multiQuestionSearchEngine.toolDefinition];
    const tools = [this.searchEngine.createSearchEngine()];
    const relevantNumbersSummary =
      this.chatGptService.createCompletionWithFunctions(
        this.gptModel,
        [
          {
            role: 'system',
            content: relevantNumbersPrompt(
              initiativeDescription,
              context,
              previousMessages,
            ),
          },
        ],
        tools,
      );

    return relevantNumbersSummary;
  }

  generateExecutionPlan(
    initiativeDescription: string,
    context: string,
    previousMessages: ChatMessageDto[],
  ) {
    // const tools = [this.multiQuestionSearchEngine.toolDefinition];
    const tools = [this.searchEngine.createSearchEngine()];
    const executionPlan = this.chatGptService.createCompletionWithFunctions(
      LLM_MODELS['gpt-4o'], // TODO: mini is not enough for this
      [
        {
          role: 'system',
          content: executionPlanPrompt(
            initiativeDescription,
            context,
            previousMessages,
          ),
        },
      ],
      tools,
    );

    return executionPlan;
  }

  generatePartnerList(
    initiativeDescription: string,
    context: string,
    previousMessages: ChatMessageDto[],
  ) {
    // const tools = [this.multiQuestionSearchEngine.toolDefinition];
    const tools = [this.searchEngine.createSearchEngine()];
    const partnerList = this.chatGptService.createCompletionWithFunctions(
      this.gptModel,
      [
        {
          role: 'system',
          content: partnersPrompt(
            initiativeDescription,
            context,
            previousMessages,
          ),
        },
      ],
      tools,
    );

    return partnerList;
  }

  generateGrantList(
    initiativeDescription: string,
    context: string,
    previousMessages: ChatMessageDto[],
  ) {
    // const tools = [this.multiQuestionSearchEngine.toolDefinition];
    const tools = [this.searchEngine.createSearchEngine()];
    const grantSummary = this.chatGptService.createCompletionWithFunctions(
      this.gptModel,
      [
        {
          role: 'system',
          content: grantListPrompt(
            initiativeDescription,
            context,
            previousMessages,
          ),
        },
      ],
      tools,
    );

    return grantSummary;
  }

  extractInitiativeInformation(previousMessages: ChatMessageDto[]) {
    const initiativeExtract = this.chatGptService.createCompletion(
      this.gptModel,
      [
        {
          role: 'system',
          content: `Extrahiere die 1 Kurzbeschreibung der Maßnahme die ausgesucht wurde
          
           <Previous_Messages>${previousMessages.map((m) => `[${m.role}]: ${m.content}\n`)}</Previous_Messages>
          `,
        },
      ],
    );

    return initiativeExtract;
  }
}

const shortSummaryPrompt = (
  initiativeDescription: string,
  context: string,
  previousMessages: ChatMessageDto[],
) => `
  Schreibe eine Kursbeschreibung der <Maßnahme> in ca. 300 Wörtern inklusiver folgender Informationen:

  - Relevanz und Notwendigkeit:
      - Erklärung der Wichtigkeit und Relevanz der Maßnahme. Welche Probleme oder Herausforderungen adressiert sie? Welche Unternehmenskennzahlen (z.B. CO2-Reduktionspotenziale, Effizienzsteigerung, Kosteneinsparungen) sind betroffen und werden optimiert?
  - Unternehmensziele und Nachhaltigkeitsstrategie:
      - Verbindung mit den übergeordneten Zielen des Unternehmens. Welche spezifischen Unternehmensziele und Nachhaltigkeitsstrategien unterstützt die Maßnahme?
  - Listung der Top 3 Vorteile der Maßnahme für das Unternehmen:
      - Erklärung der Vorteile und kurze Begründung, warum jeder Vorteil wichtig ist.
  - Relevante GRI- und ESRS-Kennzahlen:
      - Beschreibung der relevanten GRI- und ESRS-Kennzahlen, auf die die Maßnahme einzahlt (z.B. GRI 302-1, ESRS E1).
      
  Nutze dafür die <Kontext_Informationen_Unternehmen>, <Nachrichtenverlauf>.
  Nutze auch das Search Engine Tool falls du Informationen benötigst.
  Search Engine darf nur **MAXIMAL 1 MAL AUFGERUFEN WERDEN** und akzeptiert maximal ${MAX_PERPLEXITY_QUESTIONS} Fragen.
  
  
  <Maßnahme>: ${initiativeDescription} </Maßnahme>
  <Kontext_Informationen_Unternehmen>: ${context}</Kontext_Informationen_Unternehmen>
  <Nachrichtenverlauf>: ${previousMessages.map((m) => `[${m.role}]: ${m.content}\n`)} </Nachrichtenverlauf>
`;

const relevantNumbersPrompt = (
  initiativeDescription: string,
  context: string,
  previousMessages: ChatMessageDto[],
) => `
  Schreibe eine Zusammenfassung relevanter Zahlen der <Maßnahme> in ca. 300 Wörtern inklusiver folgender Informationen:

  - Voraussichtliche Emissionsreduktion:
      - Angaben zur erwarteten Reduktion der Treibhausgasemissionen und spezifische Kennzahlen (z.B. Reduktion von 10 t CO2-Äquivalenten jährlich).
  - Kategorie des CO2-Fußabdrucks:
      - Detaillierte Informationen zur Emissionskategorie (Scope 1, 2, 3) basierend auf den gelisteten Informationen aus dem Nachhaltigkeitsbericht.
  - Voraussichtliche Kosten:
      - Mögliche Kosten bei der Etablierung der Maßnahmen für den beschriebenen Use Case.
  - Voraussichtliche Umsetzungsdauer:
      - Voraussichtliche Umsetzungsdauer bei der Etablierung der Maßnahmen für den beschriebenen Use Case.
  - Voraussichtliche Ressourcen:
      - Voraussichtliche Ressourcen bei der Etablierung der Maßnahmen für den beschriebenen Use Case.
      
  Nutze dafür die <Kontext_Informationen_Unternehmen>, <Nachrichtenverlauf>.
  Nutze vorallem das Search Engine Tool um relevante Zahlen zu finden.
  Search Engine darf nur **MAXIMAL 1 MAL AUFGERUFEN WERDEN** und akzeptiert maximal ${MAX_PERPLEXITY_QUESTIONS} Fragen.
  
  <Maßnahme>: ${initiativeDescription} </Maßnahme>
  <Kontext_Informationen_Unternehmen>: ${context}</Kontext_Informationen_Unternehmen>
  <Nachrichtenverlauf>: ${previousMessages.map((m) => `[${m.role}]: ${m.content}\n`)} </Nachrichtenverlauf>
`;

const executionPlanPrompt = (
  initiativeDescription: string,
  context: string,
  previousMessages: ChatMessageDto[],
) => `
  Schreibe einen Umsetzungsplan unten beschriebenen <Maßnahme> im folgenden <Format>:

  <Format>
  - Zeitplan und Meilensteine:
      - Detaillierter Zeitplan für die Umsetzung der Maßnahme mit klar definierten Meilensteinen. Einschätzung der Dauer jeder Phase.
  - Involvierte Stakeholder, Abteilungen, Personen:
      - Kategorische Auflistung der Abteilungen, Personen, und Stakeholder, die bei der Maßnahmen involviert werden müssen.
  - Umsetzungsphasen:
      - Diese sollen je nach Maßnahme angepasst werden. Pro Schritt eine Kurzbeschreibung, was dieser umfasst, sowie die involvierten Personen, Abteilungen und Stakeholder.
      - Analyse und Planung:
          - Status quo Analyse:
          - Ziele setzen:
          - Ressourcenplanung:
          - Risikobewertung:
      - Implementierung:
          - Umsetzungsplan:
          - Pilotphase (falls relevant):
          - Schulung und Kommunikation: 
      - Monitoring und Anpassung:
          - Kontinuierliche Überwachung: 
          - Feedback-Mechanismen: 
          - Berichterstattung: 
  </Format>
      
  Nutze dafür die <Kontext_Informationen_Unternehmen>, <Nachrichtenverlauf>.
  Nutze auch das Search Engine Tool falls du Informationen benötigst.
  Fokussiere Fragen beim Search Engine Tool darauf was die Umsetzungsphase beeinflusst. Vermeide Fragen die nicht für die Umsetzung relevant sind. (z.B. Kosten, Förderungen,...)
  Search Engine darf nur **MAXIMAL 1 MAL AUFGERUFEN WERDEN** und akzeptiert maximal ${MAX_PERPLEXITY_QUESTIONS} Fragen.
  
  <Maßnahme>: ${initiativeDescription} </Maßnahme>
  <Kontext_Informationen_Unternehmen>: ${context}</Kontext_Informationen_Unternehmen>
  <Nachrichtenverlauf>: ${previousMessages.map((m) => `[${m.role}]: ${m.content}\n`)} </Nachrichtenverlauf>
`;

const grantListPrompt = (
  initiativeDescription: string,
  context: string,
  previousMessages: ChatMessageDto[],
) => `
  Schreibe eine Liste an Förderungen für die unten beschriebenen <Maßnahme> im folgenden <Format>:
  Diese Förderprogramme müssen auf die Maßnahme und das Unternehmen (Branche, geographische Lage) abgestimmt sein

  <Format>
  - **[Titel des Förderprogramms 1]:** Förderungsstelle, Kurzbeschreibung, mögliches Fördervolumen, Verlinkung zu Programm.
  - **[Titel des Förderprogramms 2]:** Förderungsstelle, Kurzbeschreibung, mögliches Fördervolumen, Verlinkung zu Programm.
  - **[Titel des Förderprogramms 3]:** Titel, Förderungsstelle, Kurzbeschreibung, mögliches Fördervolumen, Verlinkung zu Programm.
  </Format>
      
  Nutze dafür die <Kontext_Informationen_Unternehmen>, <Nachrichtenverlauf>.
  Nutze hier stark das Search Engine Tool um aktuelle Infos zu bekommen.
  Search Engine darf nur **MAXIMAL 1 MAL AUFGERUFEN WERDEN** und akzeptiert maximal ${MAX_PERPLEXITY_QUESTIONS} Fragen.
  
  <Maßnahme>: ${initiativeDescription} </Maßnahme>
  <Kontext_Informationen_Unternehmen>: ${context}</Kontext_Informationen_Unternehmen>
  <Nachrichtenverlauf>: ${previousMessages.map((m) => `[${m.role}]: ${m.content}\n`)} </Nachrichtenverlauf>
`;

const partnersPrompt = (
  initiativeDescription: string,
  context: string,
  previousMessages: ChatMessageDto[],
) => `
  Schreibe eine Liste an möglichen Anbietern für die unten beschriebenen <Maßnahme>. Falls es Sinn macht suche auch nach Kooperationspartnern.
  Diese Lösungs- oder Kooperationspartner müssen auf die Maßnahme und das Unternehmen bzw. deren Standorte (Branche, Größe, geographische Lage) abgestimmt sein.

  <Format>
  - **[Partnername 1]:** Kurzbeschreibung
  - **[Partnername 2]:** Kurzbeschreibung
  - **[Partnername 3]:** Kurzbeschreibung
  </Format>
      
  Nutze dafür die <Kontext_Informationen_Unternehmen>, <Nachrichtenverlauf>.
  Nutze hier stark das Search Engine Tool um aktuelle Infos zu bekommen.
  Vermeide Fragen die nicht für die Partnersuche relevant sind. (z.B. Förderungen,...)
  Search Engine darf nur **MAXIMAL 1 MAL AUFGERUFEN WERDEN** und akzeptiert maximal ${MAX_PERPLEXITY_QUESTIONS} Fragen.
  
  <Maßnahme>: ${initiativeDescription} </Maßnahme>
  <Kontext_Informationen_Unternehmen>: ${context}</Kontext_Informationen_Unternehmen>
  <Nachrichtenverlauf>: ${previousMessages.map((m) => `[${m.role}]: ${m.content}\n`)} </Nachrichtenverlauf>
`;

const fullSummaryPrompt = (
  initiativeDescription: string,
  context: string,
  previousMessages: ChatMessageDto[],
  initiativeShortSummary,
  relevantNumbersSummary,
  executionPlan,
  grantList,
  partnersList,
) => `
  Schreibe eine ausführliche Zusammenfassung der <Maßnahme> im gegebenen <Format> unten.
  
  Nutze dafür
  - <Kurzbeschreibung>
  - <Relevante_Zahlen>
  - <Umsetzungsplan>
  - <Förderungen>
  - <Kooperationspartner>
  nutze möglichst viele der Informationen die du zur Verfügung hast!

  <Maßnahme>: ${initiativeDescription} </Maßnahme>
  <Format>${bluePrint}</Format>
  <Kontext_Informationen_Unternehmen>: ${context}</Kontext_Informationen_Unternehmen>
  <Nachrichtenverlauf>: ${previousMessages.map((m) => `[${m.role}]: ${m.content}\n`)} </Nachrichtenverlauf>
  <Kurzbeschreibung>${initiativeShortSummary}</Kurzbeschreibung>
  <Relevante_Zahlen>${relevantNumbersSummary}</Relevante_Zahlen>
  <Umsetzungsplan>${executionPlan}</Umsetzungsplan>
  <Förderungen>${grantList}</Förderungen>
  <Kooperationspartner>${partnersList}</Kooperationspartner>
`;

const bluePrint = `
  ### Titel der Maßnahme
  
  **Kursbeschreibung der Maßnahme in ca. 300 Wörtern inklusiver folgender Informationen:**
  
  - **Relevanz und Notwendigkeit:**
      - Erklärung der Wichtigkeit und Relevanz der Maßnahme. Welche Probleme oder Herausforderungen adressiert sie? Welche Unternehmenskennzahlen (z.B. CO2-Reduktionspotenziale, Effizienzsteigerung, Kosteneinsparungen) sind betroffen und werden optimiert?
  - **Unternehmensziele und Nachhaltigkeitsstrategie:**
      - Verbindung mit den übergeordneten Zielen des Unternehmens. Welche spezifischen Unternehmensziele und Nachhaltigkeitsstrategien unterstützt die Maßnahme?
  - **Listung der Top 3 Vorteile der Maßnahme für das Unternehmen:**
      - Erklärung der Vorteile und kurze Begründung, warum jeder Vorteil wichtig ist.
  - **Relevante GRI- und ESRS-Kennzahlen:**
      - Beschreibung der relevanten GRI- und ESRS-Kennzahlen, auf die die Maßnahme einzahlt (z.B. GRI 302-1, ESRS E1).
  
  ### Relevante Zahlen
  - **Voraussichtliche Emissionsreduktion:**
      - Angaben zur erwarteten Reduktion der Treibhausgasemissionen und spezifische Kennzahlen (z.B. Reduktion von 10 t CO2-Äquivalenten jährlich).
  - **Kategorie des CO2-Fußabdrucks:**
      - Detaillierte Informationen zur Emissionskategorie (Scope 1, 2, 3) basierend auf den gelisteten Informationen aus dem Nachhaltigkeitsbericht.
  - **Voraussichtliche Kosten:**
      - Mögliche Kosten bei der Etablierung der Maßnahmen für den beschriebenen Use Case.
  - **Voraussichtliche Umsetzungsdauer:**
      - Voraussichtliche Umsetzungsdauer bei der Etablierung der Maßnahmen für den beschriebenen Use Case.
  - **Voraussichtliche Ressourcen:**
      - Voraussichtliche Ressourcen bei der Etablierung der Maßnahmen für den beschriebenen Use Case.
  
  ### Umsetzungsphasen des Projekts
  
  - **Zeitplan und Meilensteine:**
      - Detaillierter Zeitplan für die Umsetzung der Maßnahme mit klar definierten Meilensteinen. Einschätzung der Dauer jeder Phase.
  - **Involvierte Stakeholder, Abteilungen, Personen:**
      - Kategorische Auflistung der Abteilungen, Personen, und Stakeholder, die bei der Maßnahmen involviert werden müssen.
  - **Umsetzungsphasen:**
      - Diese sollen je nach Maßnahme angepasst werden. Pro Schritt eine Kurzbeschreibung, was dieser umfasst, sowie die involvierten Personen, Abteilungen und Stakeholder.
      - **Analyse und Planung:**
          - **Status quo Analyse:** Bewertung der aktuellen Situation und Identifizierung von Verbesserungspotentialen.
          - **Ziele setzen:** Festlegung klarer, messbarer Ziele.
          - **Ressourcenplanung:** Ermittlung der erforderlichen Ressourcen (Zeit, Personal, Budget).
          - **Risikobewertung:** Identifikation und Bewertung potenzieller Risiken.
      - **Implementierung:**
          - **Umsetzungsplan:** Konkrete Schritte zur Einführung der Maßnahme. Auflistung der 5-8 relevantesten Schritte.
          - **Pilotphase (falls relevant):** Testphase zur Überprüfung der Machbarkeit und Wirksamkeit.
          - **Schulung und Kommunikation:** Bewusstseinsbildung bzw. Schulung involvierter Mitarbeitenden und Kommunikation der Maßnahme an relevante Stakeholder.
      - **Monitoring und Anpassung:**
          - **Kontinuierliche Überwachung:** Regelmäßige Überprüfung des Fortschritts mittels KPIs.
          - **Feedback-Mechanismen:** Einholen von Feedback und Anpassung der Maßnahmen bei Bedarf.
          - **Berichterstattung:** Regelmäßige Berichte über Fortschritte und Ergebnisse.
  
  ### Mögliche Förderungen
  
  **Verfügbare Förderprogramme und steuerliche Anreize:**
  
  Auflistung relevanter Förderprogramme für die spezifische Maßnahme inkl. Verlinkung zur Webseite. Diese Förderprogramme müssen auf die Maßnahme und das Unternehmen (Branche, geographische Lage) abgestimmt sein
  
  - **[Titel des Förderprogramms 1]:** Förderungsstelle, Kurzbeschreibung, mögliches Fördervolumen, Verlinkung zu Programm.
  - **[Titel des Förderprogramms 2]:** Förderungsstelle, Kurzbeschreibung, mögliches Fördervolumen, Verlinkung zu Programm.
  - **[Titel des Förderprogramms 3]:** Titel, Förderungsstelle, Kurzbeschreibung, mögliches Fördervolumen, Verlinkung zu Programm.
  
  **Lösungsanbieter und Kooperationspartner:**
  
  Auflistung relevanter Anbieter, die bei der Umsetzung der Maßnahmen unterstützen können. Diese Lösungs- oder Kooperationspartner müssen auf die Maßnahme und das Unternehmen bzw. deren Standorte (Branche, Größe, geographische Lage) abgestimmt sein
  
  - **[Partnername 1]:** Kurzbeschreibung, Verlinkung zur Webseite.
  - **[Partnername 2]:** Kurzbeschreibung, Verlinkung zur Webseite.
  - **[Partnername 3]:** Kurzbeschreibung, Verlinkung zur Webseite.
`;
