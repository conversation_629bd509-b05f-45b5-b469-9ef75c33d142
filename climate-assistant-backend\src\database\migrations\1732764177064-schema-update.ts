import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1732764177064 implements MigrationInterface {
  name = 'SchemaUpdate1732764177064';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "document" ADD "documentType" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "document" ADD "esrsCategory" text`);
    await queryRunner.query(`ALTER TABLE "document" ADD "year" integer`);
    await queryRunner.query(`ALTER TABLE "document" ADD "month" integer`);
    await queryRunner.query(`ALTER TABLE "document" ADD "day" integer`);
    await queryRunner.query(`ALTER TABLE "document" ADD "remarks" text`);
    await queryRunner.query(`ALTER TABLE "document" ADD "createdBy" uuid`);
    await queryRunner.query(
      `ALTER TABLE "document" ADD CONSTRAINT "FK_a581782d3fe36e6cb98e40b0572" FOREIGN KEY ("createdBy") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "document" DROP CONSTRAINT "FK_a581782d3fe36e6cb98e40b0572"`,
    );
    await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "createdBy"`);
    await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "remarks"`);
    await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "day"`);
    await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "month"`);
    await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "year"`);
    await queryRunner.query(
      `ALTER TABLE "document" DROP COLUMN "esrsCategory"`,
    );
    await queryRunner.query(
      `ALTER TABLE "document" DROP COLUMN "documentType"`,
    );
  }
}
