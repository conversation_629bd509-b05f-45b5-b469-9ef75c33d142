import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { Language } from 'src/project/entities/project.entity';
export declare class MDRPromptService {
    indentifyMDRandGenerateGapAnalysisSystemPrompt({ esrsDatapoint, language, }: {
        esrsDatapoint: ESRSDatapoint;
        language: Language;
    }): string | null;
    generateMDRP0106GapAnalysisSystemPrompt({ esrsDatapoint, language, }: {
        esrsDatapoint: ESRSDatapoint;
        language: Language;
    }): string;
    generateMDRA0112GapAnalysisSystemPrompt({ esrsDatapoint, language, }: {
        esrsDatapoint: ESRSDatapoint;
        language: Language;
    }): string;
    generateMDRT0113GapAnalysisSystemPrompt({ esrsDatapoint, language, }: {
        esrsDatapoint: ESRSDatapoint;
        language: Language;
    }): string;
    generateMDRP0709GapAnalysisSystemPrompt({ esrsDatapoint, language, }: {
        esrsDatapoint: ESRSDatapoint;
        language: Language;
    }): string;
    generateMDRA1314GapAnalysisSystemPrompt({ esrsDatapoint, language, }: {
        esrsDatapoint: ESRSDatapoint;
        language: Language;
    }): string;
    generateMDRT1419GapAnalysisSystemPrompt({ esrsDatapoint, language, }: {
        esrsDatapoint: ESRSDatapoint;
        language: Language;
    }): string;
    indentifyMDRContentGenerationMainPrompt({ esrsDatapoint, datapointGenerationContextFromLinkedDocumentChunks, language, reportTextGenerationRules, generalCompanyProfile, reportingYear, customUserRemark, }: {
        esrsDatapoint: ESRSDatapoint;
        datapointGenerationContextFromLinkedDocumentChunks: string;
        language: Language;
        reportTextGenerationRules: string;
        generalCompanyProfile: string;
        reportingYear: string;
        customUserRemark: string;
    }): string | null;
    generateMDRPContentGenerationSystemPrompt({ esrsDatapoint, datapointGenerationContextFromLinkedDocumentChunks, language, reportTextGenerationRules, generalCompanyProfile, reportingYear, customUserRemark, }: {
        esrsDatapoint: ESRSDatapoint;
        datapointGenerationContextFromLinkedDocumentChunks: string;
        language: Language;
        reportTextGenerationRules: string;
        generalCompanyProfile: string;
        reportingYear: string;
        customUserRemark: string;
    }): string;
    generateMDRPContentGenerationSystemPrompt_o1stefan({ esrsDatapoint, datapointGenerationContextFromLinkedDocumentChunks, language, reportTextGenerationRules, customUserRemark, }: {
        esrsDatapoint: ESRSDatapoint;
        datapointGenerationContextFromLinkedDocumentChunks: string;
        language: Language;
        reportTextGenerationRules: string;
        customUserRemark: string;
    }): string;
    generateMDRAContentGenerationSystemPrompt({ esrsDatapoint, datapointGenerationContextFromLinkedDocumentChunks, language, reportTextGenerationRules, generalCompanyProfile, reportingYear, customUserRemark, }: {
        esrsDatapoint: ESRSDatapoint;
        datapointGenerationContextFromLinkedDocumentChunks: string;
        language: Language;
        reportTextGenerationRules: string;
        generalCompanyProfile: string;
        reportingYear: string;
        customUserRemark: string;
    }): string;
    generateMDRTContentGenerationSystemPrompt({ esrsDatapoint, datapointGenerationContextFromLinkedDocumentChunks, language, reportTextGenerationRules, generalCompanyProfile, reportingYear, customUserRemark, }: {
        esrsDatapoint: ESRSDatapoint;
        datapointGenerationContextFromLinkedDocumentChunks: string;
        language: Language;
        reportTextGenerationRules: string;
        generalCompanyProfile: string;
        reportingYear: string;
        customUserRemark: string;
    }): string;
}
