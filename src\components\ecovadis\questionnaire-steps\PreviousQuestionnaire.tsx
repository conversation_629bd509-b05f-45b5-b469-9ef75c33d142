
import React, { useState } from 'react';
import { FileText, Check } from 'lucide-react';
import { FileUpload } from '@/components/ui/file-upload';

interface PreviousQuestionnaireProps {
  onComplete: () => void;
}

export const PreviousQuestionnaire: React.FC<PreviousQuestionnaireProps> = ({ onComplete }) => {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadComplete, setUploadComplete] = useState(false);
  
  const handleFile = (files: File[]) => {
    if (files.length > 0) {
      const excelFile = files.find(file => 
        file.name.endsWith('.xlsx') || file.name.endsWith('.xls')
      );
      
      if (excelFile) {
        setFile(excelFile);
        simulateUpload();
      } else {
        alert('Please upload an Excel file (.xlsx or .xls)');
      }
    }
  };
  
  const simulateUpload = () => {
    setUploading(true);
    
    setTimeout(() => {
      setUploading(false);
      setUploadComplete(true);
      
      setTimeout(() => {
        onComplete();
      }, 1500);
    }, 2000);
  };
  
  return (
    <div className="flex flex-col items-center">
      <p className="text-lg text-gray-600 mb-16 text-center max-w-2xl">
        If available, upload last year's completed Ecovadis questionnaire. This will pre-fill answers and restore document links.
      </p>
      
      {!uploadComplete ? (
        <div className="w-full max-w-2xl mb-8">
          <FileUpload onChange={handleFile} />
        </div>
      ) : (
        <div className="bg-green-100 rounded-lg p-4 w-full max-w-2xl mb-8 flex items-center">
          <div className="bg-green-500 rounded-full p-1 mr-3">
            <Check className="h-5 w-5 text-white" />
          </div>
          <p className="text-green-800 font-medium">Previous questionnaire uploaded successfully!</p>
        </div>
      )}
      
      {uploading && (
        <div className="w-full max-w-2xl">
          <div className="w-full h-2 bg-gray-200 rounded-full mb-2">
            <div className="h-full bg-glacier-mint rounded-full animate-progress w-3/4"></div>
          </div>
          <p className="text-sm text-gray-500">Processing previous questionnaire...</p>
        </div>
      )}
    </div>
  );
};

