// supabase/functions/ecovadis-questionnaire-import/index.ts
// @ts-expect-error TODO look into this later
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
// @ts-expect-error TODO look into this later
import { type SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2'
// @ts-expect-error TODO look into this later
import { read, utils, WorkBook } from 'https://esm.sh/xlsx@0.18.5'
import { corsHeaders } from '../_shared/cors.ts';
import { authValidator } from '../_shared/authValidator.ts';

// Type definitions for better type safety
type ImpactScore = 'High' | 'Medium' | 'Low' | null;

type EcoVadisIndicator =
  | 'POLICIES'
  | 'ENDORSEMENTS'
  | 'MEASURES'
  | 'CERTIFICATIONS'
  | 'COVERAGE'
  | 'REPORTING'
  | 'WATCH_FINDINGS';

interface RowData {
  [key: string]: any;
}

interface NormalizedRow {
  theme: string;
  themeImpact: string;
  indicator: string;
  indicatorImpact: string;
  questionName: string;
  questionCode: string;
  questionImpact: string;
  question: string;
  optionText: string;
  optionInstructions: string;
  parentCredit: string;
  assignedTo?: string;
  userInput?: string;
  supportingDocs: {
    title: string;
    supportingValues?: string;
    pageNumbers?: string;
  }[];
  [key: string]: any;
}

interface ImportStats {
  themes: { created: number; existing: number };
  questions: { created: number; existing: number };
  options: { created: number; existing: number };
  projectThemes: { created: number; existing: number };
  projectQuestions: { created: number; existing: number };
  answers: { created: number; existing: number };
  documentLinks: { created: number; existing: number };
}

// Define our impact mappings with common variations
const impactMap: Record<string, ImpactScore> = {
  'High': 'High',
  'high': 'High',
  'HIGH': 'High',
  'Medium': 'Medium',
  'medium': 'Medium',
  'MEDIUM': 'Medium',
  'Low': 'Low',
  'low': 'Low',
  'LOW': 'Low',
  'N/A': null,
  'n/a': null,
  'N/a': null
};

// Define our indicator mappings to match the database enum exactly
// Add common variations of each indicator to handle different wordings
const indicatorMap: Record<string, EcoVadisIndicator> = {
  'Policies': 'POLICIES',
  'policies': 'POLICIES',
  'Policy': 'POLICIES',
  'policy': 'POLICIES',
  'Endorsements': 'ENDORSEMENTS',
  'endorsements': 'ENDORSEMENTS',
  'Endorsement': 'ENDORSEMENTS',
  'endorsement': 'ENDORSEMENTS',
  'Measures': 'MEASURES',
  'measures': 'MEASURES',
  'Measure': 'MEASURES',
  'measure': 'MEASURES',
  'Certifications': 'CERTIFICATIONS',
  'certifications': 'CERTIFICATIONS',
  'Certification': 'CERTIFICATIONS',
  'certification': 'CERTIFICATIONS',
  'Coverage': 'COVERAGE',
  'coverage': 'COVERAGE',
  'Reporting': 'REPORTING',
  'reporting': 'REPORTING',
  'Report': 'REPORTING',
  'report': 'REPORTING',
  '360° Watch Findings': 'WATCH_FINDINGS',
  '360° watch findings': 'WATCH_FINDINGS',
  '360° News': 'WATCH_FINDINGS',
  '360° news': 'WATCH_FINDINGS',
  '360 Watch Findings': 'WATCH_FINDINGS',
  '360 News': 'WATCH_FINDINGS'
};

// Default indicator to use when mapping fails
const DEFAULT_INDICATOR: EcoVadisIndicator = 'MEASURES';

const columnOrderMap = {
  0: 'theme',
  1: 'themeImpact',
  2: 'indicator',
  3: 'indicatorImpact',
  4: 'questionName',
  5: 'questionCode',
  6: 'questionImpact',
  7: 'parentCredit',
  8: 'question',
  9: 'optionText',
  10: 'optionInstructions',
  11: 'assignedTo',
  12: 'userInput',
  13: 'supportingDocs',
  14: 'supportingValues',
  15: 'pageNumbers'
};

// Helper function to find the matching column name from the actual headers
function createHeaderMapByPosition(headers: string[]): Record<string, string> {
  const headerMap: Record<string, string> = {};
  console.log(headers);

  // Map each standardized field to the header at the corresponding position
  for (const [indexStr, standardField] of Object.entries(columnOrderMap)) {
    const index = parseInt(indexStr, 10);
    if (index < headers.length) {
      headerMap[standardField] = headers[index];
    }
  }

  return headerMap;
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Parse multipart form data to get the file and projectId
    const formData = await req.formData();
    const file = formData.get('file') as File | null;
    const projectId = formData.get('projectId') as string | null;

    if (!file) {
      return new Response(JSON.stringify({ error: 'No file provided' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    if (!projectId) {
      return new Response(JSON.stringify({ error: 'No projectId provided' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const { error, supabaseClient: supabase, response } = await authValidator(req);
    if (error || !supabase) {
      return response;
    }

    // Check if project exists
    const { data: project, error: projectError } = await supabase
      .from('project')
      .select('*')
      .eq('id', projectId)
      .single();

    if (projectError || !project) {
      return new Response(JSON.stringify({ error: 'Project not found', details: projectError?.message }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Store the file in Supabase Storage
    // const timestamp = new Date().getTime();
    // const fileName = `ecovadis-questionnaire-${timestamp}-${file.name}`;
    // const filePath = `${projectId}/${fileName}`;

    // Uncomment if you want to store the file
    // const { data: fileData, error: fileError } = await supabase.storage
    //   .from('ecovadis-imports')
    //   .upload(filePath, file, {
    //     contentType: file.type,
    //     upsert: true
    //   });

    // if (fileError) {
    //   return new Response(JSON.stringify({ error: 'Failed to store file', details: fileError }), {
    //     status: 500,
    //     headers: { 'Content-Type': 'application/json' }
    //   });
    // }

    // Read the Excel file
    const buffer = await file.arrayBuffer();
    const workbook = read(buffer, { type: 'array' }) as WorkBook;

    // Try to find a sheet with "Questionnaire" in its name (case-insensitive)
    const sheetNames = Object.keys(workbook.Sheets);
    const questionnaireSheet = sheetNames.find(name =>
      name.toLowerCase().includes('questionnaire') ||
      name.toLowerCase().includes('fragebogen')
    );

    // If no exact match, try to find any sheet that looks like it might be a questionnaire
    // (has key column names like "Question" or "Theme")
    if (!questionnaireSheet) {
      let bestSheet = '';
      let bestScore = 0;

      for (const name of sheetNames) {
        const sheet = workbook.Sheets[name];
        const actualRange = sheet['!ref'] ? utils.decode_range(sheet['!ref']) : null;
        const jsonData = utils.sheet_to_json(sheet, {
          header: 1,
          defval: '', // Default value for empty cells
          range: actualRange, // Explicit range
          raw: false // Ensure consistent string formatting
        }) as string[][];

        if (jsonData.length > 0) {
          const headers = jsonData[0];
          let score = 0;

          // Check if key column names exist
          if (headers.some(h => h && /theme/i.test(h))) score += 2;
          if (headers.some(h => h && /question/i.test(h))) score += 2;
          if (headers.some(h => h && /indicator/i.test(h))) score += 1;

          if (score > bestScore) {
            bestScore = score;
            bestSheet = name;
          }
        }
      }

      if (bestScore > 0) {
        console.info(`Found likely questionnaire sheet: ${bestSheet}`);
      } else {
        return new Response(JSON.stringify({
          error: 'Could not find a sheet that looks like a questionnaire'
        }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }
    }

    const sheetName = questionnaireSheet || sheetNames[0]; // Fallback to first sheet if all else fails

    if (!workbook.Sheets[sheetName]) {
      return new Response(JSON.stringify({ error: `Sheet "${sheetName}" not found in the workbook` }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Convert the sheet to JSON with headers
    const jsonData = utils.sheet_to_json(workbook.Sheets[sheetName]) as RowData[];

    if (jsonData.length === 0) {
      return new Response(JSON.stringify({ error: 'No data found in the sheet' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const keyPositions = new Map();
    const optimisticHeaders = {};

    jsonData.forEach((obj, rowIndex) => {
      Object.keys(obj).forEach((key, colIndex) => {
        if (!keyPositions.has(key)) {
          // Check if position is already occupied
          const existingKeysAtOrAfter = Array.from(keyPositions.entries())
            .filter(([_, pos]) => pos >= colIndex);

          if (existingKeysAtOrAfter.length > 0) {
            // Shift all keys at this position and after by 1
            existingKeysAtOrAfter.forEach(([existingKey, existingPos]) => {
              keyPositions.set(existingKey, existingPos + 1);
            });
          }

          // Insert new key at desired position
          keyPositions.set(key, colIndex);
        }
      });
    });

    // Sort keys by their original column position
    const sortedKeys = Array.from(keyPositions.entries())
      .sort(([, posA], [, posB]) => posA - posB)
      .map(([key]) => key);

    sortedKeys.forEach(key => {
      optimisticHeaders[key] = "";
    });

    // Get all headers from the first row
    const headers = Object.keys(optimisticHeaders);

    // console.log('Headers:', headers.length, headers);

    // return new Response(JSON.stringify({
    //   message: 'Headers mapped successfully'
    // }), {
    //   headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    // });

    // Map headers to standardized field names
    const headerMap = createHeaderMapByPosition(headers);

    // Log which headers were matched for debugging
    console.log('Mapped headers:', headerMap);

    // Check if we have the minimum required fields
    const requiredFields = ['theme', 'questionName', 'questionCode', 'question'];
    const missingFields = requiredFields.filter(field => !headerMap[field]);

    if (missingFields.length > 0) {
      return new Response(JSON.stringify({
        error: `Missing required fields: ${missingFields.join(', ')}`,
        details: 'The questionnaire format does not contain all required fields',
        mappedHeaders: headerMap
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Process the data with context carrying (handling empty cells)
    const normalizedData = normalizeExcelData(jsonData, headerMap);

    // Process the data and insert into database
    const result = await processQuestionnaire(normalizedData, projectId, supabase);

    return new Response(JSON.stringify({
      ...result,
      mappedHeaders: headerMap,
      sheetName
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error processing request:', error);
    return new Response(JSON.stringify({ error: 'Internal server error', details: (error as Error).message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});


// Function to normalize Excel data by carrying forward values for empty cells
// and creating separate rows for each question option
function normalizeExcelData(jsonData: RowData[], headerMap: Record<string, string>): NormalizedRow[] {
  const normalizedRows: NormalizedRow[] = [];
  
  // Track current question-level data to carry forward
  let currentQuestionData: Partial<NormalizedRow> = {};
  
  for (const row of jsonData) {
    // Check if this row contains a new question (has questionCode)
    const questionCodeHeader = headerMap['questionCode'];
    const hasQuestionCode = questionCodeHeader && row[questionCodeHeader] && String(row[questionCodeHeader]).trim();
    
    // If this is a new question row, update our current question data
    if (hasQuestionCode) {
      currentQuestionData = {};
      
      // Extract all question-level fields from this row
      for (const [standardField, headerName] of Object.entries(headerMap)) {
        if (headerName && row[headerName] !== undefined && row[headerName] !== '') {
          // Skip option-specific and supporting doc fields - these don't get carried forward
          if (!['optionText', 'optionInstructions', 'supportingDocs', 'supportingValues', 'pageNumbers'].includes(standardField)) {
            currentQuestionData[standardField] = String(row[headerName]);
          }
        }
      }
    }
    
    // Create a new normalized row for this option
    const normalizedRow: NormalizedRow = {
      theme: '',
      themeImpact: '',
      indicator: '',
      indicatorImpact: '',
      questionName: '',
      questionCode: '',
      questionImpact: '',
      question: '',
      optionText: '',
      optionInstructions: '',
      parentCredit: '',
      assignedTo: '',
      userInput: '',
      supportingDocs: [],
      ...currentQuestionData // Carry forward question-level data
    };
    
    // Extract option-specific data from current row
    const optionTextHeader = headerMap['optionText'];
    const optionInstructionsHeader = headerMap['optionInstructions'];
    const assignedToHeader = headerMap['assignedTo'];
    const userInputHeader = headerMap['userInput'];
    
    // Set option-specific fields
    if (optionTextHeader && row[optionTextHeader]) {
      normalizedRow.optionText = String(row[optionTextHeader]);
    }
    if (optionInstructionsHeader && row[optionInstructionsHeader]) {
      normalizedRow.optionInstructions = String(row[optionInstructionsHeader]);
    }
    if (assignedToHeader && row[assignedToHeader]) {
      normalizedRow.assignedTo = String(row[assignedToHeader]);
    }
    if (userInputHeader && row[userInputHeader]) {
      normalizedRow.userInput = String(row[userInputHeader]);
    }
    
    // Handle supporting documents
    const supportingDocsHeader = headerMap['supportingDocs'];
    if (supportingDocsHeader && row[supportingDocsHeader]) {
      const docInfo = {
        title: String(row[supportingDocsHeader]),
        supportingValues: row[headerMap['supportingValues']] ? String(row[headerMap['supportingValues']]) : '',
        pageNumbers: row[headerMap['pageNumbers']] ? String(row[headerMap['pageNumbers']]) : ''
      };
      normalizedRow.supportingDocs.push(docInfo);
    }
    
    // Only add the row if it has meaningful content
    // (either it's a question row or it has option text)
    if (hasQuestionCode || normalizedRow.optionText || normalizedRow.optionInstructions || normalizedRow.supportingDocs.length > 0) {
      normalizedRows.push(normalizedRow);
    }
  }
  
  return normalizedRows;
}

// Process the questionnaire data and insert into database
async function processQuestionnaire(
  data: NormalizedRow[],
  projectId: string,
  supabase: SupabaseClient
): Promise<{ success: boolean; stats: ImportStats; message: string }> {
  const stats: ImportStats = {
    themes: { created: 0, existing: 0 },
    questions: { created: 0, existing: 0 },
    options: { created: 0, existing: 0 },
    projectThemes: { created: 0, existing: 0 },
    projectQuestions: { created: 0, existing: 0 },
    answers: { created: 0, existing: 0 },
    documentLinks: { created: 0, existing: 0 }
  };

  const themeMap = new Map<string, string>(); // Track themes to avoid duplicates
  const questionMap = new Map<string, string>(); // Track questions to avoid duplicates

  // Group rows by question code to handle multi-row options for the same question
  const questionGroups = new Map<string, NormalizedRow[]>();

  // First pass: group rows by question code
  for (const row of data) {
    const questionCode = row.questionCode;
    if (questionCode) {
      if (!questionGroups.has(questionCode)) {
        questionGroups.set(questionCode, []);
      }
      questionGroups.get(questionCode)?.push(row);
    }
  }

  // Track question sort order per theme
  const themeSortCounter = new Map<string, number>();

  // Process each question group (a question and all its options)
  // Convert questionGroups to array and process in batches of 10
  const questionGroupsArray = Array.from(questionGroups.entries());
  const BATCH_SIZE = 10;
  // Process in batches
  for (let i = 0; i < questionGroupsArray.length; i += BATCH_SIZE) {
    const batch = questionGroupsArray.slice(i, i + BATCH_SIZE);

    await Promise.all(batch.map(async ([questionCode, rows]) => {
      if (rows.length === 0) {
        return;
      }

      // Use the first row for question and theme info
      const firstRow = rows[0];
      const themeName = firstRow.theme;
      const themeImpact = impactMap[firstRow.themeImpact] || null;

      // 1. Process theme
      if (themeName && !themeMap.has(themeName)) {
        // Check if theme exists
        const { data: existingTheme } = await supabase
          .from('ecovadis_theme')
          .select('id')
          .eq('title', themeName)
          .maybeSingle();

        let themeId: string;
        if (existingTheme) {
          themeId = existingTheme.id;
          stats.themes.existing++;
        } else {
          // Create theme
          const { data: newTheme, error } = await supabase
            .from('ecovadis_theme')
            .insert({
              title: themeName,
              description: `Theme for ${themeName}`
            })
            .select('id')
            .single();

          if (error) throw new Error(`Failed to create theme: ${error.message}`);
          themeId = newTheme.id;
          stats.themes.created++;
        }

        themeMap.set(themeName, themeId);

        // Create project-theme relation if doesn't exist
        const { data: existingProjectTheme } = await supabase
          .from('project_ecovadis_theme')
          .select('id')
          .eq('projectId', projectId)
          .eq('themeId', themeId)
          .maybeSingle();

        if (!existingProjectTheme) {
          const { error } = await supabase
            .from('project_ecovadis_theme')
            .insert({
              projectId,
              themeId,
              impact: themeImpact
            });

          if (error) throw new Error(`Failed to create project-theme relation: ${error.message}`);
          stats.projectThemes.created++;
        } else {
          stats.projectThemes.existing++;
        }

        // Initialize sort counter for this theme
        themeSortCounter.set(themeName, 0);
      }

      // 2. Process question
      const questionName = firstRow.questionName;
      const questionText = firstRow.question;

      // Get indicator with default fallback to prevent null values
      let indicatorValue: EcoVadisIndicator = DEFAULT_INDICATOR;
      if (firstRow.indicator && indicatorMap[firstRow.indicator]) {
        indicatorValue = indicatorMap[firstRow.indicator];
      }

      const questionImpact = impactMap[firstRow.questionImpact] || null;

      if (questionCode && questionName && questionText && !questionMap.has(questionCode)) {
        const themeId = themeMap.get(firstRow.theme);

        if (!themeId) {
          console.warn(`Theme ID not found for question ${questionCode}. Skipping.`);
          return;
        }

        // Get the current sort order for this theme
        const currentSortOrder = themeSortCounter.get(firstRow.theme) || 0;
        // Increment the sort counter for the next question in this theme
        themeSortCounter.set(firstRow.theme, currentSortOrder + 1);

        // Check if question exists
        const { data: existingQuestion } = await supabase
          .from('ecovadis_question')
          .select('id')
          .eq('questionCode', questionCode)
          .maybeSingle();

        let questionId: string;
        if (existingQuestion) {
          questionId = existingQuestion.id;

          // Update the sort order of existing question
          await supabase
            .from('ecovadis_question')
            .update({ sort: currentSortOrder })
            .eq('id', questionId);

          stats.questions.existing++;
        } else {
          // Create question, ensuring indicator is never null
          const { data: newQuestion, error } = await supabase
            .from('ecovadis_question')
            .insert({
              themeId,
              questionCode,
              questionName,
              question: questionText,
              indicator: indicatorValue,
              sort: currentSortOrder // Set the sort order for new questions
            })
            .select('id')
            .single();

          if (error) throw new Error(`Failed to create question: ${error.message}`);
          questionId = newQuestion.id;
          stats.questions.created++;
        }

        questionMap.set(questionCode, questionId);

        // Create project-question relation if doesn't exist
        const { data: existingProjectQuestion } = await supabase
          .from('project_ecovadis_question')
          .select('id')
          .eq('projectId', projectId)
          .eq('questionId', questionId)
          .maybeSingle();

        if (!existingProjectQuestion) {
          const { error } = await supabase
            .from('project_ecovadis_question')
            .insert({
              projectId,
              questionId,
              impact: questionImpact,
              status: 'pending'
            });

          if (error) throw new Error(`Failed to create project-question relation: ${error.message}`);
          stats.projectQuestions.created++;
        } else {
          stats.projectQuestions.existing++;
        }

        // 3. Process all options for this question from all related rows
        // Replace the option processing section in processQuestionnaire function
        // Starting from "// 3. Process all options for this question from all related rows"

        // 3. Process all options for this question from all related rows
        let optionIndex = 0;

        // First, group rows by actual answer options and their associated document rows
        const optionGroups: { optionRow: NormalizedRow }[] = [];
        let currentOptionGroup: { optionRow: NormalizedRow } | null = null;

        for (const row of rows) {
          const optionText = row.optionText;
          const userInput = row.userInput;
          const effectiveOptionText = optionText || userInput || '';

          if (effectiveOptionText &&
            effectiveOptionText !== 'N/A' &&
            !effectiveOptionText.toLowerCase().includes('no predefined options available')) {
            // This is a new answer option
            currentOptionGroup = {
              optionRow: row
            };
            optionGroups.push(currentOptionGroup);

          }
        }

        // Now process each option group (option + all its associated document rows)
        for (const optionGroup of optionGroups) {
          const row = optionGroup.optionRow;
          const effectiveOptionText = row.optionText || row.userInput || '';

          // Check if option exists
          let { data: existingOption } = await supabase
            .from('ecovadis_answer_option')
            .select('id')
            .eq('questionId', questionId)
            .eq('issueTitle', effectiveOptionText)
            .maybeSingle();

          if (!existingOption) {
            // Get optionInstructionsHeader from appropriate column
            const optionInstructionsHeader = row.optionInstructions || '';

            const { data: createOption, error } = await supabase
              .from('ecovadis_answer_option')
              .insert({
                questionId,
                issueTitle: effectiveOptionText,
                instructions: optionInstructionsHeader,
                sort: optionIndex
              })
              .select('id')
              .single();

            existingOption = createOption;

            if (error) throw new Error(`Failed to create answer option: ${error.message}`);
            stats.options.created++;
          } else {
            // Update the sort order of the existing option
            const { error: updateError } = await supabase
              .from('ecovadis_answer_option')
              .update({ sort: optionIndex })
              .eq('id', existingOption.id);

            if (updateError) {
              console.error(`Failed to update sort order for option ${existingOption.id}: ${updateError.message}`);
              throw new Error(`Failed to update answer option sort order: ${updateError.message}`);
            }

            stats.options.existing++;
          }

          // Check if the option has a "yes" response
          const userInput = row.userInput?.trim().toLowerCase();
          if (userInput && userInput !== '' && userInput !== 'no') {
            // Check if answer already exists
            const { data: existingAnswer } = await supabase
              .from('project_ecovadis_answer')
              .select('id')
              .eq('projectId', projectId)
              .eq('optionId', existingOption.id)
              .maybeSingle();

            let answerId: string;
            if (existingAnswer) {
              // Update existing answer
              const { error } = await supabase
                .from('project_ecovadis_answer')
                .update({ response: userInput })
                .eq('id', existingAnswer.id);
              if (error) throw new Error(`Failed to update answer: ${error.message}`);

              answerId = existingAnswer.id;
              stats.answers.existing++;
            } else {
              // Create answer
              const { data: newAnswer, error } = await supabase
                .from('project_ecovadis_answer')
                .insert({
                  projectId,
                  optionId: existingOption.id,
                  response: userInput
                })
                .select('id')
                .single();

              if (error) throw new Error(`Failed to create answer: ${error.message}`);
              answerId = newAnswer.id;
              stats.answers.created++;
            }

            for (const docRow of optionGroup.optionRow.supportingDocs) {
              const supportingDocs = docRow.title?.trim();
              const supportingValues = docRow.supportingValues?.trim();
              const pageNumbers = docRow.pageNumbers?.toString().trim();

              if (supportingDocs && answerId) {
                await processDocumentsForAnswer({
                  supabase,
                  projectId,
                  answerId,
                  supportingDocs,
                  supportingValues,
                  pageNumbers,
                  stats
                });
              }
            }
          }

          // Increment the option sort order
          optionIndex++;
        }
      }
    }))
  }

  return {
    success: true,
    stats,
    message: 'Questionnaire imported successfully'
  };
}

// Add this new function after the linkDocumentChunkToAnswer function

// Helper function to process all documents for a specific answer
async function processDocumentsForAnswer({
  supabase,
  projectId,
  answerId,
  supportingDocs,
  supportingValues,
  pageNumbers,
  stats
}: {
  supabase: SupabaseClient;
  projectId: string;
  answerId: string;
  supportingDocs: string;
  supportingValues?: string;
  pageNumbers?: string;
  stats: ImportStats;
}) {
  // Get workspace ID from the project
  const { data: projectData } = await supabase
    .from('project')
    .select('workspaceId')
    .eq('id', projectId)
    .single();

  if (!projectData) {
    console.warn(`Project ${projectId} not found when processing documents`);
    return;
  }

  const workspaceId = projectData.workspaceId;

  // Find documents by name in the workspace
  const { data: documents } = await supabase
    .from('document')
    .select('id, name')
    .eq('workspaceId', workspaceId)
    .ilike('name', supportingDocs);

  if (documents && documents.length > 0) {
    // For each document, fetch all relevant chunks in a single query
    for (const document of documents) {
      // Parse page numbers - could be ranges like "1-3, 5, 7-9"
      const pageNumbersArray = pageNumbers ? parsePageNumbers(pageNumbers) : [];

      let chunks: { id: string }[] = [];

      if (pageNumbersArray.length === 0) {
        // If no specific pages provided, get all chunks for this document
        const { data: documentChunks, error } = await supabase
          .from('document_chunk')
          .select('id')
          .eq('documentId', document.id)
          .order('page', { ascending: true });

        chunks = documentChunks || [];
      } else {
        // Get all specified chunks in a single query using 'in'
        const pageStrings = pageNumbersArray.map(page => page.toString());
        const { data: documentChunks, error } = await supabase
          .from('document_chunk')
          .select('id')
          .eq('documentId', document.id)
          .in('page', pageStrings)
          .order('page', { ascending: true });

        chunks = documentChunks || [];
      }

      // Link chunks in batches of 10
      const chunkIds = chunks.map(chunk => chunk.id);
      for (let i = 0; i < chunkIds.length; i += 10) {
        const batch = chunkIds.slice(i, i + 10);
        await Promise.all(batch.map(chunkId =>
          linkDocumentChunkToAnswer({
            supabase,
            answerId,
            documentChunkId: chunkId,
            comment: i === 0 && supportingValues ? supportingValues : '',
            stats
          })
        ));
      }
    }
  }
}

// Helper function to link document chunks to answers
async function linkDocumentChunkToAnswer({
  supabase,
  answerId,
  documentChunkId,
  comment,
  stats
}:
  {
    supabase: SupabaseClient,
    answerId: string,
    documentChunkId: string,
    comment: string,
    stats: ImportStats
  }
) {
  // Check if link already exists
  const { data: existingLink } = await supabase
    .from('project_ecovadis_linked_document_chunks')
    .select('id')
    .eq('answerId', answerId)
    .eq('documentChunkId', documentChunkId)
    .maybeSingle();

  if (!existingLink) {
    const { error } = await supabase
      .from('project_ecovadis_linked_document_chunks')
      .insert({
        answerId,
        documentChunkId,
        comment
      });

    if (error) throw new Error(`Failed to link document chunk: ${error.message}`);
    stats.documentLinks.created++;
  } else {
    // Optionally update the comment if needed
    if (comment) {
      await supabase
        .from('project_ecovadis_linked_document_chunks')
        .update({ comment })
        .eq('id', existingLink.id);
    }
    stats.documentLinks.existing++;
  }
}

// Helper function to parse page numbers from string like "1-3, 5, 7-9"
function parsePageNumbers(pageStr: string): number[] {

  // if "toutes"
  if (pageStr.toLowerCase() === 'toutes') {
    return [];
  }

  const pages: number[] = [];

  // Split by commas
  const parts = pageStr.split(',').map(p => p.trim());

  for (const part of parts) {
    if (part.includes('-')) {
      // Handle ranges like "1-3"
      const [start, end] = part.split('-').map(num => parseInt(num.trim(), 10));
      if (!isNaN(start) && !isNaN(end)) {
        for (let i = start; i <= end; i++) {
          pages.push(i);
        }
      }
    } else {
      // Handle single numbers
      const pageNum = parseInt(part.trim(), 10);
      if (!isNaN(pageNum)) {
        pages.push(pageNum);
      }
    }
  }

  return pages;
}