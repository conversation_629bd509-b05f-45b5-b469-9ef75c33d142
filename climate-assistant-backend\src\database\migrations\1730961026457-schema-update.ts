import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1730961026457 implements MigrationInterface {
  name = 'SchemaUpdate1730961026457';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."user_workspace_role_enum" RENAME TO "user_workspace_role_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."user_workspace_role_enum" AS ENUM('SUPER_ADMIN', 'WORKSPACE_ADMIN', 'AI_CONTRIBUTOR', 'AI_ONLY_REVIEW', 'CONTRIBUTOR')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_workspace" ALTER COLUMN "role" TYPE "public"."user_workspace_role_enum" USING "role"::"text"::"public"."user_workspace_role_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."user_workspace_role_enum_old"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."user_workspace_role_enum_old" AS ENUM('SUPER_ADMIN', 'WORKSPACE_ADMIN', 'AI_CONTRIBUTOR', 'CONTRIBUTOR')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_workspace" ALTER COLUMN "role" TYPE "public"."user_workspace_role_enum_old" USING "role"::"text"::"public"."user_workspace_role_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."user_workspace_role_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."user_workspace_role_enum_old" RENAME TO "user_workspace_role_enum"`,
    );
  }
}
