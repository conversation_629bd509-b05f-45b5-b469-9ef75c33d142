"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1720764073594 = void 0;
class SchemaUpdate1720764073594 {
    constructor() {
        this.name = 'SchemaUpdate1720764073594';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "user"
                                 (
                                     "id"       uuid                   NOT NULL DEFAULT uuid_generate_v4(),
                                     "email"    character varying(100) NOT NULL,
                                     "password" character varying(100) NOT NULL,
                                     CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id")
                                 )`);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "user"`);
    }
}
exports.SchemaUpdate1720764073594 = SchemaUpdate1720764073594;
//# sourceMappingURL=1720764073594-schema-update.js.map