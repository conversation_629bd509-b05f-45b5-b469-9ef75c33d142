
import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-full text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-glacier-bluedark text-primary-foreground hover:bg-glacier-bluedark/90 hover:text-white',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-slate-800 bg-background hover:bg-accent hover:text-accent-foreground text-glacier-bluedark',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline hover:text-glacier-blue',
        forest: 'bg-green-700 text-white hover:bg-green-700/90',
        button: 'bg-transparent border-0 hover:bg-gray-200 text-gray-500',
        outlinepop: 'border border-slate-800 bg-background hover:text-primary-foreground hover:bg-glacier-bluedark',
        mint: 'bg-glacier-mint text-glacier-darkBlue hover:bg-glacier-mint/90',
        darkBlue: 'bg-glacier-darkBlue text-white hover:bg-glacier-darkBlue/90',
        warning: 'bg-yellow-500 text-white hover:bg-yellow-600',
        info: 'bg-blue-500 text-white hover:bg-blue-600',
      },
      size: {
        default: 'h-10 px-6 py-2',
        xs: 'h-6 px-2 text-xs',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  size?: 'default' | 'xs' | 'sm' | 'lg' | 'icon';
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'forest' | 'button' | 'outlinepop' | 'mint' | 'darkBlue' | 'warning' | 'info';
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <div className={props.disabled ? 'cursor-not-allowed' : ''}>
        <Comp
          className={cn(buttonVariants({ variant, size, className }))}
          ref={ref}
          {...props}
        />
      </div>
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
