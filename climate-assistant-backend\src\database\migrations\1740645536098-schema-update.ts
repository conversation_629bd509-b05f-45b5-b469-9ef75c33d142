import { MigrationInterface, QueryRunner } from 'typeorm';

//Adding a new column to store company report text generation rules
export class SchemaUpdate1740645536098 implements MigrationInterface {
  name = 'SchemaUpdate1740645536098';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "workspace" ADD "reportTextGenerationRules" text`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "workspace" DROP COLUMN "reportTextGenerationRules"`,
    );
  }
}
