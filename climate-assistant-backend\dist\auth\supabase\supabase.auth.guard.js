"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const supabase_service_1 = require("./supabase.service");
const helpers_1 = require("../helpers");
let AuthGuard = class AuthGuard {
    constructor(reflector, supabaseService) {
        this.reflector = reflector;
        this.supabaseService = supabaseService;
    }
    async canActivate(context) {
        const isPublic = this.reflector.getAllAndOverride(helpers_1.IS_PUBLIC_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (isPublic) {
            return true;
        }
        console.log('AuthGuard: Checking if user is authenticated');
        const request = context.switchToHttp().getRequest();
        const token = this.extractToken(request);
        if (!token) {
            throw new common_1.UnauthorizedException();
        }
        try {
            const { data: session, error } = await this.supabaseService
                .getClient()
                .auth.getUser(token);
            if (!session) {
                throw new common_1.UnauthorizedException('Supabase authentication failed');
            }
            const user = await this.supabaseService.findByAuthId(session.user.id);
            if (!user) {
                throw new common_1.UnauthorizedException('User not found');
            }
            const requiredRoles = this.reflector.get('roles', context.getHandler());
            const baseWorkspaceId = session.user.user_metadata.workspaces &&
                session.user.user_metadata.workspaces.length > 0
                ? session.user.user_metadata.workspaces[0].workspaceId
                : null;
            request['user'] = {
                sub: user.id,
                id: user.id,
                email: user.email,
                workspaceId: baseWorkspaceId,
            };
            if (!requiredRoles) {
                return true;
            }
            const userRole = await this.supabaseService.getUserRole(user.id, baseWorkspaceId?.workspaceId);
            if (!userRole) {
                return false;
            }
            return requiredRoles.includes(userRole);
        }
        catch {
            throw new common_1.UnauthorizedException();
        }
    }
    extractToken(request) {
        const authHeader = request.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            return authHeader.substring(7);
        }
        return request?.cookies?.['access_token'];
    }
};
exports.AuthGuard = AuthGuard;
exports.AuthGuard = AuthGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector,
        supabase_service_1.SupabaseService])
], AuthGuard);
//# sourceMappingURL=supabase.auth.guard.js.map