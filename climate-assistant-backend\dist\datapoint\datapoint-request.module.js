"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatapointRequestModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const datapoint_request_service_1 = require("./datapoint-request.service");
const workspace_entity_1 = require("../workspace/entities/workspace.entity");
const datapoint_request_guard_1 = require("./datapoint-request.guard");
const datapoint_request_controller_1 = require("./datapoint-request.controller");
const datapoint_request_entity_1 = require("./entities/datapoint-request.entity");
const esrs_datapoint_entity_1 = require("./entities/esrs-datapoint.entity");
const datapoint_document_chunk_entity_1 = require("../datapoint-document-chunk/entities/datapoint-document-chunk.entity");
const data_request_module_1 = require("../data-request/data-request.module");
const prompts_module_1 = require("../prompts/prompts.module");
const project_module_1 = require("../project/project.module");
const users_module_1 = require("../users/users.module");
const workspace_module_1 = require("../workspace/workspace.module");
const datapoint_generation_entity_1 = require("./entities/datapoint-generation.entity");
const esrs_topic_datapoint_entity_1 = require("../knowledge-base/entities/esrs-topic-datapoint.entity");
const shared_datapoint_datarequest_service_1 = require("../shared/shared-datapoint-datarequest.service");
const jobs_1 = require("../types/jobs");
const bullAdapter_1 = require("@bull-board/api/bullAdapter");
const bull_1 = require("@nestjs/bull");
const nestjs_1 = require("@bull-board/nestjs");
const llm_rate_limiter_module_1 = require("../llm-rate-limiter/llm-rate-limiter.module");
const supabase_module_1 = require("../auth/supabase/supabase.module");
let DatapointRequestModule = class DatapointRequestModule {
};
exports.DatapointRequestModule = DatapointRequestModule;
exports.DatapointRequestModule = DatapointRequestModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                esrs_datapoint_entity_1.ESRSDatapoint,
                esrs_topic_datapoint_entity_1.ESRSTopicDatapoint,
                datapoint_request_entity_1.DatapointRequest,
                workspace_entity_1.Workspace,
                datapoint_document_chunk_entity_1.DatapointDocumentChunk,
                datapoint_generation_entity_1.DatapointGeneration,
            ]),
            supabase_module_1.SupabaseAuthModule,
            (0, common_1.forwardRef)(() => data_request_module_1.DataRequestModule),
            llm_rate_limiter_module_1.LlmRateLimiterModule,
            prompts_module_1.PromptModule,
            project_module_1.ProjectModule,
            users_module_1.UsersModule,
            workspace_module_1.WorkspaceModule,
            bull_1.BullModule.registerQueue({ name: jobs_1.JobProcessor.DatapointGeneration }),
            bull_1.BullModule.registerQueue({ name: jobs_1.JobProcessor.DatapointReview }),
            nestjs_1.BullBoardModule.forFeature({
                name: jobs_1.JobProcessor.DatapointGeneration,
                adapter: bullAdapter_1.BullAdapter,
            }),
            nestjs_1.BullBoardModule.forFeature({
                name: jobs_1.JobProcessor.DatapointReview,
                adapter: bullAdapter_1.BullAdapter,
            }),
        ],
        providers: [
            datapoint_request_service_1.DatapointRequestService,
            shared_datapoint_datarequest_service_1.DatapointDataRequestSharedService,
            datapoint_request_guard_1.DatapointRequestGuard,
        ],
        exports: [datapoint_request_service_1.DatapointRequestService],
        controllers: [datapoint_request_controller_1.DatapointRequestController],
    })
], DatapointRequestModule);
//# sourceMappingURL=datapoint-request.module.js.map