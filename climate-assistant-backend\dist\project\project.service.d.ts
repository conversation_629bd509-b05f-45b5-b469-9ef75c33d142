import { DataSource, Repository } from 'typeorm';
import { Project } from './entities/project.entity';
import { CreateProjectRequest, ProjectData, UpdateProjectRequest } from './entities/project.dto';
import { Comment, CommentType } from './entities/comment.entity';
import { DataRequest } from '../data-request/entities/data-request.entity';
import { KnowledgeBaseService } from '../knowledge-base/knowledge-base.service';
import { ESRSDatapoint } from '../datapoint/entities/esrs-datapoint.entity';
import { User } from 'src/users/entities/user.entity';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { PromptService } from 'src/prompts/prompts.service';
import { MaterialESRSTopic } from './entities/material-esrs-topic.entity';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { ESRSTopicDatapoint } from 'src/knowledge-base/entities/esrs-topic-datapoint.entity';
import { ProjectDatapointRequestService } from './project-dp.service';
import { ESRSTopicLevel } from 'src/knowledge-base/entities/esrs-topic.entity';
import { CommentGeneration, CommentStatus } from './entities/comment-generation.entity';
import { LlmRateLimiterService } from 'src/llm-rate-limiter/llm-rate-limiter.service';
export declare class ProjectService {
    private readonly userRepository;
    private readonly projectRepository;
    private readonly commentRepository;
    private readonly commentGenerationRepository;
    private readonly dataRequestRepository;
    private readonly materialESRSTopicRepository;
    private readonly esrsTopicDatapointRepository;
    private readonly datapointRequestRepository;
    private readonly knowledgeBaseService;
    private readonly projectDatapointRequestService;
    private readonly workspaceService;
    private readonly llmRateLimitService;
    private readonly promptService;
    private dataSource;
    constructor(userRepository: Repository<User>, projectRepository: Repository<Project>, commentRepository: Repository<Comment>, commentGenerationRepository: Repository<CommentGeneration>, dataRequestRepository: Repository<DataRequest>, materialESRSTopicRepository: Repository<MaterialESRSTopic>, esrsTopicDatapointRepository: Repository<ESRSTopicDatapoint>, datapointRequestRepository: Repository<DatapointRequest>, knowledgeBaseService: KnowledgeBaseService, projectDatapointRequestService: ProjectDatapointRequestService, workspaceService: WorkspaceService, llmRateLimitService: LlmRateLimiterService, promptService: PromptService, dataSource: DataSource);
    findAll(workspaceId: string): Promise<Project[]>;
    findActiveCSRDProjectByUser(userId: string): Promise<Project>;
    create({ workspaceId, userId, createProjectRequest, }: {
        workspaceId: string;
        userId: string;
        createProjectRequest: CreateProjectRequest;
    }): Promise<Project>;
    initializeCSRDProjectData(projectId: string): Promise<void>;
    findById(projectId: string): Promise<Project>;
    findData(projectId: string): Promise<ProjectData>;
    update({ projectId, workspaceId, createdBy, updateProjectRequest, }: {
        projectId: string;
        workspaceId: string;
        createdBy: string;
        updateProjectRequest: UpdateProjectRequest;
    }): Promise<Project>;
    delete({ projectId, workspaceId, userId, }: {
        projectId: string;
        workspaceId: string;
        userId: string;
    }): Promise<void>;
    findComment({ commentId }: {
        commentId: string;
    }): Promise<Comment>;
    addComment({ commentableId, commentableType, userId, workspaceId, comment, evaluationLot, }: {
        commentableId: string;
        commentableType: CommentType;
        userId: string;
        workspaceId: string;
        comment: string;
        evaluationLot?: boolean;
    }): Promise<Comment>;
    updateComment({ commentId, userId, workspaceId, comment, }: {
        commentId: string;
        userId: string;
        workspaceId: string;
        comment: string;
    }): Promise<Comment>;
    updateCommentGenerationStatus({ commentId, userId, workspaceId, data, }: {
        commentId: string;
        userId: string;
        workspaceId: string;
        data: {
            status: CommentStatus;
            evaluatorComment: string;
        };
    }): Promise<CommentGeneration>;
    deleteComment({ commentId, userId, workspaceId, }: {
        commentId: string;
        userId: string;
        workspaceId: string;
    }): Promise<void>;
    resolveComment({ commentId, resolution, }: {
        commentId: string;
        resolution: boolean;
    }): Promise<Comment>;
    findAssignedESRSDatapoints({ esrs, workspaceId, }: {
        esrs: any;
        workspaceId: any;
    }): Promise<ESRSDatapoint[]>;
    private validateReportTextGenerationRules;
    generateDocx(projectId: string): Promise<Buffer | Blob>;
    generateXlsx(projectId: string): Promise<Buffer>;
    findMaterialityStatus(projectId: string): Promise<{
        project: Project;
        esrsTopics: import("src/knowledge-base/entities/esrs-topic.entity").ESRSTopic[];
    }>;
    updateMaterialityStatus(projectId: string, materialTopics: {
        esrsTopicId: number;
        level: ESRSTopicLevel;
        active: boolean;
    }[]): Promise<{
        project: Project;
        esrsTopics: import("src/knowledge-base/entities/esrs-topic.entity").ESRSTopic[];
    }>;
    isDatapointMaterial({ projectId, esrsDatapointId, }: {
        projectId: string;
        esrsDatapointId: number;
    }): Promise<boolean>;
    getProjectEsrsTopics({ projectId, esrsTopicIds, }: {
        projectId: string;
        esrsTopicIds: number[];
    }): Promise<MaterialESRSTopic[]>;
}
