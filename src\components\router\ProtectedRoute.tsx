import { Navigate, Outlet } from 'react-router-dom';

import { useAuthentication } from '@/api/authentication/authentication.query.ts';
import { UserLoading } from '@/components/UserLoading';

export function ProtectedRoute() {
  const { user, isLoading } = useAuthentication();

  if (isLoading) {
    return <UserLoading />;
  }

  if (!user) {
    return <Navigate to="/login" />;
  }

  return <Outlet />;
}
