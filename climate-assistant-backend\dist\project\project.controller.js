"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectController = void 0;
const common_1 = require("@nestjs/common");
const project_service_1 = require("./project.service");
const swagger_1 = require("@nestjs/swagger");
const project_dto_1 = require("./entities/project.dto");
const project_guard_1 = require("./project.guard");
const supabase_auth_guard_1 = require("../auth/supabase/supabase.auth.guard");
let ProjectController = class ProjectController {
    constructor(projectsService) {
        this.projectsService = projectsService;
    }
    async listAllProjects(req) {
        const workspace = req.user.workspaceId;
        const projects = await this.projectsService.findAll(workspace);
        return projects;
    }
    async createProject(req, createProjectRequest) {
        const workspaceId = req.user.workspaceId;
        const userId = req.user.id;
        const project = await this.projectsService.create({
            workspaceId,
            userId,
            createProjectRequest,
        });
        return project;
    }
    async createBaseStarterProject({ workspaceId, userId, createProjectRequest, }) {
        const project = await this.projectsService.create({
            workspaceId,
            userId,
            createProjectRequest,
        });
        return project;
    }
    async listEsrsDatapoints(req, esrs) {
        const workspaceId = req.user.workspaceId;
        const esrsDatapoints = await this.projectsService.findAssignedESRSDatapoints({
            esrs,
            workspaceId,
        });
        return esrsDatapoints;
    }
    async getProjectById(projectId) {
        return await this.projectsService.findData(projectId);
    }
    async updateProjectById(req, projectId, updateProjectRequest) {
        const workspace = req.user.workspaceId;
        const userId = req.user.id;
        const project = await this.projectsService.update({
            projectId,
            workspaceId: workspace,
            createdBy: userId,
            updateProjectRequest,
        });
        return project;
    }
    async deleteProjectById(req, projectId) {
        const workspaceId = req.user.workspaceId;
        const userId = req.user.id;
        await this.projectsService.delete({ projectId, workspaceId, userId });
        return { statusCode: 204, message: 'Project deleted successfully' };
    }
    async commentDataRequest(req, data) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        const { commentableId, commentableType } = data;
        const addComment = await this.projectsService.addComment({
            commentableId,
            commentableType,
            userId,
            workspaceId,
            comment: data.comment,
        });
        return addComment;
    }
    async updateCommentDataRequest(req, commentId, data) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        const addComment = await this.projectsService.updateComment({
            commentId,
            userId,
            workspaceId,
            comment: data.comment,
        });
        return addComment;
    }
    async updateCommentGenerationStatus(req, commentId, data) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        const addComment = await this.projectsService.updateCommentGenerationStatus({
            commentId,
            userId,
            workspaceId,
            data,
        });
        return addComment;
    }
    async resolveCommentDataRequest(commentId, data) {
        const resolve = await this.projectsService.resolveComment({
            commentId,
            resolution: data.resolve,
        });
        return resolve;
    }
    async deleteCommentDataRequest(req, commentId) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        await this.projectsService.deleteComment({
            commentId,
            userId,
            workspaceId,
        });
        return { statusCode: 204, message: 'Comment deleted successfully' };
    }
    async generateDocx(projectId, res) {
        const project = await this.projectsService.findById(projectId);
        const docxBuffer = await this.projectsService.generateDocx(projectId);
        res.setHeader('Content-Disposition', `attachment; filename="${project.name} Reporttext.docx"`);
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document; charset=UTF-8');
        res.end(docxBuffer);
    }
    async generateXlsx(projectId, res) {
        const project = await this.projectsService.findById(projectId);
        const docxBuffer = await this.projectsService.generateXlsx(projectId);
        res.setHeader('Content-Disposition', `attachment; filename="${project.name} Gaps.xlsx"`);
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.end(docxBuffer);
    }
    async findMaterialityStatus(projectId) {
        const data = await this.projectsService.findMaterialityStatus(projectId);
        return data;
    }
    async updateMaterialityStatus(projectId, body) {
        const { materialTopics } = body;
        const updatedMaterialStatus = await this.projectsService.updateMaterialityStatus(projectId, materialTopics);
        return { success: true, ...updatedMaterialStatus };
    }
};
exports.ProjectController = ProjectController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'List all projects' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Projects retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "listAllProjects", null);
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(201),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new project' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Project created successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, project_dto_1.CreateProjectRequest]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "createProject", null);
__decorate([
    (0, common_1.Post)('/starter'),
    (0, common_1.HttpCode)(201),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new starter project' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Project created successfully' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "createBaseStarterProject", null);
__decorate([
    (0, common_1.Get)('/esrs-datapoints'),
    (0, swagger_1.ApiOperation)({ summary: 'List ESRS datapoints' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'ESRS datapoints retrieved successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('esrs')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "listEsrsDatapoints", null);
__decorate([
    (0, common_1.UseGuards)(project_guard_1.ProjectGuard),
    (0, common_1.Get)('/:projectId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get project by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Project retrieved successfully' }),
    __param(0, (0, common_1.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "getProjectById", null);
__decorate([
    (0, common_1.UseGuards)(project_guard_1.ProjectGuard),
    (0, common_1.Put)('/:projectId'),
    (0, swagger_1.ApiOperation)({ summary: 'Update project by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Project updated successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('projectId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, project_dto_1.UpdateProjectRequest]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "updateProjectById", null);
__decorate([
    (0, common_1.UseGuards)(project_guard_1.ProjectGuard),
    (0, common_1.Delete)('/:projectId'),
    (0, common_1.HttpCode)(204),
    (0, swagger_1.ApiOperation)({ summary: 'Delete project by ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Project deleted successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "deleteProjectById", null);
__decorate([
    (0, common_1.UseGuards)(project_guard_1.ProjectGuard),
    (0, common_1.Post)('/:projectId/comment/create'),
    (0, common_1.HttpCode)(201),
    (0, swagger_1.ApiOperation)({ summary: 'Create a comment on a data request' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Comment created successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "commentDataRequest", null);
__decorate([
    (0, common_1.UseGuards)(project_guard_1.ProjectGuard),
    (0, common_1.Put)('/:projectId/comment/:commentId'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a comment on a data request' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Comment updated successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('commentId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "updateCommentDataRequest", null);
__decorate([
    (0, common_1.UseGuards)(project_guard_1.ProjectGuard),
    (0, common_1.Put)('/:projectId/comment/:commentId/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a AI comment review status' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Comment review updated successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('commentId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "updateCommentGenerationStatus", null);
__decorate([
    (0, common_1.UseGuards)(project_guard_1.ProjectGuard),
    (0, common_1.Put)('/:projectId/comment/:commentId/resolve'),
    (0, swagger_1.ApiOperation)({ summary: 'Resolve a comment on a data request' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Comment resolved successfully' }),
    __param(0, (0, common_1.Param)('commentId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "resolveCommentDataRequest", null);
__decorate([
    (0, common_1.UseGuards)(project_guard_1.ProjectGuard),
    (0, common_1.Delete)('/:projectId/comment/:commentId'),
    (0, common_1.HttpCode)(204),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a comment on a data request' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Comment deleted successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('commentId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "deleteCommentDataRequest", null);
__decorate([
    (0, common_1.UseGuards)(project_guard_1.ProjectGuard),
    (0, common_1.Get)('/:projectId/export-reporttext'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate DOCX report text for a project' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'DOCX report text generated successfully',
    }),
    __param(0, (0, common_1.Param)('projectId')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "generateDocx", null);
__decorate([
    (0, common_1.UseGuards)(project_guard_1.ProjectGuard),
    (0, common_1.Get)('/:projectId/export-gaps'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate XLSX datapoint gaps for a project' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: ' XLSX datapoint gaps generated successfully',
    }),
    __param(0, (0, common_1.Param)('projectId')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "generateXlsx", null);
__decorate([
    (0, common_1.UseGuards)(project_guard_1.ProjectGuard),
    (0, common_1.Get)('/:projectId/materiality'),
    (0, swagger_1.ApiOperation)({ summary: 'Get materiality status for a project' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Materiality status retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "findMaterialityStatus", null);
__decorate([
    (0, common_1.UseGuards)(project_guard_1.ProjectGuard),
    (0, common_1.Put)('/:projectId/materiality'),
    (0, swagger_1.ApiOperation)({ summary: 'Update materiality status for a project' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Materiality status updated successfully',
    }),
    __param(0, (0, common_1.Param)('projectId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "updateMaterialityStatus", null);
exports.ProjectController = ProjectController = __decorate([
    (0, swagger_1.ApiTags)('projects'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.AuthGuard),
    (0, common_1.Controller)('projects'),
    __metadata("design:paramtypes", [project_service_1.ProjectService])
], ProjectController);
//# sourceMappingURL=project.controller.js.map