import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1721561599151 implements MigrationInterface {
  name = 'SchemaUpdate1721561599151';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "file_upload" ADD "name" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "file_upload_chunk" DROP COLUMN "embedding"`,
    );
    await queryRunner.query(
      `ALTER TABLE "file_upload_chunk" ADD "embedding" text NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "file_upload_chunk" DROP COLUMN "embedding"`,
    );
    await queryRunner.query(
      `ALTER TABLE "file_upload_chunk" ADD "embedding" text NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "file_upload" DROP COLUMN "name"`);
  }
}
