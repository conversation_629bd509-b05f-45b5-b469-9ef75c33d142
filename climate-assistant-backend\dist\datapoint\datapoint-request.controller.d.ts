import { DatapointRequestService } from './datapoint-request.service';
import type { GenerateDatapointRequestTextPayload } from './entities/datapoint-request.dto';
import { datapointGenerationStatus } from './entities/datapoint-generation.entity';
import { DatapointDataRequestSharedService } from 'src/shared/shared-datapoint-datarequest.service';
import { DatapointRequest } from './entities/datapoint-request.entity';
import { User } from 'src/users/entities/user.entity';
export declare class DatapointRequestController {
    private readonly datapointRequestService;
    private readonly datapointDataRequestSharedService;
    constructor(datapointRequestService: DatapointRequestService, datapointDataRequestSharedService: DatapointDataRequestSharedService);
    getDataRequest(datapointRequestId: string): Promise<import("./entities/datapoint-request.dto").DatapointRequestData>;
    getMaterialTopics(datapointRequestId: string): Promise<any>;
    updateDatapointRequest(datapointRequestId: string, updateDatapointRequestPayload: Partial<DatapointRequest>, req: any): Promise<DatapointRequest>;
    reviewContentWithAi(datapointRequestId: string, req: any): Promise<void>;
    generateContentWithAi(datapointRequestId: string, additionalData: GenerateDatapointRequestTextPayload, req: any): Promise<void>;
    getDataRequestCitations(datapointRequestId: string, { citationId }: {
        citationId: string;
    }): Promise<any>;
    updateDataRequestCitations(datapointRequestId: string, payload: {
        citationId: string;
        index: number;
    }, req: any): Promise<DatapointRequest>;
    updateDatapointGenerationStatus(datapointGenerationId: string, req: any, payload: {
        status: datapointGenerationStatus;
    }): Promise<{
        content?: string;
        status: datapointGenerationStatus;
        evaluator?: User;
        evaluatedAt?: Date;
    }>;
    getDocumentLinksForDatapointRequest(datapointRequestId: string): Promise<import("../datapoint-document-chunk/entities/datapoint-document-chunk.entity").DatapointDocumentChunk[]>;
}
