import { MigrationInterface, QueryRunner } from 'typeorm';

// This migration is to add a new status to the datapoint_request table
// The new status is 'queued_for_generation'
// This status is used to indicate that the datapoint request is queued for generation
export class SchemaUpdate1738209468539 implements MigrationInterface {
  name = 'SchemaUpdate1738209468539';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."datapoint_request_status_enum" RENAME TO "datapoint_request_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."datapoint_request_status_enum" AS ENUM('not_answered', 'incomplete_data', 'no_data', 'complete_data', 'queued_for_generation')`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" ALTER COLUMN "status" TYPE "public"."datapoint_request_status_enum" USING "status"::"text"::"public"."datapoint_request_status_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."datapoint_request_status_enum_old"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."datapoint_request_status_enum_old" AS ENUM('not_answered', 'incomplete_data', 'no_data', 'complete_data')`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" ALTER COLUMN "status" TYPE "public"."datapoint_request_status_enum_old" USING "status"::"text"::"public"."datapoint_request_status_enum_old"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."datapoint_request_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."datapoint_request_status_enum_old" RENAME TO "datapoint_request_status_enum"`,
    );
  }
}
