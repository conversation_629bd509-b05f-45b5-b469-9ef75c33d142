
# EcoVadis Assessment Platform Documentation

## Application Overview

This application helps organizations prepare for EcoVadis sustainability assessments by managing documents, answering questionnaires, identifying gaps, and implementing improvements. The platform uses a React-based architecture with TypeScript for type safety.

## Core Data Types and Interfaces

### Document Types
- `DocumentCategory`: Categories like 'Policy', 'Report', 'Certificate', 'Contract', etc.
- `DocumentStatus`: 'active' or 'archived'
- `Document`: Main document interface with metadata, versions, and analysis status
- `DocumentMetadata`: Contains title, summary, author, file info, and table of contents
- `DocumentVersion`: Tracks version history with upload details and changes

### Assessment Types
- `Topic`: Assessment areas ('Environment', 'Labor & Human Rights', 'Ethics', etc.)
- `QuestionType`: Question categories ('Policy', 'Endorsement', 'Measures', etc.)
- `CompletionStatus`: 'complete' or 'pending'
- `ScoreEstimate`: Score levels (0, 25, 50, 75, 100)
- `Question`: Interface for assessment questions with topics, status, documents, gaps
- `Gap`: Represents identified gaps in documentation or practices
- `Improvement`: Action items to enhance EcoVadis scores

### Agent System
- `AgentActivityType`: Types of agent actions (document analysis, question answering, etc.)
- `AgentActivity`: Records of agent actions with status and results
- `AgentMessage`: Chat interface messages between user and agent

## Application Structure

### 1. Context Providers
The application uses React Context API for state management:

- **AppContext**: Main context that combines all other contexts
- **QuestionsContext**: Manages questionnaire-related state and operations
- **ImprovementsContext**: Handles improvement suggestions and tracking
- **UsersContext**: Manages user data
- **AgentContext**: Handles AI assistant functionality
- **OnboardingContext**: Manages the onboarding workflow
- **SidebarContext**: Controls sidebar visibility

### 2. Main Pages

#### Dashboard (`/`)
- Shows overall assessment completion progress
- Displays progress by EcoVadis topic (Environment, Labor & HR, Ethics, Procurement)
- Provides quick links to main application features

#### Questionnaire (`/questionnaire`)
- Lists all assessment questions grouped by topic
- Shows completion status and score estimates
- Allows filtering and searching questions

#### Question Detail (`/questionnaire/:id`)
- Displays detailed question information
- Provides answer options and document attachment
- Features AI assistant for gap analysis and answer suggestions
- Allows attaching evidence documents to specific answers

#### Improvements (`/improvements`)
- Lists all suggested improvements to enhance EcoVadis score
- Allows filtering by topic, type, benefit, and status
- Shows effort level, cost level, and potential benefits

#### Improvement Detail (`/improvements/:id`)
- Shows detailed improvement information
- Features tabs for action steps, document requirements, and AI assistance
- Allows assigning improvements to team members and setting due dates

#### Documents (`/documents`)
- Manages all evidence documents
- Provides filtering by category and search functionality
- Supports document uploads with metadata

#### Document Detail (`/documents/:id`)
- Displays document viewer with embedded preview
- Shows document metadata and version history
- Provides tabs for document content, versions, and metadata

#### Onboarding (`/onboarding`)
- Multi-step wizard for new users
- Collects company information
- Allows uploading existing EcoVadis questionnaires
- Facilitates initial document uploads

### 3. Key Components

#### Navigation & Layout
- **Layout**: Main layout wrapper with sidebar, header, and chat interface
- **Sidebar**: Navigation menu with links to main sections
- **Header**: Top bar with user info and global actions

#### Question Components
- **QuestionDetailTabs**: Tab interface for question details
- **AnswerTab**: Interface for providing answers to questions
- **GapAnalysisTab**: Shows identified gaps in documentation
- **AIAssistantTab**: Provides AI assistance for answering questions

#### Improvement Components
- **ImprovementHeader**: Shows improvement title and status
- **ImprovementOverview**: Displays improvement details
- **DetailTabs**: Tab interface for improvement details
- **ImpactSummary**: Shows potential impact of improvement
- **QuickActions**: Provides quick actions for improvements

#### Document Components
- **DocumentCard**: Card display for document listings
- **DocumentSearch**: Search interface for documents
- **DocumentUploadForm**: Form for uploading new documents
- **DocumentViewer**: Embedded document preview
- **VersionHistory**: Shows document version history

#### Agent Components
- **ChatInterface**: Chat interface for interacting with AI assistant
- **AgentActivitySidebar**: Shows recent AI assistant activities
- **AgentControls**: Controls for enabling/disabling AI assistant

#### Onboarding Components
- **OnboardingProgress**: Shows progress through onboarding steps
- **CompanySettingsStep**: Form for company information
- **EcovadisQuestionnaireStep**: Upload interface for existing questionnaires
- **EvidenceDocumentsStep**: Upload interface for initial documents

### 4. Core Features

#### Document Management
- Upload and categorize sustainability documents
- Track document versions and changes
- View document content with embedded viewer
- Attach documents as evidence for specific questions

#### Question Answering
- Answer EcoVadis assessment questions
- Attach supporting documents to answers
- Get AI assistance for answer generation
- Track question completion status

#### Gap Analysis
- Identify gaps in documentation and practices
- Generate improvement suggestions based on gaps
- Link gaps to specific improvements
- Track gap resolution progress

#### Improvement Tracking
- Create and track improvement actions
- Assign improvements to team members
- Set due dates and track completion
- Estimate effort, cost, and potential impact

#### AI Assistant
- Chat interface for getting help
- Automated document analysis
- Answer suggestions for questions
- Gap identification and improvement recommendations
- Activity tracking for AI actions

#### Onboarding Workflow
- Guided setup process for new users
- Company information collection
- Upload of existing assessments
- Initial document organization

## Core Workflows

### Assessment Preparation Workflow
1. Complete onboarding process
2. Upload relevant sustainability documents
3. Answer assessment questions using uploaded documents
4. Identify gaps in documentation
5. Create and implement improvements
6. Track progress toward higher score

### Document Management Workflow
1. Upload documents with metadata
2. Categorize documents by type and topic
3. Link documents to relevant questions
4. Update documents with new versions
5. Track document changes and improvements

### Gap Analysis Workflow
1. Use AI to analyze current documentation
2. Identify missing elements required by EcoVadis
3. Generate improvement suggestions
4. Create action items from suggestions
5. Implement and track improvements

### AI Assistance Workflow
1. Ask questions through chat interface
2. Receive suggestions for documents and answers
3. Get automated gap analysis
4. Generate improvement recommendations
5. Track AI activities and results

## Functional Relationships

- **Questions → Documents**: Questions link to supporting documents
- **Questions → Gaps**: Gap analysis identifies issues in question answers
- **Gaps → Improvements**: Gaps are linked to specific improvement actions
- **Improvements → Documents**: Improvements may require document updates
- **Agent → All Entities**: AI agent interacts with all system entities

## API Integration Points

The application includes placeholder functions for future API integrations:

- Document analysis and processing
- AI-powered answer generation
- Gap identification and analysis
- Improvement suggestion generation
- Automated document categorization

## Technical Implementation Notes

- React with TypeScript for frontend
- React Router for navigation
- Context API for state management
- React Query for data fetching
- Shadcn UI for component library
- Custom hooks for business logic
- Mock data for demonstration purposes

## Detailed Questionnaire Pages Documentation

### Main Questionnaire Page (/questionnaire)

#### Functionality
- **Question Listing**: Displays all assessment questions grouped by topic and type
- **Filtering System**: Allows filtering questions by topic, type, and completion status
- **Search Function**: Enables searching within question titles and descriptions
- **Completion Tracking**: Shows overall completion progress for the questionnaire
- **Topic Navigation**: Groups questions by topics (Environment, Labor & Human Rights, etc.)
- **Type Categorization**: Further groups questions by type (Policy, Measures, etc.)
- **Expansion Controls**: Allows expanding/collapsing question groups by type
- **Quick Navigation**: Enables jumping to specific question details

#### Data Structure
- **Questions Collection**: Array of Question objects stored in QuestionsContext
- **Filters State**: Tracks selected topic, type, and status filters
- **Search State**: Manages search term for filtering questions
- **Expansion State**: Controls which topic/type sections are expanded
- **Progress Calculation**: Computes completion percentage based on question statuses

#### User Interactions
- Click on a question to navigate to its detail page
- Toggle filter dropdown to select/deselect filters
- Type in search box to filter questions by text
- Click on topic accordion to expand/collapse topic sections
- Click on type row to expand/collapse type sections
- Reset filters button clears all active filters

### Question Detail Page (/questionnaire/:id)

#### Structure
- **Question Header**: Displays question title, description, topic and type
- **Status Controls**: Shows completion status with ability to toggle
- **Navigation**: Back button to return to questionnaire listing
- **Tab Interface**: Provides tabs for Answer, Gap Analysis, and AI Assistant

#### Answer Tab

##### Functionality
- **Question Display**: Shows the question text and description
- **Answer Selection**: Provides radio buttons or checkboxes for answer options
- **Document Linkage**: Allows attaching supporting documents to answers
- **AI Document Finder**: Suggests relevant documents from the repository
- **Document Upload**: Provides form for uploading new supporting documents
- **Option-Specific Documents**: Attaches documents to specific answer options
- **Document Management**: Displays, adds, and links documents to the question
- **Multi-select Support**: Handles single-choice or multi-choice questions appropriately

##### Data Flow
- Question and answer options loaded from QuestionsContext
- Selected answer stored in local state and synced to context
- Document attachments managed through context methods
- AI suggestions retrieved through simulated API calls

##### User Interactions
- Select answer options via radio buttons or checkboxes
- Add documents to specific answer options
- Use AI to find relevant documents from the repository
- Directly upload and link new documents
- View all documents attached to the question

#### Gap Analysis Tab

##### Functionality
- **Score Assessment**: Displays current EcoVadis score estimate for the question
- **Score Breakdown**: Shows detailed score assessment with progress indicator
- **Gap Identification**: Lists identified gaps in documentation or practices
- **Gap Generation**: Button to generate or refresh gap analysis
- **Improvement Links**: Connects gaps to specific improvement actions
- **Scoring Framework**: Provides detailed EcoVadis scoring criteria
- **Score Improvement**: Shows potential score impact of addressing gaps

##### Data Flow
- Gap data retrieved from the question object in context
- Gap analysis generated through simulated API call
- Score calculation based on question data and attached documents
- Improvements linked to gaps through relational IDs

##### User Interactions
- Click to generate or refresh gap analysis
- View detailed gaps in documentation
- Navigate to related improvements
- Toggle visibility of detailed scoring framework
- See potential score improvements

#### AI Assistant Tab

##### Functionality
- **Chat Interface**: Provides conversational interface with AI assistant
- **Context Awareness**: AI has awareness of the current question
- **Suggestion Generation**: Offers help with answering the question
- **Document Recommendations**: Suggests relevant documents
- **Best Practice Guidance**: Provides information on EcoVadis requirements
- **Message History**: Maintains chat history during the session
- **Quick Suggestions**: Offers preset questions for common inquiries

##### Data Flow
- Chat messages stored in local component state
- AI responses simulated with setTimeout and predefined responses
- Question context passed to the AI assistant component

##### User Interactions
- Type messages to ask questions about the requirements
- Click suggestion buttons for quick predefined questions
- Receive simulated AI responses with relevant information
- See typing indicators and message status

### Data Types & State Management

#### Question Object Structure
```typescript
interface Question {
  id: string;
  title: string;
  description: string;
  topic: Topic; // 'Environment', 'Labor & Human Rights', etc.
  type: QuestionType; // 'Policy', 'Endorsement', 'Measures', etc.
  status: CompletionStatus; // 'complete' or 'pending'
  scoreEstimate: ScoreEstimate; // 0, 25, 50, 75, 100
  linkedDocuments: LinkedDocument[];
  gaps: Gap[];
  aiGeneratedAnswer?: string;
  aiGapAnalysis?: string;
  answerOptions?: AnswerOption[];
  userAnswer?: string | string[];
}

interface AnswerOption {
  value: string;
  label: string;
  documents?: LinkedDocument[];
}

interface LinkedDocument {
  id: string;
  name: string;
  url: string;
  pageNumbers?: string;
  comments?: string;
  uploadDate: string;
  answerOption?: string;
}

interface Gap {
  id: string;
  description: string;
  type: 'Missing Document' | 'Content Improvement';
  linkedImprovementId: string;
}
```

#### QuestionsContext API
```typescript
interface QuestionsContextType {
  questions: Question[];
  updateQuestionStatus: (id: string, status: CompletionStatus) => void;
  updateQuestionAnswer: (id: string, answer: string | string[]) => void;
  generateAnswer: (questionId: string) => Promise<void>;
  generateGapAnalysis: (questionId: string) => Promise<void>;
  getQuestionById: (id: string) => Question | undefined;
  addLinkedDocumentToQuestion: (questionId: string, document: LinkedDocument) => void;
  addLinkedDocumentToAnswerOption: (questionId: string, answerOption: string, document: LinkedDocument) => void;
}
```

This documentation provides a comprehensive overview of the questionnaire functionality, structure, and data flow, focusing on the core features rather than styling details.
