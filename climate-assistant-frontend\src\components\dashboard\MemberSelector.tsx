import { useState } from 'react';

import { toast } from '../ui/use-toast';

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export function MemberSelector({
  action,
  currentUser,
  placeholder,
  members,
}: {
  action: (responsiblePersonId: string) => Promise<void>;
  currentUser?: string | null;
  placeholder: string;
  members: { id: string; name: string }[];
}) {
  const [selected, onSelect] = useState<string | null | undefined>(currentUser);

  async function handleChange(value: string) {
    try {
      await action(value);
      onSelect(value);
      toast({
        title: 'Responsible person updated',
        variant: 'success',
      });
    } catch (error) {
      toast({
        title: 'Failed to update responsible person',
        variant: 'destructive',
      });
    }
  }

  return (
    <Select
      value={selected || ''}
      onValueChange={(value) => handleChange(value)}
    >
      <SelectTrigger className="w-fit min-w-36 h-7">
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>Users</SelectLabel>
          {members.map((member) => (
            <SelectItem key={member.id} value={member.id}>
              {member.name}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}
