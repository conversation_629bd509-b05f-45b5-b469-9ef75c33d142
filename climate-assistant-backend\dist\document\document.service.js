"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DocumentService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const fs = require("fs");
const document_chunk_entity_1 = require("./entities/document-chunk.entity");
const document_entity_1 = require("./entities/document.entity");
const document_parser_service_1 = require("./document-parser.service");
const datapoint_document_chunk_entity_1 = require("../datapoint-document-chunk/entities/datapoint-document-chunk.entity");
const datapoint_request_entity_1 = require("../datapoint/entities/datapoint-request.entity");
const workspace_service_1 = require("../workspace/workspace.service");
const bull_1 = require("@nestjs/bull");
const jobs_1 = require("../types/jobs");
const env_helper_1 = require("../env-helper");
const chat_gpt_service_1 = require("../llm/chat-gpt.service");
const common_util_1 = require("../util/common-util");
const project_ecovadis_linked_document_chunks_entity_1 = require("../ecovadis/entities/project-ecovadis-linked-document-chunks.entity");
const pinecone_1 = require("@pinecone-database/pinecone");
const textsplitters_1 = require("@langchain/textsplitters");
const tiktoken_1 = require("@dqbd/tiktoken");
let DocumentService = DocumentService_1 = class DocumentService {
    constructor(documentChunkRepository, datapointDocumentChunkRepository, datapointRepository, documentRepository, documentParserService, workspaceService, chatGptService, chunkExtractionQueue, projectEcovadisLinkedDocumentChunksRepository) {
        this.documentChunkRepository = documentChunkRepository;
        this.datapointDocumentChunkRepository = datapointDocumentChunkRepository;
        this.datapointRepository = datapointRepository;
        this.documentRepository = documentRepository;
        this.documentParserService = documentParserService;
        this.workspaceService = workspaceService;
        this.chatGptService = chatGptService;
        this.chunkExtractionQueue = chunkExtractionQueue;
        this.projectEcovadisLinkedDocumentChunksRepository = projectEcovadisLinkedDocumentChunksRepository;
        this.logger = new common_1.Logger(DocumentService_1.name);
        this.tokenizer = (0, tiktoken_1.encoding_for_model)('gpt-3.5-turbo');
    }
    async onModuleInit() {
        try {
            this.pineconeClient = new pinecone_1.Pinecone({
                apiKey: process.env.PINECONE_API_KEY,
            });
            this.logger.log('Pinecone client initialized successfully');
        }
        catch (error) {
            this.logger.error('Failed to initialize Pinecone client:', error);
        }
    }
    async getWorkspaceDocumentUploads(workspaceId) {
        const documents = await this.documentRepository
            .createQueryBuilder('document')
            .leftJoinAndSelect('document.creator', 'creator')
            .leftJoinAndSelect('document.chunks', 'chunks')
            .leftJoinAndSelect('chunks.datapointDocumentChunkMap', 'datapointDocumentChunkMap')
            .select([
            'document.id',
            'document.name',
            'document.createdAt',
            'document.status',
            'document.documentType',
            'document.esrsCategory',
            'document.year',
            'document.month',
            'document.day',
            'document.remarks',
            'document.createdBy',
            'creator.id',
            'creator.name',
            'chunks.id',
            'datapointDocumentChunkMap.id',
            'datapointDocumentChunkMap.active',
            'datapointDocumentChunkMap.datapointRequestId',
        ])
            .where('document.workspaceId = :workspaceId', { workspaceId })
            .orderBy('document.createdAt', 'DESC')
            .getMany();
        const result = [];
        for (const document of documents) {
            const datapointsCount = this.getUniqueDatapointCount(document.chunks);
            result.push({
                ...document,
                chunks: undefined,
                creator: {
                    id: document.creator?.id,
                    name: document.creator?.name,
                },
                datapointsCount: datapointsCount,
            });
        }
        return result;
    }
    getUniqueDatapointCount(documentchunks) {
        const uniqueIds = new Set();
        return documentchunks
            .map((chunk) => {
            return chunk.datapointDocumentChunkMap.filter((map) => {
                if (map.active && !uniqueIds.has(map.datapointRequestId)) {
                    uniqueIds.add(map.datapointRequestId);
                    return true;
                }
                return false;
            }).length;
        })
            .reduce((acc, val) => acc + val, 0);
    }
    async findDocumentById(id) {
        const document = await this.documentRepository.findOne({
            where: { id },
        });
        return document;
    }
    async getDocumentData(id) {
        const documentData = await this.documentRepository.findOne({
            where: { id },
            relations: [
                'creator',
                'chunks.datapointDocumentChunkMap.datapointRequest.dataRequest',
            ],
            select: {
                creator: {
                    id: true,
                    name: true,
                },
            },
        });
        if (!documentData) {
            throw new common_1.NotFoundException(`Document not found`);
        }
        const datapointsCount = this.getUniqueDatapointCount(documentData.chunks);
        return {
            ...documentData,
            creator: {
                id: documentData.creator?.id,
                name: documentData.creator?.name,
            },
            datapointsCount: datapointsCount,
        };
    }
    async deleteDocument(id, workspaceId) {
        const hasPermission = await this.documentRepository.exists({
            where: { id, workspaceId },
        });
        if (!hasPermission) {
            throw new common_1.UnauthorizedException('You are not allowed to delete this Document');
        }
        const document = await this.documentRepository.findOneOrFail({
            where: { id, workspaceId },
        });
        const documentChunks = await this.documentChunkRepository.find({
            where: { documentId: id },
            select: ['id'],
        });
        const documentChunkIds = documentChunks.map((chunk) => chunk.id);
        await this.datapointDocumentChunkRepository.delete({
            documentChunkId: (0, typeorm_2.In)(documentChunkIds),
        });
        await this.documentChunkRepository.delete({ documentId: id });
        await this.documentRepository.delete({ id, workspaceId });
        if (fs.existsSync(document.path)) {
            fs.unlinkSync(document.path);
        }
    }
    async saveDocument({ originalname, path, workspaceId, userId, documentType, esrsCategory, year, month, day, remarks, pageNumbers, answerId, premiumParse, }) {
        const documentExists = await this.documentRepository.exists({
            where: { workspaceId, name: originalname },
        });
        if (documentExists) {
            throw new common_1.BadRequestException('Document with name already exists');
        }
        const upload = await this.documentRepository.save({
            workspaceId,
            createdBy: userId,
            path,
            name: originalname,
            documentType,
            esrsCategory,
            year,
            month,
            day,
            remarks,
        });
        await this.workspaceService.storeActionHistory({
            event: 'new_document_uploaded',
            ref: upload.id,
            workspaceId: workspaceId,
            versionData: {
                event: 'new_document_uploaded',
                doneBy: userId,
                data: upload,
            },
        });
        try {
            await this.chunkExtractionQueue.add(jobs_1.JobQueue.ChunkExtract, {
                documentId: upload.id,
                pageNumbers: pageNumbers,
                answerId: answerId,
                comment: remarks,
                premiumParse: premiumParse ?? true,
            }, {
                jobId: `chunkExtraction-${upload.id}`,
                attempts: 5,
                backoff: {
                    type: 'exponential',
                    delay: 5000,
                },
                removeOnComplete: env_helper_1.isDevelopment ? false : true,
            });
            this.logger.log(`Added job to queue for document chunk extraction: ${upload.id}`);
            this.documentRepository.update(upload.id, {
                status: document_entity_1.DocumentStatus.QueuedForExtraction,
            });
        }
        catch (error) {
            this.logger.error(`Error adding job to queue:`, error);
        }
        return upload;
    }
    async updateDocumentSettings({ id, workspaceId, userId, documentType, esrsCategory, year, month, day, remarks, }) {
        const document = await this.documentRepository.findOneOrFail({
            where: { id },
        });
        document.documentType = documentType;
        document.esrsCategory = esrsCategory;
        document.year = year;
        document.month = month;
        document.day = day;
        document.remarks = remarks;
        const update = await this.documentRepository.save(document);
        await this.workspaceService.storeActionHistory({
            event: 'document_settings_updated',
            ref: update.id,
            workspaceId: workspaceId,
            versionData: {
                event: 'document_settings_updated',
                doneBy: userId,
                data: update,
            },
        });
    }
    async extractDocumentChunks(id, premiumMode) {
        this.logger.log(`Extracting document chunks for document: ${id}`);
        const document = await this.documentRepository.findOneBy({
            id: id,
        });
        if (!document) {
            this.logger.error(`Document not found: ${id}`);
            return;
        }
        document.status = document_entity_1.DocumentStatus.ExtractingData;
        this.documentRepository.save(document);
        const chunks = await this.documentParserService.parseDocumentToMarkdown(document.path, premiumMode);
        for (const chunkContent of chunks) {
            const cleanChunk = chunkContent.text.replace(/\n/g, '').trim();
            if (cleanChunk !== '') {
                const chunk = await this.documentChunkRepository.save({
                    documentId: id,
                    page: chunkContent.metadata.pageNumber,
                    content: chunkContent.text,
                    metadataJson: JSON.stringify(chunkContent.metadata),
                });
                await this.indexChunkToPinecone({ document, chunk });
            }
        }
        document.status = document_entity_1.DocumentStatus.LinkingDataFinished;
        this.documentRepository.save(document);
    }
    countTokens(text) {
        return this.tokenizer.encode(text).length;
    }
    async splitTextIntoChunks(text, maxTokens = 500, overlap = 15, maxRetries = 3) {
        const totalTokens = this.countTokens(text);
        if (totalTokens <= maxTokens) {
            return [text];
        }
        try {
            const tokenSplitter = new textsplitters_1.TokenTextSplitter({
                chunkSize: maxTokens,
                chunkOverlap: overlap,
                encodingName: 'gpt2',
            });
            const chunks = await tokenSplitter.splitText(text);
            const validatedChunks = chunks.filter((chunk) => chunk.trim().length > 50);
            this.logger.log(`Successfully split text (${totalTokens} tokens) into ${validatedChunks.length} chunks using TokenTextSplitter`);
            return validatedChunks;
        }
        catch (error) {
            this.logger.warn(`TokenTextSplitter failed, falling back to RecursiveCharacterTextSplitter: ${error.message}`);
            return this.fallbackCharacterBasedSplitting(text, maxTokens, overlap, maxRetries);
        }
    }
    async fallbackCharacterBasedSplitting(text, maxTokens, overlap, maxRetries) {
        const totalTokens = this.countTokens(text);
        if (maxRetries <= 0) {
            this.logger.error('Maximum retries reached for text splitting. Truncating text.');
            const ratio = maxTokens / totalTokens;
            const truncatedLength = Math.floor(text.length * ratio * 0.9);
            const truncatedText = text.substring(0, truncatedLength);
            return [truncatedText];
        }
        const estimatedChunkSize = Math.floor((text.length * maxTokens) / totalTokens);
        const minChunkSize = Math.max(100, Math.floor(text.length / 10));
        const actualChunkSize = Math.max(estimatedChunkSize, minChunkSize);
        const textSplitter = new textsplitters_1.RecursiveCharacterTextSplitter({
            chunkSize: actualChunkSize,
            chunkOverlap: overlap,
            separators: ['\n\n', '\n', '. ', ' ', ''],
        });
        let chunks = await textSplitter.splitText(text);
        const validatedChunks = [];
        for (const chunk of chunks) {
            const chunkTokens = this.countTokens(chunk);
            if (chunkTokens <= maxTokens) {
                if (chunk.trim().length > 50) {
                    validatedChunks.push(chunk);
                }
            }
            else {
                this.logger.warn(`Chunk with ${chunkTokens} tokens exceeds limit (retry ${4 - maxRetries}/3)`);
                const subChunks = await this.fallbackCharacterBasedSplitting(chunk, maxTokens, Math.floor(overlap / 2), maxRetries - 1);
                validatedChunks.push(...subChunks);
            }
        }
        return validatedChunks;
    }
    async indexChunkToPinecone({ document, chunk, }) {
        if (!this.pineconeClient) {
            this.logger.error('Pinecone client not initialized');
            return;
        }
        try {
            const indexName = process.env.PINECONE_INDEX_NAME;
            const indexHost = process.env.PINECONE_INDEX_HOST;
            const namespace = process.env.PINECONE_NAMESPACE;
            if (!indexHost) {
                throw new Error('PINECONE_INDEX_HOST environment variable is required');
            }
            const index = this.pineconeClient.index(indexName, indexHost);
            const namespaceIndex = index.namespace(namespace);
            const textChunks = await this.splitTextIntoChunks(chunk.content);
            this.logger.log(`Split chunk ${chunk.id} into ${textChunks.length} sub-chunks for embedding`);
            const records = await Promise.all(textChunks.map(async (textChunk, index) => {
                try {
                    const tokenCount = this.countTokens(textChunk);
                    this.logger.debug(`Creating embedding for sub-chunk ${index} with ${tokenCount} tokens`);
                    if (tokenCount > 8191) {
                        throw new Error(`Text chunk exceeds Azure OpenAI token limit: ${tokenCount} > 8191`);
                    }
                    const embedding = await this.chatGptService.createEmbedding(textChunk);
                    if (embedding.length !== 3072) {
                        this.logger.warn(`Unexpected embedding dimensions: ${embedding.length}, expected 3072`);
                    }
                    return {
                        id: `${chunk.id}-${index}`,
                        values: embedding,
                        metadata: {
                            chunk_text: textChunk,
                            chunk_id: chunk.id,
                            document_id: document.id,
                            page: chunk.page.toString(),
                            workspace_id: document.workspaceId,
                            year: document.year?.toString() || '',
                            sub_chunk_index: index.toString() + '/' + textChunks.length.toString(),
                            token_count: tokenCount.toString(),
                        },
                    };
                }
                catch (embeddingError) {
                    this.logger.error(`Failed to create embedding for sub-chunk ${index}:`, embeddingError);
                    throw embeddingError;
                }
            }));
            await namespaceIndex.upsert(records);
            this.logger.log(`Successfully indexed ${records.length} sub-chunks for document chunk ${chunk.id} (${document.name}, page ${chunk.page})`);
        }
        catch (error) {
            this.logger.error(`Error indexing chunk ${chunk.id} to Pinecone:`, error);
            throw error;
        }
    }
    async deleteDocumentFromPinecone(documentId) {
        if (!this.pineconeClient) {
            this.logger.error('Pinecone client not initialized');
            return;
        }
        try {
            const indexName = process.env.PINECONE_INDEX_NAME || 'climate-documents';
            const indexHost = process.env.PINECONE_INDEX_HOST;
            const namespace = process.env.PINECONE_NAMESPACE || 'default';
            if (!indexHost) {
                throw new Error('PINECONE_INDEX_HOST environment variable is required');
            }
            const index = this.pineconeClient.index(indexName, indexHost);
            await index.deleteMany({
                filter: { document_id: documentId },
                namespace: namespace,
            });
            this.logger.log(`Successfully deleted all vectors for document ${documentId} from Pinecone`);
        }
        catch (error) {
            this.logger.error(`Error deleting document ${documentId} from Pinecone:`, error);
            throw error;
        }
    }
    async reindexDocument(documentId) {
        this.logger.log(`Starting reindex process for document: ${documentId}`);
        const document = await this.documentRepository.findOne({
            where: { id: documentId },
        });
        if (!document) {
            throw new common_1.NotFoundException(`Document not found`);
        }
        try {
            const chunks = await this.documentChunkRepository.find({
                where: { documentId },
                order: { page: 'ASC' },
            });
            this.logger.log(`Found ${chunks.length} chunks to reindex for document ${documentId}`);
            for (const chunk of chunks) {
                this.logger.log(`Reindexing chunk: ${chunk.id} (page ${chunk.page})`);
                await this.indexChunkToPinecone({ document, chunk });
            }
            this.logger.log(`Successfully completed reindex for document ${documentId}`);
        }
        catch (error) {
            this.logger.error(`Error during reindex of document ${documentId}:`, error);
            throw error;
        }
    }
    async bulkReindexDocuments(options) {
        const { documentIds, workspaceId } = options;
        let documentsToReindex;
        if (documentIds && documentIds.length > 0) {
            documentsToReindex = await this.documentRepository.find({
                where: { id: (0, typeorm_2.In)(documentIds) },
                select: ['id', 'name'],
            });
            this.logger.log(`Starting bulk reindex for ${documentsToReindex.length} specific documents`);
        }
        else if (workspaceId) {
            documentsToReindex = await this.documentRepository.find({
                where: { workspaceId },
                select: ['id', 'name'],
            });
            this.logger.log(`Starting bulk reindex for all ${documentsToReindex.length} documents in workspace ${workspaceId}`);
        }
        else {
            throw new common_1.BadRequestException('Either documentIds or workspaceId must be provided');
        }
        const results = {
            success: [],
            failed: [],
        };
        for (const document of documentsToReindex) {
            try {
                await this.reindexDocument(document.id);
                results.success.push(document.id);
                this.logger.log(`Successfully reindexed document: ${document.name} (${document.id})`);
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                results.failed.push({ id: document.id, error: errorMessage });
                this.logger.error(`Failed to reindex document: ${document.name} (${document.id})`, error);
            }
        }
        this.logger.log(`Bulk reindex completed. Success: ${results.success.length}, Failed: ${results.failed.length}`);
        return results;
    }
    async findDocumentChunkById(id) {
        const documentChunk = await this.documentChunkRepository.findOne({
            where: { id },
            relations: ['document'],
        });
        return documentChunk;
    }
    async deleteDocumentChunk({ id, workspaceId, userId, }) {
        const documentChunk = await this.documentChunkRepository.findOne({
            where: { id },
        });
        await this.workspaceService.storeActionHistory({
            event: 'document_chunk_deleted',
            ref: documentChunk.documentId,
            workspaceId: workspaceId,
            versionData: {
                event: 'document_chunk_deleted',
                doneBy: userId,
                data: documentChunk,
            },
        });
        await this.documentChunkRepository.delete({ id });
    }
    async getDocumentChunk(id) {
        const documentChunk = await this.documentChunkRepository.findOne({
            where: { id },
            relations: ['datapointDocumentChunkMap.datapointRequest'],
        });
        if (!documentChunk) {
            throw new common_1.NotFoundException(`Document Chunk not found`);
        }
        return documentChunk;
    }
    async createDocumentChunkMap({ documentChunkId, datapointRequestId, userId, }) {
        const documentChunk = await this.documentChunkRepository.findOne({
            where: { id: documentChunkId },
        });
        const datapointRequest = await this.datapointRepository.findOne({
            where: { id: datapointRequestId },
        });
        if (!datapointRequest || !documentChunk) {
            throw new common_1.NotFoundException(`Linking components not found`);
        }
        await this.datapointDocumentChunkRepository.save({
            documentChunk,
            datapointRequest,
            createdBy: userId,
        });
    }
    async updateDocumentChunkMap({ documentChunkId, datapointRequestId, userId, status, }) {
        const documentChunk = await this.datapointDocumentChunkRepository.findOne({
            where: {
                documentChunkId,
                datapointRequestId,
            },
        });
        if (!documentChunk) {
            if (status === false) {
                throw new common_1.NotFoundException(`Linking components not found`);
            }
            else {
                await this.createDocumentChunkMap({
                    documentChunkId,
                    datapointRequestId,
                    userId,
                });
            }
        }
        else {
            await this.datapointDocumentChunkRepository.update({
                id: documentChunk.id,
            }, {
                active: status,
                modifiedBy: userId,
            });
        }
    }
    async bulkUpdateDocumentChunkMap({ documentChunkId, userId, data, }) {
        for (const { datapointRequestId, linked } of data) {
            try {
                await this.updateDocumentChunkMap({
                    documentChunkId,
                    datapointRequestId,
                    userId,
                    status: linked,
                });
            }
            catch (error) {
                console.log(error);
            }
        }
    }
    async updateDocumentStatus(documentId, { status }) {
        await this.documentRepository.update(documentId, { status });
    }
    async findDocumentChunkByPage(documentId, pageNumber) {
        const chunks = await this.documentChunkRepository.find({
            where: {
                documentId,
            },
        });
        return chunks.find((chunk) => {
            const chunkPage = chunk.page;
            if (typeof chunkPage === 'string' &&
                (chunkPage.includes('-') || chunkPage.includes(','))) {
                const pageNumbers = (0, common_util_1.parsePageRanges)(chunkPage);
                return pageNumbers.includes(pageNumber);
            }
            return Number(chunkPage) === pageNumber;
        });
    }
    async saveLinkedDocumentChunk({ documentChunkId, answerId, comment = '', }) {
        return this.projectEcovadisLinkedDocumentChunksRepository.save({
            documentChunkId,
            answerId,
            comment,
        });
    }
};
exports.DocumentService = DocumentService;
exports.DocumentService = DocumentService = DocumentService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(document_chunk_entity_1.DocumentChunk)),
    __param(1, (0, typeorm_1.InjectRepository)(datapoint_document_chunk_entity_1.DatapointDocumentChunk)),
    __param(2, (0, typeorm_1.InjectRepository)(datapoint_request_entity_1.DatapointRequest)),
    __param(3, (0, typeorm_1.InjectRepository)(document_entity_1.Document)),
    __param(7, (0, bull_1.InjectQueue)(jobs_1.JobProcessor.ChunkExtraction)),
    __param(8, (0, typeorm_1.InjectRepository)(project_ecovadis_linked_document_chunks_entity_1.ProjectEcovadisLinkedDocumentChunks)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        document_parser_service_1.DocumentParserService,
        workspace_service_1.WorkspaceService,
        chat_gpt_service_1.ChatGptService, Object, typeorm_2.Repository])
], DocumentService);
//# sourceMappingURL=document.service.js.map