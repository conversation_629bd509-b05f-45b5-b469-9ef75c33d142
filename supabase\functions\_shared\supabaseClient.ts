// File: supabase/functions/_shared/supabaseClient.ts
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.4';

/**
 * Creates and returns a Supabase client instance
 * @param useServiceRole Whether to use the service role key (defaults to false)
 * @returns Supabase client instance
 */
export function getSupabaseClient(useServiceRole = false) {
  const supabaseUrl = Deno.env.get("SUPABASE_URL") ?? "";
  const supabaseKey = useServiceRole 
    ? Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    : Deno.env.get("SUPABASE_ANON_KEY") ?? "";
    
  return createClient(supabaseUrl, supabaseKey);
}