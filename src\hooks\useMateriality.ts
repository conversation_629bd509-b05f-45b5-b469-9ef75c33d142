import { useState, useEffect, useRef } from 'react';

import { EsrsTopic, TopicLevel } from '@/types/project';
import {
  fetchMaterialityStatus,
  fetchProjects,
  updateMaterialityStatus,
} from '@/api/project-settings/project-settings.api';
import { toast } from '@/components/ui/use-toast';

export function useMateriality() {
  const [data, setData] = useState<EsrsTopic[]>([]);
  const [checkedState, setCheckedState] = useState<{
    [id: number]: {
      level: TopicLevel;
      active: boolean;
    };
  }>({});
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [projectId, setProjectId] = useState<string | null>(null);
  const parentMapRef = useRef<{ [childId: number]: EsrsTopic }>({});

  useEffect(() => {
    const fetchData = async () => {
      try {
        const projects = await fetchProjects();
        if (projects.length === 0) {
          return;
        }
        setProjectId(projects[0].id);
        const { project, esrsTopics } = await fetchMaterialityStatus(
          projects[0].id
        );
        const sortedTopics = esrsTopics.sort((a, b) => a.id - b.id);
        setData(sortedTopics);

        const state: {
          [id: number]: {
            level: TopicLevel;
            active: boolean;
          };
        } = {};

        const materialTopicsMap = new Map<number, boolean>();
        project.materialTopics.forEach((topic) => {
          materialTopicsMap.set(topic.esrsTopicId, topic.active);
        });

        const initializeState = (items: EsrsTopic[], parent?: EsrsTopic) => {
          items.forEach((item) => {
            state[item.id] = {
              level: item.level,
              active: materialTopicsMap.get(item.id) || false,
            };

            if (parent) {
              parentMapRef.current[item.id] = parent;
            }

            if (item.children) {
              initializeState(item.children, item);
            }
          });
        };

        initializeState(esrsTopics);
        setCheckedState(state);
      } catch (error) {
        console.error('Error fetching materiality status:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleStatusChange = (item: EsrsTopic, checked: boolean) => {
    const newCheckedState = { ...checkedState };

    const updateItemAndChildren = (
      currentItem: EsrsTopic,
      isChecked: boolean
    ) => {
      newCheckedState[currentItem.id].active = isChecked;
      if (currentItem.children) {
        currentItem.children.forEach((child) => {
          updateItemAndChildren(child, isChecked);
        });
      }
    };

    updateItemAndChildren(item, checked);

    const updateParentStatus = (
      currentItem: EsrsTopic,
      newCheckedState: {
        [id: number]: {
          level: TopicLevel;
          active: boolean;
        };
      }
    ) => {
      const parent = parentMapRef.current[currentItem.id];
      if (!parent) {
        return;
      }

      // Parent is active if any of its children are active
      const isAnyChildActive = parent.children?.some(
        (child) => newCheckedState[child.id]
      );

      newCheckedState[parent.id].active = isAnyChildActive || false;

      // Recurse up to update ancestor parents
      updateParentStatus(parent, newCheckedState);
    };

    updateParentStatus(item, newCheckedState);

    setCheckedState(newCheckedState);
  };

  const handleSaveData = async () => {
    if (!projectId) {
      return;
    }
    setIsSaving(true);
    try {
      const materialTopics = Object.keys(checkedState).map((id) => ({
        esrsTopicId: Number(id),
        ...checkedState[Number(id)],
      }));

      await updateMaterialityStatus(projectId, materialTopics);

      toast({
        title: 'Materiality status saved',
      });
    } catch (error: any) {
      toast({
        title: 'Error saving materiality status',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  return {
    data,
    checkedState,
    isLoading,
    isSaving,
    handleSaveData,
    handleStatusChange,
  };
}
