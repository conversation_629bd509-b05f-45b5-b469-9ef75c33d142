import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1729021607243 implements MigrationInterface {
  name = 'SchemaUpdate1729021607243';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "data_request" ADD CONSTRAINT "FK_d05073e6e4886714a8cbbb72f03" FOREIGN KEY ("approvedBy") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "data_request" DROP CONSTRAINT "FK_d05073e6e4886714a8cbbb72f03"`,
    );
  }
}
