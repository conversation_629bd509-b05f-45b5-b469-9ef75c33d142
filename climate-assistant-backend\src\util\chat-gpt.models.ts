import {
  ChatCompletionMessageParam,
  ChatCompletionTool,
} from 'openai/resources';

export type CustomGptTool<T, S> =
  | CustomSyncGptTool<T, S>
  | CustomAsyncGptTool<T, S>
  | CustomReplacingGptTool<T, S>;

export interface CustomSyncGptTool<T, S> {
  type: 'sync-gpt-tool';
  toolDefinition: ChatCompletionTool;
  execute: (payload: T, messages: ChatCompletionMessageParam[]) => S;
}

export interface CustomAsyncGptTool<T, S> {
  type: 'async-gpt-tool';
  toolDefinition: ChatCompletionTool;
  execute: (payload: T, messages: ChatCompletionMessageParam[]) => Promise<S>;
}

export interface CustomReplacingGptTool<T, S> {
  type: 'replacing-gpt-tool';
  toolDefinition: ChatCompletionTool;
  execute: (
    payload: T,
    messages: ChatCompletionMessageParam[],
  ) => AsyncIterableIterator<S>;
}
