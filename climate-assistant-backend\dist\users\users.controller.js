"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("./users.service");
const swagger_1 = require("@nestjs/swagger");
const supabase_auth_guard_1 = require("../auth/supabase/supabase.auth.guard");
const roles_decorator_1 = require("../auth/roles.decorator");
const user_workspace_entity_1 = require("./entities/user-workspace.entity");
let UsersController = class UsersController {
    constructor(usersService) {
        this.usersService = usersService;
    }
    async getUserPromptSettings(req) {
        const userId = req.user.id;
        const context = await this.usersService.getUserPromptContext(userId);
        return {
            context,
        };
    }
    async getGeneratedPromptContext(req) {
        const userId = req.user.id;
        const context = await this.usersService.getGeneratedPromptContext(userId);
        return {
            context,
        };
    }
    async savePromptSettings(req, body) {
        const userId = req.user.id;
        const { context } = body;
        await this.usersService.saveUserPromptContext(userId, context);
        return { success: true };
    }
    async createUserCompanyWorkspace(body) {
        const { email, password, name } = body;
        const create = await this.usersService.createUserWithCompanyAndWorkspace({
            email,
            password,
            companyName: name,
        });
        return create;
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, common_1.UseGuards)(supabase_auth_guard_1.AuthGuard),
    (0, common_1.Get)('/prompt-context'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getUserPromptSettings", null);
__decorate([
    (0, common_1.UseGuards)(supabase_auth_guard_1.AuthGuard),
    (0, common_1.Get)('/generated-prompt-context'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getGeneratedPromptContext", null);
__decorate([
    (0, common_1.UseGuards)(supabase_auth_guard_1.AuthGuard),
    (0, common_1.Post)('/user-prompt-settings'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "savePromptSettings", null);
__decorate([
    (0, common_1.UseGuards)(supabase_auth_guard_1.AuthGuard),
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin),
    (0, common_1.Post)('/create-workspace'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "createUserCompanyWorkspace", null);
exports.UsersController = UsersController = __decorate([
    (0, swagger_1.ApiTags)('users'),
    (0, common_1.Controller)('users'),
    __metadata("design:paramtypes", [users_service_1.UsersService])
], UsersController);
//# sourceMappingURL=users.controller.js.map