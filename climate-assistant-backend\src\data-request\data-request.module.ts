import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataRequestService } from './data-request.service';
import { Workspace } from '../workspace/entities/workspace.entity';
import { DataRequestGuard } from './data-request.guard';
import { DataRequestController } from './data-request.controller';
import { DataRequest } from './entities/data-request.entity';
import { DatapointRequest } from '../datapoint/entities/datapoint-request.entity';
import { PromptModule } from 'src/prompts/prompts.module';
import { ProjectModule } from 'src/project/project.module';
import { UsersModule } from 'src/users/users.module';
import { WorkspaceModule } from 'src/workspace/workspace.module';
import { DatapointRequestModule } from 'src/datapoint/datapoint-request.module';
import { BullBoardModule } from '@bull-board/nestjs';
import { JobProcessor } from 'src/types/jobs';
import { BullAdapter } from '@bull-board/api/bullAdapter';
import { BullModule } from '@nestjs/bull';
import { DataRequestGeneration } from './entities/datarequest-generation.entity';
import { DatapointDataRequestSharedService } from 'src/shared/shared-datapoint-datarequest.service';
import { LlmRateLimiterModule } from 'src/llm-rate-limiter/llm-rate-limiter.module';
import { SupabaseAuthModule } from 'src/auth/supabase/supabase.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DataRequest,
      DatapointRequest,
      Workspace,
      DataRequestGeneration,
    ]),
    SupabaseAuthModule,
    PromptModule,
    ProjectModule,
    UsersModule,
    WorkspaceModule,
    LlmRateLimiterModule,
    BullModule.registerQueue({ name: JobProcessor.DatapointGeneration }),
    BullModule.registerQueue({ name: JobProcessor.DatapointReview }),
    BullBoardModule.forFeature({
      name: JobProcessor.DatapointGeneration,
      adapter: BullAdapter,
    }),
    BullBoardModule.forFeature({
      name: JobProcessor.DatapointReview,
      adapter: BullAdapter,
    }),
    forwardRef(() => DatapointRequestModule), //This is acircular dependency
  ],
  providers: [
    DataRequestService,
    DatapointDataRequestSharedService,
    DataRequestGuard,
  ],
  exports: [DataRequestService],
  controllers: [DataRequestController],
})
export class DataRequestModule {}
