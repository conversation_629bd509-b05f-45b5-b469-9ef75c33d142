export enum JobProcessor {
  ChunkExtraction = 'chunk-extraction',
  ChunkDpLinking = 'chunk-dp-linking',
  ChunkEcovadisLink = 'chunk-ecovadis-link',
  DatapointGeneration = 'datapoint-generation',
  DatapointReview = 'datapoint-review',
  LlmRequest = 'llm-request',
}

export enum JobQueue {
  ChunkExtract = 'chunk-extract',
  ChunkDpLink = 'chunk-dp-link',
  ChunkEcovadisLink = 'chunk-ecovadis-link',
  DatapointGenerate = 'datapoint-generate',
  DatapointReview = 'datapoint-review',
  LlmRequest = 'llm-request',
}
