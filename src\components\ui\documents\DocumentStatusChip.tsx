import { DocumentStatus } from '@/types/ecovadis';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Check } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface DocumentStatusChipProps {
  status: DocumentStatus;
  documentId?: string;
}

export function DocumentStatusChip({ status, documentId }: DocumentStatusChipProps) {
  const navigate = useNavigate();
  
  const getStatusColor = () => {
    switch (status) {
      case 'complete':
        return 'bg-[#f4fdf5] text-[#41997a] border border-[#41997a]/30 cursor-pointer';
      case 'processing':
        return 'bg-[#FFF8E6] text-[#D69E2E] border border-[#D69E2E]/30';
      case 'failed':
        return 'bg-[#ffefef] text-[#E53E3E] border border-[#E53E3E]/30';
      case 'invalid':
        return 'bg-[#FEF6EE] text-[#DD6B20] border border-[#DD6B20]/30';
      default:
        return 'bg-gray-100 text-gray-600 border border-gray-300';
    }
  };

  const handleClick = () => {
    if (documentId && status === 'complete') {
      navigate(`/documents/${documentId}?tab=links`);
    }
  };

  const renderStatusContent = () => {
    const badgeContent = (
      <Badge 
        variant="outline" 
        className={`${getStatusColor()} font-medium px-3 py-0.5 rounded-full`}
      >
        {status === 'processing' ? 'Processing' : 
         status === 'complete' ? 'Processed' : 
         status === 'failed' ? 'Failed' : 
         status === 'invalid' ? 'Invalid' : status}
      </Badge>
    );

    if (status === 'processing') {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>{badgeContent}</TooltipTrigger>
            <TooltipContent className="max-w-xs">
              Document is being processed by AI. (Extracting Data, automatically linking it to questions and checking formal criteria) This might take up to 1 hour.
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    if (status === 'complete') {
      return (
        <Badge 
          variant="outline" 
          className={`${getStatusColor()} px-3 py-0.5 rounded-full`}
          onClick={documentId ? handleClick : undefined}
        >
          <Check className="h-3 w-3 mr-1" />
          Processed
        </Badge>
      );
    }

    if (status === 'invalid') {
      return (
        <Badge 
          variant="outline" 
          className={`${getStatusColor()} font-medium`}
        >
          Invalid
        </Badge>
      );
    }

    return badgeContent;
  };

  return renderStatusContent();
}
