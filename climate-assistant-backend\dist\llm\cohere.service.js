"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CohereService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CohereService = void 0;
const common_1 = require("@nestjs/common");
const cohere_ai_1 = require("cohere-ai");
let CohereService = CohereService_1 = class CohereService {
    constructor() {
        this.logger = new common_1.Logger(CohereService_1.name);
        this.cohereClient = new cohere_ai_1.CohereClient({
            token: process.env.COHERE_API_KEY,
        });
    }
    async rerankResults(query, documents, topK = 10) {
        if (!process.env.COHERE_API_KEY) {
            this.logger.warn('COHERE_API_KEY not set, skipping reranking');
            return documents.slice(0, topK).map((doc, index) => ({
                ...doc,
                relevance_score: 1.0 - (index * 0.1),
                original_index: index,
            }));
        }
        try {
            const documentTexts = documents.map(doc => doc.content);
            this.logger.log(`Reranking ${documents.length} documents with query: "${query}"`);
            const response = await this.cohereClient.rerank({
                model: 'rerank-v3.5',
                query: query,
                documents: documentTexts,
                topN: Math.min(topK, documents.length),
                returnDocuments: false,
            });
            const rerankedResults = response.results.map(result => ({
                content: documents[result.index].content,
                relevance_score: result.relevanceScore,
                metadata: documents[result.index].metadata,
                original_index: result.index,
            }));
            this.logger.log(`Reranking completed, returned ${rerankedResults.length} results`);
            return rerankedResults;
        }
        catch (error) {
            this.logger.error('Error during reranking:', error);
            return documents.slice(0, topK).map((doc, index) => ({
                ...doc,
                relevance_score: 1.0 - (index * 0.1),
                original_index: index,
            }));
        }
    }
};
exports.CohereService = CohereService;
exports.CohereService = CohereService = CohereService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], CohereService);
//# sourceMappingURL=cohere.service.js.map