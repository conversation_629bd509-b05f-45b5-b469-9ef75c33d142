import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChatHistory } from './entities/chat-history.entity';
import { User } from '../users/entities/user.entity';
import { ChatMessage } from './entities/chat.message.entity';
import { HistoryCreationDto } from './entities/history-creation.dto';
import { UsersService } from '../users/users.service';
import { ChatGptService } from '../llm/chat-gpt.service';
import { ChatMessageDto, HistoryUpdateDto } from './entities/chat.message.dto';
import { KnowledgeBaseService } from '../knowledge-base/knowledge-base.service';
import { PerplexityService } from '../util/perplexity.service';
import { InitiativeSuggestionService } from './initiative-suggestion.service';
import { InitiativeDetailService } from './initiative-detail.service';
import { SearchEngineTool } from '../util/search-engine.tool';
import { LLM_MODELS } from 'src/constants';

@Injectable()
export class ChatService {
  constructor(
    @InjectRepository(ChatHistory)
    private chatHistoryRepository: Repository<ChatHistory>,
    @InjectRepository(ChatMessage)
    private chatMessageRepository: Repository<ChatMessage>,
    private usersService: UsersService,
    private readonly chatGptService: ChatGptService,
    private readonly knowledgeBaseService: KnowledgeBaseService,
    private readonly perplexityService: PerplexityService,
    private readonly initiativeSuggestionService: InitiativeSuggestionService,
    private readonly initiativeDetailService: InitiativeDetailService,
    private readonly searchEngineTool: SearchEngineTool,
  ) {}

  getChats(userId: string) {
    return this.chatHistoryRepository.find({
      where: { user: { id: userId } as User },
      order: { createdAt: 'DESC' },
    });
  }

  getChatHistory(id: string) {
    return this.chatHistoryRepository
      .findOne({
        where: { id },
        relations: ['messages'],
      })
      .then((history) => {
        return {
          ...history,
          messages: [...history.messages].sort(
            (a, b) => a.createdAt.getTime() - b.createdAt.getTime(),
          ),
        };
      });
  }

  updateChatHistory(id: string, updatedHistory: HistoryUpdateDto) {
    return this.chatHistoryRepository.update(
      {
        id,
      },
      { title: updatedHistory.title },
    );
  }

  deleteChatHistory(id: string) {
    return this.chatHistoryRepository.delete({
      id,
    });
  }

  async createEmptyHistory(userId: string, type: HistoryCreationDto['type']) {
    let systemMessageContent = '';
    let firstMessageContent = '';

    const context = await this.usersService.getUserPromptContext(userId);

    switch (type) {
      case 'initiativeCreation':
        systemMessageContent = systemPromptInitialInitiativeCreation(context);
        firstMessageContent = firstMessageInitiativeCreation;

        break;

      case 'regulatoryHelp':
        systemMessageContent = systemPromptISO50001(context);
        break;

      case 'csrdCreation':
        systemMessageContent = systemPromptCsrdCreation(context);
        firstMessageContent = firstMessageCsrdCreation;
        break;

      default:
        break;
    }

    const systemMessage: Pick<ChatMessage, 'role' | 'content'> = {
      role: 'system',
      content: systemMessageContent,
    };

    const firstMessage: Pick<ChatMessage, 'role' | 'content'> = {
      role: 'assistant',
      content: firstMessageContent,
    };

    const history = await this.chatHistoryRepository.create({});
    history.user = { id: userId } as User;

    const messages = [systemMessage, firstMessage];
    history.messages = messages.map((message) =>
      this.chatMessageRepository.create(message),
    );

    return this.chatHistoryRepository.save(history);
  }

  async addMessageToHistory(historyId: string, messages: ChatMessageDto[]) {
    const history = await this.chatHistoryRepository.findOneOrFail({
      where: { id: historyId },
      relations: ['messages'],
    });

    const newMessages = messages
      .filter((message) => message.id === undefined)
      .map((message) => this.chatMessageRepository.create(message));
    history.messages = [...history.messages, ...newMessages];

    await this.chatHistoryRepository.save(history);
  }

  async queryCompanyRelatedVectors({
    query,
    userId,
    count,
    threshold,
  }: {
    query: string;
    userId: User['id'];
    count: number;
    threshold: number;
  }) {
    // return this.usersService.getSimilarChunksWithVectorSearch(
    //   query,
    //   count,
    //   userId,
    //   threshold,
    // );

    // temporary dissociation
    return this.knowledgeBaseService.getSimilarChunksWithVectorSearch(
      query,
      count,
      threshold,
    );
  }

  async queryInternalRelatedVectors({
    query,
    count,
    threshold,
  }: {
    query: string;
    count: number;
    threshold: number;
  }) {
    return this.knowledgeBaseService.getSimilarChunksWithVectorSearch(
      query,
      count,
      threshold,
    );
  }

  async createMessageStream({
    messages,
    onMessage,
    userId,
  }: {
    messages: ChatMessageDto[];
    onMessage: (chunk: string) => void;
    userId: User['id'];
  }): Promise<string> {
    const lastQuestion = messages[messages.length - 1]?.content ?? '';

    const knowledgeBaseChunks =
      await this.knowledgeBaseService.getSimilarChunksWithVectorSearch(
        lastQuestion,
        3,
      );
    // const companyInfoChunks =
    //   await this.usersService.getSimilarChunksWithVectorSearch(
    //     lastQuestion,
    //     3,
    //     userId,
    //   );

    const context = await this.usersService.getUserPromptContext(userId);
    const contextMessage = {
      role: 'function',
      name: 'get_context',
      content: `
      Hier ist Kontext um die Frage zu beantworten:
      
      <Kontext_Unternehmen>${context}</Kontext_Unternehmen>    
      <Kontext_Vektor_Suche_Interne_Knowledgebase>${JSON.stringify(knowledgeBaseChunks)}</Kontext_Vektor_Suche_Interne_Knowledgebase>
      `,
      // <Kontext_Vektor_Suche_Interne_Kundendatenbank>${JSON.stringify(companyInfoChunks)}</Kontext_Vektor_Suche_Interne_Kundendatenbank>
    } as ChatMessageDto;

    const tools = [
      this.searchEngineTool.createSearchEngine(),
      this.initiativeSuggestionService.createInitiativeSuggestionCreator(
        userId,
      ),
      this.initiativeDetailService.createInitiativeDetailTool(userId),
    ];

    const stream = this.chatGptService.createCompletionWithFunctionsStream(
      LLM_MODELS['gpt-4o'],
      [...messages, contextMessage],
      tools,
    );

    let content = '';
    for await (const chunk of stream) {
      onMessage(chunk);
      content += chunk;
    }

    return content;
  }

  async generateHistoryTitle(id: string, question: string, answer: string) {
    const title = await this.chatGptService.createCompletion(
      LLM_MODELS['gpt-4o-mini'],
      [
        {
          role: 'user',
          content: `I'll give you question & answer that were sent to ChatGPT and you'll create me a short title (ideally less than 5 words) for the sidebar. Don't put things into quotations and don't use any punctuation marks.  \n Here's are the messages: \n Question: ${question} \n Answer: ${answer}`,
        },
      ],
    );
    await this.chatHistoryRepository.update(
      {
        id,
      },
      { title },
    );
  }

  async storeTitle(id: string, title: string) {
    return this.chatHistoryRepository.update(
      {
        id,
      },
      { title },
    );
  }
}

const systemPromptInitialInitiativeCreation = (context: string) => `
  Du bist erfahrene Beraterin spezialisiert auf Nachhaltigkeitsberatung von großen Unternehmen.
  Du begleitest seit über 10 Jahren Unternehmen und ihre Mitarbeitenden und Teams in der nachhaltigen Transformation und gibst systematische und konkrete Maßnahmen, wie Unternehmen erfolgreich und effizient ihre Nachhaltigkeitsziele erreichen können.
  Dabei orientierst du dich an den neuesten Regulatorien und Trends und bist stets engagiert, die Maßnahmen so praktikabel und greifbar wie möglich aufzubereiten, sodass der Mehrwert sofort ersichtlich ist.
  
  Bitte stelle zuerst die 3 erwähnten Fragen.
  Warte auf die Antwort bevor du die nächste Frage stellst.
  Generiere die Maßnahmenvorschläge erst wenn du mit allen Fragen durch ist
  
  Aufgaben:
  1. Für welchen Unternehmensbereich sollen Maßnahmen erarbeitet werden? (diese Frage wird automatisch gestellt)
  2. Gibt es bestimmte Fokusthemen für die wir Maßnahmen generieren sollen? [Warte auf Antwort]
  3. Hast du bestimmte Rahmenbedingungen oder Ziele, die die Maßnahmen erfüllen müssen? (verfügbares Budget, Umsetzungszeitraum, Emissionseinsparungsziele) [Warte auf Antwort]
  4. Generiere Maßnahmen Vorschläge für das Unternehmen - nutze dafür das Tool "initiative-suggestion-creator"
  5. (optional) Wenn der genaue Wortlaut "[T] Erkläre mir die Initiative [Nummer] genauer!" verwendet wird, nutze das Tool "initiative-detail-creator"
    - Ansonsten nicht! Achte vorallem auf das [T] zu Beginn. Verwende diese Information niemals im Chat
  6. Beantworte Follow Up Fragen. Nutze dafür die bereitgestellten Kontextinformationen die dir die Funktion "get_context" zur Verfügung stellt
    a. <Kontext_Unternehmen> - Kontextinformationen über das Unternehmen    
    b. <Kontext_Vektor_Suche_Interne_Knowledgebase> - Kontextinformationen aus einer Vektor-Suche in der internen Knowledgebase
    c. <Kontext_Vektor_Suche_Interne_Kundendatenbank> - Kontextinformationen aus einer Vektor-Suche in der internen Kundendatenbank
  
  Zusätzlich hast du folgende Tools zur Verfügung:
  - "get-up-to-date-infos-via-search-engine" - Sucht im Web nach der Antwort auf die Frage - verwende es wenn du aktuelle Infos zu beispielsweise Förderungen benötigst
  - "initiative-suggestion-creator" - Generiert Maßnahmen für das Unternehmen
  - "initiative-detail-creator" - Erstellt detaillierte Beschreibungen von einer gewünschten Maßnahme
  
  Verwende maximal 1 Tool pro Nachricht!
  
  Erkläre dem Nutzer NIEMALS, dass du ein Tool verwenden wirst oder welches Tool du verwenden wirst!
  
  Nutze NIEMALS links oder URLs in deinen Antworten!

  <Kontext_Unternehmen>${context}</Kontext_Unternehmen>
`;

const firstMessageInitiativeCreation = `
### Willkommen bei clima ai!

Für welchen Unternehmensbereich sollen Maßnahmen erarbeitet werden? 
Bitte gib an, ob es sich um unternehmensweite Maßnahmen oder um Maßnahmen für eine spezifische Abteilung oder Standort handelt.
`;

const systemPromptISO50001 = (context: string) => ` 
  Du bist ein erfahrener Berater, spezialisiert auf die Implementierung und Einhaltung der ISO 50001 Norm.
  Seit über 10 Jahren unterstützt du Unternehmen dabei, ihre Energiemanagementsysteme zu entwickeln und zu optimieren.
  Du bietest systematische und konkrete Maßnahmen, wie Unternehmen erfolgreich und effizient ihre Energieeffizienz verbessern und ihre Energiemanagementziele erreichen können.
  Dabei orientierst du dich an den neuesten Regulatorien und Best Practices und bist stets engagiert, die Maßnahmen so praktikabel und greifbar wie möglich aufzubereiten, sodass der Mehrwert sofort ersichtlich ist.
  
  
  
  Context Informationen über das Unternehmen:
  """
  ${context}
  """
  `;

const firstMessageCsrdCreation = `Für welchen ESRS-Standard sollen Offenlegungsanforderungen erarbeitet werden?\n\n<button className="markdown-esrs-selector" data-esrs="E1" data-dr="E1-1">E1-1</button>\n\n<button className="markdown-esrs-selector" data-esrs="S1" data-dr="S1-1">S1-1</button>`;

const systemPromptCsrdCreation = (
  context: string,
) => `You are a highly experienced sustainability consultant with over 20 years of expertise in advising companies in social topics. You specialize in the industry the company is operating in, and your role is to guide companies through complex sustainability reporting standards, ensuring compliance and best practices.

  Task:
  Your task is to apply the relevant sustainability standard for the company in the given use case. You must ensure that the report generated aligns with the specific disclosure requirements of the standard, and that it can be directly integrated into the company's sustainability report. All relevant disclosure requirements must be carefully considered to ensure the company meets its obligations under the standard.

  System Information:
  - Before generating recommendations or reports, the system will first inquire about the specific standard that needs to be applied and its requirements.
  - Once the standard is specified (e.g., ESRS S1-1 or any other relevant standard), the system will automatically refer to the respective requirements for accurate reporting.
  - The recommendations must adhere strictly to the standard’s guidelines, providing actionable insights and structured information that complies with the disclosure requirements.

  Process:
  - Confirm the specific sustainability standard or guideline to be applied (e.g., ESRS S1-1, ESRS E1-1, etc.).
  - Identify the relevant sections of the standard based on the company's context, industry, and focus areas. For this use the "find-relevant-sections" tool to extract the relevant sections from the company report.
  - Generate structured reporting that fulfills the specified disclosure requirements. Make sure the recommendations are practical, clear, and aligned with the company's sustainability goals.
  - Include company-specific context provided by the system in all your responses to ensure that the recommendations are tailored to the company’s operational and strategic environment.

  Tools and Context:
  You have the following tools available:
  - "find-relevant-sections" - Input requirement string to extract relevant sections from the company report by vector search.

  Never disclose to the user that a tool has been used or which tool has been applied. Ensure the recommendations are structured and ready to be incorporated into formal sustainability documentation.

  <Company_Context>
    ${context}
  </Company_Context>
`;
