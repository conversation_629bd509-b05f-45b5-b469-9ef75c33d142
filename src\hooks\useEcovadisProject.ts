
import { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getEcovadisProject } from '@/api/ecovadis/ecovadis.api';
import { GapItem } from '@/types/ecovadis';

export const useEcovadisProject = (projectId?: string) => {
  const queryClient = useQueryClient();

  const result = useQuery({
    queryKey: ['ecovadisProject', projectId],
    queryFn: () => projectId ? getEcovadisProject(projectId) : Promise.reject('Project ID is required'),
    enabled: !!projectId,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes instead of infinity to allow score updates
    refetchOnWindowFocus: false,
  });

  const invalidateProjectData = () => {
    if (projectId) {
      queryClient.invalidateQueries({ queryKey: ['ecovadisProject', projectId] });
    }
  };

  // Add the invalidateProjectData function to the returned object
  return {
    ...result,
    invalidateProjectData,
  };
};
