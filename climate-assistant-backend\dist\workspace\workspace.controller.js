"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkspaceController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const workspace_service_1 = require("./workspace.service");
const supabase_auth_guard_1 = require("../auth/supabase/supabase.auth.guard");
const user_workspace_entity_1 = require("../users/entities/user-workspace.entity");
const roles_decorator_1 = require("../auth/roles.decorator");
let WorkspaceController = class WorkspaceController {
    constructor(workspaceService) {
        this.workspaceService = workspaceService;
    }
    async getWorkspaceDetails(req) {
        const workspaceId = req.user.workspaceId;
        return await this.workspaceService.findById(workspaceId);
    }
    async getUsersByWorkspace(req) {
        const workspaceId = req.user.workspaceId;
        return await this.workspaceService.getUsersByWorkspace(workspaceId);
    }
    async getAllWorkspaces() {
        return await this.workspaceService.getAllWorkspaces();
    }
    async addUserToWorkspace(req, body) {
        const workspaceId = req.user.workspaceId;
        const { emails, role } = body;
        const canInviteUser = await this.workspaceService.canInviteUser(role, req.user);
        if (!canInviteUser) {
            throw new Error('You are not allowed to invite users for this role');
        }
        const origin = req.headers.origin;
        const failedEmails = [];
        const inviteAllUsersPromise = emails.map((email) => new Promise(async (resolve, reject) => {
            try {
                const user = await this.workspaceService.inviteUserToWorkspace({
                    inviteeEmail: req.user.email,
                    origin,
                    workspaceId,
                    email,
                    role,
                    shouldSendEmail: origin.endsWith('.glacier.eco'),
                });
                resolve(user);
            }
            catch (e) {
                failedEmails.push(email);
                reject(e);
            }
        }));
        await Promise.allSettled(inviteAllUsersPromise);
        if (failedEmails.length === emails.length) {
            return {
                failure: `Failed to invite ${failedEmails.length > 1 ? 'all' : ''} user${failedEmails.length > 1 ? 's' : ''}: ${failedEmails.join(' , ')}`,
            };
        }
        if (failedEmails.length > 0) {
            const invitedUsers = emails.length - failedEmails.length;
            return {
                success: `${invitedUsers} user${invitedUsers > 1 ? 's' : ''} invited successfully`,
                failure: `Failed to invite few users: ${failedEmails.join(' , ')} `,
            };
        }
        return {
            success: `${emails.length > 1 ? 'All' : ''} ${emails.length > 1 ? 'u' : 'U'}ser${emails.length > 1 ? 's' : ''} invited successfully`,
        };
    }
    async getWorkspaceById(workspaceId) {
        const workspace = await this.workspaceService.findById(workspaceId);
        return workspace;
    }
    async updateWorkspaceById(req, body) {
        const workspaceId = req.user.workspaceId;
        const updatedWorkspace = await this.workspaceService.updateById(workspaceId, body);
        return updatedWorkspace;
    }
    async updateCompanyDetail(req, body) {
        const workspaceId = req.user.workspaceId;
        const updatedWorkspace = await this.workspaceService.updateCompanyDetail(workspaceId, body);
        return updatedWorkspace;
    }
};
exports.WorkspaceController = WorkspaceController;
__decorate([
    (0, common_1.Get)('/'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "getWorkspaceDetails", null);
__decorate([
    (0, common_1.Get)('/users'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "getUsersByWorkspace", null);
__decorate([
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin),
    (0, common_1.Get)('/all-workspaces'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "getAllWorkspaces", null);
__decorate([
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin, user_workspace_entity_1.Role.AiContributor, user_workspace_entity_1.Role.WorkspaceAdmin),
    (0, common_1.Post)('/inviteUsers'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "addUserToWorkspace", null);
__decorate([
    (0, common_1.Get)('/:workspaceId'),
    __param(0, (0, common_1.Param)('workspaceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "getWorkspaceById", null);
__decorate([
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.AiContributor, user_workspace_entity_1.Role.SuperAdmin),
    (0, common_1.Put)('/'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "updateWorkspaceById", null);
__decorate([
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.AiContributor, user_workspace_entity_1.Role.SuperAdmin),
    (0, common_1.Put)('/company'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WorkspaceController.prototype, "updateCompanyDetail", null);
exports.WorkspaceController = WorkspaceController = __decorate([
    (0, swagger_1.ApiTags)('workspace'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.AuthGuard),
    (0, common_1.Controller)('workspace'),
    __metadata("design:paramtypes", [workspace_service_1.WorkspaceService])
], WorkspaceController);
//# sourceMappingURL=workspace.controller.js.map