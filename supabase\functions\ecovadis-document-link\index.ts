// @ts-expect-error TODO look into this later
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { authValidator } from "../_shared/authValidator.ts";
import { getSupabaseClient } from "../_shared/supabaseClient.ts";
import { parsePagesString } from '../_shared/utils.ts';

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Validate the user's authentication token
    const authResult = await authValidator(req);
    if (authResult.error) {
      return new Response(
        JSON.stringify({ error: authResult.error }),
        { 
          headers: { ...corsHeaders, "Content-Type": "application/json" }, 
          status: authResult.status || 401 
        }
      );
    }

    // Parse the request body - no longer need chunkIds
    const { operation, documentId, answerId, pages, comment } = await req.json();
    
    // Initialize Supabase client
    const supabase = getSupabaseClient(true);

    // Get user ID for tracking who attached the document
    const userId = authResult.user?.id;

    // Handle detach operation
    if (operation === "detach") {
      if (!documentId || !answerId) {
        return new Response(
          JSON.stringify({ error: "Missing required parameters: documentId and answerId" }),
          { 
            headers: { ...corsHeaders, "Content-Type": "application/json" }, 
            status: 400 
          }
        );
      }

      // Get all document chunks for the document first
      const { data: documentChunks, error: chunksError } = await supabase
        .from('document_chunk')
        .select('id')
        .eq('documentId', documentId);

      if (chunksError) {
        return new Response(
          JSON.stringify({ 
            error: "Failed to fetch document chunks", 
            details: chunksError.message 
          }),
          { 
            headers: { ...corsHeaders, "Content-Type": "application/json" }, 
            status: 500 
          }
        );
      }

      const documentChunkIds = documentChunks.map(chunk => chunk.id);

      // Delete all entries for the answer that belong to this document's chunks
      const { error } = await supabase
        .from('project_ecovadis_linked_document_chunks')
        .delete()
        .eq('answerId', answerId)
        .in('documentChunkId', documentChunkIds);

      if (error) {
        return new Response(
          JSON.stringify({ 
            error: "Failed to detach document", 
            details: error.message 
          }),
          { 
            headers: { ...corsHeaders, "Content-Type": "application/json" }, 
            status: 500 
          }
        );
      }

      return new Response(
        JSON.stringify({ success: true, message: "Document detached successfully" }),
        { 
          headers: { ...corsHeaders, "Content-Type": "application/json" }, 
          status: 200 
        }
      );
    }
    
    // Handle update operation
    else if (operation === "update") {
      if (!documentId || !answerId) {
        return new Response(
          JSON.stringify({ error: "Missing required parameters: documentId and answerId" }),
          { 
            headers: { ...corsHeaders, "Content-Type": "application/json" }, 
            status: 400 
          }
        );
      }

      try {
        // Step 1: Get all document chunks for the document first
        const { data: documentChunks, error: chunksError } = await supabase
          .from('document_chunk')
          .select('id, page, documentId')
          .eq('documentId', documentId)
          .order('page', { ascending: true }); // Order by page for easier processing

        if (chunksError) {
          return new Response(
            JSON.stringify({ 
              error: "Failed to fetch document chunks", 
              details: chunksError.message 
            }),
            { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
          );
        }
        //throw error if pages does not match the document chunks
        if (pages && !parsePagesString(pages).every(page => documentChunks.some(chunk => chunk.page === String(page)))) {
          return new Response(
            JSON.stringify({
              error: "Provided pages do not match the document's chunks"
            }),
            { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 400 }
          );
        }

        const allDocumentChunkIds = documentChunks.map(chunk => chunk.id);

        // Step 2: Get existing linked chunks for this answer that belong to this document
        const { data: existingLinks, error: existingError } = await supabase
          .from('project_ecovadis_linked_document_chunks')
          .select('documentChunkId, comment')
          .eq('answerId', answerId)
          .in('documentChunkId', allDocumentChunkIds);

        if (existingError) {
          return new Response(
            JSON.stringify({ 
              error: "Failed to fetch existing links", 
              details: existingError.message 
            }),
            { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
          );
        }

        // Backup existing comment (from first link that has one)
        const existingComment = existingLinks?.find(link => link.comment)?.comment || '';
        const currentChunkIds = existingLinks?.map(link => link.documentChunkId) || [];

        // Step 3: Parse page numbers from input
        const pageNumbers = parsePagesString(pages);

        if (chunksError) {
          return new Response(
            JSON.stringify({ 
              error: "Failed to fetch document chunks", 
              details: chunksError.message 
            }),
            { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
          );
        }

        // Step 4: Find target chunks based on the requested pages
        const targetChunks = documentChunks.filter(chunk => 
          pageNumbers.length === 0 || pageNumbers.includes(Number(chunk.page))
        );

        const targetChunkIds = targetChunks.map(chunk => chunk.id);

        // Step 5: Calculate chunks to keep, delete, and add
        const chunksToKeep = currentChunkIds.filter(id => targetChunkIds.includes(id));
        const chunksToDelete = currentChunkIds.filter(id => !targetChunkIds.includes(id));
        const chunksToAdd = targetChunkIds.filter(id => !currentChunkIds.includes(id));

        // Step 6: Delete chunks that are no longer needed
        if (chunksToDelete.length > 0) {
          const { error: deleteError } = await supabase
            .from('project_ecovadis_linked_document_chunks')
            .delete()
            .in('documentChunkId', chunksToDelete)
            .eq('answerId', answerId);

          if (deleteError) {
            return new Response(
              JSON.stringify({ 
                error: "Failed to delete outdated document links", 
                details: deleteError.message 
              }),
              { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
            );
          }
        }

        // Step 7: Add new chunks with user tracking and attachment source
        if (chunksToAdd.length > 0) {
          const chunksToAddData = targetChunks
            .filter(chunk => chunksToAdd.includes(chunk.id))
            .map(chunk => ({
              answerId: answerId,
              documentChunkId: chunk.id,
              comment: '', // Will update the comment separately
              created_by: userId,
              attachment_source: 'manual'
            }));

          const { error: insertError } = await supabase
            .from('project_ecovadis_linked_document_chunks')
            .insert(chunksToAddData);

          if (insertError) {
            return new Response(
              JSON.stringify({ 
                error: "Failed to create new document links", 
                details: insertError.message 
              }),
              { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
            );
          }
        }

        // Step 8: Update comment on the chunk with lowest page number
        if (targetChunks.length > 0) {
          // Find the chunk with the lowest page number among final linked chunks
          const lowestPageChunk = targetChunks
            .sort((a, b) => Number(a.page) - Number(b.page))[0];

          // Use provided comment, or fall back to existing comment
          const finalComment = comment !== undefined ? comment : existingComment;

          // Clear comments from all other chunks first
          if (targetChunkIds.length > 1) {
            const otherChunkIds = targetChunkIds.filter(id => id !== lowestPageChunk.id);
            
            if (otherChunkIds.length > 0) {
              const { error: clearError } = await supabase
                .from('project_ecovadis_linked_document_chunks')
                .update({ comment: '' })
                .in('documentChunkId', otherChunkIds)
                .eq('answerId', answerId);

              if (clearError) {
                console.error('Warning: Failed to clear comments from other chunks:', clearError.message);
              }
            }
          }

          // Update comment on the lowest page chunk
          const { error: updateError } = await supabase
            .from('project_ecovadis_linked_document_chunks')
            .update({ comment: finalComment })
            .eq('documentChunkId', lowestPageChunk.id)
            .eq('answerId', answerId);

          if (updateError) {
            return new Response(
              JSON.stringify({ 
                error: "Failed to update comment", 
                details: updateError.message 
              }),
              { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
            );
          }
        }

        return new Response(
          JSON.stringify({ 
            success: true, 
            message: "Document updated successfully",
            stats: {
              kept: chunksToKeep.length,
              added: chunksToAdd.length,
              deleted: chunksToDelete.length,
              total: targetChunkIds.length,
              commentAppliedToPage: targetChunks.length > 0 ? targetChunks[0].page : null
            }
          }),
          { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 200 }
        );
      } catch (error) {
        return new Response(
          JSON.stringify({ error: error.message }),
          { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
        );
      }
    }

    // If the operation is not supported
    return new Response(
      JSON.stringify({ error: `Operation '${operation}' not supported` }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" }, 
        status: 400 
      }
    );

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" }, 
        status: 500 
      }
    );
  }
});
