export declare function trimHtmlPreAndPostfix(llmResponse: string): string;
export declare function extractCitationsFromReportTextGeneration(input: string, documentChunksIndex: string[]): {
    reportText: string;
    citation: Record<string, {
        id: string;
        active: boolean;
        text?: string;
    }[]>;
};
export declare const CITATION_CLIENT_REGEX: RegExp;
export declare const MERGED_CELL_REGEX: RegExp;
