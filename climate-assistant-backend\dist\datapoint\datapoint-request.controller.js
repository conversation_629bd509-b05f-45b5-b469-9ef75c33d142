"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatapointRequestController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const datapoint_request_service_1 = require("./datapoint-request.service");
const common_2 = require("@nestjs/common");
const datapoint_request_guard_1 = require("./datapoint-request.guard");
const roles_decorator_1 = require("../auth/roles.decorator");
const user_workspace_entity_1 = require("../users/entities/user-workspace.entity");
const supabase_auth_guard_1 = require("../auth/supabase/supabase.auth.guard");
const shared_datapoint_datarequest_service_1 = require("../shared/shared-datapoint-datarequest.service");
let DatapointRequestController = class DatapointRequestController {
    constructor(datapointRequestService, datapointDataRequestSharedService) {
        this.datapointRequestService = datapointRequestService;
        this.datapointDataRequestSharedService = datapointDataRequestSharedService;
    }
    async getDataRequest(datapointRequestId) {
        return await this.datapointRequestService.findData(datapointRequestId);
    }
    async getMaterialTopics(datapointRequestId) {
        return await this.datapointRequestService.loadMaterialTopics(datapointRequestId);
    }
    async updateDatapointRequest(datapointRequestId, updateDatapointRequestPayload, req) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        const datapointRequest = await this.datapointRequestService.update({
            datapointRequestId,
            updateDatapointRequestPayload,
            userId,
            workspaceId,
        });
        return datapointRequest;
    }
    async reviewContentWithAi(datapointRequestId, req) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        const datapointRequest = req.datapointRequest;
        return await this.datapointDataRequestSharedService.addDatapointToReviewQueue({
            datapointRequest,
            userId,
            workspaceId,
        });
    }
    async generateContentWithAi(datapointRequestId, additionalData, req) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        const datapointRequest = req.datapointRequest;
        if (additionalData.additionalReportTextGenerationRules) {
            await this.datapointRequestService.update({
                datapointRequestId: datapointRequestId,
                updateDatapointRequestPayload: {
                    customUserRemark: additionalData.additionalReportTextGenerationRules,
                },
                userId,
                workspaceId,
            });
        }
        return await this.datapointDataRequestSharedService.addDatapointToGenerationQueue({
            datapointRequest,
            userId,
            workspaceId,
            useExistingReportTextForReference: additionalData.useExistingReportText,
        });
    }
    async getDataRequestCitations(datapointRequestId, { citationId }) {
        return await this.datapointRequestService.loadDatapointCitations(datapointRequestId, citationId);
    }
    async updateDataRequestCitations(datapointRequestId, payload, req) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        const updatedDatapointRequest = await this.datapointRequestService.updateContentAndReplaceCitation({
            datapointRequestId,
            citationId: payload.citationId,
            index: payload.index,
            userId,
            workspaceId,
        });
        return updatedDatapointRequest;
    }
    async updateDatapointGenerationStatus(datapointGenerationId, req, payload) {
        return await this.datapointRequestService.updateGenerationStatus({
            datapointGenerationId,
            status: payload.status,
            userId: req.user.id,
            workspaceId: req.user.workspaceId,
        });
    }
    async getDocumentLinksForDatapointRequest(datapointRequestId) {
        return await this.datapointRequestService.loadDocumentLinks(datapointRequestId);
    }
};
exports.DatapointRequestController = DatapointRequestController;
__decorate([
    (0, common_2.UseGuards)(datapoint_request_guard_1.DatapointRequestGuard),
    (0, common_1.Get)('/:datapointRequestId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific datapoint request by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "getDataRequest", null);
__decorate([
    (0, common_2.UseGuards)(datapoint_request_guard_1.DatapointRequestGuard),
    (0, common_1.Get)('/:datapointRequestId/material-topics'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get material topics specific datapoint request by ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'All topics related to datapoint fetched successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "getMaterialTopics", null);
__decorate([
    (0, common_2.UseGuards)(datapoint_request_guard_1.DatapointRequestGuard),
    (0, common_1.Put)('/:datapointRequestId'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a specific datapoint request by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request updated successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "updateDatapointRequest", null);
__decorate([
    (0, common_2.UseGuards)(datapoint_request_guard_1.DatapointRequestGuard),
    (0, common_1.Post)('/:datapointRequestId/review-with-ai'),
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin, user_workspace_entity_1.Role.WorkspaceAdmin, user_workspace_entity_1.Role.AiContributor, user_workspace_entity_1.Role.AiReviewer, user_workspace_entity_1.Role.Contributor),
    (0, swagger_1.ApiOperation)({ summary: 'Review datapoint request content with AI' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request content reviewed successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "reviewContentWithAi", null);
__decorate([
    (0, common_2.UseGuards)(datapoint_request_guard_1.DatapointRequestGuard),
    (0, common_1.SetMetadata)('customCheck', 'generateWithAI'),
    (0, common_1.Post)('/:datapointRequestId/generate-with-ai'),
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin, user_workspace_entity_1.Role.WorkspaceAdmin, user_workspace_entity_1.Role.AiContributor, user_workspace_entity_1.Role.Contributor),
    (0, swagger_1.ApiOperation)({ summary: 'Generate datapoint request content with AI' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request content generated successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Function, Object]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "generateContentWithAi", null);
__decorate([
    (0, common_1.Get)('/:datapointRequestId/citations'),
    (0, swagger_1.ApiOperation)({ summary: 'Get citations specific datapoint request by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "getDataRequestCitations", null);
__decorate([
    (0, common_2.UseGuards)(datapoint_request_guard_1.DatapointRequestGuard),
    (0, common_1.Put)('/:datapointRequestId/citations'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update citations specific datapoint request by ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request citations updated successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "updateDataRequestCitations", null);
__decorate([
    (0, roles_decorator_1.Roles)(user_workspace_entity_1.Role.SuperAdmin),
    (0, common_1.Put)('/generation-status/:datapointGenerationId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update generation status specific datapoint request by ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request generation status updated successfully',
    }),
    __param(0, (0, common_1.Param)('datapointGenerationId')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "updateDatapointGenerationStatus", null);
__decorate([
    (0, common_2.UseGuards)(datapoint_request_guard_1.DatapointRequestGuard),
    (0, common_1.Get)('/:datapointRequestId/document-links'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get document links specific datapoint request by ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request document links fetched successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "getDocumentLinksForDatapointRequest", null);
exports.DatapointRequestController = DatapointRequestController = __decorate([
    (0, swagger_1.ApiTags)('Data Request'),
    (0, common_2.UseGuards)(supabase_auth_guard_1.AuthGuard),
    (0, common_1.Controller)('datapoint-request'),
    __metadata("design:paramtypes", [datapoint_request_service_1.DatapointRequestService,
        shared_datapoint_datarequest_service_1.DatapointDataRequestSharedService])
], DatapointRequestController);
//# sourceMappingURL=datapoint-request.controller.js.map