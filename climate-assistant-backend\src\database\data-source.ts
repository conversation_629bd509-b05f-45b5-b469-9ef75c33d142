import * as dotenv from 'dotenv';
import { createDataSourceWithVectorSupport } from '../env-helper';

dotenv.config({ path: '../.env' });

export default createDataSourceWithVectorSupport({
  type: 'postgres',
  url: process.env.BACKEND_DB_URL,
  synchronize: false,
  dropSchema: false,
  logging: false,
  logger: 'file',
  entities: ['src/**/*.entity{.ts,.js}'],
  migrations: ['src/database/migrations/**/*.ts'],
  migrationsTableName: 'migration_table',
});
