import { Injectable, Logger } from '@nestjs/common';
import { DatapointRequestData } from 'src/data-request/entities/data-request.dto';
import { DatapointQueueStatus } from 'src/datapoint/entities/datapoint-request.entity';
import { DatapointRequestService } from 'src/datapoint/datapoint-request.service';
import { JobProcessor, JobQueue } from 'src/types/jobs';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';
import { isDevelopment } from 'src/env-helper';

@Injectable()
export class DatapointDataRequestSharedService {
  constructor(
    private readonly datapointRequestService: DatapointRequestService,
    @InjectQueue(JobProcessor.DatapointGeneration)
    private readonly datapointGenerationQueue: Queue,
    @InjectQueue(JobProcessor.DatapointReview)
    private readonly datapointReviewQueue: Queue
  ) {}

  private readonly logger = new Logger(DatapointDataRequestSharedService.name);

  async addDatapointToGenerationQueue({
    datapointRequest,
    userId,
    workspaceId,
    useExistingReportTextForReference,
  }: {
    datapointRequest: DatapointRequestData;
    userId: string;
    workspaceId: string;
    useExistingReportTextForReference: boolean;
  }): Promise<void> {
    try {
      await this.datapointRequestService.updateQueueStatus({
        datapointRequestId: datapointRequest.id,
        queueStatus: DatapointQueueStatus.QueuedForGeneration,
      });
      await this.datapointGenerationQueue.add(
        JobQueue.DatapointGenerate,
        {
          dataRequestId: datapointRequest.dataRequestId,
          datapointRequestId: datapointRequest.id,
          userId,
          workspaceId,
          useExistingReportTextForReference,
        },
        {
          attempts: 5,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
          removeOnComplete: isDevelopment ? false : true,
        }
      );
    } catch (error) {
      this.logger.error(
        `Error generating datapoint ${datapointRequest.id} for data request ${datapointRequest.dataRequestId}: ${error}`
      );
      await this.datapointRequestService.update({
        datapointRequestId: datapointRequest.id,
        updateDatapointRequestPayload: datapointRequest,
        userId,
        workspaceId,
      });
    }
  }

  async addDatapointToReviewQueue({
    datapointRequest,
    userId,
    workspaceId,
  }: {
    datapointRequest: DatapointRequestData;
    userId: string;
    workspaceId: string;
  }): Promise<void> {
    try {
      await this.datapointRequestService.updateQueueStatus({
        datapointRequestId: datapointRequest.id,
        queueStatus: DatapointQueueStatus.QueuedForReview,
      });

      await this.datapointReviewQueue.add(
        JobQueue.DatapointReview,
        {
          dataRequestId: datapointRequest.dataRequestId,
          datapointRequestId: datapointRequest.id,
          userId,
          workspaceId,
        },
        {
          attempts: 5,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
          removeOnComplete: isDevelopment ? false : true,
        }
      );
    } catch (error) {
      this.logger.error(
        `Error reviewing datapoint ${datapointRequest.id} for data request ${datapointRequest.dataRequestId}: ${error}`
      );
      await this.datapointRequestService.update({
        datapointRequestId: datapointRequest.id,
        updateDatapointRequestPayload: datapointRequest,
        userId,
        workspaceId,
      });
    }
  }
}
