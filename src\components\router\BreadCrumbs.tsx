
import { Link, useLocation, useMatches } from 'react-router-dom';

interface BreadcrumbMatch {
  id: string;
  pathname: string;
  params: Record<string, string>;
  data: unknown;
  handle?: {
    breadcrumb?: string;
  };
}

export const Breadcrumbs = () => {
  const location = useLocation();
  const matches = useMatches() as BreadcrumbMatch[];

  if (location.pathname === '/') {
    return null;
  }

  return (
    <nav className={`my-4 mb-5 flex font-medium breadcrumbs`}>
      {matches
        .filter(match => match.handle?.breadcrumb)
        .map((match, index) => {
          const to = match.pathname;
          const isLast = index === matches.length - 1;

          return (
            <div key={to} className={`flex items-center`}>
              {index === 0 && <div className={`text-sm text-slate-500 mr-1`}>Home</div>}

              {index > 0 && <div className={`mx-1 opacity-60`}>/</div>}

              {!isLast && (
                <Link className={`text-sm`} to={to}>
                  {match.handle?.breadcrumb}
                </Link>
              )}

              {isLast && (
                <div className={`text-sm text-slate-500`}>
                  {match.handle?.breadcrumb}
                </div>
              )}
            </div>
          );
        })}
    </nav>
  );
};
