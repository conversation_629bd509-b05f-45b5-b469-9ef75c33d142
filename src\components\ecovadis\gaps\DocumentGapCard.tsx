
import React from 'react';
import { FileText, FileQuestion } from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { GapItem } from '@/types/ecovadis';
import { GapCard } from './GapCard';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Chip } from '@/components/ui/chip';

interface DocumentGapCardProps {
  documentName: string;
  documentId: string;
  gaps: GapItem[];
  onMarkAsComplete: (id: string, complete: boolean) => void;
  onFixGapClick: (gap: GapItem) => void;
  onAssignUser?: (id: string, userId: string | null) => void;
  users?: Array<{ id: string, name: string }>;
  currentActionGapId?: string;
}

export const DocumentGapCard: React.FC<DocumentGapCardProps> = ({
  documentName,
  documentId,
  gaps,
  onMarkAsComplete,
  onFixGapClick,
  onAssignUser,
  users = [],
  currentActionGapId
}) => {
  const isNoDocumentGroup = documentId === "no-document";
  
  return (
    <Card className="mb-4">
      <CardHeader className="p-4 border-b">
        <div className="flex items-center gap-2">
          {isNoDocumentGroup ? (
            <FileQuestion className="h-5 w-5 text-amber-500" />
          ) : (
            <FileText className="h-5 w-5 text-glacier-darkBlue" />
          )}
          <span className="font-medium text-lg">{documentName}</span>
          <Badge variant="outline" className="ml-2">{gaps.length} gap{gaps.length !== 1 ? 's' : ''}</Badge>
        </div>
      </CardHeader>
      <Collapsible open={true}>
        <CollapsibleContent>
          <CardContent className="p-4">
          <div className="flex items-center mb-4">
            <FileText className="h-5 w-5 text-gray-500" />
            <span className="ml-2 text-sm font-medium text-glacier-darkBlue">{documentName}</span>
            <Chip className="ml-2" variant="blue">
              {gaps.length} gap(s)
            </Chip>
          </div>
            <div className="space-y-3">
              {gaps.map((gap) => (
                <GapCard
                  key={gap.id}
                  gap={gap}
                  onMarkAsComplete={onMarkAsComplete}
                  onAssignUser={onAssignUser}
                  users={users}
                  onFixGapClick={() => onFixGapClick(gap)}
                  currentActionGapId={currentActionGapId}
                />
              ))}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};
