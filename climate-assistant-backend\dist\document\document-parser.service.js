"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DocumentParserService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentParserService = void 0;
const common_1 = require("@nestjs/common");
require("dotenv/config");
const textsplitters_1 = require("@langchain/textsplitters");
const tiktoken_1 = require("@dqbd/tiktoken");
const XLSX = require("xlsx");
const constants_1 = require("../constants");
const llamaparse_service_1 = require("../llm/llamaparse.service");
const llm_rate_limiter_service_1 = require("../llm-rate-limiter/llm-rate-limiter.service");
const azure_docintelligence_service_1 = require("../llm/azure-docintelligence.service");
let DocumentParserService = DocumentParserService_1 = class DocumentParserService {
    constructor(llmRateLimitService) {
        this.llmRateLimitService = llmRateLimitService;
        this.tokenizer = (0, tiktoken_1.encoding_for_model)('gpt-3.5-turbo');
        this.logger = new common_1.Logger(DocumentParserService_1.name);
    }
    countTokens(text) {
        return this.tokenizer.encode(text).length;
    }
    parseMarkdownTable(tableLines) {
        this.logger.log(`Parsing markdown table with ${tableLines.length} lines`);
        const separatorLineIndex = tableLines.findIndex((line) => line.trim().includes('---') &&
            /^\|(\s*[-]+\s*\|)+\s*$/.test(line.trim()));
        if (separatorLineIndex <= 0) {
            this.logger.log('Table format appears to use single header row');
            const headersLine = tableLines[0];
            const dataLines = tableLines.slice(2);
            const headers = [
                headersLine
                    .trim()
                    .split('|')
                    .map((header) => header.trim())
                    .filter((header) => header.length > 0),
            ];
            const rows = dataLines.map((line) => {
                return line
                    .trim()
                    .split('|')
                    .map((cell) => cell.trim())
                    .filter((cell) => cell.length > 0);
            });
            const rowsWithContent = rows.filter((row) => row.some((cell) => cell !== ''));
            this.logger.log(`Table parsed: ${headers.length} header rows, ${rowsWithContent.length} data rows`);
            return { headers, rows: rowsWithContent };
        }
        const headerLines = tableLines.slice(0, separatorLineIndex);
        const dataLines = tableLines.slice(separatorLineIndex + 1);
        const headers = headerLines.map((line) => line
            .trim()
            .split('|')
            .map((header) => header.trim())
            .filter((header) => header.length > 0));
        const rows = dataLines.map((line) => {
            return line
                .trim()
                .split('|')
                .map((cell) => cell.trim())
                .filter((cell) => cell.length > 0);
        });
        const rowsWithContent = rows.filter((row) => row.some((cell) => cell !== ''));
        this.logger.log(`Table parsed: ${headers.length} header rows, ${rowsWithContent.length} data rows`);
        return { headers, rows: rowsWithContent };
    }
    async parseDocumentToMarkdown(path, premiumMode) {
        this.logger.log(`Starting to parse document: ${path}, premium mode: ${premiumMode}`);
        if (path.endsWith('.pdf')) {
            this.logger.log(`Detected PDF document: ${path}`);
            return this.parsePageBasedPDFToMarkdown(path, premiumMode);
        }
        else if (path.endsWith('.xlsx') ||
            path.endsWith('.xls') ||
            path.endsWith('.csv')) {
            this.logger.log(`Detected spreadsheet document: ${path}`);
            return this.parseSpreadsheetToMarkdown(path);
        }
        else {
            this.logger.log(`Unsupported file type: ${path}`);
            throw new Error('Unsupported file type');
        }
    }
    async processCurrentChunk({ currentChunk, currentChunkTokens, currentChunkPageNumbers, chunkDataArray, chunkHeadingHierarchy, sourceDocumentName, mainSections, chunkNumber, }) {
        this.logger.log(`Processing chunk #${chunkNumber}, ${currentChunkTokens} tokens, pages: ${Array.from(currentChunkPageNumbers).join(',')}`);
        const pageNumbersArray = Array.from(currentChunkPageNumbers).sort((a, b) => a - b);
        let pageNumberMetadata = '';
        if (pageNumbersArray.length === 1) {
            pageNumberMetadata = `${pageNumbersArray[0]}`;
        }
        else {
            const isConsecutive = pageNumbersArray.every((num, idx) => idx === 0 || num === pageNumbersArray[idx - 1] + 1);
            if (isConsecutive) {
                pageNumberMetadata = `${pageNumbersArray[0]}-${pageNumbersArray[pageNumbersArray.length - 1]}`;
            }
            else {
                pageNumberMetadata = pageNumbersArray.join(',');
            }
        }
        if (currentChunkTokens > 3000) {
            const htmlTableInfo = this.analyzeHtmlTables(currentChunk);
            this.logger.log(`Large chunk contains HTML tables: ${htmlTableInfo.hasTables}, table percentage: ${htmlTableInfo.tablePercentage.toFixed(2)}`);
            if (htmlTableInfo.hasTables && htmlTableInfo.tablePercentage > 0) {
                chunkDataArray.push({
                    text: currentChunk,
                    metadata: {
                        headings: [...chunkHeadingHierarchy],
                        sourceDocumentName: sourceDocumentName,
                        mainSections: [...mainSections],
                        pageNumber: pageNumberMetadata,
                        chunkNumber: chunkNumber,
                    },
                });
                chunkNumber++;
            }
            else {
                const markdownTextSplitter = new textsplitters_1.MarkdownTextSplitter({
                    chunkSize: 3000,
                    chunkOverlap: 0,
                    keepSeparator: true,
                });
                const splitChunks = await markdownTextSplitter.splitText(currentChunk);
                for (const splitChunk of splitChunks) {
                    chunkDataArray.push({
                        text: splitChunk,
                        metadata: {
                            headings: [...chunkHeadingHierarchy],
                            sourceDocumentName: sourceDocumentName,
                            mainSections: [...mainSections],
                            pageNumber: pageNumberMetadata,
                            chunkNumber: chunkNumber,
                        },
                    });
                    chunkNumber++;
                }
            }
        }
        else {
            this.logger.log(`Adding regular chunk #${chunkNumber}`);
            chunkDataArray.push({
                text: currentChunk,
                metadata: {
                    headings: [...chunkHeadingHierarchy],
                    sourceDocumentName: sourceDocumentName,
                    mainSections: [...mainSections],
                    pageNumber: pageNumberMetadata,
                    chunkNumber: chunkNumber,
                },
            });
            chunkNumber++;
        }
        this.logger.log(`Finished processing chunk, new chunk number: ${chunkNumber}`);
        return chunkNumber;
    }
    enhanceHtmlWithStyles(text) {
        this.logger.log(`Enhancing HTML with styles, input length: ${text.length}`);
        const result = text
            .replace(/<ul(?!\s+class=)/g, '<ul class="ml-6 list-disc"')
            .replace(/<ol(?!\s+class=)/g, '<ol class="ml-6 list-decimal"')
            .replace(/<table(?!\s+class=)/g, '<table class="table-auto border-collapse w-full text-left"')
            .replace(/<thead(?!\s+class=)/g, '<thead class="bg-slate-200"')
            .replace(/<th(?!\s+class=|\s+colspan|\s+rowspan)/g, '<th class="px-4 py-2 font-medium text-slate-700"')
            .replace(/<tbody(?!\s+class=)/g, '<tbody class="bg-white"')
            .replace(/<td(?!\s+class=|\s+colspan|\s+rowspan)/g, '<td class="border px-4 py-2"');
        this.logger.log(`HTML enhanced with styles, output length: ${result.length}`);
        return result;
    }
    analyzeHtmlTables(text) {
        this.logger.log(`Analyzing HTML tables in text of length: ${text.length}`);
        const result = {
            hasTables: false,
            tableCount: 0,
            tablePercentage: 0,
            tableRanges: [],
        };
        const openTags = [];
        const tableRanges = [];
        let tableOpens = 0;
        let tableCloses = 0;
        const openTagRegex = /<table[^>]*>/gi;
        const closeTagRegex = /<\/table>/gi;
        let match;
        while ((match = openTagRegex.exec(text)) !== null) {
            openTags.push(match.index);
            tableOpens++;
        }
        while ((match = closeTagRegex.exec(text)) !== null) {
            if (openTags.length > 0) {
                const start = openTags.shift();
                const end = match.index + 8;
                tableRanges.push({ start, end });
                tableCloses++;
            }
        }
        let totalTableLength = 0;
        for (const range of tableRanges) {
            totalTableLength += range.end - range.start;
        }
        result.hasTables = tableRanges.length > 0;
        result.tableCount = tableRanges.length;
        result.tablePercentage =
            text.length > 0 ? totalTableLength / text.length : 0;
        result.tableRanges = tableRanges;
        this.logger.log(`Table analysis complete: found ${result.tableCount} tables, coverage: ${(result.tablePercentage * 100).toFixed(2)}%`);
        return result;
    }
    joinLinesWithHtmlTables(lines) {
        this.logger.log(`Joining lines with HTML tables, processing ${lines.length} lines`);
        const joinedLines = [];
        let currentTableLines = [];
        let tableLevel = 0;
        for (const line of lines) {
            const openTagMatches = line.match(/<table[^>]*>/gi);
            const openTagCount = openTagMatches ? openTagMatches.length : 0;
            const closeTagMatches = line.match(/<\/table>/gi);
            const closeTagCount = closeTagMatches ? closeTagMatches.length : 0;
            tableLevel += openTagCount - closeTagCount;
            tableLevel = Math.max(0, tableLevel);
            if (tableLevel > 0 || openTagCount > 0) {
                currentTableLines.push(line);
                if (tableLevel === 0) {
                    joinedLines.push(currentTableLines.join('\n'));
                    currentTableLines = [];
                }
            }
            else {
                joinedLines.push(line);
            }
        }
        if (currentTableLines.length > 0) {
            joinedLines.push(currentTableLines.join('\n'));
        }
        this.logger.log(`Joined HTML tables, resulting in ${joinedLines.length} lines`);
        return joinedLines;
    }
    async parsePageBasedPDFToMarkdown(filePath, premiumMode) {
        this.logger.log(`Parsing PDF document by page with overlap: ${filePath}, premium mode: ${premiumMode}`);
        const allDocumentMarkdown = await (0, azure_docintelligence_service_1.parseDocumentWithAzureDocumentIntelligence)({
            filePath,
            features: premiumMode ? ['ocrHighResolution'] : [],
        });
        this.logger.log(`Received parsed document from Azure Document Intelligence`);
        const sourceDocumentName = filePath.split('/').pop();
        const pages = allDocumentMarkdown.text.split('\n<!-- PageBreak -->\n');
        this.logger.log(`Document split into ${pages.length} pages`);
        const chunkDataArray = [];
        let chunkNumber = 1;
        for (let pageIndex = 0; pageIndex < pages.length; pageIndex++) {
            let pageContent = pages[pageIndex];
            const currentPageNumber = pageIndex + 1;
            const headingHierarchy = [];
            const mainSections = [];
            const headingRegex = /^(#{1,6})\s+(.*)$/gm;
            let match;
            while ((match = headingRegex.exec(pageContent)) !== null) {
                const headingLevel = match[1].length;
                const headingText = match[2].trim();
                while (headingHierarchy.length >= headingLevel) {
                    headingHierarchy.pop();
                }
                headingHierarchy.push(headingText);
                if (headingLevel === 1 && !mainSections.includes(headingText)) {
                    mainSections.push(headingText);
                }
            }
            chunkDataArray.push({
                text: pageContent,
                metadata: {
                    headings: [...headingHierarchy],
                    sourceDocumentName: sourceDocumentName,
                    mainSections: [...mainSections],
                    pageNumber: currentPageNumber.toString(),
                    chunkNumber: chunkNumber,
                },
            });
            chunkNumber++;
        }
        this.logger.log(`Page-based PDF parsing complete, generated ${chunkDataArray.length} chunks`);
        return chunkDataArray;
    }
    async parsePDFDocumentToMarkdown(filePath, premiumMode) {
        this.logger.log(`Parsing PDF document to markdown: ${filePath}, premium mode: ${premiumMode}`);
        const allDocumentMarkdown = await (0, llamaparse_service_1.parseDocumentWithLlamaparseApi)({
            filePath,
            premiumMode,
            pageSeparator: '\n<PAGE>=================</PAGE>\n',
        });
        this.logger.log(`Received parsed document from Llamaparse API, text length: ${allDocumentMarkdown.text.length}`);
        const sourceDocumentName = filePath.split('/').pop();
        let lines = allDocumentMarkdown.text.split('\n');
        this.logger.log(`Document split into ${lines.length} lines`);
        lines = this.joinLinesWithHtmlTables(lines);
        this.logger.log(`After joining HTML tables: ${lines.length} lines`);
        const headingHierarchy = [];
        const mainSections = [];
        const chunkDataArray = [];
        let currentChunk = '';
        let currentChunkTokens = 0;
        let pageNumber = 1;
        let chunkNumber = 1;
        let currentChunkPageNumbers = new Set();
        let chunkHeadingHierarchy = [];
        const headingRegex = /^(#{1,6})\s+(.*)$/;
        const pageSeparatorRegex = /^\s*<PAGE>=================<\/PAGE>\s*$/;
        const tableLineRegex = /^\s*\|.*\|\s*$/;
        let inTable = false;
        let tableLines = [];
        const tablePageNumbers = new Set();
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (pageSeparatorRegex.test(line)) {
                pageNumber++;
                if (inTable) {
                    tablePageNumbers.add(pageNumber);
                }
                else {
                    currentChunkPageNumbers.add(pageNumber);
                }
                continue;
            }
            if (inTable) {
                if (tableLineRegex.test(line)) {
                    tableLines.push(line);
                    tablePageNumbers.add(pageNumber);
                    continue;
                }
                else {
                    inTable = false;
                    const { headers, rows } = this.parseMarkdownTable(tableLines);
                    const tableMarkdown = this.createMarkdownTable(headers, rows);
                    const tableTokenCount = this.countTokens(tableMarkdown);
                    const pageNumbersArray = Array.from(tablePageNumbers).sort((a, b) => a - b);
                    let pageNumberMetadata = '';
                    if (pageNumbersArray.length === 1) {
                        pageNumberMetadata = `${pageNumbersArray[0]}`;
                    }
                    else {
                        const isConsecutive = pageNumbersArray.every((num, idx) => idx === 0 || num === pageNumbersArray[idx - 1] + 1);
                        if (isConsecutive) {
                            pageNumberMetadata = `${pageNumbersArray[0]}-${pageNumbersArray[pageNumbersArray.length - 1]}`;
                        }
                        else {
                            pageNumberMetadata = pageNumbersArray.join(',');
                        }
                    }
                    if (tableTokenCount > 500) {
                        const splitTables = this.splitTableByTokenCount(headers, rows, 500);
                        for (const splitTable of splitTables) {
                            chunkDataArray.push({
                                text: splitTable,
                                metadata: {
                                    headings: [...chunkHeadingHierarchy],
                                    sourceDocumentName: sourceDocumentName,
                                    mainSections: [...mainSections],
                                    pageNumber: pageNumberMetadata,
                                    chunkNumber: chunkNumber,
                                },
                            });
                            chunkNumber++;
                        }
                    }
                    else {
                        chunkDataArray.push({
                            text: tableMarkdown,
                            metadata: {
                                headings: [...chunkHeadingHierarchy],
                                sourceDocumentName: sourceDocumentName,
                                mainSections: [...mainSections],
                                pageNumber: pageNumberMetadata,
                                chunkNumber: chunkNumber,
                            },
                        });
                        chunkNumber++;
                    }
                    tableLines = [];
                    tablePageNumbers.clear();
                }
            }
            if (!inTable && tableLineRegex.test(line)) {
                inTable = true;
                tableLines.push(line);
                tablePageNumbers.add(pageNumber);
                if (currentChunk !== '') {
                    chunkNumber = await this.processCurrentChunk({
                        currentChunk,
                        currentChunkTokens,
                        currentChunkPageNumbers,
                        chunkDataArray,
                        chunkHeadingHierarchy,
                        sourceDocumentName,
                        mainSections,
                        chunkNumber,
                    });
                    currentChunk = '';
                    currentChunkTokens = 0;
                    currentChunkPageNumbers.clear();
                }
                continue;
            }
            const headingMatch = line.match(headingRegex);
            if (currentChunk === '') {
                chunkHeadingHierarchy = headingHierarchy.slice();
                currentChunkPageNumbers = new Set();
            }
            if (headingMatch) {
                const headingLevel = headingMatch[1].length;
                const headingText = headingMatch[2].trim();
                while (headingHierarchy.length >= headingLevel) {
                    headingHierarchy.pop();
                }
                headingHierarchy.push(headingText);
                if (headingLevel === 1 && !mainSections.includes(headingText)) {
                    mainSections.push(headingText);
                }
            }
            currentChunk += line + '\n';
            currentChunkTokens = this.countTokens(currentChunk);
            currentChunkPageNumbers.add(pageNumber);
            if (currentChunkTokens >= 500) {
                chunkNumber = await this.processCurrentChunk({
                    currentChunk,
                    currentChunkTokens,
                    currentChunkPageNumbers,
                    chunkDataArray,
                    chunkHeadingHierarchy,
                    sourceDocumentName,
                    mainSections,
                    chunkNumber,
                });
                currentChunk = '';
                currentChunkTokens = 0;
                currentChunkPageNumbers.clear();
                chunkHeadingHierarchy = headingHierarchy.slice();
            }
        }
        if (inTable) {
            this.logger.log(`Processing final table with ${tableLines.length} lines`);
            const { headers, rows } = this.parseMarkdownTable(tableLines);
            const tableMarkdown = this.createMarkdownTable(headers, rows);
            const tableTokenCount = this.countTokens(tableMarkdown);
            const pageNumbersArray = Array.from(tablePageNumbers).sort((a, b) => a - b);
            let pageNumberMetadata = '';
            if (pageNumbersArray.length === 1) {
                pageNumberMetadata = `${pageNumbersArray[0]}`;
            }
            else {
                const isConsecutive = pageNumbersArray.every((num, idx) => idx === 0 || num === pageNumbersArray[idx - 1] + 1);
                if (isConsecutive) {
                    pageNumberMetadata = `${pageNumbersArray[0]}-${pageNumbersArray[pageNumbersArray.length - 1]}`;
                }
                else {
                    pageNumberMetadata = pageNumbersArray.join(',');
                }
            }
            if (tableTokenCount > 500) {
                const splitTables = this.splitTableByTokenCount(headers, rows, 500);
                for (const splitTable of splitTables) {
                    chunkDataArray.push({
                        text: splitTable,
                        metadata: {
                            headings: [...chunkHeadingHierarchy],
                            sourceDocumentName: sourceDocumentName,
                            mainSections: [...mainSections],
                            pageNumber: pageNumberMetadata,
                            chunkNumber: chunkNumber,
                        },
                    });
                    chunkNumber++;
                }
            }
            else {
                chunkDataArray.push({
                    text: tableMarkdown,
                    metadata: {
                        headings: [...chunkHeadingHierarchy],
                        sourceDocumentName: sourceDocumentName,
                        mainSections: [...mainSections],
                        pageNumber: pageNumberMetadata,
                        chunkNumber: chunkNumber,
                    },
                });
                chunkNumber++;
            }
        }
        if (currentChunk) {
            this.logger.log(`Processing final text chunk of ${currentChunkTokens} tokens`);
            chunkNumber = await this.processCurrentChunk({
                currentChunk,
                currentChunkTokens,
                currentChunkPageNumbers,
                chunkDataArray,
                chunkHeadingHierarchy,
                sourceDocumentName,
                mainSections,
                chunkNumber,
            });
        }
        this.logger.log(`PDF parsing complete, generated ${chunkDataArray.length} chunks`);
        return chunkDataArray;
    }
    createMarkdownTable(headers, rows) {
        this.logger.log(`Creating markdown table with ${headers.length} header rows and ${rows.length} data rows`);
        if (headers.length === 0 || rows.length === 0)
            return '';
        let markdown = '';
        const maxColumns = Math.max(...headers.map((row) => row.length), ...rows.map((row) => row.length));
        const extendedHeaders = headers.map((headerRow) => {
            const extended = [...headerRow];
            while (extended.length < maxColumns) {
                extended.push('');
            }
            return extended;
        });
        const columnsToInclude = [];
        for (let colIndex = 0; colIndex < maxColumns; colIndex++) {
            let hasData = false;
            for (const headerRow of extendedHeaders) {
                const cell = headerRow[colIndex];
                if (cell !== undefined && cell !== null && String(cell).trim() !== '') {
                    hasData = true;
                    break;
                }
            }
            if (!hasData) {
                for (const row of rows) {
                    const cell = row[colIndex];
                    if (cell !== undefined &&
                        cell !== null &&
                        String(cell).trim() !== '') {
                        hasData = true;
                        break;
                    }
                }
            }
            if (hasData) {
                columnsToInclude.push(colIndex);
            }
        }
        if (columnsToInclude.length < 2) {
            return '';
        }
        if (extendedHeaders.length === 1) {
            const filteredRow = columnsToInclude.map((colIndex) => this.escapeCell(extendedHeaders[0][colIndex]));
            markdown += '| ' + filteredRow.join(' | ') + ' |\n';
            markdown += '| ' + columnsToInclude.map(() => '---').join(' | ') + ' |\n';
            for (const row of rows) {
                const rowData = columnsToInclude.map((colIndex) => {
                    const cellData = row[colIndex] !== undefined && row[colIndex] !== null
                        ? row[colIndex]
                        : '';
                    return this.escapeCell(cellData);
                });
                markdown += '| ' + rowData.join(' | ') + ' |\n';
            }
        }
        else {
            const firstHeaderRow = extendedHeaders[0];
            const filteredFirstHeaderRow = columnsToInclude.map((colIndex) => this.escapeCell(firstHeaderRow[colIndex]));
            markdown += '| ' + filteredFirstHeaderRow.join(' | ') + ' |\n';
            markdown += '| ' + columnsToInclude.map(() => '---').join(' | ') + ' |\n';
            for (let i = 1; i < extendedHeaders.length; i++) {
                const headerRow = extendedHeaders[i];
                const filteredRow = columnsToInclude.map((colIndex) => {
                    const cell = headerRow[colIndex];
                    const cellStr = this.escapeCell(cell);
                    return cellStr ? `**${cellStr}**` : '';
                });
                markdown += '| ' + filteredRow.join(' | ') + ' |\n';
            }
            for (const row of rows) {
                const rowData = columnsToInclude.map((colIndex) => {
                    const cellData = row[colIndex] !== undefined && row[colIndex] !== null
                        ? row[colIndex]
                        : '';
                    return this.escapeCell(cellData);
                });
                markdown += '| ' + rowData.join(' | ') + ' |\n';
            }
        }
        this.logger.log(`Markdown table created, length: ${markdown.length}`);
        return markdown;
    }
    escapeCell(cell) {
        let cellStr = String(cell);
        cellStr = cellStr.replace(/\|/g, '\\|');
        cellStr = cellStr.replace(/\r?\n|\r/g, ' ');
        cellStr = cellStr.trim();
        return cellStr;
    }
    splitTableByTokenCount(headers, rows, maxTokens) {
        this.logger.log(`Splitting table by token count, threshold: ${maxTokens}, rows: ${rows.length}`);
        const tables = [];
        const maxColumns = Math.max(...headers.map((row) => row.length), ...rows.map((row) => row.length));
        const extendedHeaders = headers.map((headerRow) => {
            const extended = [...headerRow];
            while (extended.length < maxColumns) {
                extended.push('');
            }
            return extended;
        });
        this.splitRowsRecursively({
            headers: extendedHeaders,
            rows: rows,
            start: 0,
            end: rows.length,
            maxTokens: maxTokens,
            result: tables,
            countTokens: this.countTokens.bind(this),
            createMarkdownTable: this.createMarkdownTable.bind(this),
        });
        this.logger.log(`Table splitting complete, generated ${tables.length} table chunks`);
        return tables;
    }
    splitRowsRecursively({ headers, rows, start, end, maxTokens, result, countTokens, createMarkdownTable, }) {
        this.logger.log(`Splitting rows recursively: rows ${start}-${end}, max tokens: ${maxTokens}`);
        const chunkRows = rows.slice(start, end);
        const markdown = createMarkdownTable(headers, chunkRows);
        if (!markdown) {
            return;
        }
        const tokenCount = countTokens(markdown);
        if (tokenCount > maxTokens && end - start > 1) {
            const mid = Math.floor((start + end) / 2);
            this.splitRowsRecursively({
                headers,
                rows,
                start,
                end: mid,
                maxTokens,
                result,
                countTokens,
                createMarkdownTable,
            });
            this.splitRowsRecursively({
                headers,
                rows,
                start: mid,
                end,
                maxTokens,
                result,
                countTokens,
                createMarkdownTable,
            });
        }
        else {
            result.push(markdown);
        }
    }
    detectHeaderRows(data) {
        this.logger.log(`Detecting header rows with heuristic method, ${data.length} rows total`);
        if (data.length <= 1)
            return 1;
        const maxRowsToCheck = Math.min(5, data.length - 1);
        let headerRowCount = 1;
        for (let i = 1; i < maxRowsToCheck; i++) {
            const row = data[i];
            const nonEmptyCells = row.filter((cell) => cell !== undefined && cell !== null && String(cell).trim() !== '').length;
            if (nonEmptyCells < 2)
                continue;
            const textCells = row.filter((cell) => cell !== undefined &&
                cell !== null &&
                typeof cell === 'string' &&
                String(cell).trim() !== '' &&
                String(cell).length < 50 &&
                isNaN(Number(cell))).length;
            const isLikelyHeader = textCells >= nonEmptyCells * 0.7;
            if (isLikelyHeader) {
                headerRowCount = i + 1;
            }
            else {
                break;
            }
        }
        this.logger.log(`Heuristic header detection complete, found ${headerRowCount} header rows`);
        return headerRowCount;
    }
    async detectHeaderRowsWithLLM(tableData) {
        this.logger.log(`Detecting header rows with LLM, ${tableData.length} rows total`);
        if (tableData.length <= 1)
            return 1;
        const filteredTableData = tableData.filter((row) => row.some((cell) => cell !== undefined && cell !== null && String(cell).trim() !== ''));
        if (filteredTableData.length <= 1)
            return 1;
        const rowSample = filteredTableData
            .slice(0, 8)
            .map((row) => row
            .map((cell) => cell !== null && cell !== undefined ? String(cell) : '')
            .join('\t'))
            .join('\n');
        const messages = [
            {
                role: 'system',
                content: 'You are an expert in spreadsheet structure analysis. Your task is to identify header rows in tabular data.',
            },
            {
                role: 'user',
                content: `Analyze this tabular data and determine how many rows should be considered as headers.
        
        Header rows typically:
        - Contain column titles or category labels
        - Use shorter text rather than lengthy content
        - Appear at the top of the table
        - May form a hierarchical structure (in multi-row headers)
        
        Return ONLY a single number representing the count of header rows as JSON.
        Example:
        {
          "headerRowCount": 1
        }
        
        Table data (tab-separated):
        ${rowSample}`,
            },
        ];
        try {
            const result = await this.llmRateLimitService.handleRequest({
                model: constants_1.LLM_MODELS['gpt-4o'],
                messages,
                json: true,
                temperature: 0,
            });
            const headerCount = result.response.headerRowCount;
            if (headerCount) {
                this.logger.log(`LLM detected ${headerCount} header rows`);
                return headerCount;
            }
            this.logger.warn('LLM response could not be parsed as a valid header count, falling back to heuristic method');
            const fallbackCount = this.detectHeaderRows(filteredTableData);
            this.logger.log(`Fallback header detection found ${fallbackCount} header rows`);
            return fallbackCount;
        }
        catch (error) {
            this.logger.error(`Error using LLM for header detection: ${error.message}, falling back to heuristic method`);
            const fallbackCount = this.detectHeaderRows(filteredTableData);
            this.logger.log(`Fallback header detection found ${fallbackCount} header rows`);
            return fallbackCount;
        }
    }
    async parseSpreadsheetToMarkdown(path, maxTokens = 3000) {
        this.logger.log(`Enhanced parsing of spreadsheet to markdown: ${path}, max tokens: ${maxTokens}`);
        const workbook = XLSX.readFile(path, {
            cellStyles: true,
            cellNF: true,
            cellDates: true,
            cellFormula: true,
            sheetStubs: true,
            sheetRows: 0,
        });
        this.logger.log(`Loaded workbook: ${path}, sheets: ${workbook.SheetNames.join(', ')}`);
        const sourceDocumentName = path.split('/').pop();
        const chunkDataArray = [];
        let pageNumber = 0;
        let globalChunkNumber = 1;
        for (const sheetName of workbook.SheetNames) {
            pageNumber++;
            this.logger.log(`Processing sheet ${pageNumber}/${workbook.SheetNames.length}: "${sheetName}"`);
            const sheet = workbook.Sheets[sheetName];
            const { hiddenRows, hiddenCols } = this.detectHiddenRowsAndColumns(sheet);
            this.logger.log(`Sheet "${sheetName}" has ${hiddenRows.size} hidden rows and ${hiddenCols.size} hidden columns`);
            const metadataTables = this.detectNativeTables(workbook, sheetName);
            const originalMerges = sheet['!merges'] ? [...sheet['!merges']] : [];
            const rawJsonData = XLSX.utils.sheet_to_json(sheet, {
                header: 1,
                defval: '',
                blankrows: true,
                raw: false,
            });
            if (rawJsonData.length === 0) {
                this.logger.log(`Sheet "${sheetName}" is empty, skipping`);
                continue;
            }
            const processedJsonData = this.postProcessMergedCells(rawJsonData, originalMerges);
            const dataWithHidden = this.normalizeSheetDataWithHiddenCells(processedJsonData, hiddenRows, hiddenCols, true);
            let tables = [];
            if (metadataTables.length > 0) {
                for (const tableInfo of metadataTables) {
                    this.logger.log(`Processing metadata-detected table: ${tableInfo.name}`);
                    const table = this.extractNativeTable(sheet, tableInfo.range);
                    const adjustedTable = this.adjustTableForHiddenCells(table, tableInfo.range, hiddenRows, hiddenCols);
                    tables.push(adjustedTable);
                }
            }
            if (tables.length === 0) {
                this.logger.log(`No metadata tables found, using enhanced detection algorithms`);
                const detectedTables = await this.detectTablesInSheet(dataWithHidden, originalMerges);
                tables.push(...detectedTables);
            }
            tables = this.deduplicateTables(tables);
            this.logger.log(`Sheet "${sheetName}" has ${tables.length} tables after processing`);
            let tableNumber = 1;
            for (const table of tables) {
                if (table.rows.length === 0 || table.headers.length === 0) {
                    this.logger.log(`Skipping empty table #${tableNumber}`);
                    tableNumber++;
                    continue;
                }
                const hasValidHeader = table.headers.some((headerRow) => headerRow.some((cell) => cell !== undefined && cell !== null && String(cell).trim() !== ''));
                const hasValidData = table.rows.some((row) => row.some((cell) => cell !== undefined && cell !== null && String(cell).trim() !== ''));
                if (!hasValidHeader || !hasValidData) {
                    this.logger.log(`Skipping table #${tableNumber} (invalid headers or data)`);
                    tableNumber++;
                    continue;
                }
                const markdownTables = this.splitTableByTokenCount(table.headers, table.rows, maxTokens);
                this.logger.log(`Table #${tableNumber} split into ${markdownTables.length} chunks`);
                for (const markdown of markdownTables) {
                    if (!markdown || markdown.trim() === '')
                        continue;
                    chunkDataArray.push({
                        text: markdown,
                        metadata: {
                            sourceDocumentName: sourceDocumentName,
                            headings: [sheetName, `Table ${tableNumber}`],
                            pageNumber: String(pageNumber),
                            chunkNumber: globalChunkNumber++,
                            mainSections: [],
                        },
                    });
                }
                tableNumber++;
            }
        }
        this.logger.log(`Enhanced spreadsheet parsing complete, generated ${chunkDataArray.length} chunks`);
        return chunkDataArray;
    }
    normalizeSheetData(data) {
        this.logger.log(`Normalizing sheet data: ${data.length} rows`);
        if (data.length === 0)
            return [];
        const maxCols = Math.max(...data.map((row) => row.length));
        const normalizedData = data.map((row) => {
            const normalizedRow = Array(maxCols).fill('');
            for (let i = 0; i < row.length; i++) {
                const value = row[i];
                normalizedRow[i] = value !== undefined && value !== null ? value : '';
            }
            return normalizedRow;
        });
        this.logger.log(`Sheet normalized to consistent dimensions: ${data.length} rows x ${maxCols} columns`);
        return normalizedData;
    }
    extractRegionData(data, region) {
        this.logger.log(`Extracting region data: starting at row ${region.startRow}, col ${region.startCol}, ${region.height}x${region.width}`);
        const { startRow, startCol, height, width } = region;
        const regionData = [];
        for (let r = 0; r < height; r++) {
            const row = [];
            for (let c = 0; c < width; c++) {
                const dataRow = startRow + r;
                const dataCol = startCol + c;
                if (dataRow < data.length && dataCol < data[dataRow].length) {
                    row.push(data[dataRow][dataCol]);
                }
                else {
                    row.push('');
                }
            }
            regionData.push(row);
        }
        return regionData;
    }
    isLikelyDate(value) {
        if (typeof value !== 'string')
            return false;
        const datePatterns = [
            /^\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4}$/,
            /^\d{4}[-\/]\d{1,2}[-\/]\d{1,2}$/,
            /^\d{1,2}[-\/][A-Za-z]{3,9}[-\/]\d{2,4}$/,
            /^[A-Za-z]{3,9}[-\/]\d{1,2}[-\/]\d{2,4}$/,
        ];
        for (const pattern of datePatterns) {
            if (pattern.test(value))
                return true;
        }
        const date = new Date(value);
        return !isNaN(date.getTime());
    }
    findEmptyRowsAndColsAdaptive(data, headerRowCount = 2) {
        this.logger.log(`Finding empty rows and columns adaptively, ${data.length} rows, header count: ${headerRowCount}`);
        if (data.length === 0)
            return { emptyRows: [], emptyCols: [] };
        const rowCount = data.length;
        const colCount = data[0].length;
        const totalCells = rowCount * colCount;
        let nonEmptyCells = 0;
        for (let r = 0; r < rowCount; r++) {
            for (let c = 0; c < colCount; c++) {
                const value = data[r][c];
                if (value !== undefined &&
                    value !== null &&
                    String(value).trim() !== '') {
                    nonEmptyCells++;
                }
            }
        }
        const dataDensity = nonEmptyCells / totalCells;
        const emptyThreshold = Math.min(0.9, Math.max(0.95, 1 - dataDensity));
        const rowEmptyCounts = Array(rowCount).fill(0);
        const colEmptyCounts = Array(colCount).fill(0);
        for (let r = 0; r < rowCount; r++) {
            for (let c = 0; c < colCount; c++) {
                const value = data[r][c];
                const isEmpty = value === undefined || value === null || String(value).trim() === '';
                if (isEmpty) {
                    rowEmptyCounts[r]++;
                    colEmptyCounts[c]++;
                }
            }
        }
        const emptyRows = rowEmptyCounts.map((count) => count / colCount >= emptyThreshold);
        const emptyCols = colEmptyCounts.map((count, colIndex) => {
            let hasHeaderContent = false;
            for (let r = 0; r < Math.min(headerRowCount, rowCount); r++) {
                const value = data[r][colIndex];
                if (value !== undefined &&
                    value !== null &&
                    String(value).trim() !== '') {
                    console.log(`Header content check: ${String(value).trim()}`, headerRowCount);
                    hasHeaderContent = true;
                    break;
                }
            }
            if (hasHeaderContent) {
                return false;
            }
            return count / rowCount >= emptyThreshold;
        });
        this.logger.log(`Adaptive analysis complete: detected ${emptyRows.filter(Boolean).length} empty rows and ${emptyCols.filter(Boolean).length} empty columns`);
        return { emptyRows, emptyCols };
    }
    identifyTableRegionsEnhanced(data, emptyRows, emptyCols) {
        this.logger.log(`Identifying table regions with 8-way connectivity`);
        const rowCount = data.length;
        const colCount = data[0].length;
        const grid = Array(rowCount)
            .fill(false)
            .map((_, r) => Array(colCount)
            .fill(false)
            .map((_, c) => {
            if (emptyRows[r] || emptyCols[c])
                return false;
            const value = data[r][c];
            return !(value === undefined ||
                value === null ||
                String(value).trim() === '');
        }));
        const visited = Array(rowCount)
            .fill(false)
            .map(() => Array(colCount).fill(false));
        const regions = [];
        const dr = [-1, -1, 0, 1, 1, 1, 0, -1];
        const dc = [0, 1, 1, 1, 0, -1, -1, -1];
        for (let r = 0; r < rowCount; r++) {
            for (let c = 0; c < colCount; c++) {
                if (grid[r][c] && !visited[r][c]) {
                    let minRow = r, maxRow = r, minCol = c, maxCol = c;
                    const queue = [[r, c]];
                    visited[r][c] = true;
                    while (queue.length > 0) {
                        const [curR, curC] = queue.shift();
                        minRow = Math.min(minRow, curR);
                        maxRow = Math.max(maxRow, curR);
                        minCol = Math.min(minCol, curC);
                        maxCol = Math.max(maxCol, curC);
                        for (let i = 0; i < 8; i++) {
                            const newR = curR + dr[i];
                            const newC = curC + dc[i];
                            if (newR >= 0 &&
                                newR < rowCount &&
                                newC >= 0 &&
                                newC < colCount &&
                                grid[newR][newC] &&
                                !visited[newR][newC]) {
                                queue.push([newR, newC]);
                                visited[newR][newC] = true;
                            }
                        }
                    }
                    if (maxRow - minRow >= 1 && maxCol - minCol >= 1) {
                        regions.push({
                            startRow: minRow,
                            startCol: minCol,
                            height: maxRow - minRow + 1,
                            width: maxCol - minCol + 1,
                        });
                    }
                }
            }
        }
        this.logger.log(`Table region identification complete, found ${regions.length} potential regions`);
        return regions;
    }
    applyHierarchicalClustering(regions, data) {
        this.logger.log(`Applying hierarchical clustering to ${regions.length} regions`);
        if (regions.length <= 1)
            return regions;
        const distances = [];
        for (let i = 0; i < regions.length; i++) {
            for (let j = i + 1; j < regions.length; j++) {
                const r1 = regions[i];
                const r2 = regions[j];
                const r1CenterRow = r1.startRow + r1.height / 2;
                const r1CenterCol = r1.startCol + r1.width / 2;
                const r2CenterRow = r2.startRow + r2.height / 2;
                const r2CenterCol = r2.startCol + r2.width / 2;
                const distance = Math.sqrt(Math.pow(r1CenterRow - r2CenterRow, 2) +
                    Math.pow(r1CenterCol - r2CenterCol, 2));
                distances.push({ i, j, distance });
            }
        }
        distances.sort((a, b) => a.distance - b.distance);
        const parent = Array.from({ length: regions.length }, (_, i) => i);
        const find = (x) => {
            if (parent[x] !== x) {
                parent[x] = find(parent[x]);
            }
            return parent[x];
        };
        const union = (x, y) => {
            parent[find(x)] = find(y);
        };
        const dataDensity = this.calculateDataDensity(data);
        const baseClusterThreshold = 5;
        const clusterThreshold = baseClusterThreshold * (1 + Math.min(1, Math.max(0.2, dataDensity * 2)));
        for (const { i, j, distance } of distances) {
            if (distance > clusterThreshold)
                continue;
            const r1 = regions[i];
            const r2 = regions[j];
            const horizontalOverlap = r1.startCol <= r2.startCol + r2.width &&
                r2.startCol <= r1.startCol + r1.width;
            const verticalOverlap = r1.startRow <= r2.startRow + r2.height &&
                r2.startRow <= r1.startRow + r1.height;
            if (horizontalOverlap || verticalOverlap) {
                union(i, j);
            }
        }
        const clusters = new Map();
        for (let i = 0; i < regions.length; i++) {
            const clusterID = find(i);
            if (!clusters.has(clusterID)) {
                clusters.set(clusterID, []);
            }
            clusters.get(clusterID).push(i);
        }
        const mergedRegions = [];
        for (const clusterIndices of clusters.values()) {
            if (clusterIndices.length === 1) {
                mergedRegions.push(regions[clusterIndices[0]]);
                continue;
            }
            let minRow = Infinity;
            let minCol = Infinity;
            let maxRow = -Infinity;
            let maxCol = -Infinity;
            for (const idx of clusterIndices) {
                const region = regions[idx];
                minRow = Math.min(minRow, region.startRow);
                minCol = Math.min(minCol, region.startCol);
                maxRow = Math.max(maxRow, region.startRow + region.height - 1);
                maxCol = Math.max(maxCol, region.startCol + region.width - 1);
            }
            mergedRegions.push({
                startRow: minRow,
                startCol: minCol,
                height: maxRow - minRow + 1,
                width: maxCol - minCol + 1,
            });
        }
        this.logger.log(`Clustering complete: merged into ${mergedRegions.length} regions`);
        return mergedRegions;
    }
    async detectTablesInSheet(data, merges = []) {
        this.logger.log(`Detecting tables in sheet: ${data.length} rows, ${merges.length} merged regions`);
        if (data.length === 0)
            return [];
        const normalizedData = this.normalizeSheetData(data);
        if (normalizedData.length > 300) {
            this.logger.log(`Large sheet detected (${normalizedData.length} rows), using progressive approach`);
            return this.detectTablesProgressively(normalizedData, merges);
        }
        this.logger.log(`Using standard table detection for sheet with ${normalizedData.length} rows`);
        const { emptyRows, emptyCols } = this.findEmptyRowsAndColsAdaptive(normalizedData);
        const regions = this.identifyTableRegionsEnhanced(normalizedData, emptyRows, emptyCols);
        const mergedRegions = this.applyHierarchicalClustering(regions, normalizedData);
        const tables = await this.regionsToTables(mergedRegions, normalizedData);
        this.logger.log(`Standard detection complete, found ${tables.length} tables`);
        return tables;
    }
    async detectTablesProgressively(data, merges = []) {
        this.logger.log(`Starting progressive table detection on ${data.length} rows`);
        const rowCount = data.length;
        const chunkSize = 150;
        const overlap = 50;
        const firstChunk = data.slice(0, Math.min(chunkSize, rowCount));
        const { emptyRows: firstEmptyRows, emptyCols: firstEmptyCols } = this.findEmptyRowsAndColsAdaptive(firstChunk);
        const columnStructure = this.analyzeColumnStructure(firstChunk, firstEmptyCols);
        const analysisResult = this.analyzeSheetStructure(firstChunk);
        const isContinuousTable = analysisResult.continuousTableProbability > 0.7;
        this.logger.log(`Sheet analysis: continuous table probability: ${analysisResult.continuousTableProbability.toFixed(2)}, detected ${analysisResult.estimatedHeaderRows} header rows`);
        if (isContinuousTable) {
            this.logger.log(`Detected continuous table structure, handling as single table`);
            return this.handleContinuousTable(data, columnStructure);
        }
        const allRegions = [];
        for (let startRow = 0; startRow < rowCount; startRow += chunkSize - overlap) {
            const endRow = Math.min(startRow + chunkSize, rowCount);
            const chunk = data.slice(startRow, endRow);
            const { emptyRows, emptyCols } = this.findEmptyRowsAndColsAdaptive(chunk);
            const consistentEmptyCols = this.enforceColumnConsistency(emptyCols, columnStructure);
            const regions = this.identifyTableRegionsEnhanced(chunk, emptyRows, consistentEmptyCols);
            const offsetRegions = regions.map((region) => ({
                ...region,
                startRow: region.startRow + startRow,
                confidence: this.assessTableConfidenceAdaptive(this.extractRegionData(chunk, region), chunk.length, chunk[0].length),
            }));
            allRegions.push(...offsetRegions);
        }
        const cohesiveRegions = this.enforceGlobalConsistency(allRegions, data);
        this.logger.log(`Progressive detection complete, found ${cohesiveRegions.length} table regions`);
        const result = await this.regionsToTables(cohesiveRegions, data);
        this.logger.log(`Converted regions to ${result.length} tables`);
        return result;
    }
    analyzeSheetStructure(data) {
        const result = {
            continuousTableProbability: 0,
            tableTypes: [],
            estimatedHeaderRows: 1,
        };
        if (data.length < 10)
            return result;
        let headerRows = 0;
        let headerEvidence = 0;
        for (let r = 0; r < Math.min(10, data.length); r++) {
            const headerScore = this.calculateHeaderLikelihood(data[r]);
            if (headerScore > 0.6) {
                headerRows++;
                headerEvidence += headerScore;
            }
            else {
                break;
            }
        }
        result.estimatedHeaderRows = Math.max(1, headerRows);
        const rowPatternScores = [];
        for (let r = headerRows; r < Math.min(100, data.length); r++) {
            if (r > headerRows) {
                rowPatternScores.push(this.calculateRowPatternSimilarity(data[r], data[r - 1]));
            }
        }
        const avgPatternConsistency = rowPatternScores.length > 0
            ? rowPatternScores.reduce((sum, score) => sum + score, 0) /
                rowPatternScores.length
            : 0;
        const columnTypeConsistency = this.analyzeColumnTypeConsistency(data, headerRows);
        result.continuousTableProbability =
            headerEvidence * 0.3 +
                avgPatternConsistency * 0.4 +
                columnTypeConsistency * 0.3;
        if (result.continuousTableProbability > 0.7) {
            result.tableTypes.push('continuous');
        }
        else if (avgPatternConsistency > 0.6) {
            result.tableTypes.push('semi-continuous');
        }
        else {
            result.tableTypes.push('segmented');
        }
        return result;
    }
    async handleContinuousTable(data, columnStructure) {
        let headerRowCount = 1;
        for (let r = 1; r < Math.min(10, data.length); r++) {
            const headerLikelihood = this.calculateHeaderLikelihood(data[r]);
            const dataRowLikelihood = this.calculateDataRowLikelihood(data[r]);
            if (headerLikelihood > dataRowLikelihood && headerLikelihood > 0.6) {
                headerRowCount++;
            }
            else {
                break;
            }
        }
        const activeColumns = [];
        for (let c = 0; c < data[0].length; c++) {
            if (columnStructure.isActive[c]) {
                activeColumns.push(c);
            }
        }
        let lastDataRow = data.length - 1;
        while (lastDataRow > headerRowCount) {
            const isEmptyRow = activeColumns.every((c) => {
                const value = data[lastDataRow][c];
                return (value === undefined || value === null || String(value).trim() === '');
            });
            if (isEmptyRow) {
                lastDataRow--;
            }
            else {
                break;
            }
        }
        const tableRegion = {
            startRow: 0,
            startCol: 0,
            height: lastDataRow + 1,
            width: data[0].length,
        };
        const headers = data.slice(0, headerRowCount);
        const rows = data.slice(headerRowCount, lastDataRow + 1);
        return [{ headers, rows }];
    }
    analyzeColumnStructure(data, emptyCols) {
        const colCount = data[0].length;
        const isActive = Array(colCount).fill(false);
        for (let c = 0; c < colCount; c++) {
            isActive[c] = !emptyCols[c];
        }
        const columnDensity = Array(colCount).fill(0);
        for (let c = 0; c < colCount; c++) {
            let nonEmptyCells = 0;
            for (let r = 0; r < data.length; r++) {
                const value = data[r][c];
                if (value !== undefined &&
                    value !== null &&
                    String(value).trim() !== '') {
                    nonEmptyCells++;
                }
            }
            columnDensity[c] = nonEmptyCells / data.length;
        }
        for (let c = 0; c < colCount; c++) {
            if (columnDensity[c] > 0.1) {
                isActive[c] = true;
            }
        }
        return { isActive };
    }
    enforceColumnConsistency(localEmptyCols, columnStructure) {
        const adjustedEmptyCols = [...localEmptyCols];
        for (let c = 0; c < Math.min(localEmptyCols.length, columnStructure.isActive.length); c++) {
            if (columnStructure.isActive[c]) {
                adjustedEmptyCols[c] = false;
            }
        }
        return adjustedEmptyCols;
    }
    enforceGlobalConsistency(regions, data) {
        if (regions.length <= 1)
            return regions;
        regions.sort((a, b) => a.startRow - b.startRow);
        const groups = [];
        let currentGroup = [regions[0]];
        for (let i = 1; i < regions.length; i++) {
            const current = regions[i];
            const previous = regions[i - 1];
            const horizontalOverlap = this.calculateHorizontalOverlap(current, previous);
            const verticalGap = current.startRow - (previous.startRow + previous.height);
            if (horizontalOverlap > 0.5 && verticalGap < 20) {
                currentGroup.push(current);
            }
            else {
                groups.push(currentGroup);
                currentGroup = [current];
            }
        }
        if (currentGroup.length > 0) {
            groups.push(currentGroup);
        }
        const mergedRegions = [];
        for (const group of groups) {
            if (group.length === 1) {
                const { confidence, ...region } = group[0];
                mergedRegions.push(region);
            }
            else {
                let startRow = Infinity;
                let startCol = Infinity;
                let endRow = -Infinity;
                let endCol = -Infinity;
                for (const region of group) {
                    startRow = Math.min(startRow, region.startRow);
                    startCol = Math.min(startCol, region.startCol);
                    endRow = Math.max(endRow, region.startRow + region.height);
                    endCol = Math.max(endCol, region.startCol + region.width);
                }
                mergedRegions.push({
                    startRow,
                    startCol,
                    height: endRow - startRow,
                    width: endCol - startCol,
                });
            }
        }
        return mergedRegions;
    }
    async regionsToTables(regions, data) {
        const tables = [];
        for (const region of regions) {
            const regionData = this.extractRegionData(data, region);
            if (regionData.length < 2 || regionData[0].length < 2)
                continue;
            const headerRowCount = await this.detectHeaderRowsWithLLM(regionData);
            const safeHeaderCount = this.calculateOptimalHeaderCount(headerRowCount, regionData);
            tables.push({
                headers: regionData.slice(0, safeHeaderCount),
                rows: regionData.slice(safeHeaderCount),
            });
        }
        return tables;
    }
    calculateHorizontalOverlap(regionA, regionB) {
        const aLeft = regionA.startCol;
        const aRight = regionA.startCol + regionA.width;
        const bLeft = regionB.startCol;
        const bRight = regionB.startCol + regionB.width;
        const overlapStart = Math.max(aLeft, bLeft);
        const overlapEnd = Math.min(aRight, bRight);
        const overlapWidth = Math.max(0, overlapEnd - overlapStart);
        const aWidth = aRight - aLeft;
        const bWidth = bRight - bLeft;
        const totalWidth = Math.max(aWidth, bWidth);
        return totalWidth > 0 ? overlapWidth / totalWidth : 0;
    }
    calculateHeaderLikelihood(row) {
        if (!row || row.length === 0)
            return 0;
        let textCells = 0;
        let nonEmptyCells = 0;
        let shortTextCells = 0;
        for (const cell of row) {
            if (cell !== undefined && cell !== null && String(cell).trim() !== '') {
                nonEmptyCells++;
                if (typeof cell === 'string') {
                    textCells++;
                    if (String(cell).length < 50) {
                        shortTextCells++;
                    }
                }
            }
        }
        if (nonEmptyCells === 0)
            return 0;
        const textRatio = textCells / nonEmptyCells;
        const shortTextRatio = shortTextCells / nonEmptyCells;
        const coverageRatio = nonEmptyCells / row.length;
        return textRatio * 0.4 + shortTextRatio * 0.4 + coverageRatio * 0.2;
    }
    calculateDataRowLikelihood(row) {
        if (!row || row.length === 0)
            return 0;
        let numericCells = 0;
        let dateCells = 0;
        let nonEmptyCells = 0;
        for (const cell of row) {
            if (cell !== undefined && cell !== null && String(cell).trim() !== '') {
                nonEmptyCells++;
                if (typeof cell === 'number' || !isNaN(Number(cell))) {
                    numericCells++;
                }
                else if (cell instanceof Date || this.isLikelyDate(cell)) {
                    dateCells++;
                }
            }
        }
        if (nonEmptyCells === 0)
            return 0;
        const numericRatio = (numericCells + dateCells) / nonEmptyCells;
        const coverageRatio = nonEmptyCells / row.length;
        return numericRatio * 0.6 + coverageRatio * 0.4;
    }
    calculateRowPatternSimilarity(rowA, rowB) {
        if (!rowA || !rowB || rowA.length !== rowB.length)
            return 0;
        let matchingCellTypes = 0;
        let cellsCompared = 0;
        for (let c = 0; c < rowA.length; c++) {
            const cellA = rowA[c];
            const cellB = rowB[c];
            const typeA = this.getCellDataType(cellA);
            const typeB = this.getCellDataType(cellB);
            if (typeA !== 'empty' || typeB !== 'empty') {
                cellsCompared++;
                if (typeA === typeB) {
                    matchingCellTypes++;
                }
            }
        }
        return cellsCompared > 0 ? matchingCellTypes / cellsCompared : 0;
    }
    getCellDataType(cell) {
        if (cell === undefined || cell === null || String(cell).trim() === '') {
            return 'empty';
        }
        if (typeof cell === 'number' || !isNaN(Number(cell))) {
            return 'number';
        }
        if (cell instanceof Date || this.isLikelyDate(cell)) {
            return 'date';
        }
        if (typeof cell === 'boolean') {
            return 'boolean';
        }
        return 'text';
    }
    analyzeColumnTypeConsistency(data, headerRows) {
        if (data.length <= headerRows)
            return 0;
        const colCount = data[0].length;
        let totalConsistency = 0;
        let columnsAnalyzed = 0;
        for (let c = 0; c < colCount; c++) {
            let hasData = false;
            for (let r = headerRows; r < data.length; r++) {
                if (this.getCellDataType(data[r][c]) !== 'empty') {
                    hasData = true;
                    break;
                }
            }
            if (!hasData)
                continue;
            const typeCounts = {};
            let totalCells = 0;
            for (let r = headerRows; r < data.length; r++) {
                const cellType = this.getCellDataType(data[r][c]);
                if (cellType !== 'empty') {
                    typeCounts[cellType] = (typeCounts[cellType] || 0) + 1;
                    totalCells++;
                }
            }
            let maxTypeCount = 0;
            for (const count of Object.values(typeCounts)) {
                maxTypeCount = Math.max(maxTypeCount, count);
            }
            const columnConsistency = totalCells > 0 ? maxTypeCount / totalCells : 0;
            totalConsistency += columnConsistency;
            columnsAnalyzed++;
        }
        return columnsAnalyzed > 0 ? totalConsistency / columnsAnalyzed : 0;
    }
    calculateDataDensity(data) {
        if (data.length === 0)
            return 0;
        const rowCount = data.length;
        const colCount = data[0].length;
        let nonEmptyCells = 0;
        const totalCells = rowCount * colCount;
        for (let r = 0; r < rowCount; r++) {
            for (let c = 0; c < colCount; c++) {
                const value = data[r][c];
                if (value !== undefined &&
                    value !== null &&
                    String(value).trim() !== '') {
                    nonEmptyCells++;
                }
            }
        }
        return nonEmptyCells / totalCells;
    }
    assessTableConfidenceAdaptive(regionData, totalRows, totalCols) {
        if (regionData.length < 2)
            return 0;
        const rowCount = regionData.length;
        const colCount = regionData[0].length;
        if (colCount < 2)
            return 0;
        let nonEmptyCells = 0;
        const totalCells = rowCount * colCount;
        const columnDataTypes = {};
        for (let c = 0; c < colCount; c++) {
            columnDataTypes[c] = new Map();
        }
        for (let r = 0; r < rowCount; r++) {
            for (let c = 0; c < colCount; c++) {
                const value = regionData[r][c];
                if (value !== undefined &&
                    value !== null &&
                    String(value).trim() !== '') {
                    nonEmptyCells++;
                    let dataType = 'text';
                    if (typeof value === 'number' || !isNaN(Number(value))) {
                        dataType = 'number';
                    }
                    else if (value instanceof Date || this.isLikelyDate(value)) {
                        dataType = 'date';
                    }
                    const currentCount = columnDataTypes[c].get(dataType) || 0;
                    columnDataTypes[c].set(dataType, currentCount + 1);
                }
            }
        }
        const relativeSizeRatio = (rowCount * colCount) / (totalRows * totalCols);
        const isSmallTable = relativeSizeRatio < 0.1;
        const minRequiredDensity = isSmallTable ? 0.4 : 0.2;
        const densityScore = nonEmptyCells / totalCells;
        if (densityScore < minRequiredDensity) {
            return 0;
        }
        let totalConsistency = 0;
        let columnsWithData = 0;
        for (let c = 0; c < colCount; c++) {
            const types = columnDataTypes[c];
            let maxCount = 0;
            let totalCount = 0;
            for (const count of types.values()) {
                maxCount = Math.max(maxCount, count);
                totalCount += count;
            }
            if (totalCount > 0) {
                const columnConsistency = maxCount / totalCount;
                totalConsistency += columnConsistency;
                columnsWithData++;
            }
        }
        const typeConsistencyScore = columnsWithData > 0 ? totalConsistency / columnsWithData : 0;
        let headerLikeScore = 0;
        if (rowCount > 1) {
            let headerCells = 0;
            for (let c = 0; c < colCount; c++) {
                const value = regionData[0][c];
                if (value !== undefined &&
                    value !== null &&
                    String(value).trim() !== '' &&
                    typeof value === 'string' &&
                    value.length < 50) {
                    headerCells++;
                }
            }
            headerLikeScore = headerCells / colCount;
        }
        if (isSmallTable) {
            return (densityScore * 0.3 + typeConsistencyScore * 0.4 + headerLikeScore * 0.3);
        }
        else {
            return (densityScore * 0.25 +
                typeConsistencyScore * 0.35 +
                headerLikeScore * 0.4);
        }
    }
    calculateOptimalHeaderCount(llmHeaderCount, regionData) {
        if (regionData.length <= 1)
            return 1;
        const maxPossible = Math.min(5, Math.floor(regionData.length / 2));
        const headerCount = Math.max(1, Math.min(llmHeaderCount, maxPossible));
        const potentialHeaderRows = Math.min(5, regionData.length - 1);
        const headerLikeScores = [];
        for (let r = 0; r < potentialHeaderRows; r++) {
            let textValues = 0;
            let numericValues = 0;
            let nonEmptyValues = 0;
            for (let c = 0; c < regionData[r].length; c++) {
                const value = regionData[r][c];
                if (value !== undefined &&
                    value !== null &&
                    String(value).trim() !== '') {
                    nonEmptyValues++;
                    if (typeof value === 'number' || !isNaN(Number(value))) {
                        numericValues++;
                    }
                    else {
                        textValues++;
                    }
                }
            }
            const cellCoverage = nonEmptyValues / regionData[r].length;
            const textRatio = nonEmptyValues > 0 ? textValues / nonEmptyValues : 0;
            headerLikeScores.push(cellCoverage * 0.5 + textRatio * 0.5);
        }
        for (let r = 1; r < headerLikeScores.length; r++) {
            if (headerLikeScores[r] < headerLikeScores[r - 1] * 0.7) {
                return r;
            }
        }
        return headerCount;
    }
    detectHiddenRowsAndColumns(sheet) {
        const hiddenRows = new Set();
        const hiddenCols = new Set();
        if (sheet['!rows']) {
            for (let r = 0; r < sheet['!rows'].length; r++) {
                if (sheet['!rows'][r] && sheet['!rows'][r].hidden) {
                    hiddenRows.add(r);
                }
            }
        }
        if (sheet['!cols']) {
            for (let c = 0; c < sheet['!cols'].length; c++) {
                if (sheet['!cols'][c] && sheet['!cols'][c].hidden) {
                    hiddenCols.add(c);
                }
            }
        }
        this.logger.log(`Detected ${hiddenRows.size} hidden rows and ${hiddenCols.size} hidden columns`);
        return { hiddenRows, hiddenCols };
    }
    normalizeSheetDataWithHiddenCells(data, hiddenRows, hiddenCols, preserveHidden = false) {
        this.logger.log(`Normalizing sheet data: ${data.length} rows, ${hiddenRows.size} hidden rows, ${hiddenCols.size} hidden columns`);
        if (data.length === 0)
            return [];
        const maxCols = Math.max(...data.map((row) => row.length));
        const normalizedData = [];
        for (let r = 0; r < data.length; r++) {
            if (!preserveHidden && hiddenRows.has(r))
                continue;
            const row = data[r];
            const normalizedRow = [];
            for (let c = 0; c < maxCols; c++) {
                if (!preserveHidden && hiddenCols.has(c))
                    continue;
                const value = c < row.length ? row[c] : '';
                normalizedRow.push(value !== undefined && value !== null ? value : '');
            }
            normalizedData.push(normalizedRow);
        }
        this.logger.log(`Sheet normalized to: ${normalizedData.length} rows x ${normalizedData[0]?.length || 0} columns`);
        return normalizedData;
    }
    detectNativeTables(workbook, sheetName) {
        this.logger.log(`Detecting native Excel tables in sheet: ${sheetName}`);
        const tables = [];
        if (workbook.Workbook) {
            if (workbook.Workbook.Names) {
                for (const name of workbook.Workbook.Names) {
                    if (name.Sheet === workbook.SheetNames.indexOf(sheetName) &&
                        (name.Name.startsWith('Table') || name.Name.includes('_Table'))) {
                        try {
                            if (name.Ref &&
                                name.Ref.includes('!') &&
                                name.Ref.includes(':')) {
                                const range = this.parseExcelReference(name.Ref);
                                if (range) {
                                    tables.push({
                                        name: name.Name,
                                        ref: name.Ref,
                                        range,
                                    });
                                }
                            }
                        }
                        catch (error) {
                            this.logger.warn(`Error parsing name reference: ${error.message}`);
                        }
                    }
                }
            }
            if (workbook.Workbook.Names) {
                for (const name of workbook.Workbook.Names) {
                    if (name.Sheet === workbook.SheetNames.indexOf(sheetName) &&
                        (name.Name.endsWith('Data') || name.Name.includes('Range'))) {
                        try {
                            if (name.Ref &&
                                name.Ref.includes('!') &&
                                name.Ref.includes(':')) {
                                const range = this.parseExcelReference(name.Ref);
                                if (range) {
                                    tables.push({
                                        name: name.Name,
                                        ref: name.Ref,
                                        range,
                                    });
                                }
                            }
                        }
                        catch (error) {
                            this.logger.warn(`Error parsing data range reference: ${error.message}`);
                        }
                    }
                }
            }
        }
        if (tables.length === 0) {
            this.logger.log(`No native tables found in metadata, will rely on detection algorithms`);
        }
        else {
            this.logger.log(`Detected ${tables.length} potential tables from metadata in sheet: ${sheetName}`);
        }
        return tables;
    }
    detectTablesFromFormatting(sheet) {
        const tables = [];
        const range = sheet['!ref'] ? XLSX.utils.decode_range(sheet['!ref']) : null;
        if (!range)
            return tables;
        this.logger.log(`Attempting to detect tables from sheet formatting`);
        tables.push({
            name: 'DetectedTable',
            range: {
                s: { r: range.s.r, c: range.s.c },
                e: { r: range.e.r, c: range.e.c },
            },
        });
        return tables;
    }
    parseExcelReference(ref) {
        const rangePart = ref.includes('!') ? ref.split('!')[1] : ref;
        const cleanRange = rangePart.replace(/\$/g, '');
        const [start, end] = cleanRange.split(':');
        if (!start || !end)
            return null;
        try {
            const s = XLSX.utils.decode_cell(start);
            const e = XLSX.utils.decode_cell(end);
            return { s, e };
        }
        catch (error) {
            this.logger.warn(`Invalid cell reference: ${ref}`);
            return null;
        }
    }
    extractNativeTable(sheet, tableRange) {
        const { s, e } = tableRange;
        const tableData = [];
        for (let r = s.r; r <= e.r; r++) {
            const row = [];
            for (let c = s.c; c <= e.c; c++) {
                const cellRef = XLSX.utils.encode_cell({ r, c });
                const cell = sheet[cellRef];
                row.push(cell ? cell.v : '');
            }
            tableData.push(row);
        }
        const headerRowCount = 1;
        const headers = tableData.slice(0, headerRowCount);
        const rows = tableData.slice(headerRowCount);
        this.logger.log(`Native table extracted with ${headers.length} header rows and ${rows.length} data rows`);
        return { headers, rows };
    }
    enhancedUnmergeCells(sheet) {
        this.logger.log(`Enhanced unmerging of cells in worksheet`);
        const merges = sheet['!merges'];
        if (!merges) {
            this.logger.log('No merged cells found in worksheet');
            return;
        }
        this.logger.log(`Found ${merges.length} merged regions to process`);
        for (const merge of merges) {
            const cellAddress = XLSX.utils.encode_cell(merge.s);
            const cell = sheet[cellAddress];
            if (!cell)
                continue;
            const value = cell.v;
            const type = cell.t;
            const formula = cell.f;
            const rowSpan = merge.e.r - merge.s.r + 1;
            const colSpan = merge.e.c - merge.s.c + 1;
            for (let r = merge.s.r; r <= merge.e.r; ++r) {
                for (let c = merge.s.c; c <= merge.e.c; ++c) {
                    const address = { r, c };
                    const cellRef = XLSX.utils.encode_cell(address);
                    if (r === merge.s.r &&
                        c === merge.s.c &&
                        (rowSpan > 1 || colSpan > 1)) {
                        sheet[cellRef] = {
                            v: `|<r${rowSpan}#c${colSpan}>${value}|`,
                            t: type,
                            f: formula,
                        };
                    }
                    else {
                        sheet[cellRef] = {
                            v: `lalal`,
                            t: type,
                        };
                    }
                }
            }
        }
        delete sheet['!merges'];
        this.logger.log('Enhanced cell unmerging complete');
    }
    postProcessMergedCells(data, originalMerges) {
        if (!originalMerges || originalMerges.length === 0 || data.length === 0) {
            return data;
        }
        this.logger.log(`Post-processing ${originalMerges.length} merged regions in ${data.length}x${data[0].length} data grid`);
        const processedData = JSON.parse(JSON.stringify(data));
        const rowCount = data.length;
        const colCount = Math.max(...data.map((row) => row.length));
        const mergeMap = [];
        for (let r = 0; r < rowCount; r++) {
            mergeMap[r] = [];
            for (let c = 0; c < colCount; c++) {
                mergeMap[r][c] = null;
            }
        }
        this.logger.log(`Processing merges for data grid ${rowCount}x${colCount}`);
        const validMerges = originalMerges.filter((merge) => {
            const isValid = merge.s.r >= 0 &&
                merge.s.r < rowCount &&
                merge.s.c >= 0 &&
                merge.s.c < colCount &&
                merge.e.r >= 0 &&
                merge.e.r < rowCount &&
                merge.e.c >= 0 &&
                merge.e.c < colCount;
            if (!isValid) {
                this.logger.warn(`Skipping out-of-bounds merge: (${merge.s.r},${merge.s.c}) to (${merge.e.r},${merge.e.c})`);
            }
            return isValid;
        });
        for (let i = 0; i < validMerges.length; i++) {
            const merge = validMerges[i];
            const rowSpan = merge.e.r - merge.s.r + 1;
            const colSpan = merge.e.c - merge.s.c + 1;
            const firstCellValue = data[merge.s.r]?.[merge.s.c] || '';
            this.logger.debug(`Merge #${i}: (${merge.s.r},${merge.s.c}) spans ${rowSpan}x${colSpan}, value: "${firstCellValue}"`);
            for (let r = merge.s.r; r <= merge.e.r; r++) {
                for (let c = merge.s.c; c <= merge.e.c; c++) {
                    if (r < rowCount && c < colCount) {
                        mergeMap[r][c] = {
                            isMerged: true,
                            isFirstCell: r === merge.s.r && c === merge.s.c,
                            firstCellRow: merge.s.r,
                            firstCellCol: merge.s.c,
                            rowSpan,
                            colSpan,
                            value: firstCellValue,
                        };
                    }
                }
            }
        }
        let firstCellsProcessed = 0;
        let secondaryCellsProcessed = 0;
        for (let r = 0; r < rowCount; r++) {
            for (let c = 0; c < colCount; c++) {
                const mergeInfo = mergeMap[r][c];
                if (!mergeInfo)
                    continue;
                if (r >= processedData.length)
                    continue;
                if (c >= (processedData[r]?.length || 0))
                    continue;
                if (mergeInfo.isFirstCell) {
                    if (mergeInfo.rowSpan > 1 || mergeInfo.colSpan > 1) {
                        processedData[r][c] =
                            `|<r${mergeInfo.rowSpan}#c${mergeInfo.colSpan}>${mergeInfo.value}|`;
                        firstCellsProcessed++;
                    }
                }
                else {
                    processedData[r][c] = `<rm>${mergeInfo.value}</rm>`;
                    secondaryCellsProcessed++;
                }
            }
        }
        this.logger.log(`Processed ${firstCellsProcessed} first cells and ${secondaryCellsProcessed} secondary cells`);
        return processedData;
    }
    adjustTableForHiddenCells(table, range, hiddenRows, hiddenCols) {
        const rowMapping = [];
        const colMapping = [];
        let adjustedRowIdx = 0;
        for (let r = range.s.r; r <= range.e.r; r++) {
            rowMapping[r] = hiddenRows.has(r) ? -1 : adjustedRowIdx++;
        }
        let adjustedColIdx = 0;
        for (let c = range.s.c; c <= range.e.c; c++) {
            colMapping[c] = hiddenCols.has(c) ? -1 : adjustedColIdx++;
        }
        const adjustedHeaders = [];
        for (let headerRowIdx = 0; headerRowIdx < table.headers.length; headerRowIdx++) {
            const originalRow = range.s.r + headerRowIdx;
            if (rowMapping[originalRow] === -1)
                continue;
            const adjustedRow = [];
            for (let c = range.s.c; c <= range.e.c; c++) {
                if (colMapping[c] === -1)
                    continue;
                const colOffset = c - range.s.c;
                const headerRow = table.headers[headerRowIdx];
                adjustedRow.push(colOffset < headerRow.length ? headerRow[colOffset] : '');
            }
            adjustedHeaders.push(adjustedRow);
        }
        const adjustedRows = [];
        for (let dataRowIdx = 0; dataRowIdx < table.rows.length; dataRowIdx++) {
            const originalRow = range.s.r + table.headers.length + dataRowIdx;
            if (rowMapping[originalRow] === -1)
                continue;
            const adjustedRow = [];
            for (let c = range.s.c; c <= range.e.c; c++) {
                if (colMapping[c] === -1)
                    continue;
                const colOffset = c - range.s.c;
                const dataRow = table.rows[dataRowIdx];
                adjustedRow.push(colOffset < dataRow.length ? dataRow[colOffset] : '');
            }
            adjustedRows.push(adjustedRow);
        }
        return { headers: adjustedHeaders, rows: adjustedRows };
    }
    deduplicateTables(tables) {
        if (tables.length <= 1)
            return tables;
        const unique = [];
        const seen = new Set();
        for (const table of tables) {
            if (table.headers.length === 0 || table.rows.length === 0)
                continue;
            const headerSample = table.headers[0]
                .slice(0, 3)
                .map((h) => String(h || ''))
                .join('|');
            const dimensions = `${table.headers.length}:${table.rows.length}:${table.headers[0].length}`;
            const signature = `${headerSample}:${dimensions}`;
            if (!seen.has(signature)) {
                seen.add(signature);
                unique.push(table);
            }
        }
        return unique;
    }
};
exports.DocumentParserService = DocumentParserService;
exports.DocumentParserService = DocumentParserService = DocumentParserService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [llm_rate_limiter_service_1.LlmRateLimiterService])
], DocumentParserService);
//# sourceMappingURL=document-parser.service.js.map