import { useSearchParams } from 'react-router-dom';
import { useEffect, useMemo } from 'react';
import { SparklesIcon } from 'lucide-react';

import ProjectSettings from './ProjectSettings';
import WorkspaceSettings from './WorkspaceSettings';
import SuperAdminSettings from './SuperAdminSettings';

import { MainLayout } from '@/components/MainLayout';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuthentication } from '@/api/authentication/authentication.query';
import { cn } from '@/lib/utils';

const Settings = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const defaultTab = searchParams.get('tab') || 'project';
  const { user: loggedInUser } = useAuthentication();

  const currentUser = useMemo(() => {
    return loggedInUser?.userWorkspaces[0];
  }, [loggedInUser]);

  const handleTabChange = (value: string) => {
    setSearchParams({ tab: value });
  };

  useEffect(() => {
    if (!searchParams.get('tab')) {
      setSearchParams({ tab: defaultTab });
    }
  }, [defaultTab, searchParams, setSearchParams]);

  const isSuperAdmin = currentUser?.role === 'SUPER_ADMIN';

  return (
    <MainLayout>
      <div className={`pb-60`}>
        <div className="flex gap-4 w-full items-center mt-8 mb-12">
          <h1 className={`text-4xl font-bold`}>Settings</h1>
        </div>
        <Tabs
          defaultValue={defaultTab}
          onValueChange={handleTabChange}
          className="w-full"
        >
          <TabsList
            className={cn(
              'grid gap-1 mb-5 w-fit',
              isSuperAdmin ? 'grid-cols-3' : 'grid-cols-2'
            )}
          >
            <TabsTrigger value="workspace" className="h-8 mb-1">
              Workspace
            </TabsTrigger>
            <TabsTrigger value="project" className="h-8 mb-1">
              Project
            </TabsTrigger>
            {isSuperAdmin && (
              <TabsTrigger value="super-admin" className="h-8 mb-1">
                <SparklesIcon className="mr-2 w-4 h-4 inline" /> Super
              </TabsTrigger>
            )}
          </TabsList>
          <TabsContent value="project" className="space-y-5">
            <ProjectSettings />
          </TabsContent>
          <TabsContent value="workspace" className="space-y-5">
            <WorkspaceSettings />
          </TabsContent>
          {isSuperAdmin && (
            <TabsContent value="super-admin" className="space-y-5">
              <SuperAdminSettings
                currentWorkspaceId={currentUser.workspaceId}
              />
            </TabsContent>
          )}
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default Settings;
