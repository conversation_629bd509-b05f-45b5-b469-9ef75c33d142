"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DatapointGenerationProcessor_1, DatapointReviewProcessor_1, ChunkExtractionProcessor_1, ChunkLinkingProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChunkLinkingProcessor = exports.ChunkExtractionProcessor = exports.DatapointReviewProcessor = exports.DatapointGenerationProcessor = void 0;
const common_1 = require("@nestjs/common");
const bull_1 = require("@nestjs/bull");
const jobs_1 = require("../types/jobs");
const document_service_1 = require("../document/document.service");
const datapoint_document_chunk_service_1 = require("../datapoint-document-chunk/datapoint-document-chunk.service");
const users_service_1 = require("../users/users.service");
const document_entity_1 = require("../document/entities/document.entity");
const datapoint_request_service_1 = require("../datapoint/datapoint-request.service");
const data_request_service_1 = require("../data-request/data-request.service");
const constants_1 = require("../data-request/constants");
const common_util_1 = require("../util/common-util");
let DatapointGenerationProcessor = DatapointGenerationProcessor_1 = class DatapointGenerationProcessor {
    constructor(datapointRequestService, dataRequestService) {
        this.datapointRequestService = datapointRequestService;
        this.dataRequestService = dataRequestService;
        this.logger = new common_1.Logger(DatapointGenerationProcessor_1.name);
    }
    async generateDatapoint(job) {
        this.logger.log(`Processing datapoint generation: ${job.data.datapointRequestId}`);
        const payload = job?.data;
        try {
            await this.datapointRequestService.generateDatapointContentWithAI({
                datapointRequestId: payload.datapointRequestId,
                userId: payload.userId,
                workspaceId: payload.workspaceId,
                useExistingReportTextForReference: payload.useExistingReportTextForReference,
            });
            this.dataRequestService.emitSseEvents({
                dataRequestId: payload.dataRequestId,
                datapointRequestId: payload.datapointRequestId,
                status: 'success',
                operation: constants_1.BULK_DATAPOINT_OPERATIONS.GENERATE,
            });
        }
        catch (error) {
            this.logger.error(`Error generating datapoint: ${job.data.datapointId}`, error);
            this.dataRequestService.emitSseEvents({
                dataRequestId: payload.dataRequestId,
                datapointRequestId: payload.datapointRequestId,
                status: 'failed',
                operation: constants_1.BULK_DATAPOINT_OPERATIONS.GENERATE,
            });
        }
    }
};
exports.DatapointGenerationProcessor = DatapointGenerationProcessor;
__decorate([
    (0, bull_1.Process)({ name: jobs_1.JobQueue.DatapointGenerate, concurrency: 10 }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DatapointGenerationProcessor.prototype, "generateDatapoint", null);
exports.DatapointGenerationProcessor = DatapointGenerationProcessor = DatapointGenerationProcessor_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, bull_1.Processor)(jobs_1.JobProcessor.DatapointGeneration),
    __metadata("design:paramtypes", [datapoint_request_service_1.DatapointRequestService,
        data_request_service_1.DataRequestService])
], DatapointGenerationProcessor);
let DatapointReviewProcessor = DatapointReviewProcessor_1 = class DatapointReviewProcessor {
    constructor(datapointRequestService, dataRequestService) {
        this.datapointRequestService = datapointRequestService;
        this.dataRequestService = dataRequestService;
        this.logger = new common_1.Logger(DatapointReviewProcessor_1.name);
    }
    async reviewDatapoint(job) {
        this.logger.log(`Processing datapoint reviewing: ${job.data.datapointRequestId}`);
        const payload = job?.data;
        try {
            await this.datapointRequestService.reviewDatapointContentWithAI({
                datapointRequestId: payload.datapointRequestId,
                userId: payload.userId,
                workspaceId: payload.workspaceId,
            });
            this.dataRequestService.emitSseEvents({
                dataRequestId: payload.dataRequestId,
                datapointRequestId: payload.datapointRequestId,
                status: 'success',
                operation: constants_1.BULK_DATAPOINT_OPERATIONS.REVIEW,
            });
        }
        catch (error) {
            this.logger.error(`Error reviewing datapoint: ${job.data.datapointId}`, error);
            this.dataRequestService.emitSseEvents({
                dataRequestId: payload.dataRequestId,
                datapointRequestId: payload.datapointRequestId,
                status: 'failed',
                operation: constants_1.BULK_DATAPOINT_OPERATIONS.REVIEW,
            });
        }
    }
};
exports.DatapointReviewProcessor = DatapointReviewProcessor;
__decorate([
    (0, bull_1.Process)({ name: jobs_1.JobQueue.DatapointReview, concurrency: 10 }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DatapointReviewProcessor.prototype, "reviewDatapoint", null);
exports.DatapointReviewProcessor = DatapointReviewProcessor = DatapointReviewProcessor_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, bull_1.Processor)(jobs_1.JobProcessor.DatapointReview),
    __metadata("design:paramtypes", [datapoint_request_service_1.DatapointRequestService,
        data_request_service_1.DataRequestService])
], DatapointReviewProcessor);
let ChunkExtractionProcessor = ChunkExtractionProcessor_1 = class ChunkExtractionProcessor {
    constructor(documentService, chunkLinkingQueue) {
        this.documentService = documentService;
        this.chunkLinkingQueue = chunkLinkingQueue;
        this.logger = new common_1.Logger(ChunkExtractionProcessor_1.name);
    }
    async handleChunkExtraction(job) {
        const { documentId, pageNumbers, answerId, comment, premiumParse } = job.data;
        this.logger.log(`Bull Processing extraction for document: ${documentId}`);
        try {
            await this.documentService.extractDocumentChunks(documentId, premiumParse);
            if (pageNumbers && answerId) {
                const pages = (0, common_util_1.parsePageRanges)(pageNumbers);
                for (const page of pages) {
                    const documentChunk = await this.documentService.findDocumentChunkByPage(documentId, page);
                    if (documentChunk) {
                        await this.documentService.saveLinkedDocumentChunk({
                            documentChunkId: documentChunk.id,
                            answerId,
                            comment,
                        });
                    }
                }
            }
            else {
            }
            await this.documentService.updateDocumentStatus(documentId, {
                status: document_entity_1.DocumentStatus.LinkingDataFinished,
            });
        }
        catch (error) {
            this.logger.error(`Error processing document: ${documentId}`, error);
            await this.documentService.updateDocumentStatus(documentId, {
                status: document_entity_1.DocumentStatus.FailedExtraction,
            });
            throw error;
        }
    }
};
exports.ChunkExtractionProcessor = ChunkExtractionProcessor;
__decorate([
    (0, bull_1.Process)({ name: jobs_1.JobQueue.ChunkExtract, concurrency: 5 }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ChunkExtractionProcessor.prototype, "handleChunkExtraction", null);
exports.ChunkExtractionProcessor = ChunkExtractionProcessor = ChunkExtractionProcessor_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, bull_1.Processor)(jobs_1.JobProcessor.ChunkExtraction),
    __param(1, (0, bull_1.InjectQueue)(jobs_1.JobProcessor.ChunkDpLinking)),
    __metadata("design:paramtypes", [document_service_1.DocumentService, Object])
], ChunkExtractionProcessor);
let ChunkLinkingProcessor = ChunkLinkingProcessor_1 = class ChunkLinkingProcessor {
    constructor(documentService, datapointDocumentChunkService, userService) {
        this.documentService = documentService;
        this.datapointDocumentChunkService = datapointDocumentChunkService;
        this.userService = userService;
        this.logger = new common_1.Logger(ChunkLinkingProcessor_1.name);
    }
    async handleChunkLinking(job) {
        const { documentId } = job.data;
        this.logger.log(`Bull Processing linking for document: ${documentId}`);
        try {
            await this.documentService.updateDocumentStatus(documentId, {
                status: document_entity_1.DocumentStatus.LinkingDataFinished,
            });
        }
        catch (error) {
            this.logger.error(`Error linking document: ${documentId}`, error);
            await this.documentService.updateDocumentStatus(documentId, {
                status: document_entity_1.DocumentStatus.FailedLinking,
            });
            throw error;
        }
    }
};
exports.ChunkLinkingProcessor = ChunkLinkingProcessor;
__decorate([
    (0, bull_1.Process)({ name: jobs_1.JobQueue.ChunkDpLink, concurrency: 3 }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ChunkLinkingProcessor.prototype, "handleChunkLinking", null);
exports.ChunkLinkingProcessor = ChunkLinkingProcessor = ChunkLinkingProcessor_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, bull_1.Processor)(jobs_1.JobProcessor.ChunkDpLinking),
    __metadata("design:paramtypes", [document_service_1.DocumentService,
        datapoint_document_chunk_service_1.DatapointDocumentChunkService,
        users_service_1.UsersService])
], ChunkLinkingProcessor);
//# sourceMappingURL=queue.service.js.map