import { Component, ErrorInfo } from 'react';

class ErrorBoundary extends Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };

    // Suppress specific DOM nesting warnings in development !!
    if (process.env.NODE_ENV === 'development') {
      const suppressedWarnings = ['validateDOMNesting'];

      const originalConsoleError = console.error;

      console.error = (...args: any[]) => {
        if (
          typeof args[0] === 'string' &&
          suppressedWarnings.some((warning) => args[0].includes(warning))
        ) {
          return;
        }
        originalConsoleError(...args);
      };
    }
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error', error, errorInfo);
    // TODO: sentry
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="absolute w-full h-full flex flex-col justify-center items-center text-gray-800">
          <div>Something went wrong - please reload the page</div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
