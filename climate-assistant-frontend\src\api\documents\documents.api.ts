import axios from 'axios';

import { API_URL } from '@/api/apiConstants';
import { DocumentData } from '@/types/document';

export const fetchDocumentData = async (id: string): Promise<DocumentData> => {
  const response = await axios.get<DocumentData>(`${API_URL}/documents/${id}`);
  return response.data;
};

export const startDocumentExtraction = async (
  id: string
): Promise<{ message: string }> => {
  const response = await axios.post<{ message: string }>(
    `${API_URL}/documents/${id}/extract-chunks`,
    {
      premiumMode: true,
    }
  );
  return response.data;
};

export const startDocumentChunkDatapointLinking = async (
  id: string,
  testMode: boolean
): Promise<{}> => {
  const response = await axios.post<{ message: string }>(
    `${API_URL}/documents/${id}/link-to-datapoints`,
    {
      testing: testMode,
    }
  );
  return response.data;
};

export const updateDatapointLinkToDocumentChunk = async (
  documentChunkId: string,
  data: {
    datapointRequestId: string;
    linked: boolean;
  }[]
) => {
  await axios.post(
    `${API_URL}/documents/chunk/${documentChunkId}/link-datapoints`,
    data
  );
};

export const dateDocumentChunk = async (id: string) => {
  await axios.delete(`${API_URL}/documents/chunk/${id}`);
};
