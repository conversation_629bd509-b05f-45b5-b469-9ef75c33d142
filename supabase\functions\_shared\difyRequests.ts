/**
 * Types for interacting with the Dify Workflow API
 */

/**
 * File transfer method
 */
type TransferMethod = 'remote_url' | 'local_file';

/**
 * File types supported by the API
 */
type FileType = 
  | 'document' // TXT, MD, MARKDOWN, PDF, HTML, XLSX, XLS, DOCX, CSV, EML, MSG, PPTX, PPT, XML, EPUB
  | 'image'    // JPG, JPEG, PNG, GIF, WEBP, SVG
  | 'audio'    // MP3, M4A, WAV, WEBM, AMR
  | 'video'    // MP4, MOV, MPEG, MPGA
  | 'custom';  // Other file types

/**
 * Response mode for the API
 */
type ResponseMode = 'streaming' | 'blocking';

/**
 * File input structure for workflow
 */
interface FileInput {
  transfer_method: TransferMethod;
  upload_file_id?: string;  // Required when transfer_method is 'local_file'
  url?: string;             // Required when transfer_method is 'remote_url'
  type: FileType;
}

/**
 * Workflow run request parameters
 */
interface WorkflowRunRequest {
  inputs: Record<string, any | FileInput>;
  response_mode: ResponseMode;
  user: string;
  files?: Array<{
    type: FileType;
    transfer_method: TransferMethod;
    url?: string;
    upload_file_id?: string;
  }>;
}

/**
 * Workflow blocking response structure
 */
interface WorkflowBlockingResponse {
  workflow_run_id: string;
  task_id: string;
  data: {
    id: string;
    workflow_id: string;
    status: 'running' | 'succeeded' | 'failed' | 'stopped';
    outputs?: Record<string, any>;
    error?: string | null;
    elapsed_time: number;
    total_tokens: number;
    total_steps: number;
    created_at: number;
    finished_at: number;
  };
}

/**
 * Workflow run details response
 */
interface WorkflowRunDetailsResponse {
  id: string;
  workflow_id: string;
  status: 'running' | 'succeeded' | 'failed' | 'stopped';
  inputs: string;
  outputs: any;
  error: string | null;
  total_steps: number;
  total_tokens: number;
  created_at: string;
  finished_at: string;
  elapsed_time: number;
}

/**
 * File upload response
 */
interface FileUploadResponse {
  id: string;
  name: string;
  size: number;
  extension: string;
  mime_type: string;
  created_by: string;
  created_at: number;
}

/**
 * Streaming event types
 */
type StreamEvent = 
  | { event: 'workflow_started', task_id: string, workflow_run_id: string, data: any }
  | { event: 'node_started', task_id: string, workflow_run_id: string, data: any }
  | { event: 'node_finished', task_id: string, workflow_run_id: string, data: any }
  | { event: 'workflow_finished', task_id: string, workflow_run_id: string, data: any }
  | { event: 'tts_message', task_id: string, message_id: string, audio: string, created_at: number }
  | { event: 'tts_message_end', task_id: string, message_id: string, audio: string, created_at: number }
  | { event: 'ping' };

/**
 * Error structure from the API
 */
interface ApiError {
  error: string;
  message: string;
  status_code: number;
}

/**
 * Configuration for DifyClient
 */
interface DifyClientConfig {
  /** API key for authentication */
  apiKey: string;
  /** Base URL for the API */
  baseUrl?: string;
  /** Default timeout in milliseconds */
  defaultTimeout?: number;
  /** Default user identifier */
  defaultUser?: string;
}

/**
 * Options for running a workflow
 */
interface RunWorkflowOptions {
  /** Timeout in milliseconds */
  timeout?: number;
  /** Event handler for streaming responses */
  onEvent?: (event: StreamEvent) => void;
  /** Error handler */
  onError?: (error: Error | ApiError) => void;
}

/**
 * A client for interacting with the Dify API
 */
class DifyClient {
  private readonly apiKey: string;
  private readonly baseUrl: string;
  private readonly defaultTimeout: number;
  private readonly defaultUser?: string;

  /**
   * Creates a new instance of the DifyClient
   * 
   * @param config - Configuration for the client
   */
  constructor(config: DifyClientConfig) {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl || 'https://dify-ai.glacier.eco/v1';
    this.defaultTimeout = config.defaultTimeout || 300000; // 300 seconds
    this.defaultUser = config.defaultUser;
  }

  /**
   * Creates the default headers for API requests
   * 
   * @param contentType - Content type for the request
   * @returns Headers for the request
   */
  private getHeaders(contentType: string = 'application/json'): Record<string, string> {
    return {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': contentType
    };
  }

  /**
   * Handles errors from API responses
   * 
   * @param response - Fetch response object
   * @returns Promise that resolves to the response if successful, or rejects with an error
   */
  private async handleErrors(response: Response): Promise<Response> {
    if (!response.ok) {
      const errorData = await response.json() as ApiError;
      throw new Error(`API Error: ${errorData.error} - ${errorData.message}`);
    }
    return response;
  }

  /**
   * Runs a workflow with the specified parameters
   * 
   * @param params - Parameters for the workflow run
   * @param options - Additional options for the API call
   * @returns Promise with the workflow response (for blocking mode) or void (for streaming mode)
   * 
   * @example
   * // Blocking mode example
   * const result = await difyClient.runWorkflow({
   *   inputs: { query: 'What is EcoVadis?' },
   *   response_mode: 'blocking',
   *   user: 'user-123'
   * });
   * 
   * @example
   * // Streaming mode example
   * await difyClient.runWorkflow({
   *   inputs: { query: 'What is EcoVadis?' },
   *   response_mode: 'streaming',
   *   user: 'user-123'
   * }, {
   *   onEvent: (event) => {
   *     if (event.event === 'workflow_finished') {
   *       console.log('Workflow completed');
   *     }
   *   }
   * });
   * 
   * @example
   * // Example with file upload
   * const fileData = await difyClient.uploadFile(file, 'user-123');
   * const result = await difyClient.runWorkflow({
   *   inputs: {
   *     document: {
   *       transfer_method: 'local_file',
   *       upload_file_id: fileData.id,
   *       type: 'document'
   *     }
   *   },
   *   response_mode: 'blocking',
   *   user: 'user-123'
   * });
   */
  public async runWorkflow(
    params: WorkflowRunRequest,
    options: RunWorkflowOptions = {}
  ): Promise<WorkflowBlockingResponse | void> {
    // Use default user if not provided
    const workflowParams = {
      ...params,
      user: params.user || this.defaultUser || ''
    };

    // Ensure user is provided
    if (!workflowParams.user) {
      throw new Error('User identifier is required');
    }

    const {
      timeout = this.defaultTimeout,
      onEvent,
      onError
    } = options;

    try {
      if (workflowParams.response_mode === 'blocking') {
        // For blocking mode, use standard fetch with timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        const response = await fetch(`${this.baseUrl}/workflows/run`, {
          method: 'POST',
          headers: this.getHeaders(),
          body: JSON.stringify(workflowParams),
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        await this.handleErrors(response);
        return await response.json() as WorkflowBlockingResponse;
      } else {
        // For streaming mode, use EventSource
        const params = new URLSearchParams();
        params.append('data', JSON.stringify(workflowParams));
        
        const eventSource = new EventSource(
          `${this.baseUrl}/workflows/run?${params.toString()}`,
          {
            headers: this.getHeaders()
          } as any
        );
        
        return new Promise<void>((resolve, reject) => {
          const cleanup = () => {
            eventSource.close();
            resolve();
          };
          
          eventSource.onmessage = (event) => {
            try {
              const parsedEvent = JSON.parse(event.data) as StreamEvent;
              if (onEvent) onEvent(parsedEvent);
              
              // Automatically close the connection when the workflow is finished
              if (parsedEvent.event === 'workflow_finished') {
                cleanup();
              }
            } catch (error) {
              if (onError) onError(error as Error);
              cleanup();
            }
          };
          
          eventSource.onerror = (error) => {
            cleanup();
          };
          
          // Set timeout for streaming mode as well
          const timeoutId = setTimeout(() => {
            if (onError) onError(new Error('Streaming connection timed out'));
            cleanup();
          }, timeout);
          
          // Clear timeout when workflow finishes
          eventSource.addEventListener('workflow_finished', () => {
            clearTimeout(timeoutId);
            cleanup();
          });
        });
      }
    } catch (error) {
      if (onError) {
        onError(error as Error);
        return;
      }
      throw error;
    }
  }

  /**
   * Gets details about a workflow run
   * 
   * @param workflowId - ID of the workflow to get details for
   * @returns Promise with the workflow run details
   * 
   * @example
   * const details = await difyClient.getWorkflowRunDetails('workflow-123');
   * console.log(details.status);
   */
  public async getWorkflowRunDetails(workflowId: string): Promise<WorkflowRunDetailsResponse> {
    const response = await fetch(`${this.baseUrl}/workflows/run/${workflowId}`, {
      method: 'GET',
      headers: this.getHeaders()
    });
    
    await this.handleErrors(response);
    return await response.json() as WorkflowRunDetailsResponse;
  }

  /**
   * Stops an ongoing workflow task
   * 
   * @param taskId - ID of the task to stop
   * @param user - User identifier (must match the one used to start the workflow)
   * @returns Promise with the result
   * 
   * @example
   * const result = await difyClient.stopWorkflow('task-123', 'user-123');
   * console.log(result.result); // "success"
   */
  public async stopWorkflow(taskId: string, user: string = this.defaultUser || ''): Promise<{ result: string }> {
    if (!user) {
      throw new Error('User identifier is required');
    }
    
    const response = await fetch(`${this.baseUrl}/workflows/tasks/${taskId}/stop`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({ user })
    });
    
    await this.handleErrors(response);
    return await response.json() as { result: string };
  }

  /**
   * Uploads a file to be used in a workflow
   * 
   * @param file - File to upload
   * @param user - User identifier
   * @returns Promise with the uploaded file information
   * 
   * @example
   * const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
   * const file = fileInput.files?.[0];
   * if (file) {
   *   const result = await difyClient.uploadFile(file, 'user-123');
   *   console.log(result.id); // Use this ID in the workflow inputs
   * }
   */
  public async uploadFile(file: File, user: string = this.defaultUser || ''): Promise<FileUploadResponse> {
    if (!user) {
      throw new Error('User identifier is required');
    }
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('user', user);
    
    const response = await fetch(`${this.baseUrl}/files/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: formData
    });
    
    await this.handleErrors(response);
    return await response.json() as FileUploadResponse;
  }

  /**
   * Gets application information
   * 
   * @returns Promise with the application information
   * 
   * @example
   * const info = await difyClient.getApplicationInfo();
   * console.log(info.name);
   */
  public async getApplicationInfo(): Promise<{ name: string; description: string; tags: string[] }> {
    const response = await fetch(`${this.baseUrl}/info`, {
      method: 'GET',
      headers: this.getHeaders()
    });
    
    await this.handleErrors(response);
    return await response.json();
  }

  /**
   * Gets application parameters information
   * 
   * @returns Promise with the application parameters information
   * 
   * @example
   * const params = await difyClient.getApplicationParameters();
   * console.log(params.user_input_form);
   */
  public async getApplicationParameters(): Promise<{
    user_input_form: any[];
    file_upload: {
      image: {
        enabled: boolean;
        number_limits: number;
        transfer_methods: string[];
      }
    };
    system_parameters: {
      file_size_limit: number;
      image_file_size_limit: number;
      audio_file_size_limit: number;
      video_file_size_limit: number;
    };
  }> {
    const response = await fetch(`${this.baseUrl}/parameters`, {
      method: 'GET',
      headers: this.getHeaders()
    });
    
    await this.handleErrors(response);
    return await response.json();
  }

  /**
   * Gets workflow logs
   * 
   * @param options - Options for filtering logs
   * @returns Promise with the workflow logs
   * 
   * @example
   * const logs = await difyClient.getWorkflowLogs({ page: 1, limit: 10 });
   * console.log(logs.data);
   */
  public async getWorkflowLogs(options: {
    keyword?: string;
    status?: 'succeeded' | 'failed' | 'stopped';
    page?: number;
    limit?: number;
  } = {}): Promise<{
    page: number;
    limit: number;
    total: number;
    has_more: boolean;
    data: any[];
  }> {
    const params = new URLSearchParams();
    if (options.keyword) params.append('keyword', options.keyword);
    if (options.status) params.append('status', options.status);
    if (options.page) params.append('page', options.page.toString());
    if (options.limit) params.append('limit', options.limit.toString());
    
    const response = await fetch(`${this.baseUrl}/workflows/logs?${params.toString()}`, {
      method: 'GET',
      headers: this.getHeaders()
    });
    
    await this.handleErrors(response);
    return await response.json();
  }
}

export {
  DifyClient,
  // Type exports
  type WorkflowRunRequest,
  type WorkflowBlockingResponse,
  type WorkflowRunDetailsResponse,
  type FileUploadResponse,
  type StreamEvent,
  type FileInput,
  type TransferMethod,
  type FileType,
  type ResponseMode,
  type DifyClientConfig,
  type RunWorkflowOptions
};