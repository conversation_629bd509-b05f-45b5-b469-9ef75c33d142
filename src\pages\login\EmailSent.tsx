import { Button } from '@/components/ui/button';

const EmailSent = ({ switchToLogin }: { switchToLogin: () => void }) => {
  return (
    <div className={`flex flex-col w-full max-w-[380px]`}>
      <div className={`font-semibold text-3xl text-center mb-8`}>
        Password reset email sent
      </div>
      <div className="mb-4 p-4 bg-gray-100 rounded-lg">
        If a Glacier account exists for this email, a password reset link will
        be sent to it. You'll receive this email within 5 minutes. Be sure to
        check your spam folder, too.
      </div>
      <div className="text-center">
        <Button
          variant="link"
          onClick={(e) => {
            e.preventDefault();
            switchToLogin();
          }}
          className="text-sm"
        >
          Back to Log in
        </Button>
      </div>
    </div>
  );
};

export default EmailSent;
