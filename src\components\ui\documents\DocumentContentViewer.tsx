
import { DocumentContent, LinkedQuestion } from '@/types/ecovadis';
import { Card, CardContent } from '@/components/ui/card';
import ReactMarkdown from 'react-markdown';
import { FileImage, Link } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { DocumentLinksManager } from './DocumentLinksManager';

interface DocumentContentViewerProps {
  content: DocumentContent[];
  fileType: string;
  documentId: string;
  linkedQuestions?: LinkedQuestion[];
  onUpdateLinks?: (updatedLinks: LinkedQuestion[]) => void;
}

export function DocumentContentViewer({ 
  content, 
  fileType, 
  documentId,
  linkedQuestions = [],
  onUpdateLinks = () => {}
}: DocumentContentViewerProps) {
  const [isLinksDialogOpen, setIsLinksDialogOpen] = useState(false);
  const [currentPageNumber, setCurrentPageNumber] = useState<number | undefined>(undefined);
  
  if (!content || content.length === 0) {
    return (
      <div className="py-10 text-center text-gray-500">
        <p>Content is being processed or unavailable</p>
      </div>
    );
  }

  const renderMarkdown = (text: string) => {
    return (
      <div className="prose max-w-none">
        <ReactMarkdown>{text}</ReactMarkdown>
      </div>
    );
  };

  const renderImages = (images: string[] | undefined) => {
    if (!images || images.length === 0) return null;

    return (
      <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {images.map((image, index) => (
          <div key={index} className="border rounded-md overflow-hidden">
            <img 
              src={image} 
              alt={`Image ${index + 1}`} 
              className="w-full h-auto" 
            />
          </div>
        ))}
      </div>
    );
  };
  
  const handleOpenLinksManager = (pageNumber?: number) => {
    setCurrentPageNumber(pageNumber);
    setIsLinksDialogOpen(true);
  };
  
  const handleCloseLinksManager = () => {
    setIsLinksDialogOpen(false);
  };

  // Filter linked questions for a specific page
  const getLinkedQuestionsForPage = (pageNumber: number) => {
    return linkedQuestions.filter(q => {
      if (!q.pages) return false;
      
      // Handle page ranges and individual pages
      return q.pages.split(',').some(pageRange => {
        const trimmed = pageRange.trim();
        if (trimmed.includes('-')) {
          const [start, end] = trimmed.split('-').map(Number);
          return pageNumber >= start && pageNumber <= end;
        }
        return Number(trimmed) === pageNumber;
      });
    });
  };

  if (fileType === 'image') {
    // For image files, display the main image prominently
    const mainImage = content[0]?.images?.[0];
    return (
      <div className="space-y-6">
        <Card className="shadow-sm">
          <CardContent className="pt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-sm font-medium text-glacier-darkBlue">Image</h3>
              <Button 
                variant="outline" 
                size="sm" 
                className="gap-1 text-glacier-darkBlue"
                onClick={() => handleOpenLinksManager()}
              >
                <Link className="h-4 w-4" />
                Manage CSRD Links ({linkedQuestions.length})
              </Button>
            </div>
            
            {mainImage ? (
              <div className="rounded-md overflow-hidden border">
                <img src={mainImage} alt="Document Image" className="w-full h-auto" />
              </div>
            ) : (
              <div className="py-10 text-center text-gray-500 border rounded-md">
                <FileImage className="mx-auto h-12 w-12 opacity-30" />
                <p className="mt-2">Image is being processed or unavailable</p>
              </div>
            )}
            
            {content[0]?.text && renderMarkdown(content[0].text)}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {content.map((page) => {
        const pageLinkedQuestions = getLinkedQuestionsForPage(page.pageNumber);
        
        return (
          <div key={page.pageNumber}>
            <Card className="shadow-sm">
              <CardContent className="pt-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-sm font-medium text-glacier-darkBlue">
                    Page {page.pageNumber}
                  </h3>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="gap-1 text-glacier-darkBlue"
                    onClick={() => handleOpenLinksManager(page.pageNumber)}
                  >
                    <Link className="h-4 w-4" />
                    Manage CSRD Links ({pageLinkedQuestions.length})
                  </Button>
                </div>
                {renderMarkdown(page.text)}
                {renderImages(page.images)}
              </CardContent>
            </Card>
          </div>
        );
      })}
      
      <DocumentLinksManager 
        isOpen={isLinksDialogOpen}
        onClose={handleCloseLinksManager}
        linkedQuestions={currentPageNumber 
          ? getLinkedQuestionsForPage(currentPageNumber) 
          : linkedQuestions}
        onUpdateLinks={onUpdateLinks}
        documentId={documentId}
        pageNumber={currentPageNumber}
      />
    </div>
  );
}
