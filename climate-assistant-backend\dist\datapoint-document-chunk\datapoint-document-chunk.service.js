"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DatapointDocumentChunkService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatapointDocumentChunkService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const throat_1 = require("throat");
const prompts_service_1 = require("../prompts/prompts.service");
const document_chunk_entity_1 = require("../document/entities/document-chunk.entity");
const datapoint_request_entity_1 = require("../datapoint/entities/datapoint-request.entity");
const datapoint_document_chunk_entity_1 = require("./entities/datapoint-document-chunk.entity");
const document_entity_1 = require("../document/entities/document.entity");
const esrs_disclosure_requirement_entity_1 = require("../knowledge-base/entities/esrs-disclosure-requirement.entity");
const users_service_1 = require("../users/users.service");
const constants_1 = require("../constants");
const llm_rate_limiter_service_1 = require("../llm-rate-limiter/llm-rate-limiter.service");
let DatapointDocumentChunkService = DatapointDocumentChunkService_1 = class DatapointDocumentChunkService {
    constructor(llmRateLimitService, dataSource, promptService, documentChunkRepository, documentRepository, datapointRequestRepository, datapointDocumentChunkRepository, esrsDisclosureRequirementRepository, userService) {
        this.llmRateLimitService = llmRateLimitService;
        this.dataSource = dataSource;
        this.promptService = promptService;
        this.documentChunkRepository = documentChunkRepository;
        this.documentRepository = documentRepository;
        this.datapointRequestRepository = datapointRequestRepository;
        this.datapointDocumentChunkRepository = datapointDocumentChunkRepository;
        this.esrsDisclosureRequirementRepository = esrsDisclosureRequirementRepository;
        this.userService = userService;
        this.logger = new common_1.Logger(DatapointDocumentChunkService_1.name);
        this.limit = (0, throat_1.default)(4);
    }
    async linkDocumentChunksToDatapoints(documentId, userId, maxNumberOfChunks) {
        this.logger.log(`Start linking Datapoints to Chunks for DocumentId ${documentId}`);
        const document = await this.documentRepository.findOne({
            where: { id: documentId },
            relations: {
                workspace: {
                    projects: true,
                },
            },
        });
        const projectId = document.workspace?.projects[0]?.id;
        if (!projectId) {
            this.logger.error(`Document ${documentId} does not have a project`);
            return null;
        }
        this.logger.log(`Start linking Datapoints to Chunks for DocumentId ${documentId}, ProjectId ${projectId}`);
        document.status = document_entity_1.DocumentStatus.LinkingData;
        this.documentRepository.save(document);
        try {
            const documentChunks = await this.documentChunkRepository.find({
                where: {
                    documentId: documentId,
                },
                order: {
                    page: 'ASC',
                },
            });
            this.logger.log(`${documentId}: Found ${documentChunks.length} Document Chunks for Document`);
            const response = {
                inputTokensUsed: 0,
                outputTokensUsed: 0,
                costForDocument: 0,
                totalLinksCreated: 0,
                timeToGenerate: '0s',
                numberOfChunks: documentChunks.length,
                chunks: [],
            };
            const startTime = Date.now();
            const createdAtBatch = new Date();
            const chunksToProcess = documentChunks.slice(0, maxNumberOfChunks || documentChunks.length);
            const esrsDisclosrueRequirements = await this.esrsDisclosureRequirementRepository.find({
                relations: ['esrsDatapoints'],
            });
            const globalUser = await this.userService.findGlobalGlacierAIUser();
            const promises = chunksToProcess.map((chunk) => {
                return this.limit(async () => {
                    this.logger.log(`${documentId}: Linking chunk ${chunk.id}, Length: ${chunk.content.length}`);
                    const chunkContent = chunk.content;
                    this.logger.log(`${documentId}: ${chunk.id} Start Disclosure Requirement Classification`);
                    const disclosureRequirementClassificationChatCompletion = [
                        {
                            role: 'system',
                            content: this.promptService.generateDisclosureRequirementClassificationPrompt1(),
                        },
                        {
                            role: 'user',
                            content: chunkContent,
                        },
                        {
                            role: 'system',
                            content: this.promptService.generateDisclosureRequirementClassificationPrompt2(esrsDisclosrueRequirements),
                        },
                    ];
                    const disclosureRequirementClassificationResponse = await this.llmRateLimitService.handleRequest({
                        model: constants_1.LLM_MODELS['gpt-4o'],
                        messages: disclosureRequirementClassificationChatCompletion,
                        json: true,
                        temperature: 0,
                    });
                    if (disclosureRequirementClassificationResponse.status === 400) {
                        this.logger.error(`${documentId}: Chunk: ${chunk.id} DR Classification failed: ${disclosureRequirementClassificationResponse.response}`);
                        return null;
                    }
                    response.inputTokensUsed +=
                        disclosureRequirementClassificationResponse.token.prompt_tokens;
                    response.outputTokensUsed +=
                        disclosureRequirementClassificationResponse.token.completion_tokens;
                    response.costForDocument +=
                        disclosureRequirementClassificationResponse.token.total_cost;
                    const disclosureRequirementMatches = disclosureRequirementClassificationResponse.response.disclosureRequirementMatches?.map((dr) => {
                        const cleanMatchedId = dr
                            .replace(/'/g, '')
                            .replace(/"/g, '')
                            .replace(/\s/g, '');
                        return cleanMatchedId;
                    }) || [];
                    const chunkMatches = {
                        chunkContent: chunkContent,
                        topicMatches: [],
                        disclosureRequirementMatches: disclosureRequirementMatches,
                        datapointMatches: [],
                    };
                    if (disclosureRequirementMatches &&
                        disclosureRequirementMatches.length > 0) {
                        this.logger.log(`${documentId}: Chunk: ${chunk.id} DRsMatched: ${JSON.stringify(disclosureRequirementMatches.join(', '))}`);
                        for (const dr of disclosureRequirementMatches) {
                            await new Promise((resolve) => setTimeout(resolve, 500));
                            this.logger.log(`${documentId}: Chunk: ${chunk.id} DR Matching started: ${dr}`);
                            const esrsDisclosureRequirementMatch = esrsDisclosrueRequirements.find((eDr) => eDr.dr === dr);
                            if (!esrsDisclosureRequirementMatch) {
                                return this.logger.error(`${documentId}: Chunk: ${chunk.id} DR: ${dr} not found in the workspace`);
                            }
                            const esrsDatapointsToMatch = esrsDisclosureRequirementMatch.esrsDatapoints;
                            if (esrsDatapointsToMatch?.length > 0) {
                                const datapointClassificationChatCompletion = [
                                    {
                                        role: 'system',
                                        content: this.promptService.generateDatapointClassificationPrompt1(),
                                    },
                                    {
                                        role: 'user',
                                        content: chunkContent,
                                    },
                                    {
                                        role: 'user',
                                        content: this.promptService.generateSystemPromptForAllDatapoints(esrsDatapointsToMatch),
                                    },
                                    {
                                        role: 'system',
                                        content: this.promptService.generateDatapointClassificationPrompt2(esrsDisclosureRequirementMatch),
                                    },
                                ];
                                const datapointClassificationResponse = await this.llmRateLimitService.handleRequest({
                                    model: constants_1.LLM_MODELS['gpt-4o'],
                                    messages: datapointClassificationChatCompletion,
                                    json: true,
                                    temperature: 0,
                                });
                                if (datapointClassificationResponse.status === 400) {
                                    this.logger.error(`${documentId}: Chunk: ${chunk.id} DR: ${dr} DP Classification failed: ${datapointClassificationResponse.response}`);
                                    return null;
                                }
                                response.inputTokensUsed +=
                                    datapointClassificationResponse.token.prompt_tokens;
                                response.outputTokensUsed +=
                                    datapointClassificationResponse.token.completion_tokens;
                                response.costForDocument +=
                                    datapointClassificationResponse.token.total_cost;
                                const datapointMatches = datapointClassificationResponse.response.matchedDatapoints;
                                this.logger.log(`${documentId}: Chunk: ${chunk.id} DR: ${dr} DPs matched: ${JSON.stringify(datapointMatches)}`);
                                if (datapointMatches && datapointMatches.length > 0) {
                                    chunkMatches.datapointMatches.push(...datapointMatches);
                                    this.logger.log(`${documentId}: Chunk: ${chunk.id} DR: ${dr} DPMatched: ${JSON.stringify(datapointMatches.map((dp) => dp.matchedId).join(', '))}`);
                                    for (const datapointMatch of datapointMatches) {
                                        if (datapointMatch.matchedId) {
                                            datapointMatch.executedClassificationPrompt = true;
                                            const matchingDatapointRequest = await this.datapointRequestRepository
                                                .createQueryBuilder('datapointRequest')
                                                .innerJoinAndSelect('datapointRequest.esrsDatapoint', 'esrsDatapoint')
                                                .innerJoinAndSelect('datapointRequest.dataRequest', 'dataRequest')
                                                .where('esrsDatapoint.datapointId = :datapointId', {
                                                datapointId: datapointMatch.matchedId,
                                            })
                                                .andWhere('dataRequest.projectId = :projectId', {
                                                projectId: projectId,
                                            })
                                                .getOne();
                                            if (!matchingDatapointRequest) {
                                                this.logger.error(`${documentId}: Chunk: ${chunk.id} Datapoint: ${datapointMatch.matchedId} not found in the workspace`);
                                                return;
                                            }
                                            else {
                                                try {
                                                    const datapointDocumentChunk = await this.datapointDocumentChunkRepository.save({
                                                        documentChunkId: chunk.id,
                                                        datapointRequestId: matchingDatapointRequest.id,
                                                        createdBy: globalUser ? globalUser.id : userId,
                                                        createdAt: createdAtBatch,
                                                    });
                                                    response.totalLinksCreated++;
                                                    this.logger.log(`${documentId}: Chunk: ${chunk.id} DPR: ${matchingDatapointRequest.id} DPMatched: ${datapointMatch.matchedId}, Link-ID: ${datapointDocumentChunk.id}`);
                                                }
                                                catch (error) {
                                                    if (error instanceof typeorm_2.QueryFailedError &&
                                                        error.code === '23505') {
                                                        return null;
                                                    }
                                                    this.logger.error(error);
                                                }
                                                if (datapointMatch.mdrTitle) {
                                                    const cleanPolicy = datapointMatch.mdrTitle
                                                        .replace(/<\/?h6>/g, '')
                                                        .trim();
                                                    if (!matchingDatapointRequest.customUserRemark.includes(cleanPolicy)) {
                                                        if (!matchingDatapointRequest.customUserRemark ||
                                                            matchingDatapointRequest.customUserRemark.length <
                                                                12) {
                                                            matchingDatapointRequest.customUserRemark =
                                                                'Write among others about the following Policies/Target(s)/Action(s):' +
                                                                    `\n- ${cleanPolicy}`;
                                                        }
                                                        else {
                                                            matchingDatapointRequest.customUserRemark += `\n- ${cleanPolicy}`;
                                                        }
                                                        await this.datapointRequestRepository.save(matchingDatapointRequest);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        chunk.matchingsJson = JSON.stringify(chunkMatches);
                        this.documentChunkRepository.save(chunk);
                        response.chunks.push(chunkMatches);
                    }
                });
            });
            await Promise.all(promises);
            this.logger.log(`TOTAL TOKEN USAGE FOR EXTRACTION ${documentId}: inputTokensUsed ${response.inputTokensUsed}, outputTokensUsed: ${response.outputTokensUsed}, costFordocument: ${response.costForDocument}`);
            const endTime = Date.now();
            response.timeToGenerate = `${(endTime - startTime) / 1000}s`;
            document.status = document_entity_1.DocumentStatus.LinkingDataFinished;
            this.documentRepository.save(document);
            this.logger.log(`DatapointLinking Finihsed ${documentId}: ${document.status}`);
        }
        catch (e) {
            const documentStillExists = await this.documentRepository.findOne({
                where: { id: documentId },
            });
            if (!documentStillExists) {
                this.logger.log(`Document ${documentId} was deleted during linking process. Ending processing.`);
                return;
            }
            this.logger.error(e);
            document.status = document_entity_1.DocumentStatus.ErrorProcessing;
            this.documentRepository.save(document);
        }
    }
};
exports.DatapointDocumentChunkService = DatapointDocumentChunkService;
exports.DatapointDocumentChunkService = DatapointDocumentChunkService = DatapointDocumentChunkService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectDataSource)()),
    __param(3, (0, typeorm_1.InjectRepository)(document_chunk_entity_1.DocumentChunk)),
    __param(4, (0, typeorm_1.InjectRepository)(document_entity_1.Document)),
    __param(5, (0, typeorm_1.InjectRepository)(datapoint_request_entity_1.DatapointRequest)),
    __param(6, (0, typeorm_1.InjectRepository)(datapoint_document_chunk_entity_1.DatapointDocumentChunk)),
    __param(7, (0, typeorm_1.InjectRepository)(esrs_disclosure_requirement_entity_1.ESRSDisclosureRequirement)),
    __metadata("design:paramtypes", [llm_rate_limiter_service_1.LlmRateLimiterService,
        typeorm_2.DataSource,
        prompts_service_1.PromptService,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        users_service_1.UsersService])
], DatapointDocumentChunkService);
//# sourceMappingURL=datapoint-document-chunk.service.js.map