// https://claude.ai/chat/7833589b-d685-429d-8c68-cb9a898f1d24

{
    "name": "sustainability_assessment_schema",
    "description": "Schema for sustainability documentation assessment with formal criteria, current scoring and gap analysis",
    "schema": {
      "type": "object",
      "properties": {
        "formal_criteria_assessment": {
          "type": "object",
          "description": "Assessment of formal document criteria across different documents",
          "properties": {
            "company_name_present": {
              "type": "object",
              "description": "Whether company name is present in each document",
              "additionalProperties": {
                "type": "boolean",
                "description": "True if company name is present in the document"
              }
            },
            "visible_date": {
              "type": "object",
              "description": "Whether date is visible in each document",
              "additionalProperties": {
                "type": "boolean",
                "description": "True if date is visible in the document"
              }
            }
          },
          "required": ["company_name_present", "visible_date"],
          "additionalProperties": false
        },
        "current_score_assessment": {
          "type": "object",
          "description": "Current sustainability score assessment and breakdown",
          "properties": {
            "score": {
              "type": "number",
              "description": "Numerical score of the assessment"
            },
            "level": {
              "type": "string",
              "description": "Assessment level corresponding to the score: Insufficient, Partial, Good, Advanced, Outstanding"
            },
            "description": {
              "type": "string",
              "description": "Description of the score level"
            },
            "conclusion": {
              "type": "string",
              "description": "Conclusion on why the score is at this level"
            },
            "scoring_breakdown": {
              "type": "string",
              "description": "Detailed breakdown of scoring by criteria",
              "additionalProperties": {
                "type": "string",
                "description": "Table Markdown formatted criterion assessment with | Framework Criteria | Status ✔️ / ❌ | Explanation |"
              }
            }
          },
          "required": ["score", "level", "scoring_breakdown"],
          "additionalProperties": false
        },
        "gap_analysis": {
          "type": "array",
          "description": "Analysis of gaps identified in the sustainability documentation",
          "items": {
            "type": "object",
            "properties": {
              "gap_title": {
                "type": "string",
                "description": "Title of the identified gap"
              },
              "description": {
                "type": "string",
                "description": "Markdown formatted description of the gap"
              },
              "affected_documents": {
                "type": "array",
                "description": "List of document IDs affected by this gap",
                "items": {
                  "type": "string",
                  "description": "Valid document ID"
                }
              },
              "recommended_actions": {
                "type": "array",
                "description": "List of recommended actions to address the gap",
                "items": {
                  "type": "string",
                  "description": "Markdown formatted recommendation"
                }
              },
              "sample_text": {
                "type": "string",
                "description": "Markdown formatted sample text demonstrating the gap or solution"
              }
            },
            "required": ["gap_title", "description", "affected_documents", "recommended_actions", "sample_text"],
            "additionalProperties": false
          }
        }
      },
      "required": ["formal_criteria_assessment", "current_score_assessment", "gap_analysis"],
      "additionalProperties": false
    }
  }