# Grafana Alerts Documentation

This document outlines the Grafana alerting configuration for our containerized application environment. The alerts are designed to notify our team via Microsoft Teams when system resources exceed thresholds or when error rates increase significantly.

## Table of Contents

- [Alert Configuration Overview](#alert-configuration-overview)
- [Microsoft Teams Integration](#microsoft-teams-integration)
- [Resource Utilization Alerts](#resource-utilization-alerts)
  - [CPU Usage Alerts](#cpu-usage-alerts)
  - [Memory Usage Alerts](#memory-usage-alerts)
- [Database-Specific Alerts](#database-specific-alerts)
- [Application Alerts](#application-alerts)
- [Cache Alerts](#cache-alerts)
- [Alert Grouping and Throttling](#alert-grouping-and-throttling)
- [Alert Testing and Maintenance](#alert-testing-and-maintenance)

## Alert Configuration Overview

Our alerting strategy focuses on:

1. Critical infrastructure components (database, backend, redis)
2. Resource utilization metrics (CPU, memory, disk)
3. Application health indicators (errors, connection counts)
4. Preventing alert fatigue with proper grouping and throttling

## Microsoft Teams Integration

Alerts are delivered to our Microsoft Teams channel via webhook integration:

1. **Configuration Steps:**

   - Navigate to Grafana > Alerting > Notification channels
   - Add a new MS Teams channel
   - Configure with webhook URL from Teams
   - Enable "Include image" for visualizations with alerts

2. **Message Format:**
   - All alert messages include the specific metric value
   - Context about potential causes is provided
   - Where applicable, suggested remediation steps are included

## Resource Utilization Alerts

### CPU Usage Alerts

**General Container CPU Alert:**

- **Query:** `sum(rate(container_cpu_usage_seconds_total{name=~"$container"}[1m])) * 100 > 90`
- **Evaluation:** Every 1m, firing after condition persists for 5m
- **Message:**
  ```
  Warning: High CPU usage detected for container {{ $labels.name }}
  Current usage: {{ $value | printf "%.2f" }}% of capacity
  Timestamp: {{ $labels.instance }} - {{ $labels.job }}
  ```

**Individual Container CPU Alert (Container-specific):**

- **Query:** `sum by(name) (rate(container_cpu_usage_seconds_total[1m]) * 100) > 90`
- This creates separate alerts for each container that crosses the threshold

### Memory Usage Alerts

**General Container Memory Alert:**

- **Query:** `container_memory_usage_bytes{name=~"$container"} / container_memory_limit_bytes{name=~"$container"} * 100 > 90`
- **Evaluation:** Every 1m, firing after condition persists for 5m
- **Message:**
  ```
  Warning: High memory usage detected for container {{ $labels.name }}
  Current usage: {{ $value | printf "%.2f" }}% of limit
  ```

**Individual Container Memory Alert:**

- **Query:** `sum by(name) (container_memory_usage_bytes / container_memory_limit_bytes * 100) > 90`

## Database-Specific Alerts

**Database CPU Usage Alert:**

- **Query:** `sum(rate(container_cpu_usage_seconds_total{name="climate-assistant-backend-db-1"}[1m])) * 100 > 80`
- **Message:**
  ```
  Warning: High CPU usage detected for PostgreSQL database
  Current usage: {{ $value | printf "%.2f" }}% of capacity
  This may impact query performance and response times.
  ```

**Database Memory Usage Alert:**

- **Query:** `container_memory_usage_bytes{name="climate-assistant-backend-db-1"} / container_memory_limit_bytes{name="climate-assistant-backend-db-1"} * 100 > 85`
- **Message:**
  ```
  Warning: High memory usage detected for PostgreSQL database
  Current usage: {{ $value | printf "%.2f" }}% of limit
  Check for large queries, connections, or potential memory leaks.
  ```

**Database Disk Usage Alert:**

- **Query:** `(1 - (node_filesystem_avail_bytes{mountpoint="/var/lib/postgresql/data"} / node_filesystem_size_bytes{mountpoint="/var/lib/postgresql/data"})) * 100 > 85`
- **Message:**
  ```
  Warning: Database disk usage is high
  Current usage: {{ $value | printf "%.2f" }}% of available space
  Consider cleaning up old data or expanding storage.
  ```

**Database Connection Count Alert:**

- **Query:** `pg_stat_activity_count`
- **Message:**
  ```
  Warning: High number of database connections
  Current connections: {{ $value }}
  Check for connection leaks or increase max_connections if needed.
  ```

## Application Alerts

**Backend Service Health Alert:**

- **Query:** `count(container_last_seen{name="climate-assistant-backend"}) < 1`
- **Message:**
  ```
  CRITICAL: Backend service is down
  Container appears to be unavailable. Check container logs and status.
  ```

**Error Rate Alert:**

- **Query:** `sum(count_over_time({container=~".*backend.*"} |~ "(?i)error|exception|fail"[5m])) > 50`
- **Message:**
  ```
  Warning: High error rate detected in backend service
  {{ $value }} errors in the last 5 minutes
  Check application logs for details.
  ```

## Cache Alerts

**Redis Memory Usage Alert:**

- **Query:** `container_memory_usage_bytes{name="redis"} / container_memory_limit_bytes{name="redis"} * 100 > 80`
- **Message:**
  ```
  Warning: Redis cache memory usage is high
  Current usage: {{ $value | printf "%.2f" }}% of limit
  Consider checking for memory leaks or increasing limit.
  ```

**Redis Connection Count Alert:**

- **Query:** `redis_connected_clients > 100`
- **Message:**
  ```
  Warning: High number of Redis connections
  Current connections: {{ $value }}
  Check for connection leaks in applications.
  ```

## Alert Grouping and Throttling

To prevent alert fatigue and ensure our Teams channel doesn't get flooded with notifications:

1. **Alert Grouping:**

   - Alerts are grouped by service (Database, Backend, Redis)
   - Group wait: 30s (buffer time before sending initial notification)
   - Group interval: 5m (minimum time between sending updates for a group)
   - Repeat interval: 1h (minimum time before sending a notification again for the same alert)

2. **Alert Persistence:**

   - "For" duration is set to 3-5m for all alerts
   - This ensures brief spikes don't trigger alerts
   - Only persistent issues generate notifications

3. **Resolution Notifications:**
   - "Send resolved" option is enabled
   - This ensures we get notification when an issue is fixed

## Alert Testing and Maintenance

1. **Testing Procedure:**

   - Temporarily lower thresholds to confirm alerts fire correctly
   - Verify Teams channel receives notifications
   - Ensure resolution messages are received when conditions return to normal

2. **Maintenance Schedule:**

   - Review alert thresholds quarterly
   - Adjust based on historical data and application growth
   - Update contact points if team structure changes

3. **Documentation Updates:**
   - This document should be updated whenever alert configurations change
   - Include date and reason for changes
