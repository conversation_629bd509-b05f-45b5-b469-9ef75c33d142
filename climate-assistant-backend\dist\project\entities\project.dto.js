"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectData = exports.DataRequestData = exports.UpdateProjectRequest = exports.CreateProjectRequest = void 0;
const project_entity_1 = require("./project.entity");
const data_request_entity_1 = require("../../data-request/entities/data-request.entity");
class CreateProjectRequest {
}
exports.CreateProjectRequest = CreateProjectRequest;
class UpdateProjectRequest {
}
exports.UpdateProjectRequest = UpdateProjectRequest;
class DataRequestData extends data_request_entity_1.DataRequest {
}
exports.DataRequestData = DataRequestData;
class ProjectData extends project_entity_1.Project {
}
exports.ProjectData = ProjectData;
//# sourceMappingURL=project.dto.js.map