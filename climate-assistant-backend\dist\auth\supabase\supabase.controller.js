"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const supabase_auth_guard_1 = require("./supabase.auth.guard");
const supabase_service_1 = require("./supabase.service");
const helpers_1 = require("../helpers");
const questionnaire_service_1 = require("./questionnaire.service");
const upload_utils_1 = require("../../util/upload-utils");
const answer_linking_service_1 = require("./answer-linking.service");
let SupabaseController = class SupabaseController {
    constructor(supabaseService, questionnaireService, ecoVadisAnswerAgentService) {
        this.supabaseService = supabaseService;
        this.questionnaireService = questionnaireService;
        this.ecoVadisAnswerAgentService = ecoVadisAnswerAgentService;
    }
    async getProfile(req) {
        const profile = await this.supabaseService.findById(req.user.id);
        return profile;
    }
    async initiateUserMigration(req) {
        await this.supabaseService.migrateUsers();
        return {
            message: 'User migration initiated successfully',
        };
    }
    async createUser(createUserDto) {
        const { email, name, password, options: { workspaceId, workspaceName, role }, } = createUserDto;
        const create = await this.supabaseService.createUser({
            email,
            name,
            password,
            options: {
                workspaceId,
                workspaceName,
                role,
            },
        });
        return {
            create,
        };
    }
    async uploadQuestionnaire(req, projectId, file) {
        const { path } = file;
        const result = await this.questionnaireService.processExcelQuestionnaire(path, projectId);
        return {
            message: 'Excel file processed successfully',
            result,
        };
    }
    async answerEcoVadisQuestion(req, requestDto) {
        const { questionId, projectId, useGemini } = requestDto;
        const result = await this.ecoVadisAnswerAgentService.answerEcoVadisQuestion({ questionId, projectId, useGemini });
        return {
            message: 'EcoVadis question answered successfully',
            result,
        };
    }
};
exports.SupabaseController = SupabaseController;
__decorate([
    (0, common_1.UseGuards)(supabase_auth_guard_1.AuthGuard),
    (0, common_1.Get)('profile'),
    (0, swagger_1.ApiOperation)({ summary: 'Get user profile' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'User profile retrieved successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SupabaseController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Get)('migrate'),
    (0, common_1.SetMetadata)(helpers_1.IS_PUBLIC_KEY, true),
    (0, swagger_1.ApiOperation)({ summary: 'Migrate auth users to supabase' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'User migration completed successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SupabaseController.prototype, "initiateUserMigration", null);
__decorate([
    (0, common_1.Post)('create-user'),
    (0, common_1.SetMetadata)(helpers_1.IS_PUBLIC_KEY, true),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new user' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'User created successfully',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SupabaseController.prototype, "createUser", null);
__decorate([
    (0, common_1.Post)('upload-questionnaire/:projectId'),
    (0, common_1.SetMetadata)(helpers_1.IS_PUBLIC_KEY, true),
    (0, common_1.UseInterceptors)(upload_utils_1.fileInterceptor),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiOperation)({ summary: 'Upload Excel questionnaire file' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Excel file processed successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('projectId')),
    __param(2, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], SupabaseController.prototype, "uploadQuestionnaire", null);
__decorate([
    (0, common_1.Post)('ecovadis/answer'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.AuthGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Answer EcoVadis question using AI agent' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'EcoVadis question answered successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SupabaseController.prototype, "answerEcoVadisQuestion", null);
exports.SupabaseController = SupabaseController = __decorate([
    (0, swagger_1.ApiTags)('auth'),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        questionnaire_service_1.QuestionnaireService,
        answer_linking_service_1.EnhancedEcoVadisAnswerAgentService])
], SupabaseController);
//# sourceMappingURL=supabase.controller.js.map