
import { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Check, ChevronDown, X } from "lucide-react";
import { Chip } from "@/components/ui/chip";
import { cn } from "@/lib/utils";
import { QuestionStatus, QuestionScore } from "@/types/ecovadis";
import { StatusChip } from "@/components/ui/status-chip";

interface QuestionFiltersProps {
  onSearchChange: (value: string) => void;
  onStatusChange: (values: string[]) => void;
  onScoreChange: (values: string[]) => void;
  onGapsChange: (values: string[]) => void;
}

export const QuestionFilters = ({ 
  onSearchChange, 
  onStatusChange,
  onScoreChange,
  onGapsChange
}: QuestionFiltersProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [selectedScores, setSelectedScores] = useState<string[]>([]);
  const [selectedGaps, setSelectedGaps] = useState<string[]>([]);
  
  const statuses: QuestionStatus[] = ['complete', 'Incomplete', 'Not applicable'];
  const scores: QuestionScore[] = [0, 25, 50, 75, 100];
  const gapsOptions = ['0', '1-2', '3+'];

  useEffect(() => {
    onSearchChange(searchQuery);
  }, [searchQuery, onSearchChange]);

  useEffect(() => {
    onStatusChange(selectedStatuses);
  }, [selectedStatuses, onStatusChange]);

  useEffect(() => {
    onScoreChange(selectedScores);
  }, [selectedScores, onScoreChange]);

  useEffect(() => {
    onGapsChange(selectedGaps);
  }, [selectedGaps, onGapsChange]);

  const toggleStatus = (status: string) => {
    setSelectedStatuses(prev => 
      prev.includes(status) 
        ? prev.filter(s => s !== status) 
        : [...prev, status]
    );
  };

  const toggleScore = (score: string) => {
    setSelectedScores(prev => 
      prev.includes(score) 
        ? prev.filter(s => s !== score) 
        : [...prev, score]
    );
  };

  const toggleGap = (gap: string) => {
    setSelectedGaps(prev => 
      prev.includes(gap) 
        ? prev.filter(g => g !== gap) 
        : [...prev, gap]
    );
  };

  const clearFilters = () => {
    setSelectedStatuses([]);
    setSelectedScores([]);
    setSelectedGaps([]);
  };

  return (
    <div className="flex flex-wrap items-center gap-2 w-full">
      <div className="relative w-full md:w-64">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Search questions..."
          className="pl-10"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>
      
      {/* Status Filter */}
      <Popover>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            size="sm" 
            className={cn(
              "border-dashed",
              selectedStatuses.length > 0 && "border-primary"
            )}
          >
            Status
            <ChevronDown className="ml-2 h-4 w-4" />
            {selectedStatuses.length > 0 && (
              <Chip variant="primary" className="ml-2">
                {selectedStatuses.length}
              </Chip>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-56 p-0" align="start">
          <div className="p-2">
            {statuses.map(status => (
              <div
                key={status.toString()}
                className="flex items-center hover:bg-muted/50 rounded-md p-2 cursor-pointer"
                onClick={() => toggleStatus(status.toString())}
              >
                <div className={cn(
                  "w-4 h-4 border rounded-sm mr-2 flex items-center justify-center",
                  selectedStatuses.includes(status.toString()) ? "bg-primary border-primary" : "border-input"
                )}>
                  {selectedStatuses.includes(status.toString()) && (
                    <Check className="h-3 w-3 text-primary-foreground" />
                  )}
                </div>
                <StatusChip status={status} />
              </div>
            ))}
          </div>
        </PopoverContent>
      </Popover>

      {/* Score Filter */}
      <Popover>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            size="sm" 
            className={cn(
              "border-dashed",
              selectedScores.length > 0 && "border-primary"
            )}
          >
            Estimated Score
            <ChevronDown className="ml-2 h-4 w-4" />
            {selectedScores.length > 0 && (
              <Chip variant="primary" className="ml-2">
                {selectedScores.length}
              </Chip>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-56 p-0" align="start">
          <div className="p-2">
            {scores.map(score => (
              <div
                key={score}
                className="flex items-center hover:bg-muted/50 rounded-md p-2 cursor-pointer"
                onClick={() => toggleScore(score.toString())}
              >
                <div className={cn(
                  "w-4 h-4 border rounded-sm mr-2 flex items-center justify-center",
                  selectedScores.includes(score.toString()) ? "bg-primary border-primary" : "border-input"
                )}>
                  {selectedScores.includes(score.toString()) && (
                    <Check className="h-3 w-3 text-primary-foreground" />
                  )}
                </div>
                <span>Score: {score}</span>
              </div>
            ))}
          </div>
        </PopoverContent>
      </Popover>

      {/* Gaps Filter */}
      <Popover>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            size="sm" 
            className={cn(
              "border-dashed",
              selectedGaps.length > 0 && "border-primary"
            )}
          >
            GAPS
            <ChevronDown className="ml-2 h-4 w-4" />
            {selectedGaps.length > 0 && (
              <Chip variant="primary" className="ml-2">
                {selectedGaps.length}
              </Chip>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-56 p-0" align="start">
          <div className="p-2">
            {gapsOptions.map(gap => (
              <div
                key={gap}
                className="flex items-center hover:bg-muted/50 rounded-md p-2 cursor-pointer"
                onClick={() => toggleGap(gap)}
              >
                <div className={cn(
                  "w-4 h-4 border rounded-sm mr-2 flex items-center justify-center",
                  selectedGaps.includes(gap) ? "bg-primary border-primary" : "border-input"
                )}>
                  {selectedGaps.includes(gap) && (
                    <Check className="h-3 w-3 text-primary-foreground" />
                  )}
                </div>
                <span>{gap} {gap === '1' ? 'GAP' : 'GAPS'}</span>
              </div>
            ))}
          </div>
        </PopoverContent>
      </Popover>
      
      {/* Clear filters button - only show if filters are applied */}
      {(selectedStatuses.length > 0 || selectedScores.length > 0 || selectedGaps.length > 0) && (
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={clearFilters}
          className="text-muted-foreground"
        >
          Clear filters
          <X className="ml-2 h-4 w-4" />
        </Button>
      )}
    </div>
  );
};
