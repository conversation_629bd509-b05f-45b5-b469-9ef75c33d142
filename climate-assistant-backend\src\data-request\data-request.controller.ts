import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Req,
  Request,
  Res,
  Sse,
  SetMetadata,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DataRequestService } from './data-request.service';
import {
  GenerateDataRequestReportTextTextPayload,
  UpdateDataRequestPayload,
} from './entities/data-request.dto';
import { UseGuards } from '@nestjs/common';
import { DataRequestGuard } from './data-request.guard';
import { DataRequestStatus } from './entities/data-request.entity';
import { Comment } from 'src/project/entities/comment.entity';
import { Roles } from 'src/auth/roles.decorator';
import { Role } from 'src/users/entities/user-workspace.entity';
import { ProjectGuard } from 'src/project/project.guard';
import { Response } from 'express';
import { Observable, filter, switchMap } from 'rxjs';
import { DatapointGenerationEvent } from './constants';
import { createSseStream } from 'src/util/sse.util';
import { AuthGuard } from 'src/auth/supabase/supabase.auth.guard';
import { dataRequestGenerationStatus } from './entities/datarequest-generation.entity';

@ApiTags('Data Request')
@UseGuards(AuthGuard)
@Controller('data-request')
export class DataRequestController {
  constructor(private readonly dataRequestService: DataRequestService) {}

  @UseGuards(ProjectGuard)
  @Get('list/:projectId')
  @ApiOperation({ summary: 'List all data requests for a project' })
  @ApiResponse({
    status: 200,
    description: 'Data requests retrieved successfully',
  })
  async listAllDataRequests(@Param('projectId') projectId: string) {
    await this.dataRequestService.findAll(projectId);
  }

  @UseGuards(DataRequestGuard)
  @Get('/:dataRequestId')
  @ApiOperation({ summary: 'Get a specific data request by ID' })
  @ApiResponse({
    status: 200,
    description: 'Data request retrieved successfully',
  })
  async getDataRequest(
    @Req() req,
    @Param('dataRequestId') dataRequestId: string
  ) {
    return await this.dataRequestService.findRelatedData(
      dataRequestId,
      req.user.id
    );
  }

  @UseGuards(DataRequestGuard)
  @Put('/:dataRequestId')
  @ApiOperation({ summary: 'Update a specific data request by ID' })
  @ApiResponse({
    status: 200,
    description: 'Data request updated successfully',
  })
  async updateDataRequest(
    @Request() req,
    @Param('dataRequestId') dataRequestId: string,
    @Body() updateDataRequestPayload: UpdateDataRequestPayload
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const project = await this.dataRequestService.update({
      dataRequestId,
      updateDataRequestPayload,
      userId,
      workspaceId,
    });
    return project;
  }

  @UseGuards(DataRequestGuard)
  @Put('/:dataRequestId/approve')
  @ApiOperation({ summary: 'Approve a specific data request by ID' })
  @ApiResponse({
    status: 200,
    description: 'Data request approved successfully',
  })
  async approveDataRequest(
    @Request() req,
    @Param('dataRequestId') dataRequestId: string
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const updateDataRequestPayload: UpdateDataRequestPayload = {
      approvedBy: userId,
      approvedAt: new Date(),
      status: DataRequestStatus.ApprovedAnswer,
    };

    const project = await this.dataRequestService.update({
      dataRequestId,
      updateDataRequestPayload,
      userId,
      workspaceId,
      event: 'data_request_approved',
    });
    return project;
  }

  @UseGuards(DataRequestGuard)
  @Post('/:dataRequestId/review-with-ai')
  @Roles(
    Role.SuperAdmin,
    Role.WorkspaceAdmin,
    Role.AiContributor,
    Role.AiReviewer,
    Role.Contributor
  )
  @ApiOperation({ summary: 'Review data request content with AI' })
  @ApiResponse({
    status: 200,
    description: 'Data request content reviewed successfully',
  })
  async reviewContentWithAi(
    @Param('dataRequestId') dataRequestId: string,
    @Req() req
  ): Promise<Comment[]> {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    return await this.dataRequestService.reviewDataRequestContentWithAI({
      dataRequestId,
      userId,
      workspaceId,
    });
  }

  @UseGuards(DataRequestGuard)
  @SetMetadata('customCheck', 'generateWithAI')
  @Post('/:dataRequestId/generate-with-ai')
  @Roles(
    Role.SuperAdmin,
    Role.WorkspaceAdmin,
    Role.AiContributor,
    Role.Contributor
  )
  @ApiOperation({ summary: 'Generate data request content with AI' })
  @ApiResponse({
    status: 200,
    description: 'Data request content generated successfully',
  })
  async generateContentWithAi(
    @Param('dataRequestId') dataRequestId: string,
    @Body() data: GenerateDataRequestReportTextTextPayload,
    @Req() req
  ): Promise<{ content: string; id: string }> {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;

    return await this.dataRequestService.generateDataRequestTextContentWithAI({
      dataRequestId,
      userId,
      workspaceId,
      additionalData: data,
    });
  }

  @UseGuards(DataRequestGuard)
  @Put('/:dataRequestId/generate-bulk-datapoint')
  @Roles(Role.SuperAdmin, Role.WorkspaceAdmin, Role.AiContributor)
  @ApiOperation({ summary: 'Generate datapoint for data request' })
  @ApiResponse({
    status: 200,
    description: 'Datapoint generated successfully',
  })
  async generateBulkDatapointForDataRequest(
    @Param('dataRequestId') dataRequestId: string,
    @Req() req
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    return await this.dataRequestService.generateAllDatapointForDataRequest({
      dataRequestId,
      userId,
      workspaceId,
    });
  }

  @Get('/:dataRequestId/generations')
  @Roles(Role.SuperAdmin)
  @ApiOperation({ summary: 'Get all generations for a data request' })
  @ApiResponse({
    status: 200,
    description: 'Generations retrieved successfully',
  })
  async getGenerations(@Param('dataRequestId') dataRequestId: string) {
    return await this.dataRequestService.getGenerations(dataRequestId);
  }

  @UseGuards(DataRequestGuard)
  @Put('/:dataRequestId/review-bulk-datapoint')
  @Roles(Role.SuperAdmin, Role.WorkspaceAdmin, Role.AiContributor)
  @ApiOperation({ summary: 'Review datapoint for data request' })
  @ApiResponse({
    status: 200,
    description: 'Datapoint reviewed successfully',
  })
  async reviewBulkDatapointForDataRequest(
    @Param('dataRequestId') dataRequestId: string,
    @Req() req
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    return await this.dataRequestService.reviewAllDatapointForDataRequest({
      dataRequestId,
      userId,
      workspaceId,
    });
  }

  @Sse('/events/datapoint/:dataRequestId')
  subscribeToDataRequestEvents(
    @Param('dataRequestId') dataRequestId: string,
    @Res() response: Response
  ): Observable<MessageEvent> {
    const eventStream = this.dataRequestService.events$.pipe(
      filter(
        (event: DatapointGenerationEvent) =>
          event.dataRequestId === dataRequestId
      ),
      switchMap(async (event) => {
        if (event.status === 'failed') {
          //This should be passed as a callback on the queue for failure
          await this.dataRequestService.setDatapointQueueStatusToNull(
            event.datapointRequestId
          );
        }
        const datapoint = await this.dataRequestService.findDatapointById(
          event.datapointRequestId
        );

        event.datapointRequest = datapoint;
        return {
          data: JSON.stringify(event),
          type: 'message',
        } as MessageEvent;
      })
    );
    return createSseStream(response, eventStream, {
      data: { connected: true },
      type: 'connection',
    } as MessageEvent);
  }

  @Roles(Role.SuperAdmin)
  @Put('/generation-status/:dataRequestGenerationId')
  @ApiOperation({
    summary: 'Update generation status by generation ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Generation status updated successfully',
  })
  async updateDatapointGenerationStatus(
    @Param('dataRequestGenerationId') dataRequestGenerationId: string,
    @Req() req,
    @Body()
    payload: {
      status: dataRequestGenerationStatus;
    }
  ): Promise<{ content?: string; status: dataRequestGenerationStatus }> {
    const generation = await this.dataRequestService.updateGenerationStatus({
      dataRequestGenerationId,
      status: payload.status,
      userId: req.user.id,
      workspaceId: req.user.workspaceId,
    });
    if (generation) {
      await this.dataRequestService.update({
        dataRequestId: generation.dataRequest.id,
        updateDataRequestPayload: {
          content: generation.data.content,
        },
        userId: req.user.id,
        workspaceId: req.user.workspaceId,
      });
    }
    return {
      status: payload.status,
      content: generation?.data?.content || null,
    };
  }
}
