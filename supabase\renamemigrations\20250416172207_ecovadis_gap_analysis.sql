-- First, alter the table to change gaps to JSON data type
ALTER TABLE project_ecovadis_gaps
  ALTER COLUMN gaps TYPE json USING gaps::json;

-- Add assignee column with foreign key to user table
ALTER TABLE project_ecovadis_gaps
  ADD COLUMN "assigneeId" uuid,
  ADD CONSTRAINT "FK_project_ecovadis_gaps_assignee"
  FOREIGN KEY ("assigneeId") REFERENCES "user"("id") ON DELETE SET NULL;

-- Add deadline column
ALTER TABLE project_ecovadis_gaps
  ADD COLUMN "deadline" timestamp;

-- Create an index for the assignee for better performance
CREATE INDEX idx_project_ecovadis_gaps_assignee ON project_ecovadis_gaps("assigneeId");

-- Update the status column to use an enum type
CREATE TYPE question_status_enum AS ENUM ('pending', 'complete');

ALTER TABLE "project_ecovadis_question"
ALTER COLUMN "status" TYPE question_status_enum USING "status"::question_status_enum,
ALTER COLUMN "status" SET DEFAULT 'pending'::question_status_enum,
ALTER COLUMN "status" SET NOT NULL;