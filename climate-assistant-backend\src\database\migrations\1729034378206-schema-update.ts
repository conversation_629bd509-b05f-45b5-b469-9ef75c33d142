import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1729034378206 implements MigrationInterface {
  name = 'SchemaUpdate1729034378206';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "document_chunk" ADD "matchingsJson" text NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ADD "paragraph" character varying NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ADD "relatedAR" character varying NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ADD "lawText" text NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ADD "lawTextAR" text NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" ADD "updatedAt" TIMESTAMP NOT NULL DEFAULT now()`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" DROP COLUMN "updatedAt"`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" DROP COLUMN "createdAt"`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" DROP COLUMN "lawTextAR"`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" DROP COLUMN "lawText"`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" DROP COLUMN "relatedAR"`,
    );
    await queryRunner.query(
      `ALTER TABLE "esrs_datapoint" DROP COLUMN "paragraph"`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_chunk" DROP COLUMN "matchingsJson"`,
    );
  }
}
