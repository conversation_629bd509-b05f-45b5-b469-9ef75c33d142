import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1736861239908 implements MigrationInterface {
  name = 'SchemaUpdate1736861239908';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `WITH cte AS (
        SELECT
            id,
            ROW_NUMBER() OVER (
                PARTITION BY "documentChunkId", "datapointRequestId"
                ORDER BY id
            ) AS rn
        FROM "datapoint_document_chunk"
      )
      DELETE FROM "datapoint_document_chunk" t
      USING cte
      WHERE t.id = cte.id
      AND cte.rn > 1;`,
    );
    await queryRunner.query(
      `WITH Deduped AS (
            SELECT
                id,
                "documentId",
                content,
                ROW_NUMBER() OVER (PARTITION BY "documentId", content ORDER BY id) AS row_num
            FROM document_chunk
        )
        DELETE FROM document_chunk
        WHERE id IN (
            SELECT id
            FROM Deduped
            WHERE row_num > 1
        );`,
    );
    await queryRunner.query(
      `ALTER TABLE "datapoint_document_chunk" ADD CONSTRAINT "UQ_f35efcf411d58fd6f5a089621f3" UNIQUE ("documentChunkId", "datapointRequestId")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_document_chunk" DROP CONSTRAINT "UQ_f35efcf411d58fd6f5a089621f3"`,
    );
  }
}
