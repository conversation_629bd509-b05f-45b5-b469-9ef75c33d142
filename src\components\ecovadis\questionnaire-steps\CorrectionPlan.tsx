
import React, { useState } from 'react';
import { Check } from 'lucide-react';
import { FileUpload } from '@/components/ui/file-upload';

interface CorrectionPlanProps {
  onComplete: () => void;
}

export const CorrectionPlan: React.FC<CorrectionPlanProps> = ({ onComplete }) => {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadComplete, setUploadComplete] = useState(false);
  
  const handleFile = (files: File[]) => {
    if (files.length > 0) {
      setFile(files[0]);
      simulateUpload();
    }
  };
  
  const simulateUpload = () => {
    setUploading(true);
    
    // Simulate upload process
    setTimeout(() => {
      setUploading(false);
      setUploadComplete(true);
      
      // After a short delay, mark this step as complete
      setTimeout(() => {
        onComplete();
      }, 1500);
    }, 2000);
  };
  
  return (
    <div className="flex flex-col items-center">
      <p className="text-lg text-gray-600 mb-16 text-center max-w-2xl">
        Upload any correction plans from previous Ecovadis assessments. These will be automatically added to your improvement list.
      </p>
      
      {!uploadComplete ? (
        <div className="w-full max-w-2xl mb-8">
          <FileUpload onChange={handleFile} />
        </div>
      ) : (
        <div className="bg-green-100 rounded-lg p-4 w-full max-w-2xl mb-8 flex items-center">
          <div className="bg-green-500 rounded-full p-1 mr-3">
            <Check className="h-5 w-5 text-white" />
          </div>
          <p className="text-green-800 font-medium">Correction plan uploaded successfully!</p>
        </div>
      )}
      
      {uploading && (
        <div className="w-full max-w-2xl">
          <div className="w-full h-2 bg-gray-200 rounded-full mb-2">
            <div className="h-full bg-glacier-mint rounded-full animate-progress w-3/4"></div>
          </div>
          <p className="text-sm text-gray-500">Processing correction plan...</p>
        </div>
      )}
    </div>
  );
};

