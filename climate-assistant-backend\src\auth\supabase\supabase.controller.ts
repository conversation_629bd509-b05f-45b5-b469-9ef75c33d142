import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Request,
  SetMetadata,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { AuthGuard } from './supabase.auth.guard';
import { SupabaseService } from './supabase.service';
import { IS_PUBLIC_KEY } from 'src/auth/helpers';
import { Role } from 'src/users/entities/user-workspace.entity';
import { QuestionnaireService } from './questionnaire.service';

import { fileInterceptor } from 'src/util/upload-utils';
import {
  EnhancedEcoVadisAnswerAgentService,
  EcoVadisResponse,
} from './answer-linking.service';

@ApiTags('auth')
@Controller('auth')
export class SupabaseController {
  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly questionnaireService: QuestionnaireService,
    private readonly ecoVadisAnswerAgentService: EnhancedEcoVadisAnswerAgentService
  ) {}

  @UseGuards(AuthGuard)
  @Get('profile')
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({
    status: 200,
    description: 'User profile retrieved successfully',
  })
  async getProfile(@Request() req) {
    const profile = await this.supabaseService.findById(req.user.id);
    return profile;
  }

  @Get('migrate')
  @SetMetadata(IS_PUBLIC_KEY, true)
  @ApiOperation({ summary: 'Migrate auth users to supabase' })
  @ApiResponse({
    status: 200,
    description: 'User migration completed successfully',
  })
  async initiateUserMigration(@Request() req) {
    await this.supabaseService.migrateUsers();
    return {
      message: 'User migration initiated successfully',
    };
  }

  @Post('create-user')
  @SetMetadata(IS_PUBLIC_KEY, true)
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({
    status: 200,
    description: 'User created successfully',
  })
  async createUser(
    @Body()
    createUserDto: {
      email: string;
      name: string;
      password: string;
      options: { workspaceId?: string; workspaceName?: string; role?: Role };
    }
  ) {
    const {
      email,
      name,
      password,
      options: { workspaceId, workspaceName, role },
    } = createUserDto;
    const create = await this.supabaseService.createUser({
      email,
      name,
      password,
      options: {
        workspaceId,
        workspaceName,
        role,
      },
    });
    return {
      create,
    };
  }

  @Post('upload-questionnaire/:projectId')
  @SetMetadata(IS_PUBLIC_KEY, true)
  @UseInterceptors(fileInterceptor)
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload Excel questionnaire file' })
  @ApiResponse({
    status: 200,
    description: 'Excel file processed successfully',
  })
  async uploadQuestionnaire(
    @Request() req,
    @Param('projectId') projectId: string,
    @UploadedFile() file: Express.Multer.File
  ) {
    const { path } = file;
    // Process the uploaded Excel file
    const result = await this.questionnaireService.processExcelQuestionnaire(
      path,
      projectId
    );

    return {
      message: 'Excel file processed successfully',
      result,
    };
  }

  @Post('ecovadis/answer')
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Answer EcoVadis question using AI agent' })
  @ApiResponse({
    status: 200,
    description: 'EcoVadis question answered successfully',
  })
  async answerEcoVadisQuestion(
    @Request() req,
    @Body()
    requestDto: {
      questionId: string;
      projectId: string;
      useGemini?: boolean;  // Optional for backwards compatibility
    }
  ): Promise<{
    message: string;
    result: EcoVadisResponse;
  }> {
    const { questionId, projectId } = requestDto;
    const result = await this.ecoVadisAnswerAgentService.answerEcoVadisQuestion(
      { questionId, projectId }
    );
    return {
      message: 'EcoVadis question answered successfully',
      result,
    };
  }
}
