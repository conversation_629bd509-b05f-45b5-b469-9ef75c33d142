import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1729863486683 implements MigrationInterface {
  name = 'SchemaUpdate1729863486683';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `delete from data_request using esrs_disclosure_requirement where data_request."dataRequestTypeId"=esrs_disclosure_requirement."id" and esrs_disclosure_requirement."id" > 95`,
    );
    await queryRunner.query(
      `delete from esrs_topic_disclosure_requirement using esrs_disclosure_requirement where esrs_topic_disclosure_requirement."esrsDisclosureRequirementId"=esrs_disclosure_requirement."id" and esrs_disclosure_requirement."id" > 95`,
    );

    await queryRunner.query(
      `delete from esrs_disclosure_requirement where "id" > 95;`,
    );

    await queryRunner.query(`delete from esrs_topic where "id" = 12;`);

    //Change the order so that ESRS 2 is listed first
    await queryRunner.query(
      `update esrs_disclosure_requirement set sort = sort + 12;`,
    );
    await queryRunner.query(
      `update esrs_disclosure_requirement set sort = sort-95 where esrs ='ESRS 2';`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
