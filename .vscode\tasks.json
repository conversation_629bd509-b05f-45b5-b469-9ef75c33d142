{"version": "2.0.0", "tasks": [{"label": "Start Backend", "type": "shell", "command": "npm run start:dev", "options": {"cwd": "${workspaceFolder}/climate-assistant-backend"}, "isBackground": true, "problemMatcher": {"owner": "custom", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^.*$", "file": 1, "message": 1}, "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "Nest application successfully started"}}, "presentation": {"reveal": "always", "panel": "dedicated", "group": "backend"}}, {"label": "Start Frontend", "type": "shell", "command": "npm run dev", "options": {"cwd": "${workspaceFolder}/climate-assistant-frontend"}, "isBackground": true, "problemMatcher": {"owner": "custom", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^.*$", "file": 1, "message": 1}, "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "ready in"}}, "presentation": {"reveal": "always", "panel": "dedicated", "group": "frontend"}}, {"label": "Start All", "dependsOn": ["Start Backend", "Start Frontend"], "dependsOrder": "parallel", "presentation": {"reveal": "always", "panel": "shared"}}]}