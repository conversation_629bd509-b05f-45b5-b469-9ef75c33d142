import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsArray, IsString, ValidateIf } from 'class-validator';

export class BulkReindexDto {
  @ApiProperty({
    description:
      'Array of document IDs to reindex. If not provided, workspaceId must be specified.',
    required: false,
    type: [String],
    example: ['doc-123', 'doc-456'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ValidateIf((o) => !o.workspaceId || o.documentIds)
  documentIds?: string[];

  @ApiProperty({
    description:
      'Workspace ID to reindex all documents within. If not provided, documentIds must be specified.',
    required: false,
    type: String,
    example: 'workspace-789',
  })
  @IsOptional()
  @IsString()
  @ValidateIf((o) => !o.documentIds || o.workspaceId)
  workspaceId?: string;
}

export interface BulkReindexResultDto {
  message: string;
  results: {
    success: string[];
    failed: { id: string; error: string }[];
  };
}
