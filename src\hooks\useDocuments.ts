import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';

import { fetchDocumentUploads } from '@/api/workspace-settings/workspace-settings.api';
import { updateDocumentInAnswer } from '@/api/ecovadis/ecovadis.api';
import { DocumentUpload } from '@/types/project';
import { AttachedDocument } from '@/types/ecovadis';

export function useDocuments() {
  const queryClient = useQueryClient();
  const { data: uploadedFiles, isLoading: loading } = useQuery<
    DocumentUpload[]
  >({
    queryKey: ['uploadedFiles'],
    queryFn: fetchDocumentUploads,
  });

  const updateDocumentMutation = useMutation({
    mutationFn: async ({ answerId, documentId, updates }: { 
      answerId: string; 
      documentId: string; 
      updates: { pages: string; comment?: string; } 
    }) => {
      return updateDocumentInAnswer(answerId, documentId, updates);
    },
    onSuccess: () => {
      // Invalidate relevant queries on success
      queryClient.invalidateQueries({ queryKey: ['ecovadis-project'] });
    },
  });

  function refetchDocuments() {
    queryClient.invalidateQueries({ queryKey: ['uploadedFiles'] });
  }

  return {
    uploadedFiles,
    loading,
    refetchDocuments,
    updateDocument: updateDocumentMutation.mutate,
    updateDocumentAsync: updateDocumentMutation.mutateAsync,
    isUpdating: updateDocumentMutation.isPending,
    updateError: updateDocumentMutation.error,
  };
}
