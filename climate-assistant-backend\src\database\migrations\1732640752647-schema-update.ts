import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1732640752647 implements MigrationInterface {
  name = 'SchemaUpdate1732640752647';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "material_esrs_topic" ALTER COLUMN "projectId" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "material_esrs_topic" ALTER COLUMN "esrsTopicId" SET NOT NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_50a1eb248445b84935b069fc0c" ON "material_esrs_topic" ("projectId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ed602f423d73ef38205a16a56f" ON "material_esrs_topic" ("esrsTopicId")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_ed602f423d73ef38205a16a56f"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_50a1eb248445b84935b069fc0c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "material_esrs_topic" ALTER COLUMN "esrsTopicId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "material_esrs_topic" ALTER COLUMN "projectId" DROP NOT NULL`,
    );
  }
}
