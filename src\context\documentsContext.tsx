import { useContext, createContext } from 'react';

interface DocumentsProviderProps {
  children: React.ReactNode;
  refetchDocuments: () => void;
}

const DocumentRefreshContext = createContext<{
  refetchDocuments: () => void;
} | null>(null);

export const useDocumentRefetch = () => {
  const context = useContext(DocumentRefreshContext);
  if (!context) {
    throw new Error(
      'DocumentRefresh must be used within a DocumentRefreshProvider'
    );
  }
  return context;
};

const DocumentsProvider: React.FC<DocumentsProviderProps> = ({
  children,
  refetchDocuments,
}) => {
  return (
    <DocumentRefreshContext.Provider value={{ refetchDocuments }}>
      {children}
    </DocumentRefreshContext.Provider>
  );
};
export default DocumentsProvider;
