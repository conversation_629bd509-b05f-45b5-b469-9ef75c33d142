BACKEND_DB_HOST=backend-db
BACKEND_DB_PORT=5432
BACKEND_DB_NAME='glacier'
BACKEND_DB_USER='root'
BACKEND_DB_PASSWORD='root'

PGADMIN_DEFAULT_PASSWORD=admin

CUSTOMER_IO_TRANSACTIONAL_API_KEY=your_api_key

PUBLIC_PORT=3000
FLOWISE_PORT=3000
FLOWISE_POSTGRES_USER='user'
FLOWISE_POSTGRES_PASSWORD='pass'
FLOWISE_POSTGRES_DB='flowise'
FLOWISE_USERNAME=matthias
FLOWISE_PASSWORD=matthias
CORS_ORIGINS=*


HELICONE_AUTH_API_KEY=sk-helicone-eu-...
#Glacier_OPENAI_API_KEY=********************************************************
AZURE_OPENAI_API_KEY=5cH...
OPENAI_API_KEY=sk-proj-sWs..

AZURE_DEPLOYMENT=gpt-4o-2
AZURE_RESOURCE=https://simon-m2vo32nd-swedencentral.openai.azure.com/

DP_MODEL=o1
GA_MODEL=o1

GRAFANA_ROOT_URL=https://app.glacier.eco/grafana
GRAFANA_DEFAULT_PASSWORD=admin