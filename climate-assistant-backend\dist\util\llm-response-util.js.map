{"version": 3, "file": "llm-response-util.js", "sourceRoot": "", "sources": ["../../src/util/llm-response-util.ts"], "names": [], "mappings": ";;;AAAA,sDAMC;AA0ED,4FAgIC;AAhND,SAAgB,qBAAqB,CAAC,WAAmB;IACvD,IAAI,WAAW,EAAE,CAAC;QAChB,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QACpD,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IAClD,CAAC;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AA0BD,SAAS,gBAAgB,CAAC,KAAa;IACrC,IAAI,CAAC;QAGH,IAAI,UAAU,GAAG,KAAK,CAAC,OAAO,CAC5B,0CAA0C,EAC1C,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE;YAC7C,OAAO,GAAG,IAAI,GAAG,MAAM,IAAI,GAAG,GAAG,KAAK,IAAI,MAAM,GAAG,KAAK,EAAE,CAAC;QAC7D,CAAC,CACF,CAAC;QAGF,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAChC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;IACtE,CAAC;AACH,CAAC;AASD,SAAS,2BAA2B,CAClC,SAAsC,EACtC,mBAA6B;IAE7B,MAAM,OAAO,GAAmC,EAAE,CAAC;IAEnD,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;QAC7B,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YAEjC,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;gBAC3B,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC;oBACxD,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;oBAC3B,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;oBAEzC,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzD,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;gBACzD,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAgB,wCAAwC,CACtD,KAAa,EACb,mBAA6B;IAa7B,MAAM,QAAQ,GAGV,EAAE,CAAC;IAGP,IAAI,UAAU,GAAG,CAAC,CAAC;IASnB,MAAM,KAAK,GAAG,mDAAmD,CAAC;IAElE,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,YAAY,EAAE,EAAE;QAClE,UAAU,EAAE,CAAC;QACb,MAAM,GAAG,GAAG,UAAU,UAAU,EAAE,CAAC;QACnC,IAAI,cAAc,GAAqD,EAAE,CAAC;QAE1E,IAAI,kBAAkB,GAAG,IAAI,GAAG,gBAAgB,CAAC;QAEjD,IAAI,GAAG,KAAK,QAAQ,EAAE,CAAC;YAErB,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACrC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;oBACvB,GAAG,CAAC,OAAO,CAAC,CAAC,IAAY,EAAE,EAAE;wBAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;wBAE1C,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAEzD,cAAc,CAAC,IAAI,CAAC;4BAClB,EAAE,EAAE,mBAAmB,CAAC,KAAK,CAAC;4BAC9B,MAAM,EAAE,IAAI;yBACb,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,GAAG,eAAe,EAAE,KAAK,CAAC,CAAC;YACrE,CAAC;YAED,kBAAkB,IAAI,mBAAmB,CAAC;QAC5C,CAAC;aAAM,IAAI,GAAG,KAAK,iBAAiB,EAAE,CAAC;YAYrC,IAAI,CAAC;gBAQH,MAAM,aAAa,GAAkB,gBAAgB,CAAC,YAAY,CAAC,CAAC;gBAEpE,MAAM,aAAa,GAAG,2BAA2B,CAC/C,aAAa,CAAC,MAAM,EACpB,mBAAmB,CACpB,CAAC;gBACF,MAAM,eAAe,GAAG,2BAA2B,CACjD,aAAa,CAAC,QAAQ,EACtB,mBAAmB,CACpB,CAAC;gBAGF,cAAc,GAAG;oBACf,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;wBAC/B,EAAE,EAAE,KAAK,CAAC,EAAE;wBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,MAAM,EAAE,IAAI;qBACb,CAAC,CAAC;oBACH,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;wBACjC,EAAE,EAAE,KAAK,CAAC,EAAE;wBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,MAAM,EAAE,KAAK;qBACd,CAAC,CAAC;iBACJ,CAAC;gBAKF,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC7B,kBAAkB,IAAI,UAAU,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;gBAC3D,CAAC;qBAAM,CAAC;oBACN,kBAAkB,IAAI,kBAAkB,CAAC;gBAC3C,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CACX,0BAA0B,GAAG,wBAAwB,EACrD,KAAK,CACN,CAAC;YACJ,CAAC;YACD,kBAAkB,IAAI,GAAG,CAAC;QAC5B,CAAC;QAGD,QAAQ,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC;QAC/B,OAAO,kBAAkB,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC;AAC7C,CAAC;AAOY,QAAA,qBAAqB,GAChC,0DAA0D,CAAC;AAShD,QAAA,iBAAiB,GAC5B,yDAAyD,CAAC"}