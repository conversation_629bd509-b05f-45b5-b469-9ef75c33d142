"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("../users/users.service");
const jwt_1 = require("@nestjs/jwt");
const bcrypt = require("bcrypt");
let AuthService = class AuthService {
    constructor(usersService, jwtService) {
        this.usersService = usersService;
        this.jwtService = jwtService;
    }
    async login(email, password) {
        const user = await this.usersService.findByEmailWithPassword(email);
        if (user === null) {
            throw new common_1.UnauthorizedException({
                message: 'E-Mail oder Passwort ist inkorrekt',
            });
        }
        const isPasswordCorrect = await bcrypt.compare(password, user?.password);
        if (!isPasswordCorrect) {
            throw new common_1.UnauthorizedException({
                message: 'E-Mail oder Passwort ist inkorrekt',
            });
        }
        const workspace = await this.usersService.findFirstWorkspaceIdByUser(user);
        const payload = {
            sub: user.id,
            id: user.id,
            email: user.email,
            workspaceId: workspace.workspaceId,
        };
        return await this.jwtService.signAsync(payload);
    }
    async registerWithCompany(registerDto) {
        const { email, password, companyName } = registerDto;
        const existingUser = await this.usersService.findByEmail(email);
        if (existingUser) {
            throw new common_1.ConflictException('User with this email already exists');
        }
        const hashedPassword = await bcrypt.hash(password, 10);
        const register = await this.usersService.createUserWithCompanyAndWorkspace({
            email,
            password: hashedPassword,
            companyName,
        });
        const payload = {
            sub: register.user.id,
            id: register.user.id,
            email: email,
            workspaceId: register.workspace.id,
        };
        return await this.jwtService.signAsync(payload);
    }
    async resetPassword(password, token) {
        const userToken = await this.usersService.validateToken(token);
        const user = await this.usersService.resetUserPassword(userToken, password);
        const payload = {
            sub: user.id,
            id: user.id,
            email: user.email,
            workspaceId: user.userWorkspaces[0].workspaceId,
        };
        return await this.jwtService.signAsync(payload);
    }
    async sendPasswordResetEmail(email, origin) {
        await this.usersService.sendPasswordResetEmail({
            email,
            origin,
            shouldSendEmail: origin.endsWith('.glacier.eco'),
        });
    }
    async validateToken(token) {
        return this.usersService.validateToken(token);
    }
    async switchUserWorkspace(userId, workspaceId) {
        const user = await this.usersService.switchWorkspace(userId, workspaceId);
        const payload = {
            sub: user.id,
            id: user.id,
            email: user.email,
            workspaceId: workspaceId,
        };
        return await this.jwtService.signAsync(payload);
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        jwt_1.JwtService])
], AuthService);
//# sourceMappingURL=auth.service.js.map