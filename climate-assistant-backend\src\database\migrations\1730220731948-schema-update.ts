import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1730220731948 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        UPDATE user_workspace set role = 'CONTRIBUTOR' from "user" where "user"."id"= "user_workspace"."userId" and "name" <> 'Glacier AI';;
        UPDATE user_workspace set role = 'SUPER_ADMIN' from "user" where "user"."id"= "user_workspace"."userId" and "name" = 'Glacier AI';;
            `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
