import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '../../ui/button';
import { Textarea } from '../../ui/textarea';
import { Label } from '../../ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

const rejectionReasonSchema = z.object({
  comment: z.string().optional(),
});

export type RejectAICommentFormData = z.infer<typeof rejectionReasonSchema>;

export function CommentRejectionReasonModal({
  open,
  setOpen,
  callback,
  commentId,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  callback: (data: RejectAICommentFormData, commentId: string) => void;
  commentId: string;
}) {
  const { register, handleSubmit } = useForm<RejectAICommentFormData>({
    defaultValues: {
      comment: '',
    },
    resolver: zodResolver(rejectionReasonSchema),
  });

  const onSubmit = async (data: RejectAICommentFormData) => {
    callback(data, commentId);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[625px]">
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-8">
          <DialogHeader>
            <DialogTitle>Reject Comment</DialogTitle>
          </DialogHeader>

          <div className="grid gap-2">
            <Label htmlFor="reason">Reason:</Label>

            <Textarea
              id="comment"
              placeholder="Enter reason for rejection"
              autoCapitalize="none"
              autoCorrect="off"
              rows={5}
              {...register('comment')}
            />
            <DialogDescription className="pt-3">
              <span className="text-yellow-600">
                WARNING: This cannot be undone.
              </span>
            </DialogDescription>
          </div>

          <DialogFooter>
            <Button type="submit" variant='darkBlue'>Submit Rejection</Button>
            <Button
              type="button"
              onClick={() => setOpen(false)}
              variant="secondary"
            >
              Cancel
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
