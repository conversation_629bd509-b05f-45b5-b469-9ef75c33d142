
import { FunctionComponent, ReactNode } from 'react';
import { SideNavigation } from './SideNavigation';
import { MixpanelEvents, MixpanelService } from '@/services/mixpanel.service.ts';

export const ChatLayout: FunctionComponent<{ children: ReactNode }> = ({
  children,
}) => {
  // Track when the chat layout is shown
  MixpanelService.track(MixpanelEvents.sidebarToggled());

  return (
    <div className="flex flex-row">
      <SideNavigation />
      <div className="flex flex-1 flex-col ml-[var(--sidebar-width)]">
        {children}
      </div>
    </div>
  );
};
