import { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';
import { FileTextIcon, Loader2, LockIcon, UploadIcon } from 'lucide-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useDropzone } from 'react-dropzone';
import { toast } from '../ui/use-toast';
import { Button } from '@/components/ui/button';
import {
  uploadEcovadisIssuesDocument,
} from '@/api/workspace-settings/workspace-settings.api';
import {
  fileUploadFormSchema,
  type FileUploadFormValues,
} from '@/validators/file-upload';
import { cn } from '@/lib/utils';

const ACCEPTED_FILE_TYPE = {
  'application/pdf': ['.pdf'],
};

export const UploadSustainabilityIssuesForm = ({
  callback,
  projectId,
}: {
  callback: () => void;
  projectId: string
}) => {
  const {
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<FileUploadFormValues>({

    resolver: zodResolver(fileUploadFormSchema),
  });

  const selectedFile =  watch('files')?.[0];
  const [isLoading, setIsLoading] = useState(false);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (!acceptedFiles?.length) {
        toast({
          variant: 'destructive',
          title: `Error occured`,
          description: `File type not supported. Please upload a file of type ${Object.values(ACCEPTED_FILE_TYPE).join(', ')}`,
        });
      } else {
        setValue('files', acceptedFiles, { shouldValidate: true });
      }
    },
    [setValue]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false,
    accept: ACCEPTED_FILE_TYPE,
  });


  const onSubmit = async (
    data: FileUploadFormValues
  ) => {

    setIsLoading(true);
    try {
      let message = 'Success!';

        const formData = new FormData();
        formData.append('file', data.files![0]);
        formData.append('projectId', projectId);

        const upload = await uploadEcovadisIssuesDocument(formData);
        message = upload.message;
      

      toast({ description: message });
      callback();
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: `Error occured`,
        description: `${error.response?.data?.message || error.message}`,
      });
    } finally {
      setIsLoading(false);
      setValue('files', []);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-8">
      {/* File Input */}

        <div className="flex flex-col gap-2">
          <div
            {...getRootProps()}
            className="border-dashed border-2 border-gray-400 rounded-md py-4 px-8 text-center cursor-pointer hover:bg-gray-50"
          >
            <input {...getInputProps()} />
            {selectedFile ? (
              <span>{selectedFile.name}</span>
            ) : isDragActive ? (
              <div className="flex flex-col">
                <FileTextIcon className="h-8 w-8 text-glacier-bluedark mx-auto my-2" />
                <span className="text-lg font-semibold text-glacier-bluedark">
                  Drop the files here...
                </span>
              </div>
            ) : (
              <div className="flex flex-col">
                <FileTextIcon className="h-8 w-8 text-glacier-bluedark mx-auto my-2" />
                <span className="text-lg font-semibold text-glacier-bluedark">
                  Drag your document here PDF
                </span>
                <span className="text-sm text-gray-700">
                  Click to select from directory
                </span>
                <div className="flex items-center gap-2 mt-5">
                  <LockIcon className="h-4 w-4 text-gray-700" />
                  <span className="text-xs text-gray-700 flex font-thin">
                    Don't worry, your data is safe with us – protected, not
                    shared, and never used for training.
                  </span>
                </div>
              </div>
            )}
          </div>
          {errors.files && (
            <p className="text-red-500 text-xs mt-1">{errors.files.message}</p>
          )}
        </div>

      <div
        className={cn('flex gap-2 flex-col')}
      >

      {/* Submit Button */}
        <div className="flex gap-5 justify-end">
          <Button
            variant="outline"
            type="button"
            onClick={() => callback()}
            className="rounded-full bg-transparent"
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="rounded-full"
            disabled={isLoading}
            style={{ backgroundColor: '#143560' }}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <UploadIcon className="h-4 w-4 mr-2" />
            )}
            {isLoading ? 'Uploading...' : 'Upload Document'}
          </Button>
        </div>
        </div>

    </form>
  );
};
