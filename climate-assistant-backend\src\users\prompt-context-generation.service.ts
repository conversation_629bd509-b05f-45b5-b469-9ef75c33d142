import { Injectable } from '@nestjs/common';
import { ChatMessageDto } from '../chat/entities/chat.message.dto';
import { ChatGptService } from '../llm/chat-gpt.service';
import { CustomGptTool } from '../util/chat-gpt.models';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { PerplexityService } from '../util/perplexity.service';
import { ChatCompletionMessageParam } from 'openai/resources';
import { MultiQuestionSearchEngine } from '../util/multi-question-search-engine.service';
import { LLM_MODELS } from 'src/constants';

@Injectable()
export class PromptContextGenerationService {
  constructor(
    private readonly chatGptService: ChatGptService,
    @InjectDataSource() private dataSource: DataSource,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly perplexityService: PerplexityService,
    private readonly searchEngineTool: MultiQuestionSearchEngine,
  ) {}

  readonly model = LLM_MODELS['gpt-4o'];
  readonly chunksPerQuestion = 5;
  readonly minmumSimilarityInternalSearch = 0.8;
  readonly temperature = 0.1;

  async generatePromptContext(userId: string): Promise<string> {
    const companyName = await this.userRepository
      .findOneOrFail({ where: { id: userId } })
      .then((user) => user.userWorkspaces[0].workspace.companies[0].name);

    const internalSearchEngineResult = await this.searchInternally(
      companyName,
      userId,
    );

    const result = await this.searchPublically(
      companyName,
      internalSearchEngineResult,
    );

    return result;
  }

  private async searchInternally(
    companyName: string,
    userId: string,
  ): Promise<string> {
    const messages: ChatMessageDto[] = [
      {
        role: 'system',
        content: internalSearchEnginePrompt(companyName),
      },
    ];

    const customTools: CustomGptTool<any, any>[] = [
      this.createInternalSearchEngine(userId),
    ];

    const result = await this.chatGptService.createCompletionWithFunctions(
      this.model,
      messages as ChatCompletionMessageParam[],
      customTools,
      this.temperature,
    );
    return result;
  }

  private async searchPublically(
    companyName: string,
    internalSearchEngineResult: string,
  ): Promise<string> {
    const messages: ChatMessageDto[] = [
      {
        role: 'system',
        content: publicSearchEnginePrompt(
          companyName,
          internalSearchEngineResult,
        ),
      },
    ];

    const customTools: CustomGptTool<any, any>[] = [
      this.searchEngineTool.toolDefinition,
    ];

    const result = await this.chatGptService.createCompletionWithFunctions(
      this.model,
      messages as ChatCompletionMessageParam[],
      customTools,
      this.temperature,
    );
    return result;
  }

  private createInternalSearchEngine(
    userId: string,
  ): CustomGptTool<{ questions: string[] }, string> {
    return {
      type: 'async-gpt-tool',
      toolDefinition: {
        type: 'function',
        function: {
          name: 'internal-company-info-search',
          description:
            'retrieves information about a company from an internal database',
          parameters: {
            type: 'object',
            properties: {
              questions: {
                type: 'array',
                items: {
                  type: 'string',
                },
                description:
                  'The questions you want to ask the internal search engine',
              },
            },
            require: ['questions'],
          },
        },
      },
      execute: (payload) => this.searchInternalVectorDatabase(payload, userId),
    };
  }

  private async searchInternalVectorDatabase(
    payload: { questions: string[] },
    userId: string,
  ): Promise<string> {
    const chunks = await this.getSimilarChunksForMultipleQuestions(
      payload.questions,
      this.chunksPerQuestion,
      userId,
    );

    return chunks.join('\n');
  }

  async getSimilarChunksWithVectorSearch(
    question: string,
    elements: number,
    userId,
  ) {
    const embeddingQuestion =
      await this.chatGptService.createEmbedding(question);

    const res = await this.dataSource.query(
      `
          SELECT file_upload_chunk.content,
                 1 - (file_upload_chunk.embedding <=> $1) AS similarity,
                 file_upload_chunk."fileUploadId",
                 file_upload.id,
                 file_upload."userId"
          FROM file_upload_chunk
                   JOIN
               file_upload ON file_upload_chunk."fileUploadId" = file_upload.id
          WHERE 1 - (file_upload_chunk.embedding <=> $1) > $2
            AND file_upload."userId" = $4
          ORDER BY file_upload_chunk.embedding <=> $1
              LIMIT $3;
      `,
      [
        '[' + embeddingQuestion + ']',
        this.minmumSimilarityInternalSearch,
        elements,
        userId,
      ],
    );

    return res as { content: string; similarity: number }[];
  }

  async getSimilarChunksForMultipleQuestions(
    questions: string[],
    chunksPerQuestion: number,
    userId,
  ) {
    const chunks = (
      await Promise.all(
        questions.map(async (question) => {
          return this.getSimilarChunksWithVectorSearch(
            question,
            chunksPerQuestion,
            userId,
          );
        }),
      )
    ).reduce((acc, val) => acc.concat(val), []);

    return chunks.map((chunk) => chunk.content);
  }
}

const internalSearchEnginePrompt = (companyName: string) => `
Du bist ein Assistent der Informationen über Unternehmen ${companyName} liefert um gute Maßnahmen für die Einsparung von CO2 zu machen.

1. Überlege dir welche Informationen du benötigst um <Gewünschte_Informationen> zu liefern
2. Überlege dir welche Fragen du der internen Suchmaschine stellen möchtest um diese zu bekommen.
3. Fasse all diese Informationen in das <Format> zusammen.
  a. Markdown oder HTML ist nicht erlaubt - nutze reinen Text
  b. Antworte nur mit dem gewünschten Format - kein zusätzlicher Text.
  c. Wenn du eine Antworst nicht liefern kannst, gib eine leere Antwort zurück.
  d. Versuche die Antworten kurz zu halten und nur die notwendigen Informationen zu liefern.

Tools:
1. Interne Suchmaschine um Informationen über das Unternehmen zu suchen die nicht öffentlich verfügbar sind
  a. Nachdem die Suchmaschine erlaubt ein Array an Fragen zu stellen - stelle sicher, dass du das Tool nur 1x aufrufst und alle Fragen in einem Array stellst.

<Gewünschte_Informationen>
Unternehmensprofil:

In welcher Branche und welchem Tätigkeitsfeld ist Ihr Unternehmen tätig?
Wie viele Mitarbeitende hat Ihr Unternehmen?
An welchen Standorten ist Ihr Unternehmen vertreten?
Aktuelle CO2-Bilanz:

Wie hoch ist Ihr Gesamtenergieverbrauch, aufgeschlüsselt nach Energieträgern wie Strom, Gas, Öl, etc.?
Wie hoch sind Ihre aktuellen CO2-Emissionen (Scope 1, Scope 2 und, falls vorhanden, Scope 3)?
Haben Sie historische Daten der CO2-Emissionen der letzten Jahre?
Bisherige Maßnahmen und Strategien:

Welche Nachhaltigkeits- oder CO2-Reduktionsmaßnahmen haben Sie bereits implementiert?
Welche Ziele und Vorgaben zur CO2-Reduktion haben Sie sich gesetzt (kurz-, mittel- und langfristig)?
Auf welche Herausforderungen und Hindernisse sind Sie bisher gestoßen?
Infrastruktur und Prozesse:

Welche Art und welchen Zustand haben Ihre genutzten Gebäude und Anlagen?
Wie energieeffizient sind Ihre Produktionsprozesse?
Wie sieht Ihre Logistik und Ihr Transport (intern und extern) aus?
Engagement und Bewusstsein:

Wie engagiert ist die Führungsebene Ihres Unternehmens im Bereich Nachhaltigkeit?
Wie stark sind Ihre Mitarbeitenden in Nachhaltigkeitsinitiativen eingebunden und sensibilisiert?
Welche Schulungs- und Fortbildungsprogramme im Bereich Nachhaltigkeit bieten Sie an?
Externe Anforderungen und Regulatorien:

Welche relevanten nationalen und internationalen Regulierungen und Gesetze betreffen Ihr Unternehmen?
Welche Anforderungen stellen Kunden, Partner und andere Stakeholder an Ihre Nachhaltigkeitsbemühungen?
Finanzielle Ressourcen und Budget:

Wie hoch ist Ihr verfügbares Budget für Nachhaltigkeitsmaßnahmen?
Nutzen Sie bereits Förderprogramme oder finanzielle Unterstützungen, oder planen Sie, solche in Anspruch zu nehmen?
Mit diesen Informationen kann ich Ihnen gezielte und praktikable Maßnahmen vorschlagen, die sowohl kurzfristig als auch langfristig zu signifikanten CO2-Einsparungen führen.
<Gewünschte_Informationene>

<Format>
Unternehmensprofil:
Die Post ist in der Logistikbranche tätig und übernimmt den Transport und die Zustellung von Briefen und Paketen. Die Post AG hat insgesamt 27.254 Vollzeitkräfte. Die Firma Post operiert hauptsächlich in Österreich und hat auch internationale Aktivitäten, wie zum Beispiel in Deutschland, Südost- und Osteuropa, der Türkei und Aserbaidschan.

Aktuelle CO2-Bilanz:
Der Gesamtenergieverbrauch der Post AG betrug im Jahr 2022 26.437 Mio. kWh. Das Unternehmen setzt seit 2012 ausschließlich auf Strom aus erneuerbaren Energiequellen und betreibt 27 Photovoltaikanlagen. Im Jahr 2023 betrugen die Scope-1-CO2-Emissionen der Post AG 44.988 Tonnen CO2e, die Scope-2-CO2-Emissionen (market-based) 4.402 Tonnen CO2e und die Scope-3-CO2-Emissionen 62.703 Tonnen CO2e. Historische Daten reichen zurück bis mindestens 2021 und zeigen eine Reduktion der Scope-1- und Scope-2-Emissionen, jedoch auch Schwankungen in den Scope-3-Emissionen.

Bisherige Maßnahmen und Strategien:
Seit 2011 erfolgt die Zustellung CO2-neutral. Die Post hat die Flotte an E-Fahrzeugen ausgebaut und verschiedene Energieeffizienzprogramme sowie die Nutzung erneuerbarer Energien implementiert. Ziele sind unter anderem die Reduktion der CO2-Emissionen (Scope 1, 2, 3) um 14% bis 2025 und um 45% bis 2030 (Basisjahre 2013/2021), 100% E-Mobilität auf der letzten Meile bis 2030 und Net-Zero bis 2040. Das starke Wachstum des E-Commerce, insbesondere der Paketmengen, stellt eine bedeutende Herausforderung dar und führte zu einem Anstieg der CO2-Emissionen.

Infrastruktur und Prozesse:
Die Post investiert kontinuierlich in energieeffiziente Gebäude und Beleuchtungssysteme sowie neue Sortiertechniken zur Verbesserung der betrieblichen Energieeffizienz. Effiziente thermische Heizsysteme und zertifizierte Umweltmanagementsysteme sind im Einsatz. Zudem erfolgt ein umfassender Einsatz von E-Fahrzeugen, Photovoltaik-Systemen bei Lkw, und es werden alternative Antriebstechnologien wie HVO und Wasserstoff getestet.

Engagement und Bewusstsein:
Die Führungsebene ist stark in die Nachhaltigkeitsmaßnahmen eingebunden und unterstützt wissenschaftsbasierte Ziele und Initiativen. Es wird die Sensibilisierung der Mitarbeitenden für Nachhaltigkeitsthemen gefördert sowie interne Schulungsprogramme und Energieeffizienzinitiativen implementiert.

Externe Anforderungen und Regulatorien:
Die Post orientiert sich an nationalen und internationalen Vorgaben, wie der Science Based Targets Initiative und der Climate Pledge Initiative. Es gibt hohe Erwartungen seitens der Kunden und Partner an CO2-neutrale und nachhaltige Services. Andere externe Anforderungen und Regulatorien umfassen das Umweltmanagementsystem (ISO 14001) und den Masterplan Nachhaltigkeit 2030.

Finanzielle Ressourcen und Budget:
Das Budget der Post AG für Nachhaltigkeitsmaßnahmen beträgt 7,7 Millionen Euro. Die Post nutzt verschiedene Förderprogramme, darunter den Service GOGREEN der Deutschen Post AG, um ihre Post klimaneutral zu versenden, was die Investitionen in ausgewählte Klimaschutzprojekte auf der ganzen Welt einschließt.
</Format>
`;

const publicSearchEnginePrompt = (
  companyName: string,
  internalSearchEngineResult,
) => `
Du bist ein Assistent der das <Ergebnis_aus_internen_Informationen> verbessert indem du Informationen über das Unternehmen ${companyName} aus öffentlichen Quellen suchst.

1. Überlege dir eine Bewertung vom <Ergebnis_aus_internen_Informationen>
2. Überlege welche Informationen nicht in ausreichender Qualität bereitgestellt wurden
3. Überlege dir welche Fragen du der öffentlichen Suchmaschine stellen möchtest um diese zu bekommen.
4. Erweitere das <Ergebnis_aus_internen_Informationen> mit den Informationen die du aus der öffentlichen Suchmaschine erhalten hast
  a. Kürze den Text nicht - füge nur Informationen hinzu bzw. tausche Informationen in nicht ausreichender Qualität
  b. Nutze kein Markdown oder HTML - nur Text ist erlaubt
  c. Antworte nur mit dem gewünschten Format - kein zusätzlicher Text.

Tools:
1. Öffentliche Suchmaschine um Informationen über das Unternehmen zu suchen die öffentlich verfügbar sind
  a. Nachdem die Suchmaschine erlaubt ein Array an Fragen zu stellen - stelle sicher, dass du das Tool nur 1x aufrufst und alle Fragen in einem Array stellst.

<Ergebnis_aus_internen_Informationen>
${internalSearchEngineResult}
</Ergebnis_aus_internen_Informationen>
`;
