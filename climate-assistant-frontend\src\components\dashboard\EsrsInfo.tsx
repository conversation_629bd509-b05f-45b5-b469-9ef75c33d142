import { useMemo } from 'react';

import { ESRSDatapoint } from '@/types';

const EsrsInfo = ({ esrsDatapoint }: { esrsDatapoint: ESRSDatapoint }) => {
  const { lawText, lawTextAR, paragraph, relatedAR } = esrsDatapoint;
  const hasLawText = useMemo(
    () => !!lawText || !!paragraph,
    [lawText, paragraph]
  );
  const hasAR = useMemo(
    () => !!lawTextAR || !!relatedAR,
    [lawTextAR, relatedAR]
  );
  if (!hasLawText && !hasAR) return null;
  return (
    <div>
      <h1 className="font-bold text-lg">ESRS Text</h1>
      <div className="flex gap-5">
        {hasLawText && (
          <div className="border border-black p-2.5 flex-1">
            <h2 className="font-bold underline">{paragraph}</h2>
            <div>
              <p>{lawText}</p>
            </div>
          </div>
        )}
        {hasAR && (
          <div className="border border-black p-2.5 flex-1">
            <h2 className="font-bold underline">{relatedAR}</h2>
            <div>
              <p>{lawTextAR}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EsrsInfo;
