import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1728907322868 implements MigrationInterface {
  name = 'SchemaUpdate1728907322868';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_workspace" ALTER COLUMN "joinedAt" DROP NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_workspace" ALTER COLUMN "joinedAt" SET NOT NULL`,
    );
  }
}
