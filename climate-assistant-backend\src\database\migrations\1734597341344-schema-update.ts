import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1734597341344 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" ADD "metadata" text`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" DROP COLUMN "metadata"`,
    );
  }
}
