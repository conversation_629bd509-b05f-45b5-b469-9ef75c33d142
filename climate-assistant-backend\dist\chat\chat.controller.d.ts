import { ChatService } from './chat.service';
import { Response } from 'express';
import { HistoryCreationDto } from './entities/history-creation.dto';
import { ChatMessageDto, HistoryUpdateDto } from './entities/chat.message.dto';
import { InitiativeDetailService } from './initiative-detail.service';
import { CsrdReportingService } from './csrd-reporting.service';
export declare class ChatController {
    private readonly chatService;
    private readonly initiativeDetailService;
    private readonly csrdReportingService;
    constructor(chatService: ChatService, initiativeDetailService: InitiativeDetailService, csrdReportingService: CsrdReportingService);
    getChats(req: any): Promise<import("./entities/chat-history.entity").ChatHistory[]>;
    createEmptyHistory(req: any, payload: HistoryCreationDto): Promise<import("./entities/chat-history.entity").ChatHistory>;
    sendMessage(req: any, res: Response, payload: {
        historyId: string;
        messages: ChatMessageDto[];
        internalProcessRequest: {
            [key: string]: string;
        } | null;
    }): Promise<void>;
    getVectorQuery(req: any): Promise<{
        content: string;
        similarity: number;
    }[]>;
    getChat(id: string): Promise<{
        messages: import("./entities/chat.message.entity").ChatMessage[];
        id: string;
        user: import("../users/entities/user.entity").User;
        title: string;
        createdAt: Date;
    }>;
    deleteChat(id: string): Promise<import("typeorm").DeleteResult>;
    updateHistoryTitle(id: string, updatedHistory: HistoryUpdateDto): Promise<import("typeorm").UpdateResult>;
}
