import { Repository } from 'typeorm';
import { DatapointRequest, DatapointRequestStatus } from 'src/datapoint/entities/datapoint-request.entity';
import { DataRequestStatus } from 'src/data-request/entities/data-request.entity';
export interface DatapointRequestStatusReviewData {
    id: string;
    content: string;
    esrsDatapoint: {
        id: string;
    };
    dataRequest: {
        id: string;
    };
    comments: {
        id: string;
        resolved: boolean;
    }[];
    datapointDocumentChunkMap: {
        id: string;
        documentChunk: {
            id: string;
            document: {
                id: string;
            };
        };
    }[];
}
export declare class ProjectDatapointRequestService {
    private readonly datapointRequestRepository;
    constructor(datapointRequestRepository: Repository<DatapointRequest>);
    findDatapointRequestCollectiveData(datapointRequestId: string): Promise<DatapointRequestStatusReviewData>;
    datapointRequestStatusProcessor(datapointRequestId: string, optionalDatapoint: boolean): Promise<DatapointRequestStatus>;
    dataRequestStatusProcessor({ status, content, approvedBy, datapointRequests, }: {
        id: string;
        content: string;
        status: DataRequestStatus;
        approvedBy: string;
        datapointRequests: {
            id: string;
            status: DatapointRequestStatus;
            content: string;
            datapointDocumentChunkMap: {
                id: string;
            }[];
        }[];
    }): DataRequestStatus;
}
