-- Create enum for generated gaps status
CREATE TYPE generated_gaps_status AS ENUM ('pending_review', 'approved', 'rejected', 'regenerating');

-- Create the project_ecovadis_gap_generation table
CREATE TABLE project_ecovadis_gap_generation (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "projectId" UUID NOT NULL REFERENCES project(id) ON DELETE CASCADE,
    "questionId" UUID NOT NULL REFERENCES ecovadis_question(id) ON DELETE CASCADE,
    "generatedContent" JSONB NOT NULL,
    "documents" UUID[] DEFAULT '{}',
    "status" generated_gaps_status NOT NULL DEFAULT 'pending_review',
    "feedback" TEXT,
    "reviewedBy" UUID REFERENCES "user"(id),
    "reviewedAt" TIMESTAMPTZ,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX idx_project_ecovadis_gap_generation_project_id ON project_ecovadis_gap_generation(projectId);
CREATE INDEX idx_project_ecovadis_gap_generation_question_id ON project_ecovadis_gap_generation(questionId);
CREATE INDEX idx_project_ecovadis_gap_generation_status ON project_ecovadis_gap_generation(status);
CREATE INDEX idx_project_ecovadis_gap_generation_reviewed_by ON project_ecovadis_gap_generation(reviewedBy);

-- Create updatedAt trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updatedAt = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_project_ecovadis_gap_generation_updated_at 
    BEFORE UPDATE ON project_ecovadis_gap_generation
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies
ALTER TABLE project_ecovadis_gap_generation ENABLE ROW LEVEL SECURITY;

-- Policy: Super Admins can see all generated gaps
CREATE POLICY "super_admin_generated_gaps_all" ON project_ecovadis_gap_generation
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_workspace uw
            WHERE uw."userId" = auth.uid()
            AND uw.role = 'SUPER_ADMIN'
        )
    );