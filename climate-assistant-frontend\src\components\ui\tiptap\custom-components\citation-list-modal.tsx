import React, { useState, useEffect } from 'react';
import { NodeViewWrapper, NodeViewProps } from '@tiptap/react';

import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '../../dialog';
import { Button } from '../../button';
import { toast } from '../../use-toast';
import { MarkdownRenderer } from '../../markdown-renderer';

import { DocumentData } from '@/types/document';
import {
  fetchDatapointRequestCitations,
  updateDatapointRequestCitations,
} from '@/api/datapoint/datapoint-request.api';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { SquareArrowOutUpRightIcon } from 'lucide-react';
import { useEditorContext } from '@/context/tiptapContext';

interface FetchedData {
  active: boolean;
  content: string;
  createdAt: string;
  documentId: string;
  document: DocumentData;
  id: string;
  matchingsJson: any | null;
  metadataJson: string;
  page: string;
  value?: string;
}

export const CitationListModal: React.FC<NodeViewProps> = (props) => {
  const { node } = props;
  const { index, refid, color, text } = node.attrs;
  const citationId = `dpcite-${index}`;
  const [open, setOpen] = useState(false);
  const [citationData, setCitationData] = useState<FetchedData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  async function fetchData() {
    try {
      const data = await fetchDatapointRequestCitations({
        datapointRequestId: refid,
        citationId: citationId,
      });
      setCitationData(data);
    } catch (error: any) {
      setError(error.message);
    }
  }

  useEffect(() => {
    if (open && refid && index !== null) {
      setLoading(true);
      setError(null);

      fetchData();
      setLoading(false);
    }
    if (error) {
      console.log(error);
    }
  }, [open, refid, index]);

  function closeModal() {
    setOpen(false);
  }

  return (
    <NodeViewWrapper
      as="span"
      data-modal-link
      data-index={index}
      data-refid={refid}
    >
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <span
            style={{ cursor: 'pointer', textDecoration: 'underline', color }}
          >
            {text === '[source]' ? (
              <SquareArrowOutUpRightIcon className="inline-flex h-3.5 -ml-1 mb-1" />
            ) : (
              text
            )}
          </span>
        </DialogTrigger>
        <DialogContent className="w-2/3 max-w-full text-slate-900">
          <DialogHeader>
            <DialogTitle>{citationData.length} source(s) found</DialogTitle>
            <DialogDescription>{loading && 'Loading...'}</DialogDescription>
          </DialogHeader>
          <CitationInformationAccordion
            citationsData={citationData}
            citationId={citationId}
            refid={refid}
            callback={closeModal}
          />
        </DialogContent>
      </Dialog>
    </NodeViewWrapper>
  );
};

export function CitationInformationAccordion({
  refid,
  citationId,
  citationsData,
  callback,
}: {
  refid: string;
  citationId: string;
  citationsData: FetchedData[];
  callback: () => void;
}) {
  const { setContent } = useEditorContext();
  async function handleActiveChange(index: number) {
    try {
      const data = await updateDatapointRequestCitations({
        datapointRequestId: refid,
        citationId: citationId,
        index: index,
      });

      setContent(data.content);

      toast({
        title: 'Citation Updated',
      });

      callback();
    } catch (error) {
      toast({
        title: 'Error updating citation',
        variant: 'destructive',
      });
    }
  }

  return (
    <Accordion
      type="single"
      collapsible
      className="w-full flex flex-col space-y-6 max-h-[80vh] overflow-y-auto"
    >
      {citationsData.map((citation, index) => (
        <AccordionItem
          key={index}
          value={`${citation.id}-${index}`}
          className="w-full border-none bg-slate-100 rounded-lg"
        >
          <AccordionTrigger className="flex items-center justify-between w-full p-4">
            <div className="flex items-center space-x-2 justify-between w-full mr-5">
              <span className="text-sm text-left">
                {citation.document.name}
              </span>
              <span className="text-sm">Page {citation.page}</span>

              {citation.active ? (
                <span className="text-glacier-bluedark text-xs">
                  Currently used
                </span>
              ) : (
                <Button
                  variant="default"
                  size="xs"
                  className="flex items-center"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleActiveChange(index);
                  }}
                >
                  Use this metric
                </Button>
              )}
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 w-full max-w-[70vw] overflow-x-auto text-sm">
            <MarkdownRenderer
              text={
                // replace occurance of citation.value with [yellow](citation.value)
                !citation.value || citation.value === '[source]'
                  ? citation.content
                  : citation.content.replace(
                      new RegExp(citation.value, 'g'),
                      `<span class="text-yellow-700 underline underline-offset-2">${citation.value}</span>`
                    )
              }
            />
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
}
