
import { useState } from "react";
import { Check } from "lucide-react";
import { cn } from "@/lib/utils";

interface MarkCompleteToggleProps {
  isComplete: boolean;
  onToggleComplete: (isComplete: boolean) => void;
  className?: string;
  disableCompleteAction?: boolean
}

export const MarkCompleteToggle = ({ 
  isComplete, 
  onToggleComplete,
  className = "",
  disableCompleteAction
}: MarkCompleteToggleProps) => {
  const [isHovered, setIsHovered] = useState(false);
  
  return (
    <div 
      className={cn(
        "flex items-center gap-2 px-3 py-1.5 rounded-md transition-colors min-w-[140px]",
        disableCompleteAction && "bg-gray-50 text-gray-400 cursor-not-allowed",
        !disableCompleteAction && isComplete && "bg-[#F2FCE2] text-green-700 hover:bg-green-100 cursor-pointer",
        !disableCompleteAction && !isComplete && "bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer",
        className
      )}
      onClick={() => !disableCompleteAction && onToggleComplete(!isComplete)}
      onMouseEnter={() => !disableCompleteAction && setIsHovered(true)}
      onMouseLeave={() => !disableCompleteAction && setIsHovered(false)}
    >
      <div className={cn(
        "h-4 w-4 flex items-center justify-center rounded-full border",
        disableCompleteAction && "border-gray-300 bg-gray-100",
        !disableCompleteAction && isComplete && "border-green-500 bg-green-500 text-white",
        !disableCompleteAction && !isComplete && "border-gray-400 bg-white"
      )}>
        {isComplete && !disableCompleteAction && <Check className="h-3 w-3" />}
      </div>
      <span className="text-sm">
        {disableCompleteAction
          ? "Loading..."
          : isComplete 
            ? "Completed" 
            : isHovered ? "Mark as Complete" : "Incomplete"}
      </span>
    </div>
  );
};
