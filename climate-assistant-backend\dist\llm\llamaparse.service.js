"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseDocumentWithLlamaparseApi = parseDocumentWithLlamaparseApi;
const axios_1 = require("axios");
const fs = require("fs");
const FormData = require("form-data");
const LLAMA_CLOUD_API_BASE_URL = 'https://api.cloud.eu.llamaindex.ai/api';
async function parseDocumentWithLlamaparseApi({ filePath, premiumMode = true, pageSeparator = '\n=================\n', splitByPage = false, }) {
    const LLAMA_CLOUD_API_KEY = process.env.LLAMA_CLOUD_API_KEY;
    if (!LLAMA_CLOUD_API_KEY) {
        throw new Error('LLAMA_CLOUD_API_KEY environment variable is not set');
    }
    try {
        if (!fs.existsSync(filePath)) {
            throw new Error(`File not found: ${filePath}`);
        }
        const formData = new FormData();
        formData.append('file', fs.createReadStream(filePath));
        formData.append('premium_mode', premiumMode.toString());
        formData.append('split_by_page', splitByPage.toString());
        formData.append('page_separator', pageSeparator);
        formData.append('output_tables_as_HTML', 'true');
        console.log('Uploading file to LlamaIndex Cloud API...');
        const uploadResponse = await axios_1.default.post(`${LLAMA_CLOUD_API_BASE_URL}/parsing/upload`, formData, {
            headers: {
                Authorization: `Bearer ${LLAMA_CLOUD_API_KEY}`,
                ...formData.getHeaders(),
            },
            timeout: 2 * 60 * 1000,
        });
        const jobId = uploadResponse.data.id;
        if (!jobId) {
            console.error('API Response:', JSON.stringify(uploadResponse.data, null, 2));
            throw new Error('No job_id returned from the API');
        }
        console.log(`Parsing job started with ID: ${jobId}`);
        let isComplete = false;
        let maxAttempts = 2 * 60;
        let attempts = 0;
        let retryDelay = 10000;
        while (!isComplete && attempts < maxAttempts) {
            attempts++;
            console.log(`Checking job status (attempt ${attempts}/${maxAttempts})...`);
            try {
                const statusResponse = await axios_1.default.get(`${LLAMA_CLOUD_API_BASE_URL}/parsing/job/${jobId}`, {
                    headers: {
                        Authorization: `Bearer ${LLAMA_CLOUD_API_KEY}`,
                    },
                    timeout: 10000,
                });
                console.log(`Current status: ${statusResponse.data.status}`);
                if (statusResponse.data.status === 'SUCCESS') {
                    isComplete = true;
                }
                else if (statusResponse.data.status === 'FAILED' ||
                    statusResponse.data.status === 'ERROR' ||
                    statusResponse.data.status === 'CANCELLED') {
                    throw new Error(`Parsing job failed: ${statusResponse.data.error || 'Unknown error'}`);
                }
                else {
                    retryDelay = Math.min(retryDelay * 2, 30000);
                    await new Promise((resolve) => setTimeout(resolve, retryDelay));
                }
            }
            catch (pollError) {
                console.log(`Polling attempt ${attempts} failed, retrying in ${retryDelay / 1000} seconds...`);
                retryDelay = Math.min(retryDelay * 1.5, 30000);
                await new Promise((resolve) => setTimeout(resolve, retryDelay));
                if (attempts >= maxAttempts) {
                    throw new Error(`Maximum polling attempts (${maxAttempts}) reached`);
                }
            }
        }
        if (!isComplete) {
            throw new Error('Parsing job timed out');
        }
        const resultResponse = await axios_1.default.get(`${LLAMA_CLOUD_API_BASE_URL}/parsing/job/${jobId}/result/markdown`, {
            headers: {
                Authorization: `Bearer ${LLAMA_CLOUD_API_KEY}`,
            },
            timeout: 30000,
        });
        if (!resultResponse.data || !resultResponse.data.markdown) {
            console.error('Result Response:', JSON.stringify(resultResponse.data, null, 2));
            throw new Error('No markdown content returned from the API');
        }
        const markdownContent = resultResponse.data.markdown;
        console.log('Parsing complete');
        return {
            text: markdownContent,
            metadata: { source: filePath },
        };
    }
    catch (error) {
        console.error('Error while parsing document:', error);
        if (axios_1.default.isAxiosError(error)) {
            console.error('API Error:', error.response?.data || error.message);
            throw new Error(`API request failed: ${error.response?.data ? JSON.stringify(error.response.data) : error.message}`);
        }
        throw error;
    }
}
//# sourceMappingURL=llamaparse.service.js.map