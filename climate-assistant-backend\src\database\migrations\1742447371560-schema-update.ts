import { MigrationInterface, QueryRunner } from 'typeorm';

//Reason for migratoin:
//Adding reportingYear to the project entity
export class SchemaUpdate1742447371560 implements MigrationInterface {
  name = 'SchemaUpdate1742447371560';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" ADD "reportingYear" character varying`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" DROP COLUMN "reportingYear"`
    );
  }
}
