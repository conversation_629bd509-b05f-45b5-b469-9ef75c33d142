
import React, { useState } from 'react';
import { EcovadisQuestion, GapItem } from '@/types/ecovadis';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';

import { QuestionOptions } from '@/components/ecovadis/question-options/QuestionOptions';
import { EstimatedScoreSection } from '@/components/ecovadis/EstimatedScoreSection';
import { GapAnalysisSection } from '@/components/ecovadis/gap-analysis/GapAnalysisSection';
import { AlertTriangle } from 'lucide-react';
import { useParams } from 'react-router-dom';
import { useEcovadisQuestionMutations } from '@/hooks/useEcovadisQuestion';
import { DocumentUpload } from '@/types/project';

interface QuestionTabsProps {
  selectedQuestion: EcovadisQuestion;
  gaps: GapItem[];
  handleMarkGapComplete: (gapId: string, isComplete: boolean) => void;
  handleUpdateOptions: (updatedOptions: any[]) => void;
  setQuestions: React.Dispatch<React.SetStateAction<EcovadisQuestion[]>>;
  documents?: DocumentUpload[];
}

export const QuestionTabs: React.FC<QuestionTabsProps> = ({
  selectedQuestion,
  gaps,
  handleUpdateOptions,
  setQuestions,
  documents = [],
}) => {
  const { toggleOption } = useEcovadisQuestionMutations();
  const [loadingOptionIds, setLoadingOptionIds] = useState<string[]>([]);

  const handleUpdateQuestion = (updatedQuestion: EcovadisQuestion) => {
    setQuestions(prevQuestions => 
      prevQuestions.map(q => 
        q.projectQuestionId === updatedQuestion.projectQuestionId ? updatedQuestion : q
      )
    );
  };

  // Handler for toggling options
  const handleToggleOption = async (optionId: string, selected: boolean) => {
    // Add to loading state
    setLoadingOptionIds(prev => [...prev, optionId]);
    
    try {
      // Call the mutation
      await toggleOption.mutateAsync({ optionId, selected });
    } finally {
      // Remove from loading state
      setLoadingOptionIds(prev => prev.filter(id => id !== optionId));
    }
  };

  if(!selectedQuestion?.options) return null;
    
  return (
    <Tabs defaultValue="answer">
      <TabsList className="mb-4">
        <TabsTrigger value="answer">Answer</TabsTrigger>
        <TabsTrigger value="gaps" className="flex items-center gap-1">
          <AlertTriangle size={14} className="text-amber-500" />
          Gaps
        </TabsTrigger>
        <TabsTrigger value="estimated-score">
          Estimated Score
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="answer" className="space-y-4">
          <QuestionOptions 
            options={selectedQuestion.options} 
            onUpdateOptions={handleUpdateOptions}
            question={selectedQuestion}
            onUpdateQuestion={handleUpdateQuestion}
            onToggleOption={handleToggleOption}
            loadingOptionIds={loadingOptionIds}
            documents={documents}
          />
      </TabsContent>
      
      <TabsContent value="gaps">
        <GapAnalysisSection
          questionId={selectedQuestion.questionId}
          projectQuestionId={selectedQuestion.projectQuestionId}
        />
      </TabsContent>
      
      <TabsContent value="estimated-score">
        <EstimatedScoreSection
          question={selectedQuestion}
        />
      </TabsContent>
    </Tabs>
  );
};
