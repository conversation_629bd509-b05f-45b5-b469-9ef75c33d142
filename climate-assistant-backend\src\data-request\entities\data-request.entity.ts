import { ESRSDisclosureRequirement } from '../../knowledge-base/entities/esrs-disclosure-requirement.entity';
import { User } from '../../users/entities/user.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Project } from '../../project/entities/project.entity';
import { DatapointRequest } from '../../datapoint/entities/datapoint-request.entity';
import { Comment } from '../../project/entities/comment.entity';
import { CommentGeneration } from '../../project/entities/comment-generation.entity';
import { DataRequestGeneration } from './datarequest-generation.entity';

// enums/data_request_status.enum.ts
export enum DataRequestStatus {
  NoData = 'no_data',
  IncompleteData = 'incomplete_data',
  Draft = 'draft',
  CompleteData = 'complete_data',
  ApprovedAnswer = 'approved_answer',
  NotAnswered = 'not_answered',
}

export enum DataRequestQueueStatus {
  QueuedForGeneration = 'queued_for_generation',
  QueuedForReview = 'queued_for_review',
}

@Entity()
export class DataRequest {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('int')
  dataRequestTypeId: number;

  @Column({ type: 'varchar' })
  dataRequestType: string;

  @Column({ type: 'enum', enum: DataRequestStatus })
  status: DataRequestStatus;

  @Column({ type: 'text', default: '' })
  content: string;

  @Column({ type: 'text', nullable: true })
  customUserRemark: string;

  @Column({ type: 'date', nullable: true })
  dueDate: Date;

  @Column({ type: 'uuid', nullable: true })
  responsiblePersonId: string;

  @Column({ type: 'uuid', nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({
    type: 'enum',
    enum: DataRequestQueueStatus,
    default: null,
  })
  queueStatus: DataRequestQueueStatus;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column('uuid')
  projectId: string;

  @OneToMany(
    () => DataRequestGeneration,
    (dataRequestGeneration) => dataRequestGeneration.dataRequest,
  )
  dataRequestGenerations: DataRequestGeneration[];

  @ManyToOne(() => ESRSDisclosureRequirement)
  @JoinColumn({ name: 'dataRequestTypeId' })
  disclosureRequirement: ESRSDisclosureRequirement;

  @ManyToOne(() => User, (user) => user.dataRequests)
  @JoinColumn({ name: 'responsiblePersonId' })
  responsiblePerson: User;

  @ManyToOne(() => User, (user) => user.dataRequests)
  @JoinColumn({ name: 'approvedBy' })
  approver: User;

  @ManyToOne(() => Project, (project) => project.dataRequests)
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @OneToMany(
    () => DatapointRequest,
    (datapointRequest) => datapointRequest.dataRequest,
  )
  datapointRequests: DatapointRequest[];

  @OneToMany(() => Comment, (comment) => comment.dataRequest, {
    nullable: true,
    createForeignKeyConstraints: false,
  })
  comments: Comment[];

  @OneToMany(
    () => CommentGeneration,
    (commentGeneration) => commentGeneration.dataRequest,
    {
      nullable: true,
      createForeignKeyConstraints: false,
    },
  )
  commentGenerations: CommentGeneration[];
}
