
import { useMemo, useState } from 'react';
import {
  FileText,
  Loader2,
  Edit,
  Trash,
  ExternalLink,
  Link2Off,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { AttachedDocument, AttachmentSource } from '@/types/ecovadis';
import { toast } from 'sonner';
import { useEcovadisProject } from '@/hooks/useEcovadisProject';
import { Separator } from "@/components/ui/separator";
import { useParams } from 'react-router-dom';
import { convertToRanges } from '@/lib/utils';
import { Chip } from '@/components/ui/chip';
import { Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';

interface DocumentsListProps {
  documents: AttachedDocument[];
  onEdit: (docId: string) => void;
  onRemove: (docId: string, chunkIds?: string[]) => void;
  detachingDocIds?: string[];
  updatingDocIds?: string[];
}

interface GroupedDocument extends Omit<AttachedDocument, 'pages' | 'chunkId'> {
  pages: number[];
  chunkIds: string[];
  originalDocs: AttachedDocument[];
}

export const DocumentsList: React.FC<DocumentsListProps> = ({
  documents,
  onEdit,
  onRemove,
  detachingDocIds = [],
  updatingDocIds = []
}) => {
  const [expandedDocs, setExpandedDocs] = useState<string[]>([]);
  const { id: projectId } = useParams<{ id: string }>();
  const { invalidateProjectData } = useEcovadisProject(projectId);

  const groupedDocuments = useMemo(() => {
    const groups = documents.reduce((acc, doc) => {
      if (!acc[doc.id]) {
        acc[doc.id] = {
          id: doc.id,
          name: doc.name,
          pages: [],
          chunkIds: [],
          originalDocs: [],
          comment: doc.comment || '',
          createdBy: doc.createdBy,
          createdByName: doc.createdByName,
          attachmentSource: doc.attachmentSource
        };
      }
      
      if (doc.pages) {
        const pageNum = parseInt(doc.pages, 10);
        if (!isNaN(pageNum)) {
          acc[doc.id].pages.push(pageNum);
        }
      }
      
      if (doc.chunkId) {
        acc[doc.id].chunkIds.push(doc.chunkId);
      }
      
      if(!acc[doc.id].comment) {
        acc[doc.id].comment = doc.comment;
      }
      
      acc[doc.id].originalDocs.push(doc);
      return acc;
    }, {} as Record<string, GroupedDocument>);

    return Object.values(groups);
  }, [documents]);

  const getSourceChip = (attachmentSource?: AttachmentSource, createdByName?: string) => {
    switch (attachmentSource) {
      case AttachmentSource.MANUAL:
        return (
          <Tooltip delayDuration={0}>
            <TooltipTrigger>
              <Chip variant="primary" className="text-xs">
              Attached by user
              </Chip>
            </TooltipTrigger>
            <TooltipContent side='bottom'>
              <p>Manually attached document by {createdByName}</p>
            </TooltipContent>
          </Tooltip>
        );
      case AttachmentSource.AI:
        return (
          <Tooltip delayDuration={0}>
            <TooltipTrigger>
              <Chip variant="blue" className="text-xs">
                AI Linked
              </Chip>
            </TooltipTrigger>
            <TooltipContent side='bottom'>
              <p>Automatically linked by Glacier AI</p>
            </TooltipContent>
          </Tooltip>
        );
      case AttachmentSource.IMPORT:
      case AttachmentSource.SYSTEM:
        return (
          <Tooltip delayDuration={0}>
            <TooltipTrigger>
              <Chip variant="orange" className="text-xs">
                Questionnaire Import
              </Chip>
            </TooltipTrigger>
            <TooltipContent side='bottom'>
              <p>Imported from uploaded Ecovadis questionnaire</p>
            </TooltipContent>
          </Tooltip>
        );
      default:
        return (<></>);
    }
  };

  const toggleExpand = (docId: string) => {
    setExpandedDocs(prev =>
      prev.includes(docId)
        ? prev.filter(id => id !== docId)
        : [...prev, docId]
    );
  };

  const handleOpenDocument = () => {
    // This would open the document in a new tab in a real implementation
    toast.info("Opening document in a new tab...");
  };

  if (!documents || documents.length === 0) {
    return (
      <div className="text-sm text-gray-500 italic p-2">
        No documents attached yet.
      </div>
    );
  }

  const handleDetachClick = (docId: string, chunkIds: string[]) => {
    onRemove(docId, chunkIds);
    // Add invalidation here to ensure data is refreshed after detaching
    invalidateProjectData();
  };

  const handleEditClick = (docId: string) => {
    onEdit(docId);
    // Add invalidation here to ensure data is refreshed after editing
    invalidateProjectData();
  };

  return (
    <div className="space-y-2">
      {groupedDocuments.length > 0 ? (
        groupedDocuments.map(doc => {
          const isDetaching = detachingDocIds.includes(doc.id);
          const isUpdating = updatingDocIds.includes(doc.id);
          const isLoading = isDetaching || isUpdating;
          const isExpanded = expandedDocs.includes(doc.id);
          
          // Sort pages for display
          const sortedPages = [...doc.pages].sort((a, b) => a - b);
          const pageRanges = convertToRanges(sortedPages);
          
          return (
            <div 
              key={doc.id} 
              className="bg-white rounded-md p-4 border border-gray-200 hover:border-gray-300 transition-colors"
            >
              <div className="flex flex-col">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="text-sm font-medium text-glacier-darkBlue flex-1">{doc.name}</h3>
                  {getSourceChip(doc.attachmentSource, doc.createdByName)}
                </div>
                
                <div className="flex items-center gap-2 mb-4">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex items-center gap-1 text-xs rounded-full"
                    onClick={() => window.open(`/documents/${doc.id}`, '_blank')}
                  >
                    <ExternalLink className="h-3.5 w-3.5" />
                    Open in new tab
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    size="sm" 
                    disabled={isLoading}
                    className="flex items-center gap-1 text-xs rounded-full"
                    onClick={() => handleEditClick(doc.id)}
                  >
                    {isUpdating ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Edit className="h-4 w-4" />
                    )}
                    Edit
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex items-center gap-1 text-xs rounded-full"
                    onClick={() => handleDetachClick(doc.id, doc.chunkIds)}
                    disabled={isLoading}
                  >
                    {isDetaching ? (
                      <Loader2 className="h-3.5 w-3.5 animate-spin" />
                    ) : (
                      <Link2Off className="h-3.5 w-3.5" />
                    )}
                    Detach from question
                  </Button>
                </div>
                
                <Separator className="my-2" />
                
                <div className="space-y-2 text-sm text-gray-600 pt-2">
                  {pageRanges.length > 0 && (
                    <p>
                      <span className="font-medium">Relevant pages:</span> {pageRanges.join(', ')}
                    </p>
                  )}
                  
                  <p>
                    <span className="font-medium">Comment:</span> {doc.comment || ''}
                  </p>
                </div>
              </div>
            </div>
          )}
        )
      ) : (
        <div className="text-sm text-gray-500 italic p-3 text-center bg-white rounded-md border border-gray-200">
          No documents attached yet
        </div>
      )}
    </div>
  );
};
