import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1721366985356 implements MigrationInterface {
  name = 'SchemaUpdate1721366985356';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "user_prompt_context"
       (
           "userId"  uuid              NOT NULL,
           "context" character varying NOT NULL,
           CONSTRAINT "PK_fa868054a006042b047c0619387" PRIMARY KEY ("userId")
       )`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_prompt_context"
          ADD CONSTRAINT "FK_fa868054a006042b047c0619387" FOREIGN KEY ("userId") REFERENCES "user" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_prompt_context" DROP CONSTRAINT "FK_fa868054a006042b047c0619387"`,
    );
    await queryRunner.query(`DROP TABLE "user_prompt_context"`);
  }
}
