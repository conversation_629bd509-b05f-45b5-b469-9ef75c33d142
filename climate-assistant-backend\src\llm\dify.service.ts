import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance, AxiosError } from 'axios';
import { ConfigService } from '@nestjs/config';
import { DifyApiError } from 'src/middleware/error.middleware';

export interface DifyCreateDocumentRequest {
  name: string;
  text: string;
  indexing_technique: 'high_quality' | 'economy';
  process_rule: {
    mode: 'automatic' | 'custom';
  };
}

export interface DifyBaseResponse {
  batch: string;
}

export interface DifyDocument {
  id: string;
  position: number;
  data_source_type: 'upload_file' | string;
  data_source_info: {
    upload_file_id: string;
  };
  dataset_process_rule_id: string;
  name: string;
  created_from: 'api' | 'web' | string;
  created_by: string;
  created_at: number; // Unix timestamp
  tokens: number;
  indexing_status:
    | 'waiting'
    | 'parsing'
    | 'indexing'
    | 'completed'
    | 'error'
    | string;
  error: string | null;
  enabled: boolean;
  disabled_at: number | null;
  disabled_by: string | null;
  archived: boolean;
  display_status: 'queuing' | 'indexing' | 'ready' | 'error' | string;
  word_count: number;
  hit_count: number;
  doc_form: 'text_model' | string;
}

export interface DifyUpdateMetadataResponse extends DifyBaseResponse {
  status: number;
}

export interface DifyCreateDocumentResponse extends DifyBaseResponse {
  document: DifyDocument;
}

@Injectable()
export class DifyService {
  private readonly logger = new Logger(DifyService.name);
  private readonly client: AxiosInstance;
  private readonly datasetId: string;
  private readonly metadataFieldMappings = [
    {
      id: 'eebd5604-b852-4c17-baca-6a4967a70e2e',
      name: 'workspace_id',
      type: 'string',
    },
    {
      id: 'f8f93e58-b965-46d5-979d-d109cb46a7a0',
      name: 'document_chunk_id',
      type: 'string',
    },
    {
      id: '525d210f-a5ca-4038-8329-eef6f0099493',
      name: 'project_id',
      type: 'string',
    },
    {
      id: '99d3166e-29d8-4fe9-9d59-421bc003a514',
      name: 'document_id',
      type: 'string',
    },
    {
      id: '6294bae1-babf-4b25-a823-03340a21e2be',
      name: 'page',
      type: 'string',
    },
  ];

  constructor(private configService: ConfigService) {
    const DIFY_BASE_URL = 'https://dify-ai.glacier.eco/v1';
    const DIFY_KNOWLEDGE_API_KEY = process.env.DIFY_KNOWLEDGE_API_KEY;
    // this.datasetId = 'cbc8fce4-dcad-47f7-92b4-4e46319d08bc'; // dev
    this.datasetId = 'd46ea0e0-00cb-4972-a5c4-12d760738781'; // prod

    this.client = axios.create({
      baseURL: DIFY_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${DIFY_KNOWLEDGE_API_KEY}`,
      },
      timeout: 300000,
    });
  }

  /**
   * Update metadata for one or more documents
   *
   * @param operationData Array of document operations including metadata
   * @returns Response data from Dify API
   * @throws DifyApiError if the API request fails
   */
  async updateDocumentMetadata(
    difyDocumentId: string,
    metadata: {
      workspace_id?: string;
      document_chunk_id?: string;
      project_id?: string;
      document_id?: string;
      page?: string;
    }
  ): Promise<DifyUpdateMetadataResponse> {
    try {
      const payload = this.createMetadataOperationData(
        difyDocumentId,
        metadata
      );
      const response = await this.client.post<DifyUpdateMetadataResponse>(
        `/datasets/${this.datasetId}/documents/metadata`,
        {
          operation_data: payload,
        }
      );

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      this.logger.error(
        `Error updating document metadata: ${axiosError.message}`,
        axiosError.stack
      );

      throw new DifyApiError(
        axiosError.message,
        axiosError.response?.status,
        axiosError.response?.data
      );
    }
  }

  /**
   * Create a new document from text
   *
   * @param name Document name
   * @param text Document content
   * @param indexingTechnique Indexing quality level (high_quality or economy)
   * @param processMode Processing mode (automatic or custom)
   * @returns Response data from Dify API
   * @throws DifyApiError if the API request fails
   */
  async createDocumentFromText({
    name,
    text,
    indexingTechnique = 'high_quality',
    processMode = 'automatic',
  }: {
    name: string;
    text: string;
    indexingTechnique?: 'high_quality' | 'economy';
    processMode?: 'automatic' | 'custom';
  }): Promise<DifyCreateDocumentResponse> {
    try {
      const payload: DifyCreateDocumentRequest = {
        name,
        text,
        indexing_technique: indexingTechnique,
        process_rule: {
          mode: processMode,
        },
      };

      const response = await this.client.post<DifyCreateDocumentResponse>(
        `/datasets/${this.datasetId}/document/create-by-text`,
        payload
      );

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      this.logger.error(
        `Error creating document from text: ${axiosError.message}`,
        axiosError.stack
      );

      throw new DifyApiError(
        axiosError.message,
        axiosError.response?.status,
        axiosError.response?.data
      );
    }
  }

  /**
   * Generate operation data for document metadata updates
   *
   * @param difyDocumentId The ID of the Dify document to update
   * @param metadata Object containing metadata values
   * @returns Array of operation data objects ready for the Dify API
   */
  createMetadataOperationData(
    difyDocumentId: string,
    metadata: {
      workspace_id?: string;
      document_chunk_id?: string;
      project_id?: string;
      document_id?: string;
      page?: string;
    }
  ): Array<{
    document_id: string;
    metadata_list: Array<{
      id: string;
      name: string;
      type: string;
      value: string;
    }>;
  }> {
    // Create the metadata list by checking each possible field
    const metadataList = this.metadataFieldMappings
      .filter((mapping) => {
        const value = metadata[mapping.name as keyof typeof metadata];
        return value !== undefined && value !== null && value !== '';
      })
      .map((mapping) => {
        return {
          id: mapping.id,
          name: mapping.name,
          type: mapping.type,
          value: metadata[mapping.name as keyof typeof metadata] as string,
        };
      });

    // Only return an operation if we have metadata to update
    if (metadataList.length === 0) {
      return [];
    }

    return [
      {
        document_id: difyDocumentId,
        metadata_list: metadataList,
      },
    ];
  }
}
