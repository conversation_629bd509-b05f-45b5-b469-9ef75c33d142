import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { FileTextIcon, Loader2, LockIcon, UploadIcon } from 'lucide-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useDropzone } from 'react-dropzone';

import { toast } from '../ui/use-toast';

import { Button } from '@/components/ui/button';
import {
  updateDocumentSettings,
  uploadDocument,
} from '@/api/workspace-settings/workspace-settings.api';
import {
  DocumentSettingsFormData,
  documentSettingsSchema,
  type FileUploadFormData,
  type fileUploadSchema,
} from '@/validators/file-upload';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { cn, getDaysInMonth } from '@/lib/utils';

const categories = [
  { id: 'ESRS 2', label: 'General Information', color: '#48698B' },
  { id: 'ESRS E', label: 'Environmental', color: '#5FBC99' },
  { id: 'ESRS S', label: 'Social', color: '#3381C9' },
  { id: 'ESRS G', label: 'Governance', color: '#CA8A04' },
];

const ACCEPTED_FILE_TYPE = {
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
    '.xlsx',
  ],
};

export const DocumentSettingsForm = ({
  callback,
  formSchema,
  documentData,
}: {
  callback: () => void;
  formSchema: typeof documentSettingsSchema | typeof fileUploadSchema;
  documentData?: {
    documentId: string;
    documentType: string;
    esrsCategory: string[];
    year: number;
    month: number | undefined;
    day: number | undefined;
    remarks: string;
  };
}) => {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<DocumentSettingsFormData | FileUploadFormData>({
    defaultValues: documentData || {
      esrsCategory: categories.map((category) => category.id),
      documentType: 'Other',
      year: new Date().getFullYear(),
      month: undefined,
      day: undefined,
      remarks: '',
    },
    resolver: zodResolver(formSchema),
  });

  const isFileUpload = formSchema.shape.files !== undefined && !documentData;
  const selectedFile = isFileUpload ? watch('files')?.[0] || null : null;
  const [isLoading, setIsLoading] = useState(false);

  const selectedCategories = watch('esrsCategory');
  const handleCheckboxChange = (categoryId: string) => {
    const updatedCategories = selectedCategories.includes(categoryId)
      ? selectedCategories.filter((id) => id !== categoryId) // Remove if already selected
      : [...selectedCategories, categoryId]; // Add if not selected

    setValue('esrsCategory', updatedCategories, { shouldValidate: true });
  };

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (!acceptedFiles?.length) {
        toast({
          variant: 'destructive',
          title: `Error occured`,
          description: `File type not supported. Please upload a file of type ${Object.values(ACCEPTED_FILE_TYPE).join(', ')}`,
        });
      } else {
        setValue('files', acceptedFiles, { shouldValidate: true });
      }
    },
    [setValue]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false,
    accept: ACCEPTED_FILE_TYPE,
  });

  const documentType = watch('documentType');
  const selectedYear = watch('year');
  const selectedMonth = watch('month');
  const selectedDay = watch('day');

  const currentYear = new Date().getFullYear();
  const years = Array.from(
    { length: currentYear - 1900 + 1 },
    (_, i) => 1900 + i
  ).reverse();
  const months = Array.from({ length: 12 }, (_, i) => i + 1);
  const daysInMonth = getDaysInMonth(selectedYear, selectedMonth);
  const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);

  useEffect(() => {
    if (selectedDay && selectedDay > daysInMonth) {
      setValue('day', undefined, { shouldValidate: true });
    }
  }, [selectedDay, daysInMonth]);

  const onSubmit = async (
    data: DocumentSettingsFormData | FileUploadFormData
  ) => {
    setIsLoading(true);
    try {
      let message = 'Success!';
      if (isFileUpload) {
        const formData = new FormData();
        formData.append('file', data.files![0]);
        formData.append('documentType', data.documentType);
        formData.append('esrsCategory', data.esrsCategory.join(','));
        formData.append('year', data.year.toString());
        formData.append('month', `${data.month}`);
        formData.append('day', `${data.day}`);
        formData.append('remarks', data.remarks || '');

        const upload = await uploadDocument(formData);
        message = upload.message;
      } else {
        const update = await updateDocumentSettings(
          documentData!.documentId,
          data
        );
        message = update.message;
      }

      toast({ description: message });
      callback();
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: `Error occured`,
        description: `${error.response?.data?.message || error.message}`,
      });
    } finally {
      setIsLoading(false);
      setValue('files', []);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-8">
      {/* File Input */}
      {isFileUpload && (
        <div className="flex flex-col gap-2">
          <div
            {...getRootProps()}
            className="border-dashed border-2 border-gray-400 rounded-md py-4 px-8 text-center cursor-pointer hover:bg-gray-50"
          >
            <input {...getInputProps()} />
            {selectedFile ? (
              <span>{selectedFile.name}</span>
            ) : isDragActive ? (
              <div className="flex flex-col">
                <FileTextIcon className="h-8 w-8 text-glacier-bluedark mx-auto my-2" />
                <span className="text-lg font-semibold text-glacier-bluedark">
                  Drop the files here...
                </span>
              </div>
            ) : (
              <div className="flex flex-col">
                <FileTextIcon className="h-8 w-8 text-glacier-bluedark mx-auto my-2" />
                <span className="text-lg font-semibold text-glacier-bluedark">
                  Drag your document here (XLSX or PDF)
                </span>
                <span className="text-sm text-gray-700">
                  Click to select from directory
                </span>
                <div className="flex items-center gap-2 mt-5">
                  <LockIcon className="h-4 w-4 text-gray-700" />
                  <span className="text-xs text-gray-700 flex font-thin">
                    Don't worry, your data is safe with us – protected, not
                    shared, and never used for training.
                  </span>
                </div>
              </div>
            )}
          </div>
          {errors.files && (
            <p className="text-red-500 text-xs mt-1">{errors.files.message}</p>
          )}
        </div>
      )}

      <div
        className={cn('flex gap-2', isFileUpload ? 'flex-col' : 'flex-wrap')}
      >
        {/* Document Type */}

        <div
          className={cn(
            'flex flex-col gap-2',
            isFileUpload ? 'w-full' : 'w-1/2 order-1'
          )}
        >
          <Label className="text-sm font-medium">Document Type*</Label>
          <Select
            value={documentType}
            onValueChange={(value) =>
              setValue('documentType', value, { shouldValidate: true })
            }
          >
            <SelectTrigger className="border border-gray-400 w-2/3">
              <SelectValue placeholder="Choose Document Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Materiality Analysis">
                Materiality Analysis
              </SelectItem>
              <SelectItem value="Sustainability Report">
                Sustainability Report
              </SelectItem>
              <SelectItem value="Business Report">Business Report</SelectItem>
              <SelectItem value="Policy">Policy</SelectItem>
              <SelectItem value="Strategy Document">
                Strategy Document
              </SelectItem>
              <SelectItem value="Website">Website</SelectItem>
              <SelectItem value="Other">Other</SelectItem>
            </SelectContent>
          </Select>
          {errors.documentType && (
            <p className="text-red-500 text-xs mt-1">
              {errors.documentType.message}
            </p>
          )}
        </div>

        {/* Relevant ESRS Category */}
        <div
          className={cn(
            'flex flex-col gap-2',
            isFileUpload ? 'w-full' : 'w-full order-3'
          )}
        >
          <Label className="text-sm font-medium">
            Relevant for ESRS Category*
          </Label>
          <div
            className={cn('flex flex-wrap gap-2', isFileUpload && 'max-w-2xl')}
          >
            {categories.map((category) => (
              <Label
                htmlFor={category.id}
                key={category.id}
                className="flex items-center space-x-2 bg-slate-300/80 py-2 px-3 rounded-lg cursor-pointer"
              >
                <Checkbox
                  id={category.id}
                  checked={selectedCategories.includes(category.id)}
                  onCheckedChange={() => handleCheckboxChange(category.id)}
                  className="hover:border-black"
                />
                <div className="font-normal">
                  <span
                    className="inline-block rounded-md px-2 py-1 text-white mr-2"
                    style={{ backgroundColor: category.color }}
                  >
                    {category.id}
                  </span>
                  {category.label}
                </div>
              </Label>
            ))}
          </div>
          {errors.esrsCategory && (
            <p className="text-red-500 text-xs mt-1">
              {errors.esrsCategory.message}
            </p>
          )}
        </div>

        {/* Approximate Publishing Date */}
        <div
          className={cn(
            'flex flex-col gap-2',
            isFileUpload ? 'w-full' : 'order-2'
          )}
        >
          <Label className="text-sm font-medium">
            Approximate Publishing Date*
          </Label>
          <div className="flex gap-2">
            <div className="flex flex-col">
              <Select
                value={selectedYear?.toString()}
                onValueChange={(value) =>
                  setValue('year', Number(value), { shouldValidate: true })
                }
              >
                <SelectTrigger className="border border-gray-400 w-20">
                  <SelectValue placeholder="Year*" />
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.year && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.year.message}
                </p>
              )}
            </div>

            <div className="flex flex-col">
              <Select
                value={selectedMonth?.toString()}
                onValueChange={(value) =>
                  setValue('month', Number(value), { shouldValidate: true })
                }
              >
                <SelectTrigger className="border border-gray-400 w-32">
                  <SelectValue placeholder="Month" />
                </SelectTrigger>
                <SelectContent>
                  {months.map((month) => {
                    const monthName = new Date(0, month - 1).toLocaleString(
                      'default',
                      { month: 'long' }
                    );
                    return (
                      <SelectItem key={month} value={month.toString()}>
                        {monthName}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              {errors.month && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.month.message}
                </p>
              )}
            </div>

            <div className="flex flex-col">
              <Select
                value={selectedDay?.toString()}
                onValueChange={(value) =>
                  setValue('day', Number(value), { shouldValidate: true })
                }
                disabled={!selectedMonth}
              >
                <SelectTrigger className="border border-gray-400 w-20">
                  <SelectValue placeholder="Day" />
                </SelectTrigger>
                <SelectContent>
                  {days.map((day) => (
                    <SelectItem key={day} value={day.toString()}>
                      {day}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.day && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.day.message}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Remarks */}
      <div className="flex flex-col gap-2">
        <Label className="text-sm font-medium">
          Remarks
          <span className="font-normal"> (optional)</span>
        </Label>
        <Textarea
          className="border border-gray-400"
          placeholder="Who wrote or approved this document?
  Usefull details for interpretation (e.g., importance of the document, creation process, context)"
          {...register('remarks')}
        />
      </div>

      {/* Submit Button */}
      {isFileUpload ? (
        <div className="flex gap-5 justify-end">
          <Button
            variant="outline"
            type="button"
            onClick={() => callback()}
            className="rounded-full bg-transparent"
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="rounded-full"
            disabled={isLoading}
            style={{ backgroundColor: '#143560' }}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <UploadIcon className="h-4 w-4 mr-2" />
            )}
            {isLoading ? 'Uploading...' : 'Upload Document'}
          </Button>
        </div>
      ) : (
        <div className="flex">
          <Button
            type="submit"
            className="rounded-full"
            disabled={isLoading}
            style={{ backgroundColor: '#143560' }}
          >
            Save
          </Button>
        </div>
      )}
    </form>
  );
};
