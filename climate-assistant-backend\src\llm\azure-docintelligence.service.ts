import DocumentIntelligence, {
  isUnexpected,
  getLong<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  AnalyzeOperationOutput,
  parseResultIdFromResponse,
  streamToUint8Array,
} from '@azure-rest/ai-document-intelligence';
import { DefaultAzureCredential } from '@azure/identity';
import * as fs from 'fs';
import * as path from 'path';
import { Injectable, Logger } from '@nestjs/common';
import throat from 'throat';

import { LLM_MODELS } from '../constants';
import { LlmRateLimiterService } from 'src/llm-rate-limiter/llm-rate-limiter.service';

export interface FigureInterpretation {
  valid: boolean;
  type:
    | 'chart'
    | 'diagram'
    | 'table'
    | 'image'
    | 'logo'
    | 'signature'
    | 'decoration'
    | 'other';
  description?: string;
  data?: any;
  insights?: string[];
  error?: string;
}

export interface ExtractedFigure {
  id: string;
  pageNumber: number;
  interpretation: FigureInterpretation;
  imagePath?: string;
  boundingBox?: {
    top: number;
    left: number;
    width: number;
    height: number;
  };
}

export interface DocumentAnalysisResult {
  text: string;
  metadata: {
    source: string;
    contentFormat: string;
    pageCount?: number;
    apiVersion?: string;
  };
  figures?: ExtractedFigure[][];
}

@Injectable()
export class AzureDocumentIntelligenceService {
  private readonly logger = new Logger(AzureDocumentIntelligenceService.name);

  private readonly figureProcessingLimit = throat(10);
  constructor(private readonly llmRateLimiterService: LlmRateLimiterService) {}

  /**
   * Parse a document using Azure Document Intelligence API
   *
   * @param filePath Local file path to be uploaded
   * @param modelId The model to use for analysis (default: "prebuilt-layout")
   * @param locale The locale to use for analysis (optional)
   * @param features Additional features to enable (optional)
   * @param premiumMode Whether to extract and interpret figures with GPT-4o
   * @param workspaceId Workspace ID for organizing saved images
   * @param documentId Document ID for organizing saved images
   * @returns object containing the parsed text, metadata, and optionally figures
   */
  async parseDocument({
    filePath,
    modelId = 'prebuilt-layout',
    locale = 'en-US',
    premiumMode = true,
    workspaceId,
    documentId,
    userId,
  }: {
    filePath: string;
    modelId?: string;
    locale?: string;
    premiumMode?: boolean;
    workspaceId: string;
    documentId: string;
    userId: string;
  }): Promise<DocumentAnalysisResult> {
    // Check environment variables
    const endpoint = process.env.DOCUMENT_INTELLIGENCE_ENDPOINT;
    const apiKey = process.env.DOCUMENT_INTELLIGENCE_API_KEY;

    if (!endpoint) {
      throw new Error(
        'DOCUMENT_INTELLIGENCE_ENDPOINT environment variable is not set'
      );
    }

    try {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      let features: string[] = [];
      // if (premiumMode) {
      //   features = ['OCR_HIGH_RESOLUTION'];
      // }

      // Initialize client
      this.logger.log('Initializing Azure Document Intelligence client...');
      const client = apiKey
        ? DocumentIntelligence(endpoint, { key: apiKey })
        : DocumentIntelligence(endpoint, new DefaultAzureCredential());

      // Read file and convert to base64
      this.logger.log('Reading file and converting to base64...');
      const base64Source = fs.readFileSync(filePath, { encoding: 'base64' });

      // Prepare query parameters
      const queryParameters: any = {
        locale,
        outputContentFormat: 'markdown', // Request markdown format
        ...(features && features.length > 0 && { features }),
      };

      // Add figures output if premium mode
      if (premiumMode) {
        queryParameters.output = ['figures'];
      }

      // Start analysis with markdown output
      this.logger.log(`Starting document analysis with model: ${modelId}...`);
      const initialResponse = await client
        .path('/documentModels/{modelId}:analyze', modelId)
        .post({
          contentType: 'application/json',
          body: {
            base64Source,
          },
          queryParameters,
        });

      // Check for errors
      if (isUnexpected(initialResponse)) {
        throw new Error(
          `Analysis failed: ${initialResponse.body.error?.message || 'Unknown error'}`
        );
      }

      this.logger.log('Document analysis started, polling for results...');

      // Use built-in poller for long-running operation
      const poller = getLongRunningPoller(client, initialResponse);
      const result = (await poller.pollUntilDone())
        .body as AnalyzeOperationOutput;

      // Check if analysis succeeded
      if (result.status !== 'succeeded') {
        throw new Error(`Analysis failed with status: ${result.status}`);
      }

      // Extract the markdown content
      if (!result.analyzeResult?.content) {
        throw new Error('No content returned from the analysis');
      }

      this.logger.log('Document analysis complete');

      let figures: ExtractedFigure[][] | undefined;

      // Extract and interpret figures if premium mode
      if (premiumMode) {
        this.logger.log('Premium mode: extracting and interpreting figures...');
        const extractedFigures = await this.extractFigures(
          client,
          initialResponse,
          modelId,
          workspaceId,
          documentId,
          userId
        );

        const pageCount = result.analyzeResult.pages?.length || 1;
        figures = this.groupFiguresByPage(extractedFigures, pageCount);

        this.logger.log(
          `Extracted ${extractedFigures.length} figures across ${pageCount} pages`
        );
      }

      // Return in the same format as before, but with optional figures
      return {
        text: result.analyzeResult.content,
        metadata: {
          source: filePath,
          contentFormat: result.analyzeResult.contentFormat || 'markdown',
          pageCount: result.analyzeResult.pages?.length,
          apiVersion: result.analyzeResult.apiVersion,
        },
        ...(figures && { figures }),
      };
    } catch (error) {
      this.logger.error('Error while parsing document:', error);

      if (error instanceof Error) {
        throw new Error(`Document analysis failed: ${error.message}`);
      }

      throw error;
    }
  }

  private async interpretFigureWithLLM(
    imageData: Uint8Array,
    figureId: string,
    userId: string,
    workspaceId: string,
    documentId: string
  ): Promise<FigureInterpretation> {
    try {
      // Convert image data to base64
      const base64Image = Buffer.from(imageData).toString('base64');

      const messages = [
        {
          role: 'system' as const,
          content: `You are an expert at analyzing images and extracting meaningful information. Your task is to interpret the given image and provide a structured JSON response about its content.

Guidelines:
1. If the image contains valid information (charts, diagrams, tables, logo, signature, meaningful text), provide a detailed interpretation
2. If the image is decorative or contains no meaningful information, mark it as invalid
3. Always respond with valid JSON following the exact schema provided

Response JSON schema:
{
  "valid": boolean,
  "type": "chart" | "diagram" | "table" | "image" | "logo" | "signature" | "decoration" | "other",
  "description": string (optional - only if valid),
  "data": object (optional - structured data if extractable),
  "insights": string[] (optional - key insights if valid),
  "error": string (optional - only if there's an issue)
}`,
        },
        {
          role: 'user' as const,
          content: [
            {
              type: 'text' as const,
              text: `Please analyze this image (ID: ${figureId}) and provide a JSON interpretation following the schema. Focus on extracting meaningful business information, data trends, or contextual explanations.`,
            },
            {
              type: 'image_url' as const,
              image_url: {
                url: `data:image/png;base64,${base64Image}`,
                detail: 'high' as const,
              },
            },
          ],
        },
      ];

      const response = await this.llmRateLimiterService.handleRequest({
        model: LLM_MODELS['gpt-4o'],
        messages,
        temperature: 0.1,
        json: true,
      });

      return response.response as FigureInterpretation;
    } catch (error) {
      this.logger.error(`Error interpreting figure ${figureId}:`, error);
      return {
        valid: false,
        type: 'other',
        error:
          error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  private async extractFigures(
    client: any,
    initialResponse: any,
    modelId: string,
    workspaceId: string,
    documentId: string,
    userId: string
  ): Promise<ExtractedFigure[]> {
    try {
      const resultId = parseResultIdFromResponse(initialResponse);
      const poller = getLongRunningPoller(client, initialResponse);
      const result = (await poller.pollUntilDone())
        .body as AnalyzeOperationOutput;

      if (
        !result.analyzeResult?.figures ||
        result.analyzeResult.figures.length === 0
      ) {
        this.logger.log('No figures found in document');
        return [];
      }

      this.logger.log(
        `Found ${result.analyzeResult.figures.length} figures, processing with GPT-4o...`
      );
      const extractedFigures: ExtractedFigure[] = [];

      // Create directory structure for saving images if workspace and document IDs are provided
      let imageDir: string | undefined;
      if (workspaceId && documentId) {
        imageDir = path.join(
          process.cwd(),
          'user-uploads',
          workspaceId,
          documentId,
          'figures'
        );

        try {
          // Ensure directory exists
          fs.mkdirSync(imageDir, { recursive: true });
          this.logger.log(`Created image directory: ${imageDir}`);
        } catch (error) {
          this.logger.error(
            `Failed to create image directory: ${imageDir}`,
            error
          );
          imageDir = undefined; // Disable image saving if directory creation fails
        }
      }

      // Process figures in batches of up to 10 concurrent operations
      const figurePromises = result.analyzeResult.figures.map((figure) =>
        this.figureProcessingLimit(
          async (): Promise<ExtractedFigure | null> => {
            try {
              // Get figure image data
              const figureResponse = await client
                .path(
                  '/documentModels/{modelId}/analyzeResults/{resultId}/figures/{figureId}',
                  modelId,
                  resultId,
                  figure.id
                )
                .get()
                .asNodeStream();

              if (figureResponse.status !== '200' || !figureResponse.body) {
                this.logger.warn(`Failed to retrieve figure ${figure.id}`);
                return null;
              }

              const imageData = await streamToUint8Array(figureResponse.body);

              // Save image to disk if directory is available
              let imagePath: string | undefined;
              if (imageDir) {
                const imageFileName = `${figure.id}.png`;
                imagePath = path.join(imageDir, imageFileName);
                fs.writeFileSync(imagePath, imageData);
                this.logger.log(`Saved figure image: ${imagePath}`);
              }

              // Interpret figure with GPT-4o
              const interpretation = await this.interpretFigureWithLLM(
                imageData,
                figure.id,
                userId,
                workspaceId,
                documentId
              );

              const boundingRegion = figure.boundingRegions?.[0];

              const extractedFigure: ExtractedFigure = {
                id: figure.id,
                pageNumber: boundingRegion?.pageNumber || 1,
                interpretation,
                imagePath,
                boundingBox: boundingRegion
                  ? {
                      top: (boundingRegion.polygon as any)[0]?.y || 0,
                      left: (boundingRegion.polygon as any)[0]?.x || 0,
                      width: Math.abs(
                        ((boundingRegion.polygon as any)[2]?.x || 0) -
                          ((boundingRegion.polygon as any)[0]?.x || 0)
                      ),
                      height: Math.abs(
                        ((boundingRegion.polygon as any)[2]?.y || 0) -
                          ((boundingRegion.polygon as any)[0]?.y || 0)
                      ),
                    }
                  : undefined,
              };

              this.logger.log(
                `Processed figure ${figure.id} on page ${extractedFigure.pageNumber}`
              );

              return extractedFigure;
            } catch (error) {
              this.logger.error(`Error processing figure ${figure.id}:`, error);
              return null;
            }
          }
        )
      );

      // Wait for all figure processing to complete
      const figureResults = await Promise.allSettled(figurePromises);

      // Collect successful results and log failures
      const successfulResults = figureResults
        .filter(
          (result): result is PromiseFulfilledResult<ExtractedFigure | null> =>
            result.status === 'fulfilled' && result.value !== null
        )
        .map((result) => result.value as ExtractedFigure);

      const failedResults = figureResults.filter(
        (result) => result.status === 'rejected'
      );

      if (failedResults.length > 0) {
        this.logger.warn(
          `Failed to process ${failedResults.length} figures out of ${result.analyzeResult.figures.length}`
        );
      }

      extractedFigures.push(...successfulResults);

      return extractedFigures;
    } catch (error) {
      this.logger.error('Error extracting figures:', error);
      return [];
    }
  }

  private groupFiguresByPage(
    figures: ExtractedFigure[],
    pageCount: number
  ): ExtractedFigure[][] {
    const figuresByPage: ExtractedFigure[][] = Array.from(
      { length: pageCount },
      () => []
    );

    figures.forEach((figure) => {
      const pageIndex = Math.max(
        0,
        Math.min(figure.pageNumber - 1, pageCount - 1)
      );
      figuresByPage[pageIndex].push(figure);
    });

    return figuresByPage;
  }
}
