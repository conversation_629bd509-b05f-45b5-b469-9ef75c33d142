import DocumentIntelligence from '@azure-rest/ai-document-intelligence';
import { DefaultAzureCredential } from '@azure/identity';
import {
  getLongRunning<PERSON>oller,
  AnalyzeOperationOutput,
  isUnexpected,
} from '@azure-rest/ai-document-intelligence';
import * as fs from 'fs';

/**
 * Parse a document using Azure Document Intelligence API
 *
 * @param filePath Local file path to be uploaded
 * @param modelId The model to use for analysis (default: "prebuilt-layout")
 * @param locale The locale to use for analysis (optional)
 * @param features Additional features to enable (optional)
 * @returns object containing the parsed text and metadata
 */
export async function parseDocumentWithAzureDocumentIntelligence({
  filePath,
  modelId = 'prebuilt-layout',
  locale = 'en-US',
  features = [],
}: {
  filePath: string;
  modelId?: string;
  locale?: string;
  features?: string[];
}): Promise<{
  text: string;
  metadata: {
    source: string;
    contentFormat: string;
    pageCount?: number;
    apiVersion?: string;
  };
}> {
  // Check environment variables
  const endpoint = process.env.DOCUMENT_INTELLIGENCE_ENDPOINT;
  const apiKey = process.env.DOCUMENT_INTELLIGENCE_API_KEY;

  if (!endpoint) {
    throw new Error(
      'DOCUMENT_INTELLIGENCE_ENDPOINT environment variable is not set'
    );
  }

  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    // Initialize client
    console.log('Initializing Azure Document Intelligence client...');
    const client = apiKey
      ? DocumentIntelligence(endpoint, { key: apiKey })
      : DocumentIntelligence(endpoint, new DefaultAzureCredential());

    // Read file and convert to base64
    console.log('Reading file and converting to base64...');
    const base64Source = fs.readFileSync(filePath, { encoding: 'base64' });

    // Start analysis with markdown output
    console.log(`Starting document analysis with model: ${modelId}...`);
    const initialResponse = await client
      .path('/documentModels/{modelId}:analyze', modelId)
      .post({
        contentType: 'application/json',
        body: {
          base64Source,
        },
        queryParameters: {
          locale,
          outputContentFormat: 'markdown', // Request markdown format
          ...(features.length > 0 && { features }),
        },
      });

    // Check for errors
    if (isUnexpected(initialResponse)) {
      console.log(initialResponse.body);
      throw new Error(
        `Analysis failed: ${initialResponse.body.error?.message || 'Unknown error'}`
      );
    }

    console.log('Document analysis started, polling for results...');

    // Use built-in poller for long-running operation
    const poller = getLongRunningPoller(client, initialResponse);
    const result = (await poller.pollUntilDone())
      .body as AnalyzeOperationOutput;

    // Check if analysis succeeded
    if (result.status !== 'succeeded') {
      throw new Error(`Analysis failed with status: ${result.status}`);
    }

    // Extract the markdown content
    if (!result.analyzeResult?.content) {
      throw new Error('No content returned from the analysis');
    }

    console.log('Document analysis complete');

    // Return in the same format as llamaparse
    return {
      text: result.analyzeResult.content,
      metadata: {
        source: filePath,
        contentFormat: result.analyzeResult.contentFormat || 'markdown',
        pageCount: result.analyzeResult.pages?.length,
        apiVersion: result.analyzeResult.apiVersion,
      },
    };
  } catch (error) {
    console.error('Error while parsing document:', error);

    if (error instanceof Error) {
      throw new Error(`Document analysis failed: ${error.message}`);
    }

    throw error;
  }
}
