import { useState } from 'react';
import { NavigateFunction, useNavigate } from 'react-router-dom';

import { ChatHistory, ChatMessage } from '../models/chat.models.ts';

import { useHistories } from '@/api/chats/histories.state.ts';
import { API_URL } from '@/api/apiConstants.ts';
import { handle401, validateSystemPrompt } from '@/lib/utils.ts';
import {
  MixpanelEvents,
  MixpanelService,
} from '@/services/mixpanel.service.ts';
import { fetchHistory } from '@/api/chats/chats.api.ts';
import { toast } from '@/components/ui/use-toast.ts';

export const getGPTStream = ({
  messages,
  userPrompt,
  messageReceived,
  messageFinished,
  historyId,
  navigate,
  onError,
}: {
  messages: ChatMessage[];
  userPrompt: string;
  historyId: string;
  messageReceived: (message: string) => void;
  messageFinished: (message: string) => void;
  navigate: NavigateFunction;
  onError: () => void;
}): void => {
  const internalProcess = validateSystemPrompt(userPrompt);

  fetch(`${API_URL}/chats/send-message`, {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      historyId,
      messages: internalProcess
        ? messages
        : [...messages, { role: 'user', content: userPrompt }],
      internalProcessRequest: internalProcess,
    }),
  })
    .then((response) => {
      if (!response.ok && response.status === 401) {
        handle401(navigate, '/login');
      }
      if (!response.ok) {
        throw Error(response.statusText);
      }

      const stream = response.body;
      if (stream == null) {
        throw Error('Stream not available in axios response');
      }
      const reader = stream.getReader();
      let fullMessage = '';

      const readChunk = () => {
        reader
          .read()
          .then(({ value, done }) => {
            if (done) {
              reader.cancel();
              messageFinished(fullMessage);
              return;
            }
            const chunkString = new TextDecoder().decode(value);
            messageReceived(chunkString);
            fullMessage += chunkString;
            readChunk();
          })
          .catch((error) => {
            console.error(error);
            onError();
          });
      };
      readChunk();
    })
    .catch((error) => {
      console.error(error);
      onError();
    });
};

const useGPTHistory = (historyId: ChatHistory['id']) => {
  const [history, setHistory] = useState<ChatHistory | undefined>(undefined);

  const refetchHistory = async () => {
    const history = await fetchHistory(historyId);
    setHistory(history);
  };

  const addMessage = (newMessage: ChatMessage) => {
    setHistory((previousHistory) => {
      if (previousHistory === undefined) {
        return undefined;
      }
      return {
        ...previousHistory,
        messages: [...previousHistory.messages, newMessage],
      };
    });
  };

  return {
    history,
    addMessage,
    refetchHistory,
  };
};

export const useChatGPT = (historyId: ChatHistory['id']) => {
  const [error, setError] = useState<string | null>(null);
  const [pendingMessage, setPendingMessage] = useState('');
  const { history, addMessage, refetchHistory } = useGPTHistory(historyId);
  const [isMessagePending, setIsMessagePending] = useState(false);
  const { refetchHistories } = useHistories();
  const navigate = useNavigate();

  const sendGPTMessage = async (input: string) => {
    MixpanelService.track(MixpanelEvents.messageSent());
    setIsMessagePending(true);

    try {
      const internalProcess = validateSystemPrompt(input);
      if (!internalProcess) {
        const userMessage: ChatMessage = {
          role: 'user',
          content: input,
        };
        addMessage(userMessage);
      }

      getGPTStream({
        historyId,
        messages: history?.messages ?? [],
        userPrompt: input,
        messageReceived: (message) => {
          setPendingMessage((prevMessage) => prevMessage + message);
        },
        messageFinished: (message) => {
          addMessage({ role: 'assistant', content: message });
          setPendingMessage('');
          setIsMessagePending(false);

          if (history?.title == null) {
            refetchHistories();
          }
          refetchHistory();
        },
        onError: () => {
          toast({
            variant: 'destructive',
            description:
              'Clima AI wird gerade von zu vielen Nutzern verwendet. Bitte probier es nochmal in ein paar Minuten.',
          });
          setPendingMessage('');
          setIsMessagePending(false);
        },
        navigate,
      });
    } catch (error: unknown) {
      toast({
        variant: 'destructive',
        description:
          'Clima AI wird gerade von zu vielen Nutzern verwendet. Bitte probier es nochmal in ein paar Minuten.',
      });
      setError(error as string);
      setIsMessagePending(false);
    }
  };

  return {
    messages: history?.messages ?? [],
    sendGPTMessage,
    error,
    pendingMessage,
    isMessagePending,
    refetchHistory,
  };
};
