import { useState } from 'react';

import { toast } from '../ui/use-toast';

import { DatePicker } from '@/components/dashboard/DateInput';

export function DueDatePicker({
  action,
  currentDueDate,
}: {
  action: (newDueDate: Date) => Promise<void>;
  currentDueDate?: Date | null;
}) {
  const [dueDate, updateDueDate] = useState<Date>(
    currentDueDate ? new Date(currentDueDate) : new Date()
  );

  async function handleChange(value: Date | null | undefined) {
    try {
      if (!value) {
        return;
      }

      await action(value);
      updateDueDate(value);
      toast({
        title: 'Due Date Updated',
        variant: 'success',
      });
    } catch (error) {
      toast({
        title: 'Error updating due date',
        variant: 'destructive',
      });
    }
  }

  return <DatePicker date={dueDate} setDate={handleChange} />;
}
