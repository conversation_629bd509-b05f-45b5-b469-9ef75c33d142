
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { FileUpload } from "@/components/ui/file-upload";
import { FileUploadProgress } from "@/components/ui/documents/FileUploadProgress";
import { toast } from "sonner";
import { fireConfetti } from "@/lib/confetti";
import { SharePointInput } from "@/components/ui/documents/SharePointInput";

interface DocumentUploadModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DocumentUploadModal({ open, onOpenChange }: DocumentUploadModalProps) {
  const [uploadingFiles, setUploadingFiles] = useState<File[]>([]);
  const [uploadComplete, setUploadComplete] = useState(false);
  const [sharepointLink, setSharepointLink] = useState('');
  const [isProcessingSharedFolder, setIsProcessingSharedFolder] = useState(false);
  const [sharedFolderProgress, setSharedFolderProgress] = useState(0);
  
  useEffect(() => {
    if (!open) {
      // Reset state when modal is closed
      setTimeout(() => {
        setUploadingFiles([]);
        setUploadComplete(false);
        setSharepointLink('');
        setIsProcessingSharedFolder(false);
        setSharedFolderProgress(0);
      }, 300);
    }
  }, [open]);
  
  const handleFileChange = (files: File[]) => {
    if (files.length > 0) {
      setUploadingFiles(prev => [...prev, ...files]);
      
      // Auto-start upload for all files
      files.forEach((file, index) => {
        simulateFileUpload(file, prev => prev.length + index);
      });
    }
  };
  
  const simulateFileUpload = (file: File, indexGetter: (prev: File[]) => number) => {
    // Simulate successful upload for all files
    const timer = setTimeout(() => {
      setUploadComplete(true);
      toast.success("Upload complete!", {
        description: "Your document has been successfully uploaded.",
        dismissible: true
      });
      fireConfetti();
      
      // Close modal after a short delay
      setTimeout(() => {
        onOpenChange(false);
      }, 1500);
    }, 3000);
    
    return () => clearTimeout(timer);
  };
  
  const handleCancelUpload = (index: number) => {
    setUploadingFiles(prev => prev.filter((_, i) => i !== index));
  };
  
  const handleCloseModal = () => {
    if (uploadingFiles.length > 0 && !uploadComplete && !isProcessingSharedFolder) {
      if (confirm("Are you sure you want to cancel your uploads?")) {
        onOpenChange(false);
      }
    } else {
      onOpenChange(false);
    }
  };
  
  const handleSharePointConnect = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (sharepointLink.trim()) {
      setIsProcessingSharedFolder(true);
      
      // Simulate progress for SharePoint/Google Drive connection
      const interval = setInterval(() => {
        setSharedFolderProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            
            // Show success message
            toast.success("Connected to shared folder", {
              description: "Documents from the shared folder are being processed.",
              dismissible: true
            });
            
            fireConfetti();
            
            // Close modal after success
            setTimeout(() => {
              onOpenChange(false);
            }, 1500);
            
            return 100;
          }
          return prev + 5;
        });
      }, 200);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={handleCloseModal}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-glacier-darkBlue">Upload Documents</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {!isProcessingSharedFolder && (
            <>
              <div className="border rounded-lg overflow-hidden">
                <FileUpload 
                  onChange={handleFileChange}
                  acceptedFileTypes={[".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"]}
                  acceptedFileTypesMessage="Only document files are allowed (.pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx)"
                />
              </div>
              
              <SharePointInput 
                value={sharepointLink}
                onChange={(e) => setSharepointLink(e.target.value)}
                onSubmit={handleSharePointConnect}
              />
            </>
          )}
          
          {uploadingFiles.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Uploading Documents</h3>
              <div className="space-y-3 max-h-[200px] overflow-y-auto border rounded-md p-3">
                {uploadingFiles.map((file, index) => (
                  <FileUploadProgress
                    key={`${file.name}-${index}`}
                    file={file}
                    onComplete={() => {}}
                    onCancel={() => handleCancelUpload(index)}
                    skipErrorSimulation={true}
                  />
                ))}
              </div>
            </div>
          )}
          
          {isProcessingSharedFolder && (
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Processing Shared Folder</h3>
              <div className="border rounded-md p-4">
                <div className="mb-2 flex justify-between">
                  <span className="text-sm">{sharepointLink}</span>
                  <span className="text-sm text-gray-500">{sharedFolderProgress}%</span>
                </div>
                <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-glacier-mint transition-all duration-300 ease-in-out" 
                    style={{ width: `${sharedFolderProgress}%` }}
                  ></div>
                </div>
                <p className="mt-2 text-xs text-gray-500">
                  Connecting to shared folder and processing documents...
                </p>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
