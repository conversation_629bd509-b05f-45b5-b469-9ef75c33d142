
import { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { BarChart3, FileText, Settings, ChevronLeft, ChevronRight, ChevronDown, Lightbulb, Award, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useAuthentication } from '@/api/authentication/authentication.query';

type NavItem = {
  label: string;
  href: string;
  icon: React.ElementType;
};

const navItems: NavItem[] = [
  {
    label: 'Projects',
    href: '/projects',
    icon: Award
  },
  {
    label: 'All Gaps',
    href: '/improvements',
    icon: Lightbulb
  },
  {
    label: 'Documents',
    href: '/documents',
    icon: FileText
  },
  // {
  //   label: 'Settings',
  //   href: '/settings',
  //   icon: Settings
  // }
];

export function SideNavigation() {
  const [collapsed, setCollapsed] = useState(false);
  const [upcomingProjects, setUpcomingProjects] = useState<any[]>([]);
  const location = useLocation();
  const navigate = useNavigate();
  const { logout, user } = useAuthentication();

  useEffect(() => {
    const savedProjects = localStorage.getItem('glacier-dashboard-projects');
    if (savedProjects) {
      try {
        const parsedProjects = JSON.parse(savedProjects);
        const projectsWithDates = parsedProjects.map((project: any) => ({
          ...project,
          deadline: project.deadline ? new Date(project.deadline) : undefined
        }));
        
        const now = new Date();
        const futureProjects = projectsWithDates.filter((project: any) => 
          project.deadline && new Date(project.deadline) > now
        );
        
        setUpcomingProjects(futureProjects);
      } catch (error) {
        console.error('Error parsing saved projects:', error);
      }
    }
  }, [location.pathname]);

  useEffect(() => {
    document.documentElement.style.setProperty('--sidebar-width', collapsed ? 'var(--sidebar-width-collapsed)' : '240px');
  }, [collapsed]);

  const handleProjectClick = (project: any) => {
    if (project.type === 'ecovadis') {
      navigate(`/ecovadis-project/${project.id}`);
    } else {
      navigate(`/projects/${project.id}/coming-soon`);
    }
  };

  const handleLogout = () => {
    // MixpanelService.track(MixpanelEvents.sidebarToggled());
    logout();
  };

  return (
    <div className={cn("fixed flex flex-col h-screen top-0 left-0 bg-[#143560] border-r border-glacier-darkBlueAlt transition-all duration-300 z-30", collapsed ? "w-[70px]" : "w-[240px]")}>
      <div className="flex items-center justify-between px-4 h-16">
        {collapsed ? (
          <div className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center">
            <img src="/lovable-uploads/65dc3e30-e51f-45a4-8cb9-88857953e986.png" alt="Glacier AI" className="w-6 h-6 object-contain" />
          </div>
        ) : (
          <img src="/lovable-uploads/65dc3e30-e51f-45a4-8cb9-88857953e986.png" alt="Glacier AI" className="h-6 object-contain" />
        )}
        
        <button 
          onClick={() => setCollapsed(!collapsed)} 
          className="text-gray-400 hover:text-white rounded-lg hover:bg-glacier-darkBlueAlt/50 p-1.5 transition-colors" 
          aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {collapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
        </button>
      </div>

      <div className="flex-1 overflow-y-auto">
        <nav className="px-4">
          <ul className="space-y-1 w-full">
            {navItems.map(item => {
              const isActive = location.pathname === item.href || 
                              (item.href !== '/' && location.pathname.startsWith(item.href));
              return (
                <li key={item.href} className="w-full">
                  <Link
                    to={item.href}
                    className={cn(
                      "flex items-center text-sm group rounded-lg w-full",
                      "transition-all duration-200 ease-in-out",
                      "hover:bg-glacier-mint/10 hover:text-white",
                      isActive ? "bg-glacier-mint/20 text-white" : "text-gray-300",
                      collapsed ? "justify-center py-3 px-2" : "py-2 px-3"
                    )}
                  >
                    <item.icon 
                      className={cn(
                        "flex-shrink-0 w-5 h-5",
                        isActive ? "text-glacier-mint" : "text-gray-400 group-hover:text-glacier-mint",
                        "transition-colors"
                      )} 
                    />
                    
                    {!collapsed && (
                      <span className="ml-3 truncate duration-200">{item.label}</span>
                    )}
                  </Link>
                  
                  {!collapsed && item.label === 'Projects' && upcomingProjects.length > 0 && (
                    <ul className="mt-1 pl-8 space-y-1">
                      {upcomingProjects.map((project: any) => (
                        <li key={project.id}>
                          <button
                            onClick={() => handleProjectClick(project)}
                            className="flex items-center text-sm rounded-lg py-2 px-2 w-full text-gray-300 hover:bg-glacier-mint/10 hover:text-white text-left"
                          >
                            {project.type === 'ecovadis' ? (
                              <Award className="w-4 h-4 text-gray-400 mr-2" />
                            ) : (
                              <AlertTriangle className="w-4 h-4 text-gray-400 mr-2" />
                            )}
                            <span className="truncate">{project.name}</span>
                          </button>
                        </li>
                      ))}
                    </ul>
                  )}
                </li>
              );
            })}
          </ul>
        </nav>
      </div>

      <div className="px-4 py-4 mt-auto border-t border-glacier-darkBlueAlt">
        {collapsed ? (
          <div className="flex justify-center">
            <Avatar className="h-10 w-10">
              <AvatarImage src="/placeholder.svg" alt="User" />
              <AvatarFallback className="bg-glacier-mint/20 text-white">
                {user?.email?.charAt(0).toUpperCase() || 'U'}
              </AvatarFallback>
            </Avatar>
          </div>
        ) : (
          <DropdownMenu>
            <DropdownMenuTrigger className="w-full flex items-center focus:outline-none">
              <Avatar className="h-10 w-10 mr-3">
                <AvatarImage src="/placeholder.svg" alt="User" />
                <AvatarFallback className="bg-glacier-mint/20 text-white">
                  {user?.email?.charAt(0).toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {user?.email?.split('@')[0] || 'User'}
                </p>
                <p className="text-xs text-gray-400 truncate">
                  {user?.email || '<EMAIL>'}
                </p>
              </div>
              <ChevronDown size={16} className="ml-2 text-gray-400" />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[216px] bg-glacier-darkBlue/90 backdrop-blur-sm border-glacier-darkBlueAlt shadow-lg">
              <DropdownMenuItem onClick={handleLogout} className="text-red-400 hover:bg-red-500/10 hover:text-red-300">
                <span>Logout</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  );
}
