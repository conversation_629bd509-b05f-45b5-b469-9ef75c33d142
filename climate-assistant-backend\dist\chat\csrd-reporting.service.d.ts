import { ChatMessage } from './entities/chat.message.entity';
import { CustomGptTool } from 'src/util/chat-gpt.models';
import { ChatMessageDto } from './entities/chat.message.dto';
export declare class CsrdReportingService {
    constructor();
    createInitiativeDetailTool(userId: string): CustomGptTool<void, string>;
    createDetailledESRSRequirementSet(previousMessages: ChatMessageDto[], userId: string): AsyncIterableIterator<string>;
    createCsrdReportingPrompt: (internalProcessRequest: {
        [key: string]: string;
    }) => Pick<ChatMessage, "role" | "content">;
}
