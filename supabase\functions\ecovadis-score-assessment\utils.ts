
// Input types
type DocumentChunk = {
    document?: {
        id?: string;
        name?: string;
    };
    page?: string;
    content?: string;
};

type LinkedDocumentChunk = {
    answerId: string;
    document_chunk?: DocumentChunk;
    comment?: string;
};

// Output types
type PageContent = {
    page: string;
    content: string;
};

type GroupedDocument = {
    answerId: string;
    documentId: string;
    documentName: string;
    comment: string; // concatenated comments
    pages: PageContent[];
};

// Transformation function
export function groupEvidenceDocuments(linkedDocumentChunks?: LinkedDocumentChunk[]): string[] {
    if (!linkedDocumentChunks || linkedDocumentChunks.length === 0) {
        return [];
    }

    // Group by documentId first
    const documentGroups = linkedDocumentChunks.reduce((acc, chunk) => {
        const documentId = chunk.document_chunk?.document?.id || 'Unknown';

        if (!acc[documentId]) {
            acc[documentId] = [];
        }
        acc[documentId].push(chunk);
        return acc;
    }, {} as Record<string, LinkedDocumentChunk[]>);

    // Transform each document group
    const groupedDocuments: GroupedDocument[] = Object.entries(documentGroups).map(([documentId, chunks]) => {
        // Get document metadata from first chunk (assuming same for all chunks of same document)
        const firstChunk = chunks[0];

        // Concatenate all comments for this document
        const comments = chunks
            .map(chunk => chunk.comment)
            .filter(comment => comment && comment.trim() !== '')
            .join(' | ');

        // Group pages/content
        const pages: PageContent[] = chunks.map(chunk => ({
            page: chunk.document_chunk?.page || '',
            content: chunk.document_chunk?.content || ''
        }));

        return {
            answerId: firstChunk.answerId,
            documentId,
            documentName: firstChunk.document_chunk?.document?.name || 'Unknown',
            comment: comments,
            pages
        };
    });

    // Convert to string format as requested
    return groupedDocuments.map(doc => {
        const pagesString = doc.pages
            .map(page => `        page: ${page.page},\n        content: ${page.content},`)
            .join('\n');

        return `
  answerId: ${doc.answerId},
  documentId: ${doc.documentId},
  documentName: ${doc.documentName},
  comment: ${doc.comment},
  ${pagesString}`;
    });
};
