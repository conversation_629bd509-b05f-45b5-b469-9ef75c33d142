import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { Comment } from 'src/project/entities/comment.entity';
import { DatapointDocumentChunk } from 'src/datapoint-document-chunk/entities/datapoint-document-chunk.entity';
import { DocumentChunk } from 'src/document/entities/document-chunk.entity';
import { Document } from 'src/document/entities/document.entity';

export class GenerateDatapointRequestTextPayload {
  additionalReportTextGenerationRules: string;
  useExistingReportText: boolean;
}

export class DatapointRequestData extends DatapointRequest {
  esrsDatapoint: ESRSDatapoint;
  comments: Comment[];
  datapointDocumentChunkMap: (DatapointDocumentChunk & {
    documentChunk: DocumentChunk & { document: Document };
  })[];
}

export type DatapointRequestWithDocumentCount = DatapointRequest & {
  documentChunkCount: number;
};
