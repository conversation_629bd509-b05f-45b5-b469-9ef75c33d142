import { useEffect, useMemo, useState } from 'react';
import {
  CircleCheckBigIcon,
  HistoryIcon,
  LoaderCircle,
  SaveIcon,
  WandSparklesIcon,
  EditIcon,
} from 'lucide-react';

import { Button } from '../ui/button';
import IconRenderer from '../ui/icons';
import { TipTapEditor } from '../ui/tiptap/tiptap-editor';
import ConfirmDialog from '../ConfirmDialog';
import { toast } from '../ui/use-toast';

import { GeneratedContent } from './GeneratedContent';
import { AiGenerateReportTextConfirmModal } from './AiGenerateDrConfirmModal';

import { DataRequestData, generationStatus } from '@/types/project';
import { useReportText } from '@/hooks/use-reporttext';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { formatDate } from '@/lib/utils';
import { DataRequestStatus } from '@/types';

export function ReportText({
  dataRequest,
  content,
  members,
}: {
  content: string;
  dataRequest: DataRequestData;
  members: { id: string; name: string }[];
}) {
  const {
    reportText,
    setReportText,
    handleApprove,
    handleSave,
    canReviewWithAi,
    handleReviewWithAi,
    canGenerateWithAi,
    handleGenerateWithAi,
    canApproveReportText,
    isLoadingGenerateWithAi,
    isLoadingReviewWithAi,
    confirmAiDialogOpen,
    setConfirmAiDialogOpen,
    dataRequestGeneration,
    updateDrGenerationStatus,
    unapproveDataRequest,
    getApproveReportTextTooltipContent,
    isDirty,
  } = useReportText({ content, dataRequest });
  const [isEditable, setIsEditable] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  useEffect(() => {
    setIsEditable(dataRequest.status !== DataRequestStatus.ApprovedAnswer);
  }, [dataRequest.status]);

  const {
    allow: userCanGenerateWithAi,
    tooltip: canGenerateWithAiTooltipText,
  } = canGenerateWithAi();
  const { allow: userCanReviewWithAi, tooltip: canReviewWithAiTooltipText } =
    canReviewWithAi();

  const approvedByMember = useMemo(() => {
    return members.find((member) => member.id === dataRequest.approvedBy);
  }, [members, dataRequest.approvedBy]);

  const handleEditClick = () => {
    setConfirmDialogOpen(true);
  };

  const handleConfirmEdit = async () => {
    try {
      await unapproveDataRequest();
      setIsEditable(true);
    } catch (e) {
      toast({
        variant: 'destructive',
        description: 'Unable to edit the text, please try again.',
      });
    } finally {
      setConfirmDialogOpen(false);
    }
  };

  return (
    <div className="my-3 space-y-3">
      <div className="relative">
        {!isEditable && (
          <div className="flex justify-between items-center">
            <div className="flex space-x-4">
              {approvedByMember && (
                <p>
                  Approved by: <span>{approvedByMember.name}</span>{' '}
                </p>
              )}
              {dataRequest.approvedAt && (
                <p>
                  Approved at: <span>{formatDate(dataRequest.approvedAt)}</span>{' '}
                </p>
              )}
            </div>
            <Button
              variant="outline"
              className="ml-2"
              onClick={handleEditClick}
            >
              <EditIcon className="h-4 w-4 mr-2" />
              Edit Text
            </Button>
          </div>
        )}
        <TipTapEditor
          content={reportText}
          setContent={setReportText}
          isEditable={isEditable}
        />
        {false && (
          <Button
            variant={'link'}
            className="p-0 absolute right-4 bottom-2 text-foreground text-sm font-normal underline"
          >
            <HistoryIcon className="h-4 w-4 mr-2" />
            Version History
          </Button>
        )}
      </div>

      {isEditable && (
        <div className="space-x-3">
          <Tooltip>
            <TooltipTrigger>
              <Button
                onClick={handleSave}
                variant={'default'}
                disabled={!isDirty}
              >
                <SaveIcon className="h-4 w-4 mr-2" />
                Save
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {isDirty ? 'Save changes' : 'No changes to save'}
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger>
              <Button
                variant={'outline'}
                disabled={!userCanGenerateWithAi || isLoadingGenerateWithAi}
                onClick={() =>
                  // Previously, the following condition was used to determine whether to open the dialog:
                  // reportText.trim() !== ''
                  //   ? setConfirmAiDialogOpen(true)
                  //   : handleGenerateWithAi()
                  setConfirmAiDialogOpen(true)
                }
                className="ml-2"
              >
                {isLoadingGenerateWithAi ? (
                  <LoaderCircle className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <IconRenderer
                    iconName={'HammerSparcle'}
                    className="h-4 w-4 mr-2"
                  />
                )}
                Generate with AI
              </Button>
            </TooltipTrigger>
            <TooltipContent>{canGenerateWithAiTooltipText}</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger>
              <Button
                variant="outline"
                className="ml-2"
                onClick={() => handleReviewWithAi()}
                disabled={!userCanReviewWithAi}
              >
                {isLoadingReviewWithAi ? (
                  <LoaderCircle className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <WandSparklesIcon className="h-4 w-4 mr-2" />
                )}
                Review with AI
              </Button>
            </TooltipTrigger>
            <TooltipContent>{canReviewWithAiTooltipText}</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger>
              <Button
                variant={'forest'}
                onClick={handleApprove}
                disabled={!canApproveReportText}
              >
                <CircleCheckBigIcon className="h-4 w-4 mr-2" />
                Approve Text
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {getApproveReportTextTooltipContent()}
            </TooltipContent>
          </Tooltip>
        </div>
      )}

      {dataRequestGeneration && (
        <GeneratedContent
          generationContents={dataRequestGeneration}
          handleApproveOrReject={(id: string, status: generationStatus) =>
            updateDrGenerationStatus(id, status)
          }
        />
      )}

      <AiGenerateReportTextConfirmModal
        open={confirmAiDialogOpen}
        setOpen={setConfirmAiDialogOpen}
        callback={handleGenerateWithAi}
        dataRequest={dataRequest}
      />

      <ConfirmDialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
        onConfirm={handleConfirmEdit}
        title="Edit Report Text"
        description="Are you sure you want to edit the report text?"
      />
    </div>
  );
}
