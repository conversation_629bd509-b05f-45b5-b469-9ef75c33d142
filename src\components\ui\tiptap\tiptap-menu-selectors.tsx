import { Editor } from '@tiptap/react';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';

const pastelColors = {
  slate: '#0f172a',
  red: '#b91c1c',
  green: '#16a34a',
  blue: '#0284c7',
  yellow: '#eab308',
  purple: '#4338ca',
};

export const TipTapColorPicker = ({ editor }: { editor: Editor }) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className="w-8 h-8 rounded-md p-0 aspect-square"
          style={{
            backgroundColor:
              editor.getAttributes('textStyle').color || '#0f172a',
          }}
        />
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-auto p-1 flex">
        {Object.values(pastelColors).map((colorValue) => (
          <DropdownMenuItem
            key={colorValue}
            onSelect={() => {
              editor.chain().focus().setColor(colorValue).run();
            }}
            className="rounded-md w-8 h-8 m-1 p-0 border-0"
            style={{ backgroundColor: colorValue }}
          />
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const TipTapTextFormatPicker = ({ editor }: { editor: Editor }) => {
  const getTextFormat = () => {
    if (editor.isActive('heading', { level: 1 })) {
      return '1';
    } else if (editor.isActive('heading', { level: 2 })) {
      return '2';
    } else if (editor.isActive('heading', { level: 3 })) {
      return '3';
    } else {
      return 'paragraph';
    }
  };
  return (
    <Select
      onValueChange={(value) => {
        if (value === 'paragraph') {
          editor.chain().focus().setParagraph().run();
        } else {
          editor
            .chain()
            .focus()
            .toggleHeading({ level: parseInt(value) as 1 | 2 | 3 })
            .run();
        }
      }}
      value={getTextFormat()}
    >
      <SelectTrigger className="w-36 h-8">
        <SelectValue placeholder="Paragraph" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="1">Heading 1</SelectItem>
        <SelectItem value="2">Heading 2</SelectItem>
        <SelectItem value="3">Heading 3</SelectItem>
        <SelectItem value="paragraph">Paragraph</SelectItem>
      </SelectContent>
    </Select>
  );
};
