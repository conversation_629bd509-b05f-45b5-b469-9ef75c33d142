
import React from 'react';
import { FileText, ClipboardList, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import CreateProjectDialog from './CreateProjectDialog';
import { useUserRole } from '@/hooks/useUserRole';
import { canCreateProject } from '@/utils/userWorkspaceUtils';

interface EmptyProjectStateProps {
  onProjectCreated: (data: any) => void;
}

export function EmptyProjectState({ onProjectCreated }: EmptyProjectStateProps) {
  const { getUserRole } = useUserRole();
  const userRole = getUserRole();
  const canCreate = canCreateProject(userRole);

  return (
    <div className="flex flex-col items-center justify-center min-h-[80vh] text-center p-6">
      <div className="flex justify-center space-x-4 mb-4">
        <div className="rounded-full bg-blue-100 p-3">
          <FileText size={24} className="text-blue-600" />
        </div>
        <div className="rounded-full bg-green-100 p-3">
          <ClipboardList size={24} className="text-green-600" />
        </div>
      </div>
      
      <h2 className="text-2xl font-semibold mb-2">No Projects Yet</h2>
      <p className="text-gray-600 mb-8 max-w-md">
        {canCreate 
          ? "Create your first sustainability project to begin tracking and improving your performance."
          : "No projects are available. Contact your administrator to create projects."
        }
      </p>
      
      {canCreate && (
        <CreateProjectDialog
          onProjectCreated={onProjectCreated}
          trigger={
            <Button className="flex items-center">
              <Upload className="mr-2 h-5 w-5" />
              Upload EcoVadis Questionnaire
            </Button>
          }
        />
      )}
    </div>
  );
}
