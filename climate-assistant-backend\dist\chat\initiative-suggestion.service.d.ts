import { ChatGptService } from '../llm/chat-gpt.service';
import { CustomGptTool } from '../util/chat-gpt.models';
import { ChatMessageDto } from './entities/chat.message.dto';
import { UsersService } from '../users/users.service';
import { MultiQuestionSearchEngine } from '../util/multi-question-search-engine.service';
import { KnowledgeBaseService } from '../knowledge-base/knowledge-base.service';
import { LLM_MODELS } from 'src/constants';
interface KeyInformation {
    budget: string;
    timeframe: string;
    focus_topics: string[];
    location: string;
}
export declare class InitiativeSuggestionService {
    private readonly chatGptService;
    private readonly usersService;
    private readonly searchEngineTool;
    private readonly knowledgeBaseService;
    constructor(chatGptService: ChatGptService, usersService: UsersService, searchEngineTool: MultiQuestionSearchEngine, knowledgeBaseService: KnowledgeBaseService);
    readonly gptModel = LLM_MODELS['gpt-4o'];
    createInitiativeSuggestionCreator(userId: string): CustomGptTool<void, string>;
    createInitiativeSuggestions(previousMessages: ChatMessageDto[], userId: string): AsyncIterableIterator<string>;
    generateInitiativeSuggestionsStream(context: string, ideas: string[], questionAnswerPairs: string, knowledgeBaseChunks: string[], keyInformation: KeyInformation, previousMessages: ChatMessageDto[]): Promise<AsyncIterableIterator<string>>;
    generateIdeas(context: string, questionAnswerPairs: string, knowledgeBaseChunks: string[], keyInformation: KeyInformation): Promise<string[]>;
    extractSearchEngineQuestions(context: string, previousMessages: ChatMessageDto[], keyInformation: KeyInformation): Promise<string>;
    getKnowledgebaseAnswers(context: string, previousMessages: ChatMessageDto[], keyInformation: KeyInformation): Promise<string[]>;
    extractKeyInformation(context: string, previousMessages: ChatMessageDto[]): Promise<KeyInformation>;
}
export {};
