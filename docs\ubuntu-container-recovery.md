# Docker Recovery Guide

This guide covers the necessary steps to stop, reset, and restart Docker, and verify that everything is back in working order.

## Stage 1: Restart Docker and Containerd Services

Restart Docker services.

```bash
sudo service docker restart
```

## Stage 2: Stop and Start Docker and Containerd Services

Stop Docker and containerd services to ensure that all containers and Docker-related processes are halted and restart Docker and containerd services to refresh their states.

```bash
sudo service docker stop        # Stop the Docker service
sudo service containerd stop    # Stop the Docker service

sudo service docker start       # Start the Docker service
sudo service containerd start   # Start the containerd service
```

## Stage 3: Stop Docker and Related Services Completely

If still facing issue. This step ensures that Docker and any related sockets are fully stopped, clearing up resources.

### Step 1: Stop Docker Socket and Docker

```bash
sudo systemctl stop docker.socket   # Stop the Docker socket, which listens for Docker commands
sudo systemctl stop docker          # Stop Docker service to ensure no lingering processes
sudo systemctl disable docker       # Disable Docker service from restarting on boot
sudo systemctl disable containerd   # Disable containerd service from restarting on boot
```

### Step 2: Remove All Docker Containers

Force remove all containers. This clears out any active containers, whether they are running or stopped.

```bash
docker rm -f $(docker ps -a -q)     # List and force-remove all containers by ID
```

### Step 3: Kill Docker and Containerd Processes (If Still Running)

Sometimes Docker processes persist even after stopping the services. The following commands kill any remaining Docker and containerd processes.

```bash
sudo pkill -f docker         # Forcefully kill any remaining Docker processes
sudo pkill -f containerd     # Forcefully kill any remaining containerd processes
```

### Step 4: Remove All Docker Containers

Reboot the server.

```bash
sudo reboot     # Restart the whole linux server
```
```bash
ssh root@95.217.183.99      # Reconnect to the server
```

### Step 5: Verify Processes and Ports

Check to ensure no Docker-related processes are running and list all active network ports.

```bash
ps aux | grep docker                  # List any processes related to Docker
sudo lsof -i -P -n | grep LISTEN      # List all active network ports in LISTEN state
```

### Step 6: Kill Specific Processes (If Necessary)

If any specific Docker processes remain, kill them using their process IDs.

```bash
sudo kill -9 1664 1671                # Force-kill specific processes by PID (example PIDs shown)
```

### Step 7: Enable and Start Docker Services

Enable Docker and containerd services to start automatically on boot and then start them.

```bash
sudo systemctl enable docker          # Enable Docker service to start on boot
sudo systemctl enable containerd      # Enable containerd service to start on boot
sudo systemctl start docker           # Start Docker service manually
```

### Step 8: Verify Docker is Running and Empty

List all Docker containers to ensure Docker is running and has no active containers.

```bash
docker ps -a                          # Show all containers (should be empty if reset correctly)
```

### Step 9: Start Backend and Frontend Services with Docker Compose

Use Docker Compose to bring up backend and frontend services, running them in detached mode.

```bash
docker compose up -d backend frontend
```

### Step 10: Start Nginx

Use Docker Compose to bring up Nginx, running in detached mode.

```bash
docker compose up -d nginx
```
