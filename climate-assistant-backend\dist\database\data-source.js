"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv = require("dotenv");
const env_helper_1 = require("../env-helper");
dotenv.config({ path: '../.env' });
exports.default = (0, env_helper_1.createDataSourceWithVectorSupport)({
    type: 'postgres',
    url: process.env.BACKEND_DB_URL,
    synchronize: false,
    dropSchema: false,
    logging: false,
    logger: 'file',
    entities: ['src/**/*.entity{.ts,.js}'],
    migrations: ['src/database/migrations/**/*.ts'],
    migrationsTableName: 'migration_table',
});
//# sourceMappingURL=data-source.js.map