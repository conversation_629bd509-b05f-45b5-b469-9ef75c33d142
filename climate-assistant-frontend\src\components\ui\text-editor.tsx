import React, { useRef } from 'react';
import ReactQuill from 'react-quill-new';
import 'react-quill-new/dist/quill.snow.css';

interface TextEditorProps {
  content: string;
  setContent: (data: string) => void;
}

export const TextEditor: React.FC<TextEditorProps> = ({
  content,
  setContent,
}) => {
  const quillRef = useRef<ReactQuill>(null);

  const handleChange = (html: string) => {
    setContent(html);
  };

  // const imageHandler = () => {
  //   const input = document.createElement('input');
  //   input.setAttribute('type', 'file');
  //   input.setAttribute('accept', 'image/*');
  //   input.click();

  //   input.onchange = async () => {
  //     const file = input.files![0];

  //     const formData = new FormData();
  //     formData.append('image', file);

  //     const response = await fetch('upload-url', {
  //       method: 'POST',
  //       body: formData,
  //     });

  //     const result = await response.json();
  //     const imageUrl = result.url;

  //     const quill = quillRef.current?.getEditor();
  //     const range = quill?.getSelection();
  //     if (range && quill) {
  //       quill.insertEmbed(range.index, 'image', imageUrl);
  //     }
  //   };
  // };

  const modules = {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike', 'blockquote'],
      [
        { list: 'ordered' },
        { list: 'bullet' },
        { indent: '-1' },
        { indent: '+1' },
      ],
      ['link'],
      ['clean'],
      [{ header: [1, 2, 3, 4, false] }],
    ],
    clipboard: {
      matchVisual: false,
    },
  };

  const formats = [
    'header',
    //'font',
    'size',
    'bold',
    'italic',
    'underline',
    'strike',
    'blockquote',
    'list',
    //'bullet',
    'indent',
    'link',
    //'image',
    //'video',
  ];

  return (
    <div>
      <ReactQuill
        ref={quillRef}
        onChange={handleChange}
        value={content}
        modules={modules}
        formats={formats}
        bounds=".app"
      />
    </div>
  );
};
