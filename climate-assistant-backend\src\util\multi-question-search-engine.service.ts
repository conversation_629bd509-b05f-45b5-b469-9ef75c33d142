import { Injectable } from '@nestjs/common';
import { ChatMessageDto } from '../chat/entities/chat.message.dto';
import { CustomGptTool } from './chat-gpt.models';
import { PerplexityService } from './perplexity.service';

@Injectable()
export class MultiQuestionSearchEngine {
  constructor(private readonly perplexityService: PerplexityService) {}

  readonly toolDefinition: CustomGptTool<{ questions: string[] }, string> = {
    type: 'async-gpt-tool',
    toolDefinition: {
      type: 'function',
      function: {
        name: 'public-search-engine',
        description:
          'searches through the public web to find information about a company',
        parameters: {
          type: 'object',
          properties: {
            questions: {
              type: 'array',
              items: {
                type: 'string',
              },
              description:
                'The questions you want to ask the public search engine',
            },
          },
          require: ['questions'],
        },
      },
    },
    execute: (payload) => this.searchMultipleQuestions(payload),
  };

  async searchMultipleQuestions({
    questions,
  }: {
    questions: string[];
  }): Promise<string> {
    console.log(
      '[Multi-Question-Search-Engine] Received questions: ',
      questions,
    );

    if (!questions) {
      return 'No questions provided';
    }

    const promptCreator = (question): ChatMessageDto[] => [
      {
        role: 'system',
        content: ` 
          Du bist eine Suchmaschine die kurz und präzise auf Fragen antwortet.
       `,
      },
      { role: 'user', content: question },
    ];

    const results = await Promise.all(
      questions.map(async (question) => {
        const result = await this.perplexityService.createCompletion(
          'llama-3-sonar-small-32k-online',
          promptCreator(question),
        );

        return `
        Question: ${question}
        Answer: ${result}
        `;
      }),
    );

    return results.join(',');
  }
}
