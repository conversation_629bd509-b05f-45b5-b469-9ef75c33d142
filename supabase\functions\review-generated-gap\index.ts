// @ts-expect-error Deno serve import
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { authValidator } from "../_shared/authValidator.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { USER_ROLE } from "../_shared/constants.ts";
import { storeLLMGapAnalysisResponse } from "../ecovadis-ga-collector/process.ts";

type ReviewAction = 'approve' | 'disapprove' | 'regenerate';

interface ReviewRequest {
  gapId: string;
  action: ReviewAction;
  feedback?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }

  try {
    const { user, error, supabaseClient, response: errResponse } = await authValidator(req);
    
    if (!user || error || !supabaseClient) {
      return errResponse;
    }

    // Check if user is Super Admin
    const userRole = user.user_workspace?.[0]?.role;
    if (userRole !== USER_ROLE.SuperAdmin) {
      return new Response(JSON.stringify({
        error: 'Unauthorized. Only Super Admins can review generated gaps.'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 403
      });
    }

    // Parse request body
    const { gapId, action, feedback }: ReviewRequest = await req.json();

    if (!gapId || !action) {
      return new Response(JSON.stringify({
        error: 'Gap ID and action are required'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }

    // Validate action
    if (!['approve', 'disapprove', 'regenerate'].includes(action)) {
      return new Response(JSON.stringify({
        error: 'Invalid action. Must be approve, disapprove, or regenerate'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }

    // Validate feedback for disapprove and regenerate actions
    if ((action === 'disapprove' || action === 'regenerate') && !feedback?.trim()) {
      return new Response(JSON.stringify({
        error: `Feedback is required for ${action} action`
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }

    // Get the generated gap first
    const { data: generatedGap, error: fetchError } = await supabaseClient
      .from('project_ecovadis_gap_generation')
      .select('*')
      .eq('id', gapId)
      .single();

    if (fetchError || !generatedGap) {
      return new Response(JSON.stringify({
        error: 'Generated gap not found'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 404
      });
    }

    // Check if gap is in pending_review status
    if (generatedGap.status !== 'pending_review') {
      return new Response(JSON.stringify({
        error: 'Gap is not pending review'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }

    let newStatus: string;
    let shouldCopyToProjectGaps = false;

    switch (action) {
      case 'approve':
        newStatus = 'approved';
        shouldCopyToProjectGaps = true;
        break;
      case 'disapprove':
        newStatus = 'rejected';
        break;
      case 'regenerate':
        newStatus = 'regenerating';
        break;
      default:
        throw new Error('Invalid action');
    }

    // Update the generated gap status
    const { error: updateError } = await supabaseClient
      .from('project_ecovadis_gap_generation')
      .update({
        status: newStatus,
        feedback: feedback || null,
        reviewed_by: user.id,
        reviewed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', gapId);

    if (updateError) {
      console.error('Error updating generated gap:', updateError);
      return new Response(JSON.stringify({
        error: 'Failed to update generated gap status'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }

    // If approved, copy to project_ecovadis_gaps table
    if (shouldCopyToProjectGaps) {
      // Transform the individual gap data into the expected format for project_ecovadis_gaps
      const gapData = generatedGap.generated_content;
      storeLLMGapAnalysisResponse({
        supabaseClient,
        projectId: generatedGap.project_id,
        questionId: generatedGap.question_id,
        projectQuestionId: generatedGap.id, 
        gapsListed: [gapData]
      }).catch(async err => {
        await supabaseClient
          .from('project_ecovadis_gap_generation')
          .update({
            status: 'pending_review',
            feedback: null,
            reviewed_by: null,
            reviewed_at: null
          })
          .eq('id', gapId);

        return new Response(JSON.stringify({
          error: 'Failed to approve gap - could not copy to project gaps'
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          status: 500
        });
      })
        
    }

    // If regenerate, trigger the regeneration process
    if (action === 'regenerate') {
      // TODO: Trigger AI regeneration with feedback
      // This would typically call the gap analysis function with the additional feedback
      // For now, we'll just mark it as regenerating and let the frontend handle the regeneration
    }

    return new Response(JSON.stringify({
      success: true,
      message: `Gap ${action}d successfully`,
      status: newStatus
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 200
    });

  } catch (error) {
    console.error('Error in review-generated-gap function:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});
