import { Link2Icon } from 'lucide-react';
import { DialogDescription } from '@radix-ui/react-dialog';

import { Badge } from '../ui/badge';

import { DatapointLinksTable } from './DatapointLinksTable';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { DatapointRequestLinkage } from '@/types/document';

export function DatapointLinks({
  documentChunkId,
  datapointRequests,
  mutate,
}: {
  documentChunkId: string;
  datapointRequests: DatapointRequestLinkage[];
  mutate: () => void;
}) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Badge
          className="rounded-md font-normal bg-glacier-bluedark text-white hover:bg-glacier-blueDark/90"
          size="sm"
          variant={'secondary'}
          onClick={(e) => e.stopPropagation()}
        >
          <Link2Icon className="h-3 w-3 mr-1" />
          {datapointRequests.filter((d) => d.linked).length} Datapoint Links
        </Badge>
      </DialogTrigger>
      <DialogContent
        className="max-w-[80%] text-slate-900"
        onClick={(e) => e.stopPropagation()}
      >
        <DialogHeader>
          <DialogTitle>Change linked Datapoints</DialogTitle>
        </DialogHeader>
        <DialogDescription /> {/* required for render */}
        <DatapointLinksTable
          documentChunkId={documentChunkId}
          datapointRequests={datapointRequests}
          mutate={mutate}
        />
      </DialogContent>
    </Dialog>
  );
}
