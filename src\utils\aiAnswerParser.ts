
export interface AiAnswerOption {
  id: string;
  answer_name: string;
}

export interface DocumentChunk {
  document_chunk_id: string;
}

export interface DocumentContent {
  id: string;
  documentId: string;
  page: string;
  content: string;
  matchingsJson: any;
  metadataJson: string;
  createdAt: string;
}

export interface AiAnswerItem {
  answer_option: AiAnswerOption;
  document_chunks: DocumentChunk[];
  comment: string;
  contents: DocumentContent[];
}

export interface AiAnswerResponse {
  llmout: AiAnswerItem[];
  projectId: string;
  questionId: string;
  workspaceId: string;
}

export interface ParsedAttachment {
  optionId: string;
  documentId: string;
  pages: string[];
  comment: string;
  chunkIds: string[];
}

export const parseAiAnswerResponse = (response: any): ParsedAttachment[] => {
  console.log('Parsing AI answer response:', response);
  
  if (!response?.result?.llmout || !Array.isArray(response.result.llmout)) {
    console.error('Invalid AI response format - missing llmout array');
    return [];
  }

  const attachments: ParsedAttachment[] = [];

  for (const item of response.result.llmout) {
    if (!item.answer_option?.id || !item.document_chunks || !Array.isArray(item.document_chunks)) {
      console.warn('Skipping invalid AI answer item:', item);
      continue;
    }

    // Group document chunks by documentId
    const documentGroups: { [documentId: string]: { pages: string[], chunkIds: string[] } } = {};

    for (const chunk of item.document_chunks) {
      if (!chunk.document_chunk_id) continue;

      // Find the corresponding content to get documentId and page
      const content = item.contents?.find(c => c.id === chunk.document_chunk_id);
      if (!content) continue;

      if (!documentGroups[content.documentId]) {
        documentGroups[content.documentId] = { pages: [], chunkIds: [] };
      }

      documentGroups[content.documentId].pages.push(content.page);
      documentGroups[content.documentId].chunkIds.push(chunk.document_chunk_id);
    }

    // Create an attachment for each document
    for (const [documentId, data] of Object.entries(documentGroups)) {
      attachments.push({
        optionId: item.answer_option.id,
        documentId,
        pages: [...new Set(data.pages)], // Remove duplicates
        comment: item.comment || '',
        chunkIds: data.chunkIds
      });
    }
  }

  console.log('Parsed attachments:', attachments);
  return attachments;
};
