// @ts-ignore
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { authValidator } from "../_shared/authValidator.ts";
import { corsHeaders } from "../_shared/cors.ts";

serve(async (req) => {
  console.time("total-request-time");
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
  try {
    // Get the request body
    const { projectId } = await req.json();
    if (!projectId) {
      // console.timeEnd("total-request-time");
      return new Response(JSON.stringify({
        error: 'Project ID is required'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }

    console.time("auth-validation");
    const { user, error, supabaseClient, response: errResponse } = await authValidator(req);
    // console.timeEnd("auth-validation");
    
    if (!user || error || !supabaseClient) {
      // console.timeEnd("total-request-time");
      return errResponse;
    }
    
    // 1. Fetch project details
    console.time("fetch-project");
    const { data: project, error: projectError } = await supabaseClient.from('project').select('*').eq('id', projectId).single();
    // console.timeEnd("fetch-project");
    
    if (projectError) {
      console.error('Error fetching project:', projectError);
      // console.timeEnd("total-request-time");
      return new Response(JSON.stringify({
        error: 'Failed to fetch project details'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }
    // Check if project exists
    if (!project) {
      // console.timeEnd("total-request-time");
      return new Response(JSON.stringify({
        error: 'Project not found'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 404
      });
    }
    // 2. Check if there's an EcoVadis questionnaire for this project
    // For now, we'll assume questionnaire exists if there are questions
    // This can be expanded later if needed with a specific questionnaire table
    // 3. Fetch questions linked to this project
    console.time("fetch-project-questions");
    const { data: projectQuestions, error: projectQuestionsError } = await supabaseClient
      .from('project_ecovadis_question')
      .select(`
        id,
        status,
        impact,
        ecovadis_question:questionId (
          id,
          questionCode,
          question,
          questionName,
          indicator,
          themeId,
          type,
          sort
        )
      `).eq('projectId', projectId);
    // console.timeEnd("fetch-project-questions");
    
    if (projectQuestionsError) {
      console.error('Error fetching project questions:', projectQuestionsError);
      // console.timeEnd("total-request-time");
      return new Response(JSON.stringify({
        error: 'Failed to fetch project questions'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }
    // 4. Process the questions to match the expected format in the frontend
    console.time("process-questions");
    const questions = await getProjectQuestions(projectId, projectQuestions, supabaseClient);
    // console.timeEnd("process-questions");
    
    // 5. Return the complete project data
    const response = {
      project: {
        id: project.id,
        name: project.name,
        type: project.type,
        description: project.description || '',
        deadline: project.deadline || null,
        status: 'active',
        progress: {
          percentage: calculateProgress(questions),
          complete: questions.filter((q)=>q.status === 'complete').length,
          incomplete: questions.filter((q)=>q.status !== 'complete' && q.status !== 'not_applicable').length,
          gaps: questions.reduce((total, q)=>total + (Array.isArray(q.gaps) ? q.gaps.length : 0), 0)
        }
      },
      questions: questions.sort((a, b) => a.sort - b.sort),
      hasQuestionnaire: questions.length > 0
    };
    
    // console.timeEnd("total-request-time");
    return new Response(JSON.stringify(response), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    // console.timeEnd("total-request-time");
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});

// Helper function to calculate progress percentage
function calculateProgress(questions) {
  const totalQuestions = questions.filter((q)=>q.status !== 'not_applicable').length;
  const completedQuestions = questions.filter((q)=>q.status === 'complete').length;
  return totalQuestions > 0 ? Math.round(completedQuestions / totalQuestions * 100) : 0;
}

async function getProjectQuestions(projectId, projectQuestions, supabaseClient) {
  console.time("getProjectQuestions-total");
  try {
    // 1. Extract all IDs needed for batch fetching
    const questionIds = projectQuestions.map(pq => pq.ecovadis_question.id);
    const projectQuestionIds = projectQuestions.map(pq => pq.id);
    const themeIds = [...new Set(projectQuestions.map(pq => pq.ecovadis_question.themeId))];

    // 2. Fetch all required data in parallel
    console.time("fetch-related-data-parallel");
    const [
      { data: answersWithOptionsData, error: answersWithOptionsError },
      { data: themesData, error: themesError }
    ] = await Promise.all([
      // Fetch answers with their associated options for this project in one query
      // This ensures we only get options that have answers for this specific project
      supabaseClient
        .from('project_ecovadis_answer')
        .select(`
          *,
          ecovadis_answer_option:optionId (
            id,
            questionId,
            issueTitle,
            instructions,
            sort,
            createdAt
          )
        `)
        .eq('projectId', projectId)
        .in('ecovadis_answer_option.questionId', questionIds)
        .order('createdAt', { ascending: true }),
      
      // Fetch all themes in one query
      supabaseClient.from('ecovadis_theme').select('id, title').in('id', themeIds).order('createdAt', { ascending: true })
    ]);
    // console.timeEnd("fetch-related-data-parallel");

    // Handle errors
    if (answersWithOptionsError) console.error('Error fetching answers with options:', answersWithOptionsError);
    if (themesError) console.error('Error fetching themes:', themesError);

    // 3. Organize data into lookup maps
    console.time("organize-data-maps");
    
    // Process the combined answers with options data
    const answersWithOptions = answersWithOptionsData || [];
    
    // Create a map of options by questionId, extracting from the joined data
    const optionsByQuestionId = {};
    const answersByOptionId = {};
    
    answersWithOptions.forEach(answer => {
      if (answer.ecovadis_answer_option) {
        const option = answer.ecovadis_answer_option;
        const questionId = option.questionId;
        
        // Store the option grouped by questionId
        if (!optionsByQuestionId[questionId]) {
          optionsByQuestionId[questionId] = [];
        }
        
        // Check if we already have this option (to avoid duplicates)
        const existingOption = optionsByQuestionId[questionId].find(o => o.id === option.id);
        if (!existingOption) {
          optionsByQuestionId[questionId].push(option);
        }
        
        // Store the answer by optionId for quick lookup
        answersByOptionId[option.id] = answer;
      }
    });
    
    const themesMap = (themesData || []).reduce((map, theme) => ({ ...map, [theme.id]: theme }), {});
    // console.timeEnd("organize-data-maps");

    // 4. Collect all answer IDs for batch document fetching
    const answerIds = answersWithOptions.map(answer => answer.id).filter(Boolean);
    
    // 5. Fetch linked documents for all answers in one query with user information
    console.time("fetch-linked-documents");
    const BATCH_SIZE = 20;
    const linkedDocumentsData: any = [];
    
    for (let i = 0; i < answerIds.length; i += BATCH_SIZE) {
      const batch = answerIds.slice(i, i + BATCH_SIZE);
      const { data, error } = await supabaseClient
        .from('project_ecovadis_linked_document_chunks')
        .select(`
          id, comment, answerId, documentChunkId, attachment_source, created_by,
          document_chunk:documentChunkId (
            id, page,
            document:documentId (id, name)
          ),
          user:created_by (
            id,
            email,
            name
          )
        `)
        .in('answerId', batch);
      
      if (data) linkedDocumentsData.push(...data);
      if (error) console.error('Batch error:', error);
    }

    // Filter out entries where document doesn't exist
    const validLinkedDocuments = (linkedDocumentsData || [])
      .filter(item => item.document_chunk && item.document_chunk.document);
    
    // Group by answerId for quick lookup
    const linkedDocumentsByAnswerId = groupBy(validLinkedDocuments, 'answerId');

    // 8. Process all data to build the questions array
    console.time("build-questions-array");
    const questions = projectQuestions.map(pq => {
      // Map options with their attached documents
      const options = optionsByQuestionId[pq.ecovadis_question.id] || [];
      const mappedOptions = options.map(option => {
        const answer = answersByOptionId[option.id];

        if (!answer) return null; // This should not happen since we only have options with answers
        
        // Get linked documents for this option's answer with user information
        const attachedDocuments = answer ? 
          (linkedDocumentsByAnswerId[answer.id] || []).map(doc => {
            // Get user name for display
            let createdByName = null;
            if (doc.user && doc.user.user_profile) {
              const profile = doc.user.user_profile;
              createdByName = `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || doc.user.email;
            } else if (doc.user) {
              createdByName = doc.user.email;
            }

            return {
              id: doc.document_chunk.document.id,
              chunkId: doc.document_chunk.id,
              name: doc.document_chunk.document.name,
              pages: doc.document_chunk.page,
              comment: doc.comment,
              createdBy: doc.created_by,
              createdByName: createdByName,
              attachmentSource: doc.attachment_source || 'system'
            };
          }) : [];
        
        return {
          id: option.id,
          answerId: answer?.id || null,
          text: option.issueTitle,
          evidenceExamples: option.instructions,
          sort: option.sort,
          selected: !!answer?.response,
          response: answer?.response || '',
          attachedDocuments,
          supportsAnswerText: answer.supportsAnswerText || false
        };
      }).filter(Boolean).sort((a, b) => a.sort - b.sort);
      
      // Map theme to topic
      const theme = themesMap[pq.ecovadis_question.themeId];
      let topic = "🌍 General";
      if (theme) {
        const themeTitle = theme.title?.toLowerCase() || '';
        if (themeTitle.includes('environment') || themeTitle.includes('umwelt')) {
          topic = "🌱 Environment";
        }
        else if (themeTitle.includes('labor') || themeTitle.includes('human') || 
                 themeTitle.includes('arbeits') || themeTitle.includes('menschen')) {
          topic = "👥 Labor & Human Rights";
        }
        else if (themeTitle.includes('ethic') || themeTitle.includes('ethik')) {
          topic = "🔷 Ethics";
        }
        else if (themeTitle.includes('procurement') || themeTitle.includes('sustainable') || 
                 themeTitle.includes('beschaffung') || themeTitle.includes('nachhaltige')) {
          topic = "💼 Sustainable Procurement";
        }
        else if (themeTitle.includes('general') || themeTitle.includes('allgemein')) {
          topic = "🌍 General";
        }
      }
      
      return {
        
        // Project-question relationship fields
        projectQuestionId: pq.id,
        status: pq.status || 'in_progress',
        impact: pq.impact || 'medium',
        
        // Question fields
        questionId: pq.ecovadis_question.id,
        questionCode: pq.ecovadis_question.questionCode,
        type: pq.ecovadis_question.type,
        sort: pq.ecovadis_question.sort,
        question: pq.ecovadis_question.question,
        questionName: pq.ecovadis_question.questionName,
        indicator: pq.ecovadis_question.indicator,
        themeId: pq.ecovadis_question.themeId,
        
        // Related data
        options: mappedOptions,
        
        // Additional fields for UI compatibility
        name: pq.ecovadis_question.questionName,
        text: pq.ecovadis_question.question,
        code: pq.ecovadis_question.questionCode,
        topic: topic,
      };
    });
    // console.timeEnd("build-questions-array");

    // console.timeEnd("getProjectQuestions-total");
    return questions;
  } catch (error) {
    console.error('Error processing project questions:', error);
    // console.timeEnd("getProjectQuestions-total");
    return [];
  }
}

// Utility function to group array items by a key
function groupBy(array, key) {
  return array.reduce((result, item) => {
    (result[item[key]] = result[item[key]] || []).push(item);
    return result;
  }, {});
}
