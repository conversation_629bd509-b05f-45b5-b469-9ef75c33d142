"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PromptModule = void 0;
const common_1 = require("@nestjs/common");
const prompts_service_1 = require("./prompts.service");
const mdr_prompts_service_1 = require("./mdr-prompts.service");
const numerics_prompts_service_1 = require("./numerics-prompts.service");
const datapoint_generation_prompts_service_1 = require("./datapoint-generation-prompts.service");
const table_prompts_service_1 = require("./table-prompts.service");
let PromptModule = class PromptModule {
};
exports.PromptModule = PromptModule;
exports.PromptModule = PromptModule = __decorate([
    (0, common_1.Module)({
        imports: [],
        providers: [
            prompts_service_1.PromptService,
            mdr_prompts_service_1.MDRPromptService,
            numerics_prompts_service_1.NumericsPromptService,
            table_prompts_service_1.TablePromptService,
            datapoint_generation_prompts_service_1.NormalDpPromptService,
        ],
        exports: [
            prompts_service_1.PromptService,
            mdr_prompts_service_1.MDRPromptService,
            numerics_prompts_service_1.NumericsPromptService,
            table_prompts_service_1.TablePromptService,
            datapoint_generation_prompts_service_1.NormalDpPromptService,
        ],
        controllers: [],
    })
], PromptModule);
//# sourceMappingURL=prompts.module.js.map