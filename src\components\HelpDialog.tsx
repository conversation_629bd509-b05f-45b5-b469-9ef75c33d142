import { Button } from './ui/button';

import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogHeader,
} from '@/components/ui/dialog';

function HelpDialog({
  isHelpDialogOpen,
  setIsHelpDialogOpen,
}: {
  isHelpDialogOpen: boolean;
  setIsHelpDialogOpen: (value: boolean) => void;
}) {
  return (
    <Dialog open={isHelpDialogOpen} onOpenChange={setIsHelpDialogOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>You have any questions?</DialogTitle>
          <DialogDescription>
            If you have any questions, feel free to reach out to us via{' '}
            <a href="mailto:<EMAIL>"><EMAIL></a>. We
            will review your reply within one business day.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            onClick={() => setIsHelpDialogOpen(false)}
            variant="secondary"
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default HelpDialog;
