
import React, { useState } from 'react';
import { <PERSON><PERSON>, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { EcovadisQuestion } from '@/types/ecovadis';
import { requestEcovadisQuestionAiAnswering } from '@/api/ecovadis/ecovadis.api';
import { useToast } from '@/components/ui/use-toast';
import { useUserRole } from '@/hooks/useUserRole';
import { USER_ROLE } from '@/constants/workspaceConstants';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

interface AutoAnswerButtonProps {
  question: EcovadisQuestion;
  projectId: string;
  onComplete: () => void;
  disabled?: boolean;
}

interface LoadingState {
  [key: string]: boolean;
}

// Create a static object to store loading states for each question
const loadingStates: LoadingState = {};

export const AutoAnswerButton: React.FC<AutoAnswerButtonProps> = ({
  question,
  projectId,
  onComplete,
  disabled = false
}) => {
  const [isLoading, setIsLoading] = useState(loadingStates[question.projectQuestionId] || false);
  const { toast } = useToast();
  const { hasRole } = useUserRole();
  const isSuperAdmin = hasRole([USER_ROLE.SuperAdmin]);

  const handleAutoAnswer = async () => {
    if (!isSuperAdmin) {
      toast({
        description: "Only Glacier Admins can use AI Auto Answer while in development.",
        variant: "destructive",
      });
      return;
    };
    
    // Set loading state for this specific question
    loadingStates[question.projectQuestionId] = true;
    setIsLoading(true);
    try {
      toast({
        description: "Do not exit the page. Generating answer and attaching documents...",
        variant: "default",
      });
      
      // Call AI answer API - attachments are now saved automatically within the function
      await requestEcovadisQuestionAiAnswering(projectId, question.projectQuestionId);
      onComplete();
      
      setTimeout(() => {
        toast({
          description: "Answer generated and documents attached successfully!",
          variant: "success",
        });
      },300)
      
    } catch (error) {
      console.error('Error during auto-answer:', error);
      toast({
        description: "Failed to auto-answer question. Please try again.",
        variant: "destructive",
      });
    } finally {
      // Clear loading state for this specific question
      loadingStates[question.projectQuestionId] = false;
      setIsLoading(false);
    }
  };

  return (
    <Tooltip delayDuration={300}>
      <TooltipTrigger asChild>
        <div>
          <Button
            onClick={isSuperAdmin ? handleAutoAnswer : undefined}
            disabled={disabled || isLoading || !isSuperAdmin}
            variant="darkBlue"
            className="flex items-center gap-2"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Bot className="h-4 w-4" />
            )}
            AI Auto Answer
          </Button>
        </div>
      </TooltipTrigger>
      {!isSuperAdmin && (
        <TooltipContent side="bottom">
          <p>Only Super Admins can use AI Auto Answer</p>
        </TooltipContent>
      )}
    </Tooltip>
  );
};
