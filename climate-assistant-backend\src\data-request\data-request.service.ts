import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository } from 'typeorm';

import {
  DatapointRequestData,
  DataRequestData,
  GenerateDataRequestReportTextTextPayload,
  UpdateDataRequestPayload,
} from './entities/data-request.dto';

import { DataRequest, DataRequestStatus } from './entities/data-request.entity';
import { DatapointRequestStatus } from 'src/datapoint/entities/datapoint-request.entity';
import { PromptService } from 'src/prompts/prompts.service';
import { ChatCompletionMessageParam } from 'openai/resources';
import { ProjectService } from 'src/project/project.service';
import { Comment, CommentType } from 'src/project/entities/comment.entity';
import { trimHtmlPreAndPostfix } from 'src/util/llm-response-util';
import { User } from 'src/users/entities/user.entity';
import { UsersService } from 'src/users/users.service';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { DatapointRequestService } from 'src/datapoint/datapoint-request.service';
import { Role } from 'src/users/entities/user-workspace.entity';
import { LLM_MODELS } from 'src/constants';
import { Observable, Subject } from 'rxjs';

import { DatapointGenerationEvent } from './constants';
import {
  DataRequestGeneration,
  dataRequestGenerationStatus,
} from './entities/datarequest-generation.entity';
import { DatapointDataRequestSharedService } from 'src/shared/shared-datapoint-datarequest.service';
import { LlmRateLimiterService } from 'src/llm-rate-limiter/llm-rate-limiter.service';
import { DatapointRequestWithDocumentCount } from 'src/datapoint/entities/datapoint-request.dto';

@Injectable()
export class DataRequestService {
  constructor(
    @InjectRepository(DataRequest)
    private readonly dataRequestRepository: Repository<DataRequest>,
    private readonly promptService: PromptService,
    private readonly projectService: ProjectService,
    private readonly userService: UsersService,
    private readonly workspaceService: WorkspaceService,
    private readonly datapointRequestService: DatapointRequestService,
    private readonly llmRateLimitService: LlmRateLimiterService,
    private readonly datapointDataRequestSharedService: DatapointDataRequestSharedService,
    @InjectRepository(DataRequestGeneration)
    private readonly dataRequestGenerationRepository: Repository<DataRequestGeneration>
  ) {}

  private readonly eventSubject = new Subject<DatapointGenerationEvent>();

  public readonly events$: Observable<DatapointGenerationEvent> =
    this.eventSubject.asObservable();

  private readonly logger = new Logger(DataRequestService.name);

  emitSseEvents(event: DatapointGenerationEvent) {
    const eventPayload = {
      ...event,
      timestamp: new Date(),
    };
    this.eventSubject.next(eventPayload);
  }

  closeSseEvents() {
    this.eventSubject.complete();
  }

  async findAll(projectId: string): Promise<DataRequest[]> {
    return this.dataRequestRepository.find({
      where: { projectId },
    });
  }

  async findProject(dataRequestId: string): Promise<DataRequest> {
    const dataRequest = await this.dataRequestRepository.findOne({
      where: { id: dataRequestId },
      relations: {
        project: true,
        disclosureRequirement: true,
      },
    });
    return dataRequest;
  }

  async findById(dataRequestId: string): Promise<DataRequest> {
    const dataRequest = await this.dataRequestRepository.findOne({
      where: { id: dataRequestId },
    });
    return dataRequest;
  }

  async findRelatedData(
    dataRequestId: string,
    userId?: string
  ): Promise<DataRequestData> {
    const dataRequest = await this.dataRequestRepository.findOne({
      where: { id: dataRequestId },
      relations: {
        responsiblePerson: true,
        approver: true,
        disclosureRequirement: true,
        dataRequestGenerations: true,
        comments: {
          user: true,
        },
        commentGenerations: true,
      },
      order: {
        comments: {
          createdAt: 'ASC',
        },
      },
    });
    if (!dataRequest) {
      throw new NotFoundException(
        `DataRequest with ID ${dataRequest} not found`
      );
    }

    const datapointRequests: DatapointRequestWithDocumentCount[] =
      await this.datapointRequestService.findAllDataPointRequests(
        dataRequest.id,
        userId
      );

    dataRequest.datapointRequests = datapointRequests;

    const evaluatedStatus = await this.dataRequestStatusProcessor(dataRequest);

    if (dataRequest.status !== evaluatedStatus) {
      await this.update({
        dataRequestId,
        updateDataRequestPayload: { status: evaluatedStatus },
      });
      dataRequest.status = evaluatedStatus;
    }

    return dataRequest;
  }

  async update({
    dataRequestId,
    updateDataRequestPayload,
    userId,
    workspaceId,
    event = 'data_request_updated',
  }: {
    dataRequestId: string;
    updateDataRequestPayload: UpdateDataRequestPayload;
    userId?: string;
    workspaceId?: string;
    event?: string;
  }): Promise<DataRequest> {
    const dataRequest = await this.findById(dataRequestId);
    if (!dataRequest) {
      throw new NotFoundException(`Data Request not found`);
    }

    const definedPayload = Object.fromEntries(
      Object.entries(updateDataRequestPayload).filter(
        ([_, value]) => value !== undefined
      )
    );

    await this.dataRequestRepository.update(dataRequestId, definedPayload);

    const updated = await this.findById(dataRequestId);

    if (userId && workspaceId) {
      await this.workspaceService.storeActionHistory({
        event: event,
        ref: dataRequestId,
        workspaceId: workspaceId,
        versionData: {
          event: event,
          doneBy: userId,
          data: updated,
        },
      });
    }

    return updated;
  }

  async dataRequestStatusProcessor(
    dataRequest: DataRequestData
  ): Promise<DataRequestStatus> {
    const { datapointRequests, content, approvedBy } = dataRequest;

    // "Approved Answer"
    // DR: Will be displayed, if the DR has the status "Approved Answer"
    if (dataRequest.status === DataRequestStatus.ApprovedAnswer) {
      return DataRequestStatus.ApprovedAnswer;
    }

    // "Not reported"
    // DR: Will be displayed, if all the datapoints inside the "Request for Data" are marked as "Not Reported" or the Status is "Not Reported"
    const validateDPForNotReported = datapointRequests.every(
      (datapoint) => datapoint.status === DatapointRequestStatus.NotAnswered
    );

    if (
      validateDPForNotReported ||
      dataRequest.status === DataRequestStatus.NotAnswered
    ) {
      return DataRequestStatus.NotAnswered;
    }

    // "No Data"
    // DR: Will be displayed red and is shown, if there is no content and no document-chunks linked for every datapoint of the disclosure requirement

    datapointRequests.filter((datapoint) => {
      return (
        datapoint.status !== DatapointRequestStatus.NotAnswered &&
        !datapoint.content &&
        !datapoint.documentChunkCount
      );
    });

    if (datapointRequests.length === 0) {
      return DataRequestStatus.NoData;
    }

    // "Incomplete Data"
    // DR: Will be displayed yellow and is shown, if at least one of the datapoints inside the DR is not of status "Complete Data" or "Not Reported"

    const validateDPForIncompleteData = datapointRequests.some(
      (datapoint) =>
        datapoint.status !== DatapointRequestStatus.CompleteData &&
        datapoint.status !== DatapointRequestStatus.NotAnswered
    );

    if (validateDPForIncompleteData) {
      return DataRequestStatus.IncompleteData;
    }

    // "Complete Data or Ready for Draft"
    // DR: Will be displayed, if all datapoints inside the DR have the status "Complete Data", but no content (reporttext) was yet saved

    const validateDPForCompleteData = datapointRequests.every(
      (datapoint) => datapoint.status === DatapointRequestStatus.CompleteData
    );

    if (
      validateDPForCompleteData &&
      (!content || content.trim() === '' || content.trim() === '<p></p>')
    ) {
      return DataRequestStatus.CompleteData;
    }

    // "Draft"
    // DR: Will be displayed, if there exists a draft of an answer for the DR and all Datapoints have the status "Complete Data"
    if (approvedBy === null) {
      return DataRequestStatus.Draft;
    }

    return DataRequestStatus.ApprovedAnswer;
  }

  async reviewDataRequestContentWithAI({
    dataRequestId,
    userId,
    workspaceId,
  }: {
    dataRequestId: string;
    userId: string;
    workspaceId: string;
  }): Promise<Comment[]> {
    const dataRequest = await this.dataRequestRepository.findOne({
      where: { id: dataRequestId, status: Not(DataRequestStatus.NotAnswered) },
      relations: [
        'project',
        'comments',
        'disclosureRequirement',
        'datapointRequests.esrsDatapoint',
      ],
    });

    const isAiEvaluator = await this.userService.userHasRequiredRole(userId, [
      Role.SuperAdmin,
    ]);

    // Build prompt
    this.logger.log(`Start Gap Analysis for dataRequest ${dataRequest.id}`);
    const dataRequestGapAnalysisChatCompletion: ChatCompletionMessageParam[] = [
      {
        role: 'system',
        content: this.promptService.generateDataRequestGapAnalysisSystemPrompt({
          esrsDisclosureRequirement: dataRequest.disclosureRequirement,
          generationLanguage: dataRequest.project.primaryContentLanguage,
        }),
      },
      {
        role: 'system',
        content:
          this.promptService.generateDataRequestFullLawTextContextForReportedDatapoints(
            dataRequest
          ),
      },
      {
        role: 'user',
        content:
          this.promptService.generateDataRequestGapAnalysisContentContext(
            dataRequest
          ),
      },
    ];

    const reviewedGapAnalysisCompletionResponse =
      await this.llmRateLimitService.handleRequest({
        model: LLM_MODELS['gpt-4o'],
        messages: dataRequestGapAnalysisChatCompletion,
        json: true,
        temperature: 0.3,
      });

    if (reviewedGapAnalysisCompletionResponse.status === 400) {
      throw new Error(reviewedGapAnalysisCompletionResponse.response);
    }

    const reviewedGapAnalysis: {
      gapIdentified: boolean;
      datapointGaps: {
        datapoint: string;
        gap: string;
        actions: string[];
        exampleText: string;
      }[];
    } = reviewedGapAnalysisCompletionResponse.response;

    // if (reviewedGapAnalysis) {
    //   reviewedGapAnalysis = trimHtmlPreAndPostfix(reviewedGapAnalysis);
    // }

    this.logger.log(
      `Gap Analysis Tokens: ${JSON.stringify(reviewedGapAnalysisCompletionResponse.token)}`
    );

    const globalAIUser: User = await this.userService.findGlobalGlacierAIUser();

    const comments: Comment[] = [];

    if (reviewedGapAnalysis.gapIdentified) {
      for (const gapJson of reviewedGapAnalysis.datapointGaps) {
        // prettier-ignore
        const commentHtml = 
          "<h2>" + gapJson.datapoint + "</h2>" +
          "<p><strong>Gap:</strong> " + gapJson.gap + "</p>" +
          "<p><strong>Recommended Actions:</strong></p>" +
          "<ul>" +
            gapJson.actions.map(function(action) { 
              return "<li>" + action + "</li>"; 
            }).join('') +
          "</ul>" +
          "<p><strong>Example Text:</strong>" + gapJson.exampleText + "</p>";

        const comment = await this.projectService.addComment({
          commentableId: dataRequest.id,
          commentableType: CommentType.DataRequest,
          userId: globalAIUser.id,
          comment: commentHtml,
          workspaceId,
          evaluationLot: isAiEvaluator,
        });

        comments.push(comment);
      }
    }

    return comments;
  }

  async validateDataRequestGenerationRightsOrFail({
    dataRequest,
    userId,
  }: {
    dataRequest: DataRequest;
    userId: string;
  }): Promise<boolean> {
    if (!dataRequest.disclosureRequirement.publicAccess) {
      await this.userService.userHasRequiredRoleOrFail({
        userOrId: userId,
        role: [Role.SuperAdmin, Role.AiContributor],
        message: 'Data request generation is not enabled for this request.',
      });
    }

    return true;
  }

  async generateDataRequestTextContentWithAI({
    dataRequestId,
    userId,
    workspaceId,
    additionalData,
  }: {
    dataRequestId: string;
    userId: string;
    workspaceId: string;
    additionalData: GenerateDataRequestReportTextTextPayload;
  }): Promise<{ content: string; id: string }> {
    const dataRequest = await this.dataRequestRepository.findOne({
      where: { id: dataRequestId },
      relations: [
        'project',
        'disclosureRequirement',
        'datapointRequests.esrsDatapoint',
      ],
    });

    const cleanCustomUserRemark =
      additionalData?.additionalReportTextGenerationRules.trim() !== ''
        ? additionalData.additionalReportTextGenerationRules
        : null;

    if (cleanCustomUserRemark) {
      await this.dataRequestRepository.update(
        { id: dataRequestId },
        {
          customUserRemark: cleanCustomUserRemark,
        }
      );
    }

    this.logger.log(
      `Start Generating dataRequest ${dataRequest.id} - ${dataRequest.disclosureRequirement.dr} with AI`
    );

    // Generate Prompt based on Requirements
    // @S: This can become extremely long. If quality isn't good, consider splitting to 2-3 sub-DRs?
    // @S: Add a "general information about company style" to the prompt
    //- shorter system prompt
    //- law texts (exploit 'primacy bias')
    //- datapoint contents
    //- example (exploit 'recency bias')
    //- detailed instructions system prompt

    const dataRequestGenerationChatCompletion: ChatCompletionMessageParam[] = [
      {
        role: 'system',
        content:
          this.promptService.generateDataRequestContentGenerationSystemPrompt1(
            dataRequest.disclosureRequirement
          ),
      },
      {
        role: 'system',
        content:
          this.promptService.generateDataRequestFullLawTextContextForReportedDatapoints(
            dataRequest
          ),
      },
      {
        role: 'system',
        content:
          this.promptService.generateDataRequestContextFromDatapoints(
            dataRequest
          ),
      },
      {
        role: 'system',
        content:
          // if (user.ReportPreference != technical) // for if we want technical version
          this.promptService.generateDataRequestContentExampleIntegrated(),
        //this.promptService.generateDataRequestContentExampleTechnical(),
      },
      {
        role: 'system',
        content:
          this.promptService.generateDataRequestContentGenerationSystemPrompt2({
            disclosureRequirement: dataRequest.disclosureRequirement,
            generationLanguage: dataRequest.project.primaryContentLanguage,
            reportTextGenerationRules:
              dataRequest.project.reportTextGenerationRules,
            customUserRemark: cleanCustomUserRemark,
            enableDatapointTags: additionalData.enableDatapointTags,
          }),
      },
    ];

    if (additionalData.useExistingReportText) {
      dataRequestGenerationChatCompletion.push({
        role: 'user',
        content: `Following is the existing report text that was previously generated: ${dataRequest.content}`,
      });
    }

    const dataRequestGenerationChatCompletionResponse =
      await this.llmRateLimitService.handleRequest({
        model: LLM_MODELS['gpt-4o'],
        messages: dataRequestGenerationChatCompletion,
        json: true,
        temperature: 0.5,
      });

    if (dataRequestGenerationChatCompletionResponse.status === 400) {
      throw new Error(dataRequestGenerationChatCompletionResponse.response);
    }

    const isAIEvaluator = await this.userService.userHasRequiredRole(userId, [
      Role.SuperAdmin,
    ]);

    let generatedContent = dataRequestGenerationChatCompletionResponse.response;
    generatedContent = trimHtmlPreAndPostfix(generatedContent);

    const dataGenerationResponse = {
      id: dataRequest.id,
      content: generatedContent,
    };

    if (isAIEvaluator) {
      const dataGeneration = await this.dataRequestGenerationRepository.create({
        data: { content: generatedContent },
        dataRequest,
        evaluatorId: userId,
      });
      await this.dataRequestGenerationRepository.save(dataGeneration);
      dataGenerationResponse.id = dataGeneration.id;
    } else {
      dataRequest.content = generatedContent;
      await this.dataRequestRepository.save(dataRequest);
    }

    this.logger.log(
      `Finished Generating dataRequest ${dataRequest.id} - ${dataRequest.disclosureRequirement.dr} with AI`
    );

    const globalAIUser: User = await this.userService.findGlobalGlacierAIUser();

    await this.workspaceService.storeActionHistory({
      event: 'data_request_ai_generated',
      ref: dataRequestId,
      workspaceId: workspaceId,
      versionData: {
        event: 'data_request_ai_generated',
        doneBy: globalAIUser.id,
        issuedBy: userId,
        data: dataRequest,
      },
    });

    return dataGenerationResponse;
  }

  async findDatapointById(
    datapointRequestId: string
  ): Promise<DatapointRequestData> {
    return await this.datapointRequestService.findDatapointWithGenerationsById(
      datapointRequestId
    );
  }

  async generateAllDatapointForDataRequest({
    dataRequestId,
    userId,
    workspaceId,
  }: {
    dataRequestId: string;
    userId: string;
    workspaceId: string;
  }): Promise<void> {
    let datapointRequestToGenerate =
      await this.datapointRequestService.findDatapointRequestsByStatus({
        dataRequestId,
        status: [DatapointRequestStatus.NoData],
      });
    datapointRequestToGenerate =
      datapointRequestToGenerate.filter(
        (datapointRequest) =>
          datapointRequest.datapointDocumentChunkMap.filter((mp) => mp.active)
            .length > 0
      ) || [];

    for (const datapointRequest of datapointRequestToGenerate) {
      await this.datapointDataRequestSharedService.addDatapointToGenerationQueue(
        {
          datapointRequest,
          userId,
          workspaceId,
          useExistingReportTextForReference: false,
        }
      );
    }
  }

  async reviewAllDatapointForDataRequest({
    dataRequestId,
    userId,
    workspaceId,
  }: {
    dataRequestId: string;
    userId: string;
    workspaceId: string;
  }): Promise<void> {
    const datapointRequestToReview =
      await this.datapointRequestService.findDatapointRequestsExcludingStatus({
        dataRequestId,
        status: [
          DatapointRequestStatus.NoData,
          DatapointRequestStatus.NotAnswered,
        ],
      });

    for (const datapointRequest of datapointRequestToReview) {
      await this.datapointDataRequestSharedService.addDatapointToReviewQueue({
        datapointRequest,
        userId,
        workspaceId,
      });
    }
  }

  async setDatapointQueueStatusToNull(id: string) {
    await this.datapointRequestService.updateQueueStatus({
      datapointRequestId: id,
      queueStatus: null,
    });
  }

  async getGenerations(
    dataRequestId: string
  ): Promise<DataRequestGeneration[]> {
    return this.dataRequestGenerationRepository.find({
      where: { dataRequest: { id: dataRequestId } },
    });
  }

  async updateGenerationStatus({
    dataRequestGenerationId,
    status,
    userId,
  }: {
    dataRequestGenerationId: string;
    status: dataRequestGenerationStatus;
    userId: string;
    workspaceId: string;
  }) {
    try {
      await this.dataRequestGenerationRepository.update(
        {
          id: dataRequestGenerationId,
        },
        { status, evaluatorId: userId, evaluatedAt: new Date() }
      );
      if (status === dataRequestGenerationStatus.Approved) {
        return await this.dataRequestGenerationRepository.findOne({
          where: { id: dataRequestGenerationId },
          relations: ['dataRequest'],
        });
      }
      return null;
    } catch (err) {
      console.log(err);
      throw new Error(`Error while updating generation status: ${err.message}`);
    }
  }
}
