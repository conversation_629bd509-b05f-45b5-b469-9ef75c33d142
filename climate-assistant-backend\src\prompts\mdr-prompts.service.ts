import { Injectable } from '@nestjs/common';
import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { Language } from 'src/project/entities/project.entity';
@Injectable()
export class MDRPromptService {
  indentifyMDRandGenerateGapAnalysisSystemPrompt({
    esrsDatapoint,
    language,
  }: {
    esrsDatapoint: ESRSDatapoint;
    language: Language;
  }): string | null {
    const datapointId = esrsDatapoint.datapointId.split('.')[1];
    switch (datapointId) {
      case 'MDR-P_01-06':
        return this.generateMDRP0106GapAnalysisSystemPrompt({
          esrsDatapoint,
          language,
        });
      case 'MDR-A_01-12':
        return this.generateMDRA0112GapAnalysisSystemPrompt({
          esrsDatapoint,
          language,
        });

      case 'MDR-T_01-13':
        return this.generateMDRT0113GapAnalysisSystemPrompt({
          esrsDatapoint,
          language,
        });

      case 'MDR-P_07-09':
        return this.generateMDRP0709GapAnalysisSystemPrompt({
          esrsDatapoint,
          language,
        });

      case 'MDR-A_13-15':
        return this.generateMDRA1314GapAnalysisSystemPrompt({
          esrsDatapoint,
          language,
        });

      case 'MDR-T_14-19':
        return this.generateMDRT1419GapAnalysisSystemPrompt({
          esrsDatapoint,
          language,
        });
      default:
        return null;
    }
  }

  generateMDRP0106GapAnalysisSystemPrompt({
    esrsDatapoint,
    language,
  }: {
    esrsDatapoint: ESRSDatapoint;
    language: Language;
  }): string {
    return `**Task**: 
Conduct a structured gap analysis based on each individual policy a company has implemented for sustainability. For each policy, identify gaps in compliance with the specific requirements of **${esrsDatapoint.datapointId}** and the **Minimal Disclosure Requirements (MDRs)**.
    
### ${esrsDatapoint.datapointId} and Minimal Disclosure Requirements (MDRs)

### Requirements of the Standard - Extracted from ESRS:

Requirements: ${esrsDatapoint.lawText}
Footnotes: ${esrsDatapoint.footnotes}

Application Requirements: ${esrsDatapoint.lawTextAR}
Footnotes for Application Requirements: ${esrsDatapoint.footnotesAR}
    
The objective of this **Minimum Disclosure Requirement** is to provide an understanding of the policies that the undertaking has in place to prevent, mitigate, and remediate actual and potential impacts, to address risks, and to pursue opportunities.

The undertaking shall disclose information about policies adopted to manage material sustainability matters. The disclosure shall include the following information:

1. **Key Content Description**: A description of the key contents of the policy, including its general objectives and the material impacts, risks, or opportunities the policy addresses, and the process for monitoring.
2. **Scope Description**: A description of the policy's scope or exclusions, covering activities, value chain (upstream/downstream), geographies, and affected stakeholder groups.
3. **Senior Accountability**: The most senior level in the organization accountable for implementing the policy.
4. **Reference to Standards**: A reference, if relevant, to the third-party standards or initiatives the undertaking commits to respecting through the implementation of the policy.
5. **Stakeholder Consideration**: A description of the consideration given to key stakeholders' interests in setting the policy.
6. **Policy Accessibility**: Whether and how the undertaking makes the policy available to potentially affected stakeholders and those helping to implement it.

### Procedure

**1. Context Review**

Review the company’s **Context**, focusing on each individual policy related to **${esrsDatapoint.datapointId}** and **MDRs**.

**2. Structured Analysis by Policy**

For each policy, identified by an <h2> tag in the provided HTML content, identify any gaps in compliance. Structure the findings with the following headings:

- **Gap**: Identify specific missing or insufficient information for each policy.
- **Recommendation for Policy Steps**: Provide clear, actionable steps for improving each policy to meet **${esrsDatapoint.datapointId}** requirements.
- **Sample Text**: Provide sample text relevant to each policy, including any missing details needed for compliance.
- **Disclaimer**: Include a disclaimer if specific information is unavailable. Follow this structure: Detailed key figures or information on [insert missing information here] are currently not included in the [Policy Title] policy. [Link to the policy]. Please review the [Policy Title] policy to ensure all required information is included and update it accordingly.

**3. Presentation of Gaps by Policy**

Document each gap individually by policy, with recommendations and sample texts for each, to ensure that gaps for every policy are clearly presented and prioritized.

If no policy is available and no <h2> tags are present in the HTML content, check if the text "No policy available" is written. If not, include an appropriate message indicating the absence of policies.

**Expected Output**:

For each policy identified in the HTML content (using <h2> tags), provide a JSON object with the following structure:
    
{
    "title": "<Policy Title>",
    "gapIdentified": true/false,
    "gap": "<p>[Description of the gap]</p>",
    "actions": [
        "<p>[Actionable recommendation 1]</p>",
        "<p>[Actionable recommendation 2]</p>"
    ],
    "exampleText": "<p>[Sample text including missing details]</p>",
    "disclaimer": "<p>Detailed key figures or information on [insert missing information here] are currently not included in the [Policy Title] policy. [Link to the policy]. Please review the [Policy Title] policy to ensure all required information is included and update it accordingly.</p>"
}
    
**Language**:

All the outcomes that you generate are written in ${language}. Don't mention html tags like <h2> in the output as explanation, because they get rendered.

**Example Output with Gaps identified:**
    
{
    "title": "<p><strong>Policy: </strong>Waste Prevention and Minimization</p>",
    "gapIdentified": true,
    "gap": "<p>Kelag emphasizes waste prevention and has outlined specific practices, but there’s limited information on how these policies are monitored, the expected outcomes, and any exclusion scopes.</p>",
    "actions": [
        "<p>Include measurable targets (e.g., percentage reduction in waste generation) and establish a process for regularly monitoring and reporting these outcomes.</p>"
    ],
    "exampleText": "<p>“Kelag’s waste prevention initiatives target a 15% reduction in waste generation by 2026, with monitoring conducted quarterly to ensure progress toward these goals.”</p>",
    "disclaimer": "<p>Detailed key figures or information on the monitoring processes and expected outcomes are currently not included in the 'Waste Prevention and Minimization' policy. [Link to the policy]. Please review the 'Waste Prevention and Minimization' policy to ensure all required information is included and update it accordingly.</p>"
}
    
**Example Output with no Gaps identified:**
    
{
    "title": "Energy Efficiency Initiatives",
    "gapIdentified": false,
    "text": "<p>Upon reviewing the 'Energy Efficiency Initiatives' policy, no gap has been identified. The company has adequately disclosed its policies in compliance with ${esrsDatapoint.datapointId} requirements. No further action is required for this specific policy.</p>"
}
      `;
  }

  generateMDRA0112GapAnalysisSystemPrompt({
    esrsDatapoint,
    language,
  }: {
    esrsDatapoint: ESRSDatapoint;
    language: Language;
  }): string {
    return `
**Task**: Conduct a structured gap analysis based on **each individual action** a company has undertaken for sustainability. For each action, identify gaps in compliance with the specific requirements of **${esrsDatapoint.datapointId}** and the **Minimal Disclosure Requirements (MDRs)**.

**Requirements of the Standard** - **extracted from [ESRS](https://xbrl.efrag.org/e-esrs/esrs-set1-2023.html#7144)**:
  
Requirements: ${esrsDatapoint.lawText}
Footnotes: ${esrsDatapoint.footnotes}

Application Requirements: ${esrsDatapoint.lawTextAR}
Footnotes for Application Requirements: ${esrsDatapoint.footnotesAR}

The objective of this ***Minimum Disclosure Requirement*** is to provide an understanding of the key ***actions*** taken and/or planned to prevent, mitigate and ***remediate*** actual and potential ***impacts***, and to address ***risks*** and ***opportunities***, and where applicable achieve the objectives and ***targets*** of related ***policies***.
  
Where the implementation of a ***action*** requires ***action steps***, or a comprehensive action plan, to achieve its objectives, as well as when actions are implemented without a specific policy, the undertaking shall disclose the following information:
    - (a) the list of key actions taken in the reporting year and planned for the future, their expected outcomes, and, where relevant, how their implementation contributes to the achievement of policy objectives and ***targets***;
    - (b) the scope of the key actions (i.e., coverage in terms of activities, upstream and/or downstream ***value chain***, geographies, and, where applicable, affected ***stakeholder*** groups);
    - (c) the time horizons under which the undertaking intends to complete each key action;
    - (d) if applicable, key actions taken (along with results) to provide for and cooperate in or support the provision of ***remedy*** for those harmed by actual material impacts;
    - (e) if applicable, quantitative and qualitative information regarding the progress of actions or action plans disclosed in prior periods.
  
Where the implementation of an action plan requires significant operational expenditures (Opex) and/or capital expenditures (Capex), the undertaking shall:
    - (a) describe the type of current and future financial and other resources allocated to the action plan, including if applicable, the relevant terms of sustainable finance instruments, such as green bonds, social bonds, and green loans, the environmental or social objectives, and whether the ability to implement the actions or action plan depends on specific preconditions, e.g., granting of financial support or public policy and market developments;
    - (b) provide the amount of current financial resources and explain how they relate to the most relevant amounts presented in the financial statements;
    - (c) provide the amount of future financial resources.
  
  **Procedure**:
  
  1. **Context Review**:
      - Review the company's content, provided as an HTML string.
  2. **Identification of Actions**:
      - Identify each action in the content. Each is represented by a **<h2>** heading in the HTML content.
      - If no **<h2>** is available, check if the text "No action available" is present.
  3. **Structured Analysis by Action**:
      - For each action identified:
          - Extract the **actionTitle** from the **<h2>** heading.
          - Identify any gaps in compliance with **${esrsDatapoint.datapointId}** and the MDRs.
          - Structure the findings with the following fields:
              - **actionTitle**: The title of the action.
              - **gapIdentified**: A boolean indicating whether a gap was identified.
              - **gap**: A description of specific missing or insufficient information.
              - **actions**: A list of clear, actionable steps for improving the action to meet **${esrsDatapoint.datapointId}** requirements.
              - **exampleText**: Provide sample text relevant to each action, including any missing details needed for compliance.
              - **disclaimer**: Include a disclaimer if specific information is unavailable.
  4. **Handling Absence of Actions**:
      - If no **<h2>** headings are present and "No action available" is not stated, note that the absence of policies, targets, or actions should be addressed.
  5. **Presentation of Gaps by Action**:
      - Document each gap individually by action, with recommendations and sample texts for each, to ensure that gaps for every action are clearly presented and prioritized.

**Expected Output**:

For each action identified in the HTML content (using <h2> tags), provide a JSON object with the following structure:
    
{
    "title": "<Action Title>",
    "gapIdentified": true/false,
    "gap": "<p>[Description of the gap]</p>",
    "actions": [
        "<p>[Actionable recommendation 1]</p>",
        "<p>[Actionable recommendation 2]</p>"
    ],
    "exampleText": "<p>[Sample text including missing details]</p>",
    "disclaimer": "<p>Detailed key figures or information on [insert missing information here] are currently not included in the [Action Title]. [Link to the action]. Please review the [Action Title] to ensure all required information is included and update it accordingly.</p>"
}
  
  **Language**:
  All the outcomes that you generate are written in ${language}
  
  **Example Output with Gaps identified:**
  
  {
    "title": "<p><strong>Action: </strong>Anti-Corruption Training Programs</p>",
    "gapIdentified": true,
    "gap": "<p>The disclosure offers some information about training programs but lacks specific details on the nature, scope, and depth of these programs, as required by G1-3_06.</p>",
    "actions": [
      "<p>Provide a comprehensive overview of the training programs, including content, duration, and delivery methods.</p>",
      "<p>Highlight specialized training for various roles or departments.</p>"
    ],
    "exampleText": "<p>“The company's anti-corruption and anti-bribery training programs cover a wide range of topics, including risk assessment and reporting procedures. These programs are delivered through e-learning modules and in-person workshops and are tailored to different roles within the company.”</p>",
    "disclaimer": "<p>Detailed descriptions of the training programs are currently unavailable and need to be provided for compliance.</p>"
  }

  
  **Example Output with no Gaps identified:**
  
  {
    "title": "<p><strong>Action: </strong>Climate Change Mitigation Plan</p>",
    "gapIdentified": false,
    "text": "<p>Upon reviewing the provided information, no gap has been identified. The company has adequately disclosed its transition plan for climate change mitigations. No further action is required for this specific datapoint.</p>"
  }

  `;
  }

  generateMDRT0113GapAnalysisSystemPrompt({
    esrsDatapoint,
    language,
  }: {
    esrsDatapoint: ESRSDatapoint;
    language: Language;
  }): string {
    return `
**Task**: Conduct a structured gap analysis based on **each individual target** a company has set for sustainability. For each target, identify gaps in compliance with the specific requirements of **${esrsDatapoint}** and the **Minimal Disclosure Requirements (MDRs)**.
  
### Data Point and Minimal Disclosure Requirements (MDRs)
  
**Requirements of the Standard** - **extracted from [ESRS](https://xbrl.efrag.org/e-esrs/esrs-set1-2023.html#7144)**:
  
Requirements: ${esrsDatapoint.lawText}
Footnotes: ${esrsDatapoint.footnotes}

Application Requirements: ${esrsDatapoint.lawTextAR}
Footnotes for Application Requirements: ${esrsDatapoint.footnotesAR}
  
The undertaking shall disclose the measurable, outcome-oriented, and time-bound targets on material sustainability matters it has set to assess progress. For each target, the disclosure shall include the following information:
  
  1. **Description of the relationship of the target to the objectives**
  2. **The defined target level to be achieved**, including, where applicable, whether the target is absolute or relative and in which unit it is measured
  3. **The scope of the target**, including the undertaking’s activities and/or its upstream and/or downstream value chain, where applicable, and geographical boundaries
  4. **The baseline value and base year** from which progress is measured
  5. **The period to which the target applies**, including any milestones or interim targets
  6. **The methodologies and significant assumptions used to define targets**, including where applicable, the selected scenario, data sources, alignment with national, EU, or international goals, and consideration of the wider sustainable development context and/or local impacts
  7. **Whether the targets are based on conclusive scientific evidence**
  8. **Whether and how stakeholders have been involved in target setting** for each material sustainability matter
  9. **Any changes in targets and corresponding metrics** or underlying measurement methodologies, significant assumptions, limitations, sources, and data collection processes, including explanations of the rationale for those changes and their effect on comparability (see Disclosure Requirement BP-2)
  10. **The performance against its disclosed targets**, including information on how the target is monitored and reviewed, metrics used, progress analysis, and significant changes in performance trends
  
### Procedure
  
1. **Context Review**
    - Review the company's content, provided as an HTML string.
2. **Identification of Targets**
    - Identify each target in the content. Each is represented by a **<h2>** heading in the HTML content.
    - If no **<h2>** is available, check if the text "No target available" is present.
3. **Structured Analysis by Target**
    - For each target identified:
        - Extract the **targetTitle** from the **<h2>** heading.
        - Identify any gaps in compliance with **${esrsDatapoint}** and the MDRs.
        - Structure the findings with the following fields:
            - **targetTitle**: The title of the target.
            - **gapIdentified**: A boolean indicating whether a gap was identified.
            - **gap**: A description of specific missing or insufficient information.
            - **actions**: A list of clear, actionable steps for improving the target to meet **${esrsDatapoint}** requirements.
            - **exampleText**: Provide sample text relevant to the target, including any missing details needed for compliance.
            - **disclaimer**: Include a disclaimer if specific information is unavailable.
4. **Handling Absence of Targets**
    - If no **<h2>** headings are present and "No target available" is not stated, note that the absence of targets should be addressed.
5. **Presentation of Gaps by Target**
    - Document each gap individually by target, with recommendations and sample texts for each, to ensure that gaps for every target are clearly presented and prioritized.

**Expected Output**:

For each target identified in the HTML content (using <h2> tags), provide a JSON object with the following structure:
    
{
    "title": "<Target Title>",
    "gapIdentified": true/false,
    "gap": "<p>[Description of the gap]</p>",
    "actions": [
        "<p>[Actionable recommendation 1]</p>",
        "<p>[Actionable recommendation 2]</p>"
    ],
    "exampleText": "<p>[Sample text including missing details]</p>",
    "disclaimer": "<p>Detailed key figures or information on [insert missing information here] are currently not included in the [Target Title] target. [Link to the Target]. Please review the [Target Title] target to ensure all required information is included and update it accordingly.</p>"
}
  
**Language**:
All the outcomes that you generate are written in ${language}
  
**Example Output with Gaps identified:**
{
    "title": "<p><strong>Target: </strong>Reduction of Scope 1 & 2 Emissions</p>",
    "gapIdentified": true,
    "gap": "<p>While the company outlines its 50.4% reduction goal for Scope 1 and 2 emissions by 2032, there is limited detail on the baseline year assumptions and data sources. Additionally, there is no mention of stakeholder involvement in setting these targets.</p>",
    "actions": [
      "<p>Specify the methodologies used for measuring the baseline and indicate any significant assumptions made.</p>",
      "<p>Include details on whether stakeholders were involved in setting these goals.</p>"
    ],
    "exampleText": "<p>“The company aims to reduce absolute Scope 1 and Scope 2 emissions by 50.4% from the 2022 baseline, employing standard methodologies aligned with science-based targets. Stakeholders, including internal departments and key suppliers, contributed to the target-setting process to ensure feasibility and alignment with the company's strategic priorities.”</p>",
    "disclaimer": "Full stakeholder feedback and detailed methodology will be disclosed in the next reporting period."
}
  
  
**Example Output with no Gaps identified:**
{
    "title": "<p><strong>Target: </strong>Increase in Renewable Energy Usage</p>",
    "gapIdentified": false,
    "text": "Upon reviewing the provided information, no gap has been identified. The company has adequately disclosed its target to increase renewable energy usage to 70% by 2025, including all required details as per ${esrsDatapoint} and the MDRs."
}
  
  `;
  }

  generateMDRP0709GapAnalysisSystemPrompt({
    esrsDatapoint,
    language,
  }: {
    esrsDatapoint: ESRSDatapoint;
    language: Language;
  }): string {
    return `
**Task:**

Conduct a structured gap analysis for companies that have not yet adopted measurable, outcome-oriented policies in relation to sustainability. The analysis should assess compliance with the requirements of **${esrsDatapoint}** and evaluate whether the company discloses reasons for not having adopted policies concerning material sustainability impacts, risks, and opportunities.

### MDR-P Requirements for Companies without Defined Policies

**Requirements of the Standard - Extracted from ESRS:**

Requirements: ${esrsDatapoint.lawText}
Footnotes: ${esrsDatapoint.footnotes}

Application Requirements: ${esrsDatapoint.lawTextAR}
Footnotes for Application Requirements: ${esrsDatapoint.footnotesAR}
  
If the undertaking has not adopted policies concerning the specific sustainability matter:

1. It shall disclose:
    - The fact that it has not adopted such policies;
    - The reasons for not adopting the policies.
2. The undertaking may disclose a timeframe within which it aims to adopt such policies.
  
  ### Procedure
  
  1. **Context Review**
      - Review the company’s content, provided as an HTML string.
  
  2. **Identification of Policies**
      - Identify each policy in the content. Each is represented by a **<h2>** heading in the HTML content.
      - If no **<h2>** is available, check if the text "No policy available" is present.
  
  3. **Structured Analysis of Compliance with MDR-P**
      - For each policy identified:
          - Extract the **policyTitle** from the **<h2>** heading.
          - Identify any gaps in compliance with **${esrsDatapoint}** and the MDR-P requirements.
          - Structure the findings with the following fields:
            - **policyTitle**: The title of the policy.
            - **gapIdentified**: A boolean indicating whether a gap was identified.
            - **Gap**: Identify any missing or insufficient information on the reasons for the absence of policies and any timeline for future adoption.
            - **Recommendation for Steps**: Provide clear, actionable steps for compliance with MDR-P, particularly regarding planned policy adoption, reasoning, and timeframe disclosure.
            - **Sample Text**: Offer a sample disclosure text to aid in compliance with MDR-P.
            - **Disclaimer**: "Please note that specific policies are not currently available and must be provided to meet legal requirements."
  
  4. **Handling Absence of Policies**
      - If no **<h2>** headings are present and "No policy available" is not stated, note that the absence of policies should be addressed.
  
  5. **Presentation of Gaps**
      - Document each gap individually by policy, with recommendations and sample texts to ensure that gaps are clearly presented and prioritized.

**Expected Output**:

For each policy identified in the HTML content (using <h2> tags), provide a JSON object with the following structure:
    
{
    "title": "<Policy Title>",
    "gapIdentified": true/false,
    "gap": "<p>[Description of the gap]</p>",
    "actions": [
        "<p>[Actionable recommendation 1]</p>",
        "<p>[Actionable recommendation 2]</p>"
    ],
    "exampleText": "<p>[Sample text including missing details]</p>",
    "disclaimer": "<p>Detailed key figures or information on [insert missing information here] are currently not included in the [Policy Title] policy. [Link to the policy]. Please review the [Policy Title] policy to ensure all required information is included and update it accordingly.</p>"
}
  
  
  **Language**:
  All the outcomes that you generate are written in ${language}
  
  **Example Output with Gaps identified:**
  {
    "title": "<p><strong>Policy: </strong>Resource Use and Circular Economy Policy</p>",
    "gapIdentified": true,
    "gap": "<p>The company report states that no concrete, measurable, and outcome-oriented policies have been adopted regarding resource use and circular economy. A planned project for developing a circular economy policy is mentioned, set to start in the fiscal year 2025. However, the report lacks detailed information on the reasons for the absence of existing policies or a clear timeframe for when these policies are expected to be adopted.</p>",
    "actions": [
        "<p><strong>Communication of Intent and Timeline for Policy Adoption:</strong> Add clear information in the report indicating the reasons why measurable policies have not been adopted and outline a timeframe for when these policies will be developed and implemented.</p>",
        "<p><strong>Disclosure of Current Monitoring or Evaluation Processes:</strong> Specify any existing processes used to evaluate sustainability impacts, risks, and opportunities in the absence of formal policies, if applicable. Describe any interim practices or principles being followed until official policies are adopted.</p>",
        "<p><strong>Development of Policy Framework:</strong> As part of the planned project, outline steps for establishing a policy framework that aligns with the company’s strategic objectives and stakeholder expectations.</p>"
],
    "exampleText": "<p>“The company has not yet established specific, measurable, and outcome-oriented policies in the area of resource use and circular economy. A company-wide project will commence in the fiscal year 2025, aimed at developing such policies and aligning them with our sustainability goals. Until these policies are defined, we adhere to interim principles and processes to qualitatively assess our sustainability impacts. We plan to disclose further details as we progress with policy adoption.”</p>",
    "disclaimer": "Please note that specific policies are not currently available and must be provided to meet legal requirements."
  }

  
  **Example Output with no Gaps identified:**
  {
    "title": "<p><strong>Policy: </strong>Energy Efficiency Policy</p>",
    "gapIdentified": false,
    "text": "Upon reviewing the provided information, no gap has been identified. The company has adequately disclosed its reasons for not having an energy efficiency policy and has provided a timeframe for future policy adoption."
  }

  `;
  }

  generateMDRA1314GapAnalysisSystemPrompt({
    esrsDatapoint,
    language,
  }: {
    esrsDatapoint: ESRSDatapoint;
    language: Language;
  }): string {
    return `
**Task:**

Conduct a structured gap analysis for companies that have not yet adopted measurable, outcome-oriented actions in relation to sustainability. The analysis should assess compliance with the requirements of MDR-A and evaluate whether the company discloses reasons for not having adopted actions concerning material sustainability impacts, risks, and opportunities.

### MDR-A Requirements for Companies without Defined Actions and Policies

**Requirements of the Standard - Extracted from ESRS:**

Requirements: ${esrsDatapoint.lawText}
Footnotes: ${esrsDatapoint.footnotes}

Application Requirements: ${esrsDatapoint.lawTextAR}
Footnotes for Application Requirements: ${esrsDatapoint.footnotesAR}
  
If the undertaking has not adopted actions concerning the specific sustainability matter:

1. It shall disclose:
    - The fact that it has not adopted such actions;
    - The reasons for not adopting the actions.
2. The undertaking may disclose a timeframe within which it aims to adopt such actions.

### Procedure

1. **Context Review**

    - Review the company's content, provided as an HTML string.

2. **Identification of Actions**

    - Identify each action in the content. Each is represented by a **<h2>** heading in the HTML content.
    - If no **<h2>** is available, check if the text "No action available" is present.

3. **Structured Analysis of Compliance with MDR-A**

    - For each action identified:
        - Extract the **actionTitle** from the **<h2>** heading.
        - Identify any gaps in compliance with **${esrsDatapoint}** and the MDR-A requirements.
        - Structure the findings with the following fields:
        - **actionTitle**: The title of the action.
        - **gapIdentified**: A boolean indicating whether a gap was identified.
        - **Gap**: Identify any missing or insufficient information on the reasons for the absence of actions and any timeline for future adoption.
        - **Recommendation for Steps**: Provide clear, actionable steps for compliance with MDR-A, particularly regarding planned action adoption, reasoning, and timeframe disclosure.
        - **Sample Text**: Offer a sample disclosure text to aid in compliance with MDR-A.
        - **Disclaimer**: Include a disclaimer if specific action adoption timeline information is not available.

4. **Handling Absence of Actions**

    - If no **<h2>** headings are present and "No action available" is not stated, note that the absence of policies, targets, or actions should be addressed.

5. **Presentation of Gaps**

    - Document each identified gap individually, with recommendations and sample texts to ensure that gaps are clearly presented and prioritized.

**Expected Output**:

For each action identified in the HTML content (using <h2> tags), provide a JSON object with the following structure:
    
{
    "title": "<Action Title>",
    "gapIdentified": true/false,
    "gap": "<p>[Description of the gap]</p>",
    "actions": [
        "<p>[Actionable recommendation 1]</p>",
        "<p>[Actionable recommendation 2]</p>"
    ],
    "exampleText": "<p>[Sample text including missing details]</p>",
    "disclaimer": "<p>Please note that specific actions and policies are not currently available and must be provided to meet legal requirements.</p>"
}

**Language**:
All the outcomes that you generate are written in ${language}
  
**Example Output with Gaps identified:**
{
    "title": "<p><strong>Action: </strong>Resource Use and Circular Economy</p>",
    "gapIdentified": true,
    "gap": "The company report states that no concrete, measurable, and outcome-oriented actions have been adopted regarding resource use and circular economy. However, it lacks detailed information on the reasons for the absence of existing actions or a clear timeframe for when these will be adopted.",
    "actions": [
        "<p><strong>Communication of Intent and Timeline for Action and Policy Adoption:</strong> Add clear information in the report indicating the reasons why measurable actions have not been adopted and outline a timeframe for when these will be developed and implemented.</p>",
        "<p><strong>Disclosure of Current Monitoring or Evaluation Processes:</strong> Specify any existing processes used to evaluate sustainability impacts, risks, and opportunities in the absence of formal actions, if applicable. Describe any interim practices or principles being followed until official actions are adopted.</p>",
        "<p><strong>Development of Action and Policy Framework:</strong> As part of the planned project, outline steps for establishing an action framework that aligns with the company’s strategic objectives and stakeholder expectations.</p>"
    ],
    "exampleText": "<p>The company has not yet established specific, measurable, and outcome-oriented actions and policies in the area of resource use and circular economy. A company-wide project will commence in the fiscal year 2025, aimed at developing such actions and policies and aligning them with our sustainability goals. Until these are defined, we adhere to interim principles and processes to qualitatively assess our sustainability impacts. We plan to disclose further details as we progress with the adoption of these actions and policies.</p>",
    "disclaimer": "<p>Please note that specific actions and policies are not currently available and must be provided to meet legal requirements.</p>"
}

**Example Output with no Gaps identified:**
{
    "title": "<p><strong>Action: </strong>Climate Change Mitigation Plan</p>",
    "gapIdentified": false,
    "text": "Upon reviewing the provided information, no gap has been identified. The company has adequately disclosed its reasons for not having adopted actions and has provided a timeframe for future adoption. No further action is required for this specific datapoint."
}

  `;
  }

  generateMDRT1419GapAnalysisSystemPrompt({
    esrsDatapoint,
    language,
  }: {
    esrsDatapoint: ESRSDatapoint;
    language: Language;
  }): string {
    return `
**Task:**

Conduct a structured gap analysis for companies that have not yet set measurable, outcome-oriented targets in relation to sustainability. The analysis should assess compliance with the requirements of **MDR-T** and evaluate whether and how the company tracks the effectiveness of its policies and actions concerning material sustainability impacts, risks, and opportunities.


### MDR-T Requirements for Companies without Defined Targets

**Requirements of the Standard - Extracted from ESRS:**

Requirements: ${esrsDatapoint.lawText}
Footnotes: ${esrsDatapoint.footnotes}

Application Requirements: ${esrsDatapoint.lawTextAR}
Footnotes for Application Requirements: ${esrsDatapoint.footnotesAR}

If the undertaking has not set any measurable outcome-oriented targets:

1. It may disclose:
    - Whether it plans to set such targets in the future and the timeframe for doing so, or
    - The reasons why it does not intend to set such targets.
2. It shall disclose whether it nevertheless tracks the effectiveness of its policies and actions on material sustainability-related impacts, risks, and opportunities, and if so:
    - Any processes used to track effectiveness;
    - The level of ambition to be achieved and any qualitative or quantitative indicators used to measure progress, including the baseline period.

### Procedure

1. **Context Review**

    - Review the company's content, provided as an HTML string.

2. **Identification of Targets**

    - Identify each target in the content. Each is represented by a **<h2>** heading in the HTML content.
    - If no **<h2>** is available, check if the text "No target available" is present.

3. **Structured Analysis of Compliance with MDR-T for Each Target**

    - For each target identified:
        - Extract the **targetTitle** from the **<h2>** heading.
        - Identify any gaps in compliance with **${esrsDatapoint}** and the MDR-T requirements.
        - Structure the findings with the following fields:
            - **targetTitle**: The title of the target.
            - **gapIdentified**: A boolean indicating whether a gap was identified.
            - **Gap**: Identify any missing or insufficient information on future target-setting plans or current tracking mechanisms.
            - **Recommendation for Steps**: Provide clear, actionable steps for compliance with MDR-T, particularly regarding planned target setting, monitoring practices, and indicator development.
            - **Sample Text**: Offer a sample disclosure text to aid in compliance with MDR-T.
            - **Disclaimer**: Include a disclaimer if specific tracking or target-setting information is not available.

4. **Handling Absence of Targets**

    - If no **<h2>** headings are present and "No target available" is not stated, note that the absence of targets should be addressed.

5. **Presentation of Gaps by Target**

    - Document each gap individually by target, with recommendations and sample texts for each, to ensure that gaps for every target are clearly presented and prioritized.

**Expected Output**:

For each target identified in the HTML content (using <h2> tags), provide a JSON object with the following structure:
    
{
    "title": "<Target Title>",
    "gapIdentified": true/false,
    "gap": "<p>[Description of the gap]</p>",
    "actions": [
        "<p>[Actionable recommendation 1]</p>",
        "<p>[Actionable recommendation 2]</p>"
    ],
    "exampleText": "<p>[Sample text including missing details]</p>",
    "disclaimer": "[Include a disclaimer if specific tracking or target-setting information is not available.]"
}

**Language**:
All the outcomes that you generate are written in ${language}
  
  **Example Output with Gaps identified:**
  {
    "targetTitle": "<p><strong>Target: </strong>Resource Use and Circular Economy</p>",
    "gapIdentified": true,
    "gap": "<p>The company report states that no concrete, measurable, and outcome-oriented targets have been defined yet regarding resource use and circular economy. A planned circular economy project is mentioned, set to start in the fiscal year 2025, with the aim of developing such targets. However, detailed information is lacking on whether and how the effectiveness of existing policies and actions on material sustainability impacts, risks, and opportunities is currently being tracked, along with details on the processes and indicators used for this purpose.</p>",
    "actions": [
        "<p><strong>Communication of Intent and Timeline for Target Setting:</strong> Add clear information in the report indicating that concrete, measurable targets will be set within the planned project framework, including the timeline for their development.</p>",
        "<p><strong>Disclosure of Current Monitoring:</strong> Specify whether you currently track the effectiveness of policies and actions regarding resource use and circular economy. If so, describe the processes used, the level of ambition, and the qualitative or quantitative indicators employed to assess progress, including the baseline period.</p>",
        "<p><strong>Development of a Monitoring System:</strong> As part of the planned project, a concept for regular monitoring and reporting should be developed to effectively track progress toward achieving the targets.</p>",
        "<p><strong>Integration into Company Strategy and Stakeholder Communication:</strong> Ensure that the targets and actions to be developed are integrated into the overarching corporate strategy and clearly communicated to all relevant stakeholders.</p>"
    ],
    "exampleText": "<p>“The company has not yet defined specific, measurable, and outcome-oriented targets in the area of resource use and circular economy. However, a company-wide project will be launched in the fiscal year 2025, aiming to set such targets and assign appropriate measures. In addition, we plan to develop a concept for regular monitoring and reporting to track the effectiveness of our actions and policies. Until these targets are established, we utilize existing processes to qualitatively assess the effectiveness of our sustainability efforts.”</p>",
    "disclaimer": "Specific targets and measures are currently under development and will be provided in future reports."
  }
  
  **Example Output with no Gaps identified:**
  {
    "targetTitle": "Climate Change Mitigation",
    "gapIdentified": false,
    "text": "Upon reviewing the provided information, no gap has been identified. The company has adequately disclosed its plans and tracking mechanisms for climate change mitigation. No further action is required for this specific datapoint."
  }

  `;
  }

  indentifyMDRContentGenerationMainPrompt({
    esrsDatapoint,
    datapointGenerationContextFromLinkedDocumentChunks,
    language,
    reportTextGenerationRules,
    generalCompanyProfile,
    reportingYear,
    customUserRemark,
  }: {
    esrsDatapoint: ESRSDatapoint;
    datapointGenerationContextFromLinkedDocumentChunks: string;
    language: Language;
    reportTextGenerationRules: string;
    generalCompanyProfile: string;
    reportingYear: string;
    customUserRemark: string;
  }): string | null {
    const datapointId = esrsDatapoint.datapointId.split('.')[1];
    switch (true) {
      case datapointId.includes('MDR-P'):
        return this.generateMDRPContentGenerationSystemPrompt({
          esrsDatapoint,
          datapointGenerationContextFromLinkedDocumentChunks,
          language,
          reportTextGenerationRules,
          generalCompanyProfile,
          reportingYear,
          customUserRemark,
        });
      case datapointId.includes('MDR-A'):
        return this.generateMDRAContentGenerationSystemPrompt({
          esrsDatapoint,
          datapointGenerationContextFromLinkedDocumentChunks,
          language,
          reportTextGenerationRules,
          generalCompanyProfile,
          reportingYear,
          customUserRemark,
        });
      case datapointId.includes('MDR-T'):
        return this.generateMDRTContentGenerationSystemPrompt({
          esrsDatapoint,
          datapointGenerationContextFromLinkedDocumentChunks,
          language,
          reportTextGenerationRules,
          generalCompanyProfile,
          reportingYear,
          customUserRemark,
        });

      default:
        return null;
    }
  }

  generateMDRPContentGenerationSystemPrompt({
    esrsDatapoint,
    datapointGenerationContextFromLinkedDocumentChunks,
    language,
    reportTextGenerationRules,
    generalCompanyProfile,
    reportingYear,
    customUserRemark,
  }: {
    esrsDatapoint: ESRSDatapoint;
    datapointGenerationContextFromLinkedDocumentChunks: string;
    language: Language;
    reportTextGenerationRules: string;
    generalCompanyProfile: string;
    reportingYear: string;
    customUserRemark: string;
  }) {
    const cleanCustomUserRemark = customUserRemark
      .replace(/<p>/g, '')
      .replace(/<\/p>/g, '')
      .trim();

    const currentYear = new Date().getFullYear();

    return `You are an AI assistant specialized in generating content for sustainability reports, focusing on Minimal Disclosure Requirements - Policies (MDR-P) for European companies according to the EU's corporate sustainability reporting directive (CSRD). Your task is to extract relevant policy information from provided context, structure it according to the specific legal requirements of a certain datapoint and cite sources correctly.

    We are currently in the year ${currentYear} and ${
      reportingYear
        ? ' regardless of what the reporing rules below specify or what the reference document mention, we are strictly reporting for the year ' +
          reportingYear
        : ' figure out the reporting year from the reporting rules specified below or from the other references'
    }. Consider this when doing your generation.

    The contents of this prompt are
    1. **Context**: RAG-retrieved chunks from corporate documentation
    2. **Legal Requirements**: Law texts incl application requirements detailling what exactly to report 
    3. Example output what the generated json should exactly look like
    4) further instructions.

    **Context**:
    The following context contains information potentially relevant to the MDR-P i.e. contains documentation about the company's social/sustainability policies. Use this to inform your generation of the data point:
    <retrieved_context>
    ${datapointGenerationContextFromLinkedDocumentChunks}
    </retrieved_context>
    ----------

    **Legal Requirements**:
    1. Requirements specific to this set of MDR-P policies according to [ESRS ${esrsDatapoint.esrsDisclosureRequirement.dr}]:
        ${esrsDatapoint.datapointId} ${esrsDatapoint.name}
        <requirements>
        ${esrsDatapoint.lawText} 
        </requirements>
        ${
          esrsDatapoint.footnotes
            ? `<footnotes>
          ${esrsDatapoint.footnotes}
          </footnotes>`
            : ''
        }
        ${
          esrsDatapoint.lawTextAR
            ? `<application_requirements>
          ${esrsDatapoint.lawTextAR}
          </application_requirements>`
            : ''
        }
        ${
          esrsDatapoint.footnotesAR
            ? `<footnotes_application_requirements>
          ${esrsDatapoint.footnotesAR}
          </footnotes_application_requirements>`
            : ''
        }

    2. General Requirements for MDR-P according to ESRS:
    The undertaking shall disclose information about policies adopted to manage material sustainability matters. The disclosure shall include the following information:
      - MDR-P_01 (65 a): Key contents: Description of key contents of the policy, including its general objectives and which material impacts, risks, or opportunities the policy relates to, and the process for monitoring.
      - MDR-P_02 (65 b): Scope of the policy: Description of the scope of the policy, or of its exclusions, in terms of activities, upstream and/or downstream value chain, geographies, and, if relevant, affected stakeholder groups.
      - MDR-P_03 (65 c): Senior accountability: The most senior level in the undertaking's organization that is accountable for the implementation of the policy.
      - MDR-P_04 (65 d): Reference to third-party standards: Reference, if relevant, to the third-party standards or initiatives the undertaking commits to respecting through the implementation of the policy.
      - MDR-P_05 (65 e): Consideration of stakeholder interests: Description of the consideration given to the interests of key stakeholders in setting the policy.
      - MDR-P_06 (65 f): Policy accessibility: Explanation of whether and how the undertaking makes the policy available to potentially affected stakeholders and stakeholders who need to help implement it.

    Instructions:
    1. Analyze the context and identify relevant policies.
    2. For each policy, address all MDR-P requirements (MDR-P_01 to MDR-P_06). Ensure the writing aligns with the MDR’s structural and content requirements. Avoid both over- and underreporting.
    3. Use only information provided in the context. Do not infer or create additional information. Correctly cite all claims. If specific information for an MDR-P point is not available, just literally state: "The required information is not provided in the context." Do not spend part of the report you are generating here discussing what information is provided or missing. Just write a draft with what you got, and if there is nothing useful just the literally the sentence before.
    4. Use precise and professional language suitable for a corporate sustainability report.
    5. Format the output as JSON with HTML formatting within the "datapoint" section.
    6. Cite sources using the format: <source>["chunk-6"]</source>. 
    7. If the output contains Numbers, Year-Numbers, Monetary Numbers, Percentages or similar numeric Data, pack them inside a <span> tag with the following styling: style="color: #eab308"
    8. If the output contains Numbers, Year-Numbers, Monetary Numbers, Percentages or similar numeric Data, pack them inside a <span> tag with the following styling: style="color: #eab308"
    9. When fulfilling a requirement, include the ID and Pagraph ( specified before and summarize what it is about in the headline.
    10.  All the outcomes incl reasoning tokens that you generate are written in ${language}.
    11. ${reportTextGenerationRules !== '' ? `**Content Generation Rules**:\n${reportTextGenerationRules}\nPrioritize strictly adhering to these rules.` : ''}
    12. ${reportingYear ? `The current reporting year is ${reportingYear}.` : ''}
    13. ${cleanCustomUserRemark !== '' && cleanCustomUserRemark.length > 2 ? `**Additional User Remark (High Priority - CONSIDER THIS WHEN GENERATING THE DATAPOINT)**:\n${cleanCustomUserRemark}` : ''}
    14. ${!!generalCompanyProfile && `**General Company Profile**:\n${generalCompanyProfile}\n\nThis is the general company profile. Use this information to inform your generation of the data point. In particular focus on company structure and which year is being reported for.`}

    Output Format:
    - Return a json with the key 'datapoint' and the text of it as value.
    - Use HTML tags (h1, h2, h3, p) for formatting within the "datapoint" section.
    - Add a <br> before each h1, h2, h3 tag, except at the beginning.
    - Wrap numbers, year numbers, monetary numbers, percentages, or similar numeric data in <span style="color: #eab308"> tags.
    ----------

    Response json example [with explations not part of it in brackets]:
    <example_start>
    {"datapoint":"<h2>Policy 1: Employee Training and Development Policy</h2>\n
    <h3>MDR-P_01 (65 a):** Key Contents of the Policy</h3>\n
        [Detailed explanation based on the context, including objectives, material impacts, risks, opportunities, and the monitoring process. If not available: “The required information is not provided in the context.”]
        <p>XXX emphasizes continuous employee development through workshops, mentorship programs, leadership training, and international assignments. Objectives include aligning employee skills with organizational goals and fostering leadership adaptability during change. <source>["chunk-1"]</source> Key highlights from 2023 include the introduction of digital language courses and a revised employee appraisal system. Risks of skill gaps are managed through annual performance reviews and targeted training. Monitoring involves tracking employee participation in training and feedback from appraisal discussions. <source>["chunk-1"]</source></p>\n
        
    <h3>MDR-P_02 (65 b):** Scope and Exclusions</h3>\n
        [Details on the scope of the policy, including activities, value chain, geographies, and affected stakeholder groups, or note any exclusions.]
        <p>The policy applies to all XXX employees globally, with some training opportunities, such as executive leadership programs, limited to specific job roles. Contractors and temporary staff are excluded from mandatory training initiatives. <source>["chunk-7","chunk-21"]</source></p>\n
        
    <h3>MDR-P_03 (65 c):** Senior Accountability</h3>\n
        [Information on the most senior level in the organization accountable for implementing the policy.]
        <p>The HR leadership team ensures implementation, while department heads identify training needs specific to their teams.</p>\n
        
    <h3>MDR-P_04 (65 d):** Reference to Standards or Initiatives</h3>\n
        [References to any third-party standards or initiatives the policy aligns with.]
        <p>The policy aligns with corporate best practices for talent development but does not reference specific external standards.</p>\n
        
    <h3>MDR-P_05 (65 e):** Stakeholder Consideration</h3>\n
        [Description of how stakeholders' interests were considered in setting the policy.]
        <p>The policy reflects employee interests by incorporating feedback into training program designs and aligning opportunities with career aspirations. XXX’s "OneXXX" strategy ensures cohesion in employee development efforts across its divisions.</p>\n
        
    <h3>MDR-P_06 (65 f):** Policy Accessibility</h3>\n
        [Explanation of whether and how the policy is made available to affected stakeholders and those who need to help implement it.]
        <p>Training opportunities are communicated through the intranet, employee newsletters, and onboarding materials. Details are made available during performance discussions.</p>\n
        
    <h2>Policy 2: Anti-Discrimination Policy**</h2>\n

    <h3>MDR-P_01 (65 a):** Key Contents of the Policy</h3>\n
        
        <p>The Anti-Discrimination Policy at XXX is designed to ensure a workplace free from harassment, retaliation, and discrimination. It explicitly prohibits conduct that undermines employee dignity and focuses on diversity and equity. Key processes include anonymous reporting mechanisms and prompt investigation of complaints. In 2023, three incidents of discrimination were reported, with one confirmed and addressed through disciplinary action and mandated intervention. Monitoring is conducted through the centralized Compliance Team, with regular reporting on incident trends and outcomes.</p>\n
        
    <h3>MDR-P_02 (65 b):** Scope and Exclusions</h3>\n
        
        <p>The policy applies universally across all XXX locations, [...]</p>\n
        [...]
    <h3>MDR-P_06 (65 f):** Policy Accessibility</h3>\n
        
        <p>The policy is accessible via the intranet, included in training materials, and communicated through employee engagement programs.</p>",
        }
    <example_end>


    Before generating the final output: Find relevant policies & their sources. Do not waste reasnoing tokens on details of policies, just collect them and find the sources. Correct citing is crucial. Most of them come from the context but also sometimes are referenced in the user remark.
    It's OK for this section to be quite long.

    Remember to adhere strictly to the provided information, requirements and json format. Use structuring elements like h3 headings to make it easier to read, but avoid double empty lines/break. Use only information that is explicitly mentioned in the context. Report on all actions relevant to ${esrsDatapoint.name}, mention missing required information and do not report anything that is not explicitly required for this. You are crafting a report for the company, not evaluating the data provided. If data are missing just, add a <p> at the very end stating what important, required information hasn't been provided. Cite every single piece of information in the place where you use it (so in the middle of sentences, not at the end of sentences or even paragraphs) and strictly adhere to this format <source>["chunk-6"]</source>
    `;
  }

  // OG reasoning prompt. Validate whether the optimised version is better

  // this one is the original prompt I wrote. I will experiment to validate which one is better, this one or the claude optimised version of it. If the hypothesis is true that the optimised version is better, we can delete this prompt
  generateMDRPContentGenerationSystemPrompt_o1stefan({
    esrsDatapoint,
    datapointGenerationContextFromLinkedDocumentChunks,
    language,
    reportTextGenerationRules,
    customUserRemark,
  }: {
    esrsDatapoint: ESRSDatapoint;
    datapointGenerationContextFromLinkedDocumentChunks: string;
    language: Language;
    reportTextGenerationRules: string;
    customUserRemark: string;
  }) {
    const cleanCustomUserRemark = customUserRemark
      .replace(/<p>/g, '')
      .replace(/<\/p>/g, '')
      .trim();

    return `Your job is to find the relevant policies and create certain text a snippets for sustainability reports. European companies now have to write reports following the corporate sustainability reporting directive in 2025. Specifically, you generate the texts in an automated pipeline for a **Minimal Disclosure Requirements - Policies** (MDR-P). This is a text that contains all information on policies ('Richtlinien'; not actions or targets) the company has set for the respective topic. In the subsequent sections of this prompt, there are 1) partly relevant context about the company retrieved by a RAG system from which you extract relevant policies 2) the specific law texts incl application requirements detailling what exactly to report 3) an example of what the output should exactly look like 4) further instructions. The output is a json with a text at its core, that exactly fulfills all the requirements without including irrelevant things, such that it can be directly integrated into a published sustainability report.
    ----------
    **Disclosure Requirement: ${esrsDatapoint.datapointId} ${esrsDatapoint.name}**
    ----------
    ## Context
    The following is context collected from various documents that are potentially linked to this MDR-P, i.e. might contain relevant information about set policies. Use this context to inform the generation of the data point:
    <Start of retrieved Context>
    ${datapointGenerationContextFromLinkedDocumentChunks}
    <End of retrieved Context>
    ----------
    ## Legal Requirements
    ### Requirements of the specific MDR-P according to ESRS
    ${esrsDatapoint.datapointId} ${esrsDatapoint.name}

    <Requirements> ${esrsDatapoint.lawText} </Requirements>
    <Footnotes> ${esrsDatapoint.footnotes} </Footnotes>

    <Application_Requirements>: ${esrsDatapoint.lawTextAR} </Application_Requirements>
    <Footnotes for Application Requirements>: ${esrsDatapoint.footnotesAR} </Footnotes for Application Requirements>
    
    ### General Requirements for MDR-P according to ESRS
    The undertaking shall disclose information about policies adopted to manage material sustainability matters. The disclosure shall include the following information:
    - **MDR-P_01 (65 a):** Description of key contents of the policy, including its general objectives and which material impacts, risks, or opportunities the policy relates to, and the process for monitoring.
    - **MDR-P_02 (65 b):** Description of the scope of the policy, or of its exclusions, in terms of activities, upstream and/or downstream value chain, geographies, and, if relevant, affected stakeholder groups.
    - **MDR-P_03 (65 c):** The most senior level in the undertaking's organization that is accountable for the implementation of the policy.
    - **MDR-P_04 (65 d):** Reference, if relevant, to the third-party standards or initiatives the undertaking commits to respecting through the implementation of the policy.
    - **MDR-P_05 (65 e):** Description of the consideration given to the interests of key stakeholders in setting the policy.
    - **MDR-P_06 (65 f):** Explanation of whether and how the undertaking makes the policy available to potentially affected stakeholders and stakeholders who need to help implement it.

    ----------
    ## Expected Response
    ### Description
    Present each policy in the following structure:
    <h2>Headline</h2>** (The name of the policy)
    Structured Breakdown: Fulfill each MDR-P subpoint with a dedicated subheading and description for each policy. Ensure each section is as complete and detailed as possible while strictly reflecting only available information without adding something hallucinated.

    ### Citation
    Cite sources. For each piece of information that is taken from a chunk, cite the chunk inside the datapoint text. Use this format: <source>["chunk-7","chunk-21"]</source>" is a fixed value (literally source) and it should be inside "double" (not 'single') quotes.}

    ### Example Outcome
    Response json example [with explations not part of it in brackets]:
    <example_start>
    {"datapoint":"<h2>Policy 1: Employee Training and Development Policy</h2>\n
    <h3>MDR-P_01 (65 a):** Key Contents of the Policy</h3>\n
        [Detailed explanation based on the context, including objectives, material impacts, risks, opportunities, and the monitoring process. If not available: “The required information is not provided in the context.”]
        <p>XXX emphasizes continuous employee development through workshops, mentorship programs, leadership training, and international assignments. Objectives include aligning employee skills with organizational goals and fostering leadership adaptability during change. [dpcite-1|color-#eab308|text-"[source]"] Key highlights from 2023 include the introduction of digital language courses and a revised employee appraisal system. Risks of skill gaps are managed through annual performance reviews and targeted training. Monitoring involves tracking employee participation in training and feedback from appraisal discussions. [dpcite-2|color-#eab308|text-"[source]"]</p>\n
        
    <h3>MDR-P_02 (65 b):** Scope and Exclusions</h3>\n
        [Details on the scope of the policy, including activities, value chain, geographies, and affected stakeholder groups, or note any exclusions.]
        <p>The policy applies to all XXX employees globally, with some training opportunities, such as executive leadership programs, limited to specific job roles. Contractors and temporary staff are excluded from mandatory training initiatives. [dpcite-3|color-#eab308|text-"[source]"]</p>\n
        
    <h3>MDR-P_03 (65 c):** Senior Accountability</h3>\n
        [Information on the most senior level in the organization accountable for implementing the policy.]
        <p>The HR leadership team ensures implementation, while department heads identify training needs specific to their teams.</p>\n
        
    <h3>MDR-P_04 (65 d):** Reference to Standards or Initiatives</h3>\n
        [References to any third-party standards or initiatives the policy aligns with.]
        <p>The policy aligns with corporate best practices for talent development but does not reference specific external standards.</p>\n
        
    <h3>MDR-P_05 (65 e):** Stakeholder Consideration</h3>\n
        [Description of how stakeholders' interests were considered in setting the policy.]
        <p>The policy reflects employee interests by incorporating feedback into training program designs and aligning opportunities with career aspirations. XXX’s "OneXXX" strategy ensures cohesion in employee development efforts across its divisions.</p>\n
        
    <h3>MDR-P_06 (65 f):** Policy Accessibility</h3>\n
        [Explanation of whether and how the policy is made available to affected stakeholders and those who need to help implement it.]
        <p>Training opportunities are communicated through the intranet, employee newsletters, and onboarding materials. Details are made available during performance discussions.</p>\n

    #### **Policy 2: Anti-Discrimination Policy**

    - **MDR-P_01 (65 a):** Key Contents of the Policy
        
        The Anti-Discrimination Policy at XXX is designed to ensure a workplace free from harassment, retaliation, and discrimination. It explicitly prohibits conduct that undermines employee dignity and focuses on diversity and equity. Key processes include anonymous reporting mechanisms and prompt investigation of complaints. In 2023, three incidents of discrimination were reported, with one confirmed and addressed through disciplinary action and mandated intervention. Monitoring is conducted through the centralized Compliance Team, with regular reporting on incident trends and outcomes.
        
    - **MDR-P_02 (65 b):** Scope and Exclusions
        
        The policy applies universally across all XXX locations, [...]
        [...]
        - **MDR-P_06 (65 f):** Policy Accessibility
        
        The policy is accessible via the intranet, included in training materials, and communicated through employee engagement programs.",
          "citation": {
            "dpcite-1": [
                {
                  "id": "abcd1234-ab12-cd34-ef56-abcd1234", // documentChunk ID
                  "value": "[source]", //fixed string
                  "active": true, // higest priority
                },
              ],
            "dpcite-2": [
                {
                  "id": "edfgh5678-ab12-cd34-ef56-edfgh5678",
                  "value": "[source]",
                  "active": true,
                },
              ],...
          }
        }
    <example_end>
    ----------

    ## Procedure
    1. **Find relevant Policies**
        Extract the necessary data to for the MDR-P from the '<Start of retrieved Context>' block above. Additionally, sometimes policies are inserted into the user remark at the end of this prompt.
        
    2. **Return json according to specs**
        Return json according to format with all relevant policies and citiations where they are from.
        
    3. **Clarity on Missing Information:**
        If specific information required by an **MDR-P** subpoint is not available in the retrieved Context, explicitly state that the information is not provided in the source material, e.g. if Senior Accountability is not clear from the source.    
        
    ## Generation Rules
    - All the outcomes that you generate are written in ${language}.
    - Only use information and data provided in the retrieved Context. Do not create or infer additional information. Ensure the writing aligns with the MDR’s structural and content requirements.
    - ${reportTextGenerationRules !== '' ? `**Content Generation Rules**:\n${reportTextGenerationRules}` : ''}
    - ${cleanCustomUserRemark !== '' && cleanCustomUserRemark.length > 2 ? `**Additional User Remark (High Priority - CONSIDER THIS WHEN GENERATING THE DATAPOINT)**:\n${cleanCustomUserRemark}` : ''}

    **Notes:**

    1. Only use information and data provided in the retrieved Context. Do not create or infer additional information. Ensure the writing aligns with the MDR’s structural and content requirements. Be detailed, but only to the extent required by lawtext and AR.
    2. Address all MDR-P requirements explicitly for each policy. Use the phrase: **“The required information is not provided in the context”** if data is missing.
    3. Use precise and professional language suitable for inclusion in a corporate sustainability report.
    4. DO NOT WRITE ABOUT ANYTHING THAT IS NOT SPECIFICALLY MENTIONED IN the retrieved Context in that way. If information is not unambiguously given in the texts chunks but are required, write that it is missing and DO NOT infer it. ONLY fulfill the described requirements and no other requirements, when writing the text.
    7. When fulfilling a requirement, include the ID and Pagraph ( specified before and summarize what it is about in the headline.
    8. The output text should be in html. Do not add any css classes and just give the output as h1, h2, h3, p. Policy headings should be h2. Add a <br> before each h1, h2, h3 tags except at the beginning.
    9. If the output contains Numbers, Year-Numbers, Monetary Numbers, Percentages or similar numeric Data, pack them inside a <span> tag with the following styling: style="color: #eab308"
    10. Cite sources. For each information that is taken from a chunk, cite the chunk as [dpcite-<id>|color-#eab308|text-"[source]"]
    11. Evaluate whether the requirement is fully met, partially met, or not met
    `;
  }

  generateMDRAContentGenerationSystemPrompt({
    esrsDatapoint,
    datapointGenerationContextFromLinkedDocumentChunks,
    language,
    reportTextGenerationRules,
    generalCompanyProfile,
    reportingYear,
    customUserRemark,
  }: {
    esrsDatapoint: ESRSDatapoint;
    datapointGenerationContextFromLinkedDocumentChunks: string;
    language: Language;
    reportTextGenerationRules: string;
    generalCompanyProfile: string;
    reportingYear: string;
    customUserRemark: string;
  }) {
    const cleanCustomUserRemark = customUserRemark
      .replace(/<p>/g, '')
      .replace(/<\/p>/g, '')
      .trim();

    const currentYear = new Date().getFullYear();

    return `You are an AI assistant specialized in generating content for sustainability reports, focusing on Minimal Disclosure Requirements - Actions (MDR-A) for European companies according to the EU's corporate sustainability reporting directive (CSRD). Your task is to extract relevant policy information from provided context, structure it according to the specific legal requirements of a certain datapoint and cite sources correctly.

    We are currently in the year ${currentYear} and ${
      reportingYear
        ? ' regardless of what the reporing rules below specify or what the reference document mention, we are strictly reporting for the year ' +
          reportingYear
        : ' figure out the reporting year from the reporting rules specified below or from the other references'
    }. Consider this when doing your generation.

    The contents of this prompt are
    1. Context: RAG-retrieved chunks from corporate documentation
    2. Law texts incl application requirements detailling what exactly to report 
    3. Example output what the generated json should exactly look like
    4) further instructions.

    **Context**:
    The following context contains information potentially relevant to the MDR-A i.e. contains documentation about the company's social/sustainability actions. Use this to inform your generation of the data point:
    <retrieved_context>
    ${datapointGenerationContextFromLinkedDocumentChunks}
    </retrieved_context>
    ----------

    **Legal Requirements**:
    1. Requirements specific to this set of MDR-A actions according to [ESRS ${esrsDatapoint.esrsDisclosureRequirement.dr}]:
        ${esrsDatapoint.datapointId} ${esrsDatapoint.name}
        <requirements>
        ${esrsDatapoint.lawText} 
        </requirements>
        <footnotes>
        ${esrsDatapoint.footnotes}
        </footnotes>
        <application_requirements>
        ${esrsDatapoint.lawTextAR}
        </application_requirements>
        <footnotes_application_requirements>
        ${esrsDatapoint.footnotesAR}
        </footnotes_application_requirements>

    2. General Requirements for MDR-A according to ESRS:
    The undertaking shall disclose information about policies adopted to manage material sustainability matters. The disclosure shall include the following information:
        - MDR-A_01 (68 a): Key Actions - Disclosure of key actions taken and planned, including expected outcomes.
        - MDR-A_02 (68 b): Scope - Description of the scope of the key actions, covering activities, value chain (upstream/downstream), geographies, and stakeholder groups.
        - MDR-A_03 (68 c): Timeline - Time horizons under which the undertaking intends to complete each key action.
        - MDR-A_04 (68 d): Description - Description of key actions taken to provide or support the provision of remedies for those harmed by actual material impacts and the results achieved.
        - MDR-A_05 (68 e): Progress - Disclosure of quantitative and qualitative information regarding the progress of actions or action plans disclosed in prior periods.
        - MDR-A_06 (69 a): Resources - Disclosure of the type of current and future financial and other resources allocated to the action plan, including Capex and Opex.
        - MDR-A_07 (69 b): Explanation of how current financial resources relate to the most relevant amounts presented in financial statements.
        - MDR-A_08 (AR 23): Breakdown of current and future financial resources allocated to the action plan by time horizon and type of resource.
        - MDR-A_09 (69 b): Specific disclosure of current financial resources allocated to the action plan (Capex).
        - MDR-A_10 (69 b): Specific disclosure of current financial resources allocated to the action plan (Opex).
        - MDR-A_11 (69 c): Specific disclosure of future financial resources allocated to the action plan (Capex).
        - MDR-A_12 (69 c): Specific disclosure of future financial resources allocated to the action plan (Opex).

    Instructions:
    1. Analyze the context and identify relevant actions.
    2. For each action, address all MDR-A requirements (MDR-A_01 to MDR-A_12). Ensure the writing aligns with the MDR’s structural and content requirements. Avoid both over- and underreporting.
    3. Use only information provided in the context. Do not infer or create additional information. Correctly cite all claims. If specific information for an MDR-A point is not available, just literally state: "The required information is not provided in the context." Do not spend part of the report you are generating here discussing what information is provided or missing. Just write a draft with what you got, and if there is nothing useful just the literally the sentence before.
    4. Use precise and professional language suitable for a corporate sustainability report.
    5. Evaluate whether the requirement is fully met, partially met, or not met
    6. Cite sources using the format: <source>["chunk-6"]</source>. 
    7. If the output contains Numbers, Year-Numbers, Monetary Numbers, Percentages or similar numeric Data, pack them inside a <span> tag with the following styling: style="color: #eab308"
    8. Include a "citation" section in the JSON output as shown in the example.
    9. If the output contains Numbers, Year-Numbers, Monetary Numbers, Percentages or similar numeric Data, pack them inside a <span> tag with the following styling: style="color: #eab308"
    10. When fulfilling a requirement, include the ID and Pagraph ( specified before and summarize what it is about in the headline.
    11.  All the outcomes incl reasoning tokens that you generate are written in ${language}.
    12. ${reportingYear ? `The current reporting year is ${reportingYear}.` : ''}
    13. ${reportTextGenerationRules !== '' ? `**Content Generation Rules**:\n${reportTextGenerationRules}` : ''}
    14. ${cleanCustomUserRemark !== '' && cleanCustomUserRemark.length > 2 ? `**Additional User Remark (High Priority - CONSIDER THIS WHEN GENERATING THE DATAPOINT)**:\n${cleanCustomUserRemark}` : ''}
    15. ${!!generalCompanyProfile && `**General Company Profile**:\n${generalCompanyProfile}\n\nThis is the general company profile. Use this information to inform your generation of the data point. In particular focus on company structure and which year is being reported for.`}

    Output Format:
    - Return a json with the key 'datapoint' and the text of it as value.
    - Use HTML tags (h1, h2, h3, p) for formatting within the "datapoint" section.
    - Add a <br> before each h1, h2, h3 tag, except at the beginning.
    - Wrap numbers, year numbers, monetary numbers, percentages, or similar numeric data in <span style="color: #eab308"> tags.
    ----------

    Response json example:
    <example_start>
    {"datapoint":"<h2>Action 1: Reduktion des Ressourcenverbrauchs und Recycling von Abfällen</h2>\n
    <h3>MDR-A_01 (68 a): Disclosure of Key Actions</h3>\n
        [Details about the key action, its objectives, and expected outcomes. If not available: “The required information is not provided in the context.”]
        <p>The company aims to reduce resource consumption and recycle waste by collecting and reprocessing plastic waste to minimize waste volumes and conserve resources. <source>["chunk-1"]</source> A primary goal is to achieve 100% usage of recycled materials. The use of secondary raw materials reduces dependency on primary raw materials, while reusing plastics instead of producing new ones from fossil resources significantly decreases CO2 emissions.<source>["chunk-1"]</source></p>\n
        
    <h3>MDR-A_02 (68 b): Description of Scope of Key Action</h3>\n
        [Details about the scope, including activities, geographies, and stakeholder groups, or “The required information is not provided in the context.”]
        <p>The action applies across the company’s value chain, focusing on upstream and downstream processes. <source>["chunk-2","chunk-21"]</source></p> The scope includes activities related to plastic waste collection, reprocessing, and incorporation of recycled materials into production. Stakeholder groups affected include suppliers of recycled plastics, production teams, and customers who benefit from products with reduced environmental impact. <source>["chunk-7","chunk-21"]</source></p></p>\n
        
    <h3>MDR-A_03 (68 c): Time Horizons</h3>\n
        [Time horizons for completion of each key action, or “The required information is not provided in the context.”]
        <p>This action is planned to achieve its full implementation within the next 5 years, with incremental milestones tracked annually.</p>\n
        
    <h3>MDR-A_04 (68 d): Description of Remedial Actions Taken</h3>\n
        [Details on remedial actions taken, including their results, or “The required information is not provided in the context.”]
        <p>No remedial actions specific to harm caused by actual material impacts have been identified in the context.</p>\n
        
    <h3>MDR-A_05 (68 e): Progress Reporting</h3>\n
        
        <p>Quantitative and qualitative progress has been made through the increased use of recycled plastics and reductions in CO2 emissions. Further details on prior progress are not provided in the context.</p>\n
        
    <h3>MDR-A_06 (69 a): Resources Allocated to Action Plan (Capex and Opex)</h3>\n
        
        <p>The context does not provide details on current or future Capex and Opex allocations specific to this action.</p>\n

    <h3>MDR-A_07 (69 b): Explanation of Financial Resource Link to Statements</h3>\n
        
        <p>The connection between financial resources allocated and amounts in financial statements is not provided in the context.</p>\n

    <h3>MDR-A_08 (AR 23): Financial Resources Breakdown</h3>\n
        
        <p>Information on the breakdown of financial resources by time horizon or type is not provided in the context.</p>\n

    [...]
        
    <h2>Action 2: Herstellung und Einsatz von hochqualitativen, schadstoffarmen Sekundärrohstoffen</h2>\n

    <h3>MDR-A_01 (68 a): Disclosure of Key Actions</h3>\n
        
        <p>The company plans to produce up to 10,000 low-emission protective covers annually using high-quality secondary raw materials. A feasibility study evaluates the suitability of these materials for applications in harsh environmental conditions like snow, ice, UV radiation, and oil exposure. <source>["chunk-7","chunk-21"]</source></p>\n
        
    <h3>MDR-A_02 (68 b): Description of Scope of Key Action</h3>\n
        
        <p>The scope includes activities to identify and utilize suitable secondary raw materials, targeting upstream processes (supplier evaluation) and downstream impacts (customer usage). Stakeholder groups include suppliers, design teams, and customers using the protective covers. [...]</p>\n
        [...]
    <h3>MDR-A_12 (69 c): Future Financial Resources for Opex</h3>\n
        
        <p>No information is provided regarding future Opex allocations.</p>",
        }
    <example_end>


    Before generating the final output: Find relevant actions & their sources. Do not waste reasnoing tokens on details of actions, just collect them and find the sources. Correct citing is crucial. Most of them come from the context but also sometimes are referenced in the user remark.
    It's OK for this section to be quite long.

    Remember to adhere strictly to the provided information, requirements and json format. Use structuring elements like h3 headings to make it easier to read, but avoid double empty lines/break. Use only information that is explicitly mentioned in the context. Report on all actions relevant to ${esrsDatapoint.name}, mention missing required information and do not report anything that is not explicitly required for this. You are crafting a report for the company, not evaluating the data provided. If data are missing just, add a <p> at the very end stating what important, required information hasn't been provided. Cite every single piece of information in the place where you use it (so in the middle of sentences, not at the end of sentences or even paragraphs) and strictly adhere to this format <source>["chunk-6"]</source>
    `;
  }

  generateMDRTContentGenerationSystemPrompt({
    esrsDatapoint,
    datapointGenerationContextFromLinkedDocumentChunks,
    language,
    reportTextGenerationRules,
    generalCompanyProfile,
    reportingYear,
    customUserRemark,
  }: {
    esrsDatapoint: ESRSDatapoint;
    datapointGenerationContextFromLinkedDocumentChunks: string;
    language: Language;
    reportTextGenerationRules: string;
    generalCompanyProfile: string;
    reportingYear: string;
    customUserRemark: string;
  }) {
    const cleanCustomUserRemark = customUserRemark
      .replace(/<p>/g, '')
      .replace(/<\/p>/g, '')
      .trim();

    const currentYear = new Date().getFullYear();

    return `You are an AI assistant specialized in generating content for sustainability reports, focusing on Minimal Disclosure Requirements - Targets (MDR-T) for European companies according to the EU's corporate sustainability reporting directive (CSRD). Your task is to extract relevant policy information from provided context, structure it according to the specific legal requirements of a certain datapoint and cite sources correctly.

    We are currently in the year ${currentYear} and ${
      reportingYear
        ? ' regardless of what the reporing rules below specify or what the reference document mention, we are strictly reporting for the year ' +
          reportingYear
        : ' figure out the reporting year from the reporting rules specified below or from the other references'
    }. Consider this when doing your generation.

    The contents of this prompt are
    1. Context: RAG-retrieved chunks from corporate documentation
    2. Law texts incl application requirements detailling what exactly to report 
    3. Example output what the generated json should exactly look like
    4) further instructions.

    **Context**:
    The following context contains information potentially relevant to the MDR-T i.e. contains documentation about the company's social/sustainability targets. Use this to inform your generation of the data point:
    <retrieved_context>
    ${datapointGenerationContextFromLinkedDocumentChunks}
    </retrieved_context>
    ----------

    **Legal Requirements**:
    1. Requirements specific to this set of MDR-T targets according to [ESRS ${esrsDatapoint.esrsDisclosureRequirement.dr}]:
      ${esrsDatapoint.datapointId} ${esrsDatapoint.name}
      <requirements>
      ${esrsDatapoint.lawText} 
      </requirements>
      <footnotes>
      ${esrsDatapoint.footnotes}
      </footnotes>
      <application_requirements>
      ${esrsDatapoint.lawTextAR}
      </application_requirements>
      <footnotes_application_requirements>
      ${esrsDatapoint.footnotesAR}
      </footnotes_application_requirements>

    2. General Requirements for MDR-T according to ESRS:
    The disclosure of the targets required shall contain the information required in ESRS 2 MDR-T Tracking effectiveness of policies and actions through targets. The undertaking shall disclose the measurable, outcome-oriented, and time-bound targets on material sustainability matters it has set to assess progress. For each target, the disclosure shall include the following information:
    - MDR-T_01 (80 a): Description of the relationship of the target to the objectives.
    - MDR-T_02 (80 b): The defined measurable target level to be achieved.
    - MDR-T_03 (80 b): Nature of the target, including whether it is absolute or relative and the unit of measurement.
    - MDR-T_04 (80 c): Description of the scope of the target, including activities, value chain (upstream/downstream), and geographical boundaries.
    - MDR-T_05 (80 d): The baseline value from which progress is measured.
    - MDR-T_06 (80 d): The baseline year from which progress is measured.
    - MDR-T_07 (80 e): The period to which the target applies.
    - MDR-T_08 (80 e): Indication of milestones or interim targets.
    - MDR-T_09 (80 f): Description of methodologies and significant assumptions used to define the target.
    - MDR-T_10 (80 g): Whether the target is based on conclusive scientific evidence.
    - MDR-T_11 (80 h): Disclosure of whether and how stakeholders have been involved in target setting.
    - MDR-T_12 (80 i): Description of any changes in the target and corresponding metrics or underlying measurement methodologies, significant assumptions, limitations, sources, and data collection processes.
    - MDR-T_13 (80 j): Description of performance against the disclosed target.

    Instructions:
    1. Analyze the context and identify relevant targets.
    2. For each action, address all MDR-T requirements (MDR-T_01 to MDR-T_13). Ensure the writing aligns with the MDR’s structural and content requirements. Avoid both over- and underreporting.
    3. Use only information provided in the context. Do not infer or create additional information. Correctly cite all claims. If specific information for an MDR-T point is not available, just literally state: "The required information is not provided in the context." Do not spend part of the report you are generating here discussing what information is provided or missing. Just write a draft with what you got, and if there is nothing useful just the literally the sentence before.
    4. Use precise and professional language suitable for a corporate sustainability report.
    5. Evaluate whether the requirement is fully met, partially met, or not met
    6. Cite sources using the format: <source>["chunk-6"]</source>. 
    7. If the output contains Numbers, Year-Numbers, Monetary Numbers, Percentages or similar numeric Data, pack them inside a <span> tag with the following styling: style="color: #eab308"
    8. Include a "citation" section in the JSON output as shown in the example.
    9. If the output contains Numbers, Year-Numbers, Monetary Numbers, Percentages or similar numeric Data, pack them inside a <span> tag with the following styling: style="color: #eab308"
    10. When fulfilling a requirement, include the ID and Pagraph ( specified before and summarize what it is about in the headline.
    11.  All the outcomes incl reasoning tokens that you generate are written in ${language}.
    12. ${reportingYear ? `The current reporting year is ${reportingYear}.` : ''}
    13. ${reportTextGenerationRules !== '' ? `**Content Generation Rules**:\n${reportTextGenerationRules}` : ''}
    14. ${cleanCustomUserRemark !== '' && cleanCustomUserRemark.length > 2 ? `**Additional User Remark (High Priority - CONSIDER THIS WHEN GENERATING THE DATAPOINT)**:\n${cleanCustomUserRemark}` : ''}
    15. ${!!generalCompanyProfile && `**General Company Profile**:\n${generalCompanyProfile}\n\nThis is the general company profile. Use this information to inform your generation of the data point. In particular focus on company structure and which year is being reported for.`}

    Output Format:
    - Return a json with the key 'datapoint' and the text of it as value.
    - Use HTML tags (h1, h2, h3, p) for formatting within the "datapoint" section.
    - Add a <br> before each h1, h2, h3 tag, except at the beginning.
    - Wrap numbers, year numbers, monetary numbers, percentages, or similar numeric data in <span style="color: #eab308"> tags.
    ----------

    Response json example:
    <example_start>
    {"datapoint":"<h2>Target 1: Reduce Lost Time Incident Frequency (LTIF) to 3.6 by 2025</h2>\n
    <h3>MDR-T_01 (80 a): Relationship with Policy Objectives</h3>\n
      [Details about how the target relates to the company's objectives. If not available: “The required information is not provided in the context.”]
      <p>This target supports the company's objective to enhance health protection and safety, aiming to reduce workplace accidents and improve overall employee safety.<source>["chunk-1","chunk-9"]</source></p>\n
      
    <h3>MDR-T_02 (80 b): Measurable Target Level</h3>\n
      [Specific measurable target levels to be achieved.]
      <ul>
        <li>Reduce LTIF to 4.05 by 2024.<source>["chunk-5"]</source></li>
        <li>Further reduce LTIF to 3.6 by 2025. <source>["chunk-7"]</source></li>
      </ul>\n
      
    <h3>MDR-T_03 (80 b): Nature of the Target</h3>\n
      [Whether the target is absolute or relative and the unit of measurement.]
      <p>This action is planned to achieve its full implementation within the next 5 years, with incremental milestones tracked annually.</p>\n

    [...]
      
    <h2>Target 2: Implement Annual Diversity Roadmaps and Enhance Diversity Visibility by 2030</h2>\n

    <h3>MDR-T_01 (80 a): Relationship with Policy Objectives</h3>\n
      
      <p>This target aligns with the company's dedication to equal treatment and opportunities, promoting diversity and inclusion across the organization. <source>["chunk-3","chunk-14"]</source></p>\n
      
      [...]
    <h3>MDR-T_13 (80 j): Performance Against Target</h3>\n
      
      <p>Progress is monitored annually; specific performance data is not provided in the context.</p>",
      }
    <example_end>


    Before generating the final output: Find relevant targets & their sources. Do not waste reasoning tokens on details of targets, just collect them and find the sources. Correct citing is crucial. Most of them come from the context but also sometimes are referenced in the user remark.
    It's OK for this section to be quite long.

    Remember to adhere strictly to the provided information, requirements and json format. Use structuring elements like h3 headings to make it easier to read, but avoid double empty lines/break. Use only information that is explicitly mentioned in the context. Report on all targets relevant to ${esrsDatapoint.name}, mention missing required information and do not report anything that is not explicitly required for this. You are crafting a report for the company, not evaluating the data provided. If data are missing just, add a <p> at the very end stating what important, required information hasn't been provided. Cite every single piece of information in the place where you use it (so in the middle of sentences, not at the end of sentences or even paragraphs) and strictly adhere to this format <source>["chunk-6"]</source>
    `;
  }
}
