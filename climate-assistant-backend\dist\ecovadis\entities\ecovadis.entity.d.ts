import { DocumentChunk } from '../../document/entities/document-chunk.entity';
import { Project } from '../../project/entities/project.entity';
import { User } from '../../users/entities/user.entity';
export declare enum ProjectType {
    CSRD = "CSRD",
    EcoVadis = "EcoVadis"
}
export declare enum EcoVadisIndicator {
    POLICIES = "POLICIES",
    ENDORSEMENTS = "ENDORSEMENTS",
    MEASURES = "MEASURES",
    CERTIFICATIONS = "CERTIFICATIONS",
    COVERAGE = "COVERAGE",
    REPORTING = "REPORTING",
    WATCH_FINDINGS = "WATCH_FINDINGS"
}
export declare enum ImpactScore {
    High = "High",
    Medium = "Medium",
    Low = "Low"
}
export declare enum EcoVadisScoreLevel {
    Outstanding = "Outstanding",
    Advanced = "Advanced",
    Good = "Good",
    Partial = "Partial",
    Insufficient = "Insufficient"
}
export declare class EcoVadisTheme {
    id: string;
    title: string;
    description: string;
    createdAt: Date;
    questions: EcoVadisQuestion[];
    projectThemes: ProjectEcoVadisTheme[];
}
export declare class ProjectEcoVadisTheme {
    id: string;
    themeId: string;
    projectId: string;
    impact: ImpactScore;
    issues: Array<{
        issueId: string;
        impact: ImpactScore;
    }>;
    createdAt: Date;
    theme: EcoVadisTheme;
    project: Project;
}
export declare class EcoVadisQuestion {
    id: string;
    themeId: string;
    questionCode: string;
    indicator: EcoVadisIndicator;
    questionName: string;
    question: string;
    createdAt: Date;
    theme: EcoVadisTheme;
    answerOptions: EcoVadisAnswerOption[];
    projectQuestions: ProjectEcoVadisQuestion[];
    gaps: ProjectEcoVadisGaps[];
}
export declare class ProjectEcoVadisQuestionScore {
    id: string;
    questionId: string;
    score: number;
    level: EcoVadisScoreLevel;
    description: string;
    breakdown: string;
    conclusion: string;
    createdAt: Date;
    question: EcoVadisQuestion;
    history: ProjectEcoVadisQuestionScoreHistory[];
}
export declare class ProjectEcoVadisQuestionScoreHistory {
    id: string;
    scoreId: string;
    score: number;
    level: EcoVadisScoreLevel;
    description: string;
    breakdown: string;
    conclusion: string;
    version: number;
    createdAt: Date;
    currentScore: ProjectEcoVadisQuestionScore;
}
export declare class ProjectEcoVadisQuestion {
    id: string;
    questionId: string;
    projectId: string;
    impact: ImpactScore;
    status: string;
    createdAt: Date;
    question: EcoVadisQuestion;
    project: Project;
}
export declare class ProjectEcoVadisGaps {
    id: string;
    questionId: string;
    projectId: string;
    gaps: Record<string, any>;
    documents: string[];
    resolved: boolean;
    assigneeId: string;
    deadline: Date;
    createdAt: Date;
    question: EcoVadisQuestion;
    project: Project;
    assignee: User;
}
export declare class EcoVadisAnswerOption {
    id: string;
    questionId: string;
    issueTitle: string;
    instructions: string;
    createdAt: Date;
    question: EcoVadisQuestion;
    answers: ProjectEcoVadisAnswer[];
}
export declare class ProjectEcoVadisAnswer {
    id: string;
    projectId: string;
    optionId: string;
    response: string;
    createdAt: Date;
    project: Project;
    option: EcoVadisAnswerOption;
    linkedChunks: ProjectEcoVadisLinkedDocumentChunks[];
}
export declare class ProjectEcoVadisLinkedDocumentChunks {
    id: number;
    answerId: string;
    documentChunkId: string;
    comment: string;
    createdAt: Date;
    answer: ProjectEcoVadisAnswer;
    documentChunk: DocumentChunk;
}
export declare class EcoVadisSustainabilityIssue {
    id: string;
    issue: string;
    definition: string;
    industryIssues: string;
    createdAt: Date;
}
