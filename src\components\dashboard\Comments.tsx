import { useEffect, useState } from 'react';
import { format } from 'date-fns';
import { ChevronDownIcon, CircleCheckBigIcon, TrashIcon } from 'lucide-react';

import { Badge } from '../ui/badge';
import { toast } from '../ui/use-toast';
import { MarkdownRenderer } from '../ui/markdown-renderer';
import { TipTapEditor } from '../ui/tiptap/tiptap-editor';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn, userHasRequiredRole, xor } from '@/lib/utils';
import type { CommentData } from '@/types/project';
import {
  createComment,
  deleteComment,
  resolveComment,
} from '@/api/project-settings/project-settings.api';
import { useAuthentication } from '@/api/authentication/authentication.query';
import { CommentType } from '@/types';
import { USER_ROLE } from '@/constants/workspaceConstants';
import { GLOBAL_AI_USER_UUID } from '@/lib/config';
import { fireConfetti } from '@/lib/confetti';
import useSessionStorage from '@/hooks/useSessionStorage';

export const CommentSection = ({
  projectId,
  savedComments,
  commentableId,
  commentableType,
  updateCallback,
}: {
  projectId: string;
  savedComments: CommentData[];
  commentableId: string;
  commentableType: CommentType;
  updateCallback?: () => void;
}) => {
  const [comments, setComments] = useState<CommentData[]>(savedComments);
  const [newComment, setNewComment] = useState('');
  const [showResolved, setShowResolved] = useState(true);
  const [showUnresolved, setShowUnresolved] = useState(true);
  const [resolveClickCount, setResolveClickCount] = useSessionStorage<number>(
    'resolveClickCount',
    0
  );
  const { user, isLoading } = useAuthentication();

  useEffect(() => {
    setComments(savedComments);
  }, [savedComments]);

  const addComment = async () => {
    try {
      await createComment({
        projectId,
        commentData: {
          comment: newComment,
          commentableType: commentableType,
          commentableId: commentableId,
        },
      });

      setNewComment('');

      toast({
        title: 'Comment added',
        variant: 'success',
      });

      if (updateCallback) {
        updateCallback();
      }
    } catch (error) {
      toast({
        title: 'Error adding comment',
        variant: 'destructive',
      });
    }
  };

  const toggleSolved = async (commentId: string, resolve: boolean) => {
    try {
      const update = await resolveComment({
        projectId,
        commentId,
        resolve,
      });
      setComments(
        comments.map((comment) =>
          comment.id === update.id
            ? { ...comment, resolved: !comment.resolved }
            : comment
        )
      );

      if (resolve) {
        const newCount = resolveClickCount + 1;
        setResolveClickCount(newCount);

        if (newCount % 3 === 0) {
          fireConfetti();
        }
        toast({
          title: 'Comment resolved',
          variant: 'success',
        });
      } else {
        toast({
          title: 'Comment reopened',
        });
      }

      if (updateCallback) {
        updateCallback();
      }
    } catch (error) {
      toast({
        title: 'Error resolving comment',
        variant: 'destructive',
      });
    }
  };

  const removeComment = async (commentId: string) => {
    try {
      await deleteComment({ projectId, commentId });
      setComments(comments.filter((comment) => comment.id !== commentId));
      toast({
        title: 'Comment deleted',
        variant: 'success',
      });

      if (updateCallback) {
        updateCallback();
      }
    } catch (error) {
      toast({
        title: 'Error deleting comment',
        variant: 'destructive',
      });
    }
  };

  const filteredComments = xor([showResolved, showUnresolved])
    ? comments.filter((comment) =>
        showResolved ? comment.resolved : !comment.resolved
      )
    : comments;

  return (
    <div className="py-6">
      <div className="flex justify-between items-center">
        <div className="flex justify-start items-center gap-4">
          <h2 className="text-xl font-semibold">Comments</h2>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="link" className="p-0 hover:no-underline">
                <Badge
                  variant={
                    xor([showResolved, showUnresolved])
                      ? showResolved
                        ? 'forest'
                        : 'warning'
                      : 'outline'
                  }
                  className="rounded-md"
                >
                  {filteredComments.length} Comment
                  {filteredComments.length > 1 && 's'}
                  {xor([showResolved, showUnresolved]) &&
                    (showResolved ? ' resolved' : ' unresolved')}
                </Badge>
                <ChevronDownIcon className="w-4 h-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              <DropdownMenuLabel>Status</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuCheckboxItem
                checked={showResolved}
                onCheckedChange={setShowResolved}
              >
                Resolved
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={showUnresolved}
                onCheckedChange={setShowUnresolved}
              >
                Unresolved
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="mt-4 space-y-4">
        {filteredComments.length > 0 ? (
          filteredComments.map((comment) => (
            <div
              key={comment.id}
              className={`p-4 border-l-2 ${
                comment.resolved ? 'border-green-500' : 'border-yellow-500'
              }`}
            >
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-semibold">{comment.user.name}</h3>
                  <p className="text-xs text-gray-800">
                    {format(new Date(comment.createdAt), 'dd.MM.yyyy HH:mm')}
                  </p>
                </div>
                <div className="flex justify-end items-center gap-3">
                  {!isLoading &&
                    (comment.userId === user?.id ||
                      (comment.userId === GLOBAL_AI_USER_UUID &&
                        userHasRequiredRole([USER_ROLE.AiContributor], user)) ||
                      userHasRequiredRole([USER_ROLE.SuperAdmin], user)) && (
                      <Button
                        size={'xs'}
                        variant={'outline'}
                        onClick={() => removeComment(comment.id)}
                      >
                        <TrashIcon className={`w-3 h-3 mr-2`} />
                        Delete Comment
                      </Button>
                    )}
                  <Button
                    size={'xs'}
                    variant={'outline'}
                    onClick={() => toggleSolved(comment.id, !comment.resolved)}
                  >
                    <CircleCheckBigIcon className={`w-3 h-3 mr-2`} />
                    {comment.resolved ? 'Reopen Comment' : 'Solve Comment'}
                  </Button>
                </div>
              </div>
              <div className="mt-4">
                <MarkdownRenderer className="text-sm" text={comment.comment} />
              </div>
            </div>
          ))
        ) : (
          <p className="text-gray-400">No comments available.</p>
        )}
      </div>

      <div>
        <TipTapEditor
          content={newComment}
          setContent={setNewComment}
          submitAction={addComment}
          size="sm"
        />
        <Button
          variant={'secondary'}
          onClick={addComment}
          className={cn(
            'text-slate-800 bg-slate-300 h-8 px-5',
            newComment.trim() !== ''
              ? 'opacity-100 pointer-events-auto'
              : 'opacity-50 pointer-events-none'
          )}
        >
          Add Comment
        </Button>
      </div>
    </div>
  );
};
