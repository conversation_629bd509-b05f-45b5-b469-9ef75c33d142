
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { LoaderCircle, Target } from "lucide-react";
import { EcovadisQuestion } from "@/types/ecovadis";
import { ScoreDetails } from "./score/ScoreDetails";
import { MarkdownRenderer } from "@/components/ui/markdown-renderer";
import { Button } from "@/components/ui/button";
import { useScoreAssessment } from "@/hooks/useScoreAssessment";
import { useParams } from 'react-router-dom';
import { useUserRole } from "@/hooks/useUserRole";

interface EstimatedScoreSectionProps {
  question?: EcovadisQuestion;
}

export const EstimatedScoreSection = ({ 
  question,
}: EstimatedScoreSectionProps) => {
  const { id: projectId } = useParams<{ id: string }>();
  const { handleScoreAssessment, isLoading, scoreData, isLoadingScore } = useScoreAssessment(projectId!, question?.projectQuestionId);
  const { isContributor } = useUserRole();

  const handleRunScoreAssessment = () => {
    if (question?.projectQuestionId) {
      handleScoreAssessment(question.projectQuestionId);
    }
  };

  if (isLoadingScore) {
    return (
      <div className="text-center p-8 bg-glacier-mint/10 border border-glacier-mint/30 rounded-lg">
        <div className="flex items-center justify-center gap-3">
          <LoaderCircle className="h-5 w-5 text-glacier-darkBlue animate-spin" />
          <h3 className="text-glacier-darkBlue font-semibold">Loading score data...</h3>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="text-center p-8 bg-glacier-mint/10 border border-glacier-mint/30 rounded-lg">
        <div className="flex items-center justify-center gap-3">
          <LoaderCircle className="h-5 w-5 text-glacier-darkBlue animate-spin" />
          <h3 className="text-glacier-darkBlue font-semibold">AI is assessing the score...</h3>
        </div>
      </div>
    );
  }

  // Get score from the fetched data
  const score = scoreData?.score;
  
  // Handle score properly: 0 is a valid score, null/undefined means no score
  const hasScore = score !== null && score !== undefined;
  
  if (!hasScore) {
    return (
      <div className="space-y-4">
        <div className="text-center">
          <Button 
            onClick={handleRunScoreAssessment}
            disabled={isLoading}
            className="bg-glacier-darkBlue hover:bg-glacier-darkBlue/90 text-white flex items-center gap-1"
          >
            <Target className="h-3.5 w-3.5" />
            <span>AI Score Assessment</span>
          </Button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="mb-8 space-y-5">
      <Card>
        <div className="px-6 py-4 border-b border-gray-100 flex justify-between items-center">
          <h2 className="text-lg font-semibold text-glacier-darkBlue flex items-center gap-2">
            Estimated Score
          </h2>
          {!isContributor() && (
            <Button 
              onClick={handleRunScoreAssessment}
              disabled={isLoading}
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              <Target className="h-3.5 w-3.5" />
              <span>Re-assess Score</span>
            </Button>
          )}
        </div>
        
        <CardContent className="pt-4">
          <ScoreDetails 
            score={score} 
          />
        </CardContent>
      </Card>

    {scoreData?.description && (
      <Card>
        <div className="px-6 py-4 border-b border-gray-100 flex justify-between items-center">
          <h2 className="text-lg font-semibold text-glacier-darkBlue flex items-center gap-2">
            Score Assessment
          </h2>
        </div>
        
        <CardContent className="pt-4">
          <div className="overflow-x-auto">
            <table className="min-w-full text-left text-sm text-gray-700 border border-gray-200">
              <thead className="bg-gray-100 text-gray-700 font-semibold">
                <tr>
                  <th className="px-4 py-2 border-b">Score</th>
                  <th className="px-4 py-2 border-b">Level</th>
                  <th className="px-4 py-2 border-b">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="px-4 py-2 border-b text-glacier-darkBlue font-semibold">
                    {scoreData?.score || '0'}
                  </td>
                  <td className="px-4 py-2 border-b">
                    {scoreData?.level}
                  </td>
                  <td className="px-4 py-2 border-b">
                  <MarkdownRenderer text={scoreData.description} />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
      </CardContent>
      </Card>
      )}
      
    {scoreData?.breakdown && (
      <Card>
        <div className="px-6 py-4 border-b border-gray-100 flex justify-between items-center">
          <h2 className="text-lg font-semibold text-glacier-darkBlue flex items-center gap-2">
            Score Breakdown
          </h2>
        </div>
        
        <CardContent className="pt-4">
          <MarkdownRenderer text={scoreData.breakdown} />
        </CardContent>
      </Card>
      )}

    {scoreData?.conclusion && (
      <Card>
        <div className="px-6 py-4 border-b border-gray-100 flex justify-between items-center">
          <h2 className="text-lg font-semibold text-glacier-darkBlue flex items-center gap-2">
            Conculsion: Why the score is {scoreData?.score || '0'} ({scoreData?.level})
          </h2>
        </div>
        
        <CardContent className="pt-4">
          <MarkdownRenderer text={scoreData.conclusion} />
        </CardContent>
      </Card>
      )}
    </div>
  );
};
