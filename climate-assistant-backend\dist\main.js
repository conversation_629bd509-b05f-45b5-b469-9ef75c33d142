"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const cookieParser = require("cookie-parser");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const lovableAppRegex = /^https:\/\/.*\.(lovable\.app|lovableproject\.com)$/;
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.use(cookieParser());
    app.useGlobalPipes(new common_1.ValidationPipe());
    app.enableCors({
        credentials: true,
        origin: [
            'https://app.glacier.eco',
            'https://prototype.glacier.eco',
            'https://ecovadis.glacier.eco',
            lovableAppRegex,
            'http://localhost:5173',
            'http://localhost:8080',
        ],
    });
    const config = new swagger_1.DocumentBuilder()
        .setTitle('Glacier API')
        .setDescription('Glacier App API standards')
        .setVersion('1.0')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api/swagger', app, document);
    await app.listen(3000);
}
bootstrap();
//# sourceMappingURL=main.js.map