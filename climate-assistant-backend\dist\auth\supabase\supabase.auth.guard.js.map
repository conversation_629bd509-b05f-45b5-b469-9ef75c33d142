{"version": 3, "file": "supabase.auth.guard.js", "sourceRoot": "", "sources": ["../../../src/auth/supabase/supabase.auth.guard.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAKwB;AAExB,uCAAyC;AACzC,yDAAqD;AACrD,wCAAiD;AAG1C,IAAM,SAAS,GAAf,MAAM,SAAS;IACpB,YACU,SAAoB,EACpB,eAAgC;QADhC,cAAS,GAAT,SAAS,CAAW;QACpB,oBAAe,GAAf,eAAe,CAAiB;IACvC,CAAC;IAEJ,KAAK,CAAC,WAAW,CAAC,OAAyB;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAU,uBAAa,EAAE;YACxE,OAAO,CAAC,UAAU,EAAE;YACpB,OAAO,CAAC,QAAQ,EAAE;SACnB,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAE5D,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAEzC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,8BAAqB,EAAE,CAAC;QACpC,CAAC;QAED,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,eAAe;iBACxD,SAAS,EAAE;iBACX,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAEvB,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,8BAAqB,CAAC,gCAAgC,CAAC,CAAC;YACpE,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAEtE,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,8BAAqB,CAAC,gBAAgB,CAAC,CAAC;YACpD,CAAC;YAED,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CACtC,OAAO,EACP,OAAO,CAAC,UAAU,EAAE,CACrB,CAAC;YAEF,MAAM,eAAe,GACnB,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU;gBACrC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;gBAC9C,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW;gBACtD,CAAC,CAAC,IAAI,CAAC;YAEX,OAAO,CAAC,MAAM,CAAC,GAAG;gBAChB,GAAG,EAAE,IAAI,CAAC,EAAE;gBACZ,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,WAAW,EAAE,eAAe;aAC7B,CAAC;YAEF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CACrD,IAAI,CAAC,EAAE,EACP,eAAe,EAAE,WAAW,CAC7B,CAAC;YAEF,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,IAAI,8BAAqB,EAAE,CAAC;QACpC,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,OAAgB;QAEnC,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC;QACjD,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACnD,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC;QAGD,OAAO,OAAO,EAAE,OAAO,EAAE,CAAC,cAAc,CAAC,CAAC;IAC5C,CAAC;CACF,CAAA;AAxFY,8BAAS;oBAAT,SAAS;IADrB,IAAA,mBAAU,GAAE;qCAGU,gBAAS;QACH,kCAAe;GAH/B,SAAS,CAwFrB"}