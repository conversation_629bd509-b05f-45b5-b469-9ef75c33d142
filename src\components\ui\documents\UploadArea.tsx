import { useState } from 'react';
import { toast } from 'sonner';
import { fireConfetti } from '@/lib/confetti';
import { DragDropZone } from './DragDropZone';
import { FileList } from './FileList';
import { SharePointInput } from './SharePointInput';
import { DeleteConfirmationDialog } from './DeleteConfirmationDialog';

export function UploadArea() {
  const [isDragging, setIsDragging] = useState(false);
  const [sharepointLink, setSharepointLink] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<number | null>(null);
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };
  
  const handleDragLeave = () => {
    setIsDragging(false);
  };
  
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  };
  
  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(Array.from(e.target.files));
    }
  };
  
  const handleFiles = (files: File[]) => {
    // Here you would typically upload the files to your server
    // For demo purposes, we'll just add them to our local state
    setUploadedFiles(prev => [...prev, ...files]);
    
    toast.success(`Uploaded ${files.length} document${files.length > 1 ? 's' : ''}`, {
      description: 'Your documents are now available in the system.'
    });
    
    // Fire confetti for the celebration effect
    if (files.length > 0) {
      fireConfetti();
    }
  };
  
  const handleLinkSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (sharepointLink.trim()) {
      // Here you would process the link
      toast.success('Connected to shared folder', {
        description: 'Documents from the shared folder are now being processed.'
      });
      setSharepointLink('');
      fireConfetti();
    }
  };
  
  const handleDeleteClick = (index: number) => {
    setFileToDelete(index);
    setDeleteConfirmOpen(true);
  };
  
  const handleDeleteConfirm = () => {
    if (fileToDelete !== null) {
      // Removed toast notification here - just perform the action silently
      removeFile(fileToDelete);
      setFileToDelete(null);
      setDeleteConfirmOpen(false);
    }
  };
  
  const handleDeleteCancel = () => {
    setDeleteConfirmOpen(false);
    setFileToDelete(null);
  };
  
  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };
  
  const handleFileSelect = () => {
    document.getElementById('file-upload')?.click();
  };
  
  return (
    <div className="space-y-6">
      {/* Hidden file input */}
      <input
        id="file-upload"
        type="file"
        multiple
        className="hidden"
        onChange={handleFileInput}
      />
      
      {/* Drag & Drop Area */}
      <DragDropZone 
        isDragging={isDragging}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onFileSelect={handleFileSelect}
      />
      
      {/* SharePoint/Google Drive Link */}
      <SharePointInput 
        value={sharepointLink}
        onChange={(e) => setSharepointLink(e.target.value)}
        onSubmit={handleLinkSubmit}
      />
      
      {/* Recently Uploaded Files */}
      <FileList 
        files={uploadedFiles}
        onDeleteClick={handleDeleteClick}
      />
      
      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog 
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        onDelete={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
      />
    </div>
  );
}
