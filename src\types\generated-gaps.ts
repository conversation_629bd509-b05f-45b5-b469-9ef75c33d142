export type GeneratedGapStatus = 'pending_review' | 'approved' | 'rejected' | 'regenerating';

export interface GapAnalysisItem {
  gap: string;
  gap_type: string;
  description: string;
  sample_text: string;
  pillar_category: string;
  affected_documents: string[];
  recommended_actions: string[];

}

export interface GeneratedGap {
  id: string;
  projectId: string;
  questionId: string;
  generatedContent: GapAnalysisItem; // Now more specific - individual gap item
  documents: { id: string; name: string }[] | null;
  status: GeneratedGapStatus;
  feedback: string | null;
  reviewedBy: string | null;
  reviewedAt: string | null;
  createdAt: string;
  updatedAt: string | null;

  // Joined data
  project?: {
    id: string;
    name: string;
  };
  ecovadis_question?: {
    id: string;
    questionCode: string;
    question: string;
    indicator: string;
    ecovadis_theme?: {
      id: string;
      title: string;
    };
  };
  reviewer?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface ReviewGapRequest {
  gapId: string;
  action: 'approve' | 'disapprove' | 'regenerate';
  feedback?: string;
}
