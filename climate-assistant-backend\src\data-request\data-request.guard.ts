import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { DataRequestService } from './data-request.service';

@Injectable()
export class DataRequestGuard implements CanActivate {
  constructor(
    private readonly dataRequestService: DataRequestService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const dataRequestId = request.params.dataRequestId;
    const workspaceId = request.user.workspaceId;

    const dataRequest =
      await this.dataRequestService.findProject(dataRequestId);
    const project = dataRequest.project;

    if (project.workspaceId !== workspaceId) {
      throw new UnauthorizedException(`Project is not from this workspace`);
    }

    const customCheck = this.reflector.get<string>(
      'customCheck',
      context.getHandler(),
    );

    switch (customCheck) {
      case 'generateWithAI':
        return this.dataRequestService.validateDataRequestGenerationRightsOrFail(
          {
            dataRequest,
            userId: request.user.id,
          },
        );
      default:
        return true;
    }
  }
}
