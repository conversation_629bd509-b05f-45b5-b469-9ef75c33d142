
import { useState } from "react";
import { useForm } from "react-hook-form";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer, DialogDescription } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { FileText } from "lucide-react";
import { FileUpload } from "@/components/ui/file-upload";
import { uploadDocument } from "@/api/workspace-settings/workspace-settings.api";
import { useToast } from "@/components/ui/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

interface UploadDocumentDialogProps {
  open: boolean;
  onClose: () => void;
  optionId: string;
  answerId: string;
  onAddDocument: () => void;
}

export const UploadDocumentDialog = ({
  open,
  onClose,
  optionId,
  answerId,
  onAddDocument
}: UploadDocumentDialogProps) => {
  const { toast } = useToast();
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadedFileName, setUploadedFileName] = useState<string>("");
  const [isUploading, setIsUploading] = useState(false);

  const formSchema = z.object({
    pages: z.string().min(1, "Page number is required"),
    comment: z.string().optional()
  });

  type FormValues = z.infer<typeof formSchema>;
  
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      pages: "",
      comment: ""
    }
  });

  const onSubmit = async (data: FormValues ) => {
    if (!uploadedFile) {
      toast({
        description: "Please upload a file first",
        variant: "destructive",
      })
      return;
    }

    setIsUploading(true);

    try {
      // Create form data with the file and metadata
      const formData = new FormData();
      formData.append('file', uploadedFile);
      formData.append('answerId', answerId);
      formData.append('pageNumbers', data.pages || '');
      
      // Add default values for required fields
      formData.append('documentType', 'ecovadis');
      formData.append('remarks', data.comment || '');

      // Upload the document
      await uploadDocument(formData);

      toast({
        description: "Document uploaded successfully",
        variant: "success",
      })
      form.reset();
      setUploadedFile(null);
      setUploadedFileName("");
      onAddDocument();
      onClose();
    } catch (error) {
      console.error("Error uploading document:", error);
      toast({
        description: "Error uploading document",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileUpload = (files: File[]) => {
    if (files.length > 0) {
      setUploadedFile(files[0]);
      setUploadedFileName(files[0].name);
      toast({
        description: "File selected successfully",
        variant: "success",
      })
    }
  };

  return (
    <Dialog open={open} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-xl">
        <DialogHeader>
          <DialogTitle>Upload document</DialogTitle>
          <DialogDescription>
            Upload a new document to support your answer.
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-4">
              <FormLabel>Upload file</FormLabel>
              <FileUpload onChange={handleFileUpload} acceptedFileTypes={[".pdf", ".docx", ".xlsx", ".pptx", ".doc", ".xls", ".ppt", ".txt"]} acceptedFileTypesMessage="Only document files are allowed (.pdf, .docx, .xlsx, etc.)" />
              {uploadedFileName && <div className="flex items-center gap-2 p-2 bg-glacier-mint/10 rounded-md">
                  <FileText className="h-4 w-4 flex-shrink-0 text-glacier-darkBlue" />
                  <span className="text-sm truncate">{uploadedFileName}</span>
                </div>}
            </div>
            
            <FormField control={form.control} name="pages" render={({
            field
          }) => <FormItem>
                  <FormLabel>Relevant pages</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g. 10-12, 15" {...field} />
                  </FormControl>
                  <div className="text-xs text-gray-500 mt-1">
                    💡 Tip: Consider adding ±2 pages around your specified range for better accuracy (e.g., if content is on pages 10-12, try 8-14)
                  </div>
                  <FormMessage />
                </FormItem>} />
            
            <FormField control={form.control} name="comment" render={({
            field
          }) => <FormItem>
                  <FormLabel>Comment (optional)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Add context about how this document provides evidence" className="min-h-[100px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>} />
            
            <DialogFooter className="sm:justify-end">
              <Button type="button" variant="outline" onClick={onClose} disabled={isUploading}>
                Cancel
              </Button>
              <Button type="submit" disabled={!uploadedFileName || isUploading} variant="darkBlue">
                {isUploading ? "Uploading..." : "Add document"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
